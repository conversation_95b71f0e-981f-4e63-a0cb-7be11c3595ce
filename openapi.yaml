# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: Greeter API
    description: The greeting service definition.
    version: 0.0.1
paths:
    /helloworld/{name}:
        get:
            tags:
                - Greeter
            description: Sends a greeting
            operationId: Greet<PERSON>_<PERSON><PERSON><PERSON>
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/helloworld.v1.HelloReply'
components:
    schemas:
        helloworld.v1.HelloReply:
            type: object
            properties:
                message:
                    type: string
            description: The response message containing the greetings
tags:
    - name: <PERSON>reeter
