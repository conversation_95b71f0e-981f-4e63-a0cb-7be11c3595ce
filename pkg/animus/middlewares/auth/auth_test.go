package auth

import (
	"context"
	"net/http"
	"testing"

	"github.com/go-kratos/kratos/v2/transport"
	"github.com/stretchr/testify/assert"
)

func TestGetClientIP(t *testing.T) {
	// 测试从 X-Forwarded-For 获取 IP
	req := &http.Request{
		Header: http.Header{
			"X-Forwarded-For": []string{"*************"},
		},
		RemoteAddr: "***********:12345",
	}

	// 创建一个模拟的 Transporter
	tr := &mockTransporter{
		request: req,
	}

	ip := getClientIP(tr)
	assert.Equal(t, "*************", ip)

	// 测试从 X-Real-IP 获取 IP (当 X-Forwarded-For 不存在时)
	req = &http.Request{
		Header: http.Header{
			"X-Real-IP": []string{"***********01"},
		},
		RemoteAddr: "***********:12345",
	}

	tr = &mockTransporter{
		request: req,
	}

	ip = getClientIP(tr)
	assert.Equal(t, "***********01", ip)

	// 测试从 RemoteAddr 获取 IP (当 X-Forwarded-For 和 X-Real-IP 都不存在时)
	req = &http.Request{
		RemoteAddr: "***********:12345",
	}

	tr = &mockTransporter{
		request: req,
	}

	ip = getClientIP(tr)
	assert.Equal(t, "***********", ip)

	// 测试 X-Forwarded-For 包含多个 IP 的情况 (应该返回第一个)
	req = &http.Request{
		Header: http.Header{
			"X-Forwarded-For": []string{"*************, ********, **********"},
		},
		RemoteAddr: "***********:12345",
	}

	tr = &mockTransporter{
		request: req,
	}

	ip = getClientIP(tr)
	assert.Equal(t, "*************", ip)
	
	// 测试当没有实现 Request() 方法的 Transporter
	emptyTr := &emptyTransporter{}
	ip = getClientIP(emptyTr)
	assert.Equal(t, "", ip)
	
	// 测试 X-Forwarded-For 为空字符串的情况 (应该继续检查 X-Real-IP)
	req = &http.Request{
		Header: http.Header{
			"X-Forwarded-For": []string{""},
			"X-Real-IP": []string{"***********02"},
		},
		RemoteAddr: "***********:12345",
	}

	tr = &mockTransporter{
		request: req,
	}

	ip = getClientIP(tr)
	assert.Equal(t, "***********02", ip)
}

func TestIsLocalIP(t *testing.T) {
	assert.True(t, isLocalIP("127.0.0.1"))
	assert.True(t, isLocalIP("::1"))
	assert.True(t, isLocalIP("::ffff:127.0.0.1"))
	assert.True(t, isLocalIP("::ffff:0:0:0:0:0:1"))
	assert.True(t, isLocalIP(""))
	assert.False(t, isLocalIP("*************"))
	assert.False(t, isLocalIP("*******"))
}

func TestServerMiddleware(t *testing.T) {
	// 创建一个测试处理函数
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return "success", nil
	}

	// 测试本地IP访问
	middleware := Server()
	req := &http.Request{
		RemoteAddr: "127.0.0.1:12345",
	}
	
	tr := &mockTransporter{
		request: req,
		operation: "/test",
	}
	
	ctx := transport.NewServerContext(context.Background(), tr)
	
	reply, err := middleware(handler)(ctx, nil)
	assert.NoError(t, err)
	assert.Equal(t, "success", reply)
}

// mockTransporter 实现了 Transporter 接口，用于测试
type mockTransporter struct {
	request   *http.Request
	operation string
}

func (tr *mockTransporter) Kind() transport.Kind {
	return transport.KindHTTP
}

func (tr *mockTransporter) Endpoint() string {
	return "http://localhost:8080"
}

func (tr *mockTransporter) Operation() string {
	return tr.operation
}

func (tr *mockTransporter) Request() *http.Request {
	return tr.request
}

func (tr *mockTransporter) RequestHeader() transport.Header {
	return headerCarrier(tr.request.Header)
}

func (tr *mockTransporter) ReplyHeader() transport.Header {
	return nil
}

func (tr *mockTransporter) PathTemplate() string {
	return ""
}

// emptyTransporter 不实现 Request() 方法，用于测试边界情况
type emptyTransporter struct{}

func (tr *emptyTransporter) Kind() transport.Kind {
	return transport.KindHTTP
}

func (tr *emptyTransporter) Endpoint() string {
	return "http://localhost:8080"
}

func (tr *emptyTransporter) Operation() string {
	return "/test"
}

func (tr *emptyTransporter) RequestHeader() transport.Header {
	return nil
}

func (tr *emptyTransporter) ReplyHeader() transport.Header {
	return nil
}

func (tr *emptyTransporter) PathTemplate() string {
	return ""
}

type headerCarrier http.Header

func (hc headerCarrier) Get(key string) string {
	return http.Header(hc).Get(key)
}

func (hc headerCarrier) Set(key, value string) {
	http.Header(hc).Set(key, value)
}

func (hc headerCarrier) Add(key, value string) {
	http.Header(hc).Add(key, value)
}

func (hc headerCarrier) Keys() []string {
	keys := make([]string, 0, len(hc))
	for key := range hc {
		keys = append(keys, key)
	}
	return keys
}

func (hc headerCarrier) Values(key string) []string {
	return http.Header(hc).Values(key)
}