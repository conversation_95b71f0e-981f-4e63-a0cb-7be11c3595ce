package auth

import (
	"context"
	"net"
	"strings"

	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/middleware/auth/jwt"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/http"
	jwtv5 "github.com/golang-jwt/jwt/v5"
)

type Option func(*options)

// WithAllowPath set Limiter allow paths,
// default is all
func WithAllowPath(pathArray []string) Option {
	return func(o *options) {
		for _, item := range pathArray {
			o.allowPathMap[item] = true
		}
	}
}

type options struct {
	allowPathMap map[string]bool
	timeoutMs    int64
}

const key = "mpp"

// Server 针对请求进行鉴权
func Server(opts ...Option) middleware.Middleware {
	options := &options{
		allowPathMap: map[string]bool{},
	}

	for _, o := range opts {
		o(options)
	}

	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			if tr, ok := transport.FromServerContext(ctx); ok {
				if len(options.allowPathMap) != 0 {
					allowCustomTimeout, exists := options.allowPathMap[tr.Operation()]
					// do not check auth
					if !exists || !allowCustomTimeout {
						reply, err = handler(ctx, req)
						return
					}
				}

				// 本机访问放行
				if isLocalIP(getClientIP(tr)) {
					return handler(ctx, req)
				}

				jwtMiddleware := jwt.Server(func(token *jwtv5.Token) (interface{}, error) {
					return []byte(key), nil
				})

				reply, err = jwtMiddleware(handler)(ctx, req)
				if err != nil {
					// todo 增加兼容老版本无鉴权的判断
					return
				}
			}
			return handler(ctx, req)
		}
	}
}

// Client is middleware client-side metadata.
func Client(opts ...Option) middleware.Middleware {
	options := &options{
		allowPathMap: map[string]bool{},
	}
	for _, o := range opts {
		o(options)
	}
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			jwtMiddleware := jwt.Client(func(token *jwtv5.Token) (interface{}, error) {
				return []byte(key), nil
			})

			return jwtMiddleware(handler)(ctx, req)
		}
	}
}

// getClientIP 从 transport 中获取客户端 IP
func getClientIP(tr transport.Transporter) string {
	if httpTr, ok := tr.(interface{ Request() *http.Request }); ok {
		req := httpTr.Request()

		// 检查 X-Forwarded-For 头部
		forwarded := req.Header.Get("X-Forwarded-For")
		if forwarded != "" {
			ips := strings.Split(forwarded, ",")
			if len(ips) > 0 {
				return strings.TrimSpace(ips[0])
			}
		}

		// 检查 X-Real-IP 头部
		if realIP := req.Header.Get("X-Real-IP"); realIP != "" {
			return realIP
		}

		// 回退到 RemoteAddr
		ip, _, _ := net.SplitHostPort(req.RemoteAddr)
		return ip
	}
	return ""
}

func isLocalIP(ip string) bool {
	if ip == "" || ip == "127.0.0.1" || ip == "::1" || ip == "::ffff:127.0.0.1" || ip == "::ffff:0:0:0:0:0:1" {
		return true
	}

	return false
}
