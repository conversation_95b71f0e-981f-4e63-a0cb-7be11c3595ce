#!/bin/bash

OUT_ROOT=./bin
BUILD_TIME=$(date +%FT%T%z)
# shellcheck disable=SC2006
GITTAG=`git rev-parse --short HEAD`


goLint() {
  go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
  golangci-lint run
  # shellcheck disable=SC2181
  if [ $? -ne 0 ] ; then
     echo "********************************************************"
     echo "go lint failed, please fix the issues..................."
     echo "********************************************************"
     exit 1
  fi
}

build() {
    pwd
    rm -rf $OUT_ROOT/*
    export GOPROXY=http://gomodule-repository.aone.alibaba-inc.com
    export GOPRIVATE=*.alibaba-inc.com
    export GO111MODULE=on

    ### 后期开启golint，需要解决lint问题
    #goLint

    # go build  -ldflags "-X aone-project-name/version.GitTag=${GITTAG} -X aone-project-name/version.BuildTime=${BUILD_TIME}" -mod=vendor -o ${OUT_ROOT}/ossutil-server ossutil-server.go
    #CGO_ENABLED=0 GO111MODULE="on" go build -a -installsuffix cgo -ldflags '-w -s' -o ${OUT_ROOT}/mpp-taskmanager ./main.go
    #make all
    make build-"${APP_NAME}"
    # shellcheck disable=SC2181
    if [ $? -ne 0 ] ; then
       echo "go build failed"
       exit 1
    fi

    VERSION=$(date "+%Y-%m-%d-%H-%M-%S" | sed 's/-//g')
    #### 很重要，不能删除，mpp-agent需要这个做版本化升级
#    echo "${VERSION}" > ${OUT_ROOT}/version
    echo "${GITTAG}" > ${OUT_ROOT}/gittag

    rm -rf ${OUT_ROOT}/gomock_reflect*
    echo "go build done"
    #make语句的数据目录是bin
    #cp bin/* ./_build/
    ls ${OUT_ROOT}
}

echo "start aone build..."

# prepare configs
if [ "${APP_NAME}" == "mpp-taskmanager" ]; then
     mkdir -p ./APP-META/docker-config/app/conf/taskmanager/ && cp -r ./configs/taskmanager/* ./APP-META/docker-config/app/conf/taskmanager/ || exit 1
elif [ "${APP_NAME}" == "mpp-taskscheduler" ]; then
     mkdir -p ./APP-META/docker-config/app/conf/taskscheduler/ && cp -r ./configs/taskscheduler/* ./APP-META/docker-config/app/conf/taskscheduler/ || exit 1
elif [ "${APP_NAME}" == "mpp-workflow" ]; then
     mkdir -p ./APP-META/docker-config/app/conf/workflow/ && cp -r ./configs/workflow/* ./APP-META/docker-config/app/conf/workflow/ || exit 1
elif [ "${APP_NAME}" == "mpp-sensing" ]; then
     mkdir -p ./APP-META/docker-config/app/conf/sensing/ && cp -rf ./configs/sensing/* ./APP-META/docker-config/app/conf/sensing/ || exit 1
elif [ "${APP_NAME}" == "mpp-gateway" ]; then
     mkdir -p ./APP-META/docker-config/app/conf/gateway/ && cp -rf ./configs/gateway/* ./APP-META/docker-config/app/conf/gateway/ || exit 1
elif [ "${APP_NAME}" == "mpp-chief" ]; then
     echo "do nothing"
elif [ "${APP_NAME}" == "mpp-ray" ]; then
     mkdir -p ./bin
     echo "DO NOTHING" > ./bin/README.md
     mkdir -p ./APP-META/docker-config/app/conf/ && cp -rf apps/analysis/configs/* ./APP-META/docker-config/app/conf/ || exit 1
     echo "do nothing"
     exit 0
else
     echo "prepare configs failed with unknown app name!"
fi

# build application
build

echo "build success..."
