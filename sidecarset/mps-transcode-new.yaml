# sidecarset.yaml
apiVersion: apps.kruise.io/v1alpha1
kind: SidecarSet
metadata:
  name: mpp-agent-sidecarset-mps-transcode-new-beta
spec:
  selector:
    matchLabels:
#      mpp.aliyun.com/agent-sidecarset: "true"
      sigma.ali/app-name: "mps-transcode-new"
      sigma.ali/instance-group: "mps-transcode-new_center_mpp-sh-pre-largeio_prehost"
  updateStrategy:
    type: RollingUpdate
    paused: false
    maxUnavailable: 1
  containers:
    - name: agent
      image: mpp-pre-registry-vpc.cn-shanghai.cr.aliyuncs.com/mpp/mpp-worker-agent:9f5ea86f
#      image: mpp-registry-vpc.cn-shanghai.cr.aliyuncs.com/mps-test/mpp-worker-agent:20230829164012073830
      command:
        - /bin/bash
        - '-c'
        - /home/<USER>/start_in_background.sh
      transferEnv:
        - envName: APPSTACK_APP_NAME
          sourceContainerName: main
        - envName: APPSTACK_ENV_LEVEL
          sourceContainerName: main
        - envName: APPSTACK_ENV_TAG
          sourceContainerName: main
        - envName: APPSTACK_REGION
          sourceContainerName: main
        - envName: APPSTACK_UNIT
          sourceContainerName: main
        - envName: APPSTACK_AZ
          sourceContainerName: main
#        - envName: MPP_PRODUCT
#          sourceContainerName: main
#        - envName: MPP_MODELID
#          sourceContainerName: main
#        - envName: MPP_TAG
#          sourceContainerName: main
#        - envName: MPP_AGENT_START_MODE
#          sourceContainerName: main
      env:
        - name: CSE_INSTANCE_ID
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.uid
        - name: CSE_NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: spec.nodeName
        - name: CSE_NODE_IP
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: CSE_POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: CSE_POD_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        - name: CSE_POD_IP
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        - name: APP_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.labels["sigma.ali/app-name"]
        - name: MPP_REGION
          value: cn-shanghai
        - name: MPP_RESTART_LOST_TASK
          value: 'false'
        - name: MPP_PRODUCT
          value: mps
        - name: MPP_MODELID
          value: mps-transcode-new
        - name: MPP_TAG
          value: beta
        - name: MPP_RESOURCE_TYPE
          value: cpu
        - name: MPP_ENGINE_QUOTASET
          value: '{"diskIO":5000,"diskBW":140000}'
        - name: MPP_ENGINE_URL
          value: 'http:\/\/127.0.0.1:8081'
        - name: MPP_EXIT_WAIT
          value: 'true'
        - name: MPP_SCHEDULE_URL
          value: 'http:\/\/*************:8080'
        - name: IS_SIDECAR
          value: 'true'
        - name: SIGMA_IGNORE_RESOURCE
          value: 'true'
        - name: MPP_COLLECT_PROCESS_CMD_PREFIX
          value: >-
           ffmpeg,ffprobe,ossutil,\/home\/<USER>\/arbiter,\/home\/<USER>\/databus,\/home\/<USER>\/mpp-worker-agent
        - name: MPP_COLLECT_DISK_PARENT_DIR
          value: \/disk1\/temp\/
        - name: MPP_COLLECT_FREQUENCY
          value: '5'
        - name: MPP_ENGINE_LABELS
          value: '{"arch":"x86"}'
        - name: MPP_ENGINE_MOUNT_PATH
          value: \/disk1\/temp
        - name: MPP_RESTART_LOST_TASK
          value: 'false'
        - name: K8S_CONTAINER_NAME
          value: agent
        - name: MPP_NOTIFY_URI
          value: mns://1426105496228119.mns.cn-shanghai.aliyuncs.com/queues/mpp-mps-task-progress-pre
      resources:
        limits:
          cpu: '1'
          memory: 2Gi
        requests:
          cpu: '1'
          memory: 2Gi
      volumeMounts:
        - name: plugins-dir
          mountPath: /home/<USER>/plugins
        - name: shared-tmp
          mountPath: /tmp
        - name: vol-dir-0
          mountPath: /disk1/temp
        - name: vol-dir-1
          mountPath: /home/<USER>/
        - name: vol-dir-2
          mountPath: /home/<USER>/logs/
        - name: vol-dir-3
          mountPath: /home/<USER>
        - name: vol-dir-4
          mountPath: /home/<USER>/logs/
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      imagePullPolicy: Always
  volumes:
    - name: shared-tmp
      emptyDir: {}
    - name: plugins-dir
      emptyDir: {}
    - name: cse-staragent-sn
      downwardAPI:
        items:
          - path: staragent_sn
            fieldRef:
              apiVersion: v1
              fieldPath: "metadata.labels['sigma.ali/sn']"
        defaultMode: 420
    - name: vol-dir-0
      emptyDir: {}
    - name: vol-dir-1
      emptyDir: {}
    - name: vol-dir-2
      emptyDir: {}
    - name: vol-dir-3
      emptyDir: {}
    - name: vol-dir-4
      emptyDir: {}
