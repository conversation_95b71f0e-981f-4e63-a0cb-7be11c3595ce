# sidecarset.yaml
apiVersion: apps.kruise.io/v1alpha1
kind: SidecarSet
metadata:
  name: alirms-translater-mpp-agent-sidecarset
spec:
  selector:
    matchLabels:
      mpp.aliyun.com/agent-sidecarset: "true"
      sigma.ali/app-name: "alirms-translater"
  updateStrategy:
    type: RollingUpdate
    paused: false
    maxUnavailable: 1
  containers:
    - name: agent
      image: mpp-pre-registry-vpc.cn-shanghai.cr.aliyuncs.com/mpp/mpp-agent:last-pre
      transferEnv:
        - envName: SIGMA_IGNORE_RESOURCE
          sourceContainerName: '*'
        - envName: SIGMA_APP_NAME
          sourceContainerName: '*'
        - envName: SIGMA_APP_UNIT
          sourceContainerName: '*'
        - envName: SIGMA_APP_STAGE
          sourceContainerName: '*'
        - envName: SIGMA_APP_GROUP
          sourceContainerName: '*'
        - envName: SIGMA_APP_SITE
          sourceContainerName: '*'
        - envName: APPSTACK_APP_NAME
          sourceContainerName: '*'
        - envName: APPSTACK_ENV_LEVEL
          sourceContainerName: '*'
        - envName: APPSTACK_ENV_TAG
          sourceContainerName: '*'
        - envName: APPSTACK_REGION
          sourceContainerName: '*'
        - envName: APPSTACK_UNIT
          sourceContainerName: '*'
        - envName: APPSTACK_AZ
          sourceContainerName: '*'
        - envName: SN
          sourceContainerName: '*'
        - envName: TZ
          sourceContainerName: '*'
        - envName: MPP_RESOURCE_TYPE
          sourceContainerName: '*'
        - envName: MPP_ENGINE_QUOTASET
          sourceContainerName: '*'
        - envName: MPP_EXIT_WAIT
          sourceContainerName: '*'
        - envName: MPP_CHECK_TERMINATING
          sourceContainerName: '*'
        - envName: MPP_REGION
          sourceContainerName: '*'
        - envName: MPP_SCHEDULE_URL
          sourceContainerName: '*'
        - envName: MPP_ENGINE_URL
          sourceContainerName: '*'
        - envName: MPP_PRODUCT
          sourceContainerName: '*'
        - envName: MPP_MODELID
          sourceContainerName: '*'
        - envName: MPP_TAG
          sourceContainerName: '*'
      env:
        - name: CSE_INSTANCE_ID
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.uid
        - name: CSE_NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: spec.nodeName
        - name: CSE_NODE_IP
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: CSE_POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: CSE_POD_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        - name: CSE_POD_IP
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        - name: APP_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.labels['sigma.ali/app-name']
        - name: K8S_CONTAINER_NAME
          value: agent
        - name: __ECI_RESOURCE_IGNORE__
          value: 'TRUE'
      resources:
        limits:
          cpu: '1'
          memory: 1Gi
        requests:
          cpu: '1'
          memory: 1Gi
      volumeMounts:
        - name: plugins-dir
          mountPath: /home/<USER>/plugins
        - name: shared-tmp
          mountPath: /tmp
        - name: vol-dir-0
          mountPath: /home/<USER>/disk
        - name: vol-dir-1
          mountPath: /home/<USER>/agent-pub/A
        - name: vol-dir-2
          mountPath: /home/<USER>/agent-pub/B
        - name: vol-dir-3
          mountPath: /home/<USER>/agent-pub/ver
      livenessProbe:
        exec:
          command:
            - /bin/sh
            - '-c'
            - /home/<USER>/health.sh
            - liveness
        initialDelaySeconds: 30
        timeoutSeconds: 2
        periodSeconds: 5
        successThreshold: 1
        failureThreshold: 3
      readinessProbe:
        exec:
          command:
            - /bin/sh
            - '-c'
            - /home/<USER>/health.sh
            - readiness
        initialDelaySeconds: 30
        timeoutSeconds: 2
        periodSeconds: 10
        successThreshold: 1
        failureThreshold: 3
      startupProbe:
        exec:
          command:
            - /bin/sh
            - '-c'
            - /home/<USER>/health.sh
            - startup
        initialDelaySeconds: 30
        timeoutSeconds: 2
        periodSeconds: 10
        successThreshold: 1
        failureThreshold: 99
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      imagePullPolicy: Always
  volumes:
    - name: shared-tmp
      emptyDir: {}
    - name: plugins-dir
      emptyDir: {}
    - name: cse-staragent-sn
      downwardAPI:
        items:
          - path: staragent_sn
            fieldRef:
              apiVersion: v1
              fieldPath: 'metadata.labels[''sigma.ali/sn'']'
        defaultMode: 420
    - name: vol-dir-0
      emptyDir: {}
    - name: vol-dir-1
      emptyDir: {}
    - name: vol-dir-2
      emptyDir: {}
    - name: vol-dir-3
      emptyDir: {}
    - name: vol-dir-4
      emptyDir: {}
    - name: vol-dir-5
      emptyDir: {}