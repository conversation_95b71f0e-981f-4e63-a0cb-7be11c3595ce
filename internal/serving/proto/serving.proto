syntax = "proto3";

package serving;

import "google/protobuf/any.proto";

// for grpc-gateway
import "google/api/annotations.proto";
//import "github.com/metaverse/truss/deftree/googlethirdparty/annotations.proto";

option cc_enable_arenas = true;
option go_package = "internal/serving/proto";
option java_multiple_files = true;
option java_outer_classname = "ServingProto";
option java_package = "com.aliyun.serving.v1";

message Status {
    // The status code, which should be an enum value of
    // [google.rpc.Code][google.rpc.Code].
    int32 code = 1;

    // A developer-facing error message, which should be in English. Any
    // user-facing error message should be localized and sent in the
    // [google.rpc.Status.details][google.rpc.Status.details] field, or localized
    // by the client.
    string message = 2;

    // A list of messages that carry the error details.  There is a common set of
    // message types for APIs to use.
    // https://github.com/googleapis/googleapis/blob/master/google/rpc/error_details.proto
    repeated google.protobuf.Any details = 3;
}

message HealthCheckRequest {
  string service = 1;
}

message HealthCheckResponse {
  enum ServingStatus {
    UNKNOWN = 0;
    SERVING = 1;
    NOT_SERVING = 2;
  }
  ServingStatus status = 1;
}

message PluginRequest {
    string service_id = 1;

    string model_id = 2;

    string transport = 3;

    string channel = 4;

    int32  channel_max = 5;
}

message PluginResponse {
    Status status = 1;
}

message MetaData {
    string app_key = 1;

    string task_id = 2;

    string session_id = 3;

    string user_id = 4;

    string model_id = 5;

    string service_id = 6;

    // async await promise callback
    string invoke_mode = 7;

    string notify_uri = 8;

    string group_id = 9;
}

message Progress {
    // 进度完成的百分比
    int32 percent = 1;

    // 流经的时间
    uint64 elapsed = 2;

    // 完成任务的剩余时间
    uint64 remaining = 3;

    // 预期的完成任务时间
    uint64 estimated = 4;

    // 附加信息
    string message  = 5;
}

enum Action {
    DEFAULT = 0;

    QUERY = 1;

    CANCEL = 2;
}

message ServingRequest {
    MetaData metadata = 1;

    Action action = 2;

    google.protobuf.Any ctx = 3;
}

message ServingResponse {
    MetaData metadata = 1;

    Status status = 2;

    Progress progress = 3;

    google.protobuf.Any ctx = 4;
}

message JSON {
    string str = 1;
}

service Serving {
    //rpc Register(PluginRequest) returns (PluginResponse);

    // about health check https://github.com/grpc/grpc/blob/master/doc/health-checking.md
    rpc Health(HealthCheckRequest) returns (HealthCheckResponse);

    rpc Invoke(ServingRequest) returns (ServingResponse) {
        option (google.api.http) = {
          post: "/v1/serving/invoke"
          body: "*"
       };
    }

    //rpc Invoke(stream ServingRequest) returns (stream ServingResponse);
}
