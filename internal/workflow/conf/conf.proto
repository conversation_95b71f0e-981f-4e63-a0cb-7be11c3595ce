syntax = "proto3";
package mpp.api.workflow;

option go_package = "mpp/internal/workflow/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Registry registry = 2;
  Data data = 3;
  Swagger swagger = 4;
  App app = 5;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;

  string tracingUrl = 3;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
    bool autoMigrate = 3;
  }
  message Redis {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration readTimeout = 3;
    google.protobuf.Duration writeTimeout = 4;
  }
  Database database = 1;
  Database visibility = 2;
  Redis redis = 3;
}

message App {
  string region = 1;
}

message Swagger {
  string enabled = 1;
}

message Registry {
  string endpoint = 1;
}