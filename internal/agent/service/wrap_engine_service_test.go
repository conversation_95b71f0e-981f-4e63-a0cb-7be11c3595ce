package service

import (
	"context"
	"fmt"
	"github.com/agiledragon/gomonkey/v2"
	"mpp/internal/agent/model"
	"mpp/internal/agent/pkg/utils"
	httpEngine "mpp/internal/agent/service/http"
	"mpp/internal/agent/types"
	"mpp/pkg/header"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/traefik/yaegi/interp"
	"github.com/traefik/yaegi/stdlib"
)

func TestWrapService(t *testing.T) {

	//importCode := `import "fmt"`
	//printCode := `fmt.Println("Hello world")`
	code := `
			 import "fmt"
             import "encoding/json"
             func run() string {
                fmt.Println("Hello world!")
                rst := map[string]any{"foo":"bar"}
                js, _ := json.Marshal(rst)
                return string(js)
             }
             `
	i := interp.New(interp.Options{})
	//var err error
	err := i.Use(stdlib.Symbols)
	assert.NoError(t, err)

	_, err = i.<PERSON>l(code)
	assert.NoError(t, err)

	val, err := i.Eval(`run`)
	assert.NoError(t, err)

	fmt.Println("****", val.Call(nil))

	ies := httpEngine.NewHttpEngineService("engineHost")
	s := NewWrapEngineService(ies)

	patches := gomonkey.ApplyFunc(utils.SendRequest, func(_ string, _ string, _ string, _ bool, _ context.Context) types.Result {
		return types.Result{
			Status:  200,
			Code:    "200",
			Message: "",
		}
	})
	defer patches.Reset()

	task := &model.Task{
		JobId:  header.HeaderShadow + "123",
		Params: code,
	}
	s.Start(task, "")
}
