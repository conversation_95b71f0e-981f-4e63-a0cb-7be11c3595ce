package service

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/cinience/animus/safe"
	"mpp/internal/agent/conf"
	"mpp/internal/agent/logs"
	"mpp/internal/agent/model"
	"mpp/internal/agent/pkg/utils"

	"github.com/shirou/gopsutil/process"
)

type EngineProcess struct {
	process        *process.Process
	cmdLine        string
	cmd            string
	lastUpdateTime int64
	createTime     int64
	pid            int32
	ppid           int32
	cpuQuota       float64
	memInfo        *process.MemoryInfoStat
	ioStat         *model.IOStat
}

func (p *EngineProcess) convertToProcessInfo() *model.ProcessInfo {
	return &model.ProcessInfo{
		CmdLine: p.cmdLine,
		Cmd:     p.cmd,
		Pid:     p.pid,
		Ppid:    p.ppid,
		Mem: model.ProcessMemInfo{
			RSS: p.memInfo.RSS >> 20,
			VMS: p.memInfo.VMS >> 20,
		},
		Cpu: model.ProcessCpuInfo{
			Cpu: p.cpuQuota,
		},
		IOStat:          p.ioStat,
		RunningDuration: p.lastUpdateTime - p.createTime,
		CreateTime:      p.createTime,
	}
}

type TaskQuotaCollectService struct {
	commandPrefix    []string
	taskParentDisk   string
	taskQuotaInfoMap map[string]*model.TaskRuntimeQuotaInfo
	pidProcessMap    map[int32]*EngineProcess

	lock *sync.Mutex
}

var TCS TaskQuotaCollectService
var NullQuotaSet = model.QuotaSet{}

func (t *TaskQuotaCollectService) Start() {
	t.lock = new(sync.Mutex)
	if conf.DefaultConfig.App.CollectFrequency < 0 {
		logs.Info("<EMAIL>, not collect")
		return
	}

	t.setupCommandPrefix()
	t.setupParentDisk()
	if t.commandPrefix != nil {
		safe.Go(func() {
			t.collect()
		})
	}
}

func (t *TaskQuotaCollectService) QueryTaskRealQuota(taskId string) (model.QuotaSet, bool) {
	t.lock.Lock()
	defer t.lock.Unlock()
	if t.taskQuotaInfoMap != nil {
		if quotaInfo, ok := t.taskQuotaInfoMap[taskId]; ok {
			return quotaInfo.RealQuotaSet, true
		}
	}
	return NullQuotaSet, false
}

func (t *TaskQuotaCollectService) setupCommandPrefix() {
	if conf.DefaultConfig.App.CollectProcessCmdPrefix != "" {
		t.commandPrefix = strings.Split(conf.DefaultConfig.App.CollectProcessCmdPrefix, ",")
	}
}

func (t *TaskQuotaCollectService) setupParentDisk() {
	if conf.DefaultConfig.App.CollectDiskParentDir != "" {
		t.taskParentDisk = conf.DefaultConfig.App.CollectDiskParentDir
	}
}

func (t *TaskQuotaCollectService) collect() {
	timer := time.NewTicker(time.Duration(conf.DefaultConfig.App.CollectFrequency) * time.Second)
	t.pidProcessMap = make(map[int32]*EngineProcess)
	defer timer.Stop()
	for {
		<-timer.C
		t.collectCycle()
	}
}

func (t *TaskQuotaCollectService) collectCycle() {
	defer func() {
		if r := recover(); r != nil {
			logs.Error("collectCycle error,", r)
			logs.Error("collectCycle error,", debug.Stack())
		}
	}()

	err := t.collectProcessInfo()
	if err != nil {
		logs.Error("fail@collectProcessInfo,", err)
	}

	diskInfos, err := t.collectDiskInfo()
	if err != nil {
		logs.Error("fail@collectDiskInfo", err)
	}

	t.constructTaskQuotaCollectInfo(diskInfos)
}

func (t *TaskQuotaCollectService) collectProcessInfo() error {
	procs, err := process.Processes()
	if err != nil {
		logs.Error("Failed to get processes. err:%v", err)
		return err
	}

	lastUpdateTime := time.Now().Unix()
	for _, proc := range procs {
		if proc.Pid == 1 {
			continue
		}

		pid := proc.Pid
		engineProcess, exist := t.pidProcessMap[pid]
		if exist {
			cpuQuota, memInfo, ioStat, err := getProcessInfo(proc)
			if err != nil {
				continue
			} else {
				engineProcess.cpuQuota = cpuQuota
				engineProcess.memInfo = memInfo
				engineProcess.lastUpdateTime = lastUpdateTime
				engineProcess.ioStat = ioStat
			}
		} else {
			cmdline, err := proc.Cmdline()
			if err != nil {
				continue
			}

			if !t.containTargetCmd(cmdline) {
				continue
			}
			ppid, err := proc.Ppid()
			if err != nil {
				continue
			}

			createTime, err := proc.CreateTime()
			if err != nil {
				continue
			}

			cpuQuota, memInfo, iostat, err := getProcessInfo(proc)
			if err != nil {
				continue
			} else {
				t.pidProcessMap[proc.Pid] = &EngineProcess{
					process:        proc,
					createTime:     createTime / 1000,
					lastUpdateTime: lastUpdateTime,
					cpuQuota:       cpuQuota,
					memInfo:        memInfo,
					pid:            pid,
					ppid:           ppid,
					cmdLine:        cmdline,
					ioStat:         iostat,
					cmd:            strings.Split(cmdline, " ")[0],
				}
			}
		}
	}

	//clear not exist pid info
	for i, engineProcess := range t.pidProcessMap {
		if engineProcess.lastUpdateTime < lastUpdateTime {
			delete(t.pidProcessMap, i)
		}
	}
	return nil
}

func getProcessInfo(process *process.Process) (float64, *process.MemoryInfoStat, *model.IOStat, error) {
	cpuPercent, err := process.Percent(100000000)
	//cpuPercent, err := proc.CPUPercent()
	if err != nil {
		return 0.0, nil, nil, err
	}
	cpuQuota, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", cpuPercent*10), 64)
	mem, err := process.MemoryInfo()
	if err != nil {
		return 0.0, nil, nil, err
	}

	ioStat, err := getProcessDiskIO(process)
	if err != nil {
		return 0.0, nil, nil, err
	}

	return cpuQuota, mem, ioStat, nil
}

func getProcessDiskIO(process *process.Process) (*model.IOStat, error) {
	preStats, err := process.IOCounters()
	if err != nil {
		return nil, err
	}
	//0.1 second
	time.Sleep(time.Millisecond * 100)

	curStats, err := process.IOCounters()
	if err != nil {
		return nil, err
	}

	// convert 0.1s to 1s
	ioStat := model.IOStat{
		ReadIO:  (curStats.ReadCount - preStats.ReadCount) * 10,
		WriteIO: (curStats.WriteCount - preStats.WriteCount) * 10,
		ReadBW:  (curStats.ReadBytes - preStats.ReadBytes) * 10 / model.KB,
		WriteBW: (curStats.WriteBytes - preStats.WriteBytes) * 10 / model.KB,
	}

	return &ioStat, nil
}

func (t *TaskQuotaCollectService) collectDiskInfo() (map[string]*model.TaskDiskInfo, error) {
	fileInfos, err := os.ReadDir(t.taskParentDisk)
	if err != nil {
		return nil, err
	}

	var taskDiskInfos = make(map[string]*model.TaskDiskInfo)
	for _, info := range fileInfos {
		if !info.IsDir() {
			continue
		}
		var size uint64
		path := t.taskParentDisk + info.Name()
		err := filepath.Walk(path, func(_ string, info os.FileInfo, err error) error {
			if info != nil && !info.IsDir() {
				size += uint64(info.Size())
			}
			return err
		})
		if err == nil {
			taskDiskInfo := &model.TaskDiskInfo{
				DIR:  path,
				Size: size >> 20,
			}
			taskDiskInfos[path] = taskDiskInfo
		}
	}
	return taskDiskInfos, nil
}

func (t *TaskQuotaCollectService) constructTaskQuotaCollectInfo(diskInfos map[string]*model.TaskDiskInfo) {
	taskQuotaInfoMap := t.makeTaskQuotaCollectInfoMap()
	pidToTaskIdMap, notTaskProcessInfos := t.makePidToTaskIdMap(taskQuotaInfoMap)
	dirToTaskIdMap, notTaskDiskInfos := t.makeDirToTaskIdMap(taskQuotaInfoMap, diskInfos)
	for dir, taskId := range dirToTaskIdMap {
		taskQuotaInfo := taskQuotaInfoMap[taskId]
		taskQuotaInfo.RealQuotaSet.Disk += diskInfos[dir].Size
		if taskQuotaInfo.Disks == nil {
			taskQuotaInfo.Disks = make(map[string]*model.TaskDiskInfo)
		}
		taskQuotaInfo.Disks[dir] = diskInfos[dir]
	}

	for pid, taskId := range pidToTaskIdMap {
		taskQuotaInfo := taskQuotaInfoMap[taskId]
		taskQuotaInfo.RealQuotaSet.Cpu += int32(t.pidProcessMap[pid].cpuQuota)
		taskQuotaInfo.RealQuotaSet.Mem += t.pidProcessMap[pid].memInfo.RSS >> 20
		if t.pidProcessMap[pid].ioStat != nil {
			taskQuotaInfo.RealQuotaSet.DiskBW += t.pidProcessMap[pid].ioStat.ReadBW + t.pidProcessMap[pid].ioStat.WriteBW
			taskQuotaInfo.RealQuotaSet.DiskIO += t.pidProcessMap[pid].ioStat.ReadIO + t.pidProcessMap[pid].ioStat.WriteIO
		}
		if taskQuotaInfo.Processes == nil {
			taskQuotaInfo.Processes = make(map[int32]*model.ProcessInfo)
		}
		taskQuotaInfo.Processes[pid] = t.pidProcessMap[pid].convertToProcessInfo()
	}

	t.updateTaskQuotaInfo(taskQuotaInfoMap)

	now := time.Now().Unix()
	for _, info := range t.taskQuotaInfoMap {
		utils.RuntimeCostLogger.LogTaskRuntimeCostInfo(now, info)
	}
	for _, info := range notTaskProcessInfos {
		utils.RuntimeCostLogger.LogNullTaskProcessCostInfo(now, info)
	}

	for _, info := range notTaskDiskInfos {
		utils.RuntimeCostLogger.LogNullTaskDiskCostInfo(now, info)
	}

}

func (t *TaskQuotaCollectService) makeTaskQuotaCollectInfoMap() map[string]*model.TaskRuntimeQuotaInfo {
	taskQuotaInfoMap := make(map[string]*model.TaskRuntimeQuotaInfo)
	TS.taskMap.Range(func(k, v interface{}) bool {
		key := k.(string)
		value := v.(*model.Task)
		taskQuotaInfo := &model.TaskRuntimeQuotaInfo{
			JobId:         value.JobId,
			UserId:        value.UserId,
			TaskId:        key,
			AllocQuotaSet: value.QuotaSet,
		}

		taskQuotaInfoMap[key] = taskQuotaInfo
		return true
	})
	return taskQuotaInfoMap
}

func (t *TaskQuotaCollectService) makePidToTaskIdMap(taskQuotaInfoMap map[string]*model.TaskRuntimeQuotaInfo) (map[int32]string, map[int32]*model.ProcessInfo) {
	result := make(map[int32]string)
	notTaskProcessInfos := make(map[int32]*model.ProcessInfo)
	for _, engineProcess := range t.pidProcessMap {
		var found = false
		for s := range taskQuotaInfoMap {
			if strings.Contains(engineProcess.cmdLine, s) {
				result[engineProcess.pid] = s
				found = true
				break
			}
		}
		if !found {
			notTaskProcessInfos[engineProcess.pid] = engineProcess.convertToProcessInfo()
		}
	}
	return result, notTaskProcessInfos
}

func (t *TaskQuotaCollectService) makeDirToTaskIdMap(taskQuotaInfoMap map[string]*model.TaskRuntimeQuotaInfo, diskInfos map[string]*model.TaskDiskInfo) (map[string]string, map[string]*model.TaskDiskInfo) {
	result := make(map[string]string)
	notTaskDiskInfos := make(map[string]*model.TaskDiskInfo)
	for _, info := range diskInfos {
		var found = false
		for s := range taskQuotaInfoMap {
			if strings.Contains(info.DIR, s) {
				result[info.DIR] = s
				found = true
				break
			}
		}
		if !found {
			notTaskDiskInfos[info.DIR] = info
		}
	}
	return result, notTaskDiskInfos
}

func (t *TaskQuotaCollectService) updateTaskQuotaInfo(temp map[string]*model.TaskRuntimeQuotaInfo) {
	t.lock.Lock()
	defer t.lock.Unlock()
	t.taskQuotaInfoMap = temp
}

func (t *TaskQuotaCollectService) containTargetCmd(cmdline string) bool {
	if t.commandPrefix == nil {
		return false
	}
	for _, cmdName := range t.commandPrefix {
		if strings.HasPrefix(cmdline, cmdName) {
			return true
		}
	}
	return false
}
