// Package executor provides process execution and management functionality.
// This package is organized into several modules:
//
// - core: Basic execution functionality (ExecutorManager, NohupExecTask)
// - pool: Process pooling functionality (PooledExecutorManager)
// - cgroup: Cgroup-based process isolation (CgroupNohupExecTask)
//
// For backward compatibility, all main types and functions are re-exported
// from this root package.
package executor

// Re-export core types and functions for backward compatibility
import (
	"mpp/internal/agent/pkg/executor/cgroup"
	"mpp/internal/agent/pkg/executor/core"
	"mpp/internal/agent/pkg/executor/manager/general"
	"mpp/internal/agent/pkg/executor/manager/pool"
	"mpp/internal/agent/pkg/executor/nohup"
)

// Core executor types and functions
type (
	// Options represents process execution options
	Options = core.Options

	// ExecutorManager manages process execution
	ExecutorManager = general.ExecutorManager

	// ExecTask represents an executable task
	ExecTask = core.ExecTask

	// NohupExecTask represents a nohup executable task
	NohupExecTask = nohup.NohupExecTask
)

// Core executor functions
var (
	// NewExecutorManager creates a new executor manager
	NewExecutorManager = general.NewExecutorManager

	// NewNohupExecTask creates a new nohup exec task
	NewNohupExecTask = nohup.NewNohupExecTask
)

// Pool types and functions
type (
	// PooledExecutorManager manages a pool of processes
	PooledExecutorManager = pool.PooledExecutorManager

	// PoolConfig represents pool configuration
	PoolConfig = pool.PoolConfig

	// PoolStats represents pool statistics
	PoolStats = pool.PoolStats

	// PooledProcess represents a pooled process
	PooledProcess = pool.PooledProcess

	// ProcessState represents process state
	ProcessState = pool.ProcessState
)

// Pool functions
var (
	// NewPooledExecutorManager creates a new pooled executor manager
	NewPooledExecutorManager = pool.NewPooledExecutorManager

	// DefaultPoolConfig returns default pool configuration
	DefaultPoolConfig = pool.DefaultPoolConfig
)

// Pool constants
const (
	ProcessStateIdle        = pool.ProcessStateIdle
	ProcessStateInUse       = pool.ProcessStateInUse
	ProcessStateTerminating = pool.ProcessStateTerminating
)

// Cgroup types and functions
type (
	// CgroupNohupExecTask represents a cgroup-based nohup exec task
	CgroupNohupExecTask = cgroup.CgroupNohupExecTask

	// CgroupConfig represents cgroup configuration
	CgroupConfig = cgroup.CgroupConfig

	// MockCgroupStats represents mock cgroup statistics (for non-Linux systems)
	MockCgroupStats = cgroup.MockCgroupStats
)

// Cgroup functions
var (
	// NewCgroupNohupExecTask creates a new cgroup nohup exec task
	NewCgroupNohupExecTask = cgroup.NewCgroupNohupExecTask

	// DefaultCgroupConfig returns default cgroup configuration
	DefaultCgroupConfig = cgroup.DefaultCgroupConfig
)
