package cgroup

import (
	"context"
	"runtime"
	"testing"
	"time"

	"github.com/google/uuid"
	"mpp/internal/agent/pkg/executor/core"
)

func TestCgroupNohupExecTask_CrossPlatform(t *testing.T) {
	id := uuid.NewString()
	options := core.Options{
		Path: "echo",
		Args: []string{"hello", "world"},
	}
	
	config := &CgroupConfig{
		CgroupPrefix: "test-cross-platform",
		MemoryLimit:  100 * 1024 * 1024, // 100MB
		CPUQuota:     50000,              // 50% CPU
		PidsLimit:    10,                 // 最多10个进程
	}
	
	task := NewCgroupNohupExecTask(id, options, config)
	defer task.cleanupCgroup()
	
	// 检查平台信息
	platformInfo := task.GetPlatformInfo()
	t.Logf("Platform info: %+v", platformInfo)
	
	// 检查是否为mock模式
	isMock := task.IsMockMode()
	if runtime.GOOS == "linux" {
		if isMock {
			t.Error("Expected non-mock mode on Linux")
		}
	} else {
		if !isMock {
			t.Error("Expected mock mode on non-Linux systems")
		}
	}
	
	// 执行进程
	pid, err := task.Execute()
	if err != nil {
		t.Fatalf("Failed to execute process: %v", err)
	}
	
	if pid <= 0 {
		t.Error("Expected positive PID")
	}
	
	t.Logf("Process started with PID: %d (mock mode: %v)", pid, isMock)
	
	// 获取cgroup信息
	info := task.GetCgroupInfo()
	t.Logf("Cgroup info: %+v", info)
	
	// 验证mock模式标识
	if mockMode, exists := info["mock_mode"]; exists {
		if runtime.GOOS != "linux" && !mockMode.(bool) {
			t.Error("Expected mock_mode to be true on non-Linux systems")
		}
		if runtime.GOOS == "linux" && mockMode.(bool) {
			t.Error("Expected mock_mode to be false on Linux systems")
		}
	}
	
	// 获取统计信息
	stats, err := task.GetCgroupStats()
	if err != nil {
		t.Errorf("Failed to get cgroup stats: %v", err)
	} else {
		t.Logf("Cgroup stats type: %T", stats)
		
		// 在非Linux系统上，stats应该是MockCgroupStats类型
		if runtime.GOOS != "linux" {
			if mockStats, ok := stats.(*MockCgroupStats); ok {
				t.Logf("Mock stats - Memory usage: %d, CPU usage: %d", 
					mockStats.Memory.Usage, mockStats.CPU.UsageUsec)
			} else {
				t.Error("Expected MockCgroupStats on non-Linux systems")
			}
		}
	}
	
	// 测试动态更新限制
	newConfig := &CgroupConfig{
		MemoryLimit: 200 * 1024 * 1024, // 200MB
		CPUQuota:    75000,              // 75% CPU
		PidsLimit:   20,                 // 20个进程
	}
	
	err = task.UpdateCgroupLimits(newConfig)
	if err != nil {
		t.Errorf("Failed to update cgroup limits: %v", err)
	} else {
		t.Logf("Successfully updated cgroup limits (mock mode: %v)", isMock)
	}
	
	// 等待进程完成
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	err = task.Wait(ctx)
	if err != nil {
		t.Errorf("Failed to wait for process: %v", err)
	} else {
		t.Logf("Process completed successfully")
	}
}

func TestCgroupConfig_CrossPlatform(t *testing.T) {
	// 测试默认配置
	config := DefaultCgroupConfig()
	
	if config == nil {
		t.Fatal("DefaultCgroupConfig returned nil")
	}
	
	if config.CgroupRoot != "/sys/fs/cgroup" {
		t.Errorf("Expected CgroupRoot to be '/sys/fs/cgroup', got %s", config.CgroupRoot)
	}
	
	if config.CgroupPrefix != "mpp-executor" {
		t.Errorf("Expected CgroupPrefix to be 'mpp-executor', got %s", config.CgroupPrefix)
	}
	
	t.Logf("Default config created successfully on %s", runtime.GOOS)
}

func TestCgroupNohupExecTask_PlatformSpecificBehavior(t *testing.T) {
	id := uuid.NewString()
	options := core.Options{
		Path: "sleep",
		Args: []string{"1"},
	}
	
	task := NewCgroupNohupExecTask(id, options, nil)
	defer task.cleanupCgroup()
	
	// 测试平台特定行为
	platformInfo := task.GetPlatformInfo()
	
	if runtime.GOOS == "linux" {
		// Linux系统应该有真实的cgroup支持检查
		if platform, ok := platformInfo["platform"]; !ok || platform != "linux" {
			t.Error("Expected platform to be 'linux' on Linux systems")
		}
		
		if mockMode, ok := platformInfo["mock_mode"]; !ok || mockMode.(bool) {
			t.Error("Expected mock_mode to be false on Linux systems")
		}
		
		// 检查cgroup2支持
		if _, ok := platformInfo["cgroup2_enabled"]; !ok {
			t.Error("Expected cgroup2_enabled field on Linux systems")
		}
	} else {
		// 非Linux系统应该是mock模式
		if platform, ok := platformInfo["platform"]; !ok || platform != "non-linux" {
			t.Error("Expected platform to be 'non-linux' on non-Linux systems")
		}
		
		if mockMode, ok := platformInfo["mock_mode"]; !ok || !mockMode.(bool) {
			t.Error("Expected mock_mode to be true on non-Linux systems")
		}
		
		if cgroupSupport, ok := platformInfo["cgroup_support"]; !ok || cgroupSupport.(bool) {
			t.Error("Expected cgroup_support to be false on non-Linux systems")
		}
	}
	
	t.Logf("Platform-specific behavior verified for %s", runtime.GOOS)
}

func TestCgroupNohupExecTask_MockFunctionality(t *testing.T) {
	// 这个测试主要验证mock实现的功能
	if runtime.GOOS == "linux" {
		t.Skip("Skipping mock functionality test on Linux")
	}
	
	id := uuid.NewString()
	options := core.Options{
		Path: "echo",
		Args: []string{"mock", "test"},
	}
	
	config := &CgroupConfig{
		CgroupPrefix: "mock-test",
		MemoryLimit:  50 * 1024 * 1024, // 50MB
		CPUQuota:     30000,             // 30% CPU
		PidsLimit:    5,                 // 5个进程
	}
	
	task := NewCgroupNohupExecTask(id, options, config)
	defer task.cleanupCgroup()
	
	// 验证mock模式
	if !task.IsMockMode() {
		t.Error("Expected mock mode on non-Linux systems")
	}
	
	// 执行进程
	pid, err := task.Execute()
	if err != nil {
		t.Fatalf("Failed to execute process in mock mode: %v", err)
	}
	
	t.Logf("Mock process started with PID: %d", pid)
	
	// 等待一小段时间让mock统计信息更新
	time.Sleep(100 * time.Millisecond)
	
	// 获取mock统计信息
	stats, err := task.GetCgroupStats()
	if err != nil {
		t.Fatalf("Failed to get mock stats: %v", err)
	}
	
	mockStats, ok := stats.(*MockCgroupStats)
	if !ok {
		t.Fatalf("Expected MockCgroupStats, got %T", stats)
	}
	
	// 验证mock统计信息
	if mockStats.Memory.Usage == 0 {
		t.Error("Expected non-zero mock memory usage")
	}
	
	if mockStats.CPU.UsageUsec == 0 {
		t.Error("Expected non-zero mock CPU usage")
	}
	
	if mockStats.Pids.Current == 0 {
		t.Error("Expected non-zero mock pids current")
	}
	
	t.Logf("Mock stats - Memory: %d, CPU: %d, Pids: %d", 
		mockStats.Memory.Usage, mockStats.CPU.UsageUsec, mockStats.Pids.Current)
	
	// 测试mock的动态更新
	newConfig := &CgroupConfig{
		MemoryLimit: 100 * 1024 * 1024,
		CPUQuota:    60000,
		PidsLimit:   10,
	}
	
	err = task.UpdateCgroupLimits(newConfig)
	if err != nil {
		t.Errorf("Failed to update mock cgroup limits: %v", err)
	}
	
	// 测试mock的进程移动
	err = task.MoveToCgroup(12345)
	if err != nil {
		t.Errorf("Failed to move process to mock cgroup: %v", err)
	}
	
	// 测试mock的进程列表
	procs, err := task.GetCgroupProcs()
	if err != nil {
		t.Errorf("Failed to get mock cgroup processes: %v", err)
	} else {
		t.Logf("Mock cgroup processes: %v", procs)
	}
	
	// 等待进程完成
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	err = task.Wait(ctx)
	if err != nil {
		t.Errorf("Failed to wait for mock process: %v", err)
	}
	
	t.Logf("Mock functionality test completed successfully")
}

// BenchmarkCgroupNohupExecTask_CrossPlatform 跨平台基准测试
func BenchmarkCgroupNohupExecTask_CrossPlatform(b *testing.B) {
	options := core.Options{
		Path: "true", // 快速退出的命令
		Args: []string{},
	}
	
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		id := uuid.NewString()
		task := NewCgroupNohupExecTask(id, options, nil)
		
		_, err := task.Execute()
		if err != nil {
			b.Fatalf("Failed to execute process: %v", err)
		}
		
		// 等待进程结束并清理
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
		task.Wait(ctx)
		cancel()
		
		task.cleanupCgroup()
	}
	
	b.Logf("Benchmark completed on %s (mock mode: %v)", 
		runtime.GOOS, runtime.GOOS != "linux")
}
