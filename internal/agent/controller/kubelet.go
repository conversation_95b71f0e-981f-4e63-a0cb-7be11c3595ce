package controller

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"mpp/internal/agent/logs"
	"mpp/internal/agent/model"
	"mpp/internal/agent/pkg/utils"
	"mpp/internal/agent/service/mecha"
	"mpp/internal/agent/service/mecha/engine"
	"mpp/internal/agent/types"
)

type PodsServerController struct {
	imagePrefix       string
	imageNameSpace    string
	kubeletConfigUtil *utils.KubeletConfigUtil
	kubeletCriService *mecha.KubeletCriService
}

func NewPodsServerController(configUrl string, engineOnceInstance func() *engine.KubeletMechaEngine) *PodsServerController {
	var parsedUrl *url.URL
	if strings.Contains(configUrl, "diamond") {
		configUrl = strings.Replace(configUrl, "mecha"+"://", "diamond://", -1)
		parsedUrl, _ = url.Parse(configUrl)
		logs.Info("configUrl:%s", configUrl)
	} else {
		logs.Errorf("unknown config type. engineHost:%s", configUrl)
		panic(fmt.Errorf("unknown config type, engineHost:%s", configUrl))
	}
	kubeletConfigUtil := utils.NewKubeletConfigUtil(*parsedUrl)
	p := &PodsServerController{
		kubeletConfigUtil: kubeletConfigUtil,
		kubeletCriService: mecha.NewKubeletCriService(kubeletConfigUtil, engineOnceInstance),
	}
	//go p.kubeletCriService.LoadConfigAndStartCRIO()
	return p
}

func (p *PodsServerController) sendError(w http.ResponseWriter, err error) {
	rst := types.ERROR_HTTP_ERR
	rst.Message = err.Error()
	p.sendResult(w, &rst)
}

func (p *PodsServerController) sendResult(w http.ResponseWriter, result *types.Result) {
	if result.Status > 0 {
		w.WriteHeader(result.Status)
	}
	b, err := json.Marshal(result)
	if err == nil {
		w.Write(b)
	} else {
		w.WriteHeader(http.StatusInternalServerError)
	}
}

func (p *PodsServerController) InnerLoadMechaPodOnCri(w http.ResponseWriter, r *http.Request) {
	err := p.kubeletCriService.LoadMechaPod()
	if err != nil {
		logs.Errorf("LoadPodOnCri error: %v", err)
		p.sendError(w, err)
		return
	}
	p.sendResult(w, &types.Result{
		Code:    types.RESULT_SUCCESS.Code,
		Message: "success",
	})
}

func (p *PodsServerController) InnerMechaWorkerInactive(w http.ResponseWriter, r *http.Request) {
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		p.sendError(w, err)
		return
	}
	var request model.MechaWorkerInactiveRequest
	if err := json.Unmarshal(bodyBytes, &request); err != nil {
		p.sendError(w, err)
		return
	}
	err = p.kubeletCriService.InactiveMechaWorker(request)
	if err != nil {
		p.sendError(w, err)
		return
	}
	p.sendResult(w, &types.Result{
		Code:    types.RESULT_SUCCESS.Code,
		Message: "success",
	})
}
