package controller

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mpp/pkg/metrics"
	"net/http"
	"time"

	commonv1 "proto.mpp/api/common/v1"
	v1TaskManager "proto.mpp/api/taskmanager/v1"
	agentv1 "proto.mpp/api/workeragent/v1"

	"mpp/internal/agent/logs"
	"mpp/internal/agent/model"
	"mpp/internal/agent/pkg/base"
	"mpp/internal/agent/pkg/utils"
	"mpp/internal/agent/service"
	"mpp/internal/agent/types"
)

type InvokeServer struct {
	server *http.Server
}

func NewInvokeServer() *InvokeServer {
	return &InvokeServer{}
}

func (s *InvokeServer) Run() error {
	mux := http.NewServeMux()
	mux.HandleFunc("/task/sync/invoke", InnerInvoke)
	s.server = &http.Server{
		Addr:    base.GetInvokeServerAddress(),
		Handler: mux,
	}
	return s.server.ListenAndServe()
}

func (s *InvokeServer) Stop() {
	s.server.Shutdown(context.Background())
}

func InnerInvoke(w http.ResponseWriter, r *http.Request) {
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	var request model.InvokeTaskRequest
	err = json.Unmarshal(body, &request)
	if nil != err {
		InnerSendResult(w, &types.ERROR_PARAM)
	} else {
		ctx := utils.TaskContext(request.JobId, request.JobId, request.RequestId, nil)
		result := service.TS.Invoke(ctx, &request)
		InnerSendResult(w, &result)
	}
}

func InnerWorkerInactiveHandler(w http.ResponseWriter, r *http.Request) {
	result := service.WS.SetManualInActive(true)
	InnerSendResult(w, &result)
}

func InnerWorkerActiveHandler(w http.ResponseWriter, r *http.Request) {
	result := service.WS.SetManualInActive(false)
	InnerSendResult(w, &result)
}

func InnerWorkerOfflineHandler(w http.ResponseWriter, r *http.Request) {
	result := service.WS.SetManualOffline(true)
	InnerSendResult(w, &result)
}

func InnerWorkerOnlineHandler(w http.ResponseWriter, r *http.Request) {
	result := service.WS.SetManualOffline(false)
	InnerSendResult(w, &result)
}

func InnerMetricsHandler(w http.ResponseWriter, r *http.Request) {
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	var request agentv1.SetMetricsRequest
	err = json.Unmarshal(body, &request)
	if nil != err {
		internalErrorResult := &agentv1.SetMetricsReply{
			RequestId: request.RequestId,
			Result: &commonv1.CommonReplyResult{
				Code:    500,
				Message: "internal error",
				Reason:  "unexpected error: " + err.Error(),
			},
		}
		InnerSendResultCommon(w, internalErrorResult, 500)
	} else {
		ctx := utils.RequestContext(request.RequestId)
		result, err := service.WS.SetMetrics(ctx, &request)
		if nil != err {
			InnerSendResultCommon(w, result, 500)
		} else {
			InnerSendResultCommon(w, result, result.Result.Code)
		}
	}
}

func InnerCompleteHandler(w http.ResponseWriter, r *http.Request) {
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	var request model.JobCompleteRequest
	err = json.Unmarshal(body, &request)
	if nil != err {
		InnerSendResult(w, &types.ERROR_PARAM)
	} else {
		result := service.TS.Complete(&request)
		InnerSendResult(w, &result)
	}
}

func InnerLiveness(w http.ResponseWriter, r *http.Request) {
	InnerSendResult(w, &types.RESULT_SUCCESS)
}

func InnerReadiness(w http.ResponseWriter, r *http.Request) {
	if service.TS.IsReady {
		InnerSendResult(w, &types.RESULT_SUCCESS)
	} else {
		InnerSendResult(w, &types.ERROR_SERVICE_NO_READY)
	}
}

func InnerSystemLoadHandler(w http.ResponseWriter, r *http.Request) {
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	var request model.SystemLoadRequest
	err = json.Unmarshal(body, &request)
	if nil != err {
		InnerSendResult(w, &types.ERROR_PARAM)
	} else {
		result := service.WS.SystemLoad(&request)
		InnerSendResult(w, &result)
	}
}

func InnerInformationHandler(w http.ResponseWriter, r *http.Request) {
	data := map[string]string{
		"version":  base.GetVersion(),
		"commitId": base.GetCommitID(),
	}
	result := types.RESULT_SUCCESS
	result.Data = data

	InnerSendResult(w, &result)
}

func Version(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
	_, err := w.Write([]byte(base.GetVersion()))
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
	}
}

func InnerSendResult(w http.ResponseWriter, result *types.Result) {
	if result.Status > 0 {
		w.WriteHeader(result.Status)
	}

	b, err := json.Marshal(result)
	if err == nil {
		w.Write(b)
	} else {
		http.Error(w, err.Error(), http.StatusInternalServerError)
	}
}
func InnerSendResultCommon(w http.ResponseWriter, result interface{}, status uint32) {
	w.WriteHeader(int(status))
	b, err := json.Marshal(result)
	if err == nil {
		w.Write(b)
	} else {
		http.Error(w, err.Error(), http.StatusInternalServerError)
	}
}

func InnerVersion(w http.ResponseWriter, r *http.Request) {
	var result types.Result = types.RESULT_SUCCESS
	result.Message = fmt.Sprintf("version: %v", base.GetVersion())
	InnerSendResult(w, &result)
}

func InnerTaskInfo(w http.ResponseWriter, r *http.Request) {
	taskId := r.URL.Query().Get("taskId")
	rst := service.TS.Check(&model.CheckTaskRequest{
		RequestId: "",
		TaskId:    taskId,
	})
	InnerSendResult(w, &rst)
}

func InnerPprof(w http.ResponseWriter, r *http.Request) {
	metrics.SetupMetricsServer()
}

func InnerMeshConfigGetHandler(w http.ResponseWriter, r *http.Request) {
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	var request v1TaskManager.MeshGetConfigRequest
	err = json.Unmarshal(body, &request)
	if nil != err {
		InnerSendResult(w, &types.ERROR_PARAM)
	} else {
		result := service.MS.GetConfig(context.TODO(), &request)
		InnerSendResult(w, &result)
	}
}

// InnerProgressHandler receive task progress message from engine and send it to scheduler(tc by now)
func InnerProgressHandler(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	body, err := io.ReadAll(r.Body)
	if err != nil {
		logs.Error(err)
	}
	utils.TraceLog.LogApiLog(utils.LOG_EVENT_API_IN, 0, r.RequestURI, string(body), types.RESULT_SUCCESS.Code, "", "",
		utils.LL_INFO)
	var request model.NotifyTaskRequest
	err = json.Unmarshal(body, &request)
	if nil != err {
		InnerSendResult(w, &types.ERROR_PARAM)
		utils.TraceLog.LogApiLog(utils.LOG_EVENT_API_OUT, time.Since(startTime).Milliseconds(), r.RequestURI,
			string(body), types.ERROR_PARAM.Code, err.Error(), "", utils.LL_WARN)
	} else {
		result := service.TS.Progress(&request)
		InnerSendResult(w, &result)
		b, _ := json.Marshal(result)
		utils.TraceLog.LogApiLog(utils.LOG_EVENT_API_OUT, time.Since(startTime).Milliseconds(), r.RequestURI,
			string(body), types.RESULT_SUCCESS.Code, result.Message, string(b), utils.LL_INFO)
	}
}
