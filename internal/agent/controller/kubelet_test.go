package controller

import (
	"fmt"
	"net/url"
	"os"
	"strings"
	"testing"

	"github.com/cinience/animus/contribs/config/diamond"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/pelletier/go-toml"
	"github.com/shirou/gopsutil/process"
	"github.com/stretchr/testify/assert"
	"mpp/internal/agent/model"
)

func TestMockKubeletController(t *testing.T) {
}

//func TestUrlParse(t *testing.T) {
//	cs := diamond.NewConfigSourceWithURL("diamond://jmenv.tbsite.net:8080/diamond-server/diamond?appName=mpp-agent&group=crio&dataId=*")
//	ks, err := cs.Load()
//	if err != nil {
//		t.Fatal(err)
//	}
//	for k, v := range ks {
//		t.Log(k, string(v.Value))
//	}
//}

func TestConfigWriteToToml(t *testing.T) {
	var cc model.CRIOConfig
	c := config.New(
		config.WithSource(diamond.NewConfigSourceWithURL("diamond://jmenv.tbsite.net:8080/diamond-server/diamond?appName=mpp-agent&group=crio&dataId=crio.json")),
	)
	defer c.Close()

	err := c.Load()
	assert.Nil(t, err)

	err = c.Scan(&cc)
	assert.Nil(t, err)
	crioToml := &model.CRIOTomlConfig{
		Crio: model.CrioToml{
			RuntimeToml: model.RuntimeToml{
				ConmonCgroup:  cc.Crio.Runtime.ConmonCgroup,
				CgroupManager: cc.Crio.Runtime.CgroupManager,
			},
			ImageToml: model.ImageToml{
				PauseImage:         cc.Crio.Image.PauseImage,
				PauseImageAuthFile: cc.Crio.Image.PauseImageAuthFile,
			},
		},
	}
	// 指定文件路径
	filePath := "./crio.conf"

	// 序列化配置为 TOML 格式的字符串
	tomlContent, err := toml.Marshal(crioToml)

	// 写入文件
	err = os.WriteFile(filePath, tomlContent, 0644)
}

func TestProcessCmd(t *testing.T) {
	// 获取所有进程
	processes, err := process.Processes()
	if err != nil {
		t.Fatal(err)
	}

	for _, p := range processes {
		// 获取进程的命令行
		cmdline, err := p.Cmdline()
		if err != nil {
			continue
		}

		// 检查命令行是否包含指定的进程名
		if strings.Contains(cmdline, "zsh") {
			t.Log("zsh")
		}
	}
	t.Log("end")
}

func TestParse(t *testing.T) {
	u := "diamond://pre-jmenv.cn-shanghai.aliyun-inc.com:8080/diamond-server/diamond?group=compose&dataId=application.json"
	parsedUrl, _ := url.Parse(u)
	fmt.Println(parsedUrl.Host + parsedUrl.Path)
}

func TestUrlFunc(t *testing.T) {
	configUrl := "mecha://pre-jmenv.cn-shanghai.aliyun-inc.com:8080/diamond-server/diamond?group=compose&dataId=application.json"
	configUrl = strings.Replace(configUrl, "mecha"+"://", "diamond://", -1)
	parsedUrl, _ := url.Parse(configUrl)
	v := parsedUrl.Query()
	v.Set("group", "foo")
	v.Set("dataId", "redis.yaml")
	parsedUrl.RawQuery = v.Encode()
	t.Log(parsedUrl.String())
	v = parsedUrl.Query()
	v.Set("appName", "mpp-agent")
	v.Set("group", "crio")
	v.Set("dataId", "crio.conf")
	parsedUrl.RawQuery = v.Encode()
	t.Log(parsedUrl.String())
}
