package controller

import (
	"encoding/json"
	"io"
	"net/http"
	"strings"

	"mpp/internal/agent/conf"
	"mpp/internal/agent/model"
	"mpp/internal/agent/pkg/utils"
	"mpp/internal/agent/service"
	"mpp/internal/agent/types"
)

type TaskServerController struct {
}

func NewTaskServerController() *TaskServerController {
	return &TaskServerController{}
}

func (t *TaskServerController) sendError(w http.ResponseWriter, err error) {
	rst := types.ERROR_HTTP_ERR
	rst.Message = err.Error()
	t.sendResult(w, &rst)
}

func (t *TaskServerController) sendResult(w http.ResponseWriter, result *types.Result) {
	if result.Status > 0 {
		w.WriteHeader(result.Status)
	}
	b, err := json.Marshal(result)
	if err == nil {
		w.Write(b)
	} else {
		w.WriteHeader(http.StatusInternalServerError)
	}
}

func (t *TaskServerController) Auth(w http.ResponseWriter, r *http.Request) types.Result {
	if !conf.DefaultConfig.App.IsAuth {
		return types.RESULT_SUCCESS
	}

	raw := r.Header.Get("Authorization")

	if raw == "" {
		return types.ERROR_AUTH
	}
	const key = "c895beb520125bba7b415a637b846eb9"
	if raw == key {
		return types.RESULT_SUCCESS
	}

	infos := strings.Split(raw, "-")
	if len(infos) != 3 {
		return types.ERROR_AUTH
	}
	//expireTime, _ := strconv.ParseInt(infos[0], 10, 64)
	//if time.Now().Unix() > expireTime {
	//	logs.Warn("uri:%v head:%v client:%v, time:%v", t.Ctx.Request.RequestURI, raw, expireTime)
	//	return utils.ERROR_AUTH
	//}
	//
	//urlPath := r.URL.Path
	//
	//authRaw := fmt.Sprintf("%s-%d-%s-%s-%s", urlPath, expireTime, infos[1],
	//	string(t.Ctx.Input.RequestBody), config.AUTH_SECRET)
	//
	//authEnc := fmt.Sprintf("%x", md5.Sum([]byte(authRaw)))
	//
	//if authEnc != infos[2] {
	//	logs.Warn("uri:%v head:%v service:%v, client:%v, src:%v", t.Ctx.Request.RequestURI, raw, authEnc, infos[2],
	//		authRaw)
	//
	//	return utils.ERROR_AUTH
	//}

	return types.RESULT_SUCCESS
}

func (t *TaskServerController) SyncInvoke(w http.ResponseWriter, r *http.Request) {
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		t.sendError(w, err)
		return
	}

	var request model.InvokeTaskRequest
	if err := json.Unmarshal(bodyBytes, &request); err != nil {
		t.sendError(w, err)
		return
	}

	//result := RESULT_SUCCESS
	result := t.Auth(w, r)
	if result.Code != types.RESULT_SUCCESS.Code {
		t.sendResult(w, &result)
	} else if nil != err {
		t.sendResult(w, &types.ERROR_PARAM)
	} else {
		ctx := utils.TaskContext(request.JobId, request.JobId, request.RequestId, nil)
		result := service.TS.Invoke(ctx, &request)
		t.sendResult(w, &result)
	}
}

func (t *TaskServerController) Start(w http.ResponseWriter, r *http.Request) {
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		t.sendError(w, err)
		return
	}
	var request model.StartTaskRequest
	err = json.Unmarshal(bodyBytes, &request)
	if err != nil {
		t.sendError(w, err)
		return
	}

	result := t.Auth(w, r)
	if result.Code != types.RESULT_SUCCESS.Code {
		t.sendResult(w, &result)
	} else if nil != err {
		t.sendResult(w, &types.ERROR_PARAM)
	} else {
		ctx := utils.TaskContext(request.JobId, request.TaskId, request.RequestId, nil)
		result := service.TS.Start(ctx, &request)
		t.sendResult(w, &result)
	}
}

func (t *TaskServerController) Submit(w http.ResponseWriter, r *http.Request) {
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		t.sendError(w, err)
		return
	}
	var request model.StartTaskRequest
	if err := json.Unmarshal(bodyBytes, &request); err != nil {
		t.sendError(w, err)
		return
	}

	result := t.Auth(w, r)
	if result.Code != types.RESULT_SUCCESS.Code {
		t.sendResult(w, &result)
	} else if nil != err {
		t.sendResult(w, &types.ERROR_PARAM)
	} else {
		ctx := utils.TaskContext(request.JobId, request.TaskId, request.RequestId, nil)
		result := service.TS.Submit(ctx, &request)
		t.sendResult(w, &result)
	}
}

func (t *TaskServerController) BatchStart(w http.ResponseWriter, r *http.Request) {
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		t.sendError(w, err)
		return
	}
	var request model.BatchStartTaskRequest
	if err := json.Unmarshal(bodyBytes, &request); err != nil {
		t.sendError(w, err)
		return
	}
	result := t.Auth(w, r)
	if result.Code != types.RESULT_SUCCESS.Code {
		t.sendResult(w, &result)
	} else if nil != err {
		t.sendResult(w, &types.ERROR_PARAM)
	} else {
		result := service.TS.BatchStart(&request)
		t.sendResult(w, &result)
	}
}

func (t *TaskServerController) Stop(w http.ResponseWriter, r *http.Request) {
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		t.sendError(w, err)
		return
	}
	var request model.StopTaskRequest
	if err := json.Unmarshal(bodyBytes, &request); err != nil {
		t.sendError(w, err)
		return
	}
	result := t.Auth(w, r)
	if result.Code != types.RESULT_SUCCESS.Code {
		t.sendResult(w, &result)
		// todo: add trace logs
	} else if nil != err {
		t.sendResult(w, &types.ERROR_PARAM)
		// todo: add trace logs
	} else {
		ctx := utils.TaskContext(request.JobId, request.TaskId, request.RequestId, nil)
		result := service.TS.Stop(ctx, &request)
		t.sendResult(w, &result)
		// todo: add trace logs
	}
}

func (t *TaskServerController) Cancel(w http.ResponseWriter, r *http.Request) {
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		t.sendError(w, err)
		return
	}
	var request model.StopTaskRequest
	if err := json.Unmarshal(bodyBytes, &request); err != nil {
		t.sendError(w, err)
		return
	}
	result := t.Auth(w, r)
	if result.Code != types.RESULT_SUCCESS.Code {
		t.sendResult(w, &result)
	} else if nil != err {
		t.sendResult(w, &types.ERROR_PARAM)
	} else {
		ctx := utils.TaskContext(request.JobId, request.TaskId, request.RequestId, nil)
		result := service.TS.Cancel(ctx, &request)
		t.sendResult(w, &result)
	}
}

func (t *TaskServerController) BatchStop(w http.ResponseWriter, r *http.Request) {
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		t.sendError(w, err)
		return
	}
	var request model.BatchStopTaskRequest
	if err := json.Unmarshal(bodyBytes, &request); err != nil {
		t.sendError(w, err)
		return
	}
	result := t.Auth(w, r)
	if result.Code != types.RESULT_SUCCESS.Code {
		t.sendResult(w, &result)
	} else if nil != err {
		t.sendResult(w, &types.ERROR_PARAM)
	} else {
		ctx := utils.TaskContext("", "", request.RequestId, nil)
		result := service.TS.BatchStop(ctx, &request)
		t.sendResult(w, &result)
	}
}

func (t *TaskServerController) Complete(w http.ResponseWriter, r *http.Request) {
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		t.sendError(w, err)
		return
	}
	var request model.JobCompleteRequest
	if err := json.Unmarshal(bodyBytes, &request); err != nil {
		t.sendError(w, err)
		return
	}

	result := service.TS.Complete(&request)
	t.sendResult(w, &result)

}

func (t *TaskServerController) Update(w http.ResponseWriter, r *http.Request) {
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		t.sendError(w, err)
		return
	}
	var request model.UpdateTaskRequest
	if err := json.Unmarshal(bodyBytes, &request); err != nil {
		t.sendError(w, err)
		return
	}
	result := t.Auth(w, r)
	if result.Code != types.RESULT_SUCCESS.Code {
		t.sendResult(w, &result)
	} else if nil != err {
		t.sendResult(w, &types.ERROR_PARAM)
	} else {
		result := service.TS.Update(&request)
		t.sendResult(w, &result)
	}
}

func (t *TaskServerController) List(w http.ResponseWriter, r *http.Request) {
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		t.sendError(w, err)
		return
	}
	var request model.ListTaskRequest
	if err := json.Unmarshal(bodyBytes, &request); err != nil {
		t.sendError(w, err)
		return
	}
	result := t.Auth(w, r)
	if result.Code != types.RESULT_SUCCESS.Code {
		t.sendResult(w, &result)
	} else {
		result := service.TS.List(&request)
		t.sendResult(w, &result)
	}

}

func (t *TaskServerController) Command(w http.ResponseWriter, r *http.Request) {
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		t.sendError(w, err)
		return
	}
	var request model.CommandTaskRequest
	if err := json.Unmarshal(bodyBytes, &request); err != nil {
		t.sendError(w, err)
		return
	}
	result := t.Auth(w, r)
	if result.Code != types.RESULT_SUCCESS.Code {
		t.sendResult(w, &result)
	} else {
		result := service.TS.Command(&request)
		t.sendResult(w, &result)
	}
}
