package agent_taskspan

import (
	"context"
	"fmt"
	"time"

	"mpp/internal/agent/model"
	"mpp/internal/agent/pkg/base"
	"mpp/pkg/tracer/tasker_tracing"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
)

func SpanRecordWithTask(ctx context.Context, event string, task *model.Task) {
	span := tasker_tracing.SpanFromContext(ctx)
	var eventAttrs []attribute.KeyValue

	rpcTraceId := tasker_tracing.RPCTraceID(ctx)
	if rpcTraceId != "" {
		eventAttrs = append(eventAttrs,
			attribute.String("rpc.trace.id", rpcTraceId),
			attribute.String("agent.version", base.GetVersion()),
		)
	}

	if task != nil {
		eventAttrs = append(eventAttrs,
			attribute.String("task.id", task.TaskId),
			attribute.String("task.product", task.Product),
			attribute.String("task.tag", task.Tag),
			attribute.String("task.model", task.EngineModel),
			attribute.String("task.uid", task.UserId),
			attribute.String("task.pipeline", task.PipelineId),
			attribute.String("task.createAt", task.CreateAt.Format(time.RFC3339)),
			attribute.Int64("task.runSeconds", int64(time.Now().Sub(task.CreateAt).Seconds())),
		)
		span.SetAttributes(eventAttrs...)

		eventAttrs = append(eventAttrs,
			attribute.String("task.metadata", fmt.Sprintf("%v", task.Metadata)),
		)

		eventAttrs = append(eventAttrs,
			attribute.String("task.originQuotaSet", fmt.Sprintf("%+v", task.OriginQuotaSet)),
			attribute.String("task.realQuotaSet", fmt.Sprintf("%+v", task.RealQuotaSet)),
		)

		if task.Params != "" {
			var suffix string
			dataIdx := len(task.Params)
			if dataIdx > 50 {
				dataIdx = 50
				suffix = "***"
			}
			eventAttrs = append(eventAttrs,
				attribute.String("task.Param", task.Params[0:dataIdx]+suffix),
			)
		}
	}

	//span.SetAttributes(eventAttrs...)
	if event == "" {
		event = "default"
	}
	span.AddEvent(event, trace.WithAttributes(eventAttrs...))

}

func SpanRecordWithTaskCompleteRequest(ctx context.Context, event string, task *model.TaskCompleteRequest) {
	if task == nil {
		return
	}
	span := tasker_tracing.SpanFromContext(ctx)
	var attrs []attribute.KeyValue

	rpcTraceId := tasker_tracing.RPCTraceID(ctx)
	attrs = append(attrs,
		attribute.String("rpc.trace.id", rpcTraceId),
		attribute.String("agent.version", base.GetVersion()),
		attribute.String("agent.commitID", base.GetCommitID()),
	)

	if task != nil {
		attrs = append(attrs,
			attribute.String("task.product", task.Product),
			attribute.String("task.tag", task.Tag),
			attribute.String("task.model", task.EngineModel),
			attribute.String("task.id", task.TaskId),
			attribute.String("task.uid", task.UserId),
			attribute.String("task.createAt", task.CreateAt.Format(time.RFC3339)),
			attribute.Int64("task.runSeconds", int64(time.Now().Sub(task.CreateAt).Seconds())),
		)
		span.SetAttributes(attrs...)

		attrs = append(attrs,
			attribute.String("task.metadata", fmt.Sprintf("%v", task.Metadata)),
			attribute.Int("task.retried", task.Retried),
		)

		if task.Data != nil {
			data, ok := task.Data.(string)
			if ok {
				var suffix string
				dataIdx := len(data)
				if dataIdx > 50 {
					dataIdx = 50
					suffix = "***"
				}
				attrs = append(attrs,
					attribute.Int("task.dataSize", len(data)),
					attribute.String("task.data", data[0:dataIdx]+suffix),
				)
			}
		}
		if task.DataUrl != "" {
			attrs = append(attrs,
				attribute.String("task.dataURL", task.DataUrl),
			)
		}
	}

	//span.SetAttributes(attrs...)
	if event == "" {
		event = "default"
	}
	span.AddEvent(event, trace.WithAttributes(attrs...))
}
