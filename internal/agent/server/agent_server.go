package server

import (
	"net/url"
	"strings"
	"syscall"
	"time"

	"mpp/internal/agent/conf"
	"mpp/internal/agent/controller"
	"mpp/internal/agent/pkg/base"
	"mpp/internal/agent/service/mecha"
	"mpp/internal/agent/service/mecha/engine"
	"mpp/pkg/tracer/middleware/tracer"

	"github.com/cinience/animus"
	"github.com/cinience/animus/contribs/transport/wrapper"
	"github.com/cinience/animus/middlewares/ratelimit"
	"github.com/cinience/animus/middlewares/timeout"

	"mpp/pkg/pprof"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/validate"
	agentv1 "proto.mpp/api/workeragent/v1"

	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/http"
)

type AgentServer struct {
	app *animus.App
}

func NewTaskServer(logger log.Logger, engineOnceInstance func() *engine.KubeletMechaEngine) (*AgentServer, error) {
	svc := NewTaskSvc()

	endpoint := base.GetNewTaskServerEndpoint()

	u, err := url.Parse(endpoint)
	if err != nil {
		return nil, err
	}

	srv := &AgentServer{}

	var httpServer = http.NewServer(
		http.Address(":"+u.Port()),
		http.Middleware(
			recovery.Recovery(),
			metadata.Server(),
			tracing.Server(),
			tracer.MPPServerTracer(tracer.WithAttributes(base.GetAgentInfo())),
			// logging.Server(newFilteredLogger(logger, []string{})), // filtered logger
			logging.Server(newFilteredLogger(logger, []string{"/api.workeragent.v1.Agent/ListTask", "/job/complete", "/v1/job/progress"})), // filtered logger
			// metrics.Server(
			// 	metrics.WithSeconds(prom.NewHistogram(mppMetrics.DefaultMetric.MetricsSeconds)),
			// 	metrics.WithRequests(prom.NewCounter(mppMetrics.DefaultMetric.MetricsRequests)),
			// ),
			ratelimit.Server(ratelimit.WithAllowPath([]string{"/task/sync/invoke", agentv1.OperationAgentInvokeTask})),
			timeout.Server(timeout.WithAllowPath([]string{"/task/sync/invoke", agentv1.OperationAgentInvokeTask})),
			validate.Validator(),
		),
		http.Timeout(4*time.Second),
	)

	tc := controller.NewTaskServerController()

	// pprof API
	httpServer.HandlePrefix("/pprof", pprof.DefaultPProfService)

	// 和rms交互的websocket协议 ,注意是/ws/
	//httpServer.HandleFunc("/ws/", rms.HandleWebSocket)

	r := httpServer.Route("/")
	// 和老的调度交互的接口
	wrapper.POST(r, "/task/async/start", wrapper.WithStdHandler(tc.Start))
	wrapper.POST(r, "/task/async/stop", wrapper.WithStdHandler(tc.Stop))
	wrapper.POST(r, "/task/async/submit", wrapper.WithStdHandler(tc.Submit))
	wrapper.POST(r, "/task/async/cancel", wrapper.WithStdHandler(tc.Cancel))
	wrapper.POST(r, "/task/async/update", wrapper.WithStdHandler(tc.Update))
	wrapper.POST(r, "/task/list", wrapper.WithStdHandler(tc.List))
	wrapper.POST(r, "/task/async/command", wrapper.WithStdHandler(tc.Command))
	wrapper.POST(r, "/task/sync/invoke", wrapper.WithStdHandler(tc.SyncInvoke))

	// 内部使用，必须存在，请勿删除, 用 WithNoMiddlewareStdHandler ，避免频繁打日志
	wrapper.GETAndPOST(r, "/info", wrapper.WithNoMiddlewareStdHandler(controller.InnerInformationHandler))
	wrapper.GETAndPOST(r, "/version", wrapper.WithNoMiddlewareStdHandler(controller.Version))

	// 和引擎交互用的
	wrapper.GETAndPOST(r, "/job/complete", wrapper.WithStdHandler(controller.InnerCompleteHandler))
	wrapper.GETAndPOST(r, "/systemload", wrapper.WithStdHandler(controller.InnerSystemLoadHandler))
	wrapper.GETAndPOST(r, "/v1/job/progress", wrapper.WithStdHandler(controller.InnerProgressHandler))

	wrapper.GETAndPOST(r, "/animus/pprof", wrapper.WithNoMiddlewareStdHandler(controller.InnerPprof))
	wrapper.GETAndPOST(r, "/animus/taskInfo", wrapper.WithNoMiddlewareStdHandler(controller.InnerTaskInfo))

	wrapper.GETAndPOST(r, "/service/liveness", wrapper.WithNoMiddlewareStdHandler(controller.InnerLiveness))
	wrapper.GETAndPOST(r, "/service/version", wrapper.WithNoMiddlewareStdHandler(controller.InnerVersion))
	wrapper.GETAndPOST(r, "/service/readiness", wrapper.WithNoMiddlewareStdHandler(controller.InnerReadiness))

	wrapper.GETAndPOST(r, "/worker/inactive", wrapper.WithStdHandler(controller.InnerWorkerInactiveHandler))
	wrapper.GETAndPOST(r, "/worker/active", wrapper.WithStdHandler(controller.InnerWorkerActiveHandler))
	wrapper.GETAndPOST(r, "/worker/offline", wrapper.WithStdHandler(controller.InnerWorkerOfflineHandler))
	wrapper.GETAndPOST(r, "/worker/online", wrapper.WithStdHandler(controller.InnerWorkerOnlineHandler))
	wrapper.POST(r, "/worker/metrics/set", wrapper.WithStdHandler(controller.InnerMetricsHandler))

	wrapper.GETAndPOST(r, "/mesh/config/get", wrapper.WithStdHandler(controller.InnerMeshConfigGetHandler))

	if strings.HasPrefix(conf.GetEngineUrl(), mecha.OCINsenterEngineType) {
		//Init
		engineOnceInstance()
		podCtl := controller.NewPodsServerController(conf.GetEngineUrl(), engineOnceInstance)
		// kubelet管理pod
		wrapper.GETAndPOST(r, "/cri/pod/load", wrapper.WithStdHandler(podCtl.InnerLoadMechaPodOnCri))
		wrapper.GETAndPOST(r, "/cri/worker/inactive", wrapper.WithStdHandler(podCtl.InnerMechaWorkerInactive))

		// start tcp proxy for taskId routing mode, only for mecha engine
		// aone https://project.aone.alibaba-inc.com/v2/project/1143023/req/********#
		p := mecha.NewProxy(engineOnceInstance())
		go p.StartTCPProxy("1935", "")
	}

	agentv1.RegisterAgentHTTPServer(httpServer, svc)

	so := animus.WithDefaultHealthCheck()
	so = append(so,
		// to Aone #********
		// Animus App only watches SIGKILL (discard SIGTERM, SIGINT);
		// SIGTERM is reserved for agent grace termination.
		// in agent_main.go: agent watches SIGTERM -> stop the agent after all tasks stopped.
		animus.Signal(syscall.SIGKILL),

		animus.Name("mpp-agent"),
		animus.Server(
			httpServer,
		),
	)
	srv.app = animus.New(so...)

	return srv, err
}

func (srv *AgentServer) Run() error {
	return srv.app.Run()
}

func (srv *AgentServer) Stop() error {
	return srv.app.Stop()
}

// filteredLogger wraps the original logger and filters out operations in the blacklist
type filteredLogger struct {
	logger    log.Logger
	blacklist []string
}

func newFilteredLogger(logger log.Logger, blacklist []string) log.Logger {
	return &filteredLogger{
		logger:    logger,
		blacklist: blacklist,
	}
}

func (f *filteredLogger) Log(level log.Level, keyvals ...interface{}) error {
	// Check if this log contains any blacklisted operation and skip it
	for i := 0; i < len(keyvals)-1; i += 2 {
		if keyvals[i] == "operation" {
			if operation, ok := keyvals[i+1].(string); ok {
				// Check against blacklist
				for _, blacklistedOp := range f.blacklist {
					if strings.Contains(operation, blacklistedOp) {
						return nil // Skip logging
					}
				}
			}
		}
	}
	return f.logger.Log(level, keyvals...)
}
