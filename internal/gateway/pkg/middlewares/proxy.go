package middlewares

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/opentracing/opentracing-go/ext"
	"github.com/traefik/traefik/v3/pkg/config/dynamic"
	"github.com/traefik/traefik/v3/pkg/middlewares"
	"github.com/traefik/traefik/v3/pkg/tracing"
)

// A fast reverse proxy to help you expose a local server behind a NAT or firewall to the internet.
// 后续也可以参考下frp的实现

const (
	typeName = "ReverseProxy"
)

// AddPrefix is a middleware used to add prefix to an URL request.
type addPrefix struct {
	next   http.Handler
	prefix string
	name   string
}

// New creates a new handler.
func New(ctx context.Context, next http.Handler, config dynamic.AddPrefix, name string) (http.Handler, error) {
	middlewares.GetLogger(ctx, name, typeName).Debug().Msg("Creating middleware")
	var result *addPrefix

	if len(config.Prefix) > 0 {
		result = &addPrefix{
			prefix: config.Prefix,
			next:   next,
			name:   name,
		}
	} else {
		return nil, fmt.Errorf("prefix cannot be empty")
	}

	return result, nil
}

func (a *addPrefix) GetTracingInformation() (string, ext.SpanKindEnum) {
	return a.name, tracing.SpanKindNoneEnum
}

func (a *addPrefix) ServeHTTP(rw http.ResponseWriter, req *http.Request) {
	logger := middlewares.GetLogger(req.Context(), a.name, typeName)
	logger.Debug().Msgf("URL.Path is %s.", req.URL.Path)

	timeout := 5 * time.Second // 设置超时时间为5秒

	// 创建一个带超时的chan
	timeoutChan := time.After(timeout)

	// 将req序列化 通过传给grpc的chan ，并新建一个等待chan
	resultChan := make(chan string, 1)
	// 使用select语句等待结果或超时
	select {
	case result := <-resultChan:
		fmt.Println(result)
	case <-timeoutChan:
		http.Error(rw, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		logger.Error().Msgf("URL.Path is %s. request remote client timeout", req.URL.Path)
	}

	// 不再往后走，直接返回
	//a.next.ServeHTTP(rw, req)
}

func ensureLeadingSlash(str string) string {
	if str == "" {
		return str
	}

	if str[0] == '/' {
		return str
	}

	return "/" + str
}
