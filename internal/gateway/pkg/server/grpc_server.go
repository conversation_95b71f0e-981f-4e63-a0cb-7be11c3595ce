package server

import (
	"github.com/cinience/animus"
	"github.com/cinience/animus/health"
	"github.com/cinience/animus/registry"
	"mpp/internal/gateway/pkg/conf"
	"mpp/internal/gateway/pkg/service"
	"mpp/pkg/hostinfo"
	"mpp/pkg/tracer/middleware/tracer"

	healthpb "google.golang.org/grpc/health/grpc_health_v1"
	configpb "proto.mpp/api/configuration/v1"
	exchangerpb "proto.mpp/api/exchanger/v1"

	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/grpc"
)

func RunExchangerServer(conf *conf.Config, reg registry.Registrar, exchanger *service.ExchangerService) error {
	gs := grpc.NewServer(grpc.Address(conf.GrpcServerAddr),
		grpc.Middleware(
			recovery.Recovery(),
			metadata.Server(),
			tracing.Server(),
			tracer.MPPServerTracer(),
		), grpc.CustomHealth())

	app := animus.New(
		animus.Name(hostinfo.GetAppName()),
		animus.Version("v1.0.0"),
		animus.Server(gs),
		animus.Registrar([]registry.Registrar{
			reg,
		}),
	)

	//exchanger := service.NewExchangerService()
	healthpb.RegisterHealthServer(gs, health.GetHealthGrpcService())
	exchangerpb.RegisterExchangerServer(gs, exchanger)
	configpb.RegisterConfigurationServer(gs, service.NewConfigurableService(conf))

	return app.Run()
}
