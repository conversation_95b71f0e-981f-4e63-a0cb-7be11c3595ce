package server

// todo 修正，超时10m
//func TestExchanger(t *testing.T) {
//	sn := "test"
//	//remoteAddr := "127.0.0.1:8000"
//	remoteAddr := "101.133.233.140:8000"
//
//	conn, err := grpc.DialInsecure(
//		context.Background(),
//		grpc.WithEndpoint(remoteAddr),
//	)
//	client := pb.NewExchangerClient(conn)
//
//	md := metadata.Pairs(header.HeaderSN, sn, header.HeaderClientRryConnectSize, "1")
//
//	// 创建带有元数据的上下文
//	ctxWithMetadata := metadata.NewOutgoingContext(context.TODO(), md)
//
//	// 发起带有元数据的RPC调用
//	s, err := client.Exchange(ctxWithMetadata)
//	assert.NoError(t, err)
//	if err != nil {
//		return
//	}
//
//	recivedMD, err := s.<PERSON>er()
//	if err == nil {
//		log.Println("recivedMD :", recivedMD)
//	}
//
//	go func() {
//		for {
//			time.Sleep(time.Second * 3)
//			jsonData, err := json.Marshal(map[string]string{"name": "John", "email": "johndoe"})
//			if err != nil {
//				fmt.Println("序列化请求数据时发生错误:", err)
//				return
//			}
//
//			log.Print("***********************")
//			req, err := http.NewRequest("POST", "http://127.0.0.1:12008/proxy", bytes.NewBuffer(jsonData))
//			req.Header.Set("X-SN", sn)
//			req.Header.Set("Content-Type", "application/json")
//			client := &http.Client{}
//			resp, err := client.Do(req)
//			if err != nil {
//				fmt.Println("发送请求时发生错误:", err)
//				return
//			}
//			defer resp.Body.Close()
//			assert.NoError(t, err)
//			if err != nil {
//				panic(err)
//			}
//			log.Println("post resp:", resp)
//
//			data, err := codecs.EncodeResponse(resp)
//			if err != nil {
//				panic(err)
//			}
//			log.Print(string(data.Value))
//		}
//
//	}()
//
//	for {
//		req, err := s.Recv()
//		if err != nil {
//			assert.NoError(t, err)
//			return
//		}
//		log.Print("req is ", req)
//
//		resp, err := http.Get("https://www.aliyun.com")
//		assert.NoError(t, err)
//
//		data, err := codecs.EncodeResponse(resp)
//		assert.NoError(t, err)
//		log.Print("EncodeResponse:", string(data.Value))
//		err = s.SendMsg(&pb.ExchangeMessage{
//			Id:   req.Id,
//			Path: req.Path,
//			Data: data,
//		})
//		assert.NoError(t, err)
//		if err == io.EOF {
//			log.Println("connection is EOF")
//			break
//		}
//		time.Sleep(5 * time.Second)
//		log.Printf("task continue...")
//	}
//}
//
//type TestHandler struct {
//}
//
//func (t *TestHandler) Serve(ctx context.Context, in *pb.ExchangeMessage, out *pb.ExchangeMessage) error {
//	log.Println(in)
//	out.Data = in.Data
//	return nil
//}
//
//func PostProxy() {
//	jsonData, err := json.Marshal(map[string]string{"name": "John", "email": "johndoe"})
//	if err != nil {
//		return
//	}
//
//	req, err := http.NewRequest("POST", "http://127.0.0.1:9000/", bytes.NewBuffer(jsonData))
//	req.Header.Set("X-SN", "test")
//	req.Header.Set("Content-Type", "application/json")
//	client := &http.Client{}
//	resp, err := client.Do(req)
//	if err != nil {
//		panic(err)
//		return
//	}
//	defer resp.Body.Close()
//	body, err := io.ReadAll(resp.Body)
//	if err != nil {
//		panic(err)
//		return
//	}
//	fmt.Println(string(body))
//}
//
//func TestExchangerClient(t *testing.T) {
//	cli, err := rpc.NewClient("127.0.0.1:8000", nil, "test")
//	assert.NoError(t, err)
//
//	cli.Route(stream.PathRPC, serve.NewHttpServe("127.0.0.1:8000").Serve)
//
//	conf := &conf.Config{
//		GrpcServerAddr:  ":8000",
//		ProxyServerAddr: ":9000",
//	}
//	exchanger := service.NewExchangerService(conf)
//
//	go func() {
//		RunHttpServer(conf, nil, nil, exchanger)
//	}()
//	time.Sleep(time.Second)
//
//	go func() {
//		RunExchangerServer(conf, nil, exchanger)
//	}()
//	time.Sleep(time.Second)
//
//	go func() {
//		for {
//			time.Sleep(3 * time.Second)
//			PostProxy()
//		}
//	}()
//	err = cli.Run()
//	assert.NoError(t, err)
//}
