package server

//import (
//	"bytes"
//	"fmt"
//	"io/ioutil"
//	"net/http"
//	"testing"
//	"time"
//)
//
//// 测试gateway转发
//func TestNewHTTPServer(t *testing.T) {
//	url := "http://127.0.0.1:8000/v1/worker/list"
//	// 创建一个新的请求
//	req, err := http.NewRequest("POST", url, bytes.NewBuffer([]byte(`{
//	}`)))
//	if err != nil {
//		t.Logf("Failed to create request: %s", err)
//	}
//	// 设置请求头
//	req.Header.Set("Content-Type", "application/json")
//	req.Header.Set("X-Scheduler-Tag", "mpp-taskscheduler-l3.http")
//
//	// 创建一个自定义的 HTTP 客户端，并设置超时时间
//	client := &http.Client{
//		Timeout: time.Second * 10,
//	}
//
//	// 发送请求
//	resp, err := client.Do(req)
//	if err != nil {
//		t.Logf("Request failed: %s", err)
//	}
//	defer resp.Body.Close()
//
//	// 读取响应体
//	body, err := ioutil.ReadAll(resp.Body)
//	if err != nil {
//		t.Logf("Failed to read response body: %s", err)
//	}
//
//	// 打印响应内容
//	fmt.Println(string(body))
//}
