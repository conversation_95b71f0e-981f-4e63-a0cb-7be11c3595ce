package server

import (
	"net/http"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	// 解决跨域问题
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
} // use default options

type Paris struct {
	UserConn  *websocket.Conn
	ProxyConn *websocket.Conn
}

// var sessions map[string]*Paris = make(map[string]*Paris)
var GProxyConn *websocket.Conn
var GUserConn *websocket.Conn

func Echo(w http.ResponseWriter, r *http.Request) {
	c, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		return
	}
	log.Infof("remoteAddr:%s", c.RemoteAddr().String())
	defer func() {
		c.Close()
	}()

	GUserConn = c
	var (
		mt  int
		msg []byte
	)

	for {
		if mt, msg, err = c.ReadMessage(); err != nil {
			log.Errorf("read: %s", err.Error())
			break
		}
		if mt == 1 {
			log.Infof("GUserConnRecv: messageType:%d, msg:%s", mt, string(msg))
		} else {
			log.Infof("GUserConnRecv: messageType:%d, msgLen:%d", mt, len(msg))
		}
		if GProxyConn != nil {
			if err := GProxyConn.WriteMessage(mt, msg); err != nil {
				log.Errorf("write: %s", err.Error())
				//break
			}
		} else {
			log.Errorf("GProxyConn is nil")
		}
	}
}

func Proxy(w http.ResponseWriter, r *http.Request) {
	c, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		return
	}
	log.Infof("remoteAddr:%s", c.RemoteAddr().String())
	defer func() {
		c.Close()
	}()

	GProxyConn = c
	var (
		mt  int
		msg []byte
	)

	for {
		if mt, msg, err = c.ReadMessage(); err != nil {
			log.Errorf("read: %s", err.Error())
			break
		}
		if mt == 1 {
			log.Infof("GProxyConn: messageType:%d, msg:%s", mt, string(msg))
		} else {
			log.Infof("GProxyConn: messageType:%d, msgLen:%d", mt, len(msg))
		}

		if GUserConn != nil {
			if err := GUserConn.WriteMessage(mt, msg); err != nil {
				log.Errorf("write: %s", err.Error())
			}
		} else {
			log.Errorf("GUserConn is nil")
		}
	}
}

func RunWSServer() {
	http.HandleFunc("/realtime/v2/", Echo)
	http.HandleFunc("/realtime/v2/proxy/", Proxy)
	log.Fatal(http.ListenAndServe(":10111", nil))
}
