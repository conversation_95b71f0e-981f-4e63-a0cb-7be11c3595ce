package server

import (
	"context"
	"fmt"
	"io"
	"mpp/internal/gateway/pkg/conf"
	"mpp/pkg/header"
	"mpp/pkg/hostinfo"
	"mpp/pkg/stream"
	streamTransport "mpp/pkg/stream/transport"
	"mpp/pkg/tracer/middleware/tracer"
	stdhttp "net/http"
	"net/http/httputil"
	"strings"
	"time"

	"github.com/cinience/animus"
	"github.com/cinience/animus/contribs/transport/wrapper"
	"github.com/cinience/animus/health"
	"github.com/cinience/animus/middlewares/timeout"
	"github.com/cinience/animus/registry"
	"github.com/cinience/animus/utils"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

const pathPrefix = "/proxy"

type ProxyServer struct {
	app    *animus.App
	caller stream.RemoteCaller
}

func (p *ProxyServer) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// uuid - proxy
	fmt.Println("ServeHTTP ", r.URL.String())
	log.Info("ServeHTTP ", r.URL.String())
	ctx := r.Context()
	r.URL.Path = strings.Replace(r.URL.Path, pathPrefix, "/", 1)
	sn := r.Header.Get(header.HeaderSN)
	if sn == "" {
		stdhttp.Error(w, utils.Error(fmt.Errorf("sn is not found")).Error(), stdhttp.StatusInternalServerError)
		return
	}

	if !p.caller.Exists(ctx, sn) {
		fmt.Println("sn is not exists...")
		// 去调度查询最新worker，路由过去
		proxy := httputil.NewSingleHostReverseProxy(r.URL)
		r.Host = "todo"
		// Request.URL.Path = "/path/to/forward" // 修改请求路径
		proxy.ServeHTTP(w, r)
		return
	}

	tp := streamTransport.NewHttpOverGrpcRoundTrip(p.caller)
	resp, err := tp.RoundTrip(r)
	if err != nil {
		stdhttp.Error(w, utils.Error(err).Error(), stdhttp.StatusInternalServerError)
		return
	}
	bodyData, err := io.ReadAll(resp.Body)
	if err != nil {
		stdhttp.Error(w, utils.Error(err).Error(), stdhttp.StatusInternalServerError)
		return
	}

	for k, _ := range resp.Header {
		w.Header().Set(k, resp.Header.Get(k))
	}
	w.WriteHeader(resp.StatusCode)

	_, err = w.Write(bodyData)
	if err != nil {
		stdhttp.Error(w, utils.Error(err).Error(), stdhttp.StatusInternalServerError)
		return
	}
	return
}
func (p *ProxyServer) ProxyHandler() func(ctx http.Context) error {
	return func(ctx http.Context) error {
		http.SetOperation(ctx, "/gateway/ProxyHandler")
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			r := req.(*stdhttp.Request)
			r.URL.Path = strings.Replace(r.URL.Path, pathPrefix, "/", 1)
			sn := r.Header.Get(header.HeaderSN)
			if sn == "" {
				return nil, fmt.Errorf("unexpected header: %v", header.HeaderSN)
			}
			if !p.caller.Exists(ctx, sn) {
				fmt.Println("sn is not exists...")
				// 去调度查询最新worker，路由过去
				//proxy := httputil.NewSingleHostReverseProxy(r.URL)
				//r.Host = "todo"
				//// Request.URL.Path = "/path/to/forward" // 修改请求路径
				//proxy.ServeHTTP(w, r)
				return nil, nil
			}
			tp := streamTransport.NewHttpOverGrpcRoundTrip(p.caller)
			return tp.RoundTrip(r)
		})

		out, err := h(ctx, ctx.Request())
		if err != nil {
			return utils.Error(err)
		}

		resp, ok := out.(*stdhttp.Response)
		if !ok {
		}
		w := ctx.Response()
		for k, _ := range resp.Header {
			w.Header().Set(k, resp.Header.Get(k))
		}
		w.WriteHeader(resp.StatusCode)
		bodyData, err := io.ReadAll(resp.Body)
		if err != nil {
			return err
		}
		_, err = w.Write(bodyData)
		if err != nil {
			return err
		}

		return nil
	}
}

func RunHttpServer(conf *conf.Config, reg registry.Registrar, discovery registry.Discovery, caller stream.RemoteCaller) error {
	httpServer := http.NewServer(
		http.Address(conf.ProxyServerAddr),
		http.Middleware(
			recovery.Recovery(),
			metadata.Server(),
			tracing.Server(),
			tracer.MPPServerTracer(),
			metrics.Server(),
			timeout.Server(), //自定义超时时间中间件
		),
		http.Timeout(5*time.Second),
	)

	srv := &ProxyServer{}
	srv.caller = caller
	r := httpServer.Route("/")

	if discovery != nil {
		wrapper.GETAndPOST(r, "/animus/discovery", wrapper.WithStdHandler(discovery.Overhaul))
	}

	// 后续增加优雅下线统计时用
	httpServer.Handle("/metrics", promhttp.Handler())
	httpServer.HandlePrefix("/health", health.DefaultHealthService)

	//httpServer.HandlePrefix("/v1", srv)
	httpServer.Route("/v1").POST("/task/start", srv.ProxyHandler())
	httpServer.Route("/v1").POST("/task/stop", srv.ProxyHandler())
	httpServer.Route("/v1").POST("/task/update", srv.ProxyHandler())
	httpServer.Route("/v1").POST("/task/submit", srv.ProxyHandler())
	httpServer.Route("/v1").POST("/task/cancel", srv.ProxyHandler())
	httpServer.Route("/v1").POST("/task/list", srv.ProxyHandler())
	httpServer.Route("/v1").POST("/worker/status", srv.ProxyHandler())

	o := animus.WithDefaultHealthCheck()
	o = append(o,
		animus.Name(hostinfo.GetAppName()),
		animus.Server(httpServer),
		animus.Registrar([]registry.Registrar{
			reg,
		}),
	)

	srv.app = animus.New(o...)

	// todo 临时增加ws server
	go RunWSServer()

	return srv.app.Run()
}
