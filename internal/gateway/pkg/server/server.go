package server

import (
	"mpp/internal/gateway/pkg/conf"
	"mpp/pkg/utils"

	_ "github.com/cinience/animus/contribs/registry/nacos"
	_ "github.com/cinience/animus/contribs/registry/redis"
	"github.com/cinience/animus/registry"
	"github.com/google/wire"
)

// ProviderSet is server providers.
var ProviderSet = wire.NewSet(RunHttpServer, RunExchangerServer, NewRegister, NewDiscover)

func NewRegister(conf *conf.Config) registry.Registrar {
	if conf == nil {
		return nil
	}

	if conf.RegistryUrl == "" {
		return nil
	}

	r, err := registry.GetRegistrarProvider(utils.PatchRegistryUrl(conf.RegistryUrl))
	if err != nil {
		panic(err)
	}

	if r == nil {
		return nil
	}

	return r
}

func NewDiscover(conf *conf.Config) registry.Discovery {
	if conf == nil {
		return nil
	}

	if conf.RegistryUrl == "" {
		return nil
	}

	r, err := registry.GetDiscoveryProvider(utils.PatchRegistryUrl(conf.RegistryUrl))
	if err != nil {
		panic(err)
	}

	if r == nil {
		return nil
	}

	return registry.NewFailOverDiscovery(r)
}
