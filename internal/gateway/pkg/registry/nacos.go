package registry

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"github.com/cinience/animus/registry"
)

type NacosDiscovery struct {
	discovery registry.Discovery
}

func NewNacosDiscovery(discovery registry.Discovery) *NacosDiscovery {
	return &NacosDiscovery{discovery: discovery}
}

func (d *NacosDiscovery) PickService(ctx context.Context, serviceName string) (string, error) {
	if d.discovery == nil {
		return "", fmt.Errorf("discovery is not configured")
	}

	item, err := d.discovery.PickService(ctx, serviceName)
	if err != nil {
		return "", err
	}
	if item == nil || len(item.Endpoints) == 0 {
		return "", fmt.<PERSON><PERSON><PERSON>("service %s not found", serviceName)
	}

	return item.Endpoints[0], nil
}

func randomString(arrays []string) string {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	return arrays[r.Intn(len(arrays))]
}
