package logs

import (
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/rs/zerolog"
)

type WrapperLog struct {
	logger zerolog.Logger
}

func NewWrapperLog(logger zerolog.Logger) *WrapperLog {
	return &WrapperLog{logger: logger}
}

func (w *WrapperLog) Log(level log.Level, v ...interface{}) error {
	switch level {
	case log.LevelDebug:
		w.logger.Print(v...)
		return nil
	case log.LevelInfo:
		w.logger.Info().Msgf(fmt.Sprint(v...))
		return nil
	case log.LevelWarn:
		w.logger.Warn().Msgf(fmt.Sprint(v...))
		return nil
	case log.LevelError:
		w.logger.Error().Msgf(fmt.Sprint(v...))
		return nil
	case log.LevelFatal:
		w.logger.Fatal().Msgf(fmt.Sprint(v...))
		return nil
	default:
		return nil
	}
}
