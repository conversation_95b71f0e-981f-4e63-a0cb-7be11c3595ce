package service

import (
	"testing"

	"mpp/internal/gateway/pkg/conf"
	"mpp/pkg/agent"

	"google.golang.org/grpc/metadata"
)

// go test ./internal/gateway/pkg/service/ -v
func TestConfigurableService_PrecomputedConfigs(t *testing.T) {
	md := metadata.New(map[string]string{})
	md.Set("x-md-global-agent-version", "1.0.0")
	md.Set("x-md-global-agent-build-date", "20240101000000")
	md.Set("x-md-global-agent-commit-id", "123456")

	config := &conf.Config{
		Agent: agent.AgentConfig{
			MnsEndpoint: "mns://127.0.0.1:8848",
			OssEndpoint: "oss://127.0.0.1:8848",
		},
		AgentVersionRules: []conf.AgentVersionRule{
			{
				Name: "test",
				Conditions: conf.VersionCondition{
					BuildDateMin: "20210101000000",
					BuildDateMax: "20991231000000",
				},
				ExcludeFields: []string{"MnsEndpoint"},
			},
		},
	}
	service := NewConfigurableService(config)

	// Extract agent info from metadata
	agentInfo := extractAgentInfo(md)

	// Get config key
	configKey := service.getConfigKey(agentInfo)

	// Get cached config
	cachedConfig, exists := service.configCache[configKey]
	if !exists {
		t.Errorf("Config key %s not found in cache", configKey)
		return
	}

	t.Logf("config: %+v\n", cachedConfig)

	// MnsEndpoint should be empty (excluded)
	if cachedConfig.MnsEndpoint != "" {
		t.Errorf("MnsEndpoint should be empty due to exclusion, but got %s", cachedConfig.MnsEndpoint)
	}

	// OssEndpoint should remain unchanged
	if cachedConfig.OssEndpoint != "oss://127.0.0.1:8848" {
		t.Errorf("OssEndpoint should be oss://127.0.0.1:8848, but got %s", cachedConfig.OssEndpoint)
	}
}

func TestConfigurableService_PrecomputeConfigs(t *testing.T) {
	tests := []struct {
		name              string
		agentVersionRules []conf.AgentVersionRule
		expectedCacheSize int
	}{
		{
			name:              "no rules",
			agentVersionRules: []conf.AgentVersionRule{},
			expectedCacheSize: 1, // Only base config
		},
		{
			name: "single rule",
			agentVersionRules: []conf.AgentVersionRule{
				{
					Name: "rule1",
					Conditions: conf.VersionCondition{
						BuildDateMax: "20250730000000",
					},
					ExcludeFields: []string{"MnsEndpoint"},
				},
			},
			expectedCacheSize: 2, // 2^1 = 2 combinations
		},
		{
			name: "two rules",
			agentVersionRules: []conf.AgentVersionRule{
				{
					Name: "rule1",
					Conditions: conf.VersionCondition{
						BuildDateMax: "20250730000000",
					},
					ExcludeFields: []string{"MnsEndpoint"},
				},
				{
					Name: "rule2",
					Conditions: conf.VersionCondition{
						BuildDateMin: "20210101000000",
					},
					ExcludeFields: []string{"OssEndpoint"},
				},
			},
			expectedCacheSize: 4, // 2^2 = 4 combinations
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := &conf.Config{
				Agent: agent.AgentConfig{
					MnsEndpoint: "mns://test:8848",
					OssEndpoint: "oss://test:8848",
				},
				AgentVersionRules: tt.agentVersionRules,
			}

			service := NewConfigurableService(config)

			// Check cache size
			if len(service.configCache) != tt.expectedCacheSize {
				t.Errorf("Expected cache size %d, got %d", tt.expectedCacheSize, len(service.configCache))
			}

			// Verify all cached configs are not nil
			for key, cachedConfig := range service.configCache {
				if cachedConfig == nil {
					t.Errorf("Cached config for key %s is nil", key)
				}
			}
		})
	}
}

func TestConfigurableService_GetConfigKey(t *testing.T) {
	tests := []struct {
		name              string
		agentVersionRules []conf.AgentVersionRule
		agentInfo         AgentInfo
		expectedKey       string
	}{
		{
			name:              "no rules",
			agentVersionRules: []conf.AgentVersionRule{},
			agentInfo:         AgentInfo{BuildDate: "20240101000000"},
			expectedKey:       "0",
		},
		{
			name: "single rule matches",
			agentVersionRules: []conf.AgentVersionRule{
				{
					Name: "rule1",
					Conditions: conf.VersionCondition{
						BuildDateMax: "20250730000000",
					},
					ExcludeFields: []string{"MnsEndpoint"},
				},
			},
			agentInfo:   AgentInfo{BuildDate: "20240101000000"},
			expectedKey: "1", // First rule matches, mask = 1
		},
		{
			name: "single rule doesn't match",
			agentVersionRules: []conf.AgentVersionRule{
				{
					Name: "rule1",
					Conditions: conf.VersionCondition{
						BuildDateMax: "20230730000000",
					},
					ExcludeFields: []string{"MnsEndpoint"},
				},
			},
			agentInfo:   AgentInfo{BuildDate: "20240101000000"},
			expectedKey: "0", // No rules match, mask = 0
		},
		{
			name: "two rules, both match",
			agentVersionRules: []conf.AgentVersionRule{
				{
					Name: "rule1",
					Conditions: conf.VersionCondition{
						BuildDateMax: "20250730000000",
					},
					ExcludeFields: []string{"MnsEndpoint"},
				},
				{
					Name: "rule2",
					Conditions: conf.VersionCondition{
						BuildDateMin: "20210101000000",
					},
					ExcludeFields: []string{"OssEndpoint"},
				},
			},
			agentInfo:   AgentInfo{BuildDate: "20240101000000"},
			expectedKey: "3", // Both rules match, mask = 11 (binary) = 3
		},
		{
			name: "two rules, only second matches",
			agentVersionRules: []conf.AgentVersionRule{
				{
					Name: "rule1",
					Conditions: conf.VersionCondition{
						BuildDateMax: "20230730000000",
					},
					ExcludeFields: []string{"MnsEndpoint"},
				},
				{
					Name: "rule2",
					Conditions: conf.VersionCondition{
						BuildDateMin: "20210101000000",
					},
					ExcludeFields: []string{"OssEndpoint"},
				},
			},
			agentInfo:   AgentInfo{BuildDate: "20240101000000"},
			expectedKey: "2", // Only second rule matches, mask = 10 (binary) = 2
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := &conf.Config{
				Agent: agent.AgentConfig{
					MnsEndpoint: "mns://test:8848",
					OssEndpoint: "oss://test:8848",
				},
				AgentVersionRules: tt.agentVersionRules,
			}

			service := NewConfigurableService(config)
			key := service.getConfigKey(tt.agentInfo)

			if key != tt.expectedKey {
				t.Errorf("Expected key %s, got %s", tt.expectedKey, key)
			}
		})
	}
}

func TestConfigurableService_ConfigCombinations(t *testing.T) {
	config := &conf.Config{
		Agent: agent.AgentConfig{
			MnsEndpoint: "mns://test:8848",
			OssEndpoint: "oss://test:8848",
		},
		AgentVersionRules: []conf.AgentVersionRule{
			{
				Name: "exclude-mns",
				Conditions: conf.VersionCondition{
					BuildDateMax: "20250730000000",
				},
				ExcludeFields: []string{"MnsEndpoint"},
			},
			{
				Name: "exclude-oss",
				Conditions: conf.VersionCondition{
					BuildDateMin: "20210101000000",
				},
				ExcludeFields: []string{"OssEndpoint"},
			},
		},
	}

	service := NewConfigurableService(config)

	// Test all combinations
	combinations := map[string]struct {
		shouldHaveMns bool
		shouldHaveOss bool
	}{
		"0": {true, true},   // No rules applied
		"1": {false, true},  // Only first rule applied (exclude MNS)
		"2": {true, false},  // Only second rule applied (exclude OSS)
		"3": {false, false}, // Both rules applied (exclude both)
	}

	for key, expected := range combinations {
		cachedConfig, exists := service.configCache[key]
		if !exists {
			t.Errorf("Config key %s not found in cache", key)
			continue
		}

		hasMns := cachedConfig.MnsEndpoint != ""
		hasOss := cachedConfig.OssEndpoint != ""

		if hasMns != expected.shouldHaveMns {
			t.Errorf("Key %s: expected MnsEndpoint present=%v, got present=%v", key, expected.shouldHaveMns, hasMns)
		}

		if hasOss != expected.shouldHaveOss {
			t.Errorf("Key %s: expected OssEndpoint present=%v, got present=%v", key, expected.shouldHaveOss, hasOss)
		}
	}
}
