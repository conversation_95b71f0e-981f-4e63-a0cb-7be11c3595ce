package service

import (
	"context"
	"fmt"
	"io"
	"mpp/pkg/header"

	"strings"
	"sync"
	"time"

	"mpp/internal/gateway/pkg/conf"
	"mpp/pkg/hostinfo"
	"mpp/pkg/stream"
	"mpp/pkg/stream/proto"

	"github.com/cinience/animus/utils"

	pb "proto.mpp/api/exchanger/v1"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/peer"
	"google.golang.org/protobuf/types/known/anypb"
)

type Client struct {
	Conn       pb.Exchanger_ExchangeServer
	RemoteAddr string
	lock       sync.Mutex
}

type ExchangerService struct {
	pb.UnimplementedExchangerServer

	clientMaps sync.Map
	chanMaps   sync.Map

	conf        *conf.Config
	handlerLock sync.RWMutex
	handlers    map[string]stream.Handler
}

func NewExchangerService(conf *conf.Config) *ExchangerService {
	return &ExchangerService{
		handlers: make(map[string]stream.Handler),
		conf:     conf,
	}
}

func (s *ExchangerService) Exists(ctx context.Context, sn string) bool {
	_, ok := s.clientMaps.Load(sn)
	if ok {
		return true
	}

	return false
}

func (s *ExchangerService) Invoke(ctx context.Context, sn string, msg *anypb.Any) (*anypb.Any, error) {
	// 后续换成ctx来控制超时时间
	var err error
	cliObj, ok := s.clientMaps.Load(sn)
	if !ok {
		return nil, utils.Errorf("cannot found cli for sn:%s", sn)
	}

	cli := cliObj.(*Client)
	//anyValue := &anypb.Any{}
	//err := anypb.MarshalFrom(anyValue, bytesValue, proto.MarshalOptions{})
	streamId := stream.GetStreamID()
	cli.lock.Lock()

	err = cli.Conn.Send(&pb.ExchangeMessage{Id: streamId, Path: stream.PathRPC, Data: msg})
	cli.lock.Unlock()
	if err != nil {
		log.Infof("Conn Send err: %v", err)
		return nil, utils.Error(err)
	}
	ch := make(chan *anypb.Any, 1)
	s.chanMaps.Store(streamId, ch)
	defer func() {
		s.chanMaps.Delete(streamId)
		close(ch)
	}()

	select {
	case result := <-ch:
		log.Infof("received result. len: %v", result)
		return result, nil
	case <-time.After(3 * time.Second):
		err = fmt.Errorf("wait reply chan for %s timeout", streamId)
		log.Errorf("wait reply chan for %s timeout", streamId)
	}
	return nil, err
}

func (s *ExchangerService) Serve(ctx context.Context, in *pb.ExchangeMessage, out *pb.ExchangeMessage) error {
	return s.Reply(in.Id, in.Data)
}

func (s *ExchangerService) Reply(streamId string, msg *anypb.Any) error {
	// todo 优化下怎么防止写一个已经关闭的chan
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("reply panic:%v", err)
		}
	}()
	timeout := time.NewTimer(time.Microsecond * 500)
	ch, ok := s.chanMaps.Load(streamId)
	if ok {
		ch := ch.(chan *anypb.Any)
		select {
		case ch <- msg:
			return nil
		case <-timeout.C:
			return fmt.Errorf("reply to chan for %s timeout", streamId)
		}
	} else {
		return fmt.Errorf("can not found chan for %s", streamId)
	}
}

func (s *ExchangerService) Route(path string, handler stream.Handler) error {
	s.handlerLock.Lock()
	defer s.handlerLock.Unlock()
	s.handlers[path] = handler

	return nil
}

func (s *ExchangerService) Exchange(conn pb.Exchanger_ExchangeServer) error {
	// 加入连接池
	var remoteAddr string
	var sn string
	peerInfo, ok := peer.FromContext(conn.Context())
	if ok {
		remoteAddr = peerInfo.Addr.String()
		log.Infof("remoteAddr %s", remoteAddr)
	} else {
		return fmt.Errorf("cannot found peerInfo from Context")
	}

	//ctx := context.WithValue(conn.Context(), "sn", sn)
	//ctx = context.WithValue(ctx, "remoteAddr", remoteAddr)
	//
	sns := metadata.ValueFromIncomingContext(conn.Context(), header.HeaderSN)
	if len(sns) == 0 {
		return fmt.Errorf("remoteAddr:%s cannot found sn from metadata", remoteAddr)
	}
	sn = sns[0]
	log.Infof("remoteAddr:%s sn:%s", remoteAddr, sn)
	s.clientMaps.Store(sn, &Client{Conn: conn, RemoteAddr: remoteAddr})

	localProxyAddr := hostinfo.GetHostIP() + ":" + strings.Split(s.conf.ProxyServerAddr, ":")[1]

	anyData, err := anypb.New(
		&proto.MetadataMessage{
			Md: map[string]string{header.HeaderServerAddress: localProxyAddr}})

	if err != nil {
		log.Error(err)
		return err
	}

	err = conn.Send(&pb.ExchangeMessage{
		Id:   stream.GetStreamID(),
		Path: stream.PathInit,
		Data: anyData,
	})
	if err != nil {
		log.Error(err)
		return err
	}

	s.Route(stream.PathRPC, s)
	for {
		req, err := conn.Recv()
		if err != nil {
			log.Errorf("sn:%s, remoteAddr:%s err:%v", sn, remoteAddr, err)
			s.clientMaps.Delete(sn)
		}
		if err == io.EOF {
			log.Errorf("sn:%s, remoteAddr:%s conn is EOF", sn, remoteAddr)
			return nil
		}

		if err != nil {
			return err
		}

		if req == nil {
			continue
		}

		mid := req.Id
		if req.Data != nil {
			log.Infof("sn:%s, mid:%s typeUrl:%s valueLen:%d", sn, mid, req.Data.TypeUrl, len(req.Data.Value))
		} else {
			log.Warnf("sn:%s, mid:%s data is nil", sn, mid)
		}

		s.handlerLock.RLock()
		handler, ok := s.handlers[req.Path]
		s.handlerLock.RUnlock()

		if !ok {
			log.Error(err)
			continue
		}

		go func() {
			if err := handler.Serve(context.TODO(), req, nil); err != nil {
				log.Errorf("sn:%s, mid:%s, err:%v", sn, mid, err)

			}
		}()

	}
}
