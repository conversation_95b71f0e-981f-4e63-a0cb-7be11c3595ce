package service

import (
	"testing"

	"mpp/internal/gateway/pkg/conf"
	"mpp/pkg/agent"
	"mpp/pkg/proto/utils"

	"google.golang.org/protobuf/proto"
)

// go test -bench=. -benchmem ./internal/gateway/pkg/service/
// tests the performance of different approaches to modify the NetIp field of the agent config

// BenchmarkProtoClone tests the performance of proto.Clone approach
func BenchmarkProtoClone(b *testing.B) {
	config := &agent.AgentConfig{
		MnsEndpoint:     "mns://test:8848",
		OssEndpoint:     "oss://test:8848",
		TracerEndpoint:  "sls://test:10010?project=test",
		MetricsEndpoint: "https://test:443/prometheus/test/api/v1/write?interval=5",
		NacosEndpoint:   "nacos://test:8848",
		NetIp:           "",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Current approach: proto.Clone
		agentConf := proto.Clone(config).(*agent.AgentConfig)
		agentConf.NetIp = "*************"

		// Simulate serialization
		_, err := utils.ProtoToJSON(agentConf)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkShallowCopy tests the performance of shallow copy approach
func BenchmarkShallowCopy(b *testing.B) {
	config := &agent.AgentConfig{
		MnsEndpoint:     "mns://test:8848",
		OssEndpoint:     "oss://test:8848",
		TracerEndpoint:  "sls://test:10010?project=test",
		MetricsEndpoint: "https://test:443/prometheus/test/api/v1/write?interval=5",
		NacosEndpoint:   "nacos://test:8848",
		NetIp:           "",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Shallow copy approach
		agentConf := *config
		agentConf.NetIp = "*************"

		// Simulate serialization
		_, err := utils.ProtoToJSON(&agentConf)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkDirectModify tests the performance of direct modification (unsafe for concurrency)
func BenchmarkDirectModify(b *testing.B) {
	config := &agent.AgentConfig{
		MnsEndpoint:     "mns://test:8848",
		OssEndpoint:     "oss://test:8848",
		TracerEndpoint:  "sls://test:10010?project=test",
		MetricsEndpoint: "https://test:443/prometheus/test/api/v1/write?interval=5",
		NacosEndpoint:   "nacos://test:8848",
		NetIp:           "",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Direct modification (not safe for concurrency)
		originalNetIp := config.NetIp
		config.NetIp = "*************"

		// Simulate serialization
		_, err := utils.ProtoToJSON(config)
		if err != nil {
			b.Fatal(err)
		}

		// Restore original value
		config.NetIp = originalNetIp
	}
}

// BenchmarkConfigCacheAccess tests the performance of config cache access
func BenchmarkConfigCacheAccess(b *testing.B) {
	config := &conf.Config{
		Agent: agent.AgentConfig{
			MnsEndpoint:     "mns://test:8848",
			OssEndpoint:     "oss://test:8848",
			TracerEndpoint:  "sls://test:10010?project=test",
			MetricsEndpoint: "https://test:443/prometheus/test/api/v1/write?interval=5",
			NacosEndpoint:   "nacos://test:8848",
		},
		AgentVersionRules: []conf.AgentVersionRule{
			{
				Name: "test-rule",
				Conditions: conf.VersionCondition{
					BuildDateMax: "20250730000000",
				},
				ExcludeFields: []string{"MnsEndpoint"},
			},
		},
	}

	service := NewConfigurableService(config)
	agentInfo := AgentInfo{BuildDate: "20240101000000"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Get config key
		configKey := service.getConfigKey(agentInfo)

		// Get cached config
		cachedConfig, exists := service.configCache[configKey]
		if !exists {
			b.Fatal("Config not found in cache")
		}

		// Current approach: proto.Clone
		agentConf := proto.Clone(cachedConfig).(*agent.AgentConfig)
		agentConf.NetIp = "*************"

		// Simulate serialization
		_, err := utils.ProtoToJSON(agentConf)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkEndToEndComparison compares the full end-to-end performance
func BenchmarkEndToEndProtoClone(b *testing.B) {
	config := &conf.Config{
		Agent: agent.AgentConfig{
			MnsEndpoint:     "mns://test:8848",
			OssEndpoint:     "oss://test:8848",
			TracerEndpoint:  "sls://test:10010?project=test",
			MetricsEndpoint: "https://test:443/prometheus/test/api/v1/write?interval=5",
			NacosEndpoint:   "nacos://test:8848",
		},
		AgentVersionRules: []conf.AgentVersionRule{
			{
				Name: "test-rule",
				Conditions: conf.VersionCondition{
					BuildDateMax: "20250730000000",
				},
				ExcludeFields: []string{"MnsEndpoint"},
			},
		},
	}

	service := NewConfigurableService(config)
	agentInfo := AgentInfo{BuildDate: "20240101000000"}
	configKey := service.getConfigKey(agentInfo)
	cachedConfig := service.configCache[configKey]

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// proto.Clone approach
		agentConf := proto.Clone(cachedConfig).(*agent.AgentConfig)
		agentConf.NetIp = "*************"
		_, err := utils.ProtoToJSON(agentConf)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkEndToEndShallowCopy(b *testing.B) {
	config := &conf.Config{
		Agent: agent.AgentConfig{
			MnsEndpoint:     "mns://test:8848",
			OssEndpoint:     "oss://test:8848",
			TracerEndpoint:  "sls://test:10010?project=test",
			MetricsEndpoint: "https://test:443/prometheus/test/api/v1/write?interval=5",
			NacosEndpoint:   "nacos://test:8848",
		},
		AgentVersionRules: []conf.AgentVersionRule{
			{
				Name: "test-rule",
				Conditions: conf.VersionCondition{
					BuildDateMax: "20250730000000",
				},
				ExcludeFields: []string{"MnsEndpoint"},
			},
		},
	}

	service := NewConfigurableService(config)
	agentInfo := AgentInfo{BuildDate: "20240101000000"}
	configKey := service.getConfigKey(agentInfo)
	cachedConfig := service.configCache[configKey]

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Shallow copy approach
		agentConf := *cachedConfig
		agentConf.NetIp = "*************"
		_, err := utils.ProtoToJSON(&agentConf)
		if err != nil {
			b.Fatal(err)
		}
	}
}
