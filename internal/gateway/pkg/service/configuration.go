package service

import (
	"context"
	"fmt"
	"net"
	"reflect"
	"strconv"
	"strings"

	"mpp/internal/gateway/pkg/conf"
	"mpp/pkg/agent"
	"mpp/pkg/proto/utils"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/peer"
	"google.golang.org/protobuf/proto"
	configpb "proto.mpp/api/configuration/v1"
)

type ConfigurableService struct {
	configpb.UnimplementedConfigurationServer
	conf *conf.Config
	// Pre-computed config cache, key is rule combination hash, value is corresponding config
	configCache map[string]*agent.AgentConfig
}

func NewConfigurableService(conf *conf.Config) *ConfigurableService {
	service := &ConfigurableService{
		conf:        conf,
		configCache: make(map[string]*agent.AgentConfig),
	}
	service.precomputeConfigs()
	return service
}

func (c *ConfigurableService) Get(ctx context.Context, r *configpb.GetConfigurationRequest) (*configpb.GetConfigurationResponse, error) {
	log.Infof("ConfigurableService Get start...")
	var netIP string
	if pr, ok := peer.FromContext(ctx); ok {
		if tcpAddr, ok := pr.Addr.(*net.TCPAddr); ok {
			netIP = tcpAddr.IP.String()
		} else {
			netIP = pr.Addr.String()
		}
	}

	// Get real IP if there's a proxy in front
	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		rips := md.Get("x-real-ip")
		if len(rips) > 0 {
			netIP = rips[0]
		}
	}

	// Extract agent information
	agentInfo := extractAgentInfo(md)

	// Get corresponding config key
	configKey := c.getConfigKey(agentInfo)

	// Get pre-computed config from cache
	cachedConfig, exists := c.configCache[configKey]
	if !exists {
		log.Errorf("Config key not found in cache: %s", configKey)
		return nil, fmt.Errorf("config key not found in cache: %s", configKey)
	}

	// Clone config and set NetIp, see in configuration_bench_test.go
	// Performance evaluation (BenchmarkEndToEndProtoClone vs BenchmarkEndToEndShallowCopy):
	//   proto.Clone():   368,342 ns/op, 2,285 B/op, 61 allocs/op
	//   shallow copy:      4,764 ns/op, 1,400 B/op, 31 allocs/op
	// Although shallow copy is ~77x faster, proto.Clone() provides complete safety
	// and 0.368ms per request is acceptable for most scenarios
	agentConf := proto.Clone(cachedConfig).(*agent.AgentConfig)
	agentConf.NetIp = netIP

	out := &configpb.GetConfigurationResponse{
		Items: make(map[string]*configpb.ConfigurationItem),
	}

	str, err := utils.ProtoToJSON(agentConf)
	if err != nil {
		return nil, err
	}
	messageName := utils.MessageNameString(agentConf)
	out.Items[messageName] = &configpb.ConfigurationItem{
		Value:    str,
		Version:  "v0",
		Metadata: nil,
	}

	return out, err
}

// AgentInfo contains extracted agent metadata
type AgentInfo struct {
	EngineModel string
	BuildDate   string
	CommitID    string
}

// extractAgentInfo extracts agent information from gRPC metadata
func extractAgentInfo(md metadata.MD) AgentInfo {
	info := AgentInfo{}

	if buildDates := md.Get("x-md-global-agent-build-date"); len(buildDates) > 0 {
		info.BuildDate = buildDates[0]
	}
	if commitIDs := md.Get("x-md-global-agent-commit-id"); len(commitIDs) > 0 {
		info.CommitID = commitIDs[0]
	}
	if engineModels := md.Get("x-md-global-agent-model-id"); len(engineModels) > 0 {
		info.EngineModel = engineModels[0]
	}

	return info
}

// matchesConditions checks if agent info matches rule conditions
// return true if
// 1. all conditions are met
// 2. agentInfo.BuildDate is not set, return true: agent very old version which no matadata set
func (c *ConfigurableService) matchesConditions(agentInfo AgentInfo, conditions conf.VersionCondition) bool {
	log.Infof("ConfigurableService matchesConditions: agentInfo: %+v, conditions: %+v", agentInfo, conditions)
	// if agentInfo.BuildDate is not set, return true: agent very old version which no matadata set
	if agentInfo.BuildDate == "" {
		log.Infof("ConfigurableService matchesConditions: agentInfo.BuildDate is not set, return true")
		return true
	}

	// Check engine model match
	if len(conditions.EngineModel) > 0 {
		matched := false
		for _, engineModel := range conditions.EngineModel {
			if agentInfo.EngineModel == engineModel {
				matched = true
				break
			}
		}
		if !matched {
			log.Infof("ConfigurableService matchesConditions: engine model not matched, return false")
			return false
		}
	}

	// Check build date range
	// if build date is not set, return true
	if conditions.BuildDateMin != "" || conditions.BuildDateMax != "" {
		agentBuildTime, err := strconv.ParseInt(agentInfo.BuildDate, 10, 64)
		if err != nil {
			log.Errorf("ConfigurableService matchesConditions: invalid build date format: %s", agentInfo.BuildDate)
			return true // Invalid build date format, return true (agent very old version which no build date is not set correctly)
		}

		if conditions.BuildDateMin != "" {
			minTime, err := strconv.ParseInt(conditions.BuildDateMin, 10, 64)
			if err == nil && agentBuildTime < minTime {
				log.Infof("ConfigurableService matchesConditions: build date not matched, return false")
				return false
			}
		}

		if conditions.BuildDateMax != "" {
			maxTime, err := strconv.ParseInt(conditions.BuildDateMax, 10, 64)
			if err == nil && agentBuildTime > maxTime {
				log.Infof("ConfigurableService matchesConditions: build date not matched, return false")
				return false
			}
		}
		log.Infof("ConfigurableService matchesConditions true: build date: %s, build date min: %s, build date max: %s",
			agentInfo.BuildDate, conditions.BuildDateMin, conditions.BuildDateMax)
		return true
	}

	return true
}

// applyRuleToConfig applies a rule to modify the config
// if excludeFields is not empty, exclude the specified fields
// Aone: https://project.aone.alibaba-inc.com/v2/project/1143023/bug/68348303#
func (c *ConfigurableService) applyRuleToConfig(config *agent.AgentConfig, rule conf.AgentVersionRule) *agent.AgentConfig {
	if len(rule.ExcludeFields) == 0 {
		log.Infof("ConfigurableService applyRuleToConfig: no exclude fields, return config")
		return config
	}

	// Use reflection to exclude specified fields
	configValue := reflect.ValueOf(config).Elem()
	configType := configValue.Type()

	for _, fieldName := range rule.ExcludeFields {
		// Find field by name (case-insensitive)
		for i := 0; i < configType.NumField(); i++ {
			field := configType.Field(i)
			if strings.EqualFold(field.Name, fieldName) {
				fieldValue := configValue.Field(i)
				if fieldValue.CanSet() {
					// Set field to zero value
					fieldValue.Set(reflect.Zero(fieldValue.Type()))
				}
				break
			}
		}
	}

	return config
}

// precomputeConfigs pre-computes all possible rule combination configurations
func (c *ConfigurableService) precomputeConfigs() {
	ruleCount := len(c.conf.AgentVersionRules)
	if ruleCount == 0 {
		// When no rules, only cache base config
		c.configCache["0"] = proto.Clone(&c.conf.Agent).(*agent.AgentConfig)
		return
	}

	// Calculate all possible rule combinations (2^n types)
	maxCombinations := 1 << ruleCount // 2^n

	for mask := 0; mask < maxCombinations; mask++ {
		// Start from base config
		config := proto.Clone(&c.conf.Agent).(*agent.AgentConfig)

		// Apply corresponding rules
		for i := 0; i < ruleCount; i++ {
			if mask&(1<<i) != 0 { // The i-th rule needs to be applied
				rule := c.conf.AgentVersionRules[i]
				config = c.applyRuleToConfig(config, rule)
			}
		}

		// Cache config
		c.configCache[strconv.Itoa(mask)] = config
	}

	log.Infof("Pre-computation completed, generated %d config combinations", maxCombinations)
}

// getConfigKey determines which config key to use based on agent info
func (c *ConfigurableService) getConfigKey(agentInfo AgentInfo) string {
	if len(c.conf.AgentVersionRules) == 0 {
		return "0"
	}

	mask := 0
	for i, rule := range c.conf.AgentVersionRules {
		if c.matchesConditions(agentInfo, rule.Conditions) {
			mask |= (1 << i) // Set the i-th bit
		}
	}

	return strconv.Itoa(mask)
}
