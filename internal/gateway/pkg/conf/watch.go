package conf

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/cinience/animus/config"
	kconf "github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/log"
	"gopkg.in/yaml.v3"
)

type RouterConfig struct {
	RouterConfig map[string]string `json:"routerConfig" yaml:"router_config"`
	Directory    string            `json:"directory" yaml:"directory"`
	Routers      []string          `json:"routers" yaml:"routers"`
}

type RouterWatcher struct {
	// routerConfigSource map[string]kconf.Source `json:"routerConfigSource" yaml:"router_config_source"`
	routerConfigSource map[string]kconf.Source
}

const entryDataId = "mpp-gateway-routers-entry.yml"

var ErrorNoRoutersEntryConfig = errors.New("no routers entry config")

func WatchRouter(configServer string, defaultConfigDir string, ctx context.Context) {
	const gatewayRouterConfigDir = "/home/<USER>/mpp-gateway/conf/gateway/subs"
	if defaultConfigDir == "" {
		defaultConfigDir = gatewayRouterConfigDir
	}

	rw := NewRouterWatcher()
	err := rw.UpdateRouterConfig(configServer, defaultConfigDir)
	if err != nil && !errors.Is(err, ErrorNoRoutersEntryConfig) {
		log.Errorf("UpdateRouterConfig error: %v", err)
		panic(err)
	}

	for {
		select {
		case <-ctx.Done():
			return
		case <-time.After(10 * time.Second):
			err = rw.UpdateRouterConfig(configServer, defaultConfigDir)
			if err != nil {
				if errors.Is(err, ErrorNoRoutersEntryConfig) {
					log.Debugf("no routers entry config")
				} else {
					log.Errorf("UpdateRouterConfig error: %v", err)
				}
			}
		}
	}
}

func NewRouterWatcher() *RouterWatcher {
	return &RouterWatcher{routerConfigSource: make(map[string]kconf.Source)}
}

func (r *RouterWatcher) UpdateRouterConfig(configServer string, confDir string) error {
	if configServer == "" || configServer == "nil" {
		return fmt.Errorf("ConfigServer is not enabled")
	}

	uri := configServer

	const defaultGroup = "routers"
	value, err := r.getConfig(uri, entryDataId)
	if err != nil {
		return err
	}

	var routers RouterConfig
	if strings.HasSuffix(entryDataId, ".yml") {
		err = yaml.Unmarshal(value, &routers)
	} else if strings.HasSuffix(entryDataId, ".json") {
		err = json.Unmarshal(value, &routers)
	} else {
		err = fmt.Errorf("unknown config file format")
	}
	if err != nil {
		return fmt.Errorf("cannot unmarshal config.err: %v", err)
	}

	if routers.Directory != "" {
		confDir = routers.Directory
	}

	for _, routerName := range routers.Routers {
		if !strings.HasSuffix(routerName, ".yml") {
			routerName = routerName + ".yml"
		}

		routerValue, cErr := r.getConfig(uri, routerName)
		if cErr != nil {
			return cErr
		}

		needUpdate := false
		fileName := filepath.Join(confDir, routerName)
		// 使用 os.Stat 检查文件是否存在
		_, err = os.Stat(fileName)
		if err == nil {
			// 文件存在
			content, rErr := os.ReadFile(fileName)
			if rErr != nil {
				return rErr
			}
			if string(content) != string(routerValue) {
				needUpdate = true
			}
		} else if err != nil && os.IsNotExist(err) {
			// 文件不存在
			needUpdate = true
		} else {
			return err
		}

		if needUpdate {
			log.Infof("start update router config: %s", fileName)
			err = os.WriteFile(fileName, routerValue, 0644)
			if err != nil {
				return err
			}
			log.Infof("end update router config: %s", fileName)
		}
	}

	return err
}

func (r *RouterWatcher) getConfig(uri string, dataId string) ([]byte, error) {
	u, err := url.Parse(uri)
	if err != nil {
		return nil, err
	}

	q := u.Query()
	const keyGroup = "group"
	const keyId = "dataId"

	// 不能替换分组
	// q.Set(keyGroup, group)
	q.Set(keyId, dataId)

	newUri := fmt.Sprintf("%s://%s?%s", u.Scheme, u.Host, q.Encode())

	p, exists := r.routerConfigSource[newUri]
	if !exists {
		fc := config.GetConfigProvider(u.Scheme)
		if fc == nil {
			return nil, fmt.Errorf("cannot find config provider for %s", u.Scheme)

		}
		p = fc(newUri)
		r.routerConfigSource[newUri] = p
	}

	kv, err := p.Load()
	if err != nil {
		if strings.Contains(err.Error(), "config data not exist") && dataId == entryDataId {
			return nil, ErrorNoRoutersEntryConfig
		}
		return nil, err
	}
	if len(kv) != 1 {
		return nil, fmt.Errorf("cannot load config from %s", newUri)
	}

	return kv[0].Value, nil
}
