package conf

import (
	"context"
	"fmt"

	"mpp/pkg/agent"

	"github.com/cinience/animus/config"
)

// AgentVersionRule defines rules for different agent versions
// Aone: https://project.aone.alibaba-inc.com/v2/project/1143023/bug/68348303#
type AgentVersionRule struct {
	Name          string           `json:"name"`          // Rule name for debugging
	Conditions    VersionCondition `json:"conditions"`    // Matching conditions
	ExcludeFields []string         `json:"excludeFields"` // Fields to exclude from config
}

// VersionCondition defines matching conditions for agent versions
type VersionCondition struct {
	EngineModel    []string `json:"engineModel,omitempty"`    // Match specific engine models
	BuildDateMin   string   `json:"buildDateMin,omitempty"`   // Minimum build date (YYYYMMDDHHMMSS)
	BuildDateMax   string   `json:"buildDateMax,omitempty"`   // Maximum build date (YYYYMMDDHHMMSS)
	CommitPrefixes []string `json:"commitPrefixes,omitempty"` // Match commit ID prefixes
}

type Config struct {
	ConfigServer string `json:"configServer"`

	GrpcServerAddr  string `json:"grpcServerAddr"`
	ProxyServerAddr string `json:"proxyServerAddr"`
	RegistryUrl     string `json:"registryUrl"`
	TracingUrl      string `json:"tracingUrl"`

	Agent agent.AgentConfig `json:"agent"`

	// Agent version compatibility rules
	AgentVersionRules []AgentVersionRule `json:"agentVersionRules,omitempty"`
}

func NewConfig() (*Config, error) {
	var conf Config

	if err := config.NewConfig(&conf); err != nil {
		return nil, err
	}

	if conf.GrpcServerAddr == "" {
		conf.GrpcServerAddr = ":12007"
	}

	if conf.ProxyServerAddr == "" {
		conf.ProxyServerAddr = ":12008"
	}

	// add router config watch
	go func() {
		WatchRouter(conf.ConfigServer, "", context.Background())
	}()

	fmt.Printf("gateway config: %+v\n", &conf)
	fmt.Printf("AgentVersionRules count: %d\n", len(conf.AgentVersionRules))
	for i, rule := range conf.AgentVersionRules {
		fmt.Printf("Rule %d: %+v\n", i, rule)
	}
	return &conf, nil
}
