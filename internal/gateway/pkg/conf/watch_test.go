package conf

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"

	_ "github.com/cinience/animus/contribs/config/diamond"
	_ "github.com/cinience/animus/contribs/config/nacos"
)

func TestWatch(t *testing.T) {
	server := "diamond://jmenv.tbsite.net:8080/diamond-server/diamond?appName=mpp-gateway&dataId=mpp-gateway.json&group=00"
	rw := NewRouterWatcher()
	tmpDir, err := os.MkdirTemp("/tmp", "*")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)
	err = rw.UpdateRouterConfig(server, tmpDir)
	if err != nil {
		t.Fatal(err)
	}

	fileCount := 0
	filepath.WalkDir(tmpDir, func(path string, d os.DirEntry, err error) error {
		if err != nil {
			return err
		}
		if d.IsDir() {
			return nil
		}
		fileCount++
		t.Log(path)
		return nil
	})

	if fileCount == 0 {
		t.Fatal("no file found")
	}
}

func TestWatchConfig(t *testing.T) {
	server := "diamond://jmenv.tbsite.net:8080/diamond-server/diamond?appName=mpp-gateway&dataId=mpp-gateway.json&group=00"

	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer func() {
		cancel()
	}()

	tmpDir, err := os.MkdirTemp("/tmp", "*")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	WatchRouter(server, tmpDir, ctx)
}
