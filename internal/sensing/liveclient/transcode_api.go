package liveclient

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"io/ioutil"
	"net/http"
	"strings"
)

var (
	transcodeClient *http.Client
)

func init() {
	transcodeClient = &http.Client{}
}

func MigrateTask(transcodeHost, taskId string) error {
	data := map[string]interface{}{
		"taskId":       taskId,
		"sameDictator": true,
	}
	fmt.Printf("data:%s\n", data)
	resultData, err := SendRequest(transcodeHost, "/task/migrate", data)
	if err != nil {
		return err
	}

	log.Infof("get send task result, data:%s, resultData:%s", data, string(resultData))

	return nil
}

func GetTaskByWorker(transcodeHost, workerId string) ([]TaskInfo, error) {
	// 查询workerId

	data := map[string]interface{}{
		"workerUuid": workerId,
	}
	fmt.Printf("data:%s\n", data)
	resultData, err := SendRequest(transcodeHost, "/task/getWorkerTasks", data)
	if err != nil {
		log.Errorf("get task by worker error:%v", err)
		return nil, err
	}
	var taskInfo GetTasksByWorkerResponse
	err = json.Unmarshal(resultData, &taskInfo)
	if err != nil {
		log.Errorf("unmarshal task info fail, transcodeHost:%s, workerId:%s, resultData:%s", transcodeHost, workerId, string(resultData))
		return nil, err
	}

	log.Infof("get task by worker result, data:%s, taskInfo:%+v", data, taskInfo)
	return taskInfo.Data.Tasks, nil
}

func SendRequest(addr, uriPath string, params interface{}) ([]byte, error) {
	data, err := json.Marshal(params)
	if err != nil {
		log.Errorf("marshal param fail,error:%v", err)
		return nil, err
	}

	log.Infof("send request to transcode, addr:%s, uri:%s, data:%s", addr, uriPath, string(data))

	addr = strings.ReplaceAll(addr, "http://", "")
	req, err := http.NewRequest("POST", "http://"+addr+uriPath, bytes.NewReader(data))
	if err != nil {
		log.Errorf("construct http req fail,error:%v", err)
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	resp, err := transcodeClient.Do(req)
	if err != nil {
		log.Errorf("send request fail,error:%v", err)
		return nil, err
	}

	var bodyByte []byte
	if resp.Body != nil {
		bodyByte, err = ioutil.ReadAll(resp.Body)
		if err != nil {
			log.Errorf("read body fail when send transcode request,error:%v", err)
			return nil, err
		}
	}

	if resp.StatusCode != 200 {
		log.Errorf("server return fail when send transcode request, statusCode:%d, respBody:%s", resp.StatusCode, string(bodyByte))
		return bodyByte, ErrStatusCode
	}

	return bodyByte, nil
}
