package liveclient

import (
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/sensing/conf"
	"mpp/internal/sensing/pop"
	"strings"
)

type PopClient struct {
	c   *conf.Data
	log *log.Helper
}

func NewRmsPopClient(c *conf.Data) *PopClient {
	return &PopClient{
		c:   c,
		log: log.NewHelper(log.With(log.GetLogger(), "module", "internal/sensing/liveclient/rms_api")),
	}
}

func (p *PopClient) getPopClient(taskManagerHost string) *pop.Client {
	config := new(openapi.Config)
	config.SetAccessKeyId(p.c.Mns.AccessKeyId).
		SetAccessKeySecret(p.c.Mns.AccessKeySecret).
		SetEndpoint(taskManagerHost)
	popClient, _ := pop.NewClient(config)
	return popClient
}

func (p *PopClient) MigrateRmsTasks(text, taskManagerHost string, taskIds []string) error {
	var taskIdOrWorkerIdArrays []*string
	for _, taskId := range taskIds {
		taskIdOrWorkerIdArrays = append(taskIdOrWorkerIdArrays, &taskId)
	}
	request := &pop.JobAsyncMigrateRequest{}
	if strings.Contains(text, "worker级别") {
		request.WorkIds = taskIdOrWorkerIdArrays
	} else {
		request.TaskIds = taskIdOrWorkerIdArrays
	}

	// 配置为 区域前缀 = host;product;说明
	taskManagerHostAndProduct := strings.Split(taskManagerHost, ";")
	if len(taskManagerHostAndProduct) < 2 {
		log.Errorf("migrateRmsTask error,taskManagerHostAndProduct len is not 2, taskManagerHost:%s", taskManagerHost)
		return errors.InternalServer("taskManagerHost config error", "taskManagerHost config error")
	}
	request.SetProduct(taskManagerHostAndProduct[1]) // todo 可配置
	request.SetUserId("mpp")
	client := p.getPopClient(taskManagerHostAndProduct[0])
	result, _ := client.JobAsyncMigrate(request)
	if result == nil || result.StatusCode == nil {
		log.Errorf("migrate rms tasks fail, data:%s, result:%s", request, result)
		return errors.InternalServer("pop request error", "pop request error")
	} else if *result.StatusCode != 200 {
		log.Errorf("migrate rms tasks fail, data:%s, result:%s", request, result)
		return errors.InternalServer("pop request error", "pop request return "+string(*result.StatusCode))
	}
	return nil
}

func (p *PopClient) QueryRmsTask(taskManagerHost string, taskId string) (*pop.ManagerTaskQueryResponseBodyDataTask, error) {
	request := &pop.ManagerTaskQueryRequest{
		TaskId: &taskId,
	}
	request.SetJobId(taskId)
	// 配置为 taskId前缀 = host;product
	taskManagerHostAndProduct := strings.Split(taskManagerHost, ";")
	if len(taskManagerHostAndProduct) < 2 {
		log.Errorf("queryRmsTask error,taskManagerHostAndProduct len is not 2, taskManagerHost:%s", taskManagerHost)
		return nil, errors.InternalServer("taskManagerHost config error", "taskManagerHost config error")
	}
	request.SetProduct(taskManagerHostAndProduct[1]) // todo 可配置
	client := p.getPopClient(taskManagerHostAndProduct[0])
	result, _ := client.ManagerTaskQuery(request)
	if result == nil || result.StatusCode == nil {
		log.Errorf("query rms tasks fail, data:%s, result:%s", request, result)
		return nil, errors.InternalServer("pop request error", "pop request error or data not exist")
	} else if *result.StatusCode != 200 {
		log.Errorf("query rms tasks fail, data:%s, result:%s", request, result)
		return nil, errors.InternalServer("pop request error", "pop request return "+string(*result.StatusCode))
	}
	return result.Body.Data.Task, nil
}

//func MigrateRmsTasks(taskManagerHost string, taskIds []string) error {
//	requestId, _ := uuid.NewUUID()
//	log.Infof("migrate rms tasks, taskManagerHost:%s, taskIds:%s, requestId:{}", taskManagerHost, taskIds, requestId)
//	data := map[string]interface{}{
//		"taskIds":   taskIds,
//		"requestId": requestId,
//	}
//	resultData, err := SendRmsRequest(taskManagerHost, "/v1/task/migrate", data)
//	if err != nil {
//		log.Errorf("migrate rms tasks fail, data:%s, err:%s", data, err.Error())
//		return err
//	}
//	log.Infof("migrate rms tasks end, data:%s, resultData:%s", data, string(resultData))
//	return nil
//}

//func StopRmsTask(taskManagerHost string, taskId string) error {
//	requestId, _ := uuid.NewUUID()
//	log.Infof("stop rms tasks, taskManagerHost:%s, taskIds:%s, requestId:{}", taskManagerHost, taskId, requestId)
//	data := map[string]interface{}{
//		"taskId":    taskId,
//		"jobId":     taskId,
//		"requestId": requestId,
//	}
//	resultData, err := SendRmsRequest(taskManagerHost, "/v1/task/stop", data)
//	if err != nil {
//		log.Errorf("stop rms tasks fail, data:%s, err:%s", data, err.Error())
//		return err
//	}
//	log.Infof("stop rms tasks end, data:%s, resultData:%s", data, string(resultData))
//	return nil
//}

func (p *PopClient) StopRmsTask(taskManagerHost string, taskId string) error {
	request := &pop.JobAsyncStopRequest{
		JobId: &taskId,
	}
	taskManagerHostAndProduct := strings.Split(taskManagerHost, ";")
	if len(taskManagerHostAndProduct) < 2 {
		log.Errorf("stopRmsTask error, taskManagerHostAndProduct len is not 2, taskManagerHost:%s", taskManagerHost)
		return errors.InternalServer("taskManagerHost config error", "taskManagerHost config error")
	}
	request.SetProduct(taskManagerHostAndProduct[1])
	request.SetUserId("mpp")
	client := p.getPopClient(taskManagerHostAndProduct[0])
	result, _ := client.JobAsyncStop(request)
	if result == nil || result.StatusCode == nil {
		log.Errorf("stop rms tasks fail, data:%s, result:%s", request, result)
		return errors.InternalServer("pop request error", "pop request error")
	} else if *result.StatusCode != 200 {
		log.Errorf("stop rms tasks fail, data:%s, result:%s", request, result)
		return errors.InternalServer("pop request error", "pop request return "+string(*result.StatusCode))
	}
	return nil
}

//// todo 启动任务
//func StartRmsTask(taskManagerHost string, taskId string) error {
//	// 启动任务接口，需要查询任务信息后再启动
//	requestId, _ := uuid.NewUUID()
//	log.Infof("stop rms tasks, taskManagerHost:%s, taskIds:%s, requestId:{}", taskManagerHost, taskId, requestId)
//	data := map[string]interface{}{
//		"taskId":    taskId,
//		"requestId": requestId,
//	}
//	resultData, err := SendRmsRequest(taskManagerHost, "/v1/task/start", data)
//	if err != nil {
//		return err
//	}
//	log.Infof("stop rms tasks end, data:%s, resultData:%s", data, string(resultData))
//	return nil
//}
//
//func SendRmsRequest(addr, uriPath string, params interface{}) ([]byte, error) {
//	data, err := json.Marshal(params)
//	if err != nil {
//		log.Errorf("marshal param fail", err.Error())
//		return nil, err
//	}
//
//	log.Infof("send request to transcode, addr:%s, uri:%s, data:%s", addr, uriPath, string(data))
//
//	req, err := http.NewRequest("POST", "http://"+addr+uriPath, bytes.NewReader(data))
//	if err != nil {
//		log.Errorf("construct http req fail", err.Error())
//		return nil, err
//	}
//
//	req.Header.Set("Content-Type", "application/json")
//	resp, err := rmsClient.Do(req)
//	if err != nil {
//		log.Errorf("send request fail", err.Error())
//		return nil, err
//	}
//
//	var bodyByte []byte
//	if resp.Body != nil {
//		bodyByte, err = io.ReadAll(resp.Body)
//		if err != nil {
//			log.Errorf("read body fail when send transcode request", err.Error())
//			return nil, err
//		}
//	}
//
//	if resp.StatusCode != 200 {
//		log.Errorf("server return fail when send transcode request, code:{}, body:{}", resp.StatusCode, string(bodyByte))
//		return bodyByte, errors.New(string(bodyByte))
//	}
//
//	return bodyByte, nil
//}
