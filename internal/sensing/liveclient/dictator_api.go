package liveclient

import (
	"bytes"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"io/ioutil"
	"math/rand"
	"net/http"
	"strings"
	"time"
)

const (
	DICTATOR_SECRET = "DIC!2017"
)

var (
	dictatorClient *http.Client
	ErrStatusCode  = errors.New("code not success")
)

func init() {
	dictatorClient = &http.Client{}
}

func AddTag(dictatorHost, tag string, ips []string) error {
	data := map[string]interface{}{
		"tag":    tag,
		"ipList": ips,
	}

	resultData, err := SendDictatorRequest(dictatorHost, "/worker/addTag", data)
	if err != nil {
		return err
	}

	log.Infof("get send task result, data:%s, resultData:%s", data, string(resultData))

	return nil
}

func GetWorkerInfoByIp(dictatorHost, workerIp string) *WorkerInfo {
	log.Infof("get worker info by ip, dictatorHost:%s, workerIp:%s", dictatorHost, workerIp)
	request := map[string]interface{}{
		"ip": workerIp,
	}
	resultData, err := SendDictatorRequest(dictatorHost, "/worker/getWorkerByIp", request)
	if err != nil {
		log.Errorf("get worker info by ip fail, dictatorHost:%s, workerIp:%s, err:%s", dictatorHost, workerIp, err.Error())
		return nil
	}

	var resp GetWorkersByIpResponse
	err = json.Unmarshal(resultData, &resp)
	if err != nil {
		log.Errorf("unmarshal worker info fail, dictatorHost:%s, workerIp:%s, resultData:%s,err:%+v", dictatorHost, workerIp, string(resultData), err)
		return nil
	}
	log.Infof("get worker info by ip, dictatorHost:%s, workerIp:%s, workerInfo:%+v", dictatorHost, workerIp, resp)
	return &resp.Data.Worker
}

func SendDictatorRequest(addr, uriPath string, params interface{}) ([]byte, error) {
	data, err := json.Marshal(params)
	if err != nil {
		log.Errorf("marshal param fail,error:%v", err)
		return nil, err
	}

	log.Infof("send request to dictator,addr:%s, uri:%s, data:%s", addr, uriPath, string(data))
	addr = strings.ReplaceAll(addr, "http://", "")
	req, err := http.NewRequest("POST", "http://"+addr+uriPath, bytes.NewReader(data))
	if err != nil {
		log.Errorf("construct http req fail,error:%v", err)
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", DictatorAuth(uriPath, string(data)))

	resp, err := dictatorClient.Do(req)
	if err != nil {
		log.Errorf("send request fail,error:%v", err)
		return nil, err
	}

	var bodyByte []byte
	if resp.Body != nil {
		bodyByte, err = ioutil.ReadAll(resp.Body)
		if err != nil {
			log.Errorf("read body fail when send dictator request,error:%v", err)
			return nil, err
		}
	}

	if resp.StatusCode != 200 {
		log.Errorf("server return fail when send dictator request, statusCode:%d, respBody:%s", resp.StatusCode, string(bodyByte))
		return bodyByte, ErrStatusCode
	}

	return bodyByte, nil
}

func DictatorAuth(uri, body string) string {
	authTimestamp := time.Now().Add(time.Minute).Unix()
	authRand := rand.Intn(10000)

	rawStr := fmt.Sprintf("%s-%d-%d-%s-%s", uri, authTimestamp, authRand, body, DICTATOR_SECRET)

	key := fmt.Sprintf("%x", md5.Sum([]byte(rawStr)))

	result := fmt.Sprintf("%d-%d-%s", authTimestamp, authRand, key)

	return result
}
