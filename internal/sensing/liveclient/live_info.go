package liveclient

type WorkerInfo struct {
	Status        byte           `json:"status,omitempty"`
	RestQuotaRef  int            `json:"restQuotaRef,omitempty"`
	RealQuota     int            `json:"realQuota,omitempty"`
	IP            string         `json:"ip,omitempty"`
	Resources     []ResourceInfo `json:"resources,omitempty"`
	OnlineVersion int            `json:"onlineVersion,omitempty"`
	Quota         int            `json:"quota,omitempty"`
	Tag           string         `json:"tag,omitempty"`
	CurrentQuota  int            `json:"currentQuota,omitempty"`
	Load1         float64        `json:"load1,omitempty"`
	Uuid          string         `json:"uuid,omitempty"`
}

type ResourceInfo struct {
	Uuid                string `json:"uuid,omitempty"`
	Quota               int    `json:"quota,omitempty"`
	WorkerUuid          string `json:"workerUuid,omitempty"`
	WorkerOnlineVersion int    `json:"workerOnlineVersion,omitempty"`
}

type TaskInfo struct {
	TaskId              string `json:"taskId,omitempty"`
	Quota               int    `json:"quota,omitempty"`
	Params              string `json:"params,omitempty"`
	Status              int    `json:"status,omitempty"`
	WorkerUuid          string `json:"workerUuid,omitempty"`
	WorkerIp            string `json:"workerIp,omitempty"`
	WorkerOnlineVersion int    `json:"workerOnlineVersion,omitempty"`
	Running             int    `json:"running,omitempty"`
	Env                 string `json:"env,omitempty"`
	TemplateName        string `json:"templateName,omitempty"`
	Extend              string `json:"extend,omitempty"`
	UserData            string `json:"userData,omitempty"`
	GmtCreate           int64  `json:"gmtCreate,omitempty"`
	GmtModified         int64  `json:"gmtModified,omitempty"`
}

type GetWorkersByIpResponse struct {
	Result ResponseResultDTO          `json:"result"`
	Data   GetWorkersByIpResponseData `json:"data"`
}

type GetWorkersByIpResponseData struct {
	Worker WorkerInfo `json:"worker"`
}

type GetTasksByWorkerResponse struct {
	Result ResponseResultDTO           `json:"result"`
	Data   GetTaskByWorkerResponseData `json:"data"`
}

type GetTaskByWorkerResponseData struct {
	Tasks []TaskInfo `json:"tasks"`
}

type ResponseResultDTO struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}
