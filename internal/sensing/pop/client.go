// This file is auto-generated, don't edit it. Thanks.
/**
 *
 */
package pop

import (
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	endpointutil "github.com/alibabacloud-go/endpoint-util/service"
	openapiutil "github.com/alibabacloud-go/openapi-util/service"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
)

type JobAsyncMigrateRequest struct {
	Product *string   `json:"product,omitempty" xml:"product,omitempty"`
	TaskIds []*string `json:"taskIds,omitempty" xml:"taskIds,omitempty" type:"Repeated"`
	UserId  *string   `json:"userId,omitempty" xml:"userId,omitempty"`
	WorkIds []*string `json:"workIds,omitempty" xml:"workIds,omitempty" type:"Repeated"`
}

func (s JobAsyncMigrateRequest) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncMigrateRequest) GoString() string {
	return s.String()
}

func (s *JobAsyncMigrateRequest) SetProduct(v string) *JobAsyncMigrateRequest {
	s.Product = &v
	return s
}

func (s *JobAsyncMigrateRequest) SetTaskIds(v []*string) *JobAsyncMigrateRequest {
	s.TaskIds = v
	return s
}

func (s *JobAsyncMigrateRequest) SetUserId(v string) *JobAsyncMigrateRequest {
	s.UserId = &v
	return s
}

func (s *JobAsyncMigrateRequest) SetWorkIds(v []*string) *JobAsyncMigrateRequest {
	s.WorkIds = v
	return s
}

type JobAsyncMigrateShrinkRequest struct {
	Product       *string `json:"product,omitempty" xml:"product,omitempty"`
	TaskIdsShrink *string `json:"taskIds,omitempty" xml:"taskIds,omitempty"`
	UserId        *string `json:"userId,omitempty" xml:"userId,omitempty"`
	WorkIdsShrink *string `json:"workIds,omitempty" xml:"workIds,omitempty"`
}

func (s JobAsyncMigrateShrinkRequest) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncMigrateShrinkRequest) GoString() string {
	return s.String()
}

func (s *JobAsyncMigrateShrinkRequest) SetProduct(v string) *JobAsyncMigrateShrinkRequest {
	s.Product = &v
	return s
}

func (s *JobAsyncMigrateShrinkRequest) SetTaskIdsShrink(v string) *JobAsyncMigrateShrinkRequest {
	s.TaskIdsShrink = &v
	return s
}

func (s *JobAsyncMigrateShrinkRequest) SetUserId(v string) *JobAsyncMigrateShrinkRequest {
	s.UserId = &v
	return s
}

func (s *JobAsyncMigrateShrinkRequest) SetWorkIdsShrink(v string) *JobAsyncMigrateShrinkRequest {
	s.WorkIdsShrink = &v
	return s
}

type JobAsyncMigrateResponseBody struct {
	Result *JobAsyncMigrateResponseBodyResult `json:"result,omitempty" xml:"result,omitempty" type:"Struct"`
}

func (s JobAsyncMigrateResponseBody) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncMigrateResponseBody) GoString() string {
	return s.String()
}

func (s *JobAsyncMigrateResponseBody) SetResult(v *JobAsyncMigrateResponseBodyResult) *JobAsyncMigrateResponseBody {
	s.Result = v
	return s
}

type JobAsyncMigrateResponseBodyResult struct {
	Code     *int32             `json:"code,omitempty" xml:"code,omitempty"`
	Message  *string            `json:"message,omitempty" xml:"message,omitempty"`
	Metadata map[string]*string `json:"metadata,omitempty" xml:"metadata,omitempty"`
	Reason   *string            `json:"reason,omitempty" xml:"reason,omitempty"`
}

func (s JobAsyncMigrateResponseBodyResult) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncMigrateResponseBodyResult) GoString() string {
	return s.String()
}

func (s *JobAsyncMigrateResponseBodyResult) SetCode(v int32) *JobAsyncMigrateResponseBodyResult {
	s.Code = &v
	return s
}

func (s *JobAsyncMigrateResponseBodyResult) SetMessage(v string) *JobAsyncMigrateResponseBodyResult {
	s.Message = &v
	return s
}

func (s *JobAsyncMigrateResponseBodyResult) SetMetadata(v map[string]*string) *JobAsyncMigrateResponseBodyResult {
	s.Metadata = v
	return s
}

func (s *JobAsyncMigrateResponseBodyResult) SetReason(v string) *JobAsyncMigrateResponseBodyResult {
	s.Reason = &v
	return s
}

type JobAsyncMigrateResponse struct {
	Headers    map[string]*string           `json:"headers,omitempty" xml:"headers,omitempty"`
	StatusCode *int32                       `json:"statusCode,omitempty" xml:"statusCode,omitempty"`
	Body       *JobAsyncMigrateResponseBody `json:"body,omitempty" xml:"body,omitempty"`
}

func (s JobAsyncMigrateResponse) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncMigrateResponse) GoString() string {
	return s.String()
}

func (s *JobAsyncMigrateResponse) SetHeaders(v map[string]*string) *JobAsyncMigrateResponse {
	s.Headers = v
	return s
}

func (s *JobAsyncMigrateResponse) SetStatusCode(v int32) *JobAsyncMigrateResponse {
	s.StatusCode = &v
	return s
}

func (s *JobAsyncMigrateResponse) SetBody(v *JobAsyncMigrateResponseBody) *JobAsyncMigrateResponse {
	s.Body = v
	return s
}

type JobAsyncStartRequest struct {
	EngineModel    *string `json:"engineModel,omitempty" xml:"engineModel,omitempty"`
	EngineParams   *string `json:"engineParams,omitempty" xml:"engineParams,omitempty"`
	JobId          *string `json:"jobId,omitempty" xml:"jobId,omitempty"`
	JobType        *string `json:"jobType,omitempty" xml:"jobType,omitempty"`
	Notify         *string `json:"notify,omitempty" xml:"notify,omitempty"`
	Product        *string `json:"product,omitempty" xml:"product,omitempty"`
	ScheduleParams *string `json:"scheduleParams,omitempty" xml:"scheduleParams,omitempty"`
	Tag            *string `json:"tag,omitempty" xml:"tag,omitempty"`
	TaskId         *string `json:"taskId,omitempty" xml:"taskId,omitempty"`
	Token          *string `json:"token,omitempty" xml:"token,omitempty"`
	Trace          *string `json:"trace,omitempty" xml:"trace,omitempty"`
	UserData       *string `json:"userData,omitempty" xml:"userData,omitempty"`
	UserId         *string `json:"userId,omitempty" xml:"userId,omitempty"`
}

func (s JobAsyncStartRequest) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncStartRequest) GoString() string {
	return s.String()
}

func (s *JobAsyncStartRequest) SetEngineModel(v string) *JobAsyncStartRequest {
	s.EngineModel = &v
	return s
}

func (s *JobAsyncStartRequest) SetEngineParams(v string) *JobAsyncStartRequest {
	s.EngineParams = &v
	return s
}

func (s *JobAsyncStartRequest) SetJobId(v string) *JobAsyncStartRequest {
	s.JobId = &v
	return s
}

func (s *JobAsyncStartRequest) SetJobType(v string) *JobAsyncStartRequest {
	s.JobType = &v
	return s
}

func (s *JobAsyncStartRequest) SetNotify(v string) *JobAsyncStartRequest {
	s.Notify = &v
	return s
}

func (s *JobAsyncStartRequest) SetProduct(v string) *JobAsyncStartRequest {
	s.Product = &v
	return s
}

func (s *JobAsyncStartRequest) SetScheduleParams(v string) *JobAsyncStartRequest {
	s.ScheduleParams = &v
	return s
}

func (s *JobAsyncStartRequest) SetTag(v string) *JobAsyncStartRequest {
	s.Tag = &v
	return s
}

func (s *JobAsyncStartRequest) SetTaskId(v string) *JobAsyncStartRequest {
	s.TaskId = &v
	return s
}

func (s *JobAsyncStartRequest) SetToken(v string) *JobAsyncStartRequest {
	s.Token = &v
	return s
}

func (s *JobAsyncStartRequest) SetTrace(v string) *JobAsyncStartRequest {
	s.Trace = &v
	return s
}

func (s *JobAsyncStartRequest) SetUserData(v string) *JobAsyncStartRequest {
	s.UserData = &v
	return s
}

func (s *JobAsyncStartRequest) SetUserId(v string) *JobAsyncStartRequest {
	s.UserId = &v
	return s
}

type JobAsyncStartResponseBody struct {
	RequestId *string `json:"RequestId,omitempty" xml:"RequestId,omitempty"`
	Code      *string `json:"code,omitempty" xml:"code,omitempty"`
	JobId     *string `json:"jobId,omitempty" xml:"jobId,omitempty"`
	Message   *string `json:"message,omitempty" xml:"message,omitempty"`
}

func (s JobAsyncStartResponseBody) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncStartResponseBody) GoString() string {
	return s.String()
}

func (s *JobAsyncStartResponseBody) SetRequestId(v string) *JobAsyncStartResponseBody {
	s.RequestId = &v
	return s
}

func (s *JobAsyncStartResponseBody) SetCode(v string) *JobAsyncStartResponseBody {
	s.Code = &v
	return s
}

func (s *JobAsyncStartResponseBody) SetJobId(v string) *JobAsyncStartResponseBody {
	s.JobId = &v
	return s
}

func (s *JobAsyncStartResponseBody) SetMessage(v string) *JobAsyncStartResponseBody {
	s.Message = &v
	return s
}

type JobAsyncStartResponse struct {
	Headers    map[string]*string         `json:"headers,omitempty" xml:"headers,omitempty"`
	StatusCode *int32                     `json:"statusCode,omitempty" xml:"statusCode,omitempty"`
	Body       *JobAsyncStartResponseBody `json:"body,omitempty" xml:"body,omitempty"`
}

func (s JobAsyncStartResponse) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncStartResponse) GoString() string {
	return s.String()
}

func (s *JobAsyncStartResponse) SetHeaders(v map[string]*string) *JobAsyncStartResponse {
	s.Headers = v
	return s
}

func (s *JobAsyncStartResponse) SetStatusCode(v int32) *JobAsyncStartResponse {
	s.StatusCode = &v
	return s
}

func (s *JobAsyncStartResponse) SetBody(v *JobAsyncStartResponseBody) *JobAsyncStartResponse {
	s.Body = v
	return s
}

type JobAsyncStopRequest struct {
	JobId   *string `json:"jobId,omitempty" xml:"jobId,omitempty"`
	Product *string `json:"product,omitempty" xml:"product,omitempty"`
	UserId  *string `json:"userId,omitempty" xml:"userId,omitempty"`
}

func (s JobAsyncStopRequest) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncStopRequest) GoString() string {
	return s.String()
}

func (s *JobAsyncStopRequest) SetJobId(v string) *JobAsyncStopRequest {
	s.JobId = &v
	return s
}

func (s *JobAsyncStopRequest) SetProduct(v string) *JobAsyncStopRequest {
	s.Product = &v
	return s
}

func (s *JobAsyncStopRequest) SetUserId(v string) *JobAsyncStopRequest {
	s.UserId = &v
	return s
}

type JobAsyncStopResponseBody struct {
	RequestId *string `json:"RequestId,omitempty" xml:"RequestId,omitempty"`
	Code      *string `json:"code,omitempty" xml:"code,omitempty"`
	Message   *string `json:"message,omitempty" xml:"message,omitempty"`
}

func (s JobAsyncStopResponseBody) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncStopResponseBody) GoString() string {
	return s.String()
}

func (s *JobAsyncStopResponseBody) SetRequestId(v string) *JobAsyncStopResponseBody {
	s.RequestId = &v
	return s
}

func (s *JobAsyncStopResponseBody) SetCode(v string) *JobAsyncStopResponseBody {
	s.Code = &v
	return s
}

func (s *JobAsyncStopResponseBody) SetMessage(v string) *JobAsyncStopResponseBody {
	s.Message = &v
	return s
}

type JobAsyncStopResponse struct {
	Headers    map[string]*string        `json:"headers,omitempty" xml:"headers,omitempty"`
	StatusCode *int32                    `json:"statusCode,omitempty" xml:"statusCode,omitempty"`
	Body       *JobAsyncStopResponseBody `json:"body,omitempty" xml:"body,omitempty"`
}

func (s JobAsyncStopResponse) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncStopResponse) GoString() string {
	return s.String()
}

func (s *JobAsyncStopResponse) SetHeaders(v map[string]*string) *JobAsyncStopResponse {
	s.Headers = v
	return s
}

func (s *JobAsyncStopResponse) SetStatusCode(v int32) *JobAsyncStopResponse {
	s.StatusCode = &v
	return s
}

func (s *JobAsyncStopResponse) SetBody(v *JobAsyncStopResponseBody) *JobAsyncStopResponse {
	s.Body = v
	return s
}

type JobAsyncUpdateRequest struct {
	EngineParams *string `json:"engineParams,omitempty" xml:"engineParams,omitempty"`
	JobId        *string `json:"jobId,omitempty" xml:"jobId,omitempty"`
	Product      *string `json:"product,omitempty" xml:"product,omitempty"`
	UserId       *string `json:"userId,omitempty" xml:"userId,omitempty"`
}

func (s JobAsyncUpdateRequest) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncUpdateRequest) GoString() string {
	return s.String()
}

func (s *JobAsyncUpdateRequest) SetEngineParams(v string) *JobAsyncUpdateRequest {
	s.EngineParams = &v
	return s
}

func (s *JobAsyncUpdateRequest) SetJobId(v string) *JobAsyncUpdateRequest {
	s.JobId = &v
	return s
}

func (s *JobAsyncUpdateRequest) SetProduct(v string) *JobAsyncUpdateRequest {
	s.Product = &v
	return s
}

func (s *JobAsyncUpdateRequest) SetUserId(v string) *JobAsyncUpdateRequest {
	s.UserId = &v
	return s
}

type JobAsyncUpdateResponseBody struct {
	RequestId *string `json:"RequestId,omitempty" xml:"RequestId,omitempty"`
	Code      *string `json:"code,omitempty" xml:"code,omitempty"`
	Message   *string `json:"message,omitempty" xml:"message,omitempty"`
}

func (s JobAsyncUpdateResponseBody) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncUpdateResponseBody) GoString() string {
	return s.String()
}

func (s *JobAsyncUpdateResponseBody) SetRequestId(v string) *JobAsyncUpdateResponseBody {
	s.RequestId = &v
	return s
}

func (s *JobAsyncUpdateResponseBody) SetCode(v string) *JobAsyncUpdateResponseBody {
	s.Code = &v
	return s
}

func (s *JobAsyncUpdateResponseBody) SetMessage(v string) *JobAsyncUpdateResponseBody {
	s.Message = &v
	return s
}

type JobAsyncUpdateResponse struct {
	Headers    map[string]*string          `json:"headers,omitempty" xml:"headers,omitempty"`
	StatusCode *int32                      `json:"statusCode,omitempty" xml:"statusCode,omitempty"`
	Body       *JobAsyncUpdateResponseBody `json:"body,omitempty" xml:"body,omitempty"`
}

func (s JobAsyncUpdateResponse) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncUpdateResponse) GoString() string {
	return s.String()
}

func (s *JobAsyncUpdateResponse) SetHeaders(v map[string]*string) *JobAsyncUpdateResponse {
	s.Headers = v
	return s
}

func (s *JobAsyncUpdateResponse) SetStatusCode(v int32) *JobAsyncUpdateResponse {
	s.StatusCode = &v
	return s
}

func (s *JobAsyncUpdateResponse) SetBody(v *JobAsyncUpdateResponseBody) *JobAsyncUpdateResponse {
	s.Body = v
	return s
}

type JobAsyncUpdateTaskStatusRequest struct {
	BizStatus      *int32  `json:"bizStatus,omitempty" xml:"bizStatus,omitempty"`
	InternalStatus *int32  `json:"internalStatus,omitempty" xml:"internalStatus,omitempty"`
	JobId          *string `json:"jobId,omitempty" xml:"jobId,omitempty"`
	Product        *string `json:"product,omitempty" xml:"product,omitempty"`
	TaskId         *string `json:"taskId,omitempty" xml:"taskId,omitempty"`
	UserId         *string `json:"userId,omitempty" xml:"userId,omitempty"`
}

func (s JobAsyncUpdateTaskStatusRequest) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncUpdateTaskStatusRequest) GoString() string {
	return s.String()
}

func (s *JobAsyncUpdateTaskStatusRequest) SetBizStatus(v int32) *JobAsyncUpdateTaskStatusRequest {
	s.BizStatus = &v
	return s
}

func (s *JobAsyncUpdateTaskStatusRequest) SetInternalStatus(v int32) *JobAsyncUpdateTaskStatusRequest {
	s.InternalStatus = &v
	return s
}

func (s *JobAsyncUpdateTaskStatusRequest) SetJobId(v string) *JobAsyncUpdateTaskStatusRequest {
	s.JobId = &v
	return s
}

func (s *JobAsyncUpdateTaskStatusRequest) SetProduct(v string) *JobAsyncUpdateTaskStatusRequest {
	s.Product = &v
	return s
}

func (s *JobAsyncUpdateTaskStatusRequest) SetTaskId(v string) *JobAsyncUpdateTaskStatusRequest {
	s.TaskId = &v
	return s
}

func (s *JobAsyncUpdateTaskStatusRequest) SetUserId(v string) *JobAsyncUpdateTaskStatusRequest {
	s.UserId = &v
	return s
}

type JobAsyncUpdateTaskStatusResponseBody struct {
	RequestId *string                                     `json:"RequestId,omitempty" xml:"RequestId,omitempty"`
	Code      *int32                                      `json:"code,omitempty" xml:"code,omitempty"`
	Message   *string                                     `json:"message,omitempty" xml:"message,omitempty"`
	Metadata  *string                                     `json:"metadata,omitempty" xml:"metadata,omitempty"`
	Reason    *string                                     `json:"reason,omitempty" xml:"reason,omitempty"`
	Result    *JobAsyncUpdateTaskStatusResponseBodyResult `json:"result,omitempty" xml:"result,omitempty" type:"Struct"`
}

func (s JobAsyncUpdateTaskStatusResponseBody) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncUpdateTaskStatusResponseBody) GoString() string {
	return s.String()
}

func (s *JobAsyncUpdateTaskStatusResponseBody) SetRequestId(v string) *JobAsyncUpdateTaskStatusResponseBody {
	s.RequestId = &v
	return s
}

func (s *JobAsyncUpdateTaskStatusResponseBody) SetCode(v int32) *JobAsyncUpdateTaskStatusResponseBody {
	s.Code = &v
	return s
}

func (s *JobAsyncUpdateTaskStatusResponseBody) SetMessage(v string) *JobAsyncUpdateTaskStatusResponseBody {
	s.Message = &v
	return s
}

func (s *JobAsyncUpdateTaskStatusResponseBody) SetMetadata(v string) *JobAsyncUpdateTaskStatusResponseBody {
	s.Metadata = &v
	return s
}

func (s *JobAsyncUpdateTaskStatusResponseBody) SetReason(v string) *JobAsyncUpdateTaskStatusResponseBody {
	s.Reason = &v
	return s
}

func (s *JobAsyncUpdateTaskStatusResponseBody) SetResult(v *JobAsyncUpdateTaskStatusResponseBodyResult) *JobAsyncUpdateTaskStatusResponseBody {
	s.Result = v
	return s
}

type JobAsyncUpdateTaskStatusResponseBodyResult struct {
	Code     *int32  `json:"code,omitempty" xml:"code,omitempty"`
	Message  *string `json:"message,omitempty" xml:"message,omitempty"`
	Metadata *string `json:"metadata,omitempty" xml:"metadata,omitempty"`
	Reason   *string `json:"reason,omitempty" xml:"reason,omitempty"`
}

func (s JobAsyncUpdateTaskStatusResponseBodyResult) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncUpdateTaskStatusResponseBodyResult) GoString() string {
	return s.String()
}

func (s *JobAsyncUpdateTaskStatusResponseBodyResult) SetCode(v int32) *JobAsyncUpdateTaskStatusResponseBodyResult {
	s.Code = &v
	return s
}

func (s *JobAsyncUpdateTaskStatusResponseBodyResult) SetMessage(v string) *JobAsyncUpdateTaskStatusResponseBodyResult {
	s.Message = &v
	return s
}

func (s *JobAsyncUpdateTaskStatusResponseBodyResult) SetMetadata(v string) *JobAsyncUpdateTaskStatusResponseBodyResult {
	s.Metadata = &v
	return s
}

func (s *JobAsyncUpdateTaskStatusResponseBodyResult) SetReason(v string) *JobAsyncUpdateTaskStatusResponseBodyResult {
	s.Reason = &v
	return s
}

type JobAsyncUpdateTaskStatusResponse struct {
	Headers    map[string]*string                    `json:"headers,omitempty" xml:"headers,omitempty"`
	StatusCode *int32                                `json:"statusCode,omitempty" xml:"statusCode,omitempty"`
	Body       *JobAsyncUpdateTaskStatusResponseBody `json:"body,omitempty" xml:"body,omitempty"`
}

func (s JobAsyncUpdateTaskStatusResponse) String() string {
	return tea.Prettify(s)
}

func (s JobAsyncUpdateTaskStatusResponse) GoString() string {
	return s.String()
}

func (s *JobAsyncUpdateTaskStatusResponse) SetHeaders(v map[string]*string) *JobAsyncUpdateTaskStatusResponse {
	s.Headers = v
	return s
}

func (s *JobAsyncUpdateTaskStatusResponse) SetStatusCode(v int32) *JobAsyncUpdateTaskStatusResponse {
	s.StatusCode = &v
	return s
}

func (s *JobAsyncUpdateTaskStatusResponse) SetBody(v *JobAsyncUpdateTaskStatusResponseBody) *JobAsyncUpdateTaskStatusResponse {
	s.Body = v
	return s
}

type ManagerTaskQueryRequest struct {
	JobId   *string `json:"jobId,omitempty" xml:"jobId,omitempty"`
	Product *string `json:"product,omitempty" xml:"product,omitempty"`
	TaskId  *string `json:"taskId,omitempty" xml:"taskId,omitempty"`
}

func (s ManagerTaskQueryRequest) String() string {
	return tea.Prettify(s)
}

func (s ManagerTaskQueryRequest) GoString() string {
	return s.String()
}

func (s *ManagerTaskQueryRequest) SetJobId(v string) *ManagerTaskQueryRequest {
	s.JobId = &v
	return s
}

func (s *ManagerTaskQueryRequest) SetProduct(v string) *ManagerTaskQueryRequest {
	s.Product = &v
	return s
}

func (s *ManagerTaskQueryRequest) SetTaskId(v string) *ManagerTaskQueryRequest {
	s.TaskId = &v
	return s
}

type ManagerTaskQueryResponseBody struct {
	RequestId *string                             `json:"RequestId,omitempty" xml:"RequestId,omitempty"`
	Data      *ManagerTaskQueryResponseBodyData   `json:"data,omitempty" xml:"data,omitempty" type:"Struct"`
	Result    *ManagerTaskQueryResponseBodyResult `json:"result,omitempty" xml:"result,omitempty" type:"Struct"`
}

func (s ManagerTaskQueryResponseBody) String() string {
	return tea.Prettify(s)
}

func (s ManagerTaskQueryResponseBody) GoString() string {
	return s.String()
}

func (s *ManagerTaskQueryResponseBody) SetRequestId(v string) *ManagerTaskQueryResponseBody {
	s.RequestId = &v
	return s
}

func (s *ManagerTaskQueryResponseBody) SetData(v *ManagerTaskQueryResponseBodyData) *ManagerTaskQueryResponseBody {
	s.Data = v
	return s
}

func (s *ManagerTaskQueryResponseBody) SetResult(v *ManagerTaskQueryResponseBodyResult) *ManagerTaskQueryResponseBody {
	s.Result = v
	return s
}

type ManagerTaskQueryResponseBodyData struct {
	Task *ManagerTaskQueryResponseBodyDataTask `json:"task,omitempty" xml:"task,omitempty" type:"Struct"`
}

func (s ManagerTaskQueryResponseBodyData) String() string {
	return tea.Prettify(s)
}

func (s ManagerTaskQueryResponseBodyData) GoString() string {
	return s.String()
}

func (s *ManagerTaskQueryResponseBodyData) SetTask(v *ManagerTaskQueryResponseBodyDataTask) *ManagerTaskQueryResponseBodyData {
	s.Task = v
	return s
}

type ManagerTaskQueryResponseBodyDataTask struct {
	BizStatus       *int32                                               `json:"bizStatus,omitempty" xml:"bizStatus,omitempty"`
	EngineModel     *string                                              `json:"engineModel,omitempty" xml:"engineModel,omitempty"`
	EngineParams    *ManagerTaskQueryResponseBodyDataTaskEngineParams    `json:"engineParams,omitempty" xml:"engineParams,omitempty" type:"Struct"`
	GmtCreate       *string                                              `json:"gmtCreate,omitempty" xml:"gmtCreate,omitempty"`
	GmtModified     *string                                              `json:"gmtModified,omitempty" xml:"gmtModified,omitempty"`
	InternalStatus  *int32                                               `json:"internalStatus,omitempty" xml:"internalStatus,omitempty"`
	JobId           *string                                              `json:"jobId,omitempty" xml:"jobId,omitempty"`
	MigrateConfig   *string                                              `json:"migrateConfig,omitempty" xml:"migrateConfig,omitempty"`
	MigrateTimes    *int32                                               `json:"migrateTimes,omitempty" xml:"migrateTimes,omitempty"`
	Notify          *string                                              `json:"notify,omitempty" xml:"notify,omitempty"`
	Product         *string                                              `json:"product,omitempty" xml:"product,omitempty"`
	RequestResource *ManagerTaskQueryResponseBodyDataTaskRequestResource `json:"requestResource,omitempty" xml:"requestResource,omitempty" type:"Struct"`
	Result          *ManagerTaskQueryResponseBodyDataTaskResult          `json:"result,omitempty" xml:"result,omitempty" type:"Struct"`
	Tag             *string                                              `json:"tag,omitempty" xml:"tag,omitempty"`
	TaskId          *string                                              `json:"taskId,omitempty" xml:"taskId,omitempty"`
	TaskType        *int32                                               `json:"taskType,omitempty" xml:"taskType,omitempty"`
	Trace           *string                                              `json:"trace,omitempty" xml:"trace,omitempty"`
	UserData        *string                                              `json:"userData,omitempty" xml:"userData,omitempty"`
	Worker          *ManagerTaskQueryResponseBodyDataTaskWorker          `json:"worker,omitempty" xml:"worker,omitempty" type:"Struct"`
}

func (s ManagerTaskQueryResponseBodyDataTask) String() string {
	return tea.Prettify(s)
}

func (s ManagerTaskQueryResponseBodyDataTask) GoString() string {
	return s.String()
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetBizStatus(v int32) *ManagerTaskQueryResponseBodyDataTask {
	s.BizStatus = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetEngineModel(v string) *ManagerTaskQueryResponseBodyDataTask {
	s.EngineModel = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetEngineParams(v *ManagerTaskQueryResponseBodyDataTaskEngineParams) *ManagerTaskQueryResponseBodyDataTask {
	s.EngineParams = v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetGmtCreate(v string) *ManagerTaskQueryResponseBodyDataTask {
	s.GmtCreate = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetGmtModified(v string) *ManagerTaskQueryResponseBodyDataTask {
	s.GmtModified = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetInternalStatus(v int32) *ManagerTaskQueryResponseBodyDataTask {
	s.InternalStatus = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetJobId(v string) *ManagerTaskQueryResponseBodyDataTask {
	s.JobId = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetMigrateConfig(v string) *ManagerTaskQueryResponseBodyDataTask {
	s.MigrateConfig = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetMigrateTimes(v int32) *ManagerTaskQueryResponseBodyDataTask {
	s.MigrateTimes = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetNotify(v string) *ManagerTaskQueryResponseBodyDataTask {
	s.Notify = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetProduct(v string) *ManagerTaskQueryResponseBodyDataTask {
	s.Product = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetRequestResource(v *ManagerTaskQueryResponseBodyDataTaskRequestResource) *ManagerTaskQueryResponseBodyDataTask {
	s.RequestResource = v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetResult(v *ManagerTaskQueryResponseBodyDataTaskResult) *ManagerTaskQueryResponseBodyDataTask {
	s.Result = v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetTag(v string) *ManagerTaskQueryResponseBodyDataTask {
	s.Tag = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetTaskId(v string) *ManagerTaskQueryResponseBodyDataTask {
	s.TaskId = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetTaskType(v int32) *ManagerTaskQueryResponseBodyDataTask {
	s.TaskType = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetTrace(v string) *ManagerTaskQueryResponseBodyDataTask {
	s.Trace = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetUserData(v string) *ManagerTaskQueryResponseBodyDataTask {
	s.UserData = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTask) SetWorker(v *ManagerTaskQueryResponseBodyDataTaskWorker) *ManagerTaskQueryResponseBodyDataTask {
	s.Worker = v
	return s
}

type ManagerTaskQueryResponseBodyDataTaskEngineParams struct {
	Param    *string `json:"param,omitempty" xml:"param,omitempty"`
	ParamUrl *string `json:"paramUrl,omitempty" xml:"paramUrl,omitempty"`
}

func (s ManagerTaskQueryResponseBodyDataTaskEngineParams) String() string {
	return tea.Prettify(s)
}

func (s ManagerTaskQueryResponseBodyDataTaskEngineParams) GoString() string {
	return s.String()
}

func (s *ManagerTaskQueryResponseBodyDataTaskEngineParams) SetParam(v string) *ManagerTaskQueryResponseBodyDataTaskEngineParams {
	s.Param = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTaskEngineParams) SetParamUrl(v string) *ManagerTaskQueryResponseBodyDataTaskEngineParams {
	s.ParamUrl = &v
	return s
}

type ManagerTaskQueryResponseBodyDataTaskRequestResource struct {
	Quota map[string]*int64 `json:"quota,omitempty" xml:"quota,omitempty"`
}

func (s ManagerTaskQueryResponseBodyDataTaskRequestResource) String() string {
	return tea.Prettify(s)
}

func (s ManagerTaskQueryResponseBodyDataTaskRequestResource) GoString() string {
	return s.String()
}

func (s *ManagerTaskQueryResponseBodyDataTaskRequestResource) SetQuota(v map[string]*int64) *ManagerTaskQueryResponseBodyDataTaskRequestResource {
	s.Quota = v
	return s
}

type ManagerTaskQueryResponseBodyDataTaskResult struct {
	Code     *int32  `json:"code,omitempty" xml:"code,omitempty"`
	Data     *string `json:"data,omitempty" xml:"data,omitempty"`
	DataUrl  *string `json:"dataUrl,omitempty" xml:"dataUrl,omitempty"`
	Duration *string `json:"duration,omitempty" xml:"duration,omitempty"`
	Message  *string `json:"message,omitempty" xml:"message,omitempty"`
	Reason   *string `json:"reason,omitempty" xml:"reason,omitempty"`
}

func (s ManagerTaskQueryResponseBodyDataTaskResult) String() string {
	return tea.Prettify(s)
}

func (s ManagerTaskQueryResponseBodyDataTaskResult) GoString() string {
	return s.String()
}

func (s *ManagerTaskQueryResponseBodyDataTaskResult) SetCode(v int32) *ManagerTaskQueryResponseBodyDataTaskResult {
	s.Code = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTaskResult) SetData(v string) *ManagerTaskQueryResponseBodyDataTaskResult {
	s.Data = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTaskResult) SetDataUrl(v string) *ManagerTaskQueryResponseBodyDataTaskResult {
	s.DataUrl = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTaskResult) SetDuration(v string) *ManagerTaskQueryResponseBodyDataTaskResult {
	s.Duration = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTaskResult) SetMessage(v string) *ManagerTaskQueryResponseBodyDataTaskResult {
	s.Message = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTaskResult) SetReason(v string) *ManagerTaskQueryResponseBodyDataTaskResult {
	s.Reason = &v
	return s
}

type ManagerTaskQueryResponseBodyDataTaskWorker struct {
	Ip       *string `json:"ip,omitempty" xml:"ip,omitempty"`
	Port     *int32  `json:"port,omitempty" xml:"port,omitempty"`
	WorkerId *string `json:"workerId,omitempty" xml:"workerId,omitempty"`
}

func (s ManagerTaskQueryResponseBodyDataTaskWorker) String() string {
	return tea.Prettify(s)
}

func (s ManagerTaskQueryResponseBodyDataTaskWorker) GoString() string {
	return s.String()
}

func (s *ManagerTaskQueryResponseBodyDataTaskWorker) SetIp(v string) *ManagerTaskQueryResponseBodyDataTaskWorker {
	s.Ip = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTaskWorker) SetPort(v int32) *ManagerTaskQueryResponseBodyDataTaskWorker {
	s.Port = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyDataTaskWorker) SetWorkerId(v string) *ManagerTaskQueryResponseBodyDataTaskWorker {
	s.WorkerId = &v
	return s
}

type ManagerTaskQueryResponseBodyResult struct {
	Code    *int32  `json:"code,omitempty" xml:"code,omitempty"`
	Message *string `json:"message,omitempty" xml:"message,omitempty"`
	Reason  *string `json:"reason,omitempty" xml:"reason,omitempty"`
}

func (s ManagerTaskQueryResponseBodyResult) String() string {
	return tea.Prettify(s)
}

func (s ManagerTaskQueryResponseBodyResult) GoString() string {
	return s.String()
}

func (s *ManagerTaskQueryResponseBodyResult) SetCode(v int32) *ManagerTaskQueryResponseBodyResult {
	s.Code = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyResult) SetMessage(v string) *ManagerTaskQueryResponseBodyResult {
	s.Message = &v
	return s
}

func (s *ManagerTaskQueryResponseBodyResult) SetReason(v string) *ManagerTaskQueryResponseBodyResult {
	s.Reason = &v
	return s
}

type ManagerTaskQueryResponse struct {
	Headers    map[string]*string            `json:"headers,omitempty" xml:"headers,omitempty"`
	StatusCode *int32                        `json:"statusCode,omitempty" xml:"statusCode,omitempty"`
	Body       *ManagerTaskQueryResponseBody `json:"body,omitempty" xml:"body,omitempty"`
}

func (s ManagerTaskQueryResponse) String() string {
	return tea.Prettify(s)
}

func (s ManagerTaskQueryResponse) GoString() string {
	return s.String()
}

func (s *ManagerTaskQueryResponse) SetHeaders(v map[string]*string) *ManagerTaskQueryResponse {
	s.Headers = v
	return s
}

func (s *ManagerTaskQueryResponse) SetStatusCode(v int32) *ManagerTaskQueryResponse {
	s.StatusCode = &v
	return s
}

func (s *ManagerTaskQueryResponse) SetBody(v *ManagerTaskQueryResponseBody) *ManagerTaskQueryResponse {
	s.Body = v
	return s
}

type Client struct {
	openapi.Client
}

func NewClient(config *openapi.Config) (*Client, error) {
	client := new(Client)
	err := client.Init(config)
	return client, err
}

func (client *Client) Init(config *openapi.Config) (_err error) {
	_err = client.Client.Init(config)
	if _err != nil {
		return _err
	}
	client.EndpointRule = tea.String("")
	_err = client.CheckConfig(config)
	if _err != nil {
		return _err
	}
	client.Endpoint, _err = client.GetEndpoint(tea.String("mppinner"), client.RegionId, client.EndpointRule, client.Network, client.Suffix, client.EndpointMap, client.Endpoint)
	if _err != nil {
		return _err
	}

	return nil
}

func (client *Client) GetEndpoint(productId *string, regionId *string, endpointRule *string, network *string, suffix *string, endpointMap map[string]*string, endpoint *string) (_result *string, _err error) {
	if !tea.BoolValue(util.Empty(endpoint)) {
		_result = endpoint
		return _result, _err
	}

	if !tea.BoolValue(util.IsUnset(endpointMap)) && !tea.BoolValue(util.Empty(endpointMap[tea.StringValue(regionId)])) {
		_result = endpointMap[tea.StringValue(regionId)]
		return _result, _err
	}

	_body, _err := endpointutil.GetEndpointRules(productId, regionId, endpointRule, network, suffix)
	if _err != nil {
		return _result, _err
	}
	_result = _body
	return _result, _err
}

func (client *Client) JobAsyncMigrateWithOptions(tmpReq *JobAsyncMigrateRequest, runtime *util.RuntimeOptions) (_result *JobAsyncMigrateResponse, _err error) {
	_err = util.ValidateModel(tmpReq)
	if _err != nil {
		return _result, _err
	}
	request := &JobAsyncMigrateShrinkRequest{}
	openapiutil.Convert(tmpReq, request)
	if !tea.BoolValue(util.IsUnset(tmpReq.TaskIds)) {
		request.TaskIdsShrink = openapiutil.ArrayToStringWithSpecifiedStyle(tmpReq.TaskIds, tea.String("taskIds"), tea.String("simple"))
	}

	if !tea.BoolValue(util.IsUnset(tmpReq.WorkIds)) {
		request.WorkIdsShrink = openapiutil.ArrayToStringWithSpecifiedStyle(tmpReq.WorkIds, tea.String("workIds"), tea.String("simple"))
	}

	body := map[string]interface{}{}
	if !tea.BoolValue(util.IsUnset(request.Product)) {
		body["product"] = request.Product
	}

	if !tea.BoolValue(util.IsUnset(request.TaskIdsShrink)) {
		body["taskIds"] = request.TaskIdsShrink
	}

	if !tea.BoolValue(util.IsUnset(request.UserId)) {
		body["userId"] = request.UserId
	}

	if !tea.BoolValue(util.IsUnset(request.WorkIdsShrink)) {
		body["workIds"] = request.WorkIdsShrink
	}

	req := &openapi.OpenApiRequest{
		Body: openapiutil.ParseToMap(body),
	}
	params := &openapi.Params{
		Action:      tea.String("JobAsyncMigrate"),
		Version:     tea.String("2024-02-01"),
		Protocol:    tea.String("HTTPS"),
		Pathname:    tea.String("/"),
		Method:      tea.String("POST"),
		AuthType:    tea.String("AK"),
		Style:       tea.String("RPC"),
		ReqBodyType: tea.String("formData"),
		BodyType:    tea.String("json"),
	}
	_result = &JobAsyncMigrateResponse{}
	_body, _err := client.CallApi(params, req, runtime)
	if _err != nil {
		return _result, _err
	}
	_err = tea.Convert(_body, &_result)
	return _result, _err
}

func (client *Client) JobAsyncMigrate(request *JobAsyncMigrateRequest) (_result *JobAsyncMigrateResponse, _err error) {
	runtime := &util.RuntimeOptions{}
	_result = &JobAsyncMigrateResponse{}
	_body, _err := client.JobAsyncMigrateWithOptions(request, runtime)
	if _err != nil {
		return _result, _err
	}
	_result = _body
	return _result, _err
}

func (client *Client) JobAsyncStartWithOptions(request *JobAsyncStartRequest, runtime *util.RuntimeOptions) (_result *JobAsyncStartResponse, _err error) {
	_err = util.ValidateModel(request)
	if _err != nil {
		return _result, _err
	}
	query := map[string]interface{}{}
	if !tea.BoolValue(util.IsUnset(request.EngineModel)) {
		query["engineModel"] = request.EngineModel
	}

	if !tea.BoolValue(util.IsUnset(request.JobId)) {
		query["jobId"] = request.JobId
	}

	if !tea.BoolValue(util.IsUnset(request.JobType)) {
		query["jobType"] = request.JobType
	}

	if !tea.BoolValue(util.IsUnset(request.Notify)) {
		query["notify"] = request.Notify
	}

	if !tea.BoolValue(util.IsUnset(request.Product)) {
		query["product"] = request.Product
	}

	if !tea.BoolValue(util.IsUnset(request.ScheduleParams)) {
		query["scheduleParams"] = request.ScheduleParams
	}

	if !tea.BoolValue(util.IsUnset(request.Tag)) {
		query["tag"] = request.Tag
	}

	if !tea.BoolValue(util.IsUnset(request.TaskId)) {
		query["taskId"] = request.TaskId
	}

	if !tea.BoolValue(util.IsUnset(request.Token)) {
		query["token"] = request.Token
	}

	if !tea.BoolValue(util.IsUnset(request.Trace)) {
		query["trace"] = request.Trace
	}

	if !tea.BoolValue(util.IsUnset(request.UserData)) {
		query["userData"] = request.UserData
	}

	if !tea.BoolValue(util.IsUnset(request.UserId)) {
		query["userId"] = request.UserId
	}

	body := map[string]interface{}{}
	if !tea.BoolValue(util.IsUnset(request.EngineParams)) {
		body["engineParams"] = request.EngineParams
	}

	req := &openapi.OpenApiRequest{
		Query: openapiutil.Query(query),
		Body:  openapiutil.ParseToMap(body),
	}
	params := &openapi.Params{
		Action:      tea.String("JobAsyncStart"),
		Version:     tea.String("2024-02-01"),
		Protocol:    tea.String("HTTPS"),
		Pathname:    tea.String("/"),
		Method:      tea.String("POST"),
		AuthType:    tea.String("AK"),
		Style:       tea.String("RPC"),
		ReqBodyType: tea.String("formData"),
		BodyType:    tea.String("json"),
	}
	_result = &JobAsyncStartResponse{}
	_body, _err := client.CallApi(params, req, runtime)
	if _err != nil {
		return _result, _err
	}
	_err = tea.Convert(_body, &_result)
	return _result, _err
}

func (client *Client) JobAsyncStart(request *JobAsyncStartRequest) (_result *JobAsyncStartResponse, _err error) {
	runtime := &util.RuntimeOptions{}
	_result = &JobAsyncStartResponse{}
	_body, _err := client.JobAsyncStartWithOptions(request, runtime)
	if _err != nil {
		return _result, _err
	}
	_result = _body
	return _result, _err
}

func (client *Client) JobAsyncStopWithOptions(request *JobAsyncStopRequest, runtime *util.RuntimeOptions) (_result *JobAsyncStopResponse, _err error) {
	_err = util.ValidateModel(request)
	if _err != nil {
		return _result, _err
	}
	query := map[string]interface{}{}
	if !tea.BoolValue(util.IsUnset(request.JobId)) {
		query["jobId"] = request.JobId
	}

	if !tea.BoolValue(util.IsUnset(request.Product)) {
		query["product"] = request.Product
	}

	if !tea.BoolValue(util.IsUnset(request.UserId)) {
		query["userId"] = request.UserId
	}

	req := &openapi.OpenApiRequest{
		Query: openapiutil.Query(query),
	}
	params := &openapi.Params{
		Action:      tea.String("JobAsyncStop"),
		Version:     tea.String("2024-02-01"),
		Protocol:    tea.String("HTTPS"),
		Pathname:    tea.String("/"),
		Method:      tea.String("POST"),
		AuthType:    tea.String("AK"),
		Style:       tea.String("RPC"),
		ReqBodyType: tea.String("formData"),
		BodyType:    tea.String("json"),
	}
	_result = &JobAsyncStopResponse{}
	_body, _err := client.CallApi(params, req, runtime)
	if _err != nil {
		return _result, _err
	}
	_err = tea.Convert(_body, &_result)
	return _result, _err
}

func (client *Client) JobAsyncStop(request *JobAsyncStopRequest) (_result *JobAsyncStopResponse, _err error) {
	runtime := &util.RuntimeOptions{}
	_result = &JobAsyncStopResponse{}
	_body, _err := client.JobAsyncStopWithOptions(request, runtime)
	if _err != nil {
		return _result, _err
	}
	_result = _body
	return _result, _err
}

func (client *Client) JobAsyncUpdateWithOptions(request *JobAsyncUpdateRequest, runtime *util.RuntimeOptions) (_result *JobAsyncUpdateResponse, _err error) {
	_err = util.ValidateModel(request)
	if _err != nil {
		return _result, _err
	}
	query := map[string]interface{}{}
	if !tea.BoolValue(util.IsUnset(request.JobId)) {
		query["jobId"] = request.JobId
	}

	if !tea.BoolValue(util.IsUnset(request.Product)) {
		query["product"] = request.Product
	}

	if !tea.BoolValue(util.IsUnset(request.UserId)) {
		query["userId"] = request.UserId
	}

	body := map[string]interface{}{}
	if !tea.BoolValue(util.IsUnset(request.EngineParams)) {
		body["engineParams"] = request.EngineParams
	}

	req := &openapi.OpenApiRequest{
		Query: openapiutil.Query(query),
		Body:  openapiutil.ParseToMap(body),
	}
	params := &openapi.Params{
		Action:      tea.String("JobAsyncUpdate"),
		Version:     tea.String("2024-02-01"),
		Protocol:    tea.String("HTTPS"),
		Pathname:    tea.String("/"),
		Method:      tea.String("POST"),
		AuthType:    tea.String("AK"),
		Style:       tea.String("RPC"),
		ReqBodyType: tea.String("formData"),
		BodyType:    tea.String("json"),
	}
	_result = &JobAsyncUpdateResponse{}
	_body, _err := client.CallApi(params, req, runtime)
	if _err != nil {
		return _result, _err
	}
	_err = tea.Convert(_body, &_result)
	return _result, _err
}

func (client *Client) JobAsyncUpdate(request *JobAsyncUpdateRequest) (_result *JobAsyncUpdateResponse, _err error) {
	runtime := &util.RuntimeOptions{}
	_result = &JobAsyncUpdateResponse{}
	_body, _err := client.JobAsyncUpdateWithOptions(request, runtime)
	if _err != nil {
		return _result, _err
	}
	_result = _body
	return _result, _err
}

func (client *Client) JobAsyncUpdateTaskStatusWithOptions(request *JobAsyncUpdateTaskStatusRequest, runtime *util.RuntimeOptions) (_result *JobAsyncUpdateTaskStatusResponse, _err error) {
	_err = util.ValidateModel(request)
	if _err != nil {
		return _result, _err
	}
	body := map[string]interface{}{}
	if !tea.BoolValue(util.IsUnset(request.BizStatus)) {
		body["bizStatus"] = request.BizStatus
	}

	if !tea.BoolValue(util.IsUnset(request.InternalStatus)) {
		body["internalStatus"] = request.InternalStatus
	}

	if !tea.BoolValue(util.IsUnset(request.JobId)) {
		body["jobId"] = request.JobId
	}

	if !tea.BoolValue(util.IsUnset(request.Product)) {
		body["product"] = request.Product
	}

	if !tea.BoolValue(util.IsUnset(request.TaskId)) {
		body["taskId"] = request.TaskId
	}

	if !tea.BoolValue(util.IsUnset(request.UserId)) {
		body["userId"] = request.UserId
	}

	req := &openapi.OpenApiRequest{
		Body: openapiutil.ParseToMap(body),
	}
	params := &openapi.Params{
		Action:      tea.String("JobAsyncUpdateTaskStatus"),
		Version:     tea.String("2024-02-01"),
		Protocol:    tea.String("HTTPS"),
		Pathname:    tea.String("/"),
		Method:      tea.String("POST"),
		AuthType:    tea.String("AK"),
		Style:       tea.String("RPC"),
		ReqBodyType: tea.String("formData"),
		BodyType:    tea.String("json"),
	}
	_result = &JobAsyncUpdateTaskStatusResponse{}
	_body, _err := client.CallApi(params, req, runtime)
	if _err != nil {
		return _result, _err
	}
	_err = tea.Convert(_body, &_result)
	return _result, _err
}

func (client *Client) JobAsyncUpdateTaskStatus(request *JobAsyncUpdateTaskStatusRequest) (_result *JobAsyncUpdateTaskStatusResponse, _err error) {
	runtime := &util.RuntimeOptions{}
	_result = &JobAsyncUpdateTaskStatusResponse{}
	_body, _err := client.JobAsyncUpdateTaskStatusWithOptions(request, runtime)
	if _err != nil {
		return _result, _err
	}
	_result = _body
	return _result, _err
}

func (client *Client) ManagerTaskQueryWithOptions(request *ManagerTaskQueryRequest, runtime *util.RuntimeOptions) (_result *ManagerTaskQueryResponse, _err error) {
	_err = util.ValidateModel(request)
	if _err != nil {
		return _result, _err
	}
	query := map[string]interface{}{}
	if !tea.BoolValue(util.IsUnset(request.Product)) {
		query["product"] = request.Product
	}

	body := map[string]interface{}{}
	if !tea.BoolValue(util.IsUnset(request.JobId)) {
		body["jobId"] = request.JobId
	}

	if !tea.BoolValue(util.IsUnset(request.TaskId)) {
		body["taskId"] = request.TaskId
	}

	req := &openapi.OpenApiRequest{
		Query: openapiutil.Query(query),
		Body:  openapiutil.ParseToMap(body),
	}
	params := &openapi.Params{
		Action:      tea.String("ManagerTaskQuery"),
		Version:     tea.String("2024-02-01"),
		Protocol:    tea.String("HTTPS"),
		Pathname:    tea.String("/"),
		Method:      tea.String("POST"),
		AuthType:    tea.String("AK"),
		Style:       tea.String("RPC"),
		ReqBodyType: tea.String("formData"),
		BodyType:    tea.String("json"),
	}
	_result = &ManagerTaskQueryResponse{}
	_body, _err := client.CallApi(params, req, runtime)
	if _err != nil {
		return _result, _err
	}
	_err = tea.Convert(_body, &_result)
	return _result, _err
}

func (client *Client) ManagerTaskQuery(request *ManagerTaskQueryRequest) (_result *ManagerTaskQueryResponse, _err error) {
	runtime := &util.RuntimeOptions{}
	_result = &ManagerTaskQueryResponse{}
	_body, _err := client.ManagerTaskQueryWithOptions(request, runtime)
	if _err != nil {
		return _result, _err
	}
	_result = _body
	return _result, _err
}
