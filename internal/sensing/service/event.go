package service

import (
	"github.com/go-kratos/kratos/v2/log"
	jsoniter "github.com/json-iterator/go"
	_ "io"
	"mpp/internal/sensing/biz"
	"net/http"
	"proto.mpp/api/sensing/v1"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

type EventService struct {
	v1.UnimplementedSensingServer
	eventBiz *biz.EventBiz
}

func NewEventService(eventBiz *biz.EventBiz) *EventService {
	return &EventService{
		eventBiz: eventBiz,
	}
}

// 原proto接口
//func (e *EventService) PushEvent(ctx context.Context, req *v1.EventRequest) (*v1.PushEventReply, error) {
//	log.Infof("EventService.PushEvent req:%v", req)
//	bizEvent := transToEvent(req)
//	e.eventBiz.Handle(ctx, bizEvent)
//	return &v1.PushEventReply{}, nil
//}

func (e *EventService) PushEvent(w http.ResponseWriter, r *http.Request) (any, error) {
	//query := r.URL.Query()
	//var bodyStr string
	//body, err := io.ReadAll(r.Body)
	log.Infof("EventService.PushEvent receive an event req:%v", r)
	decoder := json.NewDecoder(r.Body)
	var bizEvent biz.Event
	err := decoder.Decode(&bizEvent)
	if err != nil {
		log.Errorf("JSON unmarshaling failed: %s", err)
		return e.Result("Failed", err.Error(), nil), err
	}

	//bizEvent = transToEvent(js)
	err = e.eventBiz.Handle(r.Context(), &bizEvent)
	return e.Result("Success", "success", nil), err
}

func (s *EventService) ResultError(err error) any {
	return map[string]string{
		"code":    "InternalError",
		"message": err.Error(),
	}
}

func (s *EventService) Result(reason string, message string, rst map[string]interface{}) any {
	if rst == nil {
		rst = make(map[string]interface{})
	}
	rst["code"] = reason
	rst["message"] = message

	if rst["code"] == "" || rst["code"] == "ok" {
		rst["code"] = "Success"
	}

	return rst
}

//func transToEvent() *biz.Event {
//	log.Infof("trans request to bizEvent start")
//	//s, _ := c.Params.Get("uuid")
//	//body, _ := ioutil.ReadAll(c.Request.Body)
//	// 解析入参 转换成标准event
//	var bizEvent biz.Event
//	bizEvent.ENV = req.Env
//	if bizEvent.ENV == "" {
//		bizEvent.ENV = "Unknown"
//	}
//	bizEvent.CODE = int(req.Type)
//	bizEvent.REGION = req.Region
//	detail := req.Detail
//	detail["workerIp"] = req.WorkerIp
//
//	jsonStr, err := json.Marshal(req.Extend)
//	if err != nil {
//		log.Fatalf("JSON marshaling failed: %s", err)
//	}
//	detail["extend"] = string(jsonStr)
//	//str, _ := json.Marshal(detail)
//	bizEvent.DETAIL = detail
//	return &bizEvent
//}
