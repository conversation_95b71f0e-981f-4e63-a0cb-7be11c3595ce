package service

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/sensing/biz"
	"proto.mpp/api/sensing/v1"
)

type RobotService struct {
	v1.UnimplementedRobotServer
	robotBiz *biz.RobotBiz
}

func NewRobotService(robotBiz *biz.RobotBiz) *RobotService {
	return &RobotService{
		robotBiz: robotBiz,
	}
}

func (e *RobotService) RobotPush(ctx context.Context, req *v1.RobotRequest) (*v1.PushRobotReply, error) {
	log.Infof("Robot Service.RobotPush req:%v", req)
	//bizEvent := transToEvent(req)
	// 目前仅实现查询水位功能  ---- 拓展其他场景
	err := e.robotBiz.DealRobotRequest(ctx, req.SessionWebhook, req.Text.Content)
	if err != nil {
		log.Errorf("Robot Service.RobotPush deal robot request error:%v", err)
		return &v1.PushRobotReply{
			Success:   false,
			ErrorCode: 500,
			ErrorMsg:  err.Error(),
		}, err
	}
	data := make(map[string]string)
	data["data"] = req.MsgId
	return &v1.PushRobotReply{
		Success:   true,
		ErrorCode: 200,
		ErrorMsg:  "success",
		Fields:    data,
	}, nil
}
