syntax = "proto3";
package kratos.api;

option go_package = "sensingg/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
  Business business = 3;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
  }
  message Redis {
    string network = 1;
    string addr = 2;
    string password = 3;
    int32 db = 4;
    google.protobuf.Duration read_timeout = 5;
    google.protobuf.Duration write_timeout = 6;
  }
  message Sunfire {
    string accessKeyId = 1;
    string accessKeySecret = 2;
  }
  message Mns {
    string url = 1;
    string accessKeyId = 2;
    string accessKeySecret = 3;
    string queueName = 4;
  }
  Database database = 1;
  Redis redis = 2;
  Sunfire sunfire = 3;
  Mns mns = 4;
}

message Business {
  message ErrorCode {
    int64 errorCodeTimeRegion = 1;
  }
  ErrorCode errorCode = 1;
}