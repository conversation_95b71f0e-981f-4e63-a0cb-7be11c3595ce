package data

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/sensing/biz"
	"time"
)

//var rdb *redis.Client

type RedisApi struct {
	data *Data
	log  *log.Helper
}

func NewRedisRepo(data *Data, logger log.Logger) biz.RedisRepo {
	return &RedisApi{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (r RedisApi) Get(ctx context.Context, key string) (interface{}, error) {
	return r.data.rdb.Get(ctx, key).Result()
}

func (r RedisApi) Set(ctx context.Context, key string, value interface{}, expire int64) error {
	return r.data.rdb.Set(ctx, key, value, time.Duration(expire)*time.Second).Err()
}

func (r RedisApi) SMembers(ctx context.Context, key string) ([]string, error) {
	return r.data.rdb.SMembers(ctx, key).Result()
}

func (r RedisApi) SAdd(ctx context.Context, key string, value ...interface{}) error {
	return r.data.rdb.SAdd(ctx, key, value...).Err()
}

func (r RedisApi) SRemove(ctx context.Context, key string, members ...interface{}) error {
	return r.data.rdb.SRem(ctx, key, members...).Err()
}

func (r RedisApi) Del(ctx context.Context, key string) error {
	return r.data.rdb.Del(ctx, key).Err()
}

func (r RedisApi) Incr(ctx context.Context, key string) (int64, error) {
	return r.data.rdb.Incr(ctx, key).Result()
}

func (r RedisApi) IncrBy(ctx context.Context, key string, value int64) (int64, error) {
	return r.data.rdb.IncrBy(ctx, key, value).Result()
}

func (r RedisApi) Decr(ctx context.Context, key string) (int64, error) {
	return r.data.rdb.Decr(ctx, key).Result()
}

func (r RedisApi) DecrBy(ctx context.Context, key string, value int64) (int64, error) {
	return r.data.rdb.DecrBy(ctx, key, value).Result()
}

func (r RedisApi) Expire(ctx context.Context, key string, value int64) error {
	return r.data.rdb.Expire(ctx, key, time.Duration(value)*time.Second).Err()
}

//var ctx = context.Background()

//func newRedis(c *conf.Data) *redis.Client {
//	log.Infof("init redis. info:%v", c.Redis)
//	rdb := redis.NewClient(&redis.Options{
//		Addr:         c.Redis.Addr,
//		Password:     c.Redis.Password, // no password set
//		DB:           int(c.Redis.Db),  // use default DB
//		PoolSize:     15,               // 连接池最大socket连接数，默认为4倍CPU数， 4 * runtime.NumCPU
//		MinIdleConns: 10,               //在启动阶段创建指定数量的Idle连接，并长期维持idle状态的连接数不少于指定数量；。
//		//超时
//		DialTimeout:  5 * time.Second, //连接建立超时时间，默认5秒。
//		ReadTimeout:  3 * time.Second, //读超时，默认3秒， -1表示取消读超时
//		WriteTimeout: 3 * time.Second, //写超时，默认等于读超时
//		PoolTimeout:  4 * time.Second, //当所有连接都处在繁忙状态时，客户端等待可用连接的最大等待时长，默认为读超时+1秒。
//		//闲置连接检查包括IdleTimeout，MaxConnAge
//	})
//	return rdb
//}

//func init() {
//	rdb = newRedis(conf.Conf.Data)
//}

//func init() {
//	rdb = redis.NewClient(&redis.Options{
//		Addr:         "127.0.0.1:6379",
//		Password:     "", // no password set
//		DB:           1,  // use default DB
//		PoolSize:     15, // 连接池最大socket连接数，默认为4倍CPU数， 4 * runtime.NumCPU
//		MinIdleConns: 10, //在启动阶段创建指定数量的Idle连接，并长期维持idle状态的连接数不少于指定数量；。
//
//		//超时
//		DialTimeout:  5 * time.Second, //连接建立超时时间，默认5秒。
//		ReadTimeout:  3 * time.Second, //读超时，默认3秒， -1表示取消读超时
//		WriteTimeout: 3 * time.Second, //写超时，默认等于读超时
//		PoolTimeout:  4 * time.Second, //当所有连接都处在繁忙状态时，客户端等待可用连接的最大等待时长，默认为读超时+1秒。
//
//		//闲置连接检查包括IdleTimeout，MaxConnAge
//		IdleCheckFrequency: 60 * time.Second, //闲置连接检查的周期，默认为1分钟，-1表示不做周期性检查，只在客户端获取连接时对闲置连接进行处理。
//		IdleTimeout:        5 * time.Minute,  //闲置超时，默认5分钟，-1表示取消闲置超时检查
//		MaxConnAge:         0 * time.Second,  //连接存活时长，从创建开始计时，超过指定时长则关闭连接，默认为0，即不关闭存活时长较长的连接
//
//		//命令执行失败时的重试策略
//		MaxRetries:      0,                      // 命令执行失败时，最多重试多少次，默认为0即不重试
//		MinRetryBackoff: 8 * time.Millisecond,   //每次计算重试间隔时间的下限，默认8毫秒，-1表示取消间隔
//		MaxRetryBackoff: 512 * time.Millisecond, //每次计算重试间隔时间的上限，默认512毫秒，-1表示取消间隔
//
//		//可自定义连接函数
//		Dialer: func(ctx context.Context, network, addr string) (net.Conn, error) {
//			netDialer := &net.Dialer{
//				Timeout:   5 * time.Second,
//				KeepAlive: 5 * time.Minute,
//			}
//			return netDialer.Dial("tcp", "127.0.0.1:6379")
//		},
//
//		//钩子函数
//		OnConnect: func(ctx context.Context, conn *redis.Conn) error {
//			//仅当客户端执行命令时需要从连接池获取连接时，如果连接池需要新建连接时则会调用此钩子函数
//			fmt.Printf("conn=%v\n", conn)
//			return nil
//		},
//	})
//	_, err := rdb.Ping(ctx).Result()
//	if err != nil {
//		panic(err)
//	}
//}

//	func Set(key string, value interface{}, expire int64) error {
//		return rdb.Set(ctx, key, value, time.Duration(expire)*time.Second).Err()
//	}
//func (r *RedisApi) HSet(key string, field string, value interface{}) error {
//	return r.data.rdb.HSet(ctx, key, field, value).Err()
//}

//
//func Get(key string) (interface{}, error) {
//	return rdb.Get(ctx, key).Result()
//}
//
//func SetExpire(key string, expire int64) error {
//	return rdb.Expire(ctx, key, time.Duration(expire)*time.Second).Err()
//}
//
//func SMembers(key string) ([]string, error) {
//	return rdb.SMembers(ctx, key).Result()
//}
//
//func SAdd(key string, members ...interface{}) error {
//	return rdb.SAdd(ctx, key, members...).Err()
//}
//
//func SRemove(key string, members ...interface{}) error {
//	return rdb.SRem(ctx, key, members...).Err()
//}
//
//func Del(key string) error {
//	return rdb.Del(ctx, key).Err()
//}
//
//func Incr(key string) (int64, error) {
//	return rdb.Incr(ctx, key).Result()
//}
//
//func IncrBy(key string, value int64) (int64, error) {
//	return rdb.IncrBy(ctx, key, value).Result()
//}
//
//func Decr(key string) (int64, error) {
//	return rdb.Decr(ctx, key).Result()
//}
//
//func DecrBy(key string, value int64) (int64, error) {
//	return rdb.DecrBy(ctx, key, value).Result()
//}
//
//func Expire(key string, expire int64) error {
//	return rdb.Expire(ctx, key, time.Duration(expire)*time.Second).Err()
//}
//
//func Keys(pattern string) ([]string, error) {
//	return rdb.Keys(ctx, pattern).Result()
//}
//
//func Scan(cursor uint64, match string, count int64) ([]string, uint64, error) {
//	return rdb.Scan(ctx, cursor, match, count).Result()
//}
//
//func CloseRedis() {
//	err := rdb.Close()
//	if err != nil {
//		return
//	}
//}
//
//func GetRedisClient() *redis.Client {
//	return rdb
//}
//
//func SetRedisClient(client *redis.Client) {
//	rdb = client
//}
//
//func GetRedisContext() context.Context {
//	return ctx
//}
//
//func SetRedisContext(ctxNew context.Context) {
//	ctx = ctxNew
//}
