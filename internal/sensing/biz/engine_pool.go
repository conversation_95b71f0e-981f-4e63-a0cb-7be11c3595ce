package biz

import (
	"github.com/bilibili/gengine/engine"
	"github.com/go-kratos/kratos/v2/log"
)

type RuleEnginePool struct {
	pool *engine.GenginePool
}

func NewRuleEnginePool(poolMinLen, poolMaxLen int64, em int, rulesStr string, apiOuter map[string]interface{}) *RuleEnginePool {
	enginePool, e := engine.NewGenginePool(poolMinLen, poolMaxLen, em, rulesStr, apiOuter)
	if e != nil {
		log.Error("new gengine pool error", e)
	}
	ruleEnginePool := &RuleEnginePool{
		pool: enginePool,
	}
	return ruleEnginePool
}

func (r *RuleEnginePool) Execute(event *SensingEvent, ruleNames []string) error {
	data := make(map[string]interface{})
	data["Event"] = event
	e, _ := r.pool.ExecuteSelectedRules(data, ruleNames)
	if e != nil {
		log.Error("execute gengine error", e)
	}
	return e
}

func (r *RuleEnginePool) ExecuteWithStopTag(event *SensingEvent) error {
	data := make(map[string]interface{})
	data["Event"] = event
	stag := &engine.Stag{StopTag: false}
	data["stag"] = stag
	log.Infof("execute gengine withStopTag, data:%+v", event)
	e, result := r.pool.ExecuteWithStopTagDirect(data, true, stag)
	if e != nil {
		log.Error("execute gengine withStopTag error", e)
	}
	if result != nil {
		log.Infof("execute gengine withStopTag result:%v", result)
	}
	return e
}
