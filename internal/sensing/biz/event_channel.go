package biz

import (
	"sync"
)

type SafeQueue struct {
	queue []Event
	lock  sync.Mutex
}

func (q *SafeQueue) Enqueue(value *Event) {
	q.lock.Lock()
	defer q.lock.Unlock()

	q.queue = append(q.queue, *value)
}

func (q *SafeQueue) Dequeue() *Event {
	q.lock.Lock()
	defer q.lock.Unlock()

	if len(q.queue) == 0 {
		return nil // 表示队列为空
	}

	value := q.queue[0]
	q.queue = q.queue[1:]
	return &value
}

var queue = SafeQueue{}

func PushEvent(event *Event) {
	queue.Enqueue(event)
}

func TakeEvent() *Event {
	//log.Info("take one event")
	return queue.Dequeue()
}
