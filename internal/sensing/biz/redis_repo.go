package biz

import "context"

type RedisRepo interface {
	Get(context.Context, string) (interface{}, error)
	Set(context.Context, string, interface{}, int64) error
	SMembers(context.Context, string) ([]string, error)
	SAdd(context.Context, string, ...interface{}) error
	SRemove(context.Context, string, ...interface{}) error
	Del(context.Context, string) error
	Incr(context.Context, string) (int64, error)
	IncrBy(context.Context, string, int64) (int64, error)
	Decr(context.Context, string) (int64, error)
	DecrBy(context.Context, string, int64) (int64, error)
	Expire(context.Context, string, int64) error
}
