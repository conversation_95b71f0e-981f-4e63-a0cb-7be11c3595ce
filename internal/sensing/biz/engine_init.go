/*
 * @Descripttion: 从文件读取配置，初始化加载各引擎   先写死
 * @Author: jing<PERSON><PERSON>
 * @Date: 2023-10-07 11:35:00
 */

package biz

import (
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/sensing/util"
	"strconv"
)

const stuck_rule1 = `
rule "pull-l3.douyincdn.com"  salience 0
begin
		if  Event.Stuck_event(5,3,20) & Event.Stuck_fra(45, 10) {
			Event.SetAction("MigrateTask")
		} else if  Event.Stuck_event(5,3,20){
			Event.SetAction("Alarm")
		} else {
            Event.SetAction("Stuck")
        }
end
`

const stuck_rule2 = `
rule "pull-l3-spc.douyincdn.com"  salience 0
begin
		if  Event.Int("code")  > 10 {
			Event.SetAction("big")
			Event.Log("info", Event.GetAction())
		} else {
			Event.SetAction("old")
		}
end
`

type EventRuleConfig struct {
	Condition []string `json:"condition"`
	Action    []string `json:"action"`
}

//type RuleConfig struct {
//	EventsRuleConfig map[string]map[string]EventRuleConfig `json:"eventsRuleConfig"`
//}

var EngineArray map[string]*RuleEngine
var EnginePoolArray map[string]*RuleEnginePool

func init() {
	// 读取json配置   -->  读取nacos配置
	content, error := util.GetConfig("engine_rule.json", "DEFAULT_GROUP_PRE")
	if error != nil {
		log.Errorf("get engine rule config error, err:%s", error)
		return
	}
	if content == "" {
		log.Errorf("get engine rule config error, content is empty")
		return
	}

	err := util.ListenConfig("engine_rule.json", "DEFAULT_GROUP_PRE", func(namespace, group, dataId, data string) {
		if data == "" {
			log.Errorf("listen engine rule config change error, data is empty")
			return
		}
		getRuleConfig(data)
		updateEnginePool(data)
	})

	if err != nil {
		log.Errorf("listen engine rule config change error, err:%s", err)
		return
	}

	//file, err := ioutil.ReadFile("internal/sensing/biz/ruleengine/config2.json")
	//if err != nil {
	//	fmt.Println("读取配置文件失败:", err)
	//	return
	//}

	getRuleConfig(content)
	initEnginePool(content)
}

// todo 确认更新时的并发安全
func getRuleConfig(data string) {
	var ruleConfigData map[string]map[string]EventRuleConfig
	err := json.Unmarshal([]byte(data), &ruleConfigData)
	if err != nil {
		log.Errorf("parse engine rule config error, err:%s", err)
	}
	EngineArray = make(map[string]*RuleEngine)
	for eventType, eventRuleMap := range ruleConfigData {
		fmt.Println("Event Type:", eventType)
		level := len(eventRuleMap)
		for ruleKey, ruleConfig := range eventRuleMap {
			eng, _ := NewRuleEngine()
			eng.Reload(parseRule(ruleKey, ruleConfig, level))
			EngineArray[eventType+"_"+ruleKey] = eng
			level--
		}
	}
}

// action单独取出执行模式
//func parseRule(ruleKey string, ruleConfig EventRuleConfig) string {
//	// 将配置的条件和执行动作组装为策略引擎的Rule
//	var ruleHead = "rule \"" + ruleKey + "\" salience 0 \nbegin\n"
//	var ruleBody = ""
//	for index, value := range ruleConfig.Condition {
//		ruleBody += "if " + value + "{\n" + "Event.SetAction(\"" + ruleConfig.Action[index] + "\")\n" + "}\n"
//	}
//	var result = ruleHead + ruleBody + "\nend"
//	log.Infof("parseRule result:%s", result)
//	return result
//}

// action直接在策略里执行模式
func parseRule(ruleKey string, ruleConfig EventRuleConfig, level int) string {
	// 将配置的条件和执行动作组装为策略引擎的Rule。单rule执行后设置stag.StopTag = true跳过后续rule
	var ruleHead = "rule \"" + ruleKey + "\" salience " + strconv.Itoa(level) + " \nbegin\n"
	var ruleBody = ""
	for index, value := range ruleConfig.Condition {
		ruleBody += "if " + value + "{\n" + ruleConfig.Action[index] + "\n stag.StopTag = true }\n"
	}
	var result = ruleHead + ruleBody + "\nend"
	log.Infof("parseRule result:%s", result)
	return result
}

// 以下为引擎池的初始化，做成一个多迁引擎池数组形式
func initEnginePool(data string) {
	var ruleConfigData map[string]map[string]EventRuleConfig
	err := json.Unmarshal([]byte(data), &ruleConfigData)
	if err != nil {
		log.Errorf("parse engine rule config error, err:%s", err)
	}
	EnginePoolArray = make(map[string]*RuleEnginePool)
	for eventType, eventRuleMap := range ruleConfigData {
		fmt.Println("Event Type:", eventType)
		allRuleString := ""
		// 规则级别是数字越大越高
		level := len(eventRuleMap)
		for ruleKey, ruleConfig := range eventRuleMap {
			allRuleString += parseRule(ruleKey, ruleConfig, level) + "\n"
			level--
		}
		EnginePoolArray[eventType] = NewRuleEnginePool(5, 10, 1, allRuleString, nil)
	}
}

func updateEnginePool(data string) {
	var ruleConfigData map[string]map[string]EventRuleConfig
	err := json.Unmarshal([]byte(data), &ruleConfigData)
	if err != nil {
		log.Errorf("parse engine rule config error, err:%s", err)
	}
	for eventType, eventRuleMap := range ruleConfigData {
		fmt.Println("Event Type:", eventType)
		allRuleString := ""
		// 规则级别是数字越大越高
		level := len(eventRuleMap)
		for ruleKey, ruleConfig := range eventRuleMap {
			allRuleString += parseRule(ruleKey, ruleConfig, level) + "\n"
			level--
		}
		enginePool := EnginePoolArray[eventType]
		if enginePool != nil {
			err := enginePool.pool.UpdatePooledRulesIncremental(allRuleString)
			if err != nil {
				log.Errorf("update engine pool error, err:%s", err)
				return
			}
		} else {
			EnginePoolArray[eventType] = NewRuleEnginePool(5, 10, 1, allRuleString, nil)
		}
	}
}
