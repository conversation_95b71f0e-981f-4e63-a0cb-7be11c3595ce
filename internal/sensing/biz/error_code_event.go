package biz

import (
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/tidwall/gjson"
	"time"
)

type ErrorCodeEventBiz struct {
	*CommonEvent
}

func NewErrorCodeEventBiz(event *CommonEvent) *ErrorCodeEventBiz {
	return &ErrorCodeEventBiz{
		CommonEvent: event,
	}
}

// 引擎错误码事件
func (e *ErrorCodeEventBiz) EventHandle(event *Event) {
	log.Infof("Handle event with eventType: 101, event:%+v", event)
	jsonData, _ := json.Marshal(event)
	es := NewSensingEvent(e.config, "l2", string(jsonData), e.redisRepo)
	errorCodeRedisKey := "errorCode-" + gjson.Get(es.Data, "detail.workerIp").String()
	es.RedisStore(errorCodeRedisKey, 60*60)
	action, ok := event.DETAIL["action"].(string)
	if !ok {
		log.Info("action is not string or without action")
	} else {
		// 带了明确action的，直接执行
		es.SetAction(action)
		doAction(es)
		return
	}
	// 没有明确action的，调用策略判定
	eng := EnginePoolArray["errorCodeEvent"]
	es.SetData(e.getHistoryDataByTime(event))
	err := eng.ExecuteWithStopTag(es)
	if err != nil {
		log.Errorf("Handle event with eventType: 101, engine execute error, event:%v, err:%v", event, err)
		return
	}
}

func (e *ErrorCodeEventBiz) getHistoryDataByTime(eventOrigin *Event) string {
	log.Infof("getHistoryDataByTime")
	// 性能考虑，可将时间移除出来，只保留错误码。数据预处理在外部先处理掉。在有多个策略的情况下，性能较优
	workerIp, _ := eventOrigin.DETAIL["workerIp"].(string)
	errorCodeRedisKey := "errorCode-" + workerIp
	datas, _ := redisRepo.SMembers(ctx, errorCodeRedisKey)
	if len(datas) == 0 {
		// 理论上不会进来，至少有自身一个
		log.Infof("getHistoryDataByTime with no history data:%s", errorCodeRedisKey)
	}

	var events []SensingEvent
	for _, str := range datas {
		var eventOld SensingEvent
		err := json.Unmarshal([]byte(str), &eventOld)
		if err != nil {
			log.Errorf("unmarshal error, err:%s", err)
			continue
		}
		events = append(events, eventOld)
	}

	earliestTime := time.Now().Unix() - e.business.ErrorCode.ErrorCodeTimeRegion
	codeArray := eventOrigin.DETAIL["errorCode"].([]interface{})
	var errorCodeNames []string
	for _, v := range codeArray {
		errorCodeNames = append(errorCodeNames, v.(map[string]interface{})["name"].(string))
	}

	errorCodeCountMap := make(map[string]int64)
	levelCountMap := make(map[string]int64)

	for _, event := range events {
		if event.Time < earliestTime {
			continue
		} else {
			eventCodeArray := gjson.Get(event.Data, "detail.errorCode").Array()
			for _, eventCode := range eventCodeArray {
				levelCountMap[gjson.Get(eventCode.String(), "level").String()] += gjson.Get(eventCode.String(), "count").Int()
				errorCodeName := gjson.Get(eventCode.String(), "name").String()
				if contains(errorCodeNames, errorCodeName) {
					errorCodeCountMap[errorCodeName] += gjson.Get(eventCode.String(), "count").Int()
				}
			}
		}
	}
	eventOrigin.DETAIL["errorCodeCountMap"] = errorCodeCountMap
	eventOrigin.DETAIL["levelCountMap"] = levelCountMap

	jsonData, _ := json.Marshal(eventOrigin)
	return string(jsonData)
}

func contains(arr []string, val string) bool {
	for _, item := range arr {
		if item == val {
			return true
		}
	}
	return false
}

func (e *ErrorCodeEventBiz) SaveEvent() error {
	return nil
}

// 引擎错误码判断逻辑 具体错误码 + 时间 + 次数
func (e *SensingEvent) ErrorCodeCountByTime(errorCode string, timeSeconds int64) int64 {
	log.Infof("condition, ErrorCodeCountByTime start, errorCode:%s, timeSeconds:%d", errorCode, timeSeconds)

	// 判断本次上报有没有符合规则输入的errorcode
	codeArray := gjson.Get(e.Data, "detail.errorCode").Array()
	var count int64
	for _, code := range codeArray {
		if gjson.Get(code.String(), "name").String() == errorCode {
			count += gjson.Get(code.String(), "count").Int()
		}
	}
	if count == 0 {
		// 该判定错误码本次未上报或上报是0，本次判断不做处理，返回false
		log.Warnf("condition, ErrorCodeCountByTime end with no attache errorCode this time, errorCode:%s", errorCode)
		return 0
	}

	errorCodeRedisKey := "errorCode-" + gjson.Get(e.Data, "detail.workerIp").String()
	datas, _ := redisRepo.SMembers(ctx, errorCodeRedisKey)
	if len(datas) == 0 {
		// 理论上不会进来，至少有自身一个
		log.Infof("no list errorCode, errorCode:%s, key:%s", errorCode, errorCodeRedisKey)
		return 0
	}

	// count重新置为0,统计指定时间内的值
	count = 0
	var events []SensingEvent
	for _, str := range datas {
		var eventOld SensingEvent
		err := json.Unmarshal([]byte(str), &eventOld)
		if err != nil {
			log.Errorf("unmarshal error, err:%s", err)
			continue
		}
		events = append(events, eventOld)
	}

	earliestTime := e.Time - timeSeconds
	for _, eventData := range events {
		if eventData.Time > earliestTime {
			codeArray = gjson.Get(eventData.Data, "detail.errorCode").Array()
			for _, code := range codeArray {
				if gjson.Get(code.String(), "name").String() == errorCode {
					count += gjson.Get(code.String(), "count").Int()
				}
			}
		}
	}
	log.Infof("condition, ErrorCodeCountByTime end, errorCode:%s, count:%d", errorCode, count)
	return count
}

func (e *SensingEvent) ErrorCodeCount(errorCode string) int64 {
	log.Infof("condition, ErrorCodeCount start, errorCode:%s", errorCode)

	codeCountMap := gjson.Get(e.Data, "detail.errorCodeCountMap").Map()
	count := codeCountMap[errorCode].Int()
	log.Infof("condition, ErrorCodeCount end, errorCode:%s, count:%d", errorCode, count)
	return count
}

func (e *SensingEvent) LevelCountByTime(level string, timeSeconds int64) int64 {
	log.Infof("condition, LevelCountByTime start, level:%s, timeSeconds:%d", level, timeSeconds)

	// 判断本次上报有没有符合规则输入的level
	codeArray := gjson.Get(e.Data, "detail.errorCode").Array()
	var count int64
	for _, code := range codeArray {
		if gjson.Get(code.String(), "level").String() == level {
			count += gjson.Get(code.String(), "count").Int()
		}
	}
	if count == 0 {
		// 该判定错误码本次未上报或上报是0，本次判断不做处理，返回false
		log.Warnf("condition, LevelCountByTime end with no attache level this time, level:%s", level)
		return 0
	}

	errorCodeRedisKey := "errorCode-" + gjson.Get(e.Data, "detail.workerIp").String()
	datas, _ := redisRepo.SMembers(ctx, errorCodeRedisKey)
	if len(datas) == 0 {
		log.Infof("no list errorCode, level:%s, key:%s", level, errorCodeRedisKey)
		return 0
	}
	// count重新置为0,统计指定时间内的值
	count = 0
	var events []SensingEvent
	for _, str := range datas {
		var eventOld SensingEvent
		err := json.Unmarshal([]byte(str), &eventOld)
		if err != nil {
			log.Errorf("unmarshal error, err:%s", err)
			continue
		}
		events = append(events, eventOld)
	}

	earliestTime := e.Time - timeSeconds
	for _, eventData := range events {
		if eventData.Time > earliestTime {
			codeArray = gjson.Get(eventData.Data, "detail.errorCode").Array()
			for _, code := range codeArray {
				if gjson.Get(code.String(), "level").String() == level {
					count += gjson.Get(code.String(), "count").Int()
				}
			}
		}
	}
	log.Infof("condition, LevelCountByTime end, level:%s, count:%d", level, count)
	return count
}

func (e *SensingEvent) LevelCount(level string) int64 {
	log.Infof("condition, GetLevelCount start, level:%s", level)
	levelCountMap := gjson.Get(e.Data, "detail.levelCountMap").Map()
	count := levelCountMap[level].Int()
	log.Infof("condition, GetLevelCount end, level:%s, count:%d", level, count)
	return count
}
