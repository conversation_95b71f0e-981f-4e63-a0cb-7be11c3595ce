package biz

import (
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"strings"
)

type StuckEventBiz struct {
	*CommonEvent
}

func NewStuckEventBiz(event *CommonEvent) *StuckEventBiz {
	return &StuckEventBiz{
		CommonEvent: event,
	}
}

// 引擎池测试
func (e *StuckEventBiz) EventHandle(event *Event) {
	// 引擎上报编码卡顿事件处理
	log.Info("Handle event with eventType: 26")
	// 处理好所有判断数据，交由策略判定
	// 1.当前时间中的所有需要的数据 2.redis中的历史卡顿数据  注：暂不调用worker查询负载，查询redis也不用了---策略的action里看看怎么使用
	//stream := gjson.Get(event.DETAIL, "stream").String()
	//workerIp := gjson.Get(event.DETAIL, "workerIp").String()
	//taskId := gjson.Get(event.DETAIL, "taskId").String()
	//redisKey := workerIp + "-" + taskId

	_, _, domain := urlFrom(event.DETAIL["stream"].(string))

	// 转sensing-event
	jsonData, _ := json.Marshal(event)
	es := NewSensingEvent(e.config, "l2", string(jsonData), e.redisRepo)
	// 调用策略判定单次卡顿
	// todo 增加策略是否存在的提前判断，存在的策略名称存个数组
	eng := EnginePoolArray["stuckEvent"]
	err := eng.Execute(es, []string{domain})
	if err != nil {
		log.Errorf("Handle event with eventType: 26, engine execute error, event:%+v", event)
		panic(err)
		return
	}
	// 判断action和执行action   都写到event里可以不用反射调用
	doAction(es)
}

func urlFrom(streamUrl string) (string, string, string) {
	// 使用正则表达式进行匹配
	if regex.MatchString(streamUrl) {
		fmt.Println("regex match success")
	}
	matches := regex.FindAllStringSubmatch(streamUrl, 1)
	if matches != nil {
		appName := matches[0][1]
		streamName := matches[0][2]
		domain := matches[0][3]
		if strings.LastIndex(streamName, "_") > 0 {
			streamName = streamName[:strings.LastIndex(streamName, "_")]
		}
		return appName, streamName, domain
	}
	return "", "", ""
}
