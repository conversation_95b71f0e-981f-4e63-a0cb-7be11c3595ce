package biz

import (
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/sensing/conf"
	"mpp/internal/sensing/util"
	"reflect"
	"strings"
)

type CommonEvent struct {
	config    *conf.Data
	business  *conf.Business
	mnsClient *util.MnsClient
	redisRepo RedisRepo
	log       *log.Helper
}

func NewCommonEvent(c *conf.Data, business *conf.Business, mnsClient *util.MnsClient, redisRepo RedisRepo, logger log.Logger) *CommonEvent {
	return &CommonEvent{
		config:    c,
		business:  business,
		mnsClient: mnsClient,
		redisRepo: redisRepo,
		log:       log.NewHelper(log.With(logger, "module", "biz/common_event")),
	}
}

// EventHandle 通用事件处理  --- 简单demo
// 规则一：带了明确action的，直接执行
// 规则二：没有明确action的，按照策略判定后执行
func (e *CommonEvent) EventHandle(event *Event) {
	// 处理好所有判断数据，交由策略判定
	// 任务操作需要条件：taskId、transcode域名--> 携带区域即可
	// 转sensing-event common事件就不做存储了
	jsonData, _ := json.Marshal(event)
	es := NewSensingEvent(e.config, "l2", string(jsonData), e.redisRepo)
	//action := gjson.Get(event.DETAIL, "action").String()
	action, ok := event.DETAIL["action"].(string)
	if !ok {
		log.Info("action is not string or without action")
	} else {
		// 带了明确action的，直接执行
		es.SetAction(action)
		doAction(es)
		return
	}
	// 没有明确action的，调用策略判定
	eng := EnginePoolArray["commonEvent"]
	//ruleName := gjson.Get(event.DETAIL, "ruleName").String()
	ruleName, ok := event.DETAIL["ruleName"].(string)
	if !ok {
		log.Errorf("ruleName is not string or without ruleName")
		return
	}
	err := eng.Execute(es, []string{ruleName})
	if err != nil {
		log.Errorf("Handle event with eventType: 1, engine execute error, event:%+v, error:%s", event, err)
		return
	}
	// 判断action和执行action   都写到event里可以不用反射调用
	doAction(es)
}

func doAction(es *SensingEvent) {
	action := es.GetAction()
	if len(action) == 0 {
		log.Info("action is empty, no need to execute")
		return
	}
	// 遍历反射执行action
	for _, value := range action {
		// action方法名字可能是method，a，b  a,b是入参，需要切割
		methodArgsValue := strings.Split(value, ",")
		methodValue := reflect.ValueOf(es).MethodByName(methodArgsValue[0])
		// 判断方法是否存在
		if methodValue.IsValid() {
			var args []reflect.Value
			// 调用方法，传入入参
			if len(methodArgsValue) > 1 {
				// 遍历除第一个元素之后的值
				for _, param := range methodArgsValue[1:] {
					// todo 入参可以是函数执行么，比如是json获取？
					args = append(args, reflect.ValueOf(param))
				}
			}
			methodValue.Call(args)
		}
	}
}
