package biz

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

// 定义规则
const rule1 = `
rule "name test" "i can"  salience 0
begin
		if  Event.Int("age")  < 100 {
			Event.SetAction("small")
			Event.Log("info", Event.GetAction())
		} else {
			Event.SetAction("old")
		}
end
`

// 定义规则
const rule2 = `rule "name test" "i can"  salience 0 begin if  Event.Int("age")  < 100 { Event.SetAction("small2") } end `

func TestRuleEngine(t *testing.T) {
	const json = `{"name":{"first":"<PERSON>","last":"<PERSON>rich<PERSON>"},"age":47}`
	es := NewSensingEvent(nil, "l2", json, nil)
	eng, err := NewRuleEngine()
	assert.NoError(t, err)

	eng.Reload(rule1)

	err = eng.Run(es)
	//assert.NoError(t, err)
	assert.Equal(t, "small", es.GetAction()[0])
}

func TestRuleEngineElse(t *testing.T) {
	const json = `{"name":{"first":"<PERSON>","last":"<PERSON><PERSON><PERSON>"},"age":101}`
	es := NewSensingEvent(nil, "l2", json, nil)
	eng, err := NewRuleEngine()
	assert.NoError(t, err)

	eng.Reload(rule1)

	err = eng.Run(es)
	//assert.NoError(t, err)
	assert.Equal(t, "old", es.GetAction()[0])
}

func TestRuleEngineMultiRule(t *testing.T) {
	const json = `{"name":{"first":"Janet","last":"Prichard"},"age":47}`
	es := NewSensingEvent(nil, "l2", json, nil)
	eng, err := NewRuleEngine()
	assert.NoError(t, err)

	//eng.Reload(rule1)
	eng.Reload(rule2)

	err = eng.Run(es)
	//assert.NoError(t, err)
	assert.Equal(t, "small2", es.GetAction()[0])
}
