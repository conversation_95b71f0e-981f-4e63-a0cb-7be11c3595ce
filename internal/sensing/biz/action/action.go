package action

import (
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/magiconair/properties"
	"mpp/internal/sensing/conf"
	"mpp/internal/sensing/liveclient"
	"mpp/internal/sensing/pop"
	"mpp/internal/sensing/util"
	"strings"
	"time"
)

const (
	OFF_TAG = "livetranscode_off_pub"
)

//type Action interface {
//	Alarm(msg string)
//	AddTag(tag string)
//	//MigrateTask(taskID string)
//}

// dictator & transcode接口

type ResourceLevel interface {
	AddTag(dictatorKey, dictatorUrl string, workerIps []string, tag string)
}

type TaskLevel interface {
	MigrateTask(transcodeKey, transcodeUrl, taskId string)
}

// 预留集群级别
type ClusterLevel interface {
}

// 预留区域级别
type RegionLevel interface {
}

type Action struct {
	clientConfig *properties.Properties
	popClient    *liveclient.PopClient
}

func NewAction(c *conf.Data) *Action {
	ac := &Action{
		clientConfig: GetClientConfig(),
		popClient:    liveclient.NewRmsPopClient(c),
	}
	go ac.reloadConfig()
	return ac
}

func (a *Action) reloadConfig() {
	// 设置一个定时器，每2秒执行一次
	ticker := time.NewTicker(5 * time.Second)

	// 使用一个无限循环来不断地检查定时器的通道
	for {
		select {
		case <-ticker.C:
			// 在这里编写定期执行的任务
			err := util.ListenConfig("client_config.properties", "DEFAULT_GROUP_PRE",
				func(namespace, group, dataId, data string) {
					log.Infof("listen lt/ld client config on change, dataId:%s, data:%s", dataId, data)
					a.clientConfig = properties.MustLoadString(data)
				})
			if err != nil {
				log.Errorf("listen lt/ld client config error:%v", err)
				return
			}
		}
	}
}

func GetClientConfig() *properties.Properties {
	content, err := util.GetConfig("client_config.properties", "DEFAULT_GROUP")
	if err != nil {
		log.Errorf("get lt/ld client config error, err:%s", err)
		return nil
	}
	if content == "" {
		log.Errorf("get lt/ld client config error, content is empty")
		// todo 加载配置失败的后续处理手段，是否直接应用失败 --> return后结束
		return nil
	}
	clientConfig := properties.MustLoadString(content)
	return clientConfig
}

//func init() {
//	//clientConfig = properties.MustLoadFile("internal/sensing/biz/action/clientConfig.properties", properties.UTF8)
//	//// 定时更新配置
//	//timer := time.NewTicker(10 * time.Second)
//	//go func() {
//	//	for {
//	//		// 等待定时器的触发事件
//	//		<-timer.C
//	//		// 执行任务操作
//	//		clientConfig = properties.MustLoadFile("internal/sensing/biz/action/clientConfig.properties", properties.UTF8)
//	//	}
//	//}()
//	// 获取nacos中的配置
//	//util.NewClient("127.0.0.1", "ec854294-0ef7-41f9-99db-78da59fafb8f", 8848)
//	content, error := util.GetConfig("client_config.properties", "DEFAULT_GROUP_PRE")
//	if error != nil {
//		log.Errorf("get lt/ld client config error, err:%s", error)
//		return
//	}
//	if content == "" {
//		log.Errorf("get lt/ld client config error, content is empty")
//		// todo 加载配置失败的后续处理手段，是否直接应用失败 --> return后结束
//		return
//	}
//	clientConfig = properties.MustLoadString(content)
//	err := util.ListenConfig("client_config.properties", "DEFAULT_GROUP_PRE",
//		func(namespace, group, dataId, data string) {
//			log.Infof("listen lt/ld client config on change, dataId:%s, data:%s", dataId, data)
//			//content, _ := util.GetConfig("livetranscode_dictator_config.properties", "DEFAULT_GROUP")
//			//if content == "" {
//			//	log.Errorf("change lt/ld client config error, content is empty")
//			//	return
//			//}
//			clientConfig = properties.MustLoadString(data)
//		})
//	if err != nil {
//		log.Errorf("listen lt/ld client config error", err)
//		return
//	}
//}

// 告警透出：实际是把event所有信息上报到sls即可
func (a *Action) Alarm(msg string) {
	log.Warnf("action alarm, msg:%s", msg)
}

//func AddTag(sensingEvent *ruleengine.SensingEvent) {
//	log.WithoutContext().Info("action add tag", sensingEvent)
//	// 确定dictator地址   ---通过region+env确定 或者 直接上报dictator字段(可以根据事件区分）  dictator需要一个配置
//
//	dictatorAddress :=
//	// 确定达标下线的workerIp或Id
//
//	// 调用dictator进行打标  -- 需要对接审批？
//}

func (a *Action) AddTag(dictatorKey, dictatorUrl string, workerIps []string, tag string) {
	log.Infof("action add tag, dictatorKey:%s, url:%s, workerIps:%s", dictatorKey, dictatorUrl, workerIps)
	dictatorAddress := dictatorUrl
	if dictatorAddress == "" && dictatorKey != "" {
		dictatorAddress, _ = a.clientConfig.Get(dictatorKey) // todo dictator配置
	}
	if dictatorAddress == "" {
		log.Errorf("add tag error, dictatorAddress is empty, dictatorKey:%s, url:%s, workerIps:%s",
			dictatorKey, dictatorUrl, workerIps)
		return
	}
	err := liveclient.AddTag(dictatorAddress, tag, workerIps)
	if err != nil {
		log.Error("add tag error", err)
		return
	}
}

func (a *Action) MigrateTask(transcodeKey, transcodeUrl, taskId string) {
	log.Infof("action migrate task, transcodeKey:%s, transcodeUrl:%s, taskId:%s", transcodeKey, transcodeUrl, taskId)
	transcodeAddress := transcodeUrl
	if transcodeAddress == "" && transcodeKey != "" {
		transcodeAddress, _ = a.clientConfig.Get(transcodeKey)
	}
	if transcodeAddress == "" {
		log.Errorf("add tag error, transcodeAddress is empty, transcodeKey:%s, transcodeUrl:%s, taskId:%s",
			transcodeKey, transcodeUrl, taskId)
		return
	}
	err := liveclient.MigrateTask(transcodeAddress, taskId)
	if err != nil {
		log.Errorf("migrate task error:%v", err)
		return
	}
}

func (a *Action) MigrateTaskByWorker(transcodeAddrs []string, workerId string) {
	log.Infof("action migrate task by worker, transcodeAddrs:%s, workerId:%s", transcodeAddrs, workerId)
	for _, transcodeAddr := range transcodeAddrs {
		taskInfo, err := liveclient.GetTaskByWorker(transcodeAddr, workerId)
		if err != nil {
			log.Errorf("migrate task by worker error,can not get Task by worker:%v", err)
			continue
		}
		if len(taskInfo) == 0 {
			log.Warnf("migrate task by worker but get task empty, tr:%s, workerId:%s", transcodeAddr, workerId)
			continue
		}
		go func() {
			for _, task := range taskInfo {
				a.MigrateTask("", transcodeAddr, task.TaskId)
			}
		}()
	}
	log.Infof("action migrate task by worker success, transcodeAddrs:%s, workerId:%s", transcodeAddrs, workerId)
}

func (a *Action) MigrateRmsTasks(text, taskManagerKey, taskManagerUrl string, taskOrWorkerIds []string) error {
	log.Infof("action migrate rms task")
	taskManagerAddress := taskManagerUrl
	if taskManagerAddress == "" && taskManagerKey != "" {
		taskManagerAddress, _ = a.clientConfig.Get(taskManagerKey)
	}
	if taskManagerAddress == "" {
		log.Errorf("migrate rms tasks error, taskManagerAddress is empty, taskManagerKey:%s, taskManagerUrl:%s,"+
			" taskIds or workerIds:%s", taskManagerKey, taskManagerUrl, taskOrWorkerIds)
		return errors.InternalServer("migrate rms tasks error", "can not get taskManagerAddress")
	}
	err := a.popClient.MigrateRmsTasks(text, taskManagerAddress, taskOrWorkerIds)
	if err != nil {
		log.Errorf("migrate rms task error:%v", err)
		return err
	}
	return nil
}

func (a *Action) StopRmsTask(taskManagerKey, taskManagerUrl, taskId string) error {
	log.Infof("action stop rms task")
	taskManagerAddress := taskManagerUrl
	if taskManagerAddress == "" && taskManagerKey != "" {
		taskManagerAddress, _ = a.clientConfig.Get(taskManagerKey)
	}
	if taskManagerAddress == "" {
		log.Errorf("stop rms tasks error, taskManagerAddress is empty, taskManagerKey:%s, taskManagerUrl:%s,"+
			" taskIds:%s", taskManagerKey, taskManagerUrl, taskId)
		return errors.InternalServer("stop rms tasks error", "can not get taskManagerAddress")
	}
	err := a.popClient.StopRmsTask(taskManagerAddress, taskId)
	if err != nil {
		log.Errorf("stop rms task error:%v", err)
		return err
	}
	return nil
}

func (a *Action) QueryRmsTask(taskManagerKey, taskManagerUrl, taskId string) (*pop.ManagerTaskQueryResponseBodyDataTask, error) {
	log.Infof("action query rms task")
	taskManagerAddress := taskManagerUrl
	if taskManagerAddress == "" && taskManagerKey != "" {
		taskManagerAddress, _ = a.clientConfig.Get(taskManagerKey)
	}
	if taskManagerAddress == "" {
		log.Errorf("query rms tasks error, taskManagerAddress is empty, taskManagerKey:%s, taskManagerUrl:%s,"+
			" taskIds:%s", taskManagerKey, taskManagerUrl, taskId)
		return nil, errors.InternalServer("query rms tasks error", "can not get taskManagerAddress")
	}
	data, err := a.popClient.QueryRmsTask(taskManagerAddress, taskId)
	if err != nil {
		log.Errorf("query rms task error:%v", err)
		return data, err
	}
	return data, nil
}

func (a *Action) QueryMappingRelation(keyPrefix string) (map[string]string, error) {
	log.Infof("action query %s mapping relation", keyPrefix)
	if keyPrefix == "" {
		log.Errorf("query mapping relation error, key is empty")
		return nil, errors.InternalServer("query mapping relation error", "key is empty")
	}
	mapping := make(map[string]string)
	for _, key := range a.clientConfig.Keys() {
		// 检查键名是否有特定的前缀
		if strings.HasPrefix(key, keyPrefix) {
			value, _ := a.clientConfig.Get(key)
			mapping[key[len(keyPrefix)+1:]] = value
		}
	}
	return mapping, nil
}

func (a *Action) GetTranscodeAddr(transcodeKey string) string {
	log.Infof("getTranscodeAddr with transcodeKey:%s", transcodeKey)
	transcodeAddress, _ := a.clientConfig.Get(transcodeKey)
	if transcodeAddress == "" {
		log.Errorf("getTranscodeAddr with transcodeKey :%s, error, transcodeAddress is empty", transcodeKey)
		return ""
	}
	return transcodeAddress
}

func (a *Action) GetDictatorAddr(dictatorKey string) string {
	log.Infof("getDictatorAddr with dictatorKey:%s", dictatorKey)
	dictatorAddress, _ := a.clientConfig.Get(dictatorKey)
	if dictatorAddress == "" {
		log.Errorf("getDictatorAddr with dictatorKey :%s, error, dictatorAddress is empty", dictatorKey)
		return ""
	}
	return dictatorAddress
}
