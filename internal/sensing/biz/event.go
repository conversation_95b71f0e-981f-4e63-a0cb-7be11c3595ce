package biz

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"regexp"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"mpp/internal/sensing/conf"
	"mpp/internal/sensing/util"

	ali_mns "github.com/aliyun/aliyun-mns-go-sdk"
)

type Event struct {
	ID          int64                  `json:"id,omitempty"`
	GMTCREATE   time.Time              `json:"-"`
	GMTMODIFIED time.Time              `json:"-"`
	UUID        string                 `json:"uuid,omitempty"`
	TYPE        int                    `json:"type,omitempty"`
	ENV         string                 `json:"env,omitempty"`
	REGION      string                 `json:"region,omitempty"`
	STATUS      int                    `json:"status,omitempty"`
	DETAIL      map[string]interface{} `json:"detail,omitempty"`
	EXTEND      map[string]interface{} `json:"extend,omitempty"`
}

type StuckEvent struct {
	TYPE     int                    `json:"type,omitempty"`
	ENV      string                 `json:"env,omitempty"`
	REGION   string                 `json:"region,omitempty"`
	WORKERIP string                 `json:"workerIp,omitempty"`
	DETAIL   map[string]interface{} `json:"detail,omitempty"`
	EXTEND   map[string]interface{} `json:"extend,omitempty"`
}

type CodeMap map[int]string

var CodeMapInstance = CodeMap{
	1:   "COMMON_EVENT",
	10:  "E_ECS_SYSMAIN_REBOOT",
	11:  "E_ECS_INST_FAIL",
	12:  "E_ECS_SYS_FAIL",
	13:  "E_ECS_MIGARATE",
	14:  "E_POD_EVICT",
	15:  "E_POD_EVICT_L2",
	16:  "E_NODE_CUTOVER_L2",
	17:  "E_NODE_CUTOVER_DONE_L2",
	20:  "E_TW_CPU",
	21:  "E_TW_MEM",
	22:  "E_TW_DISK",
	23:  "E_TW_NETWORK",
	24:  "E_TW_LOAD",
	25:  "E_TW_STUCK_READ",
	26:  "E_TW_STUCK_ENCODE",
	27:  "E_TW_STUCK_WRITE",
	28:  "E_TW_FPS_READ",
	30:  "E_LC_HOTSTREAM",
	101: "E_TW_ERRORCODE",
}

//var event *Event

var regex, _ = regexp.Compile("rtmp\\://[^/]+/([^/]+)/([^\\?]+)\\?.*vhost=([^\\&]+)")

//func init() {
//	go HandleEvent()
//}

type EventRepo interface {
	SaveEvent() error
}

type EventOperator interface {
	EventHandle(*Event)
}

type EventBiz struct {
	mnsClient *util.MnsClient
	repo      EventRepo
	log       *log.Helper

	commonEvent    *CommonEvent
	stuckEvent     *StuckEventBiz
	errorCodeEvent *ErrorCodeEventBiz
}

func NewEventBiz(c *conf.Data, business *conf.Business, mnsClient *util.MnsClient, repo EventRepo, redisRepo RedisRepo, logger log.Logger) *EventBiz {
	eventBiz := &EventBiz{
		mnsClient: mnsClient,
		repo:      repo,
		log:       log.NewHelper(log.With(logger, "module", "biz/event")),
	}
	eventBiz.commonEvent = NewCommonEvent(c, business, mnsClient, redisRepo, logger)
	eventBiz.errorCodeEvent = NewErrorCodeEventBiz(eventBiz.commonEvent)
	eventBiz.stuckEvent = NewStuckEventBiz(eventBiz.commonEvent)

	eventBiz.scheduledEventHandle()
	return eventBiz
}

func (e *EventBiz) scheduledEventHandle() {
	go e.handleEvent()
	go e.handleMnsEvent()
}

func (e *EventBiz) handleMnsEvent() {
	for {
		// 处理mns事件
		endChan := make(chan int)
		respChan := make(chan ali_mns.MessageReceiveResponse)
		errChan := make(chan error)
		go func() {
			log.Infof("response:")
			select {
			case resp := <-respChan:
				{
					log.Infof("change the visibility:%s ", resp.ReceiptHandle)
					if ret, err := e.mnsClient.Queue.ChangeMessageVisibility(resp.ReceiptHandle, 5); err != nil {
						log.Errorf("change visibility error:%s ", err)
					} else {
						log.Infof("visibility changed:%v", ret)
						log.Infof("delete it now:%s ", ret.ReceiptHandle)
						// 先转为event
						decodedBytes, err := base64.StdEncoding.DecodeString(resp.MessageBody)
						if err != nil {
							fmt.Println("Decode Error:", err)
							endChan <- 1
						}
						// decodedBytes是一个字节切片，转换为字符串
						decodedString := string(decodedBytes)
						var mnsData map[string]interface{}
						unmarshalErr := json.Unmarshal([]byte(decodedString), &mnsData)
						if unmarshalErr != nil {
							fmt.Println("Error unmarshalling JSON:", err)
							endChan <- 1
						}
						// todo 对接策略，策略配置过滤条件配置告警

						// 只取出data部分给event的detail
						commonEvent := &Event{
							GMTCREATE:   time.Now(),
							GMTMODIFIED: time.Now(),
							UUID:        mnsData["id"].(string),
							TYPE:        2, // todo类型待定
							ENV:         "mns",
							REGION:      "mns",
							STATUS:      0,
							DETAIL:      mnsData["data"].(map[string]interface{}),
							EXTEND:      nil,
						}
						PushEvent(commonEvent)

						if err := e.mnsClient.Queue.DeleteMessage(ret.ReceiptHandle); err != nil {
							log.Errorf("delete message error:%v ", e)
						}
						endChan <- 1
					}
				}
			case err := <-errChan:
				{
					log.Errorf("receive message error:%s ", err)
					// 没有消息会走这里
					time.Sleep(1000 * time.Millisecond)
					endChan <- 1
				}
			}
		}()
		e.mnsClient.ReceiveMessage(respChan, errChan)
		<-endChan
	}
}

func (e *EventBiz) handleEvent() {
	// 循环取事件处理
	for {
		event := TakeEvent()
		if event == nil {
			log.Info("event queue is empty")
			time.Sleep(500 * time.Millisecond)
			continue
		}
		switch event.TYPE {
		case 1:
			// todo 通用快恢事件，直接编排所有action，配置一条通用策略,由事件字段触发对应action。
			e.commonEvent.EventHandle(event)
		case 2:
			// 任务级别通用快恢事件
			//taskEventHandle(event)
			//log.Info("Handle event with eventType: 2")
		case 3:
			// 实例级别通用快恢事件
			//instanceEventHandle(event)
			log.Info("Handle event with eventType: 3")
		case 4:
			// 集群级别通用快恢事件
			//clusterEventHandle(event)
			log.Info("Handle event with eventType: 3")
		case 26:
			// 引擎上报编码卡顿事件处理
			e.stuckEvent.EventHandle(event)
		case 30:
			// 直播中心汇报热流事件处理
			log.Info("Handle event with eventType: 30")
		case 101:
			// 引擎上报错误码事件处理
			e.errorCodeEvent.EventHandle(event)
		default:
			// 错误事件处理，打印日志，啥也不干
			log.Errorf("Handle event with unknown eventType error")
		}
	}
}

//func stuckEventHandle(event Event) {
//	// 引擎上报编码卡顿事件处理
//	log.Info("Handle event with eventType: 26")
//	// 处理好所有判断数据，交由策略判定
//	// 1.当前时间中的所有需要的数据 2.redis中的历史卡顿数据  注：暂不调用worker查询负载，查询redis也不用了---策略的action里看看怎么使用
//	//stream := gjson.Get(event.DETAIL, "stream").String()
//	//workerIp := gjson.Get(event.DETAIL, "workerIp").String()
//	//taskId := gjson.Get(event.DETAIL, "taskId").String()
//	//redisKey := workerIp + "-" + taskId
//
//	_, _, domain := urlFrom(gjson.Get(event.DETAIL, "stream").String())
//
//	// 转sensing-event
//	jsonData, _ := json.Marshal(event)
//	es := ruleengine.NewSensingEvent("l2", string(jsonData))
//	// 调用策略判定单次卡顿
//	eng := ruleengine.EngineArray["stuckEvent_"+domain]
//	if eng == nil {
//		log.Warnf("Handle event with eventType: 26, eng is null, event:%s", event)
//		// 使用默认策略
//		eng = ruleengine.EngineArray["stuckEvent_default"]
//	}
//	err := eng.Run(es)
//	if err != nil {
//		panic(err)
//		return
//	}
//	// 判断action和执行action   都写到event里可以不用反射调用
//	doAction(es)
//}

func hot_event_handle() {
	// 直播中心汇报热流事件处理
	log.Info("Handle event with eventType: 30")
}

func CheckAndFillEvent(event *Event) bool {
	// 1.判断code的真实性 2.补充uuid字段
	var code = CodeMapInstance[event.TYPE]
	if code == "" {
		// todo 日志透出
		return false
	}
	if event.UUID == "" {
		str := fmt.Sprintf("%d%s%s%s%d", event.TYPE, event.ENV, event.REGION, event.DETAIL, time.Now().Unix())
		uuidFromByte := uuid.NewSHA1(uuid.NameSpaceDNS, []byte(str))
		event.UUID = uuidFromByte.String()
	}
	event.GMTCREATE = time.Now()
	event.GMTMODIFIED = time.Now()
	event.STATUS = 0
	return true
}

func SaveEvent(event *Event) {
	// todo 单次事件入库保存
}

func (e *EventBiz) Handle(ctx context.Context, event *Event) error {
	if !CheckAndFillEvent(event) {
		// todo 异常情况处理
		return errors.BadRequest("Input", "Type not exist")
	}
	SaveEvent(event)
	PushEvent(event)
	return nil
}
