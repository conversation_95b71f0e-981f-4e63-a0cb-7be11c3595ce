package biz

import (
	"github.com/bilibili/gengine/builder"
	"github.com/bilibili/gengine/context"
	"github.com/bilibili/gengine/engine"
)

type RuleEngine struct {
	eng        *engine.Gengine
	ruleString string
}

func NewRuleEngine() (r *RuleEngine, err error) {
	return &RuleEngine{}, nil
}

func (r *RuleEngine) Reload(ruleString string) {
	r.ruleString = ruleString
}

func (r *RuleEngine) Run(se *SensingEvent) error {
	dataContext := context.NewDataContext()
	//注入初始化的结构体
	dataContext.Add("Event", se)

	ruleBuilder := builder.NewRuleBuilder(dataContext)
	ruleBuilder.BuildRuleFromString(r.ruleString)
	eng := engine.NewGengine() // todo 线程池
	//dataContext.Add("Event", "aa")   // 可生效
	//执行规则
	err := eng.Execute(ruleBuilder, true)
	return err
}
