package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/tidwall/gjson"
	"mpp/internal/sensing/biz/action"
	"mpp/internal/sensing/conf"
	"mpp/internal/sensing/liveclient"
	"strconv"
	"strings"
	"time"
)

type SensingEvent struct {
	Source string   `json:"source,omitempty"`
	Action []string `json:"action,omitempty"`
	Data   string   `json:"data,omitempty"`
	Time   int64    `json:"time,omitempty"`
}

var redisRepo RedisRepo
var ac *action.Action
var ctx = context.Background()

func NewSensingEvent(c *conf.Data, source string, jsonData string, redis RedisRepo) *SensingEvent {
	t := &SensingEvent{Source: source}
	t.Data = jsonData
	//t.Action = make([]string, 10)
	t.Time = time.Now().Unix()
	redisRepo = redis
	ac = action.NewAction(c)
	return t
}

func (s *SensingEvent) GetData() string {
	return s.Data
}

func (s *SensingEvent) SetData(data string) {
	s.Data = data
}

func (s *SensingEvent) Int(path string) int64 {
	return gjson.Get(s.Data, path).Int()
}

func (s *SensingEvent) String(path string) string {
	return gjson.Get(s.Data, path).String()
}

func (s *SensingEvent) Exists(path string) bool {
	return gjson.Get(s.Data, path).Exists()
}

func (s *SensingEvent) Float(path string) float64 {
	return gjson.Get(s.Data, path).Float()
}

func (s *SensingEvent) SetAction(Action string) {
	s.Action = append(s.Action, Action)
}

func (s *SensingEvent) GetAction() []string {
	return s.Action
}

func (s *SensingEvent) Log(level, msg string) {
	fmt.Println(msg)
}

// 实现action的告警接口
func (e *SensingEvent) Alarm() {
	ac.Alarm(e.GetData())
}

// 实现action的打标下线接口
func (e *SensingEvent) AddTag(tag string) {
	log.Infof("action, add tag, data：%v", e)
	// 确定获取dictator地址的key   ---通过region+env确定
	dictatorAddressKey := "livedictator." + e.String("region") + "." + e.String("env")

	// 也允许detail里带有地址  小心空指针？
	dictatorUrl := gjson.Get(e.Data, "detail.dictatorAddr").String()

	// 确定打标下线的workerIp (注意，可能是个数组）
	workerIp := gjson.Get(e.Data, "detail.workerIp").String()
	// 调用dictator进行打标  -- 需要对接审批？
	ac.AddTag(dictatorAddressKey, dictatorUrl, []string{workerIp}, tag)
}

func (e *SensingEvent) MigrateTask() {
	log.Infof("action, migrate task, data：%v", e)
	// 确定transcode地址，确定taskId
	transcodeKey := "livetranscode." + e.String("region")
	detail := e.String("detail")
	ac.MigrateTask(transcodeKey, "", gjson.Get(detail, "taskId").String()) // todo 成功失败的后续处理？
}

// 将来上云期间，会存在dictator对多个transcode，包括单元2场景，针对worker的迁移，需要迁移全部transcode关联的任务
// 这里设计transcode是由dictator拿到，格式是 {dictatorAddr:"transcodeAddr1,transcodeAddr2"}
func (e *SensingEvent) MigrateTaskByWorker() {
	log.Infof("action, migrate task by worker, data：%v", e)
	// 确定transcode地址, 这里是个逗号分隔的string，实际可能是transcode列表
	transcodeAddr := getTranscodeAddr(e)
	if transcodeAddr == "" {
		log.Errorf("getTranscodeAddr empty")
		return
	}

	transcodeAddrs := strings.Split(transcodeAddr, ",")
	// 确定打标下线的workerIp (注意，可能是个数组）
	workerIp := gjson.Get(e.Data, "detail.workerIp").String()
	workerInfo := liveclient.GetWorkerInfoByIp(getDictatorAddr(e), workerIp)
	if workerInfo == nil {
		log.Errorf("get worker info return null, workerIp:%s", workerIp)
		return
	}
	ac.MigrateTaskByWorker(transcodeAddrs, workerInfo.Uuid)
	log.Infof("action, migrate task by worker, data：%v", e)
}

func getTranscodeAddr(e *SensingEvent) string {
	// 第一种情况，有dictator信息，通过dictator拿到所有的transcode地址
	if gjson.Get(e.Data, "detail.dictatorAddr").String() != "" {
		transcodeKey := gjson.Get(e.Data, "detail.dictatorAddr").String()
		return ac.GetTranscodeAddr(strings.ReplaceAll(transcodeKey, "http://", ""))
	}
	// 第二种情况，带有region信息
	if e.String("region") != "" {
		transcodeKey := "livetranscode." + e.String("region")
		return ac.GetTranscodeAddr(transcodeKey)
	}
	return ""
}

func getDictatorAddr(e *SensingEvent) string {
	// 第一种情况，直接有dictator信息
	if gjson.Get(e.Data, "detail.dictatorAddr").String() != "" {
		return gjson.Get(e.Data, "detail.dictatorAddr").String()
	}
	// 第二种情况，带有region信息
	if e.String("region") != "" {
		dictatorKey := "livedictator." + e.String("region")
		return ac.GetDictatorAddr(dictatorKey)
	}
	return ""
}

// ---------- 以下是卡顿相关特有判断方法 -----------------
// 判断是否卡顿
func (e *SensingEvent) StuckJudge(stuckFrames, stuckSeconds, acceptableFrames int) bool {
	log.Infof("condition, stuck judge, data：%v", e)
	detail := e.String("detail")
	stucksPerSecond := gjson.Get(detail, "stucks_per_second").String()
	framesPerSecond := gjson.Get(detail, "frames_per_second").String()
	if stucksPerSecond == "" || framesPerSecond == "" {
		return false
	}
	stucks := strings.Split(stucksPerSecond, "#")
	frames := strings.Split(framesPerSecond, "#")
	if len(stucks) != len(frames) || len(stucks) != 5 {
		return false
	}
	seconds := 0
	for i, v := range stucks {
		stuck, _ := strconv.Atoi(v)
		currentFrames, _ := strconv.Atoi(frames[i])
		if stuck >= stuckFrames && currentFrames < acceptableFrames {
			seconds++
		}
	}
	if seconds >= stuckSeconds {
		e.StuckRedisStore()
	}
	return seconds >= stuckSeconds
}

// todo worker负载查询
func (e *SensingEvent) WorkerLoadJudge() bool {
	log.Infof("condition, worker load judge, data：%v", e)
	dictatorAddressKey := "livedictator." + e.String("region") + "." + e.String("env")
	// 确定打标下线的workerIp (注意，可能是个数组）
	workerIp := gjson.Get(e.Data, "detail.workerIp").String()
	workerInfo := liveclient.GetWorkerInfoByIp(dictatorAddressKey, workerIp)
	if workerInfo == nil {
		log.Warnf("get worker info return null, workerIp:%s", workerIp)
		return false
	}
	if workerInfo.Load1 > float64(workerInfo.Quota)*0.9 {
		return false
	}
	return true
}

// 直播中心人数查询

// 满足条件的数据存入redis  key:stuck_workerIp_taskid  value：List<e>   固定stuck前缀，用于后续可以捞出全部stuck事件
func (e *SensingEvent) StuckRedisStore() bool {
	log.Infof("redis store start, data：%v", e)
	detail := e.String("detail")
	redisKey := "stuck_" + gjson.Get(detail, "workerIp").String() + "_" + gjson.Get(detail, "taskId").String()
	data, err := json.Marshal(e)
	if err != nil {
		panic(err)
	}
	log.Infof("redis store key:%s, data:%s", redisKey, string(data))
	err = redisRepo.SAdd(ctx, redisKey, data)
	if err != nil {
		return false
	}
	// 设置有效期1小时
	_ = redisRepo.Expire(ctx, redisKey, 60*60)
	return true
}

// 迁移任务判断
// todo 限制条件： 1.同一任务不允许频繁迁移，两分钟内只迁移一次 2.确保一天内一个taskId只能迁移最多两次
func (e *SensingEvent) NeedMigrateTaskJudge(maxPeriod, stuckPeriod int) bool {
	log.Infof("condition, need migrate task judge, data：%v", e)
	// 根据workerIp与taskId查询出历史结果，判断是否在满足连续卡顿，迁移任务
	detail := e.String("detail")
	taskId := gjson.Get(detail, "taskId").String()
	redisKey := "stuck_" + gjson.Get(detail, "workerIp").String() + "_" + taskId
	datas, _ := redisRepo.SMembers(ctx, redisKey)
	if len(datas) == 0 {
		log.Infof("no list stuck record, redisKey:%s", redisKey)
	}
	// 需要注意判断当前的事件是否已入库，会影响一次计数
	var events []SensingEvent
	for _, str := range datas {
		var eventOld SensingEvent
		err := json.Unmarshal([]byte(str), &eventOld)
		if err != nil {
			return false
		}
		events = append(events, eventOld)
	}
	// todo 加一个按时间排序后移除大于1小时的数据
	// 用于判断的历史最早时间，即往前maxPeriod个周期看看有多少相同的
	earliestTime := e.Time - int64(maxPeriod*5)
	count := 0
	for _, eventData := range events {
		if eventData.Time > earliestTime {
			count++
		}
		if count == stuckPeriod {
			// 等于就可以退出了，减少冗余判断
			break
		}
	}
	if count == stuckPeriod {
		// 卡顿周期满足要求，判断时效2分钟的redis里有任务记录，说明上次有迁移，本次不再迁移，负责进行迁移
		migrateRedisKeyMin := "migrate_min" + taskId
		migrateRedisKeyDay := "migrate_day" + taskId
		migrateRecordMin, _ := redisRepo.Get(ctx, migrateRedisKeyMin)
		migrateRecordDay, _ := redisRepo.Get(ctx, migrateRedisKeyDay)
		log.Infof("need migrate tasker, taskId:%s", taskId)
		if migrateRecordMin == "" && migrateRecordDay == "" {
			redisRepo.Set(ctx, migrateRedisKeyMin, "true", 120)
			redisRepo.Set(ctx, migrateRedisKeyDay, "true", 86400)
			log.Infof("add migrate action success, taskId:%s", taskId)
			return true
		}
		log.Infof("need migrate but task get limit, migrateLimitMin:%s,migrateLimitDay:%s", migrateRecordMin, migrateRecordDay)
	}
	return false
}

func (e *SensingEvent) MigrateRmsTasks() {
	log.Infof("action, migrate rms tasks, data：%v", e)
	// 确定taskmanager地址，确定taskId数组
	taskManagerKey := "mpp-taskmanager." + e.String("region")
	detail := e.String("detail")
	//taskId := gjson.Get(detail, "taskIds").String()
	//taskId = strings.ReplaceAll(taskId, "[", "")
	//taskId = strings.ReplaceAll(taskId, "]", "")
	taskIds := gjson.Get(detail, "taskIds").Array()
	var taskIdsStr []string
	for _, taskId := range taskIds {
		taskIdsStr = append(taskIdsStr, taskId.String())
	}
	ac.MigrateRmsTasks("", taskManagerKey, "", taskIdsStr)
}

func (e *SensingEvent) StopRmsTasks() {
	log.Infof("action, stop rms tasks, data：%v", e)
	// 确定taskmanager地址，确定taskId数组
	taskManagerKey := "mpp-taskmanager." + e.String("region")
	detail := e.String("detail")
	taskId := gjson.Get(detail, "taskId").String()
	ac.StopRmsTask(taskManagerKey, "", taskId)
}

// 引擎错误码判断逻辑 具体错误码 + 时间 + 次数
func (e *SensingEvent) ErrorCodeJudge(errorCode string, timeSeconds, num int64) bool {
	log.Infof("condition, ErrorCodeJudge, errorCode:%s, timeSeconds:%d, num:%d", errorCode, timeSeconds, num)
	errorCodeRedisKey := "errorCode-" + gjson.Get(e.Data, "detail.workerIp").String()
	datas, _ := redisRepo.SMembers(ctx, errorCodeRedisKey)
	if len(datas) == 0 {
		log.Infof("no list stuck errorCode, errorCode:%s", errorCode)
	}

	var events []SensingEvent
	for _, str := range datas {
		var eventOld SensingEvent
		err := json.Unmarshal([]byte(str), &eventOld)
		if err != nil {
			return false
		}
		events = append(events, eventOld)
	}

	earliestTime := e.Time - timeSeconds
	codeArray := gjson.Get(e.Data, "detail.errorCode").Array()
	var count int64
	for _, code := range codeArray {
		if gjson.Get(code.String(), "name").String() == errorCode {
			count += gjson.Get(code.String(), "count").Int()
		}
	}
	if count == 0 {
		// 该判定错误码本次未上报或上报是0，本次判断不做处理，返回false
		return false
	}
	// 本次事件已入库，重置count
	count = 0
	for _, eventData := range events {
		if eventData.Time > earliestTime {
			codeArray := gjson.Get(e.Data, "detail.errorCode").Array()
			for _, code := range codeArray {
				if gjson.Get(code.String(), "name").String() == errorCode {
					count += gjson.Get(code.String(), "count").Int()
				}
			}
		}
		if count >= num {
			// 等于就可以退出了，减少冗余判断
			break
		}
	}
	if count >= num {
		// 等于就可以退出了，减少冗余判断
		return true
	}
	return false
}

func (e *SensingEvent) RedisStore(redisKey string, expireTime int64) bool {
	log.Infof("redis store start, data:%v", e)
	data, err := json.Marshal(e)
	if err != nil {
		log.Errorf("redis store error while marshal data, data:%v, err:%v", e, err)
		return false
	}
	err = redisRepo.SAdd(ctx, redisKey, data)
	if err != nil {
		log.Errorf("redis store error while sadd data, data:%v, err:%v", e, err)
		return false
	}
	// 设置有效期1小时
	_ = redisRepo.Expire(ctx, redisKey, expireTime)
	log.Infof("redis store success, data:%v", e)
	return true
}
