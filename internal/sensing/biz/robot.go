package biz

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"io"
	"mpp/internal/sensing/biz/action"
	"mpp/internal/sensing/conf"
	"net/http"
	"net/url"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"
)

// Robot 机器人
type RobotBiz struct {
	config *conf.Data
	action *action.Action
}

// NewRobotBiz 机器人
func NewRobotBiz(c *conf.Data) *RobotBiz {
	return &RobotBiz{
		config: c,
		action: action.NewAction(c),
	}
}

type RobotReplay struct {
	Data string
	Code int
	Msg  string
}

const (
	SQL    = "select tw_avg(avg('value')) from %s where 'region' = '%s' and 'tag' = 'livetranscode_pub' since %s,now()-60s timewindow %s"
	ACTION = "Mql.query"
)

// 处理机器人请求
func (r *RobotBiz) DealRobotRequest(ctx context.Context, sessionWebhook, text string) error {
	// 划分类型  选择对应的场景action   case1 查询sunfire的、case2：直接调用调度系统的action的、case3：转化为标准event抛出由策略进行判断的
	log.Infof("robot input text content:%s", text)
	if strings.Contains(text, "水位") {
		// 先写死场景，查询水位
		return r.DealSunfireRequest(ctx, sessionWebhook, text)
	} else if strings.Contains(text, "rms") {
		// rms 快恢场景，具体场景方法内再细分
		return r.DealRmsRequest(ctx, sessionWebhook, text)
	} else {
		// 其他场景，转标准event再对应策略处理
		return r.DealNormalEventRequest(ctx, sessionWebhook, text)
	}
	return nil
}

func (r *RobotBiz) DealRmsRequest(ctx context.Context, sessionWebhook, text string) error {
	var err error
	var data string
	if strings.Contains(text, "迁移") || strings.Contains(text, "停止") || strings.Contains(text, "查询") || strings.Contains(text, "映射") {
		// 执行迁移和停止action
		data, err = r.DealRmsMigrateOrStop(ctx, sessionWebhook, text)
	} else {
		// rms 未知action，报错
		err = errors.BadRequest("unknown rms action", "当前仅支持迁移&停止&查询")
	}
	if err != nil {
		log.Errorf("deal rms request error:%s", err.Error())
		// 结果反馈回机器人
		return r.assemableRmsMarkDownReturn(ctx, err.Error(), sessionWebhook)
	}
	err = r.assemableRmsMarkDownReturn(ctx, "操作成功,请确认操作结果\n"+data, sessionWebhook)
	return err
}

func (r *RobotBiz) assemableRmsMarkDownReturn(ctx context.Context, data string, sessionWebhook string) error {
	// 遍历 result 数组，拼接markdown格式
	markdownData := "### 操作结果反馈 ###\n"
	markdownData += fmt.Sprintf("  %s \n", data)

	response := make(map[string]interface{})
	response["msgtype"] = "markdown"
	response["markdown"] = map[string]string{"title": "操作结果", "text": markdownData}

	// 调用机器人接口返回结果
	requestBody, _ := json.Marshal(response)
	post, err := http.Post(sessionWebhook, "application/json", bytes.NewReader(requestBody))
	if err != nil {
		log.Errorf("rms request error while do request:%s", err.Error())
		return err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			log.Errorf("rms request error with response body close :%s", err.Error())
		}
	}(post.Body)
	body, err := io.ReadAll(post.Body)
	log.Infof("rms request result:%s", string(body))
	return nil
}

func (r *RobotBiz) DealNormalEventRequest(ctx context.Context, sessionWebhook, text string) error {
	// 组装标准event
	// todo 预留
	return nil
}

func (r *RobotBiz) DealRmsMigrateOrStop(ctx context.Context, sessionWebhook, text string) (string, error) {
	var err error
	var data string
	if strings.Contains(text, "映射") {
		// 查询环境信息映射关系
		queryData, e := r.action.QueryMappingRelation("rms")
		err = e
		if queryData != nil {
			// 数据转为markdown格式
			for k, v := range queryData {
				data = data + fmt.Sprintf("- %s: %v \n", k, v)
			}
		} else {
			data = "未查询到环境信息映射关系"
		}
		return data, err
	}

	// text中解析出taskId，再由taskId解析出region
	taskIds := getRmsTaskIdsFromText(text)
	if len(taskIds) == 0 || taskIds == nil {
		return "", errors.BadRequest("taskId error", "输入规则为: 迁移/停止rms任务:xxx,xxx")
	}

	//区域配置 rms的taskId以00/01开头，该值与区域的关联关系由配置文件记录
	taskIdPrefix := strings.Split(taskIds[0], "&")
	taskManagerKey := "rms." + taskIdPrefix[0]
	taskIds[0] = taskIdPrefix[1]
	if strings.Contains(text, "迁移") {
		// 迁移场景，需要指定region
		err = r.action.MigrateRmsTasks(text, taskManagerKey, "", taskIds)
	} else if strings.Contains(text, "停止") {
		// 停止场景，需要指定region
		err = r.action.StopRmsTask(taskManagerKey, "", taskIds[0])
	} else if strings.Contains(text, "查询") && strings.Contains(text, "任务") {
		// 查询场景，需要指定region
		queryData, e := r.action.QueryRmsTask(taskManagerKey, "", taskIds[0])
		err = e
		if queryData != nil {
			// 数据转为markdown格式
			val := reflect.ValueOf(queryData).Elem()
			for i := 0; i < val.NumField(); i++ {
				valueField := val.Field(i)
				typeField := val.Type().Field(i)
				data = data + fmt.Sprintf("- %s: %v \n", typeField.Name, valueField.Elem())
			}
			//data = fmt.Sprintf("%+v", queryData)
		}
	} else {
		// 其他场景，报错
		return "", errors.BadRequest("unknown rms action", "当前仅支持迁移&停止&查询")
	}

	// todo 错误处理完善
	return data, err
}

func (r *RobotBiz) DealSunfireRequest(ctx context.Context, sessionWebhook, text string) error {
	sunfireData, _ := r.sunfireRequest(text)
	// 组装markdown数据返回
	err := r.assemableMarkDown(ctx, sunfireData, sessionWebhook)
	return err
}

func (r *RobotBiz) assemableMarkDown(ctx context.Context, sunfireData map[string]interface{}, sessionWebhook string) error {
	if sunfireData == nil {
		log.Errorf("sunfireData is nil")
		return fmt.Errorf("sunfireData is nil")
	}
	data := sunfireData["data"].(map[string]interface{})
	resultArray, resultOk := data["result"].([]interface{})
	if !resultOk {
		log.Errorf("Error: 'result' key is not an array")
		return fmt.Errorf("result is not an array")
	}

	// 遍历 result 数组，拼接markdown格式
	markdownData := "### 水位查询结果测试 ###\n"
	for _, item := range resultArray {
		itemMap, itemOk := item.(map[string]interface{})
		if !itemOk {
			log.Errorf("Error: item is not a map")
			continue
		}
		twAvg, _ := itemMap["tw_avg(avg(value))"].(float64)
		timestamp, _ := itemMap["timestamp"].(float64) // JSON 中的数字默认解析为 float64
		markdownData += fmt.Sprintf("- %s  ----  %f \n", time.Unix(int64(timestamp/1000), 0).Format("2006-01-02 15:04:05"), twAvg)
	}

	response := make(map[string]interface{})
	response["msgtype"] = "markdown"
	response["markdown"] = map[string]string{"title": "水位查询测试", "text": markdownData}

	// 调用机器人接口返回结果
	requestBody, _ := json.Marshal(response)
	post, err := http.Post(sessionWebhook, "application/json", bytes.NewReader(requestBody))
	if err != nil {
		log.Errorf("sunfire request error while do request:%s", err.Error())
		return err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			log.Errorf("sunfire request error with response body close :%s", err.Error())
		}
	}(post.Body)
	body, err := io.ReadAll(post.Body)
	log.Infof("sunfire request result:%s", string(body))
	return nil
}

func (r *RobotBiz) sunfireRequest(text string) (map[string]interface{}, error) {
	// 计算鉴权
	sql := getSunfireSql(text)
	params := url.Values{}
	params.Add("accessKeyId", r.config.Sunfire.AccessKeyId)
	params.Add("action", ACTION)
	params.Add("query", sql)
	params.Add("signatureVersion", "2")
	params.Add("timestamp", strconv.FormatInt(time.Now().UnixMilli(), 10))

	// 将"="连接得到的参数组合按顺序依次用"&"进行连接,得到形如"key1=value1&key2=value2..."的字符串,需要排序，不然鉴权有问题
	// 提取map中所有的键到一个切片
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}

	// 对切片进行排序
	sort.Strings(keys)

	var query = ""
	for k := range keys {
		query += keys[k] + "=" + params.Get(keys[k]) + "&"
	}
	query = query[:len(query)-1]

	// 计算签名
	var sk = r.config.Sunfire.AccessKeySecret
	var signature = getSignature(sk, query)
	params.Add("signature", signature)
	// 发送请求
	var baseURL = "http://api.x.alibaba-inc.com/api/dispatcher.do"
	u, err := url.Parse(baseURL)
	if err != nil {
		log.Fatal("Error parsing URL:", err)
	}
	// 将查询参数添加到URL的查询字符串中
	u.RawQuery = params.Encode()
	res, err := http.Get(u.String())
	if err != nil {
		log.Errorf("sunfire request error while do request:%s", err.Error())
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			log.Errorf("sunfire request error with response body close :%s", err.Error())
		}
	}(res.Body)
	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Errorf("sunfireRequest error in read response:%s", err.Error())
		return nil, err
	}
	// 失败结果形似：
	// 成功结果形似：{"code":0,"data":{"attends":null,"metadata":null,"result":[]},"msg":"success"}
	log.Infof("sunfire request result:%s", string(body))
	// body 转 json
	var resultData map[string]interface{}
	err = json.Unmarshal(body, &resultData)
	if err != nil {
		log.Errorf("sunfireRequest error in unmarshal response:%s", err.Error())
		return nil, err
	}
	return resultData, nil
}

func getSignature(sk, query string) string {
	key := []byte(sk)
	h := hmac.New(sha1.New, key)
	h.Write([]byte(query))
	signature := h.Sum(nil)
	// 将结果转换为十六进制字符串表示
	signatureHex := hex.EncodeToString(signature)
	fmt.Println("HMAC-SHA1加密结果（十六进制）:", signatureHex)
	return signatureHex
}

func getSunfireSql(text string) string {
	region := "cn-beijing"
	image := "sunfire.15_multiMinute_11244"
	if strings.Contains(text, "北京") {
		region = "cn-beijing"
	} else if strings.Contains(text, "北京售卖区") {
		region = "cn-beijing-public"
	} else if strings.Contains(text, "北京L2") || strings.Contains(text, "北京l2") {
		region = "cn-beijing-l2"
		image = "sunfire.15_multiMinute_25279"
	} else if strings.Contains(text, "北京L2-2") || strings.Contains(text, "北京l2-2") {
		region = "cn-beijing-l2-2"
		image = "sunfire.15_multiMinute_25279"
	} else if strings.Contains(text, "上海") {
		region = "cn-shanghai"
	} else if strings.Contains(text, "上海L2") || strings.Contains(text, "上海l2") {
		region = "cn-shanghai"
		image = "sunfire.15_multiMinute_24834"
	} else if strings.Contains(text, "深圳") {
		region = "cn-shenzhen"
	} else if strings.Contains(text, "新加坡") {
		region = "ap-southeast-1"
	} else if strings.Contains(text, "青岛") {
		region = "cn-qingdao"
	}

	sqlTime := "now()-86400s"
	pointTime := "300s" // 5分钟一个点
	re := regexp.MustCompile(`(\d+)(天|小时)`)
	matches := re.FindStringSubmatch(text)
	if matches != nil {
		log.Infof("原始字符串: '%s'，数值: '%s'，单位: '%s'\n", text, matches[1], matches[2])
		if matches[2] == "天" {
			tt, _ := strconv.ParseInt(matches[1], 10, 32)
			sqlTime = fmt.Sprintf("now()-%ds", 86400*tt)
			pointTime = fmt.Sprintf("%ds", 300*24*tt/2)
		} else if matches[2] == "小时" {
			tt, _ := strconv.ParseInt(matches[1], 10, 32)
			sqlTime = fmt.Sprintf("now()-%ds", 3600*tt)
			pointTime = fmt.Sprintf("%ds", 300*tt/2)
		}
	} else {
		log.Warnf("未匹配到时间字符串: '%s'\n", text)
	}
	return fmt.Sprintf(SQL, image, region, sqlTime, pointTime)
}

func getRmsTaskIdsFromText(text string) []string {
	//// 根据正则匹配获取
	//re := regexp.MustCompile(`\d{1,10}`)
	//matches := re.FindStringSubmatch(text)
	//var taskIdsStr []string
	//if matches != nil {
	//	for _, v := range matches {
	//		taskIdsStr = append(taskIdsStr, v)
	//	}
	//}

	// 规定输入语法规则为：迁移rms任务:xxx,xxx
	taskIdsStr := strings.Split(text, ":")
	if len(taskIdsStr) != 2 || len(taskIdsStr[1]) == 0 {
		return nil
	}
	taskIdsStr = strings.Split(taskIdsStr[1], ",")
	return taskIdsStr
}
