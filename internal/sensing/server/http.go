package server

import (
	"github.com/cinience/animus/contribs/transport/wrapper"
	"github.com/cinience/animus/health"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/http"
	"mpp/internal/sensing/conf"
	"mpp/internal/sensing/service"
	v1 "proto.mpp/api/sensing/v1"
	"time"

	netHttp "net/http"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(c *conf.Server, event *service.EventService, robot *service.RobotService, logger log.Logger) *http.Server {
	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
		),
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	} else {
		opts = append(opts, http.Timeout(3*time.Second))
	}
	httpServer := http.NewServer(opts...)
	// 增加默认健康检查API
	httpServer.Handle("/health", health.DefaultHealthService)
	//v1.RegisterSensingHTTPServer(httpServer, event)
	v1.RegisterRobotHTTPServer(httpServer, robot)

	// golang debug 信息泄露解决
	httpServer.HandleFunc("/debug/pprof", netHttp.NotFound)

	// 快恢输入参数为json，不使用protobuf接口
	r := httpServer.Route("/")
	r.POST("/v1/sensing/event", wrapper.WithStdHandlerAndError(event.PushEvent))
	//r.POST("/v1/sensing/robot", tm_http_wrapper.WithRecover(taskSrv.GetTaskStartHandler()))
	return httpServer
}
