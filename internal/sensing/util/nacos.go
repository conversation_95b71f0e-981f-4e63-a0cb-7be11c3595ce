package util

import (
	"github.com/nacos-group/nacos-sdk-go/v2/clients"
	"github.com/nacos-group/nacos-sdk-go/v2/clients/config_client"
	"github.com/nacos-group/nacos-sdk-go/v2/common/constant"
	"github.com/nacos-group/nacos-sdk-go/v2/model"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"
)

var client config_client.IConfigClient

func init() {
	//sc := []constant.ServerConfig{
	//	*constant.NewServerConfig(ipAddr, port, constant.WithContextPath("/nacos")),
	//}

	//create ClientConfig
	cc := *constant.NewClientConfig(
		constant.WithEndpoint("jmenv.tbsite.net:8080"),
		constant.WithEndpointContextPath("diamond-server"),
		constant.WithClusterName("diamond"),
		//constant.WithClusterName("diamond-unit-pre"),
		constant.WithEndpointQueryParams("nofix=1"),
		constant.WithAppName("mpp-sensing"),
		//constant.WithNamespaceId(namespaceId),
		constant.WithNamespaceId(""),
		constant.WithTimeoutMs(5000),
		constant.WithNotLoadCacheAtStart(true),
		constant.WithLogDir("/tmp/nacos/log"),
		constant.WithCacheDir("/tmp/nacos/cache"),
		constant.WithLogLevel("debug"),
	)

	var err error
	client, err = clients.NewConfigClient(
		vo.NacosClientParam{
			ClientConfig: &cc,
			//ServerConfigs: sc,
		},
	)
	if err != nil {
		panic(err)
	}
}

//func init() {
//	sc := []constant.ServerConfig{
//		*constant.NewServerConfig("127.0.0.1", 8848, constant.WithContextPath("/nacos")),
//	}
//
//	//create ClientConfig
//	cc := *constant.NewClientConfig(
//		constant.WithNamespaceId("ec854294-0ef7-41f9-99db-78da59fafb8f"),
//		constant.WithTimeoutMs(5000),
//		constant.WithNotLoadCacheAtStart(true),
//		constant.WithLogDir("/tmp/nacos/log"),
//		constant.WithCacheDir("/tmp/nacos/cache"),
//		constant.WithLogLevel("debug"),
//	)
//
//	var err error
//	client, err = clients.NewConfigClient(
//		vo.NacosClientParam{
//			ClientConfig:  &cc,
//			ServerConfigs: sc,
//		},
//	)
//	if err != nil {
//		panic(err)
//	}
//	content, _ := getConfig("application.yaml", "DEFAULT_GROUP")
//	fmt.Println("GetConfig,config :" + content)
//}

func GetConfig(dataId, group string) (string, error) {
	content, err := client.GetConfig(vo.ConfigParam{
		DataId: dataId,
		Group:  group,
	})
	return content, err
}

//func ListenConfig(dataId, group string, ) error {
//	err := client.ListenConfig(vo.ConfigParam{
//		DataId: dataId,
//		Group:  group,
//		OnChange: func(namespace, group, dataId, data string) {
//			fmt.Println("config changed group:" + group + ", dataId:" + dataId + ", content:" + data)
//		},
//	})
//	return err
//}

func ListenConfig(dataId, group string, callback func(namespace, group, dataId, data string)) error {
	err := client.ListenConfig(vo.ConfigParam{
		DataId: dataId,
		Group:  group,
		OnChange: func(namespace, group, dataId, data string) {
			callback(namespace, group, dataId, data)
		},
	})
	return err
}

func CancelListenConfig(dataId, group string) error {
	err := client.CancelListenConfig(vo.ConfigParam{
		DataId: dataId,
		Group:  group,
	})
	return err
}

func SearchConfig(search, dataId, group string, pageNo, pageSize int) (*model.ConfigPage, error) {
	searchPage, err := client.SearchConfig(vo.SearchConfigParam{
		Search:   search,
		DataId:   dataId,
		Group:    group,
		PageNo:   pageNo,
		PageSize: pageSize,
	})
	return searchPage, err
}
