/*
 * @Author: jinde.zgm
 * @Date: 2019-12-20 10:16:55
 * @Description:
 */

package util

import (
	"database/sql"
	"fmt"
	"time"
	//_ "github.com/go-sql-driver/mysql"
)

const (
	MysqlMaxIdleConns    = 40
	MysqlMaxOpenConns    = 1000
	MysqlConnMaxLifetime = time.Hour
	MysqlDeleteLimit     = 1000
)

var dbClient *sql.DB

func Init() {
	user := "root"
	password := "mysql123456"
	endpoint := "127.0.0.1"
	database := "livetranscode_sensing"
	db, err := NewMysqlDB(user, password, endpoint, database)
	dbClient = db
	if nil != err {
		panic(err)
	}
}

// NewMysqlDB create mysql client.
func NewMysqlDB(user, password, endpoint, database string) (*sql.DB, error) {
	// Open mysql.
	db, err := sql.Open("mysql", fmt.Sprintf("%s:%s@tcp(%s)/%s", user, password, endpoint, database))
	if nil != err {
		return nil, err
	}

	// Open doesn't open a connection. Validate DSN data:
	if err := db.Ping(); nil != err {
		return nil, err
	}

	db.SetMaxIdleConns(MysqlMaxIdleConns)
	db.SetMaxOpenConns(MysqlMaxOpenConns)
	db.SetConnMaxLifetime(MysqlConnMaxLifetime)

	return db, nil
}

func GetDbClient() *sql.DB {
	if dbClient == nil {
		Init()
	}
	return dbClient
}
