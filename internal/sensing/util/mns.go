package util

import (
	"github.com/aliyun/aliyun-mns-go-sdk"
	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/sensing/conf"
	_ "net/http/pprof"
)

type MnsClient struct {
	client *ali_mns.MNSClient
	Queue  ali_mns.AliMNSQueue
}

func NewMnsClient(c *conf.Data, logger log.Logger) *MnsClient {
	log.NewHelper(logger).Info("mns client init")
	m := &MnsClient{}
	mnsClient := ali_mns.NewAliMNSClient(c.Mns.Url, c.Mns.AccessKeyId, c.Mns.AccessKeySecret)
	queue := ali_mns.NewMNSQueue(c.Mns.QueueName, mnsClient)
	m.client = &mnsClient
	m.Queue = queue
	return m
}

func (m *MnsClient) ReceiveMessage(respChan chan ali_mns.MessageReceiveResponse, err<PERSON>han chan error) {
	m.Queue.ReceiveMessage(resp<PERSON><PERSON>, err<PERSON><PERSON>, 3)
}
