package server

import (
	"mpp/internal/taskmanager/mesh"
	"mpp/pkg/tracer/middleware/tracer"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/middleware/validate"
	"github.com/go-kratos/kratos/v2/transport/grpc"

	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/service"

	taskmanagerv1 "proto.mpp/api/taskmanager/v1"
)

// NewGRPCServer new a gRPC server.
func NewGRPCServer(c *conf.Server, taskService *service.TaskService, pipelineService *service.PipelineService, meshSrv *mesh.MeshService, grayscaleSrv *service.GrayscaleService, logger log.Logger) *grpc.Server {
	var opts = []grpc.ServerOption{
		grpc.Middleware(
			recovery.Recovery(),
			metadata.Server(),
			tracing.Server(),
			tracer.MPPServerTracer(),
			logging.Server(logger),
			validate.Validator(),
		),
	}
	if c.Grpc.Network != "" {
		opts = append(opts, grpc.Network(c.Grpc.Network))
	}
	if c.Grpc.Addr != "" {
		opts = append(opts, grpc.Address(c.Grpc.Addr))
	}
	if c.Grpc.Timeout != nil {
		opts = append(opts, grpc.Timeout(c.Grpc.Timeout.AsDuration()))
	}

	srv := grpc.NewServer(opts...)
	taskmanagerv1.RegisterTaskServer(srv, taskService)
	taskmanagerv1.RegisterPipelineServer(srv, pipelineService)
	taskmanagerv1.RegisterMeshServer(srv, meshSrv)
	taskmanagerv1.RegisterGrayscaleServiceServer(srv, grayscaleSrv)

	return srv
}
