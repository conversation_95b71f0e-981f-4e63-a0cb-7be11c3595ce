package server

import (
	"mpp/pkg/utils"
	"net/url"

	"mpp/internal/taskmanager/conf"

	_ "github.com/cinience/animus/contribs/registry/nacos"
	_ "github.com/cinience/animus/contribs/registry/redis"
	"github.com/cinience/animus/registry"

	"github.com/google/wire"
)

// ProviderSet is server providers.
var ProviderSet = wire.NewSet(NewGRPCServer, NewHTTPServer, NewRegister, NewDiscovery)

func NewRegister(conf *conf.Registry) []registry.Registrar {
	if conf == nil {
		return nil
	}
	rs := make([]registry.Registrar, 0)
	u, err := url.Parse(conf.Endpoint)
	if err != nil {
		panic(err)
	}
	if u.Scheme == "virtual" {
		return nil
	} else {
		r, err := registry.GetRegistrarProvider(utils.PatchRegistryUrl(conf.Endpoint))
		if err != nil {
			panic(err)
		}
		if r == nil {
			return nil
		}
		rs = append(rs, r)
		return rs
	}

	return nil
}

func NewDiscovery(conf *conf.Registry) registry.Discovery {
	if conf == nil {
		return nil
	}
	u, err := url.Parse(conf.Endpoint)
	if err != nil {
		panic(err)
	}
	if u.Scheme == "virtual" {
		return registry.NewVirtualDiscovery()
	} else {
		r, err := registry.GetDiscoveryProvider(utils.PatchRegistryUrl(conf.Endpoint))
		if err != nil {
			panic(err)
		}

		if r == nil {
			return nil
		}

		return registry.NewFailOverDiscoveryWithMode(r, false)
	}

	return nil
}
