package server

import (
	"time"

	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/mesh"
	"mpp/internal/taskmanager/qproxy/server"
	"mpp/internal/taskmanager/service"
	"mpp/internal/taskmanager/transport/tm_http_wrapper"
	"mpp/pkg/pprof"
	"mpp/pkg/tracer/middleware/tracer"

	"github.com/cinience/animus/contribs/transport/wrapper"
	"github.com/cinience/animus/health"
	"github.com/cinience/animus/middlewares/timeout"
	"github.com/cinience/animus/registry"
	"github.com/go-kratos/kratos/v2/middleware/validate"
	"github.com/prometheus/client_golang/prometheus"
	taskmanagerv1 "proto.mpp/api/taskmanager/v1"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/http"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(c *conf.Server, taskSrv *service.TaskService, haSrv *service.UnitTaskService, pipelineSrv *service.PipelineService, meshSrv *mesh.MeshService, grayscaleSrv *service.GrayscaleService,
	logger log.Logger, discovery registry.Discovery, queueServer *server.AsynqServer, promReg prometheus.Registerer, swagger *conf.Swagger, grayConfigSrv *service.GrayConfigService) *http.Server {
	metricsSeconds := prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Namespace: "server",
		Subsystem: "requests",
		Name:      "duration_sec",
		Help:      "server requests duratio(sec).",
		Buckets:   []float64{0.005, 0.01, 0.025, 0.05, 0.1, 0.250, 0.5, 1},
	}, []string{"kind", "operation"})

	metricsRequests := prometheus.NewCounterVec(prometheus.CounterOpts{
		Namespace: "client",
		Subsystem: "requests",
		Name:      "code_total",
		Help:      "The total number of processed requests",
	}, []string{"kind", "operation", "code", "reason"})

	if promReg != nil {
		//mppMetrics.GetPromRegistry().MustRegister(metricsSeconds, metricsRequests)
		promReg.MustRegister(metricsSeconds, metricsRequests)
	}

	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			metadata.Server(),
			tracing.Server(),
			tracer.MPPServerTracer(),
			logging.Server(logger),
			//metrics.Server(
			//	metrics.WithSeconds(prom.NewHistogram(metricsSeconds)),
			//	metrics.WithRequests(prom.NewCounter(metricsRequests)),
			//),
			timeout.Server(), //自定义超时时间中间件
			validate.Validator(),
		),
	}

	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	} else {
		opts = append(opts, http.Timeout(5*time.Second))
	}
	httpServer := http.NewServer(opts...)

	// pprof API
	httpServer.HandlePrefix("/pprof", pprof.DefaultPProfService)
	// 增加默认健康检查API
	httpServer.HandlePrefix("/health", health.DefaultHealthService)
	// asynq的dashboard
	httpServer.HandlePrefix("/task/dashboard", queueServer.Dashboard("/task/dashboard"))

	taskmanagerv1.RegisterTaskHTTPServer(httpServer, taskSrv)
	taskmanagerv1.RegisterPipelineHTTPServer(httpServer, pipelineSrv)
	taskmanagerv1.RegisterMeshHTTPServer(httpServer, meshSrv)
	taskmanagerv1.RegisterGrayscaleServiceHTTPServer(httpServer, grayscaleSrv)
	r := httpServer.Route("/")

	// 兼容老的任务管理接口
	wrapper.POST(r, "/task/sync/invoke", tm_http_wrapper.WithInvokeHandlerAndError(taskSrv.TCInvokeTask))
	wrapper.POST(r, "/task/async/submit", tm_http_wrapper.WithTCHandlerAndError(taskSrv.TCSubmitTask))
	wrapper.POST(r, "/task/async/cancel", tm_http_wrapper.WithTCHandlerAndError(taskSrv.TCCancelTask))
	wrapper.POST(r, "/task/async/start", tm_http_wrapper.WithTCHandlerAndError(taskSrv.TCStartTask))
	wrapper.POST(r, "/task/async/stop", tm_http_wrapper.WithTCHandlerAndError(taskSrv.TCStopTask))
	wrapper.POST(r, "/task/async/update", tm_http_wrapper.WithTCHandlerAndError(taskSrv.TCUpdateTask))
	wrapper.POST(r, "/task/async/command", tm_http_wrapper.WithTCHandlerAndError(taskSrv.TCUpdateTask))

	wrapper.POST(r, "/task/async/query", tm_http_wrapper.WithTCHandlerAndError(taskSrv.StdQueryTask))
	//dashBoardHandler := queueServer.Dashboard("/tasker/dashboard")
	//tm_http_wrapper.Any(r, "/tasker/dashboard/*", tm_http_wrapper.WithStdServeHandler(dashBoardHandler))

	// 兼容老的tc接口
	wrapper.POST(r, "/job/async/start", tm_http_wrapper.WithCommonHandlerAndError(taskSrv.StdStartTask))
	wrapper.POST(r, "/job/async/stop", tm_http_wrapper.WithCommonHandlerAndError(taskSrv.StdStopTask))
	wrapper.POST(r, "/job/async/update", tm_http_wrapper.WithCommonHandlerAndError(taskSrv.StdUpdateTask))

	wrapper.POST(r, "/job/async/submit", tm_http_wrapper.WithCommonHandlerAndError(taskSrv.StdSubmitTask))
	wrapper.POST(r, "/job/sync/invoke", tm_http_wrapper.WithInvokeHandlerAndError(taskSrv.StdInvokeTask))
	wrapper.GETAndPOST(r, "/job/async/command", tm_http_wrapper.WithCommonHandlerAndError(taskSrv.StdCommandTask))
	wrapper.GETAndPOST(r, "/job/async/query", tm_http_wrapper.WithCommonHandlerAndError(taskSrv.StdQueryTask))
	wrapper.GETAndPOST(r, "/job/async/cancel", tm_http_wrapper.WithCommonHandlerAndError(taskSrv.StdCancelTask))
	wrapper.GETAndPOST(r, "/job/async/cancelRunning", tm_http_wrapper.WithCommonHandlerAndError(taskSrv.StdCancelTask))

	// 兼容 apigateway 提交的任务（默认带了 /api/ 的前缀）
	wrapper.POST(r, "/api/job/async/submit", tm_http_wrapper.WithVODHandlerAndError(taskSrv.StdSubmitTask))
	wrapper.POST(r, "/api/job/sync/invoke", tm_http_wrapper.WithInvokeHandlerAndError(taskSrv.StdInvokeTask))
	wrapper.GETAndPOST(r, "/api/job/async/command", tm_http_wrapper.WithVODHandlerAndError(taskSrv.StdCommandTask))
	wrapper.GETAndPOST(r, "/api/job/async/query", wrapper.WithStdHandlerAndError(taskSrv.StdQueryTask))
	wrapper.GETAndPOST(r, "/api/job/async/cancel", tm_http_wrapper.WithVODHandlerAndError(taskSrv.StdCancelTask))
	wrapper.GETAndPOST(r, "/api/job/async/cancelRunning", wrapper.WithStdHandlerAndError(taskSrv.StdCancelTask))
	wrapper.POST(r, "/api/pipeline/updateQuotaConfig", wrapper.WithStdHandlerAndError(pipelineSrv.AdapterUpdateQuotaConfigPipeline))

	// 直播系接口适配
	//wrapper.GETAndPOST(r, "/task/start", tm_http_wrapper.WithTMHandlerAndError(taskSrv.RecordHlsStartTask))
	wrapper.GETAndPOST(r, "/task/start", tm_http_wrapper.WithLiveHandlerAndError(taskSrv.LiveStartTask, tm_http_wrapper.MakeTMResult))
	wrapper.GETAndPOST(r, "/task/resume", tm_http_wrapper.WithLiveHandlerAndError(taskSrv.LiveStartTask, tm_http_wrapper.MakeTMResult))
	wrapper.GETAndPOST(r, "/task/pause", tm_http_wrapper.WithLiveHandlerAndError(taskSrv.LiveStopTask, tm_http_wrapper.MakeTMResult))
	wrapper.GETAndPOST(r, "/task/stop", tm_http_wrapper.WithLiveHandlerAndError(taskSrv.LiveStopTask, tm_http_wrapper.MakeTMResult))
	wrapper.GETAndPOST(r, "/monitor/listTask", tm_http_wrapper.WithTMHandlerAndError(taskSrv.RecordHlsListTask))
	wrapper.GETAndPOST(r, "/monitor/worker/list", tm_http_wrapper.WithTMHandlerAndError(taskSrv.RecordHlsListWorker))
	// 直播转码接口适配
	//wrapper.POST(r, "/livetranscode/start", wrapper.WithStdHandlerAndError(taskSrv.LiveTranscodeStartTask))
	//wrapper.POST(r, "/livetranscode/stop", wrapper.WithStdHandlerAndError(taskSrv.LiveTranscodeStopTask))
	//wrapper.POST(r, "/livetranscode/pause", wrapper.WithStdHandlerAndError(taskSrv.LiveTranscodeStopTask))
	wrapper.POST(r, "/task/migrate", tm_http_wrapper.WithLiveHandlerAndError(taskSrv.LiveTranscodeMigrateTask, tm_http_wrapper.MakeLiveTranscodeResult))
	wrapper.POST(r, "/task/sendCommand", tm_http_wrapper.WithLiveHandlerAndError(taskSrv.LiveTranscodeSendCommonCommand, tm_http_wrapper.MakeTMResult))
	wrapper.POST(r, "/task/update", tm_http_wrapper.WithLiveHandlerAndError(taskSrv.LiveTranscodeUpdateTask, tm_http_wrapper.MakeTMResult))
	wrapper.POST(r, "/pic/edit", tm_http_wrapper.WithLiveHandlerAndError(taskSrv.LiveTranscodeEditPic, tm_http_wrapper.MakeTMResult))
	wrapper.POST(r, "/text/edit", tm_http_wrapper.WithLiveHandlerAndError(taskSrv.LiveTranscodeEditText, tm_http_wrapper.MakeTMResult))
	wrapper.POST(r, "/stat/query", tm_http_wrapper.WithLiveHandlerAndError(taskSrv.LiveTranscodeQueryStat, tm_http_wrapper.MakeTMResult))
	wrapper.POST(r, "/task/getTasks", tm_http_wrapper.WithLiveHandlerAndError(taskSrv.LiveTranscodeGetTasks, tm_http_wrapper.MakeTMResult))
	wrapper.POST(r, "/task/getTask", tm_http_wrapper.WithLiveHandlerAndError(taskSrv.LiveTranscodeGetTask, tm_http_wrapper.MakeLiveTranscodeResult))
	wrapper.POST(r, "/task/statistics", tm_http_wrapper.WithLiveHandlerAndError(taskSrv.LiveTranscodeTaskStatistics, tm_http_wrapper.MakeLiveTranscodeResult))
	wrapper.POST(r, "/tag/list", tm_http_wrapper.WithLiveHandlerAndError(taskSrv.LiveTranscodeListTag, tm_http_wrapper.MakeLiveTranscodeResult))
	wrapper.POST(r, "/inner/updateGlobalConfig", tm_http_wrapper.WithLiveHandlerAndError(taskSrv.UpdateGlobalConfig, tm_http_wrapper.MakeTMResult))
	wrapper.GETAndPOST(r, "/debug/getSyncData", tm_http_wrapper.WithLiveHandlerAndError(taskSrv.LiveTranscodeGetSyncData, tm_http_wrapper.MakeTMResult))
	wrapper.POST(r, "/stream/queryTasksWithSourceStream", tm_http_wrapper.WithLiveHandlerAndError(taskSrv.QueryTasksWithSourceStream, tm_http_wrapper.MakeTMResult))

	wrapper.POST(r, "/ha/getTask", tm_http_wrapper.WithLiveHandlerAndError(haSrv.GetTask, tm_http_wrapper.MakeLiveTranscodeResult))
	wrapper.POST(r, "/ha/getTasks", tm_http_wrapper.WithLiveHandlerAndError(haSrv.GetTasks, tm_http_wrapper.MakeLiveTranscodeResult))
	wrapper.POST(r, "/ha/updateTask", tm_http_wrapper.WithLiveHandlerAndError(haSrv.UpdateTask, tm_http_wrapper.MakeLiveTranscodeResult))
	wrapper.POST(r, "/ha/setHAStatus", tm_http_wrapper.WithLiveHandlerAndError(haSrv.SetHAStatus, tm_http_wrapper.MakeLiveTranscodeResult))
	wrapper.POST(r, "/ha/syncAllTasks", tm_http_wrapper.WithLiveHandlerAndError(haSrv.SyncAllTasks, tm_http_wrapper.MakeLiveTranscodeResult))

	//mediaai灰度测试接口
	//wrapper.POST(r, "/api/mediaai/job/async/submit", wrapper.WithStdHandlerAndError(taskSrv.AIStdSubmitTask))

	wrapper.POST(r, "/pipeline/create", wrapper.WithStdHandlerAndError(pipelineSrv.AdapterCreatePipeline))
	wrapper.POST(r, "/pipeline/activate", wrapper.WithStdHandlerAndError(pipelineSrv.AdapterActivatePipeline))
	wrapper.POST(r, "/pipeline/pause", wrapper.WithStdHandlerAndError(pipelineSrv.AdapterPausePipeline))
	wrapper.POST(r, "/pipeline/updateQuotaConfig", wrapper.WithStdHandlerAndError(pipelineSrv.AdapterUpdateQuotaConfigPipeline))

	//pop接口兼容
	wrapper.POST(r, "/v1/task/pop/start", tm_http_wrapper.WithPOPHandlerAndError(taskSrv.POPStartTask))
	wrapper.POST(r, "/v1/task/pop/stop", tm_http_wrapper.WithPOPHandlerAndError(taskSrv.POPStopTask))

	//并行转码兼容
	wrapper.POST(r, "/job/start", wrapper.WithStdHandlerAndError(taskSrv.StdStartTask))
	wrapper.POST(r, "/job/stop", wrapper.WithStdHandlerAndError(taskSrv.StdStopTask))
	wrapper.POST(r, "/job/update", wrapper.WithStdHandlerAndError(taskSrv.StdUpdateTask))
	wrapper.GETAndPOST(r, "/job/command", tm_http_wrapper.WithVODHandlerAndError(taskSrv.StdCommandTask))
	wrapper.GETAndPOST(r, "/job/query", wrapper.WithStdHandlerAndError(taskSrv.StdQueryTask))
	// 内部管理接口
	wrapper.GETAndPOST(r, "/animus/discovery", wrapper.WithStdHandler(discovery.Overhaul))

	//直播转码切流运维接口
	wrapper.GETAndPOST(r, "/grayconfig/add", wrapper.WithStdHandlerAndError(grayConfigSrv.Add))
	wrapper.GETAndPOST(r, "/grayconfig/delete", wrapper.WithStdHandlerAndError(grayConfigSrv.Delete))
	wrapper.GETAndPOST(r, "/grayconfig/query", wrapper.WithStdHandlerAndError(grayConfigSrv.Query))
	wrapper.GETAndPOST(r, "/grayconfig/update", wrapper.WithStdHandlerAndError(grayConfigSrv.Update))
	// 还未实现的接口
	/*
		     /job/async/query
		     /job/async/cancel
			 /job/async/cancelRunning
			 /job/async/command
			 /job/async/submit
			      等等运维接口，钦心 先实现
			 /ops/job/async/dispatchStart
	*/
	// "job/async/query"

	return httpServer
}
