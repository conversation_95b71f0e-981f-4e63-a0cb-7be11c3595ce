package repository

import (
	"context"
	"mpp/internal/taskmanager/models/domain"
)

type KVStoreRepo interface {
	Set(context.Context, *domain.KVStore) error
	Get(context.Context, *domain.KVStore) (string, error)
	Delete(context.Context, *domain.KVStore)
	ListAll(context.Context) ([]*domain.KVStore, error)
	Cas(context.Context, *domain.KVStore, *domain.KVStore) (bool, error)
	List(ctx context.Context, repo string, key string) ([]*domain.KVStore, error)
	ListLike(ctx context.Context, repo string, key string) ([]*domain.KVStore, error)
}
