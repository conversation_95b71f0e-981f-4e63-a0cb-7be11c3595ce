package repository

import (
	"context"
	"mpp/internal/taskmanager/models"
)

type TaskSentinelConfigRepo interface {
	// Create
	Create(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) error
	// Delete
	Delete(ctx context.Context, sentinelConfigId string) error
	// 创建或更新流控sentinelConfig
	InsertUpdateOnDuplicate(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) error
	// Get
	GetTaskSentinelConfig(ctx context.Context, sentinelConfigId string) (*models.TaskSentinelConfig, error)
	// list状态为active的流控sentinelConfig
	ListActiveTaskSentinelConfig(ctx context.Context) ([]*models.TaskSentinelConfig, error)
	// Update
	UpdateTaskSentinelConfig(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) (bool, error)
	UpdateSentinelStatus(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) (bool, error)
}
