package repository

import (
	"context"
	"mpp/internal/taskmanager/models"
)

type TaskRepo interface {
	CloneWithSpecifiedTable(string) TaskRepo

	InsertUpdateOnDuplicate(context.Context, *models.Task) error

	InsertIgnoreDuplicate(ctx context.Context, task *models.Task) (bool, error)

	UpdateScheduleWorker(context.Context, *models.Task) error
	UpdateInternalStatus(context.Context, *models.Task) error
	UpdateScheduleWorkerAndInternalStatus(context.Context, *models.Task) error
	UpdateBizStatus(context.Context, *models.Task) error
	UpdateResult(context.Context, *models.Task) error
	//同时更新result、biz_status、internal_status
	UpdateResultAndAllStatus(context.Context, *models.Task) error
	UpdateAbnormalStopped(context.Context, *models.Task) error
	UpdateDagGroup(ctx context.Context, task *models.Task) error

	UpdateEngineParam(context.Context, *models.Task) error
	UpdateMetadata(context.Context, *models.Task) error
	UpdateInternalAndBizStatus(context.Context, *models.Task) error
	UpdateEngineParamAndBizStatus(context.Context, *models.Task) error
	ClearResultAndAddMigrateTimes(context.Context, *models.Task) error
	IncreaseMigrateTimesAndClearResult(ctx context.Context, taskId string) error

	// FindFirstID 查找最早的ID tasker
	FindFirstID(context.Context) (*models.Task, error)
	FindByID(context.Context, string) (*models.Task, error)
	FindByJobId(context.Context, string) ([]*models.Task, error)
	FindByIDList(ctx context.Context, taskIds []string) ([]*models.Task, error)
	FindRunning(context.Context, []string) ([]*models.Task, error)
	ListAllBizRunningTasks(ctx context.Context) ([]*models.Task, error)
	//todo 查询运行中的非dag任务
	ListBizRunningTasksExceptDagJob(ctx context.Context) ([]*models.Task, error)
	//根据任务信息list所有任务
	ListTasks(ctx context.Context, taskInfo *models.Task) ([]*models.Task, error)
	ListTasksOptimized(ctx context.Context, taskInfo *models.Task) ([]*models.Task, error)
	ListBizRunningTasks(ctx context.Context, taskInfo *models.Task) ([]*models.Task, error)
	ListRunningTaskByWorkerId(ctx context.Context, workerId string) ([]*models.Task, error)
	ListRunningTaskByWorkerIp(ctx context.Context, workerIp string) ([]*models.Task, error)
	ListMissingRunningTasks(ctx context.Context) ([]*models.Task, error)
	ListUnexpectedRunningTasks(ctx context.Context) ([]*models.Task, error)

	//暂不使用，batch操作会偶发性的报create错误:Incorrect datetime value: '0000-00-00' for column 'gmt_create
	//BatchUpdateAbnormalStopped(ctx context.Context, tasks []*models.Task) error
	//批量更新 重新实现
	BatchUpdateAbnormalStopped(ctx context.Context, taskIds []string, taskResult *models.TaskResult) error
	//测试中，暂未使用
	ListBizTasksWithOptionalFilters(ctx context.Context, taskInfo *models.Task) ([]*models.Task, error)
	//查询运行中任务数量，按userId、pipelineId，taskType和三元组条件过滤分组
	CountAggregatedTasks(ctx context.Context) ([]*models.TempQueryTask, error)
}
