package repository

import (
	"context"
	"mpp/internal/taskmanager/models/domain"
)

type UnitTaskRepo interface {
	GetUnitTask(context.Context, string) (*domain.UnitTask, error)
	GetUnitTaskAll(context.Context, string) ([]*domain.UnitTask, error)
	InsertUnitTask(context.Context, *domain.UnitTask) error
	UpdateUnitTaskStatus(context.Context, string) error
	SyncTask(context.Context, *domain.Task) error
	UpdateTaskStatus(context.Context, string, int8, int8) error
}
