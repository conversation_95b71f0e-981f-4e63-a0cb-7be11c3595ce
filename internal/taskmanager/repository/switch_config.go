package repository

import (
	"context"
	"time"
)

type SwitchConfig struct {
	Id           int64
	GmtCreate    time.Time
	GmtModified  time.Time
	Product      string
	EngineModel  string
	Tag          string
	Domain       string
	App          string
	Token        string
	SwitchConfig string
	BackupConfig string
	Extend       string
	Status       int8
}

type SwitchConfigRepo interface {
	Insert(context.Context, *SwitchConfig) error
	Query(context.Context, *SwitchConfig) ([]*SwitchConfig, error)
	Delete(context.Context, *SwitchConfig)
	ListAll(context.Context) ([]*SwitchConfig, error)
}
