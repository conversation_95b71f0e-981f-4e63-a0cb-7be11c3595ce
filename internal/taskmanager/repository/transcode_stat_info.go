package repository

import (
	"context"
	"mpp/internal/taskmanager/models/domain"
	"time"
)

type TranscodeStatInfoRepo interface {
	Create(ctx context.Context, transcodeStatInfo *domain.TranscodeStatInfo) error
	GetLatest(ctx context.Context, tag string) (*domain.TranscodeStatInfo, error)
	Query(ctx context.Context, tag, transcodeDomain string, statTime time.Time) ([]*domain.TranscodeStatInfo, error)
}
