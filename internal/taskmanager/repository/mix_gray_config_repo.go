package repository

import (
	"context"
	"mpp/internal/taskmanager/models/domain"
)

/**
 * @Author: qinxin
 * @Date: 2024/12/24 20:43
 * @Desc: 混跑规则repo
 * @Version 1.0
 */

type MixGrayConfigRepo interface {
	Create(ctx context.Context, mixGrayConfig *domain.MixGrayConfig) error
	Delete(ctx context.Context, ruleName string) error
	Update(ctx context.Context, mixGrayConfig *domain.MixGrayConfig) error
	UpdateMixRatio(ctx context.Context, mixGrayConfig *domain.MixGrayConfig) error
	InsertUpdateOnDuplicate(ctx context.Context, mixGrayConfig *domain.MixGrayConfig) error
	GetMixGrayConfigByName(ctx context.Context, ruleName string) (*domain.MixGrayConfig, error)
	ListActiveMixGrayConfig(ctx context.Context) ([]*domain.MixGrayConfig, error)
	List(ctx context.Context) ([]*domain.MixGrayConfig, error)
}
