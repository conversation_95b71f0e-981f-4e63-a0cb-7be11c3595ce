package repository

import (
	"context"
	"mpp/internal/taskmanager/models"
	v1 "proto.mpp/api/common/v1"
)

type AsyncTaskPipelineRepo interface {
	Create(ctx context.Context, pipeline *models.Pipeline) (bool, error)
	Delete(ctx context.Context, pipelineId string, pipelineType v1.PipelineType) (bool, error)
	Update(ctx context.Context, pipeline *models.Pipeline) (bool, error)
	FindByPipelineAndType(ctx context.Context, pipelineId string, pipelineType v1.PipelineType) (*models.Pipeline, error)
	QueryByUserId(ctx context.Context, userId string) ([]*models.Pipeline, error)

	UpdatePipelineStatus(ctx context.Context, pipeline *models.Pipeline) (bool, error)
	UpdatePipelineScheduleLevel(ctx context.Context, pipeline *models.Pipeline) (bool, error)
	UpdatePipelineScheduleLevelByUserId(ctx context.Context, pipeline *models.Pipeline) (bool, error)
}
