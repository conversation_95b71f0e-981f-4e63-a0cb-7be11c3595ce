package repository

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/hibiken/asynq"
	"github.com/redis/go-redis/v9"
	"mpp/internal/taskmanager/conf"
)

type RedisClient struct {
	client redis.UniversalClient
}

func NewRedisClient(conf *conf.Bootstrap) *RedisClient {
	r := &RedisClient{}
	uri := conf.GetData().Redis.Endpoint
	redisConnOpt, err := asynq.ParseRedisURI(uri)
	if err != nil {
		log.Errorf("redis init error: %v", err)
		panic(err)
	}
	r.client = redisConnOpt.MakeRedisClient().(redis.UniversalClient)
	return r
}

func (r *RedisClient) GetClient() redis.UniversalClient {
	return r.client
}
