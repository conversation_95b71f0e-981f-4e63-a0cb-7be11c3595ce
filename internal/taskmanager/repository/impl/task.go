package impl

import (
	"context"
	"encoding/json"
	"errors"
	"sync"
	"time"

	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/domain"
	"mpp/internal/taskmanager/repository"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
	"gorm.io/hints"
)

type RequestResource struct {
	Quota    map[string]int64
	Affinity TaskAffinity
}

type TaskAffinity struct {
	Required  []Affinity
	Preferred []Affinity
}

type Affinity struct {
	Key      string
	Operator string
	Values   []string
}

type TaskRepo struct {
	data      *repository.Data
	log       *log.Helper
	tableName string
	db        *gorm.DB
}

type TableNameContext struct{}

// NewTaskRepo .
func NewTaskRepo(data *repository.Data, logger log.Logger) repository.TaskRepo {
	return &TaskRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (t *TaskRepo) CloneWithSpecifiedTable(tableName string) repository.TaskRepo {
	newRepo := &TaskRepo{
		data:      t.data,
		log:       t.log,
		tableName: tableName,
	}
	newRepo.db = newRepo.getTX(context.Background())
	return newRepo
}

func (t *TaskRepo) WithTableName(ctx context.Context, tableName string) context.Context {
	return context.WithValue(ctx, TableNameContext{}, tableName)
}

func (t *TaskRepo) getTX(ctx context.Context) *gorm.DB {
	// t.data.GetDB().WithContext(ctx).Model(&domain.Task{})
	//if tableName, ok := ctx.Value(TableNameContext{}).(string); ok && tableName != "" {
	//	return t.data.GetDB().WithContext(ctx).Table(tableName)
	//} else {
	//	//panic("table name is empty")
	if t.db == nil || t.db != t.data.GetDB() {
		t.db = t.data.GetDB().Model(&domain.Task{}).Table(t.tableName)
	}

	return t.db.WithContext(ctx)
	//}
}

func (t *TaskRepo) InsertIgnoreDuplicate(ctx context.Context, task *models.Task) (bool, error) {
	dbTask, err := toTaskModel(task)
	if err != nil {
		return false, err
	}

	result := t.getTX(ctx).Exec(
		"INSERT ignore INTO "+t.tableName+" (gmt_create, gmt_modified, job_id, task_id, pipeline_id, task_type, biz_status, internal_status, product, engine_model, tag, "+
			"request_resource, user_data, trace, notify, engine_param, migrate_config, dependency, migrate_times, metadata, user_id, schedule_param,domain)"+
			"VALUES(now(), now(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, ? ,? ,? ,?) ",
		dbTask.JobId, dbTask.TaskId, dbTask.PipelineId, dbTask.TaskType, dbTask.BizStatus,
		dbTask.InternalStatus, dbTask.Product, dbTask.EngineModel, dbTask.Tag, dbTask.RequestResource,
		dbTask.UserData, dbTask.Trace, dbTask.Notify, dbTask.EngineParam, dbTask.MigrateConfig, dbTask.Dependency,
		dbTask.Metadata, dbTask.UserId, dbTask.ScheduleParam, dbTask.Domain,
	)

	if result.Error != nil {
		log.Errorf("taskRepo InsertUpdateOnDuplicate failed, taskId:%v, err:%v", task.TaskId, result.Error)
		return false, result.Error
	}

	return result.RowsAffected > 0, nil
}

func (t *TaskRepo) InsertUpdateOnDuplicate(ctx context.Context, task *models.Task) error {
	dbTask, err := toTaskModel(task)
	if err != nil {
		return err
	}

	var result = t.getTX(ctx).Exec(
		"INSERT INTO "+t.tableName+" (gmt_create, gmt_modified, job_id, task_id, pipeline_id, task_type, biz_status, internal_status, product, engine_model, tag, "+
			"request_resource, user_data, trace, notify, engine_param, migrate_config, dependency, migrate_times, metadata, user_id, schedule_param,domain)"+
			"VALUES(now(), now(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, ?, ? , ?) "+
			"ON DUPLICATE KEY UPDATE product = ?, engine_model = ?, tag = ?, biz_status = ?, request_resource = ?, user_data = ?, user_id = ?, schedule_param = ?, "+
			"trace = ?, notify = ?, engine_param = ?, migrate_config = ?, metadata = ?, dependency = ?, "+
			"result = NULL, worker_id = NULL, worker_ip = NULL, worker_port = NULL, migrate_times = 0, gmt_modified = now()",
		dbTask.JobId, dbTask.TaskId, dbTask.PipelineId, dbTask.TaskType, dbTask.BizStatus,
		dbTask.InternalStatus, dbTask.Product, dbTask.EngineModel, dbTask.Tag, dbTask.RequestResource,
		dbTask.UserData, dbTask.Trace, dbTask.Notify, dbTask.EngineParam, dbTask.MigrateConfig, dbTask.Dependency, dbTask.Metadata, dbTask.UserId, dbTask.ScheduleParam, dbTask.Domain,
		dbTask.Product, dbTask.EngineModel, dbTask.Tag, dbTask.BizStatus, dbTask.RequestResource, dbTask.UserData, dbTask.UserId, dbTask.ScheduleParam,
		dbTask.Trace, dbTask.Notify, dbTask.EngineParam, dbTask.MigrateConfig, dbTask.Metadata, dbTask.Dependency,
	)
	if result.Error != nil {
		log.Errorf("taskRepo InsertUpdateOnDuplicate failed, taskId:%v, err:%v", task.TaskId, result.Error)
		return result.Error
	}

	return nil
}

func (t *TaskRepo) UpdateScheduleWorker(ctx context.Context, task *models.Task) error {
	data := map[string]interface{}{
		"WorkerId":    task.Worker.Id,
		"WorkerIp":    task.Worker.Ip,
		"WorkerPort":  task.Worker.Port,
		"GmtModified": time.Now(),
	}

	result := t.getTX(ctx).Where("task_id = ?", task.TaskId).Updates(data)
	if result.Error != nil {
		return result.Error
	}

	return nil
}

func (t *TaskRepo) UpdateInternalStatus(ctx context.Context, task *models.Task) error {
	data := map[string]interface{}{
		"InternalStatus": int32(task.InternalStatus),
		"GmtModified":    time.Now(),
	}

	result := t.getTX(ctx).Where("task_id = ? ", task.TaskId).Updates(data)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (t *TaskRepo) UpdateScheduleWorkerAndInternalStatus(ctx context.Context, task *models.Task) error {
	//log.Debugf("taskRepo UpdateScheduleWorker called.tasker:%v", tasker)
	data := map[string]interface{}{
		"WorkerId":       task.Worker.Id,
		"WorkerIp":       task.Worker.Ip,
		"WorkerPort":     task.Worker.Port,
		"InternalStatus": int32(task.InternalStatus),
		"GmtModified":    time.Now(),
	}

	result := t.getTX(ctx).Where("task_id = ?", task.TaskId).Updates(data)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (t *TaskRepo) UpdateBizStatus(ctx context.Context, task *models.Task) error {
	//log.Debugf("taskRepo UpdateInternalStatus called.tasker:%v", tasker)

	data := map[string]interface{}{
		"BizStatus":   int32(task.BizStatus),
		"GmtModified": time.Now(),
	}

	result := t.getTX(ctx).Where("task_id = ? ", task.TaskId).Updates(data)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (t *TaskRepo) UpdateResult(ctx context.Context, task *models.Task) error {
	//log.Debugf("taskRepo UpdateInternalStatus called.tasker:%v", tasker)
	resultJSON, err := json.Marshal(task.Result)
	if err != nil {
		return err
	}

	data := map[string]interface{}{
		"Result":      resultJSON,
		"GmtModified": time.Now(),
		"BizStatus":   int32(models.TaskStateStopped),
	}

	result := t.getTX(ctx).Where("task_id = ? ", task.TaskId).Updates(data)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (t *TaskRepo) UpdateResultAndAllStatus(ctx context.Context, task *models.Task) error {
	//log.Debugf("taskRepo UpdateInternalStatus called.tasker:%v", tasker)

	resultJSON, err := json.Marshal(task.Result)
	if err != nil {
		return err
	}

	data := map[string]interface{}{
		"BizStatus":      int32(task.BizStatus),
		"InternalStatus": int32(task.InternalStatus),
		"Result":         resultJSON,
		"GmtModified":    time.Now(),
	}

	result := t.getTX(ctx).Where("task_id = ? ", task.TaskId).Updates(data)
	if result.Error != nil {
		return result.Error
	}

	return nil
}

func (t *TaskRepo) UpdateAbnormalStopped(ctx context.Context, task *models.Task) error {
	resultJSON, err := json.Marshal(task.Result)
	if err != nil {
		return err
	}

	data := map[string]interface{}{
		"Result":         resultJSON,
		"GmtModified":    time.Now(),
		"InternalStatus": int32(models.TaskStateStopped),
	}

	result := t.getTX(ctx).Clauses(hints.UseIndex("idx_task_id")).Where("task_id = ? and biz_status = ?", task.TaskId, models.TaskStateActive).Updates(data)
	if result.Error != nil {
		return result.Error
	}
	return nil

}

func (t *TaskRepo) UpdateDagGroup(ctx context.Context, task *models.Task) error {
	resultJSON, err := json.Marshal(task.DagGraph)
	if err != nil {
		return err
	}

	data := map[string]interface{}{
		"DagGraph":    resultJSON,
		"GmtModified": time.Now(),
	}

	result := t.getTX(ctx).Where("task_id = ? ", task.TaskId).Updates(data)
	if result.Error != nil {
		return result.Error
	}
	return nil
	return nil

}

//
//func (t *taskRepo) BatchUpdateAbnormalStopped(ctx context.Context, tasks []*models.Task) error {
//	//增加batch的数据库操作
//	timeNow := time.Now()
//	for i := range tasks {
//		tasks[i].InternalStatus = models.TaskStateStopped
//		tasks[i].LastModified = timeNow
//	}
//	//
//	dbTaskList, _ := toTaskModelList(tasks)
//	//批处理上限，可配置
//	batchSize := 100
//	for i := 0; i < len(dbTaskList); i += batchSize {
//		end := i + batchSize
//		if end > len(dbTaskList) {
//			end = len(dbTaskList)
//		}
//
//		ids := make([]string, 0, batchSize)
//		for _, dbTaskList := range dbTaskList[i:end] {
//			ids = append(ids, dbTaskList.TaskId)
//		}
//
//		batchTask := make([]*domain.Task, len(dbTaskList[i:end]))
//		copy(batchTask, dbTaskList[i:end])
//
//		//采用create冲突处理方法，创建任务时发现task_id有冲突，后续执行更新操作，实现批量更新
//		//TODO 采用create操作是否会有隐患？
//		clauseRst := t.getTX(ctx).Clauses(clause.OnConflict{
//			Columns:   []clause.Column{{Name: "task_id"}},
//			DoUpdates: clause.AssignmentColumns([]string{"result", "gmt_modified", "internal_status"}),
//		}).Create(&batchTask)
//
//		if clauseRst.Error != nil {
//			log.Errorf("taskRepo BatchUpdateAbnormalStopped failed, taskIds:[%v], table:[%s] err:%v", ids, t.tableName, clauseRst.Error)
//			return clauseRst.Error
//		}
//	}
//	return nil
//}

func (t *TaskRepo) BatchUpdateAbnormalStopped(ctx context.Context, taskIds []string, taskResult *models.TaskResult) error {
	resultJSON, err := json.Marshal(taskResult)
	if err != nil {
		return err
	}
	data := map[string]interface{}{
		"Result":         resultJSON,
		"GmtModified":    time.Now(),
		"InternalStatus": int32(models.TaskStateStopped),
	}

	result := t.getTX(ctx).Clauses(hints.UseIndex("idx_task_id")).Where("task_id in ? and biz_status = ?", taskIds, models.TaskStateActive).Updates(data)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (t *TaskRepo) UpdateEngineParam(ctx context.Context, task *models.Task) error {
	paramJson, err := json.Marshal(task.Param)
	if err != nil {
		return err
	}

	//data := map[string]interface{}{
	//	"EngineParam": paramJson,
	//	"GmtModified": time.Now(),
	//}
	//result := t.getTX(ctx).Where("task_id = ? ", tasker.TaskId).Updates(data)

	data := map[string]interface{}{
		"EngineParam": paramJson,
		"GmtModified": time.Now(),
	}

	result := t.getTX(ctx).Where("task_id = ? ", task.TaskId).Updates(data)
	if result.Error != nil {
		if !errors.Is(result.Error, context.Canceled) {
			log.Errorf("taskRepo UpdateEngineParam failed, taskId:[%s], table:[%s], err:%v", task.TaskId, t.tableName, result.Error)
		}

		return result.Error
	}
	return nil
}

func (t *TaskRepo) UpdateMetadata(ctx context.Context, task *models.Task) error {
	metadataJson, err := json.Marshal(task.Metadata)
	if err != nil {
		log.Errorf("taskRepo UpdateMetadata failed when parse json, taskId:[%s], table:[%s], err:%v", task.TaskId, t.tableName, err)
		return err
	}

	data := map[string]interface{}{
		"Metadata":    metadataJson,
		"GmtModified": time.Now(),
	}

	result := t.getTX(ctx).Where("task_id = ? ", task.TaskId).Updates(data)
	if result.Error != nil {
		if !errors.Is(result.Error, context.Canceled) {
			log.Errorf("taskRepo metadata failed, taskId:[%s], table:[%s], err:%v", task.TaskId, t.tableName, result.Error)
		}

		return result.Error
	}
	return nil
}

func (t *TaskRepo) UpdateInternalAndBizStatus(ctx context.Context, task *models.Task) error {
	paramJson, err := json.Marshal(task.Param)
	if err != nil {
		return err
	}

	data := map[string]interface{}{
		"EngineParam":    paramJson,
		"BizStatus":      task.BizStatus,
		"InternalStatus": task.InternalStatus,
		"GmtModified":    time.Now(),
	}
	//data := domain.Task{
	//	EngineParam: paramJson,
	//	BizStatus:   int32(tasker.BizStatus),
	//	GmtModified: time.Now(),
	//}
	result := t.getTX(ctx).Where("task_id = ? ", task.TaskId).Updates(data)
	if result.Error != nil {
		if !errors.Is(result.Error, context.Canceled) {
			log.Errorf("taskRepo UpdateInternalAndBizStatus failed, taskId:[%s], tabel:[%s] err:%v", task.TaskId, t.tableName, result.Error)
		}
		return result.Error
	}

	return nil
}

func (t *TaskRepo) UpdateEngineParamAndBizStatus(ctx context.Context, task *models.Task) error {
	paramJson, err := json.Marshal(task.Param)
	if err != nil {
		return err
	}

	data := map[string]interface{}{
		"EngineParam": paramJson,
		"BizStatus":   task.BizStatus,
		"GmtModified": time.Now(),
	}
	//data := domain.Task{
	//	EngineParam: paramJson,
	//	BizStatus:   int32(tasker.BizStatus),
	//	GmtModified: time.Now(),
	//}
	result := t.getTX(ctx).Where("task_id = ? ", task.TaskId).Updates(data)
	if result.Error != nil {
		if !errors.Is(result.Error, context.Canceled) {
			log.Errorf("taskRepo UpdateEngineParam failed, taskId:[%s], tabel:[%s] err:%v", task.TaskId, t.tableName, result.Error)
		}

		return result.Error
	}

	return nil
}

func (t *TaskRepo) ClearResultAndAddMigrateTimes(ctx context.Context, task *models.Task) error {
	resultJson, err := json.Marshal(task.Result)
	if err != nil {
		return err
	}

	//var data = domain.Task{
	//	MigrateTimes: tasker.MigrateTimes,
	//	Result:       resultJson,
	//	GmtModified:  time.Now(),
	//}
	data := map[string]interface{}{
		"MigrateTimes": task.MigrateTimes,
		"Result":       resultJson,
		"GmtModified":  time.Now(),
	}

	result := t.getTX(ctx).Clauses(hints.UseIndex("idx_task_id")).Where("task_id = ? and biz_status = ?",
		task.TaskId, models.TaskStateActive).Updates(data)
	if result.Error != nil {
		return result.Error
	}

	return nil
}

// IncreaseMigrateTimesAndClearResult 原子增加迁移次数并清空 Result 字段
func (t *TaskRepo) IncreaseMigrateTimesAndClearResult(ctx context.Context, taskId string) error {
	query := "UPDATE " + t.tableName + " SET " +
		"migrate_times = migrate_times + 1, " +
		"result = NULL, " +
		"gmt_modified = NOW() " +
		"WHERE task_id = ? AND biz_status = ?"

	result := t.getTX(ctx).Clauses(hints.UseIndex("idx_task_id")).Exec(query, taskId, models.TaskStateActive)

	if result.Error != nil {
		log.Errorf("IncreaseMigrateTimesAndClearResult failed for taskId: %s, error: %v", taskId, result.Error)
		return result.Error
	}

	if result.RowsAffected == 0 {
		log.Warnf("No rows affected for taskId: %s. Task may not exist or is not active.", taskId)
		return common.ErrTaskNotRunning // 或者自定义错误
	}

	return nil
}

func (t *TaskRepo) FindFirstID(ctx context.Context) (*models.Task, error) {
	var dbTask domain.Task
	result := t.getTX(ctx).Order("id").First(&dbTask)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, common.ErrTaskNotFoundError
		}

		if !errors.Is(result.Error, context.Canceled) {
			log.Errorf("taskRepo FindFirstID failed in table:[%s], err:%v", t.tableName, result.Error)
		}

		return nil, result.Error
	}

	return toTask(&dbTask)
}

func (t *TaskRepo) FindByID(ctx context.Context, taskId string) (*models.Task, error) {
	var dbTask domain.Task
	result := t.getTX(ctx).Where("task_id = ?", taskId).Take(&dbTask)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			log.Errorf("taskRepo FindByID fail in ErrRecordNotFound. taskId:[%s] in table:[%s], err:%v", taskId, t.tableName, result.Error)
			return nil, common.ErrTaskNotFoundError
		}

		if !errors.Is(result.Error, context.Canceled) {
			log.Errorf("taskRepo FindByID failed. taskId:[%s] in table:[%s], err:%v", taskId, t.tableName, result.Error)
		}
		return nil, result.Error
	}

	return toTask(&dbTask)
}

func (t *TaskRepo) FindByIDList(ctx context.Context, taskIds []string) ([]*models.Task, error) {
	//分批处理避免慢sql
	batchSize := 50
	var allTasks []*models.Task
	for i := 0; i < len(taskIds); i += batchSize {
		end := i + batchSize
		if end > len(taskIds) {
			end = len(taskIds)
		}
		batchTaskIds := taskIds[i:end]
		var tasks []domain.Task
		result := t.getTX(ctx).Where("task_id in ?", batchTaskIds).Find(&tasks)
		if result.Error != nil {
			return nil, result.Error
		}

		tempTasks, err := toTaskList(tasks)
		if err != nil {
			log.Errorf("FindByIDList toTaskList failed. table:[%s], tasks:%+v, err:%v", t.tableName, tasks, err)
			return nil, err

		}
		allTasks = append(allTasks, tempTasks...)
	}
	//var tasks []Task
	//result := t.data.GetDB().WithContext(ctx).Where("task_id in ?", taskIds).Find(&tasks)
	//if result.Error != nil {
	//	return nil, result.Error
	//}
	//
	//return toTaskList(tasks)
	return allTasks, nil
}

func (t *TaskRepo) FindRunning(ctx context.Context, engineModels []string) ([]*models.Task, error) {
	var tasks []domain.Task
	result := t.getTX(ctx).Where("internal_status = ? And engine_model in ?", 1, engineModels).Find(&tasks)
	if result.Error != nil {
		return nil, result.Error
	}
	return toTaskList(tasks)
}

func (t *TaskRepo) FindByJobId(ctx context.Context, jobId string) ([]*models.Task, error) {
	var tasks []domain.Task
	result := t.getTX(ctx).Where("job_id = ?", jobId).Find(&tasks)
	if result.Error != nil {
		return nil, result.Error
	}

	return toTaskList(tasks)
}

func (t *TaskRepo) ListAllBizRunningTasks(ctx context.Context) ([]*models.Task, error) {
	var tasks []domain.Task
	result := t.getTX(ctx).Where("biz_status = ?", models.TaskStateActive).Find(&tasks)

	if result.Error != nil {
		return nil, result.Error
	}

	return toTaskList(tasks)
}

func (t *TaskRepo) ListBizRunningTasksExceptDagJob(ctx context.Context) ([]*models.Task, error) {
	var tasks []domain.Task
	result := t.getTX(ctx).Where("biz_status = ? AND task_type <> ?", models.TaskStateActive, models.DagAsyncTaskType).Find(&tasks)

	if result.Error != nil {
		return nil, result.Error
	}

	return toTaskList(tasks)
}

// ListTasks 保留原非分页查询方法，保证兼容
func (t *TaskRepo) ListTasks(ctx context.Context, taskInfo *models.Task) ([]*models.Task, error) {
	var tasks []domain.Task
	if taskInfo.BizStatus == models.TaskStateStopped && taskInfo.InternalStatus == models.TaskStateStopped ||
		(taskInfo.BizStatus == models.TaskStateNone && taskInfo.InternalStatus == models.TaskStateNone) {
		return nil, errors.New("taskInfo.BizStatus and taskInfo.InternalStatus can not be both stopped or both none")
	}
	dbTask, err := toTaskInfoModel(taskInfo)
	if err != nil {
		log.Errorf("ListTasks fail in Invalid biz.tasker, biz.tasker:%v", taskInfo)
		return nil, err
	}
	result := t.getTX(ctx).Where(dbTask).Find(&tasks)
	if result.Error != nil {
		log.Errorf("ListTasks failed. table:[%s], taskInfo:%+v, err:%v", t.tableName, taskInfo, result.Error)
		return nil, result.Error
	}

	return toTaskList(tasks)
}

func (t *TaskRepo) ListTasksOptimized(ctx context.Context, taskInfo *models.Task) ([]*models.Task, error) {
	if taskInfo.BizStatus == models.TaskStateStopped && taskInfo.InternalStatus == models.TaskStateStopped ||
		(taskInfo.BizStatus == models.TaskStateNone && taskInfo.InternalStatus == models.TaskStateNone) {
		return nil, errors.New("taskInfo.BizStatus and taskInfo.InternalStatus can not be both stopped or both none")
	}

	var statusTasks []domain.SimpleTaskWithStatus
	g := t.getTX(ctx).Where("biz_status = ? AND product = ? AND engine_model = ?", taskInfo.BizStatus, taskInfo.Product, taskInfo.EngineModel)
	if taskInfo.InternalStatus == models.TaskStateActive {
		g = g.Where("internal_status = ?", taskInfo.InternalStatus)
	}
	g.Find(&statusTasks)
	return toTaskListWithStatus(statusTasks)

	//优化查询
	//offset := 0
	//if opt.BatchSize <= 0 {
	//	opt.BatchSize = 5000
	//}
	//var allTasks []*models.Task
	//dbTask, err := toTaskInfoModel(taskInfo)
	//lastId := uint64(0) // 游标分页的游标
	//if err != nil {
	//	log.Errorf("ListTasks fail in Invalid biz.tasker, biz.tasker:%v", taskInfo)
	//	return nil, err
	//}
	//for {
	//	var tasks []domain.Task
	//	var result *gorm.DB
	//	if opt.EnableCursorPagination {
	//		//游标
	//		result = t.getTX(ctx).Clauses(hints.UseIndex("PRIMARY")).Where("id > ?", lastId).Where(dbTask).Order("id ASC").Limit(opt.BatchSize).Find(&tasks)
	//	} else {
	//		//offset
	//		result = t.getTX(ctx).Where(dbTask).Offset(offset).Limit(opt.BatchSize).Find(&tasks)
	//	}
	//	if result.Error != nil {
	//		t.log.WithContext(ctx).Errorf("ListTaskOptimized failed, err:%v", result.Error)
	//		return nil, result.Error
	//	}
	//	if len(tasks) == 0 {
	//		break // 没有更多数据了
	//	}
	//	// 批次处理当前数据
	//	batchTasks, err := t.processTasks(tasks, opt.WorkerCount)
	//	if err != nil {
	//		t.log.WithContext(ctx).Errorf("Process batch resources failed, err:%v", err)
	//		return nil, err
	//	}
	//	allTasks = append(allTasks, batchTasks...)
	//	t.log.WithContext(ctx).Infof("Processed batch: offset=%d, count=%d, total so far=%d",
	//		offset, len(tasks), len(allTasks))
	//	// 如果这批数据少于batchSize，说明已经是最后一批了
	//	if len(tasks) < opt.BatchSize {
	//		break
	//	}
	//	offset += opt.BatchSize
	//	// 更新游标
	//	lastId = tasks[len(tasks)-1].ID
	//}
}

func (t *TaskRepo) ListBizRunningTasks(ctx context.Context, taskInfo *models.Task) ([]*models.Task, error) {
	var tasks []domain.Task
	taskInfo.BizStatus = models.TaskStateActive
	dbTask, err := toTaskInfoModel(taskInfo)
	if err != nil {
		return nil, err
	}
	//查询运行中的任务信息，biz_status = 1
	//result := t.getTX(ctx).Where("user_id = ? AND product = ? AND engine_model = ? AND tag = ? AND"+
	//	" pipeline_id = ? AND notify = ? AND trace = ? AND worker_id = ? AND worker_ip = ? AND"+
	//	" biz_status = ? AND internal_status = ?",
	//	taskInfo.UserId, taskInfo.Product, taskInfo.EngineModel, taskInfo.Tag,
	//	taskInfo.PipelineId, taskInfo.Notify, taskInfo.Trace, taskInfo.Worker.Id, taskInfo.Worker.Ip,
	//	models.TaskStateActive, taskInfo.InternalStatus).Find(&tasks)

	result := t.getTX(ctx).Where(dbTask).Find(&tasks)

	if result.Error != nil {
		return nil, result.Error
	}

	return toTaskList(tasks)
}

func (t *TaskRepo) ListRunningTaskByWorkerId(ctx context.Context, workerId string) ([]*models.Task, error) {
	var tasks []domain.Task

	result := t.getTX(ctx).Where("biz_status = ? and worker_id = ?", models.TaskStateActive, workerId).Find(&tasks)

	if result.Error != nil {
		return nil, result.Error
	}

	return toTaskList(tasks)
}

func (t *TaskRepo) ListRunningTaskByWorkerIp(ctx context.Context, workerIp string) ([]*models.Task, error) {
	var tasks []domain.Task
	result := t.getTX(ctx).Where("biz_status = ? and worker_ip = ?", models.TaskStateActive, workerIp).Find(&tasks)

	if result.Error != nil {
		return nil, result.Error
	}

	return toTaskList(tasks)
}

func (t *TaskRepo) ListMissingRunningTasks(ctx context.Context) ([]*models.Task, error) {
	var tasks []domain.Task
	// todo 需要改成next的方式，不能一次性操作
	start := time.Now()
	result := t.getTX(ctx).Clauses(hints.UseIndex("idx_biz_status")).Where("biz_status = ? and internal_status = ?", models.TaskStateActive, models.TaskStateStopped).Limit(1000).Find(&tasks)
	duration := time.Since(start)
	log.Infof("ListMissingRunningTasks took %v, len:%v", duration, len(tasks))
	if result.Error != nil {
		return nil, result.Error
	}

	return toTaskList(tasks)
}

func (t *TaskRepo) ListUnexpectedRunningTasks(ctx context.Context) ([]*models.Task, error) {
	var tasks []domain.Task

	// 数据库命中biz_status索引, 导致扫描行大慢sql，指定使用internal_status索引
	result := t.getTX(ctx).Clauses(hints.UseIndex("idx_internal_status")).Where("internal_status = ? and biz_status = ?", models.TaskStateActive, models.TaskStateStopped).Limit(1000).Find(&tasks)

	if result.Error != nil {
		return nil, result.Error
	}

	return toTaskList(tasks)
}

// 测试中，通过传入taskInfo查找任务
func (t *TaskRepo) ListBizTasksWithOptionalFilters(ctx context.Context, taskInfo *models.Task) ([]*models.Task, error) {
	var tasks []domain.Task

	dbTask, err := toTaskModel(taskInfo)
	if err != nil {
		log.Errorf("Invalid toTaskModel, biz.tasker:%v", taskInfo)
		return nil, err

	}

	result := t.getTX(ctx).Where(dbTask).Find(&tasks)

	if result.Error != nil {
		return nil, result.Error
	}

	return toTaskList(tasks)
}

func (t *TaskRepo) CountAggregatedTasks(ctx context.Context) ([]*models.TempQueryTask, error) {
	var aggregatedTasks []*models.TempQueryTask

	result := t.getTX(ctx).
		Select("COUNT(*) AS task_num, user_id, pipeline_id, task_type, product, engine_model, tag").
		Where("biz_status = ?", models.TaskStateActive).
		Group("user_id, pipeline_id, task_type, product, engine_model, tag")

	if err := result.Scan(&aggregatedTasks).Error; err != nil {
		return nil, err
	}
	return aggregatedTasks, nil
}

func (t *TaskRepo) processTasks(tasks []domain.Task, workerCount int) ([]*models.Task, error) {
	// 预分配结果slice容量，避免动态扩容
	resultTasks := make([]*models.Task, 0, len(tasks))
	results := make(chan *models.Task, len(tasks))
	// 创建工作池
	var wg sync.WaitGroup
	input := make(chan domain.Task, len(tasks))
	// 将任务分配给工作池
	for _, task := range tasks {
		input <- task
	}
	close(input)
	// 启动多个工作 goroutine
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for dbTask := range input {
				task, err := toTask(&dbTask)
				if err != nil {
					continue
				}
				results <- task
			}
		}()
	}
	// 收集结果
	go func() {
		wg.Wait()
		close(results)
	}()
	// 收集所有结果
	for result := range results {
		resultTasks = append(resultTasks, result)
	}
	return resultTasks, nil
}

func toTaskList(dbTasks []domain.Task) ([]*models.Task, error) {
	var resultList []*models.Task

	for i := range dbTasks {
		cur, err := toTask(&dbTasks[i])
		if err != nil {
			log.Error("invalid tasker", dbTasks[i])
			continue
		}
		resultList = append(resultList, cur)
	}

	return resultList, nil
}

func toTaskModelList(tasks []*models.Task) ([]*domain.Task, error) {
	var result []*domain.Task

	for _, task := range tasks {
		dbTask, err := toTaskModel(task)
		if err != nil {
			log.Errorf("Invalid biz.tasker, biz.tasker:%v", task)
			continue
		}
		result = append(result, dbTask)
	}

	return result, nil
}

func toTaskModel(task *models.Task) (*domain.Task, error) {
	if task.EngineModel == "" {
		task.EngineModel = "default"
	}

	if task.Tag == "" {
		task.Tag = "default"
	}

	dbTask := &domain.Task{
		TaskId:         task.TaskId,
		JobId:          task.JobId,
		PipelineId:     task.PipelineId,
		Product:        task.Product,
		EngineModel:    task.EngineModel,
		Tag:            task.Tag,
		Notify:         task.Notify,
		UserData:       task.UserData,
		Trace:          task.Trace,
		InternalStatus: int32(task.GetInternalStatus()),
		BizStatus:      int32(task.GetBizStatus()),
		TaskType:       int32(task.TaskType),
		MigrateTimes:   task.MigrateTimes,
		GmtModified:    task.LastModified,
		GmtCreate:      task.GmtCreate,
		UserId:         task.UserId,
		Domain:         task.Domain,
	}

	resultData, err := json.Marshal(task.Result)
	if err != nil {
		return nil, err
	}
	dbTask.Result = resultData

	requestResourceData, err := json.Marshal(task.RequestResource)
	if err != nil {
		return nil, err
	}
	dbTask.RequestResource = requestResourceData

	engineParamData, err := json.Marshal(task.Param)
	if err != nil {
		return nil, err
	}
	dbTask.EngineParam = engineParamData

	if len(task.Dependencies) > 0 {
		data, err := json.Marshal(task.Dependencies)
		if err != nil {
			return nil, err
		}
		dbTask.Dependency = data
	}

	if task.Worker != nil {
		dbTask.WorkerIp = task.Worker.Ip
		dbTask.WorkerId = task.Worker.Id
		dbTask.WorkerPort = task.Worker.Port
	}

	if task.Result != nil {
		data, err := json.Marshal(task.Result)
		if err != nil {
			return nil, err
		}
		dbTask.Result = data
	}

	if task.MigrateConfig != nil {
		data, err := json.Marshal(task.MigrateConfig)
		if err != nil {
			return nil, err
		}
		dbTask.MigrateConfig = data
	}

	if len(task.Metadata) > 0 {
		data, err := json.Marshal(task.Metadata)
		if err != nil {
			return nil, err
		}
		dbTask.Metadata = data
	}

	if task.ScheduleParams != "" {
		date, err := json.Marshal(task.ScheduleParams)
		if err != nil {
			return nil, err
		}
		dbTask.ScheduleParam = date
	}

	return dbTask, nil
}

func toTask(dbTask *domain.Task) (*models.Task, error) {
	task := &models.Task{
		Product:        dbTask.Product,
		TaskId:         dbTask.TaskId,
		JobId:          dbTask.JobId,
		PipelineId:     dbTask.PipelineId,
		EngineModel:    dbTask.EngineModel,
		Tag:            dbTask.Tag,
		Notify:         dbTask.Notify,
		UserData:       dbTask.UserData,
		Trace:          dbTask.Trace,
		InternalStatus: models.TaskStatus(dbTask.InternalStatus),
		BizStatus:      models.TaskStatus(dbTask.BizStatus),
		TaskType:       models.TaskType(dbTask.TaskType),
		MigrateTimes:   dbTask.MigrateTimes,
		LastModified:   dbTask.GmtModified,
		GmtCreate:      dbTask.GmtCreate,
		Metadata:       map[string]string{},
		UserId:         dbTask.UserId,
		Domain:         dbTask.Domain,
	}

	if task.EngineModel == "" {
		task.EngineModel = "default"
	}

	if task.Tag == "" {
		task.Tag = "default"
	}

	err := json.Unmarshal(dbTask.RequestResource, &task.RequestResource)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(dbTask.EngineParam, &task.Param)
	if err != nil {
		return nil, err
	}

	if dbTask.WorkerId != "" {
		task.Worker = &models.Worker{
			Id:   dbTask.WorkerId,
			Ip:   dbTask.WorkerIp,
			Port: dbTask.WorkerPort,
		}
	}

	if len(dbTask.Result) > 0 {
		err = json.Unmarshal(dbTask.Result, &task.Result)
		if err != nil {
			return nil, err
		}
	}

	if len(dbTask.Dependency) > 0 {
		err = json.Unmarshal(dbTask.Dependency, &task.Dependencies)
		if err != nil {
			return nil, err
		}
	}

	if dbTask.MigrateConfig != nil {
		err = json.Unmarshal(dbTask.MigrateConfig, &task.MigrateConfig)
		if err != nil {
			return nil, err
		}
	}

	if len(dbTask.Metadata) > 0 {
		err = json.Unmarshal(dbTask.Metadata, &task.Metadata)
		if err != nil {
			// 忽略metadata的解析错误
			log.Errorf("json.Unmarshal failed. err:%v", err)
		}
	}

	return task, nil
}

func toTaskInfoModel(task *models.Task) (*domain.Task, error) {
	dbTask := &domain.Task{
		PipelineId:     task.PipelineId,
		Product:        task.Product,
		EngineModel:    task.EngineModel,
		Tag:            task.Tag,
		Notify:         task.Notify,
		UserData:       task.UserData,
		Trace:          task.Trace,
		InternalStatus: int32(task.GetInternalStatus()),
		BizStatus:      int32(task.GetBizStatus()),
		TaskType:       int32(task.TaskType),
		MigrateTimes:   task.MigrateTimes,
		UserId:         task.UserId,
	}
	if task.Worker != nil {
		dbTask.WorkerIp = task.Worker.Ip
		dbTask.WorkerId = task.Worker.Id
		dbTask.WorkerPort = task.Worker.Port
	}

	return dbTask, nil
}

func toTaskListWithStatus(statusTasks []domain.SimpleTaskWithStatus) ([]*models.Task, error) {
	tasks := make([]*models.Task, 0, len(statusTasks))
	for i := range statusTasks {
		statusTask := statusTasks[i]
		task := &models.Task{
			TaskId:         statusTask.TaskId,
			GmtCreate:      statusTask.GmtCreate,
			LastModified:   statusTask.GmtModified,
			InternalStatus: models.TaskStatus(statusTask.InternalStatus),
			BizStatus:      models.TaskStatus(statusTask.BizStatus),
			Product:        statusTask.Product,
			Tag:            statusTask.Tag,
			Domain:         statusTask.Domain,
			EngineModel:    statusTask.EngineModel,
		}
		tasks = append(tasks, task)
	}
	return tasks, nil
}
