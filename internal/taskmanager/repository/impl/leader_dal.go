package impl

import (
	"context"
	"mpp/internal/taskmanager/biz/leader"
	"time"

	"mpp/internal/taskmanager/models/domain"
	"mpp/internal/taskmanager/repository"

	"github.com/go-kratos/kratos/v2/log"
)

type leaderRepo struct {
	data *repository.Data
	log  *log.Helper
}

// NewLeaderRepo .
func NewLeaderRepo(data *repository.Data, logger log.Logger) leader.LeaderRepo {
	return &leaderRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (l *leaderRepo) Create(leader leader.Leader) error {
	leaderDO := domain.Leader{
		Key:      leader.Key,
		Instance: leader.Instance,
	}

	result := l.data.GetDB().WithContext(context.TODO()).Exec(
		"INSERT IGNORE INTO leaders(gmt_create, gmt_modified, `key`, `instance`, last_tick_time)"+
			" VALUES(now(), now(), ?, ?, now() ) ",
		leaderDO.Key, leaderDO.Instance)

	if result.Error != nil {
		log.Errorf("create leader, leader:%v error: %v", leaderDO, result.Error)
		return result.Error
	}

	return nil
}

func (l *leaderRepo) QueryLeader(key string) (*leader.Leader, error) {
	var leaderDO domain.Leader
	result := l.data.GetDB().WithContext(context.TODO()).Model(&domain.Leader{}).Where("`key` = ?", key).Take(&leaderDO)

	if result.Error != nil {
		if result.Error.Error() == "record not found" {
			return nil, leader.ErrLeaderNotExist
		}
		log.Error("QueryLeader ", result.Error)
		return nil, result.Error
	}

	return &leader.Leader{
		Instance:     leaderDO.Instance,
		Key:          leaderDO.Key,
		LastTickTime: leaderDO.LastTickTime,
	}, nil
}

func (l *leaderRepo) KeepAlive(leader leader.Leader) (bool, error) {
	data := map[string]interface{}{
		"LastTickTime": time.Now(),
		"GmtModified":  time.Now(),
	}

	result := l.data.GetDB().WithContext(context.TODO()).Model(&domain.Leader{}).Where("`key` = ? and `instance` = ? ", leader.Key, leader.Instance).Updates(data)
	if result.Error != nil {
		return false, result.Error
	}
	return result.RowsAffected > 0, nil
}

func (l *leaderRepo) UpdateLeader(leader leader.Leader, outDateSeconds int) (bool, error) {
	data := map[string]interface{}{
		"LastTickTime": time.Now(),
		"Instance":     leader.Instance,
		"GmtModified":  time.Now(),
	}

	result := l.data.GetDB().WithContext(context.TODO()).Model(&domain.Leader{}).Where("`key` = ? "+
		"and TIMESTAMPDIFF(SECOND, last_tick_time, NOW()) > ? ",
		leader.Key, outDateSeconds).Updates(data)
	if result.Error != nil {
		return false, result.Error
	}
	return result.RowsAffected > 0, nil
}
