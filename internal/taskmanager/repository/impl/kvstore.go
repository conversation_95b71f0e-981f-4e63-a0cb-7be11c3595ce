package impl

import (
	"context"

	"mpp/internal/taskmanager/models/domain"
	"mpp/internal/taskmanager/repository"

	"github.com/go-kratos/kratos/v2/log"
)

const (
	DefaultRepo        = "default"
	DefaultExpiredTime = 86400 * 365 * 100
)

type dbKVStore struct {
	data *repository.Data
	log  *log.Helper
}

func NewDBKVStore(data *repository.Data, logger log.Logger) repository.KVStoreRepo {
	return &dbKVStore{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (k *dbKVStore) Set(ctx context.Context, kv *domain.KVStore) error {
	result := k.data.GetDB().Table("kv_stores").WithContext(ctx).Exec(
		"INSERT INTO kv_stores(gmt_create, gmt_modified, repo, `key`, value, gmt_expired) "+
			"VALUES(now(), now(), ?, ?, ?, ?) "+
			"ON DUPLICATE KEY UPDATE value = ?, gmt_expired = ?, gmt_modified = now()",
		kv.Repo, kv.Key, kv.Value, kv.ExpiredTime, kv.Value, kv.ExpiredTime,
	)
	if result.Error != nil {
		k.log.WithContext(ctx).Warnf("set kvstore failed, err:%v", result)
		return result.Error
	}

	return nil
}

func (k *dbKVStore) ListAll(ctx context.Context) ([]*domain.KVStore, error) {
	var kvStores []*domain.KVStore
	result := k.data.GetDB().WithContext(ctx).Where("CURRENT_TIMESTAMP <= gmt_expired").Find(&kvStores)
	if result.Error != nil {
		return nil, result.Error
	}
	return kvStores, nil
}

func (k *dbKVStore) Get(ctx context.Context, kv *domain.KVStore) (string, error) {
	var resultKv domain.KVStore
	result := k.data.GetDB().WithContext(ctx).Where("repo = ? AND `key` = ? AND CURRENT_TIMESTAMP < gmt_expired", kv.Repo, kv.Key).Take(&resultKv)
	if result.Error != nil {
		k.log.WithContext(ctx).Warnf("get kvstore failed, err:%v", result)
		return kv.Value, result.Error
	}

	return resultKv.Value, nil
}

func (k *dbKVStore) Delete(ctx context.Context, kv *domain.KVStore) {
	k.data.GetDB().WithContext(ctx).Where("repo =? and `key` =?", kv.Repo, kv.Key).Delete(&domain.KVStore{})
}

func (k *dbKVStore) Cas(ctx context.Context, newKV *domain.KVStore, oldKV *domain.KVStore) (bool, error) {
	result := k.data.GetDB().WithContext(ctx).Exec(
		"UPDATE kv_stores SET value = ?, gmt_expired = ?, gmt_modified = CURRENT_TIMESTAMP WHERE repo = ? AND `key` = ? AND value = ?",
		newKV.Value, newKV.ExpiredTime, oldKV.Repo, oldKV.Key, oldKV.Value,
	)
	if result.Error != nil {
		k.log.WithContext(ctx).Warnf("cas kvstore failed, err:%v", result)
		return false, result.Error
	}

	if result.RowsAffected == 0 {
		k.log.WithContext(ctx).Warnf("compare and set kvstore new:%+v old:%+v failed, affected rows is 0", newKV, oldKV)
		return false, nil
	}

	return true, nil
}

func (k *dbKVStore) List(ctx context.Context, repo string, key string) ([]*domain.KVStore, error) {
	var kvStores []*domain.KVStore
	result := k.data.GetDB().WithContext(ctx).Where("repo = ? AND `key` = ? AND CURRENT_TIMESTAMP < gmt_expired", repo, key).Find(&kvStores)
	if result.Error != nil {
		return nil, result.Error
	}
	return kvStores, nil
}

func (k *dbKVStore) ListLike(ctx context.Context, repo string, key string) ([]*domain.KVStore, error) {
	var kvStores []*domain.KVStore
	result := k.data.GetDB().WithContext(ctx).Where("repo = ? AND `key` LIKE ? AND CURRENT_TIMESTAMP < gmt_expired", repo, "%"+key+"%").Find(&kvStores)
	if result.Error != nil {
		return nil, result.Error
	}
	return kvStores, nil
}
