package impl

import (
	"context"
	"testing"
	"time"

	"mpp/internal/taskmanager/models/domain"

	"github.com/stretchr/testify/assert"
)

func TestTranscodeStatInfo(t *testing.T) {
	transcodeAttachmentRepo := NewDBTranscodeStatInfo(mySQLDb, nil)
	tx := time.Now()
	sw := &domain.TranscodeStatInfo{
		Domain:        "example.com",
		Tag:           "sample_tag",
		StatTime:      tx,
		StatInfo:      "{\"key\":\"value\"}",
		TotalQuota:    100,
		TotalTaskNum:  50,
		InnerQuota:    30,
		InnerTaskNum:  20,
		CloudQuota:    40,
		CloudTaskNum:  25,
		L2Quota:       10,
		L2TaskNum:     5,
		L1Quota:       5,
		L1TaskNum:     3,
		L22Quota:      8,
		L22TaskNum:    4,
		Inner2Quota:   15,
		Inner2TaskNum: 7,
		Extend:        "{\"additional\":\"info\"}",
	}
	c := context.Background()
	err := transcodeAttachmentRepo.Create(c, sw)
	assert.Nil(t, err)
	result, err := transcodeAttachmentRepo.Query(c, "sample_tag", "example.com", tx.Add(time.Hour))
	assert.Nil(t, err)
	assert.Equal(t, len(result), 0)
	statInfo, err := transcodeAttachmentRepo.GetLatest(ctx, "sample_tag")
	assert.Nil(t, err)
	assert.Equal(t, statInfo.Domain, sw.Domain)
	result, err = transcodeAttachmentRepo.Query(c, "sample_tag", "example.com", statInfo.StatTime)
	assert.Nil(t, err)
	assert.Equal(t, len(result), 1)
}
