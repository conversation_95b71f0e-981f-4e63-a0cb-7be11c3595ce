package impl

import (
	"context"
	"errors"
	"strings"
	"time"

	"mpp/internal/taskmanager/models/domain"
	"mpp/internal/taskmanager/repository"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type dbTranscodeAttachment struct {
	data *repository.Data
	log  *log.Helper
}

func NewDBTranscodeAttachment(data *repository.Data, logger log.Logger) repository.TranscodeAttachmentRepo {
	return &dbTranscodeAttachment{
		data: data,
		log:  log.<PERSON><PERSON><PERSON>per(logger),
	}
}

func (t *dbTranscodeAttachment) Create(ctx context.Context, transcodeAttachments []*domain.TranscodeAttachment) error {
	if len(transcodeAttachments) == 0 {
		return nil
	}

	// 设置所有记录的创建和修改时间
	now := time.Now()
	for _, attachment := range transcodeAttachments {
		attachment.CreateTime = now
		attachment.ModifiedTime = now
		attachment.Status = 0
	}

	db := t.data.GetDB().WithContext(ctx)

	result := db.Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "task_id"},
			{Name: "attach_type"},
			{Name: "name"},
		},
		DoUpdates: clause.AssignmentColumns([]string{"params", "status", "gmt_modified"}),
		UpdateAll: true,
	}).CreateInBatches(transcodeAttachments, 100)

	if result.Error != nil {
		t.log.WithContext(ctx).Warnf("set transcode_attachment failed err:%v", result)
		return result.Error
	}

	return nil
}

func (t *dbTranscodeAttachment) Delete(ctx context.Context, transcodeAttachments []*domain.TranscodeAttachment) error {
	if len(transcodeAttachments) == 0 {
		return nil
	}

	db := t.data.GetDB().WithContext(ctx)

	// 构建 OR 条件
	var conditions []string
	var args []interface{}

	for _, item := range transcodeAttachments {
		if item.AttachType == "" || item.Name == "" {
			continue
		}
		conditions = append(conditions, "(task_id = ? AND attach_type = ? AND name = ?)")
		args = append(args, item.TaskID, item.AttachType, item.Name)
	}
	if len(conditions) == 0 || len(args) == 0 {
		return errors.New("no valid conditions provided")
	}

	whereClause := strings.Join(conditions, " OR ")

	result := db.Model(&domain.TranscodeAttachment{}).
		Where(whereClause, args...).
		Updates(map[string]interface{}{
			"status":       1,
			"gmt_modified": gorm.Expr("CURRENT_TIMESTAMP"),
		})

	if result.Error != nil {
		t.log.WithContext(ctx).Warnf("delete transcode_attachment failed err:%v", result)
		return result.Error
	}
	return nil
}

func (t *dbTranscodeAttachment) SelectByTaskType(ctx context.Context, taskId, attachType string) ([]*domain.TranscodeAttachment, error) {
	var transcodeAttachments []*domain.TranscodeAttachment
	result := t.data.GetDB().WithContext(ctx).Where("task_id = ? AND attach_type = ? AND status = 0", taskId, attachType).Find(&transcodeAttachments)
	if result.Error != nil {
		t.log.WithContext(ctx).Warnf("select transcode_attachment failed err:%v", result)
		return nil, result.Error
	}
	return transcodeAttachments, nil
}
