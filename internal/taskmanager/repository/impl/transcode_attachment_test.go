package impl

import (
	"context"
	"testing"

	"mpp/internal/taskmanager/models/domain"

	"github.com/stretchr/testify/assert"
)

func TestTranscodeAttachmentStore(t *testing.T) {
	attachmentStore := NewDBTranscodeAttachment(mySQLDb, nil)
	sw := []*domain.TranscodeAttachment{
		{
			TaskID:     "294902424",
			AttachType: "pic",
			Name:       "pic1",
			Params:     "{}",
			Status:     0,
		},
		{
			TaskID:     "294902424",
			AttachType: "text",
			Name:       "pic1",
			Params:     "{xxxx}",
			Status:     0,
		},
		{
			TaskID:     "294902424",
			AttachType: "pic",
			Name:       "pic2",
			Params:     "{}",
			Status:     0,
		},
	}
	c := context.Background()
	err := attachmentStore.Create(c, sw)
	assert.Nil(t, err)
	result, err := attachmentStore.SelectByTaskType(c, "294902424", "pic")
	assert.Nil(t, err)
	assert.Equal(t, result[0].Name, "pic1")
	err = attachmentStore.Delete(c, []*domain.TranscodeAttachment{
		{
			TaskID:     "294902424",
			Name:       "pic1",
			AttachType: "pic",
		},
		{
			TaskID:     "294902424",
			AttachType: "text",
			Name:       "pic1",
		},
	})
	assert.Nil(t, err)
	result, err = attachmentStore.SelectByTaskType(c, "294902424", "pic")
	assert.Equal(t, len(result), 1)
	assert.Equal(t, result[0].Name, "pic2")
}
