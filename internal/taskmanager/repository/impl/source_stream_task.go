package impl

import (
	"context"

	"mpp/internal/taskmanager/models/domain"
	"mpp/internal/taskmanager/repository"

	"github.com/go-kratos/kratos/v2/log"
)

type dbSourceStreamTask struct {
	data *repository.Data
	log  *log.Helper
}

func NewDBSourceStreamTaskStore(data *repository.Data, logger log.Logger) repository.SourceStreamTaskRepo {
	return &dbSourceStreamTask{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (s *dbSourceStreamTask) Insert(ctx context.Context, task *domain.SourceStreamTask) error {
	result := s.data.GetDB().WithContext(ctx).Exec(
		"INSERT INTO source_stream_tasks(gmt_create, gmt_modified, source_stream_id, domain, app, stream,task_id) "+
			"VALUES(now(), now(), ?, ?, ?, ?, ?) "+
			"ON DUPLICATE KEY UPDATE gmt_modified = now()",
		task.SourceStreamId, task.Domain, task.App, task.Stream, task.TaskId,
	)
	if result.Error != nil {
		s.log.WithContext(ctx).Warnf("set source stream task failed, err:%v", result)
		return result.Error
	}

	return nil
}

func (s *dbSourceStreamTask) ListBySourceStreamId(ctx context.Context, sourceStreamId string) ([]*domain.SourceStreamTask, error) {
	var tasks []*domain.SourceStreamTask
	result := s.data.GetDB().WithContext(ctx).Where("source_stream_id = ?", sourceStreamId).Find(&tasks)
	if result.Error != nil {
		s.log.WithContext(ctx).Warnf("list source stream task failed, err:%v", result)
		return nil, result.Error
	}
	return tasks, nil
}
