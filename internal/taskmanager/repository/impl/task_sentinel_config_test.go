package impl

import (
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"
	v1 "proto.mpp/api/common/v1"
	"time"

	"github.com/google/uuid"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("taskSentinelConfig repo create_update_query", func() {
	var (
		taskSentinelConfigRepo repository.TaskSentinelConfigRepo
	)
	BeforeEach(func() {
		taskSentinelConfigRepo = NewTaskSentinelConfigRepoRepo(Db, nil)
	})

	It("SaveAndQueryByIdAndType", func() {
		//流控配置创建
		taskSentinelConfig := newTaskSentinelConfig()
		err := taskSentinelConfigRepo.Create(ctx, taskSentinelConfig)
		Expect(err).Should(BeNil())
		querySaveResult, err := taskSentinelConfigRepo.GetTaskSentinelConfig(ctx, taskSentinelConfig.SentinelConfigId)
		Expect(err).Should(BeNil())
		ShouldTaskSentinelConfigEqual(taskSentinelConfig, querySaveResult)
		//流控配置删除
		err = taskSentinelConfigRepo.Delete(ctx, taskSentinelConfig.SentinelConfigId)
		Expect(err).Should(BeNil())
		querySaveResult, err = taskSentinelConfigRepo.GetTaskSentinelConfig(ctx, taskSentinelConfig.SentinelConfigId)
		Ω(querySaveResult).To(BeNil())

		//流控配置创建或更新
		taskSentinelConfig2 := newTaskSentinelConfig()
		err = taskSentinelConfigRepo.InsertUpdateOnDuplicate(ctx, taskSentinelConfig2)
		Expect(err).Should(BeNil())
		querySaveResult, err = taskSentinelConfigRepo.GetTaskSentinelConfig(ctx, taskSentinelConfig2.SentinelConfigId)
		Expect(err).Should(BeNil())
		ShouldTaskSentinelConfigEqual(taskSentinelConfig2, querySaveResult)

		//流控配置状态更新
		taskSentinelConfig.SentinelStatus = models.SentinelStatusStatePaused
		_, err = taskSentinelConfigRepo.UpdateSentinelStatus(ctx, taskSentinelConfig2)
		Expect(err).Should(BeNil())
		querySaveResult, err = taskSentinelConfigRepo.GetTaskSentinelConfig(ctx, taskSentinelConfig2.SentinelConfigId)
		Expect(err).Should(BeNil())
		ShouldTaskSentinelConfigEqual(taskSentinelConfig2, querySaveResult)

		//流控配置更新
		taskSentinelConfig2.SentinelConfig.MaxParallelNum = 20
		_, err = taskSentinelConfigRepo.UpdateTaskSentinelConfig(ctx, taskSentinelConfig2)
		Expect(err).Should(BeNil())
		querySaveResult, err = taskSentinelConfigRepo.GetTaskSentinelConfig(ctx, taskSentinelConfig2.SentinelConfigId)
		Expect(err).Should(BeNil())
		ShouldTaskSentinelConfigEqual(taskSentinelConfig2, querySaveResult)
	})
})

func ShouldTaskSentinelConfigEqual(taskSentinelConfig1, taskSentinelConfig2 *models.TaskSentinelConfig) {

	Ω(taskSentinelConfig1.PipelineId).To(Equal(taskSentinelConfig2.PipelineId))
	Ω(taskSentinelConfig1.UserId).To(Equal(taskSentinelConfig2.UserId))
	Ω(taskSentinelConfig1.TaskType).To(Equal(taskSentinelConfig2.TaskType))
	Ω(taskSentinelConfig1.Product).To(Equal(taskSentinelConfig2.Product))
	Ω(taskSentinelConfig1.EngineModel).To(Equal(taskSentinelConfig2.EngineModel))
	Ω(taskSentinelConfig1.Tag).To(Equal(taskSentinelConfig2.Tag))

	Ω(taskSentinelConfig1.SentinelConfig).To(Equal(taskSentinelConfig2.SentinelConfig))
	Ω(taskSentinelConfig1.SentinelConfigId).To(Equal(taskSentinelConfig2.SentinelConfigId))
	Ω(taskSentinelConfig1.SentinelStatus).To(Equal(taskSentinelConfig2.SentinelStatus))
	Ω(taskSentinelConfig1.CurrentTaskNum).To(Equal(taskSentinelConfig2.CurrentTaskNum))

	Ω(taskSentinelConfig1.GmtModified).To(BeTemporally("~", taskSentinelConfig2.GmtModified, time.Second))
	Ω(taskSentinelConfig1.GmtCreate).To(BeTemporally("~", taskSentinelConfig2.GmtCreate, time.Second))
}

func newTaskSentinelConfig() *models.TaskSentinelConfig {
	taskSentinelConfig := &models.TaskSentinelConfig{
		UserId:      "123456",
		PipelineId:  uuid.NewString(),
		TaskType:    v1.TaskType_Async,
		Product:     "mpp_product_test",
		EngineModel: "mpp_engineModel_test",
		Tag:         "default",

		SentinelStatus: models.SentinelStatusStateActive,
		GmtCreate:      time.Now(),
		GmtModified:    time.Now(),
	}

	taskSentinelConfig.GenerateSentinelConfigId()
	taskSentinelConfig.SentinelConfig = &models.SentinelConfig{
		MaxParallelNum: 10,
	}

	return taskSentinelConfig
}
