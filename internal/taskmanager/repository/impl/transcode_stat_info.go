package impl

import (
	"context"
	"mpp/internal/taskmanager/models/domain"
	"mpp/internal/taskmanager/repository"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

type dbTranscodeStatInfo struct {
	data *repository.Data
	log  *log.Helper
}

func NewDBTranscodeStatInfo(data *repository.Data, logger log.Logger) repository.TranscodeStatInfoRepo {
	return &dbTranscodeStatInfo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (t *dbTranscodeStatInfo) Create(ctx context.Context, transcodeStatInfo *domain.TranscodeStatInfo) error {
	// 设置创建和修改时间
	now := time.Now()
	transcodeStatInfo.CreateTime = now
	transcodeStatInfo.ModifiedTime = now

	// 执行创建操作
	result := t.data.GetDB().Create(transcodeStatInfo)
	if result.Error != nil {
		t.log.Errorf("failed to create transcode stat info: %v", result.Error)
		return result.Error
	}

	return nil
}

func (t *dbTranscodeStatInfo) GetLatest(ctx context.Context, tag string) (*domain.TranscodeStatInfo, error) {
	var statInfo domain.TranscodeStatInfo

	result := t.data.GetDB().
		Where("tag = ?", tag).
		Order("id DESC").
		Limit(1).
		First(&statInfo)

	if result.Error != nil {
		t.log.Errorf("failed to get latest transcode stat info: %v", result.Error)
		return nil, result.Error
	}

	return &statInfo, nil
}

func (t *dbTranscodeStatInfo) Query(ctx context.Context, tag, transcodeDomain string, statTime time.Time) ([]*domain.TranscodeStatInfo, error) {
	var statInfos []*domain.TranscodeStatInfo

	db := t.data.GetDB()

	if tag != "" {
		db = db.Where("tag = ?", tag)
	}

	if transcodeDomain != "" {
		db = db.Where("domain = ?", transcodeDomain)
	}

	if !statTime.IsZero() {
		db = db.Where("stat_time = ?", statTime)
	}

	result := db.Order("id").Find(&statInfos)
	if result.Error != nil {
		t.log.Errorf("failed to query transcode stat info: %v", result.Error)
		return nil, result.Error
	}

	return statInfos, nil
}
