package impl

import (
	"context"
	"database/sql"
	"testing"

	"mpp/internal/taskmanager/conf"

	"github.com/stretchr/testify/assert"
	"mpp/internal/taskmanager/repository"
)

var mySQLDb *repository.Data

func init() {
	//conn := "root:88888888@tcp(localhost:3306)/taskmanager?charset=utf8mb4&parseTime=True&loc=Local"
	conn := "root:secret@tcp(localhost:3306)/mysql?parseTime=true"
	_, err := sql.Open("mysql", conn)

	config := &conf.Data{Database: &conf.Data_Database{Driver: "mysql", Source: conn, AutoMigrate: true}}
	db, _ := repository.NewDB(config, &conf.LiveTransCodeConfig{})
	mySQLDb, _, err = repository.NewData(nil, db, nil, config)
	if err != nil {
		return
	}
}

func TestCreateSuccessAndQueryExpectAndDelete(t *testing.T) {
	tesSwitchConfigRepo := NewSwitchConfigRepo(mySQLDb, nil)
	sw := repository.SwitchConfig{
		App:          "app",
		Domain:       "domain",
		EngineModel:  "engine_model",
		Extend:       "extend",
		Product:      "product",
		SwitchConfig: "swc",
		BackupConfig: "sfc",
		Token:        "token",
		Tag:          "tag",
	}
	c := context.Background()
	err := tesSwitchConfigRepo.Insert(c, &sw)
	assert.Nil(t, err)

	sws, err := tesSwitchConfigRepo.Query(c, &sw)
	assert.Nil(t, err)
	assert.Equal(t, sws[0].Tag, sw.Tag)
	sws, err = tesSwitchConfigRepo.ListAll(c)
	assert.Nil(t, err)
	assert.Equal(t, len(sws), 1)
	sw.SwitchConfig = "swc2"
	err = tesSwitchConfigRepo.Insert(c, &sw)
	assert.Nil(t, err)
	sws, err = tesSwitchConfigRepo.Query(c, &sw)
	assert.Nil(t, err)
	assert.NotEqual(t, sws[0].SwitchConfig, "swcc")
	tesSwitchConfigRepo.Delete(c, &sw)
}
