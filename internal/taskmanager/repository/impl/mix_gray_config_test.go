package impl

import (
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

/**
 * @Author: qinxin
 * @Date: 2024/12/26 16:24
 * @Desc: 混跑灰度数据库操作测试
 * @Version 1.0
 */

func newMixGrayConfig() *models.MixGrayConfig {
	return &models.MixGrayConfig{
		RuleName:     "test_rule",
		Priority:     1,
		MixRatio:     0.5,
		ConfigStatus: models.MixGrayConfigStatusActive,
		RuleConfigs:  []*models.MixRuleConfig{},
		MixConfigs: []*models.MixConfig{
			{
				EngineModel:      "engine_model",
				MixAllocStrategy: "NormalMixed",
				Product:          "product",
				Ratio:            100,
				Tag:              "tag",
				Version:          "version",
			},
		},
	}
}

func ShouldMixGrayConfigEqual(a *models.MixGrayConfig, b *models.MixGrayConfig) {
	Ω(a.RuleName).Should(Equal(b.RuleName))
	Ω(a.Priority).Should(Equal(b.Priority))
	Ω(a.MixRatio).Should(Equal(b.MixRatio))
	Ω(a.ConfigStatus).Should(Equal(b.ConfigStatus))
	Ω(a.MixConfigs).Should(Equal(b.MixConfigs))
	Ω(a.RuleConfigs).Should(Equal(b.RuleConfigs))

	Ω(a.GmtModified).To(BeTemporally("~", b.GmtModified, time.Second))
	Ω(a.GmtCreate).To(BeTemporally("~", b.GmtCreate, time.Second))
}

var _ = Describe("mixGrayConfigRepo repo create_update_query", func() {
	var (
		mixGrayConfigRepo repository.MixGrayConfigRepo
	)
	//初始化查询实例 添加表名
	BeforeEach(func() {
		mixGrayConfigRepo = NewMixGrayConfigRepo(Db, nil)
	})

	It("create and update and delete", func() {
		mixGrayConfig := newMixGrayConfig()
		mixGrayConfigDomain, err := models.MixGrayConfigBizToDomain(mixGrayConfig)
		Expect(err).Should(BeNil())
		err = mixGrayConfigRepo.Create(ctx, mixGrayConfigDomain)
		Expect(err).Should(BeNil())
		querySaveResult, err := mixGrayConfigRepo.GetMixGrayConfigByName(ctx, mixGrayConfig.RuleName)
		Expect(err).Should(BeNil())
		querySaveResultBiz, err := models.DomainToMixGrayConfigBiz(querySaveResult)
		ShouldMixGrayConfigEqual(mixGrayConfig, querySaveResultBiz)

		//更新测试
		mixGrayConfig.MixRatio = 0.6
		err = mixGrayConfigRepo.UpdateMixRatio(ctx, mixGrayConfigDomain)
		Expect(err).Should(BeNil())

		mixGrayConfig.Priority = 2
		err = mixGrayConfigRepo.Update(ctx, mixGrayConfigDomain)
		Expect(err).Should(BeNil())
		querySaveResult, err = mixGrayConfigRepo.GetMixGrayConfigByName(ctx, mixGrayConfig.RuleName)
		Expect(err).Should(BeNil())
		//querySaveResultBiz, err = models.DomainToMixGrayConfigBiz(querySaveResult)
		//ShouldMixGrayConfigEqual(mixGrayConfig, querySaveResultBiz)

		//list 查询测试
		queryListResult, err := mixGrayConfigRepo.ListActiveMixGrayConfig(ctx)
		Expect(err).Should(BeNil())
		Expect(len(queryListResult)).Should(Equal(1))

		queryListResult, err = mixGrayConfigRepo.List(ctx)
		Expect(err).Should(BeNil())
		Expect(len(queryListResult)).Should(Equal(1))

		//delete
		err = mixGrayConfigRepo.Delete(ctx, mixGrayConfig.RuleName)
		Expect(err).Should(BeNil())
		querySaveResult, err = mixGrayConfigRepo.GetMixGrayConfigByName(ctx, mixGrayConfig.RuleName)
		Expect(err).Should(BeNil())
		Expect(querySaveResult).Should(BeNil())
	})

})
