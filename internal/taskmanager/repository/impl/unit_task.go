package impl

import (
	"context"
	"time"

	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/domain"
	"mpp/internal/taskmanager/repository"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

type unitTaskRepo struct {
	data *repository.Data
	log  *log.Helper
}

func NewUnitTaskRepo(data *repository.Data, logger log.Logger) repository.UnitTaskRepo {
	return &unitTaskRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (u unitTaskRepo) GetUnitTaskAll(ctx context.Context, unit string) ([]*domain.UnitTask, error) {
	var tasks []*domain.UnitTask
	result := u.data.GetDB().WithContext(ctx).Model(&domain.UnitTask{}).Where("unit = ?", unit).Find(&tasks)
	if result.Error != nil {
		return nil, result.Error
	}
	return tasks, nil
}

func (u unitTaskRepo) GetUnitTask(ctx context.Context, taskId string) (*domain.UnitTask, error) {
	var task domain.UnitTask
	result := u.data.GetDB().WithContext(ctx).Model(&domain.UnitTask{}).Where("task_id = ?", taskId).First(&task)
	if result.Error != nil {
		return nil, result.Error
	}
	return &task, nil
}

func (u unitTaskRepo) InsertUnitTask(ctx context.Context, task *domain.UnitTask) error {
	if task.GmtCreate.IsZero() {
		task.GmtCreate = time.Now()
	}
	if task.GmtModified.IsZero() {
		task.GmtModified = time.Now()
	}
	result := u.data.GetDB().WithContext(ctx).Model(&domain.UnitTask{}).Create(task)
	if result.Error != nil {
		u.log.Errorf("InsertUnitTask failed, error: %v", result.Error)
		return result.Error
	}
	return nil
}

func (u unitTaskRepo) UpdateUnitTaskStatus(ctx context.Context, taskId string) error {
	result := u.data.GetDB().WithContext(ctx).Model(&domain.UnitTask{}).Where("task_id = ?", taskId).Updates(map[string]interface{}{"status": 0, "gmt_modified": gorm.Expr("CURRENT_TIMESTAMP")})
	if result.Error != nil {
		u.log.Errorf("UpdateUnitTaskStatus failed, error: %v", result.Error)
		return result.Error
	}
	return nil
}

func (u unitTaskRepo) SyncTask(ctx context.Context, task *domain.Task) error {
	result := u.data.GetDB().Model(&domain.Task{}).WithContext(ctx).Table(models.StreamTaskTableName).Exec(
		"INSERT INTO "+models.StreamTaskTableName+" (gmt_create, gmt_modified, job_id, task_id, pipeline_id, task_type, biz_status, internal_status, product, engine_model, tag, "+
			"request_resource, user_data, trace, notify, engine_param, migrate_config, dependency, migrate_times, metadata, user_id, schedule_param,domain)"+
			"VALUES(now(), now(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, ?, ? , ?) "+
			"ON DUPLICATE KEY UPDATE product = ?, engine_model = ?, tag = ?, biz_status = ?, request_resource = ?, user_data = ?, user_id = ?, schedule_param = ?, "+
			"trace = ?, notify = ?, engine_param = ?, migrate_config = ?, metadata = ?, dependency = ?, "+
			"result = NULL, worker_id = NULL, worker_ip = NULL, worker_port = NULL, migrate_times = 0, gmt_modified = now()",
		task.JobId, task.TaskId, task.PipelineId, task.TaskType, task.BizStatus,
		task.InternalStatus, task.Product, task.EngineModel, task.Tag, task.RequestResource,
		task.UserData, task.Trace, task.Notify, task.EngineParam, task.MigrateConfig, task.Dependency, task.Metadata, task.UserId, task.ScheduleParam, task.Domain,
		task.Product, task.EngineModel, task.Tag, task.BizStatus, task.RequestResource, task.UserData, task.UserId, task.ScheduleParam,
		task.Trace, task.Notify, task.EngineParam, task.MigrateConfig, task.Metadata, task.Dependency,
	)
	if result.Error != nil {
		u.log.Errorf("SyncTask failed, error: %v", result.Error)
		return result.Error
	}
	return nil
}

func (u unitTaskRepo) UpdateTaskStatus(ctx context.Context, taskId string, bizStatus int8, internalStatus int8) error {
	result := u.data.GetDB().WithContext(ctx).Model(&domain.Task{}).Table(models.StreamTaskTableName).Where("task_id = ?", taskId).Updates(map[string]interface{}{"biz_status": bizStatus, "internal_status": internalStatus, "gmt_modified": gorm.Expr("CURRENT_TIMESTAMP")})
	if result.Error != nil {
		u.log.Errorf("UpdateTaskStatus failed, error: %v", result.Error)
		return result.Error
	}
	return nil
}
