package impl

import (
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"mpp/internal/taskmanager/models/domain"
)

//func KVStore(key, value string) *domain.KVStore {
//	kv := &domain.KVStore{
//		Repo:        "repo-test",
//		Key:         key,
//		Value:       value,
//		ExpiredTime: time.Now().Add(1 * time.Second),
//	}
//	return kv
//}
//
//func KVStoreList(len int) []*domain.KVStore {
//	kvs := make([]*domain.KVStore, 0)
//	for i := 0; i < len; i++ {
//
//		var second time.Duration
//		if i < 5 {
//			second = 1
//		} else {
//			second = 3
//		}
//
//		kv := &domain.KVStore{
//			Repo:        fmt.Sprintf("repo-test-batch-%v", i),
//			Key:         fmt.Sprintf("key-test-batch-%v", i),
//			Value:       fmt.Sprintf("value-test-batch-%v", i),
//			ExpiredTime: time.Now().Add(second * time.Second),
//		}
//
//		kvs = append(kvs, kv)
//	}
//
//	return kvs
//}

//var _ = Describe("KvStore", func() {
//	var ro repository.KVStoreRepo
//	var uD *domain.KVStore
//	var uDList []*domain.KVStore
//	BeforeEach(func() {
//		// 这里的 Db 是 data_suite_test.go 文件里面定义的
//		ro = NewDBKVStore(Db, log.GetLogger())
//		// 这里你可以引入外部组装好的数据
//		uD = KVStore("key", "value")
//
//		uDList = KVStoreList(10)
//	})
//
//	// 读写kv
//	It("KVStoreSetGet", func() {
//		err := ro.Set(ctx, uD)
//		Ω(err).ShouldNot(HaveOccurred())
//		value, err := ro.Get(ctx, uD)
//		Ω(err).ShouldNot(HaveOccurred())
//		Ω(value).Should(Equal(uD.Value))
//	})
//
//	// 写->超时->读失败
//	It("KVStoreSetExpiredGet", func() {
//		err := ro.Set(ctx, uD)
//		Ω(err).ShouldNot(HaveOccurred())
//
//		// 等待两秒超时
//		time.Sleep(3 * time.Second)
//
//		// 读失败
//		_, err = ro.Get(ctx, uD)
//		Ω(err).Should(HaveOccurred())
//
//		kvs, err := ro.ListAll(ctx)
//		Ω(err).ShouldNot(HaveOccurred())
//		Ω(len(kvs)).Should(Equal(0))
//	})
//
//	// 批量操作
//	It("KVStoreBatchOp", func() {
//		uDList = KVStoreList(10)
//		for _, kv := range uDList {
//			err := ro.Set(ctx, kv)
//			Ω(err).ShouldNot(HaveOccurred())
//		}
//		kvs, err := ro.ListAll(ctx)
//		Ω(err).ShouldNot(HaveOccurred())
//		Ω(len(kvs)).Should(Equal(len(uDList)))
//		for i, kv := range kvs {
//			KvStoreEqual(kv, uDList[i])
//		}
//
//		time.Sleep(3 * time.Second)
//
//		kvs, err = ro.ListAll(ctx)
//		Ω(err).ShouldNot(HaveOccurred())
//		Ω(len(kvs)).Should(Equal(len(uDList) / 2))
//		for i, kv := range kvs {
//			KvStoreEqual(kv, uDList[5+i])
//		}
//	})
//
//	// 写/删除kv
//	It("KVStoreSetDelete", func() {
//		err := ro.Set(ctx, uD)
//		Ω(err).ShouldNot(HaveOccurred())
//		ro.Delete(ctx, uD)
//	})
//
//	// 写/CAP,kv
//	It("KVStoreSetCAP", func() {
//		uD2 := KVStore("key", "value2")
//		err := ro.Set(ctx, uD)
//		Ω(err).ShouldNot(HaveOccurred())
//		ok, err := ro.Cas(ctx, uD2, uD)
//		Ω(err).ShouldNot(HaveOccurred())
//		Ω(ok).Should(BeTrue())
//	})
//})

//func KvStoreEqual(a *domain.KVStore, b *domain.KVStore) {
//	Ω(a.Repo).Should(Equal(b.Repo))
//	Ω(a.Key).Should(Equal(b.Key))
//	Ω(a.Value).Should(Equal(b.Value))
//}

func TestKvStore(t *testing.T) {
	kvRepo := NewDBKVStore(mySQLDb, log.GetLogger())
	kvPairss, err := kvRepo.ListAll(ctx)
	assert.Nil(t, err)
	for _, kvPairs := range kvPairss {
		kvRepo.Delete(ctx, kvPairs)
	}
	err = kvRepo.Set(ctx, &domain.KVStore{
		Repo:        "repo1",
		Key:         "key1",
		Value:       `{"value1"}`,
		ExpiredTime: time.Now().Add(10 * time.Minute),
	})
	assert.Nil(t, err)
	err = kvRepo.Set(ctx, &domain.KVStore{
		Repo:        "repo1",
		Key:         "key2",
		Value:       `{"value2"}`,
		ExpiredTime: time.Now().Add(10 * time.Minute),
	})
	assert.Nil(t, err)
	v, err := kvRepo.Get(ctx, &domain.KVStore{
		Repo: "repo1",
		Key:  "key1",
	})
	assert.Nil(t, err)
	assert.Equal(t, `{"value1"}`, v)
	kvPairs, err := kvRepo.List(ctx, "repo1", "key1")
	assert.Nil(t, err)
	assert.Equal(t, 1, len(kvPairs))
	kvRepo.Delete(ctx, &domain.KVStore{
		Repo: "repo1",
		Key:  "key2",
	})
	kvPairss, err = kvRepo.ListAll(ctx)
	assert.Nil(t, err)
	assert.Equal(t, 1, len(kvPairss))
	kvPairsss, err := kvRepo.ListLike(ctx, "repo1", "")
	assert.Nil(t, err)
	assert.Equal(t, 1, len(kvPairsss))
}
