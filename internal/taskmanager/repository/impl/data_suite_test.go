package impl

import (
	"database/sql"

	"context"
	"testing"

	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/repository"

	"github.com/onsi/ginkgo/v2"
	"github.com/onsi/gomega"
)

// 测试 data 方法
func TestData(t *testing.T) {
	//  Ginkgo 测试通过调用 Fail(description string) 功能来表示失败
	// 使用 RegisterFailHandler 将此函数传递给 Gomega 。这是 Ginkgo 和 Gomega 之间的唯一连接点
	gomega.RegisterFailHandler(ginkgo.Fail)
	// 通知 Ginkgo 启动测试套件。如果您的任何 specs 失败，Ginkgo 将自动使 testing.T 失败。
	ginkgo.RunSpecs(t, "test biz data ")
}

var cleaner func()      // 定义删除 mysql 容器的回调函数
var Db *repository.Data // 用于测试的 data
var ctx context.Context // 上下文

// initialize  AutoMigrate gorm 自动建表的方法
//func initialize(db *gorm.DB) error {
//	err := db.AutoMigrate(
//		&data.Worker{},
//	)
//	return errors.WithStack(err)
//}

// ginkgo 使用 BeforeEach 为您的 Specs 设置状态
var _ = ginkgo.BeforeSuite(func() {
	// 执行测试数据库操作之前，链接之前 docker 容器创建的 mysql
	//con, f := DockerMysql("mysql", "latest")
	//cleaner = f // 测试完成，关闭容器的回调方法
	//conn := "root:88888888@tcp(localhost:3306)/taskmanager?charset=utf8mb4&parseTime=True&loc=Local"

	conn := "root:secret@tcp(localhost:3306)/mysql?parseTime=true"
	_, err := sql.Open("mysql", conn)

	config := &conf.Data{Database: &conf.Data_Database{Driver: "mysql", Source: conn, AutoMigrate: true}}
	db, _ := repository.NewDB(config, &conf.LiveTransCodeConfig{})
	mySQLDb, _, err := repository.NewData(nil, db, nil, config)
	if err != nil {
		return
	}
	Db = mySQLDb
	//err = initialize(db)
	//if err != nil {
	//	return
	//}
	gomega.Expect(err).NotTo(gomega.HaveOccurred())
})

// 测试结束后 通过回调函数，关闭并删除 docker 创建的容器
var _ = ginkgo.AfterSuite(func() {
	//cleaner()
})
