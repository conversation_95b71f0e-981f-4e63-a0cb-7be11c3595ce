package impl

import (
	"context"
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/domain"
	"mpp/internal/taskmanager/repository"
	v1 "proto.mpp/api/common/v1"
	"time"
)

type taskSentinelConfigRepo struct {
	data *repository.Data
	log  *log.Helper
}

func NewTaskSentinelConfigRepoRepo(data *repository.Data, logger log.Logger) repository.TaskSentinelConfigRepo {
	return &taskSentinelConfigRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// 创建
func (r *taskSentinelConfigRepo) Create(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) error {
	dbtaskSentinelConfig, err := ToTaskSentinelConfigFromModel(taskSentinelConfig)
	if err != nil {
		return err
	}
	result := r.data.GetDB().WithContext(context.TODO()).Exec(
		"INSERT IGNORE INTO task_sentinel_configs(gmt_create, gmt_modified, sentinel_config_id , sentinel_status, user_id, pipeline_id,"+
			" task_type, product,engine_model,tag,sentinel_config)"+
			" VALUES(now(), now(), ?, ?, ?, ?,"+
			" ?, ?, ? , ? , ?) ",
		dbtaskSentinelConfig.SentinelConfigId, dbtaskSentinelConfig.SentinelStatus, dbtaskSentinelConfig.UserId, dbtaskSentinelConfig.PipelineId,
		dbtaskSentinelConfig.TaskType, dbtaskSentinelConfig.Product, dbtaskSentinelConfig.EngineModel, dbtaskSentinelConfig.Tag, dbtaskSentinelConfig.SentinelConfig,
	)

	if result.Error != nil {
		log.Errorf("create taskSentinelConfigRepo failed, taskSentinelConfig:%v error: %v", taskSentinelConfig, result.Error)
		return result.Error
	}

	return nil
}

func (r *taskSentinelConfigRepo) InsertUpdateOnDuplicate(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) error {
	dbtaskSentinelConfig, err := ToTaskSentinelConfigFromModel(taskSentinelConfig)
	if err != nil {
		return err
	}
	result := r.data.GetDB().WithContext(context.TODO()).Exec(
		"INSERT INTO task_sentinel_configs(gmt_create, gmt_modified, sentinel_config_id , sentinel_status, user_id, pipeline_id,"+
			" task_type, product,engine_model,tag,sentinel_config)"+
			" VALUES(now(), now(), ?, ?, ?, ?,"+
			" ?, ?, ? , ? , ?) ON DUPLICATE KEY UPDATE "+
			" gmt_modified = now(), sentinel_config = ?",
		dbtaskSentinelConfig.SentinelConfigId, dbtaskSentinelConfig.SentinelStatus, dbtaskSentinelConfig.UserId, dbtaskSentinelConfig.PipelineId,
		dbtaskSentinelConfig.TaskType, dbtaskSentinelConfig.Product, dbtaskSentinelConfig.EngineModel, dbtaskSentinelConfig.Tag, dbtaskSentinelConfig.SentinelConfig,
		dbtaskSentinelConfig.SentinelConfig,
	)
	if result.Error != nil {
		log.Errorf("create taskSentinelConfigRepo failed, taskSentinelConfig:%v error: %v", taskSentinelConfig, result.Error)
		return result.Error
	}
	return nil
}

func (r *taskSentinelConfigRepo) Delete(ctx context.Context, sentinelConfigId string) error {
	result := r.data.GetDB().WithContext(ctx).Exec("DELETE FROM task_sentinel_configs WHERE sentinel_config_id = ?", sentinelConfigId)
	if result.Error != nil {
		log.Errorf("delete taskSentinelConfigRepo failed, sentinelConfigId: %s, error: %v", sentinelConfigId, result.Error)
		return result.Error
	}
	return nil
}

func (r *taskSentinelConfigRepo) GetTaskSentinelConfig(ctx context.Context, sentinelConfigId string) (*models.TaskSentinelConfig, error) {
	dbTaskSentinelConfig := &domain.TaskSentinelConfig{}
	result := r.data.GetDB().WithContext(ctx).Model(&domain.TaskSentinelConfig{}).Where("sentinel_config_id = ?", sentinelConfigId).First(dbTaskSentinelConfig)
	if result.Error != nil {
		log.Errorf("GetTaskSentinelConfig failed, sentinelConfigId: %s, error: %v", sentinelConfigId, result.Error)
		return nil, result.Error
	}
	return ToTaskSentinelConfigModel(dbTaskSentinelConfig)
}

func (r *taskSentinelConfigRepo) ListActiveTaskSentinelConfig(ctx context.Context) ([]*models.TaskSentinelConfig, error) {
	var dbTaskSentinelConfigs []*domain.TaskSentinelConfig
	result := r.data.GetDB().WithContext(ctx).Model(&domain.TaskSentinelConfig{}).Where("sentinel_status = ?", models.SentinelStatusStateActive).Find(&dbTaskSentinelConfigs)
	if result.Error != nil {
		log.Errorf("ListActiveTaskSentinelConfig failed, error: %v", result.Error)
		return nil, result.Error
	}
	return ToTaskSentinelConfigModelList(dbTaskSentinelConfigs)
}

// 更新流控配置,不更新流控启用状态和当前任务量
func (r *taskSentinelConfigRepo) UpdateTaskSentinelConfig(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) (bool, error) {

	dbTaskSentinelConfig, err := ToTaskSentinelConfigFromModel(taskSentinelConfig)
	if err != nil {
		return false, err
	}

	data := map[string]interface{}{
		"GmtModified":    time.Now(),
		"SentinelConfig": dbTaskSentinelConfig.SentinelConfig,
		"CurrentTaskNum": dbTaskSentinelConfig.CurrentTaskNum,

		//"SentinelConfigId": dbTaskSentinelConfig.SentinelConfigId,
		//"UserId":           dbTaskSentinelConfig.UserId,
		//"PipelineId":       dbTaskSentinelConfig.PipelineId,
		//"TaskType":         dbTaskSentinelConfig.TaskType,
		//"Product":          dbTaskSentinelConfig.Product,
		//"EngineModel":      dbTaskSentinelConfig.EngineModel,
		//"Tag":              dbTaskSentinelConfig.Tag,
	}

	result := r.data.GetDB().WithContext(ctx).Model(&domain.TaskSentinelConfig{}).Where("sentinel_config_id = ?", taskSentinelConfig.SentinelConfigId).Updates(data)
	if result.Error != nil {
		log.Errorf("UpdateSentinelStatus failed, SentinelConfigId: %s, error: %v", taskSentinelConfig.SentinelConfigId, result.Error)
		return false, result.Error
	}

	if result.RowsAffected == 0 {
		log.Warnf("UpdateSentinelStatus not updated, no changes found for SentinelConfigId: %s", taskSentinelConfig.SentinelConfigId)
	}
	return true, nil
}

// 更新流控启用状态
func (r *taskSentinelConfigRepo) UpdateSentinelStatus(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) (bool, error) {

	data := map[string]interface{}{
		"SentinelStatus": taskSentinelConfig.SentinelStatus,
		"GmtModified":    time.Now(),
	}

	result := r.data.GetDB().WithContext(ctx).Model(&domain.TaskSentinelConfig{}).Where("sentinel_config_id = ?", taskSentinelConfig.SentinelConfigId).Updates(data)
	if result.Error != nil {
		log.Errorf("UpdateSentinelStatus failed, SentinelConfigId: %s, error: %v", taskSentinelConfig.SentinelConfigId, result.Error)
		return false, result.Error
	}

	if result.RowsAffected == 0 {
		log.Warnf("UpdateSentinelStatus not updated, no changes found for SentinelConfigId: %s", taskSentinelConfig.SentinelConfigId)
	}

	return true, nil
}

func ToTaskSentinelConfigFromModel(taskSentinelConfig *models.TaskSentinelConfig) (*domain.TaskSentinelConfig, error) {

	dbTaskSentinelConfig := &domain.TaskSentinelConfig{
		GmtCreate:        taskSentinelConfig.GmtCreate,
		GmtModified:      taskSentinelConfig.GmtModified,
		SentinelConfigId: taskSentinelConfig.SentinelConfigId,
		SentinelStatus:   int32(taskSentinelConfig.SentinelStatus),
		UserId:           taskSentinelConfig.UserId,
		PipelineId:       taskSentinelConfig.PipelineId,
		TaskType:         int32(taskSentinelConfig.TaskType),
		Tag:              taskSentinelConfig.Tag,
		Product:          taskSentinelConfig.Product,
		EngineModel:      taskSentinelConfig.EngineModel,
	}

	sentinelConfig, err := json.Marshal(taskSentinelConfig.SentinelConfig)
	if err != nil {
		return nil, err
	}
	dbTaskSentinelConfig.SentinelConfig = sentinelConfig

	return dbTaskSentinelConfig, nil
}
func ToTaskSentinelConfigModel(dbTaskSentinelConfig *domain.TaskSentinelConfig) (*models.TaskSentinelConfig, error) {

	taskSentinelConfig := &models.TaskSentinelConfig{
		GmtCreate:        dbTaskSentinelConfig.GmtCreate,
		GmtModified:      dbTaskSentinelConfig.GmtModified,
		SentinelConfigId: dbTaskSentinelConfig.SentinelConfigId,
		SentinelStatus:   models.SentinelStatus(dbTaskSentinelConfig.SentinelStatus),
		UserId:           dbTaskSentinelConfig.UserId,
		PipelineId:       dbTaskSentinelConfig.PipelineId,
		TaskType:         v1.TaskType(dbTaskSentinelConfig.TaskType),
		Tag:              dbTaskSentinelConfig.Tag,
		Product:          dbTaskSentinelConfig.Product,
		EngineModel:      dbTaskSentinelConfig.EngineModel,
	}

	if dbTaskSentinelConfig.SentinelConfig != nil {
		err := json.Unmarshal(dbTaskSentinelConfig.SentinelConfig, &taskSentinelConfig.SentinelConfig)
		if err != nil {
			return nil, err
		}
	}

	return taskSentinelConfig, nil
}

func ToTaskSentinelConfigModelList(dbTaskSentinelConfigs []*domain.TaskSentinelConfig) ([]*models.TaskSentinelConfig, error) {
	var taskSentinelConfigs []*models.TaskSentinelConfig
	for _, dbTaskSentinelConfig := range dbTaskSentinelConfigs {
		taskSentinelConfig, err := ToTaskSentinelConfigModel(dbTaskSentinelConfig)
		if err != nil {
			return nil, err
		}
		taskSentinelConfigs = append(taskSentinelConfigs, taskSentinelConfig)
	}
	return taskSentinelConfigs, nil
}
