package impl

import (
	"context"
	"testing"

	"mpp/internal/taskmanager/models/domain"

	"github.com/stretchr/testify/assert"
)

func TestDBSourceStreamTaskStore(t *testing.T) {
	sourceStreamTaskStore := NewDBSourceStreamTaskStore(mySQLDb, nil)
	sw := domain.SourceStreamTask{
		App:            "app",
		Domain:         "domain",
		Stream:         "engine_model",
		SourceStreamId: "test",
		TaskId:         "test",
	}
	c := context.Background()
	err := sourceStreamTaskStore.Insert(c, &sw)
	assert.Nil(t, err)
	result, err := sourceStreamTaskStore.ListBySourceStreamId(c, "test")
	assert.Nil(t, err)
	assert.Equal(t, result[0].App, "app")
}
