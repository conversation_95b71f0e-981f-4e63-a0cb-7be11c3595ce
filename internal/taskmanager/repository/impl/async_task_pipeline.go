package impl

import (
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/domain"
	"mpp/internal/taskmanager/repository"
	v1 "proto.mpp/api/common/v1"
	"time"
)

type asyncTaskPipelineRepo struct {
	data *repository.Data
	log  *log.Helper
}

// NewAsyncTaskPipeline.
func NewAsyncTaskPipelineRepo(data *repository.Data, logger log.Logger) repository.AsyncTaskPipelineRepo {
	return &asyncTaskPipelineRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (r *asyncTaskPipelineRepo) Create(ctx context.Context, pipeline *models.Pipeline) (bool, error) {

	result := r.data.GetDB().WithContext(context.TODO()).Exec(
		"INSERT IGNORE INTO async_task_pipelines(gmt_create, gmt_modified, user_id, pipeline_id,"+
			" status, schedule_level,`type`,reserved_instance_id,real_user_id)"+
			" VALUES(now(), now(), ?, ?,"+
			" ?, ?, ? , ?, ?) ",
		pipeline.UserId, pipeline.PipelineId,
		pipeline.Status, pipeline.ScheduleLevel, pipeline.Type, pipeline.ReservedInstanceId, pipeline.RealUserId,
	)

	if result.Error != nil {
		log.Errorf("create asyncTaskPipelineRepo failed, Pipeline:%v error: %v", pipeline, result.Error)
		return false, result.Error
	}

	return true, nil
}

// Delete 删除异步任务管道
func (r *asyncTaskPipelineRepo) Delete(ctx context.Context, pipelineId string, pipelineType v1.PipelineType) (bool, error) {
	result := r.data.GetDB().WithContext(ctx).Model(&domain.AsyncTaskPipeline{}).Delete(
		&models.Pipeline{
			PipelineId: pipelineId,
			Type:       pipelineType,
		})
	if result.Error != nil {
		log.Errorf("delete asyncTaskPipelineRepo failed, PipelineId: %s, error: %v", pipelineId, result.Error)
		return false, result.Error
	}

	if result.RowsAffected == 0 {
		log.Warnf("asyncTaskPipelineRepo not found for PipelineId: %s", pipelineId)
	}

	return true, nil
}

// 更新异步任务管道
func (r *asyncTaskPipelineRepo) Update(ctx context.Context, pipeline *models.Pipeline) (bool, error) {

	result := r.data.GetDB().WithContext(ctx).Model(&domain.AsyncTaskPipeline{}).Save(pipeline)
	if result.Error != nil {
		log.Errorf("update asyncTaskPipelineRepo failed, Pipeline: %v, error: %v", pipeline, result.Error)
		return false, result.Error
	}

	if result.RowsAffected == 0 {
		log.Warnf("asyncTaskPipelineRepo not updated, no changes found for PipelineId: %s", pipeline.PipelineId)
	}

	return true, nil
}

// 更新管道状态
func (r *asyncTaskPipelineRepo) UpdatePipelineStatus(ctx context.Context, pipeline *models.Pipeline) (bool, error) {

	data := map[string]interface{}{
		"Status":      int32(pipeline.Status),
		"GmtModified": time.Now(),
	}

	result := r.data.GetDB().WithContext(ctx).Model(&domain.AsyncTaskPipeline{}).Where("pipeline_id = ? and type = ?", pipeline.PipelineId, pipeline.Type).Updates(data)
	if result.Error != nil {
		log.Errorf("UpdatePipelineStatus failed, PipelineId: %s, error: %v , pipeline:%v", pipeline.PipelineId, result.Error, pipeline)
		return false, result.Error
	}

	if result.RowsAffected == 0 {
		log.Warnf("UpdatePipelineStatus not updated, no changes found for PipelineId: %s", pipeline.PipelineId)
	}

	return true, nil
}

// 更新管道服务级别
func (r *asyncTaskPipelineRepo) UpdatePipelineScheduleLevel(ctx context.Context, pipeline *models.Pipeline) (bool, error) {

	data := map[string]interface{}{
		"ScheduleLevel": int32(pipeline.ScheduleLevel),
		"GmtModified":   time.Now(),
	}

	result := r.data.GetDB().WithContext(ctx).Model(&domain.AsyncTaskPipeline{}).Where("pipeline_id = ? and type = ?", pipeline.PipelineId, pipeline.Type).Updates(data)
	if result.Error != nil {
		log.Errorf("UpdatePipelineScheduleLevel failed, PipelineId: %s, error: %v , pipeline:%v", pipeline.PipelineId, result.Error, pipeline)
		return false, result.Error
	}

	if result.RowsAffected == 0 {
		log.Warnf("UpdatePipelineScheduleLevel not updated, no changes found for PipelineId: %s", pipeline.PipelineId)
	}

	return true, nil
}

// 更新管道服务级别
func (r *asyncTaskPipelineRepo) UpdatePipelineScheduleLevelByUserId(ctx context.Context, pipeline *models.Pipeline) (bool, error) {

	data := map[string]interface{}{
		"ScheduleLevel": int32(pipeline.ScheduleLevel),
		"GmtModified":   time.Now(),
	}

	result := r.data.GetDB().WithContext(ctx).Model(&domain.AsyncTaskPipeline{}).Where("user_id = ?", pipeline.UserId).Updates(data)
	if result.Error != nil {
		log.Errorf("UpdatePipelineScheduleLevelByUserId failed, PipelineId: %s, error: %v , pipeline:%v", pipeline.PipelineId, result.Error, pipeline)
		return false, result.Error
	}

	if result.RowsAffected == 0 {
		log.Warnf("UpdatePipelineScheduleLevelByUserId not updated, no changes found for PipelineId: %s", pipeline.PipelineId)
	}

	return true, nil
}

// Query 查询异步任务管道
func (r *asyncTaskPipelineRepo) FindByPipelineAndType(ctx context.Context, pipelineId string, pipelineType v1.PipelineType) (*models.Pipeline, error) {
	var pipeline *domain.AsyncTaskPipeline
	result := r.data.GetDB().WithContext(ctx).Where("pipeline_id = ? and type = ?", pipelineId, pipelineType).Take(&pipeline)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, models.ErrPipelineNotFoundError
		}

		if !errors.Is(result.Error, context.Canceled) {
			r.log.WithContext(ctx).Errorf("get asyncTaskPipelineRepo failed, PipelineId: %s,pipelineType:%s, error: %v", pipelineId, pipelineType, result.Error)
		}
		return nil, result.Error

	}
	return ToPipelineModel(pipeline), nil
}

func (r *asyncTaskPipelineRepo) QueryByUserId(ctx context.Context, userId string) ([]*models.Pipeline, error) {

	var pipelines []*domain.AsyncTaskPipeline
	result := r.data.GetDB().WithContext(ctx).Where("user_id = ?", userId).Find(&pipelines)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("QueryByUserId failed, UserId: %s, error: %v", userId, result.Error)
		return nil, result.Error
	}
	return ToPipelineModels(pipelines), nil
}

func ToPipelineFromModel(pipeline *models.Pipeline) *domain.AsyncTaskPipeline {

	dbPipeline := &domain.AsyncTaskPipeline{
		PipelineId:         pipeline.PipelineId,
		UserId:             pipeline.UserId,
		Status:             int32(pipeline.Status),
		ScheduleLevel:      int32(pipeline.ScheduleLevel),
		Type:               int32(pipeline.Type),
		ReservedInstanceId: pipeline.ReservedInstanceId,
		RealUserId:         pipeline.RealUserId,
		GmtModified:        pipeline.LastModified,
		GmtCreate:          pipeline.GmtCreate,
	}

	return dbPipeline
}
func ToPipelineModel(dbPipeline *domain.AsyncTaskPipeline) *models.Pipeline {

	pipeline := &models.Pipeline{
		PipelineId:         dbPipeline.PipelineId,
		UserId:             dbPipeline.UserId,
		Status:             v1.PipelineStatus(dbPipeline.Status),
		ScheduleLevel:      v1.PipelineSchedulerLevel(dbPipeline.ScheduleLevel),
		Type:               v1.PipelineType(dbPipeline.Type),
		ReservedInstanceId: dbPipeline.ReservedInstanceId,
		RealUserId:         dbPipeline.RealUserId,
		LastModified:       dbPipeline.GmtModified,
		GmtCreate:          dbPipeline.GmtCreate,
	}
	return pipeline
}
func ToPipelineModels(dbPipelines []*domain.AsyncTaskPipeline) []*models.Pipeline {
	var pipelines []*models.Pipeline
	for _, dbPipeline := range dbPipelines {
		pipelines = append(pipelines, ToPipelineModel(dbPipeline))
	}
	return pipelines
}
