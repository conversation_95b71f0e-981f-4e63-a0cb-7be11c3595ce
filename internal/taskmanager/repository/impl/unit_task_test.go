package impl

import (
	"context"
	"testing"
	"time"

	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/domain"

	pb "proto.mpp/api/taskmanager/v1"

	"github.com/stretchr/testify/assert"
)

func TestDBUnitTaskStore(t *testing.T) {
	unitTaskStore := NewUnitTaskRepo(mySQLDb, nil)
	c := context.Background()
	sw := domain.UnitTask{
		TaskId: "197329074017940",
		Unit:   "test",
		Status: 1,
		Extend: nil,
	}
	err := unitTaskStore.InsertUnitTask(c, &sw)
	assert.Nil(t, err)
	err = unitTaskStore.UpdateUnitTaskStatus(c, "197329074017940")
	assert.Nil(t, err)
	task, err := unitTaskStore.GetUnitTask(c, "197329074017940")
	assert.Nil(t, err)
	assert.Equal(t, task.Status, int8(0))
	tasks, err := unitTaskStore.GetUnitTaskAll(c, "test")
	assert.Nil(t, err)
	assert.Equal(t, len(tasks), 1)
	mockTask := &models.Task{
		Product:         "example-product",
		TaskId:          "197329074017940",
		JobId:           "job-001",
		UserId:          "user-001",
		PipelineId:      "pipeline-001",
		BizStatus:       1,
		InternalStatus:  1,
		Worker:          &models.Worker{Id: "worker-001", Ip: "***********", Port: 8080},
		UserData:        "example-user-data",
		Param:           &models.TaskEngineParam{Param: "value1"},
		EngineModel:     "example-engine",
		Tag:             "example-tag",
		Trace:           "example-trace",
		RequestResource: &models.RequestResource{Quota: map[string]int64{"cpu": 2, "memory": 4}},
		Result:          &models.TaskResult{Message: "success"},
		TaskType:        1,
		MigrateConfig:   &pb.MigrateConfig{MaxRuntime: 1},
		MigrateTimes:    0,
		Notify:          "http://example.com/notify",
		Dependencies:    []models.Dependency{{TaskId: "197329074017940", Fields: map[string]string{"field1": "value1"}}},
		LastModified:    time.Now(),
		GmtCreate:       time.Now(),
		Domain:          "example-domain",
		Metadata:        map[string]string{"metadata1": "value1"},
		EngineEntrance:  map[string]string{"entrance1": "value1"},
		ScheduleParams:  "{\"schedule\": \"example-schedule\"}",
		DagGraph:        "{\"dag\": \"example-dag\"}",
	}
	dbTask, err := toTaskModel(mockTask)
	assert.Nil(t, err)
	err = unitTaskStore.SyncTask(c, dbTask)
	assert.Nil(t, err)
	err = unitTaskStore.UpdateTaskStatus(c, "197329074017940", 1, 1)
	assert.Nil(t, err)
}
