package impl

import (
	"github.com/google/uuid"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"mpp/internal/taskmanager/biz/leader"
	"time"
)

var _ = Describe("Leader", func() {
	var (
		leaderRepo leader.LeaderRepo
	)
	BeforeEach(func() {
		leaderRepo = NewLeaderRepo(Db, nil)
	})

	It("ShouldCreateSuccessAndQueryExpect", func() {
		leader := leader.Leader{
			Key:      uuid.NewString(),
			Instance: uuid.NewString(),
		}
		err := leaderRepo.Create(leader)
		Expect(err).Should(BeNil())

		leader2, err := leaderRepo.QueryLeader(leader.Key)
		Expect(err).Should(BeNil())
		Expect(leader2.Instance).Should(Equal(leader.Instance))
		Expect(leader2.Key).Should(Equal(leader.Key))
	})

	It("CreateDuplicateWillIgnore", func() {
		leader1 := leader.Leader{
			Key:      uuid.NewString(),
			Instance: uuid.NewString(),
		}
		err := leaderRepo.Create(leader1)
		Expect(err).Should(BeNil())

		leader2 := leader.Leader{
			Key:      uuid.NewString(),
			Instance: uuid.NewString(),
		}

		err = leaderRepo.Create(leader2)
		Expect(err).Should(BeNil())

		leader3, err := leaderRepo.QueryLeader(leader1.Key)
		Expect(err).Should(BeNil())
		Expect(leader3.Instance).Should(Equal(leader1.Instance))
		Expect(leader3.Key).Should(Equal(leader1.Key))
	})

	It("KeepAlive", func() {
		leader1 := leader.Leader{
			Key:      uuid.NewString(),
			Instance: uuid.NewString(),
		}
		err := leaderRepo.Create(leader1)
		Expect(err).Should(BeNil())

		success, err := leaderRepo.KeepAlive(leader1)
		Expect(err).Should(BeNil())
		Expect(success).Should(BeTrue())

		anotherInstanceLeader := leader.Leader{
			Key:      leader1.Key,
			Instance: uuid.NewString(),
		}
		success, err = leaderRepo.KeepAlive(anotherInstanceLeader)
		Expect(err).Should(BeNil())
		Expect(success).Should(BeFalse())
	})

	It("UpdateLeader", func() {
		leader1 := leader.Leader{
			Key:      uuid.NewString(),
			Instance: uuid.NewString(),
		}
		err := leaderRepo.Create(leader1)
		Expect(err).Should(BeNil())

		time.Sleep(time.Second * 3)

		anotherInstanceLeader := leader.Leader{
			Key:      leader1.Key,
			Instance: uuid.NewString(),
		}
		success, err := leaderRepo.UpdateLeader(anotherInstanceLeader, 2)
		Expect(err).Should(BeNil())
		Expect(success).Should(BeTrue())

		leader2, err := leaderRepo.QueryLeader(leader1.Key)
		Expect(err).Should(BeNil())
		Expect(leader2.Instance).Should(Equal(anotherInstanceLeader.Instance))

		success, err = leaderRepo.UpdateLeader(anotherInstanceLeader, 1)
		Expect(err).Should(BeNil())
		Expect(success).Should(BeFalse())
	})
})
