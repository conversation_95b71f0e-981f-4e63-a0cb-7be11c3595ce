package impl

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"mpp/internal/taskmanager/models/domain"
	"mpp/internal/taskmanager/repository"
	"time"
)

/**
 * @Author: qinxin
 * @Date: 2024/12/24 20:46
 * @Desc:
 * @Version 1.0
 */

type MixGrayConfigRepo struct {
	data *repository.Data
	log  *log.Helper
}

func NewMixGrayConfigRepo(data *repository.Data, logger log.Logger) repository.MixGrayConfigRepo {
	mixGrayConfigRepo := &MixGrayConfigRepo{
		data: data,
		log:  log.NewHelper(logger),
	}

	return mixGrayConfigRepo
}

// // Create 插入一条新的 MixGrayConfig 记录
func (m *MixGrayConfigRepo) Create(ctx context.Context, mixGrayConfig *domain.MixGrayConfig) error {
	log.Context(ctx).Infof("Create MixGrayConfig: %s", mixGrayConfig.RuleName)
	if mixGrayConfig.GmtCreate.IsZero() {
		mixGrayConfig.GmtCreate = time.Now()
	}
	if mixGrayConfig.GmtModified.IsZero() {
		mixGrayConfig.GmtModified = time.Now()
	}
	db := m.data.GetDB()
	if err := db.Create(mixGrayConfig).Error; err != nil {
		m.log.Errorf("failed to create mix gray ,mixGrayConfig: %+v, err:%v", mixGrayConfig, err)
		return err
	}
	return nil
}

// // Delete 根据 ruleName 删除一条 MixGrayConfig 记录
func (m *MixGrayConfigRepo) Delete(ctx context.Context, ruleName string) error {
	db := m.data.GetDB()
	if err := db.Where("rule_name = ?", ruleName).Delete(&domain.MixGrayConfig{}).Error; err != nil {
		m.log.Errorf("failed to delete mix gray config: %v", err)
		return err
	}
	return nil
}

func (m *MixGrayConfigRepo) InsertUpdateOnDuplicate(ctx context.Context, mixGrayConfig *domain.MixGrayConfig) error {

	db := m.data.GetDB()

	//报错Error 1054 (42S22): Unknown column 'mix_configs' in 'field list'","metadata":{}}
	err := db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "rule_name"}},
		DoUpdates: clause.AssignmentColumns([]string{"config_status", "extends", "mix_configs", "mix_ratio", "priority", "rule_configs", "uid"}),
	}).Create(mixGrayConfig).Error

	if err != nil {
		m.log.WithContext(ctx).Errorf("failed to insert or update mix gray config: %v", err)
		return err
	}
	return nil
}

func (m *MixGrayConfigRepo) Update(ctx context.Context, mixGrayConfig *domain.MixGrayConfig) error {
	db := m.data.GetDB()
	//更新修改时间
	mixGrayConfig.GmtModified = time.Now()
	result := db.Model(&domain.MixGrayConfig{}).Where("rule_name = ?", mixGrayConfig.RuleName).Updates(mixGrayConfig)
	if result.Error != nil {
		m.log.WithContext(ctx).Errorf("failed to update mix gray config: %v", result.Error)
		return result.Error
	}
	return nil
}

func (m *MixGrayConfigRepo) UpdateMixRatio(ctx context.Context, mixGrayConfig *domain.MixGrayConfig) error {
	db := m.data.GetDB()
	gmtModified := time.Now()
	//更新修改时间
	data := map[string]interface{}{
		"MixRatio":    float32(mixGrayConfig.MixRatio),
		"GmtModified": gmtModified,
	}
	mixGrayConfig.GmtModified = gmtModified
	result := db.Model(&domain.MixGrayConfig{}).Where("rule_name = ?", mixGrayConfig.RuleName).Updates(data)
	if result.Error != nil {
		m.log.WithContext(ctx).Errorf("failed to update mix ratio, error: %v", result.Error)
		return result.Error
	}
	return nil
}

func (m *MixGrayConfigRepo) GetMixGrayConfigByName(ctx context.Context, ruleName string) (*domain.MixGrayConfig, error) {
	db := m.data.GetDB()
	var mixGrayConfig domain.MixGrayConfig
	if err := db.Where("rule_name = ?", ruleName).First(&mixGrayConfig).Error; err != nil {
		//如果找不到记录，返回nil
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		m.log.Errorf("failed to get mix gray config: %v", err)
		return nil, err
	}
	return &mixGrayConfig, nil
}

func (m *MixGrayConfigRepo) ListActiveMixGrayConfig(ctx context.Context) ([]*domain.MixGrayConfig, error) {
	db := m.data.GetDB()
	var mixGrayConfigs []*domain.MixGrayConfig
	if err := db.Where("config_status = ?", 1).Find(&mixGrayConfigs).Error; err != nil {
		m.log.Errorf("failed to list active mix gray configs: %v", err)
		return nil, err
	}
	return mixGrayConfigs, nil
}

// List 列出所有 MixGrayConfig 记录
func (m *MixGrayConfigRepo) List(ctx context.Context) ([]*domain.MixGrayConfig, error) {
	db := m.data.GetDB()
	var mixGrayConfigs []*domain.MixGrayConfig

	if err := db.Find(&mixGrayConfigs).Error; err != nil {
		m.log.Errorf("failed to list all mix gray configs: %v", err)
		return nil, err
	}
	return mixGrayConfigs, nil
}

// Query 根据各种条件查询 MixGrayConfig 记录
//func (m *MixGrayConfigRepo) Query(ctx context.Context, conditions map[string]interface{}) ([]*models.MixGrayConfig, error) {
//	db := m.data.GetDB()
//	var mixGrayConfigs []*models.MixGrayConfig
//	query := db.Model(&models.MixGrayConfig{})
//	for key, value := range conditions {
//		query = query.Where(key+" = ?", value)
//	}
//
//	if err := query.Find(&mixGrayConfigs).Error; err != nil {
//		m.log.Errorf("failed to query mix gray configs: %v", err)
//		return nil, err
//	}
//	return mixGrayConfigs, nil
//}
