package impl

import (
	"context"

	"mpp/internal/taskmanager/models/domain"
	"mpp/internal/taskmanager/repository"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
)

type switchConfigRepo struct {
	data *repository.Data
	log  *log.Helper
}

func NewSwitchConfigRepo(data *repository.Data, logger log.Logger) repository.SwitchConfigRepo {
	return &switchConfigRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (s switchConfigRepo) Insert(ctx context.Context, config *repository.SwitchConfig) error {
	result := s.data.GetDB().Model(&domain.SwitchConfig{}).Table("switch_configs").WithContext(ctx).Exec(
		"INSERT INTO "+"switch_configs (gmt_create, gmt_modified, product, engine_model, tag, domain, app, token, switch_config, backup_config, extend, status) "+
			"VALUES(now(), now(), ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)"+
			" ON DUPLICATE KEY UPDATE "+
			" gmt_modified = now(), switch_config = ?, backup_config = ?, status = 1",
		config.Product, config.EngineModel, config.Tag, config.Domain, config.App, config.Token, config.SwitchConfig, config.BackupConfig, config.Extend,
		config.SwitchConfig, config.BackupConfig,
	)
	if result.Error != nil {
		s.log.WithContext(ctx).Warnf("set switchConfig failed, err:%v", result)
		return result.Error
	}

	return nil
}

func (s switchConfigRepo) Query(ctx context.Context, config *repository.SwitchConfig) ([]*repository.SwitchConfig, error) {
	var configs []domain.SwitchConfig
	// todo 能不能运行，可能需要.model
	result := s.data.GetDB().WithContext(ctx).Where(config).Find(&configs)
	if result.Error != nil {
		s.log.WithContext(ctx).Warnf("query switchConfig failed, err:%v", result)
		return nil, result.Error
	}
	var switchConfigs []*repository.SwitchConfig
	for _, c := range configs {
		switchConfig := repository.SwitchConfig{}
		err := copier.Copy(&switchConfig, &c)
		if nil != err {
			s.log.WithContext(ctx).Warnf("copy switchConfig failed, err:%v", err)
			return nil, err
		}
		switchConfigs = append(switchConfigs, &switchConfig)
	}
	return switchConfigs, nil
}

func (s switchConfigRepo) Delete(ctx context.Context, config *repository.SwitchConfig) {
	s.data.GetDB().WithContext(ctx).Where(config).Delete(&repository.SwitchConfig{})
}

func (s switchConfigRepo) ListAll(ctx context.Context) ([]*repository.SwitchConfig, error) {
	var configs []domain.SwitchConfig
	result := s.data.GetDB().WithContext(ctx).Where("status = 1").Find(&configs)
	if result.Error != nil {
		s.log.WithContext(ctx).Warnf("list switchConfig failed, err:%v", result)
		return nil, result.Error
	}
	var switchConfigs []*repository.SwitchConfig
	for _, c := range configs {
		switchConfig := repository.SwitchConfig{}
		err := copier.Copy(&switchConfig, &c)
		if nil != err {
			s.log.WithContext(ctx).Warnf("copy switchConfig failed, err:%v", err)
			return nil, err
		}
		switchConfigs = append(switchConfigs, &switchConfig)
	}
	return switchConfigs, nil
}
