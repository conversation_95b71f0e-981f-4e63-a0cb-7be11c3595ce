package impl

import (
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"
	v1 "proto.mpp/api/common/v1"
	"time"

	"github.com/google/uuid"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("pipeline repo create_update_query", func() {
	var (
		pipelineRepo repository.AsyncTaskPipelineRepo
	)
	BeforeEach(func() {
		pipelineRepo = NewAsyncTaskPipelineRepo(Db, nil)
	})

	It("SaveAndQueryByIdAndType", func() {
		//管道创建
		pipeline := newPipeline()
		_, err := pipelineRepo.Create(ctx, pipeline)
		Expect(err).Should(BeNil())

		querySaveResult, err := pipelineRepo.FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type)
		Expect(err).Should(BeNil())
		ShouldPipelineEqual(pipeline, querySaveResult)

		//任务状态更新
		pipeline.Status = v1.PipelineStatus_PAUSED
		_, err = pipelineRepo.UpdatePipelineStatus(ctx, pipeline)
		Expect(err).Should(BeNil())
		querySaveResult, err = pipelineRepo.FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type)
		Expect(err).Should(BeNil())
		ShouldPipelineEqual(pipeline, querySaveResult)

		pipeline.Status = v1.PipelineStatus_ACTIVE
		_, err = pipelineRepo.UpdatePipelineStatus(ctx, pipeline)
		Expect(err).Should(BeNil())
		querySaveResult, err = pipelineRepo.FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type)
		Expect(err).Should(BeNil())
		ShouldPipelineEqual(pipeline, querySaveResult)

		//任务schedulelevel更新
		pipeline.ScheduleLevel = v1.PipelineSchedulerLevel_LOW_PRIORITY
		_, err = pipelineRepo.UpdatePipelineScheduleLevel(ctx, pipeline)
		Expect(err).Should(BeNil())
		querySaveResult, err = pipelineRepo.FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type)
		Expect(err).Should(BeNil())
		ShouldPipelineEqual(pipeline, querySaveResult)

		//任务schedulelevel根据userId批量更新
		pipeline.ScheduleLevel = v1.PipelineSchedulerLevel_URGENT
		_, err = pipelineRepo.UpdatePipelineScheduleLevelByUserId(ctx, pipeline)
		Expect(err).Should(BeNil())
		querySaveResult, err = pipelineRepo.FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type)
		Expect(err).Should(BeNil())
		ShouldPipelineEqual(pipeline, querySaveResult)
	})

})

func ShouldPipelineEqual(pipeline1, pipeline2 *models.Pipeline) {
	Ω(pipeline1.PipelineId).To(Equal(pipeline2.PipelineId))
	Ω(pipeline1.UserId).To(Equal(pipeline2.UserId))
	Ω(pipeline1.Type).To(Equal(pipeline2.Type))
	Ω(pipeline1.Status).To(Equal(pipeline2.Status))
	Ω(pipeline1.ScheduleLevel).To(Equal(pipeline2.ScheduleLevel))
	Ω(pipeline1.ReservedInstanceId).To(Equal(pipeline2.ReservedInstanceId))
	Ω(pipeline1.RealUserId).To(Equal(pipeline2.RealUserId))

	Ω(pipeline1.LastModified).To(BeTemporally("~", pipeline2.LastModified, time.Second))
	Ω(pipeline1.GmtCreate).To(BeTemporally("~", pipeline2.GmtCreate, time.Second))
}

func newPipeline() *models.Pipeline {
	pipeline := &models.Pipeline{
		UserId:       "123456",
		PipelineId:   uuid.NewString(),
		Type:         v1.PipelineType_SHARED,
		Status:       v1.PipelineStatus_ACTIVE,
		GmtCreate:    time.Now(),
		LastModified: time.Now(),
	}

	return pipeline
}
