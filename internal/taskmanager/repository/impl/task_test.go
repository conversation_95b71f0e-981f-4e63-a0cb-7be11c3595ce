package impl

import (
	"time"

	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"

	v1 "proto.mpp/api/common/v1"
	pb "proto.mpp/api/taskmanager/v1"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("tasker repo create_update_query", func() {
	var (
		taskRepo repository.TaskRepo
	)
	//初始化查询实例 添加表名
	BeforeEach(func() {
		taskRepo = NewTaskRepo(Db, nil)
		taskRepo = taskRepo.CloneWithSpecifiedTable(models.StreamTaskTableName)
	})

	It("SaveAndQueryById", func() {
		task := newTask()
		err := taskRepo.InsertUpdateOnDuplicate(ctx, task)
		Expect(err).Should(BeNil())

		querySaveResult, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult)
	})

	It("SaveDuplicatedAndQueryById", func() {
		task := newTask()
		err := taskRepo.InsertUpdateOnDuplicate(ctx, task)
		Expect(err).Should(BeNil())

		task.Param = &models.TaskEngineParam{
			Param:    "224565",
			ParamUrl: "22455",
		}
		err = taskRepo.InsertUpdateOnDuplicate(ctx, task)
		Expect(err).Should(BeNil())

		querySaveResult2, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult2)

		//-----
		task.Result = &models.TaskResult{
			Code:     200,
			Data:     "data",
			DataUrl:  "data_url",
			Duration: 30,
			Extends: map[string]interface{}{
				"key": "value",
			},
			Reason:  "Success",
			Message: "success",
		}

		err = taskRepo.UpdateResult(ctx, task)
		Expect(err).Should(BeNil())
		querySaveResult3, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult3)

		//测试重复插入转换为更新时能否把结果置为null
		task.Result = nil
		err = taskRepo.InsertUpdateOnDuplicate(ctx, task)
		Expect(err).Should(BeNil())
		querySaveResult4, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult4)
		//-----

		task.BizStatus = models.TaskStateStopped
		err = taskRepo.InsertUpdateOnDuplicate(ctx, task)
		Expect(err).Should(BeNil())

		querySaveResult, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult)
	})

	It("SaveIgnoreOnDuplicate", func() {
		task := newTask()
		exist, err := taskRepo.InsertIgnoreDuplicate(ctx, task)
		Expect(err).Should(BeNil())
		Expect(exist).Should(BeTrue())

		exist, err = taskRepo.InsertIgnoreDuplicate(ctx, task)
		Expect(err).Should(BeNil())
		Expect(exist).Should(BeFalse())

		querySaveResult, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult)
	})

	It("UpdateMetadata", func() {
		task := newTask()
		err := taskRepo.InsertUpdateOnDuplicate(ctx, task)
		Expect(err).Should(BeNil())

		task.Metadata = map[string]string{
			"key": "value",
		}
		err = taskRepo.UpdateMetadata(ctx, task)
		Expect(err).Should(BeNil())

		querySaveResult, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult)
	})

	It("UpdateScheduleWorker", func() {
		task := newTask()
		err := taskRepo.InsertUpdateOnDuplicate(ctx, task)
		Expect(err).Should(BeNil())

		task.Worker = &models.Worker{
			Id:   uuid.NewString(),
			Ip:   "***********",
			Port: 1111,
		}

		err = taskRepo.UpdateScheduleWorker(ctx, task)
		Expect(err).Should(BeNil())

		querySaveResult, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult)
	})

	It("UpdateInternalStatus", func() {
		task := newTask()
		err := taskRepo.InsertUpdateOnDuplicate(ctx, task)
		Expect(err).Should(BeNil())

		task.InternalStatus = models.TaskStateActive
		err = taskRepo.UpdateInternalStatus(ctx, task)
		Expect(err).Should(BeNil())

		querySaveResult, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult)

		task.InternalStatus = models.TaskStateStopped
		err = taskRepo.UpdateInternalStatus(ctx, task)
		Expect(err).Should(BeNil())

		querySaveResult2, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult2)
	})

	It("UpdateBizStatus", func() {
		task := newTask()
		err := taskRepo.InsertUpdateOnDuplicate(ctx, task)
		Expect(err).Should(BeNil())

		task.BizStatus = models.TaskStateActive
		err = taskRepo.UpdateBizStatus(ctx, task)
		Expect(err).Should(BeNil())

		querySaveResult, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult)

		task.BizStatus = models.TaskStateStopped
		err = taskRepo.UpdateBizStatus(ctx, task)
		Expect(err).Should(BeNil())

		querySaveResult2, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult2)
	})

	It("UpdateResult", func() {
		task := newTask()
		err := taskRepo.InsertUpdateOnDuplicate(ctx, task)
		Expect(err).Should(BeNil())

		task.Result = &models.TaskResult{
			Code:     200,
			Data:     "data",
			DataUrl:  "data_url",
			Duration: 30,
			Extends: map[string]interface{}{
				"key": "value",
			},
			Reason:  "Success",
			Message: "success",
		}

		err = taskRepo.UpdateResult(ctx, task)
		Expect(err).Should(BeNil())
		querySaveResult, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult)
	})

	It("UpdateEngineParams", func() {
		task := newTask()
		err := taskRepo.InsertUpdateOnDuplicate(ctx, task)
		Expect(err).Should(BeNil())

		task.Param = &models.TaskEngineParam{
			Param:    "224565",
			ParamUrl: "22455",
		}
		err = taskRepo.UpdateEngineParam(ctx, task)
		Expect(err).Should(BeNil())
		querySaveResult, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult)
	})

	//UpdateEngineParamAndBizStatus
	It("UpdateEngineParamAndBizStatus", func() {
		task := newTask()
		err := taskRepo.InsertUpdateOnDuplicate(ctx, task)
		Expect(err).Should(BeNil())

		task.Param = &models.TaskEngineParam{
			Param:    "224565",
			ParamUrl: "22455",
		}
		task.BizStatus = models.TaskStateActive
		err = taskRepo.UpdateEngineParamAndBizStatus(ctx, task)
		Expect(err).Should(BeNil())
		querySaveResult, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult)

		task.Param = &models.TaskEngineParam{
			Param:    "123123123",
			ParamUrl: "321321",
		}
		task.BizStatus = models.TaskStateStopped
		err = taskRepo.UpdateEngineParamAndBizStatus(ctx, task)
		Expect(err).Should(BeNil())
		querySaveResult2, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult2)
	})

	It("UpdateAbnormalStoppedAndClearResultAndAddMigrateTimes", func() {
		task := newTask()
		err := taskRepo.InsertUpdateOnDuplicate(ctx, task)
		Expect(err).Should(BeNil())

		task.BizStatus = models.TaskStateActive
		taskRepo.UpdateBizStatus(ctx, task)

		task.Result = &models.TaskResult{
			Code:    models.MigratedCode,
			Reason:  models.MigratedReason,
			Message: "task migrated",
		}
		err = taskRepo.UpdateAbnormalStopped(ctx, task)
		Expect(err).Should(BeNil())

		querySaveResult, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult)

		task.Result = nil
		task.MigrateTimes = task.MigrateTimes + 1
		err = taskRepo.ClearResultAndAddMigrateTimes(ctx, task)
		Expect(err).Should(BeNil())

		querySaveResult2, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult2)

		task.BizStatus = models.TaskStateStopped
		err = taskRepo.UpdateBizStatus(ctx, task)
		Expect(err).Should(BeNil())
	})

	It("UpdateAbnormalStoppedAndIncreaseMigrateTimesAndClearResult", func() {
		task := newTask()
		task.MigrateConfig = &pb.MigrateConfig{
			MaxRuntime: 2,
			UnMigrate:  false,
		}
		task.MigrateTimes = 0
		err := taskRepo.InsertUpdateOnDuplicate(ctx, task)
		Expect(err).Should(BeNil())

		task.BizStatus = models.TaskStateActive
		taskRepo.UpdateBizStatus(ctx, task)

		task.Result = &models.TaskResult{
			Code:    models.MigratedCode,
			Reason:  models.MigratedReason,
			Message: "task migrated",
		}
		err = taskRepo.UpdateAbnormalStopped(ctx, task)
		Expect(err).Should(BeNil())

		querySaveResult, err := taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		ShouldTaskEqual(task, querySaveResult)

		err = taskRepo.IncreaseMigrateTimesAndClearResult(ctx, task.TaskId)
		Expect(err).Should(BeNil())

		task.MigrateTimes = task.MigrateTimes + 1
		_, err = taskRepo.FindByID(ctx, task.TaskId)
		Expect(err).Should(BeNil())
		//ShouldTaskEqual(task, querySaveResult2)

		task.BizStatus = models.TaskStateStopped
		err = taskRepo.UpdateBizStatus(ctx, task)
		Expect(err).Should(BeNil())
	})

	It("BatchUpdateAbnormalStoppedAndClearResultAndAddMigrateTimes", func() {
		task := newTask()
		task2 := newTask2()
		err := taskRepo.InsertUpdateOnDuplicate(ctx, task)
		Expect(err).Should(BeNil())
		err = taskRepo.InsertUpdateOnDuplicate(ctx, task2)
		Expect(err).Should(BeNil())

		task.BizStatus = models.TaskStateActive
		task2.BizStatus = models.TaskStateActive
		taskRepo.UpdateBizStatus(ctx, task)
		taskRepo.UpdateBizStatus(ctx, task2)

		task.Result = &models.TaskResult{
			Code:    models.MigratedCode,
			Reason:  models.MigratedReason,
			Message: "task migrated",
		}
		task2.Result = &models.TaskResult{
			Code:    models.MigratedCode,
			Reason:  models.MigratedReason,
			Message: "task migrated",
		}

		tasks := make([]*models.Task, 2)
		tasks[0] = task
		tasks[1] = task2
		taskIds := make([]string, 2)
		taskIds = append(taskIds, task.TaskId)
		taskIds = append(taskIds, task2.TaskId)
		err = taskRepo.BatchUpdateAbnormalStopped(ctx, taskIds, task.Result)
		Expect(err).Should(BeNil())
		for i := range tasks {
			querySaveResult, err := taskRepo.FindByID(ctx, tasks[i].TaskId)
			Expect(err).Should(BeNil())
			log.Infof("tasker[%d] = %+v, querySaveResult = %+v", i, tasks[i], querySaveResult)
			ShouldTaskEqual(tasks[i], querySaveResult)

			tasks[i].Result = nil
			tasks[i].MigrateTimes = tasks[i].MigrateTimes + 1
			err = taskRepo.ClearResultAndAddMigrateTimes(ctx, tasks[i])
			Expect(err).Should(BeNil())

			querySaveResult2, err := taskRepo.FindByID(ctx, tasks[i].TaskId)
			log.Infof("querySaveResult2 = %v", querySaveResult2.Result)
			Expect(err).Should(BeNil())
			ShouldTaskEqual(tasks[i], querySaveResult2)

			tasks[i].BizStatus = models.TaskStateStopped
			err = taskRepo.UpdateBizStatus(ctx, tasks[i])
			Expect(err).Should(BeNil())
		}
	})
})

var _ = Describe("tasker repo list", func() {
	var (
		taskRepo repository.TaskRepo
	)
	BeforeEach(func() {
		//初始化查询实例 添加表名
		taskRepo = NewTaskRepo(Db, nil)
		taskRepo = taskRepo.CloneWithSpecifiedTable(models.StreamTaskTableName)
	})

	It("FindByJobId", func() {
		jobId := models.NewTaskID(v1.TaskType_Stream)
		taskIdsOfTargetJobId := make([]string, 0)
		var allTasks = make(map[string]*models.Task)
		for i := 0; i < 10; i++ {
			task := newTask()
			if i%2 == 0 || i%3 == 0 {
				task.JobId = jobId
				taskIdsOfTargetJobId = append(taskIdsOfTargetJobId, task.TaskId)
				log.Infof("FindByJobId task[%d] = %+v", i, task)
			}
			err := taskRepo.InsertUpdateOnDuplicate(ctx, task)
			Expect(err).Should(BeNil())
			allTasks[task.TaskId] = task
		}

		tasksOfTargetJobId, err := taskRepo.FindByJobId(ctx, jobId)
		Expect(err).Should(BeNil())
		Expect(len(taskIdsOfTargetJobId)).Should(Equal(len(tasksOfTargetJobId)))
		for i := range taskIdsOfTargetJobId {
			var foundTask *models.Task
			for j := range tasksOfTargetJobId {
				if tasksOfTargetJobId[j].TaskId == taskIdsOfTargetJobId[i] {
					foundTask = tasksOfTargetJobId[j]
					break
				}
			}
			Expect(foundTask).ShouldNot(BeNil())
			ShouldTaskEqual(allTasks[taskIdsOfTargetJobId[i]], foundTask)
		}

		tasksOfRandomJobId, err := taskRepo.FindByJobId(ctx, uuid.NewString())
		Expect(err).Should(BeNil())
		Expect(len(tasksOfRandomJobId)).Should(Equal(0))
	})

	It("FindByIDList", func() {
		allTaskIds := make([]string, 0)
		var allTasks = make(map[string]*models.Task)
		for i := 0; i < 10; i++ {
			task := newTask()
			err := taskRepo.InsertUpdateOnDuplicate(ctx, task)
			Expect(err).Should(BeNil())
			allTasks[task.TaskId] = task
			allTaskIds = append(allTaskIds, task.TaskId)
		}

		toFoundTaskIds := make([]string, 0)
		toFoundTaskIds = append(toFoundTaskIds, allTaskIds[1])
		toFoundTaskIds = append(toFoundTaskIds, allTaskIds[5])
		toFoundTaskIds = append(toFoundTaskIds, allTaskIds[7])

		tasks, err := taskRepo.FindByIDList(ctx, toFoundTaskIds)
		Expect(err).Should(BeNil())
		Expect(len(tasks)).Should(Equal(len(toFoundTaskIds)))
		for i := range toFoundTaskIds {
			var foundTask *models.Task
			for j := range tasks {
				if tasks[j].TaskId == toFoundTaskIds[i] {
					foundTask = tasks[j]
					break
				}
			}
			Expect(foundTask).ShouldNot(BeNil())
			ShouldTaskEqual(allTasks[toFoundTaskIds[i]], foundTask)
		}
		ShouldListResultExpected(tasks, allTasks, toFoundTaskIds)

		toFoundTaskIdAndRandomIds := append(toFoundTaskIds, uuid.NewString())
		tasks, err = taskRepo.FindByIDList(ctx, toFoundTaskIdAndRandomIds)
		Expect(err).Should(BeNil())
		ShouldListResultExpected(tasks, allTasks, toFoundTaskIds)
	})

	It("List*", func() {
		bizRunningTaskIds := make([]string, 0)
		bizStoppedTaskIds := make([]string, 0)
		worker1 := &models.Worker{
			Id:   uuid.NewString(),
			Ip:   uuid.NewString(),
			Port: 111,
		}

		worker2 := &models.Worker{
			Id:   uuid.NewString(),
			Ip:   uuid.NewString(),
			Port: 111,
		}
		worker1TaskIds := make([]string, 0)
		worker2TaskIds := make([]string, 0)

		var allTasks = make(map[string]*models.Task)
		for i := 0; i < 12; i++ {
			task := newTask()
			err := taskRepo.InsertUpdateOnDuplicate(ctx, task)
			Expect(err).Should(BeNil())

			if i%2 == 0 || i%5 == 0 {
				task.BizStatus = models.TaskStateActive
				err = taskRepo.UpdateBizStatus(ctx, task)
				Expect(err).Should(BeNil())

				if i%2 == 0 {
					worker1TaskIds = append(worker1TaskIds, task.TaskId)
					task.Worker = worker1
					err = taskRepo.UpdateScheduleWorker(ctx, task)
					Expect(err).Should(BeNil())
				}

				if i%5 == 0 && i%2 != 0 {
					worker2TaskIds = append(worker2TaskIds, task.TaskId)
					task.Worker = worker2
					err = taskRepo.UpdateScheduleWorker(ctx, task)
					Expect(err).Should(BeNil())
				}

				bizRunningTaskIds = append(bizRunningTaskIds, task.TaskId)
			} else {
				bizStoppedTaskIds = append(bizStoppedTaskIds, task.TaskId)
			}
			allTasks[task.TaskId] = task
		}

		//ListAllBizRunningTasks，which biz_status is running
		runningTasks, err := taskRepo.ListAllBizRunningTasks(ctx)
		Expect(err).Should(BeNil())
		ShouldListResultExpected(runningTasks, allTasks, bizRunningTaskIds)

		listTasksOptimized, err := taskRepo.ListTasksOptimized(ctx, &models.Task{
			BizStatus:   models.TaskStateActive,
			Product:     "product_test",
			EngineModel: "product_test",
		})
		Expect(err).Should(BeNil())
		Expect(len(listTasksOptimized)).ShouldNot(BeZero())

		listTasksOptimized, err = taskRepo.ListTasksOptimized(ctx, &models.Task{
			BizStatus:      models.TaskStateActive,
			Product:        "product_test",
			EngineModel:    "product_test",
			InternalStatus: models.TaskStateActive,
		})
		Expect(err).Should(BeNil())
		Expect(len(listTasksOptimized)).Should(BeZero())

		//ListMissingRunningTasks, which biz_status is running and internal_status is stopped
		missingRunningTasks, err := taskRepo.ListMissingRunningTasks(ctx)
		Expect(err).Should(BeNil())
		ShouldListResultExpected(missingRunningTasks, allTasks, bizRunningTaskIds)

		//ListRunningTaskByWorkerId, which biz_status is running and workerId is worker1
		worker1Tasks, err := taskRepo.ListRunningTaskByWorkerId(ctx, worker1.Id)
		Expect(err).Should(BeNil())
		ShouldListResultExpected(worker1Tasks, allTasks, worker1TaskIds)

		//ListRunningTaskByWorkerId, which biz_status is running and workerId is worker2
		worker2Tasks, err := taskRepo.ListRunningTaskByWorkerId(ctx, worker2.Id)
		Expect(err).Should(BeNil())
		ShouldListResultExpected(worker2Tasks, allTasks, worker2TaskIds)

		//clear biz_status
		for i := range bizRunningTaskIds {
			allTasks[bizRunningTaskIds[i]].BizStatus = models.TaskStateStopped
			err := taskRepo.UpdateBizStatus(ctx, allTasks[bizRunningTaskIds[i]])
			Expect(err).Should(BeNil())
		}

		//test ListUnexpectedRunningTasks, first update some tasker internal_status = running
		unexpectedRunningTaskIds := bizStoppedTaskIds[0:2]
		for i := range unexpectedRunningTaskIds {
			allTasks[unexpectedRunningTaskIds[i]].InternalStatus = models.TaskStateActive
			err := taskRepo.UpdateInternalStatus(ctx, allTasks[unexpectedRunningTaskIds[i]])
			Expect(err).Should(BeNil())
		}
		unexpectedRunningTasks, err := taskRepo.ListUnexpectedRunningTasks(ctx)
		Expect(err).Should(BeNil())
		ShouldListResultExpected(unexpectedRunningTasks, allTasks, unexpectedRunningTaskIds)
	})

})

func ShouldListResultExpected(queryResult []*models.Task, allTasks map[string]*models.Task, expectedTaskIds []string) {
	Expect(len(queryResult)).Should(Equal(len(expectedTaskIds)))
	for i := range expectedTaskIds {
		var foundTask *models.Task
		for j := range queryResult {
			if queryResult[j].TaskId == expectedTaskIds[i] {
				foundTask = queryResult[j]
				break
			}
		}
		Expect(foundTask).ShouldNot(BeNil())
		ShouldTaskEqual(allTasks[expectedTaskIds[i]], foundTask)
	}
}

func ShouldTaskEqual(a *models.Task, b *models.Task) {
	Ω(a.Product).Should(Equal(b.Product))
	Ω(a.EngineModel).Should(Equal(b.EngineModel))
	Ω(a.Tag).Should(Equal(b.Tag))
	Ω(a.Notify).Should(Equal(b.Notify))
	Ω(a.JobId).Should(Equal(b.JobId))
	Ω(a.TaskId).Should(Equal(b.TaskId))
	Ω(a.RequestResource).Should(Equal(b.RequestResource))
	Ω(a.Result).Should(Equal(b.Result))
	Ω(a.Worker).Should(Equal(b.Worker))
	Ω(a.Param).Should(Equal(b.Param))
	Ω(a.BizStatus).Should(Equal(b.BizStatus))
	Ω(a.InternalStatus).Should(Equal(b.InternalStatus))
	Ω(a.Trace).Should(Equal(b.Trace))
	Ω(a.UserData).Should(Equal(b.UserData))
	Ω(a.TaskType).Should(Equal(b.TaskType))
	Ω(a.MigrateConfig).Should(Equal(b.MigrateConfig))
	Ω(a.MigrateTimes).Should(Equal(b.MigrateTimes))
	Ω(a.Dependencies).Should(Equal(b.Dependencies))
	Ω(a.Notify).Should(Equal(b.Notify))
	//Ω(a.LastModified).To(BeTemporally("~", b.LastModified, time.Second))
	//Ω(a.GmtCreate).To(BeTemporally("~", b.GmtCreate, time.Second))
}

func newTask() *models.Task {

	taskId := models.NewTaskID(v1.TaskType_Stream)
	return &models.Task{
		Product:        "product_test",
		EngineModel:    "product_test",
		Tag:            "product_test",
		TaskId:         taskId,
		JobId:          taskId,
		BizStatus:      models.TaskStateStopped,
		InternalStatus: models.TaskStateStopped,
		Param: &models.TaskEngineParam{
			Param:    "abcde",
			ParamUrl: "",
		},
		UserData: "user_data_test",
		Trace:    "trace_test",
		RequestResource: &models.RequestResource{
			Quota: map[string]int64{
				"cpu": 1,
				"mem": 1,
			},
			Affinity: nil,
		},
		Domain:       "domain_test",
		TaskType:     models.StreamTaskType,
		Dependencies: nil,
		LastModified: time.Now(),
		GmtCreate:    time.Now(),
		Notify:       uuid.NewString(),
	}
}

// newTask2
func newTask2() *models.Task {
	taskId := models.NewTaskID(v1.TaskType_Stream)
	return &models.Task{
		Product:        "product_test",
		EngineModel:    "product_test",
		Tag:            "product_test",
		TaskId:         taskId,
		JobId:          taskId,
		BizStatus:      models.TaskStateStopped,
		InternalStatus: models.TaskStateStopped,
		Param: &models.TaskEngineParam{
			Param:    "fghijk",
			ParamUrl: "",
		},
		UserData: "user_data_test",
		Trace:    "trace_test",
		RequestResource: &models.RequestResource{
			Quota: map[string]int64{
				"cpu": 1,
				"mem": 1,
			},
			Affinity: nil,
		},
		TaskType:     models.StreamTaskType,
		Dependencies: nil,
		LastModified: time.Now(),
		GmtCreate:    time.Now(),
		Notify:       uuid.NewString(),
	}
}
