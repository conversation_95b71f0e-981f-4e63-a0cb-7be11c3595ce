package repository

import (
	"context"

	"mpp/internal/taskmanager/models/domain"
)

type TranscodeAttachmentRepo interface {
	Create(ctx context.Context, transcodeAttachments []*domain.TranscodeAttachment) error
	Delete(ctx context.Context, transcodeAttachments []*domain.TranscodeAttachment) error
	SelectByTaskType(ctx context.Context, taskId, attachType string) ([]*domain.TranscodeAttachment, error)
}
