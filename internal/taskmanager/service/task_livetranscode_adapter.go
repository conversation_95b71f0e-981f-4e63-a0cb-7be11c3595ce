package service

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"mpp/internal/taskmanager/biz/common"
	lcUtils "mpp/internal/taskmanager/biz/livetranscode"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/domain"
	lc "mpp/internal/taskmanager/models/livetranscode"
	"mpp/internal/taskscheduler/biz"
	"mpp/pkg/header"

	pb "proto.mpp/api/taskmanager/v1"

	pkError "github.com/go-kratos/kratos/v2/errors"
	"github.com/google/uuid"
)

const (
	ParameterErrorCode        = "Task.ParameterError"
	InternalErrorCode         = "Task.InternalError"
	ThrottledErrorCode        = "Throttled"
	InvalidParameterNullValue = "InvalidParameter.NullValue"
)

func (s *TaskService) LiveTranscodeResult(reason string, message string, rst interface{}) any {
	result := make(map[string]interface{})
	if reason == "" || reason == "ok" || reason == "Success" {
		reason = "Success"
		message = "Ok"
	}
	result["result"] = map[string]string{
		"code":    reason,
		"message": message,
	}
	if rst != nil {
		result["data"] = rst
	}
	return result
}

func (s *TaskService) LiveTranscodeApiResult(reason string, message string, rst map[string]interface{}) any {
	if rst == nil {
		rst = make(map[string]interface{})
	}
	rst["description"] = message
	if reason == "" || reason == "ok" || reason == "OK" || reason == "Success" {
		rst["retCode"] = 0
	} else {
		rst["retCode"] = -1
	}
	return rst
}

func (s *TaskService) WrapErrorWithReasonAndMessage(reason string, message string) error {
	err := pkError.New(500, reason, message)
	return err
}

// LiveTranscodeStartTask 启动转码任务
func (s *TaskService) LiveTranscodeStartTask(w http.ResponseWriter, r *http.Request, requestBody []byte) (any, error) {
	s.log.Infof("LiveTranscodeStartTask start, url:%s", r.URL.String())
	//限流
	rateLimiter := s.globalParamLoader.GetRateLimiter(s.getFunctionName())
	if rateLimiter != nil && !rateLimiter.Allow() {
		//TODO 返回状态码和老调度统一
		s.log.Errorf("LiveTranscodeStartTask throttled, url:%s", r.URL.String())
		return nil, s.WrapErrorWithReasonAndMessage(ThrottledErrorCode, "throttled by qps")
	}
	// 参数修正
	var streamParams lc.StreamParams
	err := json.Unmarshal(requestBody, &streamParams)
	if err != nil {
		s.log.Errorf("LiveTranscodeStartTask decode request error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(ParameterErrorCode, fmt.Sprintf("LiveTranscodeStartTask decode request error, err:%v", err))
	}
	//参数修正
	taskId := lcUtils.GenerateTaskId(streamParams.Domain, streamParams.AppName, streamParams.StreamName, streamParams.Token)
	s.log.Infof("LiveTranscodeStartTask, url:%s, headers:%+v, body:%s taskId:%s", r.URL.String(), r.Header, string(requestBody), taskId)
	// 开启影子链路
	query := r.URL.Query()
	if r.Header.Get(header.HeaderShadow) != "" {
		query.Set("jobId", header.HeaderShadow+uuid.NewString())
	}
	s.liveTransCodeParamsAdapter.CorrectLiveTransCodeStartParams(&streamParams)
	//TODO 高可用能力
	//ha.DoBefore(taskId)
	ctx, startTaskRequest, taskInfo, err := s.liveTransCodeParamsAdapter.AssembleStartTaskRequest(r.Context(), taskId, streamParams)
	if err != nil {
		s.log.Errorf("LiveTranscodeStartTask assembleStartTaskRequest error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(ParameterErrorCode, fmt.Sprintf("LiveTranscodeStartTask assembleStartTaskRequest error, err:%v", err))
	}
	err = s.streamTaskBiz.AddSourceStreamTask(ctx, streamParams, taskId, lcUtils.GenerateStreamId(streamParams.Domain, streamParams.AppName, streamParams.StreamName))
	if err != nil {
		s.log.Errorf("LiveTranscodeStartTask add source stream task error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(InternalErrorCode, fmt.Sprintf("LiveTranscodeStartTask add source stream task error, err:%v", err))
	}
	return s.AnimusLiveTranscodeStartTask(ctx, startTaskRequest, streamParams.Unit, taskInfo, streamParams.NoFailHoldAndReturnError)
}

func (s *TaskService) AnimusLiveTranscodeStartTask(ctx context.Context, task *pb.StartTaskRequest, unit string, taskInfo map[string]any, noFailHoldAndReturnError string) (any, error) {
	// 直播的切流与兜底,切流能力与原来一致，兜底能力细化为一致
	switchStreams := s.schedulerSwitcher.SwitchScheduler(ctx, task)

	// 单元化
	s.haService.DoHAStartBefore(task.TaskId)
	out, err := s.StartTask(ctx, task)
	if err != nil {
		if strings.ToLower(noFailHoldAndReturnError) == "true" {
			s.log.WithContext(ctx).Errorf("LiveTranscodeStartTask start task failed and request noFailHoldAndReturnError, err:%v", err)
			return nil, s.WrapErrorWithReasonAndMessage(InternalErrorCode, fmt.Sprintf("LiveTranscodeStartTask start task failed and request noFailHoldAndReturnError, err:%v", err))
		}
		// 资源池兜底开始生效
		if switchStreams != nil && len(switchStreams) > 0 && (!errors.Is(err, common.ErrTaskStillRunning)) {
			out, err = s.HoldFailedAndRetry(ctx, task, switchStreams, unit)
			if err != nil {
				// 兜底失败，降级源流
				s.log.WithContext(ctx).Errorf("hold failed and retry failed,taskId:%s, switchStreams:%+v, err:%v and back up to source stream", task.TaskId, switchStreams, err)
				// check域名是否满足降级条件
				if s.globalParamLoader.BackupDomainExist(task.Domain) {
					err = s.streamTaskBiz.TranscodeTaskBackupToSourceStream(ctx, task.TaskId)
					if err != nil {
						s.log.WithContext(ctx).Errorf("back up to source stream failed,taskId:%s, err:%v", task.TaskId, err)
						return nil, s.WrapErrorWithReasonAndMessage(InternalErrorCode, fmt.Sprintf("back up to source stream failed,taskId:%s, err:%v", task.TaskId, err))
					} else {
						// 源流兜底成功不返回失败
						s.log.WithContext(ctx).Warnf("back up to source stream success,taskId:%s", task.TaskId)
						return s.LiveTranscodeResult("Success", "Ok", taskInfo), nil
					}
				}
				return nil, s.WrapErrorWithReasonAndMessage(InternalErrorCode, fmt.Sprintf("hold failed and retry failed,taskId:%s, switchStreams:%+v, err:%v and back up to source stream", task.TaskId, switchStreams, err))
			}
		} else { // 未有兜底资源池，防止返回Panic
			s.haService.DoHAStartAfter(unit, task.TaskId)
			// check域名是否满足降级条件
			if s.globalParamLoader.BackupDomainExist(task.Domain) {
				err = s.streamTaskBiz.TranscodeTaskBackupToSourceStream(ctx, task.TaskId)
				if err != nil {
					s.log.WithContext(ctx).Errorf("back up to source stream failed,taskId:%s, err:%v", task.TaskId, err)
					return nil, s.WrapErrorWithReasonAndMessage(InternalErrorCode, fmt.Sprintf("back up to source stream failed,taskId:%s, err:%v", task.TaskId, err))
				} else {
					// 源流兜底成功不返回失败
					s.log.WithContext(ctx).Warnf("back up to source stream success,taskId:%s", task.TaskId)
					return s.LiveTranscodeResult("Success", "Ok", taskInfo), nil
				}
			}
			return nil, s.WrapErrorWithReasonAndMessage(InternalErrorCode, fmt.Sprintf("LiveTranscodeStartTask start task failed without default tag and back up to source stream, err:%v", err))
		}
	}
	s.haService.DoHAStartAfter(unit, task.TaskId)

	return s.LiveTranscodeResult(out.Result.Reason, out.Result.Message, taskInfo), nil
}

func (s *TaskService) HoldFailedAndRetry(ctx context.Context, task *pb.StartTaskRequest, switchStreams []map[string]interface{}, unit string) (*pb.StartTaskReply, error) {
	// 资源池兜底开始生效 todo 本文件需增加业务日志
	var errReturn error
	paramMap := biz.StringToMap(task.EngineParams)
	for _, switchStream := range switchStreams {
		s.log.WithContext(ctx).Infof("hold failed and retry start,taskId:%s, switchStreams:%+v", task.TaskId, switchStreams)
		stream := lcUtils.GetStreamByMap(paramMap)
		stream["rtmpDest"] = switchStream["RtmpDest"]
		paramMap["streamSource"] = switchStream["StreamSource"]
		if switchStream["Tag"] != nil {
			task.Tag = switchStream["Tag"].(string)
		}
		stream["env"] = switchStream["Env"]
		paramMap["streamSourceBakVips"] = switchStream["StreamSourceBakVips"]
		paramMap["streams"] = []interface{}{stream}

		task.EngineParams = biz.JsonToString(paramMap)
		task.Metadata["taskSchedulerTag"] = switchStream["TaskSchedulerTag"].(string)
		out, err := s.StartTask(ctx, task)
		if err == nil {
			s.haService.DoHAStartAfter(unit, task.TaskId)
			s.log.WithContext(ctx).Infof("hold failed and retry success,taskId:%s, switchStream:%+v", task.TaskId, switchStream)
			return out, nil
		} else {
			s.log.WithContext(ctx).Errorf("hold failed and retry failed,taskId:%s, switchStream:%+v, err:%v", task.TaskId, switchStream, err)
			errReturn = err
			continue
		}
	}
	s.log.WithContext(ctx).Infof("hold failed and retry failed finally,taskId:%s, switchStreams:%+v", task.TaskId, switchStreams)
	return nil, errReturn
}

// LiveTranscodeStopTask 停止转码任务
func (s *TaskService) LiveTranscodeStopTask(w http.ResponseWriter, r *http.Request, requestBody []byte) (any, error) {
	s.log.Infof("LiveTranscodeStopTask, url:%s, headers:%+v", r.URL.String(), r.Header)
	//限流
	rateLimiter := s.globalParamLoader.GetRateLimiter(s.getFunctionName())
	if rateLimiter != nil && !rateLimiter.Allow() {
		s.log.Errorf("LiveTranscodeStopTask throttled by qps url:%s", r.URL.String())
		return nil, s.WrapErrorWithReasonAndMessage(ThrottledErrorCode, "throttled by qps")
	}
	s.log.Infof("LiveTranscodeStopTask, url:%s, headers:%+v, body:%s", r.URL.String(), r.Header, string(requestBody))
	// 参数修正
	var stopRequest lc.LiveTransCodeStopRequest
	err := json.Unmarshal(requestBody, &stopRequest)
	if err != nil {
		s.log.Errorf("LiveTranscodeStopTask decode request error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(ParameterErrorCode, fmt.Sprintf("LiveTranscodeStopTask decode request error, err:%v", err))
	}
	ctx, liveTransCodeStopReq := s.liveTransCodeParamsAdapter.AssembleStopTaskRequest(r.Context(), stopRequest)

	s.haService.DoStopBefore(stopRequest.Unit, liveTransCodeStopReq.TaskId)

	out, err := s.StopTask(ctx, liveTransCodeStopReq)

	s.haService.DoStopAfter(stopRequest.Unit, liveTransCodeStopReq.TaskId)
	if err != nil {
		return nil, s.WrapErrorWithReasonAndMessage(InternalErrorCode, fmt.Sprintf("LiveTranscodeStopTask stop task failed, err:%v", err))
	}

	return s.LiveTranscodeResult(out.Result.Reason, out.Result.Message, nil), nil
}

// LiveTranscodeMigrateTask 迁移转码任务
func (s *TaskService) LiveTranscodeMigrateTask(w http.ResponseWriter, r *http.Request) (any, error) {
	s.log.Infof("LiveTranscodeMigrateTask, url:%s, headers:%+v", r.URL.String(), r.Header)
	//限流
	rateLimiter := s.globalParamLoader.GetRateLimiter(s.getFunctionName())
	if rateLimiter != nil && !rateLimiter.Allow() {
		s.log.Errorf("LiveTranscodeMigrateTask throttled by qps url:%s", r.URL.String())
		return nil, s.WrapErrorWithReasonAndMessage(ThrottledErrorCode, "throttled by qps")
	}
	var migrateRequest lc.LiveTransCodeMigrateRequest
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&migrateRequest)
	s.log.Infof("LiveTranscodeMigrateTask, url:%s, headers:%+v, body:%v", r.URL.String(), r.Header, migrateRequest)
	if err != nil {
		s.log.Errorf("LiveTranscodeMigrateTask decode request error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(ParameterErrorCode, fmt.Sprintf("LiveTranscodeMigrateTask decode request error, err:%v", err))
	}
	//先stop
	ctx, liveTransCodeStopReq := s.liveTransCodeParamsAdapter.AssembleStopTaskRequestOnMigrate(r.Context(), migrateRequest)
	taskId := liveTransCodeStopReq.TaskId
	_, err = s.StopTask(ctx, liveTransCodeStopReq)
	if err != nil {
		s.log.WithContext(ctx).Errorf("LiveTranscodeMigrateTask stop task failed,taskId:%s, err:%v", taskId, err)
	}
	// 再start
	task, err := s.streamTaskBiz.GetTask(ctx, taskId)
	if err != nil {
		s.log.WithContext(ctx).Errorf("LiveTranscodeMigrateTask get task failed,taskId:%s, err:%v", taskId, err)
		return nil, s.WrapErrorWithReasonAndMessage(InternalError, fmt.Sprintf("LiveTranscodeMigrateTask get task failed,taskId:%s, err:%v", taskId, err))
	}
	// set start params
	s.liveTransCodeParamsAdapter.AssembleStartTaskRequestOnMigrate(ctx, task, migrateRequest.SameDictator)
	err = s.streamTaskBiz.StartTask(ctx, task)
	if err != nil {
		s.log.WithContext(ctx).Errorf("LiveTranscodeMigrateTask start task failed,taskId:%s, err:%v", taskId, err)
		return nil, s.WrapErrorWithReasonAndMessage(InternalError, fmt.Sprintf("LiveTranscodeMigrateTask start task failed,taskId:%s, err:%v", taskId, err))
	}
	// todo 返回规范
	return s.LiveTranscodeApiResult("OK", "OK", nil), nil
}

// LiveTranscodeSendCommonCommand  下发引擎命令
func (s *TaskService) LiveTranscodeSendCommonCommand(w http.ResponseWriter, r *http.Request) (any, error) {
	var sendCommandRequest lc.LiveTransCodeSendCommandRequest
	bodyBytes, _ := io.ReadAll(r.Body)
	sendCommandRequestStr := string(bodyBytes)
	s.log.Infof("LiveTranscodeSendCommand, url:%s, headers:%+v,body:%s", r.URL.String(), r.Header, sendCommandRequestStr)
	err := json.Unmarshal([]byte(sendCommandRequestStr), &sendCommandRequest)
	if err != nil {
		s.log.Errorf("LiveTranscodeSendCommand decode request error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(ParameterErrorCode, fmt.Sprintf("LiveTranscodeSendCommand decode request error, err:%v", err))
	}
	newCtx, in, err := s.liveTransCodeParamsAdapter.AssembleSendCommonCommand(r.Context(), sendCommandRequest.TaskId, sendCommandRequestStr, lc.COMMAN_COMMAND)
	if err != nil {
		s.log.Errorf("LiveTranscodeSendCommand assembleSendCommonCommand error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(InternalError, fmt.Sprintf("LiveTranscodeSendCommand assembleSendCommonCommand error, err:%v", err))
	}
	out, err := s.CommandTask(newCtx, in)
	if err != nil {
		s.log.Errorf("LiveTranscodeSendCommand CommandTask error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(InternalError, fmt.Sprintf("LiveTranscodeSendCommand CommandTask error, err:%v", err))
	}
	return s.LiveTranscodeResult(out.Result.Reason, out.Result.Message, nil), nil
}

// LiveTranscodeUpdateTask 更新转码任务
func (s *TaskService) LiveTranscodeUpdateTask(w http.ResponseWriter, r *http.Request) (any, error) {
	var updateTaskRequest lc.LiveTransCodeUpdateRequest
	bodyBytes, _ := io.ReadAll(r.Body)
	updateTaskRequestStr := string(bodyBytes)
	s.log.Infof("LiveTranscodeUpdateTask, url:%s, headers:%+v ,body:%s", r.URL.String(), r.Header, updateTaskRequestStr)
	err := json.Unmarshal([]byte(updateTaskRequestStr), &updateTaskRequest)
	if err != nil {
		s.log.Errorf("LiveTranscodeSendCommand decode request error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(ParameterErrorCode, fmt.Sprintf("LiveTranscodeSendCommand decode request error, err:%v", err))
	}
	newCtx, in, err := s.liveTransCodeParamsAdapter.AssembleUpdateTaskRequest(r.Context(), updateTaskRequest.TaskId, updateTaskRequestStr)
	if err != nil {
		s.log.Errorf("LiveTranscodeUpdateTask assembleUpdateTaskCommand error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(InternalError, fmt.Sprintf("LiveTranscodeUpdateTask assembleUpdateTaskCommand error, err:%v", err))
	}
	out, err := s.UpdateTask(newCtx, in)
	if err != nil {
		s.log.Errorf("LiveTranscodeUpdateTask UpdateTask error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(InternalError, fmt.Sprintf("LiveTranscodeUpdateTask UpdateTask error, err:%v", err))
	}
	return s.LiveTranscodeResult(out.Result.Reason, out.Result.Message, nil), nil
}

// LiveTranscodeEditPic 编辑转码任务水印
func (s *TaskService) LiveTranscodeEditPic(w http.ResponseWriter, r *http.Request) (any, error) {
	var picEditTaskRequest lc.LiveTransCodePicEditTaskRequest
	bodyBytes, _ := io.ReadAll(r.Body)
	editTaskRequestStr := string(bodyBytes)
	s.log.Infof("LiveTranscodePicEditTask, url:%s, headers:%+v, body: %s", r.URL.String(), r.Header, editTaskRequestStr)
	err := json.Unmarshal([]byte(editTaskRequestStr), &picEditTaskRequest)
	if err != nil {
		s.log.Errorf("LiveTransCodePicEditTask decode request error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(ParameterErrorCode, fmt.Sprintf("LiveTranscodePicEditTask decode request error, err:%v", err))
	}
	out, err := s.PicEditTransCodeTask(r.Context(), picEditTaskRequest)
	if err != nil {
		s.log.Errorf("LiveTransCodePicEditTask EditTransCodeTask error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(InternalError, fmt.Sprintf("LiveTranscodePicEditTask EditTransCodeTask error, err:%v", err))
	}
	return s.LiveTranscodeResult(out.Result.Reason, out.Result.Message, nil), nil
}

// LiveTranscodeEditText 编辑转码任务文字
func (s *TaskService) LiveTranscodeEditText(w http.ResponseWriter, r *http.Request) (any, error) {
	var textEditTaskRequest lc.LiveTransCodeTextEditTaskRequest
	bodyBytes, _ := io.ReadAll(r.Body)
	editTaskRequestStr := string(bodyBytes)
	s.log.Infof("LiveTranscodeTextEditTask, url:%s, headers:%+v, body: %s", r.URL.String(), r.Header, editTaskRequestStr)
	err := json.Unmarshal([]byte(editTaskRequestStr), &textEditTaskRequest)
	if err != nil {
		s.log.Errorf("LiveTranscodeTextEditPTask decode request error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(ParameterErrorCode, fmt.Sprintf("LiveTranscodeTextEditTask decode request error, err:%v", err))
	}
	out, err := s.TextEditTransCodeTask(r.Context(), textEditTaskRequest)
	if err != nil {
		s.log.Errorf("LiveTranscodeTextEditTask EditTransCodeTask error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(InternalError, fmt.Sprintf("LiveTranscodeTextEditTask EditTransCodeTask error, err:%v", err))
	}
	return s.LiveTranscodeResult(out.Result.Reason, out.Result.Message, nil), nil
}

// LiveTranscodeQueryStat 查询转码情况统计
func (s *TaskService) LiveTranscodeQueryStat(w http.ResponseWriter, r *http.Request) (any, error) {
	//TODO 完善
	var queryStatRequest lc.LiveTransCodeQueryStatRequest
	bodyBytes, _ := io.ReadAll(r.Body)
	queryStatRequestStr := string(bodyBytes)
	s.log.Infof("LiveTranscodeQueryStat, url:%s, headers:%+v, body:%s", r.URL.String(), r.Header, queryStatRequestStr)
	err := json.Unmarshal([]byte(queryStatRequestStr), &queryStatRequest)
	if err != nil {
		s.log.Errorf("LiveTransCodeQueryStat decode request error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(ParameterErrorCode, fmt.Sprintf("LiveTransCodeQueryStat decode request error, err:%v", err))
	}
	out, err := s.streamTaskBiz.QueryStat(r.Context(), queryStatRequest.Tag, queryStatRequest.Domain)
	if err != nil {
		s.log.Errorf("LiveTransCodeQueryStat queryStat error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(InternalError, fmt.Sprintf("LiveTransCodeQueryStat queryStat error, err:%v", err))
	}
	return s.LiveTranscodeResult("Success", "Success", map[string]any{"statInfos": out}), nil
}

// LiveTranscodeGetTasks 获取正在执行的转码任务
func (s *TaskService) LiveTranscodeGetTasks(w http.ResponseWriter, r *http.Request) (any, error) {
	s.log.Infof("LiveTranscodeGetTasks, url:%s, headers:%+v", r.URL.String(), r.Header)
	req := s.liveTransCodeParamsAdapter.AssembleListTranscodeRunningTaskReq()
	tasks, err := s.streamTaskBiz.GetRunningTaskIds(r.Context(), req)
	if err != nil {
		s.log.Errorf("LiveTranscodeGetTasks getRunningTaskIds error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(InternalError, fmt.Sprintf("LiveTranscodeGetTasks getRunningTaskIds error, err:%v", err))
	}
	liveTransCodeTasks := make([]*lc.LiveTransCodeTask, 0)
	for _, task := range tasks {
		liveTransCodeTask := s.liveTransCodeParamsAdapter.CovertToLiveTransCodeTask(task)
		liveTransCodeTasks = append(liveTransCodeTasks, liveTransCodeTask)
	}

	return s.LiveTranscodeResult("Success", "Success", map[string]any{"tasks": liveTransCodeTasks}), nil
}

// LiveTranscodeGetTask 获取单个转码任务
func (s *TaskService) LiveTranscodeGetTask(w http.ResponseWriter, r *http.Request) (any, error) {
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return nil, err
	}
	if body != nil {
		bodyStr = string(body)
	}
	s.log.Infof("LiveTranscodeGetTask message, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)
	taskId := getBodyOrQueryString(js, r.URL.Query(), "taskId")
	task, err := s.streamTaskBiz.GetTask(r.Context(), taskId)
	if err != nil {
		s.log.Errorf("LiveTranscodeGetTask getTask error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(InternalError, fmt.Sprintf("LiveTranscodeGetTask getTask error, err:%v", err))
	}
	liveTransCodeTask := s.liveTransCodeParamsAdapter.CovertToLiveTransCodeTask(task)
	return s.LiveTranscodeApiResult("Success", "Success", map[string]any{"content": map[string]any{"task": liveTransCodeTask, "info": ""}}), nil
}

// LiveTranscodeTaskStatistics 获取转码任务统计
func (s *TaskService) LiveTranscodeTaskStatistics(w http.ResponseWriter, r *http.Request) (any, error) {
	var taskStatisticsRequest lc.LiveTransCodeTaskStatisticsRequest
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&taskStatisticsRequest)
	s.log.Infof("LiveTranscodeTaskStatistics, url:%s, headers:%+v, body:%v", r.URL.String(), r.Header, taskStatisticsRequest)
	if err != nil {
		s.log.Errorf("LiveTranscodeTaskStatistics decode request error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(ParameterErrorCode, fmt.Sprintf("LiveTranscodeTaskStatistics decode request error, err:%v", err))
	}
	taskStats, err := s.streamTaskBiz.CallBizHost(r.Context(), s.globalParamLoader.GetBizHost(), taskStatisticsRequest.Domain, taskStatisticsRequest.App)
	if err != nil {
		s.log.Errorf("LiveTranscodeTaskStatistics CallBizHost error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(InternalError, fmt.Sprintf("LiveTranscodeTaskStatistics CallBizHost error, err:%v", err))
	}
	return s.LiveTranscodeApiResult("Success", "Success", map[string]any{"content": map[string]any{"taskStats": taskStats, "tag": taskStatisticsRequest.Tag}}), nil
}

// LiveTranscodeListTag 获取转码任务标签
func (s *TaskService) LiveTranscodeListTag(w http.ResponseWriter, r *http.Request) (any, error) {
	s.log.Infof("LiveTranscodeListTag, url:%s, headers:%+v", r.URL.String(), r.Header)
	var listTagRequest lc.LiveTransCodeListTagRequest
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&listTagRequest)
	if err != nil {
		s.log.Errorf("LiveTranscodeListTag decode request error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(ParameterErrorCode, fmt.Sprintf("LiveTranscodeListTag decode request error, err:%v", err))
	}
	//TODO 完善
	var out []struct {
		Tag  string `json:"tag"`
		Name string `json:"name"`
	}

	// 添加元素
	out = append(out, struct {
		Tag  string `json:"tag"`
		Name string `json:"name"`
	}{
		Tag:  TAG_TRANSCODE,
		Name: "transcode",
	})
	return s.LiveTranscodeApiResult("Success", "Success", map[string]any{"content": map[string]any{"tags": out}}), nil
}

// LiveTranscodeGetSyncData 获取同步数据
func (s *TaskService) LiveTranscodeGetSyncData(w http.ResponseWriter, r *http.Request) (any, error) {
	s.log.Infof("LiveTranscodeGetSyncData, url:%s, headers:%+v", r.URL.String(), r.Header)

	// 获取正在运行的任务
	req := s.liveTransCodeParamsAdapter.AssembleListTranscodeRunningTaskReq()
	tasks, err := s.streamTaskBiz.GetRunningSimpleTasks(r.Context(), req)
	if err != nil {
		s.log.Errorf("LiveTranscodeGetSyncData getRunningTaskIds error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(InternalError, fmt.Sprintf("LiveTranscodeGetSyncData getRunningTaskIds error, err:%v", err))
	}

	// 构建响应数据
	data := make([]map[string]interface{}, 0)
	parseTag := s.globalParamLoader.GetParseTagForSyncData()

	for _, task := range tasks {
		taskData := map[string]interface{}{
			"task_id":      task.TaskId,
			"gmt_modified": task.LastModified.UnixMilli(),
		}

		if parseTag {
			tag := s.getTaskTag(task.Tag)
			taskData["tag"] = tag
		}

		data = append(data, taskData)
	}

	return s.LiveTranscodeResult("Success", "Ok", data), nil
}

// getTaskTag 从任务参数中提取标签
func (s *TaskService) getTaskTag(tag string) string {
	tagSplits := strings.Split(tag, "_")
	if len(tagSplits) <= 2 {
		return ""
	}
	// 去掉第一个和最后一个元素
	middle := tagSplits[1 : len(tagSplits)-1]
	return strings.Join(middle, "_")
	//if params == "" {
	//	return ""
	//}
	//var jsonData map[string]interface{}
	//err := json.Unmarshal([]byte(params), &jsonData)
	//if err != nil {
	//	s.log.Errorf("getTaskTag unmarshal error, err:%v", err)
	//	return ""
	//}
	//// 获取streams数组
	//streams, ok := jsonData["streams"].([]interface{})
	//if !ok || len(streams) == 0 {
	//	return ""
	//}
	//// 获取第一个stream对象
	//stream, ok := streams[0].(map[string]interface{})
	//if !ok {
	//	return ""
	//}
	//// 检查是否包含class字段且不为空
	//if class, exists := stream["class"]; exists {
	//	if classStr, ok := class.(string); ok && classStr != "" {
	//		return classStr
	//	}
	//}
	//return ""
}

// UpdateGlobalConfig 更新引擎全局配置
func (s *TaskService) UpdateGlobalConfig(w http.ResponseWriter, r *http.Request) (any, error) {
	s.log.Infof("UpdateGlobalConfig, url:%s, headers:%+v", r.URL.String(), r.Header)

	// 限流检查
	rateLimiter := s.globalParamLoader.GetRateLimiter(s.getFunctionName())
	if rateLimiter != nil && !rateLimiter.Allow() {
		s.log.Errorf("UpdateGlobalConfig throttled by qps url:%s", r.URL.String())
		return nil, s.WrapErrorWithReasonAndMessage(ThrottledErrorCode, "throttled by qps")
	}

	// 读取请求体
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		s.log.Errorf("UpdateGlobalConfig read body error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(InvalidParameterNullValue, fmt.Sprintf("UpdateGlobalConfig read body error, err:%v", err))
	}

	// 解析请求参数
	var requestBody struct {
		RateCallbackConfig *lc.RateCallbackConfig `json:"rateCallbackConfig"`
	}

	err = json.Unmarshal(bodyBytes, &requestBody)
	if err != nil {
		s.log.Errorf("UpdateGlobalConfig unmarshal request error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(InvalidParameterNullValue, fmt.Sprintf("UpdateGlobalConfig unmarshal request error, err:%v", err))
	}

	// 验证参数
	if requestBody.RateCallbackConfig == nil || len(requestBody.RateCallbackConfig.Contents) == 0 {
		return nil, s.WrapErrorWithReasonAndMessage(InvalidParameterNullValue, "rateCallbackConfig.contents cannot be null or empty")
	}

	// TODO: 需要注入KV存储服务和TaskManager服务
	// 设置过期时间为一年后
	expiredTime := time.Now().AddDate(1, 0, 0)

	// 保存到KV存储
	kv := &domain.KVStore{
		Repo:        "params",
		Key:         "rateCallbackConfig",
		Value:       biz.JsonToString(requestBody.RateCallbackConfig),
		ExpiredTime: expiredTime,
	}

	err = s.globalParamLoader.SetParams(r.Context(), kv)
	if err != nil {
		s.log.Errorf("UpdateGlobalConfig save to kvstore error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(InternalErrorCode, fmt.Sprintf("UpdateGlobalConfig save to kvstore error, err:%v", err))
	}

	// 发送配置到worker
	err = s.streamTaskBiz.SendConfigToWorker(r.Context(), requestBody.RateCallbackConfig)
	if err != nil {
		s.log.Errorf("UpdateGlobalConfig send config to worker error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(InternalErrorCode, fmt.Sprintf("UpdateGlobalConfig send config to worker error, err:%v", err))
	}

	s.log.Infof("UpdateGlobalConfig success, config: %+v", requestBody.RateCallbackConfig)
	return s.LiveTranscodeResult("Success", "Ok", nil), nil
}

func (s *TaskService) QueryTasksWithSourceStream(w http.ResponseWriter, r *http.Request) (any, error) {
	s.log.Infof("QueryTasksWithSourceStream, url:%s, headers:%+v", r.URL.String(), r.Header)

	// 限流检查
	rateLimiter := s.globalParamLoader.GetRateLimiter(s.getFunctionName())
	if rateLimiter != nil && !rateLimiter.Allow() {
		s.log.Errorf("QueryTasksWithSourceStream throttled by qps url:%s", r.URL.String())
		return nil, s.WrapErrorWithReasonAndMessage(ThrottledErrorCode, "throttled by qps")
	}

	// 解析请求参数
	var param lc.QueryTasksWithSourceStreamParam
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&param)
	if err != nil {
		s.log.Errorf("QueryTasksWithSourceStream decode request error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(ParameterErrorCode, fmt.Sprintf("QueryTasksWithSourceStream decode request error, err:%v", err))
	}

	// 验证参数
	if param.Domain == "" || param.App == "" || param.Stream == "" {
		return nil, s.WrapErrorWithReasonAndMessage(InvalidParameterNullValue, "domain, app, and stream cannot be empty")
	}

	// 查询任务
	tasks, err := s.queryTasksWithSourceStream(r.Context(), param)
	if err != nil {
		s.log.Errorf("QueryTasksWithSourceStream queryTasksWithSourceStream error, err:%v", err)
		return nil, s.WrapErrorWithReasonAndMessage(InternalErrorCode, fmt.Sprintf("QueryTasksWithSourceStream queryTasksWithSourceStream error, err:%v", err))
	}

	if tasks == nil {
		tasks = []*lc.TranscodeTask{}
	}

	s.log.Infof("QueryTasksWithSourceStream getTasks num = %d", len(tasks))

	response := map[string]interface{}{
		"tasks": tasks,
	}

	return s.LiveTranscodeResult("Success", "Success", response), nil
}

// queryTasksWithSourceStream 查询带有源流的转码任务
func (s *TaskService) queryTasksWithSourceStream(ctx context.Context, param lc.QueryTasksWithSourceStreamParam) ([]*lc.TranscodeTask, error) {
	// 生成源流ID
	sourceStreamId := lcUtils.GenerateStreamId(param.Domain, param.App, param.Stream)

	// 查询源流任务 - 通过streamTaskBiz访问sourceStreamTaskRepo
	sourceStreamTasks, err := s.streamTaskBiz.ListBySourceStreamId(ctx, sourceStreamId)
	if err != nil {
		return nil, fmt.Errorf("failed to query source stream tasks: %v", err)
	}

	if len(sourceStreamTasks) == 0 {
		return []*lc.TranscodeTask{}, nil
	}

	tasks, err := s.streamTaskBiz.GetRunningTasks(ctx, []string{"livetranscode"})
	if err != nil {
		s.log.Errorf("queryTasksWithSourceStream, failed to get tasks,err:%v ", err)
		return nil, fmt.Errorf("failed to get tasks,err:%v", err)
	}
	// 构建转码任务列表
	return s.constructTranscodeTasks(tasks, sourceStreamTasks), nil
}

// constructTranscodeTasks 构建转码任务列表
func (s *TaskService) constructTranscodeTasks(tasks []*models.Task, sourceStreamTasks []*domain.SourceStreamTask) []*lc.TranscodeTask {
	if tasks == nil || len(tasks) == 0 || sourceStreamTasks == nil || len(sourceStreamTasks) == 0 {
		return []*lc.TranscodeTask{}
	}

	var transcodeTasks []*lc.TranscodeTask
	// 创建源流任务映射
	sourceStreamMap := make(map[string]*domain.SourceStreamTask)
	for _, sourceStreamTask := range sourceStreamTasks {
		sourceStreamMap[sourceStreamTask.TaskId] = sourceStreamTask
	}

	// 构建转码任务
	for _, task := range tasks {
		sourceStreamTask, exists := sourceStreamMap[task.TaskId]
		if !exists {
			continue
		}

		transcodeTask := &lc.TranscodeTask{
			TaskId:     task.TaskId,
			Env:        "pub",
			DestStream: sourceStreamTask.Stream,
			WorkerIp:   task.Worker.Ip,
		}

		transcodeTasks = append(transcodeTasks, transcodeTask)
	}
	return transcodeTasks
}
