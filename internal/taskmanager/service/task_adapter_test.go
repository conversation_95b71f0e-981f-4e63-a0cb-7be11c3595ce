package service

import (
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/quota"
	"mpp/internal/taskmanager/biz/tasker"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/custom_types"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/bytedance/mockey"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"

	"github.com/stretchr/testify/assert"
	pb "proto.mpp/api/taskmanager/v1"
)

var testData = `
{
  "engineParamsUrl": null,
  "engineModel": "mpp-integration-test-engine",
  "product": "mpp-integration-test",
  "userData": "{\"engineModel\":\"mpp-integration-test-engine\",\"jobId\":\"665cf8e292be420c82b5df1825bf46de\",\"product\":\"mpp-integration-test\",\"resourceType\":\"cpu\",\"tag\":\"mpp-tag-gproxy\",\"taskType\":\"REAL_TIME\"}",
  "index": 2,
  "notify": "http://21.43.81.124:8080/cluster/task/callback",
  "jobId": "665cf8e292be420c82b5df1825bf46de",
  "trace": {
    "product": "mpp-integration-test",
    "userId": "mpp",
    "subUserId": null,
    "clientTrace": null,
    "requestId": "7310313D-B6A5-4B84-A9BA-B293D4DBA777",
    "extend": null,
    "originQuotaSet": null,
    "jobType": null,
    "taskCreateTime": null
  },
  "commandParams": null,
  "engineParams": "{\"sleepTime\":86400000}",
  "tag": "mpp-tag-gproxy",
  "quotaSet": {
    "cpu": 100
  },
  "taskId": "665cf8e292be420c82b5df1825bf46de",
  "engineEntrance": null
}
`
var (
	taskAdapterRequestBody  = `{"engineParams":{"sleepTime":300000},"metadata":{"syncstart":"true"}}`
	dagTaskEngineParamsBody = `{"engineParams":{
  "streamSourceBitrate": 2441,
  "streamSourceWidth": 1088,
  "streams": [
    {
      "rtmpDest": "rtmp://3.3.3.3/test/stream-111_sd5?key=value",
      "vfilter": "no_ph_vf[0:v]blendimages=json={}[vfo]",
      "input_bypass": "-threads 2",
      "fps": "1:30",
      "env": "pub",
      "type": "transcode",
      "ffver": "ffv5",
      "liveDictator": "livedictator-l2-cn-beijing-inner.alivecdn.com",
      "sw2main": 3,
      "width": -2,
      "x265_params": "qpmax=45:all-p-refresh=1:max-merge=5:ref=2:refreshframe-rc=1:cutree-motion-beta=1:rc-lookahead=5:frame-threads=2:numtl=1:info=0:bframes=3:ctb3-fix=1:scenecut=0:open-gop=0:aq-mode=4:aq-strength=1.2:keyint=-1:bitrate=1000:vbv-maxrate=1200:vbv-bufsize=1500 -preset faster -tune ssim -vcopybr 200:1:1600 -copyts -optimize_cts -force_key_frames source -enable_hevcdec_opt 1 -enable_avx2_opt 1 -ignore_subsei_ex 1 -qsd_flush -sei_hold_all -force_avsync -sei_order_by_dts -colorinfo_bypass all -global_setopt scalemaxthreads=8",
      "afilter": "",
      "output_bypass": "-cqfactor 25 -annexb 0 -rule adapt_revertv2_540",
      "aCodec": "copy",
      "height": 540,
      "vCodec": "librs265",
      "parallel":"3"
    }
  ],
  "streamNum": 1,
  "streamSource": "rtmp://*******/test/mpp.aliyun.com_stream-111?key=value",
  "streamSourceCodec": "h265",
  "streamSourceFps": 22,
  "streamSourceHeight": 1920,
  "streamTimestampOffset": 0.0,
  "region": "cn-beijing",
  "switchStreams": [],
  "taskId": "mpp.aliyun.com/18ad03fa-b8bc-3592-b572-5715a44fa0e2",
  "streamSourceType": "stream",
  "http_header_host": "livetransworker.alivecdn.com",
  "quota": 375}}`
	adapterConfig = &conf.Bootstrap{
		Data: &conf.Data{
			Redis: &conf.Data_Redis{
				Endpoint: "redis://127.0.0.1:6379",
			},
		},
	}

	defaultSuccessResult = &pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}

	stdStartUrl  = "http://***************:80/job/async/start?scheduleParams=%7B%22quotaSet%22%3A%7B%22cpu%22%3A1%7D%7D&product=mpp-integration-test&engineModel=mpp-integration-test-engine&tag=mpp-new-test&userId=mpp"
	stdStopUrl   = "http://***************:80/job/async/stop?jobId=00-3-65ddedd8-961fbfecd329106e24fb2161e547f25f&product=mediaai&userId=mpp"
	stdCancelUrl = "http://***************:80/job/async/cancel?jobId=002-817e70f55a255e78ab879aff989a9ab4&product=mpp-integration-test&userId=mpp&AppCode=3cbdb71021954603a282d7bed8f72410"

	DefaultQuotaSetTest = map[string]int64{
		"cpu": 8000,
	}

	defaultModelTask = &models.Task{
		TaskId:         "123test",
		JobId:          "123test",
		UserId:         "mpp",
		Product:        "mpp-integration-test",
		BizStatus:      2,
		InternalStatus: 2,
		UserData:       "userData",
		RequestResource: &models.RequestResource{
			Quota: DefaultQuotaSetTest,
		},
		Result: &models.TaskResult{
			Message: "success",
			Code:    200,
		},
		GmtCreate:    time.Now(),
		LastModified: time.Now(),
		Notify:       "mns://1426105496228119.mns.cn-shanghai.aliyuncs.com/queues/mpp-notify-test,mns://1426105496228119.mns.cn-shanghai.aliyuncs.com/queues/mpp-notify-test?dequeued=inner",
	}

	testjobData = `{
  "ip":"************",
  "traceJobId":"",
  "startTime":"2024-10-12 09:03:12",
  "results":{
    "signUrl_bucket-empty_530/transition/smart_mix_general_no_tts_1721612336347_HBzFUVWuN__1721612336347_HBzFUVWuN_0.mp4":{
      "result":"http://bucket-empty.oss-cn-shanghai.aliyuncs.com/530%2Ftransition%2Fsmart_mix_general_no_tts_1721612336347_HBzFUVWuN__1721612336347_HBzFUVWuN_0.mp4?Expires=1728810192&OSSAccessKeyId=STS.NT5H84xnTLAghfwazGncfwkT8&Signature=73QlV2LABWPGGgletwp5a7ri4xY%3D&security-token=CAISxAJ1q6Ft5B2yfSjIr5eAA4KAlbF1%2B4OMakDGhXoSYuxKmK7%2F2jz2IHhMenBvBOoat%2F0wnGtQ5v4elq1vRoRZHekSpzewtMY5yxioRqacke7XhOV2pf%2FIMGyXDAGBr622Su7lTdTbV%2B6wYlTf7EFayqf7cjPQND7Mc%2Bf%2B6%2FhdY88QQxOzYBdfGd5SPXECksIBMmbLPvvfWXyDwEioVRoz4VAk0zkksf3nkpfGtSCz1gOqlrUnwK3qOYWhYsVWO5Nybsy4xuQedNCaiHYOskcXrvsv1fMcom%2BX5o2Hf3BV4gSbNcv%2F3%2FFiIgZkYrQAHKpJvBEBWWfCVA%2B5fe3VvPUtVYk9O0y3LAvwU0tGj26HHGKYZGRWSpXcU6Fux6OPxycOS1zlGk3kFaRPJAH%2Bd%2FIi5L90xM3D2hT%2BBi3HLQztMcmv%2BcQdpz0agAFswjy4tconZgfSgoCUT8RIsO1m0WvT00PYZDGuZPylvI7Grp13KohO4ZAs2UDAHuime6Mv3M28Z1XX%2F6LWPT5m%2BYo4T%2BVPehUAmP0hR5uietvEn492EiVvu83s8mBBGBe2M5ii7XMoFd9VcwJG2n0rPSj3yMPnNZHcXf1dWQUC3CAA",
      "errorMessage":"",
      "error_code":0,
      "status":"succeed"
    }
  },
  "version":"v0.1.0",
  "timecost":21
}`
)

func init() {
	//queueServer, _, _ = server.NewAsynqServer(&conf.Bootstrap{Data: &conf.Data{Redis: &conf.Data_Redis{Endpoint: "redis://127.0.0.1:6379"}}}, nil)
}
func TestJsonParse(t *testing.T) {
	js := json.Get([]byte(testData))
	in := &pb.StartTaskRequest{
		RequestId:       js.Get("trace").Get("requestId").ToString(),
		TaskId:          js.Get("taskId").ToString(),
		JobId:           js.Get("jobId").ToString(),
		Quota:           nil,
		Product:         js.Get("product").ToString(),
		EngineModel:     js.Get("engineModel").ToString(),
		Tag:             js.Get("tag").ToString(),
		EngineParams:    js.Get("engineParams").ToString(),
		EngineParamsUrl: js.Get("engineParamsUrl").ToString(),
		Notify:          js.Get("notify").ToString(),
		Trace:           js.Get("trace").ToString(),
		UserData:        js.Get("userData").ToString(),
		Affinity:        nil,
		QuotaSet:        map[string]int64{},
	}

	js.Get("quotaSet").ToVal(&in.QuotaSet)

	assert.Equal(t, int64(100), in.QuotaSet["cpu"])

}

func TestResultError(t *testing.T) {
	result := taskService.ResultError(errors.New(500, "test_error_reason", "test_error_reason"))
	assert.NotNil(t, result)
}

func TestTaskService_StdStartTask(t *testing.T) {
	//
	//taskService.taskTracer = tasker_tracing.NewTracer()
	////dag任务启动流程测试
	//mockey.PatchConvey("StdStartTask and start dag task", t, func() {
	//	dagStdStartUrl := "http://***************:80/job/async/start?scheduleParams=%7B%22quotaSet%22%3A%7B%22cpu%22%3A1%7D%2C%22parallelNum%22%3A3%7D&product=mpp-integration-test&engineModel=mpp-integration-test-engine&tag=mpp-new-test&userId=mpp"
	//
	//	req, _ := http.NewRequest("POST", dagStdStartUrl, strings.NewReader(dagTaskEngineParamsBody))
	//	req.Header.Add("User-Agent", "my-app/0.0.1")
	//	//mockey.Mock((*TaskService).StartTask).Return(defaultSuccessResult, nil).Build()
	//	mockey.Mock((*impl.TaskRepo).FindByID).Return(nil).Build()
	//	mockey.Mock((*impl.TaskRepo).InsertUpdateOnDuplicate).Return(nil).Build()
	//	_, err := taskService.StdStartTask(nil, req)
	//	assert.Nil(t, err)
	//})
	//返回成功
	mockey.PatchConvey("StdStartTask and return rtc-robot result", t, func() {
		req, _ := http.NewRequest("POST", stdStartUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		//mockey.Mock((*TaskService).StartTask).Return(defaultSuccessResult, nil).Build()
		rst := &pb.StartTaskReply{
			Result: defaultSuccessReplyResult,
		}
		rst.Result.Metadata = custom_types.Metadata{
			"rtc-robot": "rtc-robot-start-result-test",
		}
		mockey.Mock((*TaskService).StartTask).Return(rst, nil).Build()
		_, err := taskService.StdStartTask(nil, req)
		assert.Nil(t, err)
	})
	//返回成功
	mockey.PatchConvey("StdStartTask and startTask return success", t, func() {
		req, _ := http.NewRequest("POST", stdStartUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		//mockey.Mock((*TaskService).StartTask).Return(defaultSuccessResult, nil).Build()
		mockey.Mock((*tasker.CommonTaskBiz).StartTask).Return(nil).Build()
		_, err := taskService.StdStartTask(nil, req)
		assert.Nil(t, err)
	})

}

func TestTaskService_StdStopTask(t *testing.T) {

	//返回成功
	mockey.PatchConvey("StdStopTask and stopTask return success", t, func() {
		req, _ := http.NewRequest("POST", stdStopUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		//mockey.Mock((*TaskService).StopTask).Return(defaultSuccessResult, nil).Build()
		mockey.Mock((*tasker.CommonTaskBiz).StopTask).Return(nil).Build()
		_, err := taskService.StdStopTask(nil, req)
		assert.Nil(t, err)
	})

}

func TestTaskService_StdUpdateTask(t *testing.T) {
	//返回成功
	stdUpdateUrl := "http://***************:80/job/async/update?jobId=002-817e70f55a255e78ab879aff989a9ab4&product=mpp-integration-test&userId=mpp&AppCode=3cbdb71021954603a282d7bed8f72410"
	mockey.PatchConvey("StdUpdateTask and updateTask return success", t, func() {
		req, _ := http.NewRequest("POST", stdUpdateUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		//mockey.Mock((*TaskService).UpdateTask).Return(defaultSuccessResult, nil).Build()
		mockey.Mock((*tasker.CommonTaskBiz).UpdateTask).Return(nil).Build()
		_, err := taskService.StdUpdateTask(nil, req)
		assert.Nil(t, err)
	})
}

func TestTaskService_StdSubmitTask(t *testing.T) {

	//返回成功
	mockey.PatchConvey("_StdSubmitTask meta", t, func() {
		stdSubmitUrl := "http://***************:80/job/async/submit?jobId=003-qinxin-test-notify&product=mpp-integration-test&engineModel=mpp-integration-test-engine&tag=mpp-new-test&userId=mpp&notify=mns://1426105496228119.mns.cn-shanghai-internal.aliyuncs.com/queues/mpp-qinxin-test,mns://1426105496228119.mns.cn-shanghai-internal.aliyuncs.com/queues/mpp-qinxin-test?vendor=inner&dequeueNotify=mns://1426105496228119.mns.cn-shanghai-internal.aliyuncs.com/queues/mpp-qinxin-test"
		taskAdapterRequestBody = `{"engineParams":{"sleepTime":300000},"scheduleParams":"{\"quotaSet\":{\"gpu\":2000000}}"}`
		req, _ := http.NewRequest("POST", stdSubmitUrl, strings.NewReader(taskAdapterRequestBody))

		req.Header.Add("User-Agent", "my-app/0.0.1")
		//mockey.Mock((*TaskService).SubmitTask).Return(defaultSuccessResult, nil).Build()
		mockey.Mock((*tasker.BatchTaskBiz).SubmitTask).Return(nil, nil).Build()
		mockey.Mock((*quota.QuotaService).GetAnalysisQuota).Return(DefaultQuotaSetTest, nil).Build()
		_, err := taskService.StdSubmitTask(nil, req)
		assert.Nil(t, err)
	})
	//返回成功
	mockey.PatchConvey("_StdSubmitTask and submitTask return success", t, func() {
		stdSubmitUrl := "http://***************:80/job/async/submit?jobId=003-qinxin-test-notify&scheduleParams=%7B%22quotaSet%22%3A%7B%22cpu%22%3A1%7D%7D&product=mpp-integration-test&engineModel=mpp-integration-test-engine&tag=mpp-new-test&userId=mpp&notify=mns://1426105496228119.mns.cn-shanghai-internal.aliyuncs.com/queues/mpp-qinxin-test,mns://1426105496228119.mns.cn-shanghai-internal.aliyuncs.com/queues/mpp-qinxin-test?vendor=inner&dequeueNotify=mns://1426105496228119.mns.cn-shanghai-internal.aliyuncs.com/queues/mpp-qinxin-test"
		req, _ := http.NewRequest("POST", stdSubmitUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		//mockey.Mock((*TaskService).SubmitTask).Return(defaultSuccessResult, nil).Build()
		mockey.Mock((*tasker.BatchTaskBiz).SubmitTask).Return(nil, nil).Build()
		mockey.Mock((*quota.QuotaService).GetAnalysisQuota).Return(DefaultQuotaSetTest, nil).Build()
		_, err := taskService.StdSubmitTask(nil, req)
		assert.Nil(t, err)
	})
}

func TestTaskService_StdCancelTask(t *testing.T) {
	//返回成功
	mockey.PatchConvey("StdCancelTask and CancelTask return success", t, func() {
		req, _ := http.NewRequest("POST", stdCancelUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		//mockey.Mock((*TaskService).CancelTask).Return(defaultSuccessResult, nil).Build()
		mockey.Mock((*tasker.BatchTaskBiz).CancelTask).Return(nil).Build()
		_, err := taskService.StdCancelTask(nil, req)
		assert.Nil(t, err)
	})
}

func TestTaskService_StdQueryTask(t *testing.T) {
	//单任务查询 返回成功
	mockey.PatchConvey("SingleTask，StdQueryTask and GetTask return success", t, func() {
		stdQueryUrl := "http://***************:80/job/async/query?taskId=002-817e70f55a255e78ab879aff989a9ab4"
		req, _ := http.NewRequest("POST", stdQueryUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		//mockey.Mock((*TaskService).GetTask).Return(
		//	&pb.GetTaskReply{
		//		Result: defaultSuccessReplyResult,
		//		Data: &pb.GetTaskReply_Data{
		//			Task: &pb.TaskDetail{
		//				TaskId: "665cf8e292be420c82b5df1825bf46de",
		//			},
		//		}}, nil).Build()
		mockey.Mock((*tasker.CommonTaskBiz).GetTask).Return(defaultModelTask, nil).Build()
		result, err := taskService.StdQueryTask(nil, req)
		assert.NotNil(t, result)
		assert.Nil(t, err)
	})
	//多任务查询 返回成功
	mockey.PatchConvey("，StdQueryTask and GetTask return success", t, func() {
		stdQueryUrl := "http://***************:80/job/async/query?jobIds=002-817e70f55a255e78ab879aff989a9ab4"
		req, _ := http.NewRequest("POST", stdQueryUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		mockey.Mock((*TaskService).GetTask).Return(
			&pb.GetTaskReply{
				Result: defaultSuccessReplyResult,
				Data: &pb.GetTaskReply_Data{
					Task: &pb.TaskDetail{
						TaskId: "665cf8e292be420c82b5df1825bf46de",
					},
				}}, nil).Build()
		_, err := taskService.StdQueryTask(nil, req)
		assert.Nil(t, err)
	})
}

func TestTaskService_StdInvokeTask(t *testing.T) {
	//返回成功
	mockey.PatchConvey("StdInvokeTask and InvokeTask return success", t, func() {
		req, _ := http.NewRequest("POST", stdStartUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		mockey.Mock((*TaskService).InvokeTask).Return(&pb.InvokeTaskReply{
			Result: defaultSuccessReplyResult,
			Job:    nil,
			Data:   testjobData,
		}, nil).Build()
		result, err := taskService.StdInvokeTask(nil, req)
		assert.NotNil(t, result)
		assert.Nil(t, err)
	})

	//返回失败
	mockey.PatchConvey("StdInvokeTask and InvokeTask return err", t, func() {
		req, _ := http.NewRequest("POST", stdStartUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		mockey.Mock((*TaskService).InvokeTask).Return(nil, common.ErrInternalError).Build()
		result, err := taskService.StdInvokeTask(nil, req)
		log.Infof("StdInvokeTask result: %+v ,err:%v", result, err)
		assert.NotNil(t, err)
	})
}

func TestLiveStartTask(t *testing.T) {
	liveTaskAdapterRequestBody := `{"weight":100}`
	//录制时移adapt成功
	mockey.PatchConvey("LiveTaskStart adapt to live record and return success", t, func() {
		req, _ := http.NewRequest("POST", "http://***************:80/task/start", strings.NewReader(liveTaskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		mockey.Mock((*TaskService).RecordHlsStartTask).Return(nil, nil).Build()
		_, err := taskService.LiveStartTask(nil, req)
		assert.Nil(t, err)
	})

	liveTaskAdapterRequestBody = `{"token":100}`
	//直播转码adapt成功
	mockey.PatchConvey("LiveTaskStart adapt to live transcode and return success", t, func() {
		req, _ := http.NewRequest("POST", "http://***************:80/task/start", strings.NewReader(liveTaskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		mockey.Mock((*TaskService).LiveTranscodeStartTask).Return(nil, nil).Build()
		_, err := taskService.LiveStartTask(nil, req)
		assert.Nil(t, err)
	})

	liveTaskAdapterRequestBody = `{"code":100}`
	//未命中adapt，失败
	mockey.PatchConvey("LiveTaskStart failed to adapt to return err", t, func() {
		req, _ := http.NewRequest("POST", "http://***************:80/task/start", strings.NewReader(liveTaskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		_, err := taskService.LiveStartTask(nil, req)
		assert.NotNil(t, err)
		assert.Contains(t, err.Error(), "not support task type")
	})
}

func TestLiveStopTask(t *testing.T) {
	liveTaskAdapterRequestBody := `{"soft":100}`
	//录制时移adapt成功
	mockey.PatchConvey("LiveTaskStart adapt to live record and return success", t, func() {
		req, _ := http.NewRequest("POST", "http://***************:80/task/stop", strings.NewReader(liveTaskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		mockey.Mock((*TaskService).LiveTranscodeStopTask).Return(nil, nil).Build()
		_, err := taskService.LiveStopTask(nil, req)
		assert.Nil(t, err)
	})

	liveTaskAdapterRequestBody = `{"appName":100}`
	//直播转码adapt成功
	mockey.PatchConvey("LiveTaskStart adapt to live transcode and return success", t, func() {
		req, _ := http.NewRequest("POST", "http://***************:80/task/stop", strings.NewReader(liveTaskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		mockey.Mock((*TaskService).RecordHlsStopTask).Return(nil, nil).Build()
		_, err := taskService.LiveStopTask(nil, req)
		assert.Nil(t, err)
	})

	liveTaskAdapterRequestBody = `{"code":100}`
	//未命中adapt，失败
	mockey.PatchConvey("LiveTaskStart failed to adapt to return err", t, func() {
		req, _ := http.NewRequest("POST", "http://***************:80/task/stop", strings.NewReader(liveTaskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		_, err := taskService.LiveStopTask(nil, req)
		assert.NotNil(t, err)
		assert.Contains(t, err.Error(), "not support task type")
	})
}

//func TestTaskService_StdCommandTask(t *testing.T) {
//	//返回成功
//	mockey.PatchConvey("StdCommandTask and CommandTask return success", t, func() {
//		req, _ := http.NewRequest("POST", stdStartUrl, strings.NewReader(taskAdapterRequestBody))
//		req.Header.Add("User-Agent", "my-app/0.0.1")
//		_, err := taskService.StdCommandTask(nil, req)
//		assert.NotNil(t, err)
//	})
//}
