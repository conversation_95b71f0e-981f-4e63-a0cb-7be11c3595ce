package service

import (
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"

	"mpp/internal/taskmanager/biz/pipeline"
	"mpp/internal/taskmanager/biz/sentinel"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	"mpp/pkg/tracer/tasker_tracing"
	"mpp/pkg/utils"

	v1 "proto.mpp/api/common/v1"
	pb "proto.mpp/api/taskmanager/v1"
)

type PipelineService struct {
	pb.UnimplementedPipelineServer
	commonPipelineBiz *pipeline.CommonPipelineBiz
	sentinelBiz       *sentinel.SentinelBiz

	taskTracer *tasker_tracing.Tracer
	log        *log.Helper
}

func NewPipelineService(bootstrap *conf.Bootstrap, commonPipelineBiz *pipeline.CommonPipelineBiz, sentinelBiz *sentinel.SentinelBiz, logger log.Logger) *PipelineService {
	return &PipelineService{
		commonPipelineBiz: commonPipelineBiz,
		sentinelBiz:       sentinelBiz,
		taskTracer:        tasker_tracing.NewTracer(tasker_tracing.WithProducerSpanKind()),
		log:               log.NewHelper(logger),
	}
}

func (s *PipelineService) CreatePipeline(ctx context.Context, req *pb.CreatePipelineRequest) (*pb.CreatePipelineReply, error) {
	var (
		err  error
		resp *pb.CreatePipelineReply
	)
	ctx = utils.SetRequestId(ctx)
	if req.UserId == "" {
		return nil, errors.New("CreatePipline failed, userId is nil")
	}
	pipelineModel := &models.Pipeline{
		UserId: req.UserId,
		Type:   v1.PipelineType_SHARED,
		Status: v1.PipelineStatus_ACTIVE,
	}
	//生成pipelineId
	pipelineModel.PipelineId = models.GeneratePipelineId()
	_, err = s.commonPipelineBiz.CreatePipeline(ctx, pipelineModel)
	if err != nil {
		s.log.WithContext(ctx).Errorf("CreatePipline failed,err:%v", err)
		return nil, err
	}

	resp = &pb.CreatePipelineReply{
		Result: defaultSuccessReplyResult,
		Data: &pb.CreatePipelineReply_Data{
			PipelineId: pipelineModel.PipelineId,
			UserId:     pipelineModel.UserId,
			Status:     v1.PipelineStatus_name[int32(pipelineModel.Status)],
		},
	}
	s.log.WithContext(ctx).Infof("CreatePipline success, pipeline:%s , resp:%v", pipelineModel.PipelineId, resp)
	return resp, nil

}

func (s *PipelineService) ActivatePipeline(ctx context.Context, req *pb.ActivatePipelineRequest) (*pb.ActivatePipelineReply, error) {

	var (
		err  error
		resp *pb.ActivatePipelineReply
	)
	ctx = utils.SetRequestId(ctx)

	if req.PipelineId == "" || req.UserId == "" {
		return nil, errors.New("ActivatePipline failed, pipelineId or userId is nil")
	}

	pipelineModel := &models.Pipeline{
		PipelineId: req.PipelineId,
		UserId:     req.UserId,
		Type:       v1.PipelineType_SHARED,
		Status:     v1.PipelineStatus_ACTIVE,
	}
	_, err = s.commonPipelineBiz.UpdatePipelineStatus(ctx, pipelineModel)
	if err != nil {
		s.log.WithContext(ctx).Errorf("ActivatePipline failed,err:%v", err)
		return nil, err
	}
	resp = &pb.ActivatePipelineReply{
		Result: defaultSuccessReplyResult,
	}
	s.log.WithContext(ctx).Infof("ActivatePipline success, pipeline:%s , resp:%v", pipelineModel.PipelineId, resp)
	return resp, nil
}
func (s *PipelineService) PausePipeline(ctx context.Context, req *pb.PausePipelineRequest) (*pb.PausePipelineReply, error) {
	var (
		err  error
		resp *pb.PausePipelineReply
	)
	ctx = utils.SetRequestId(ctx)

	if req.PipelineId == "" || req.UserId == "" {
		return nil, errors.New("ActivatePipline failed, pipelineId or userId is nil")
	}
	pipelineModel := &models.Pipeline{
		PipelineId: req.PipelineId,
		UserId:     req.UserId,
		Type:       v1.PipelineType_SHARED,
		Status:     v1.PipelineStatus_PAUSED,
	}
	_, err = s.commonPipelineBiz.UpdatePipelineStatus(ctx, pipelineModel)
	if err != nil {
		s.log.WithContext(ctx).Errorf("PausePipeline failed,err:%v", err)
		return nil, err
	}
	resp = &pb.PausePipelineReply{
		Result: defaultSuccessReplyResult,
	}
	s.log.WithContext(ctx).Infof("PausePipeline success, pipeline:%s , resp:%v", pipelineModel.PipelineId, resp)
	return resp, nil
}

// todo 目前仅更新主管道调度级别，后续quota更新操作依赖子管道实现和排队实现
func (s *PipelineService) UpdateQuotaConfigPipeline(ctx context.Context, req *pb.UpdateQuotaConfigPipelineRequest) (*pb.UpdateQuotaConfigPipelineReply, error) {
	var (
		err  error
		resp *pb.UpdateQuotaConfigPipelineReply
	)
	ctx = utils.SetRequestId(ctx)

	//参数校验
	if req.Product == "" || req.UserId == "" || req.PipelineId == "" || req.EngineModel == "" || req.QuotaConfig == nil {
		return nil, errors.New("UpdateQuotaConfigPipeline failed, product or userId or pipelineId or engineModel or quotaConfig is nil")
	}
	if req.Tag == "" {
		req.Tag = models.DefaultTaskTag
	}
	if req.ScheduleLevel == "" {
		req.ScheduleLevel = v1.PipelineSchedulerLevel_name[int32(v1.PipelineSchedulerLevel_STANDARD)]
	}

	pipelineModel := &models.Pipeline{
		PipelineId:    req.PipelineId,
		UserId:        req.UserId,
		ScheduleLevel: v1.PipelineSchedulerLevel(v1.PipelineSchedulerLevel_value[req.ScheduleLevel]),
		Type:          v1.PipelineType_SHARED,
	}
	if req.ScheduleLevel == "" {
		pipelineModel.ScheduleLevel = v1.PipelineSchedulerLevel_STANDARD
	}

	if _, exists := v1.PipelineSchedulerLevel_value[req.ScheduleLevel]; !exists {
		return nil, errors.New("not support scheduleLevel")
	} else {
		pipelineModel.ScheduleLevel = v1.PipelineSchedulerLevel(v1.PipelineSchedulerLevel_value[req.ScheduleLevel])
	}

	//_, err = s.commonPipelineBiz.UpdatePipelinesScheduleLevel(ctx, pipelineModel)
	if err != nil {
		s.log.WithContext(ctx).Errorf("UpdateQuotaConfigPipeline failed in UpdatePipelineScheduleLevel, pipeline:%s ,err:%v", pipelineModel.PipelineId, err)
		return nil, err
	}

	//更新流控表
	taskSentinelConfig := &models.TaskSentinelConfig{
		UserId:      req.UserId,
		PipelineId:  req.PipelineId,
		TaskType:    v1.TaskType_Async,
		Product:     req.Product,
		EngineModel: req.EngineModel,
		Tag:         req.Tag,
	}
	if req.QuotaConfig != nil {
		taskSentinelConfig.SentinelConfig = &models.SentinelConfig{
			MaxParallelNum: req.QuotaConfig.MaxParallelNum,
		}
	}
	_, err = s.sentinelBiz.CreateTaskSentinelConfig(ctx, taskSentinelConfig)
	if err != nil {
		s.log.WithContext(ctx).Errorf("UpdateQuotaConfigPipeline failed in CreateTaskSentinelConfig, pipeline:%s ,taskSentinelConfig:%+v ,err:%v", pipelineModel.PipelineId, taskSentinelConfig, err)
		return nil, err

	}

	resp = &pb.UpdateQuotaConfigPipelineReply{
		Result: defaultSuccessReplyResult,
	}

	s.log.WithContext(ctx).Infof("UpdateQuotaConfigPipeline success, pipeline:%s , resp:%v", pipelineModel.PipelineId, resp)

	return resp, nil
}

func (s *PipelineService) DeleteQuotaConfigPipeline(ctx context.Context, req *pb.DeleteQuotaConfigPipelineRequest) (*pb.DeleteQuotaConfigPipelineReply, error) {
	var (
		err  error
		resp *pb.DeleteQuotaConfigPipelineReply
	)
	ctx = utils.SetRequestId(ctx)
	//参数校验
	//if req.Product == "" || req.UserId == "" || req.PipelineId == "" || req.EngineModel == "" {
	//	return nil, errors.New("DeleteQuotaConfigPipeline failed, product or userId or pipelineId or engineModel is nil")
	//}
	if req.Tag == "" {
		req.Tag = models.DefaultTaskTag
	}
	//删除流控表
	taskSentinelConfig := &models.TaskSentinelConfig{
		UserId:           req.UserId,
		PipelineId:       req.PipelineId,
		TaskType:         v1.TaskType_Async,
		Product:          req.Product,
		EngineModel:      req.EngineModel,
		Tag:              req.Tag,
		SentinelConfigId: req.SentinelConfigId,
	}
	_, err = s.sentinelBiz.DeleteTaskSentinelConfig(ctx, taskSentinelConfig)
	if err != nil {
		s.log.WithContext(ctx).Errorf("DeleteQuotaConfigPipeline failed in DeleteTaskSentinelConfig, tasksentinelConfig:%+v ,err:%v", taskSentinelConfig, err)
		return nil, err
	}
	resp = &pb.DeleteQuotaConfigPipelineReply{
		Result: defaultSuccessReplyResult,
	}
	return resp, nil
}

func (s *PipelineService) GetQuotaConfigPipeline(ctx context.Context, req *pb.GetQuotaConfigPipelineRequest) (*pb.GetQuotaConfigPipelineReply, error) {
	var (
		err  error
		resp *pb.GetQuotaConfigPipelineReply
	)
	ctx = utils.SetRequestId(ctx)
	//参数校验
	//if req.Product == "" || req.UserId == "" || req.PipelineId == "" || req.EngineModel == "" {
	//    return nil, errors.New("GetQuotaConfigPipeline failed, product or userId or pipelineId or engineModel is nil")
	//}
	if req.Tag == "" {
		req.Tag = models.DefaultTaskTag
	}
	//查询流控表
	taskSentinelConfig := &models.TaskSentinelConfig{
		UserId:           req.UserId,
		PipelineId:       req.PipelineId,
		TaskType:         v1.TaskType_Async,
		Product:          req.Product,
		EngineModel:      req.EngineModel,
		Tag:              req.Tag,
		SentinelConfigId: req.SentinelConfigId,
	}
	getTaskSentinelConfig, err := s.sentinelBiz.GetTaskSentinelConfig(ctx, taskSentinelConfig)
	if err != nil {
		s.log.WithContext(ctx).Errorf("GetQuotaConfigPipeline failed in GetTaskSentinelConfig, tasksentinelConfig:%+v ,err:%v", taskSentinelConfig, err)
		return nil, err
	}
	resp = &pb.GetQuotaConfigPipelineReply{
		Result: defaultSuccessReplyResult,
		Data:   &pb.GetQuotaConfigPipelineReply_Data{},
	}
	if getTaskSentinelConfig != nil {
		resp.Data.SentinelConfig = &pb.SentinelConfig{
			Product:          getTaskSentinelConfig.Product,
			UserId:           getTaskSentinelConfig.UserId,
			PipelineId:       getTaskSentinelConfig.PipelineId,
			EngineModel:      getTaskSentinelConfig.EngineModel,
			Tag:              getTaskSentinelConfig.Tag,
			SentinelConfigId: getTaskSentinelConfig.SentinelConfigId,
			TaskType:         int32(getTaskSentinelConfig.TaskType),
			SentinelStatus:   int32(getTaskSentinelConfig.SentinelStatus),
			CurrentTaskNum:   getTaskSentinelConfig.CurrentTaskNum,
		}
		if getTaskSentinelConfig.SentinelConfig != nil {
			resp.Data.SentinelConfig.QuotaConfig = &pb.QuotaConfig{
				MaxParallelNum: getTaskSentinelConfig.SentinelConfig.MaxParallelNum,
			}
		}
	}

	s.log.WithContext(ctx).Infof("GetQuotaConfigPipeline success, resp:%v", resp)
	return resp, nil
}
