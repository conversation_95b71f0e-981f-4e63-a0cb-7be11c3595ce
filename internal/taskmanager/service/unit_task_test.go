package service

import (
	"net/http"
	"strings"
	"testing"

	"mpp/internal/taskmanager/biz/ha"
	"mpp/internal/taskmanager/mocks"
	"mpp/internal/taskmanager/models/domain"

	"github.com/bytedance/mockey"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
)

var testUnitTaskService *UnitTaskService

func InitUnitTaskService(ctl *gomock.Controller) {
	repo := mocks.NewMockUnitTaskRepo(ctl)
	repo.EXPECT().GetUnitTask(gomock.Any(), gomock.Any()).Return(&domain.UnitTask{
		TaskId: "17304790174071047141",
	}, nil).AnyTimes()
	repo.EXPECT().GetUnitTaskAll(gomock.Any(), gomock.Any()).Return([]*domain.UnitTask{
		{
			TaskId: "17304790174071047141",
		},
		{
			TaskId: "104817541375903751",
		},
	}, nil).AnyTimes()
	repo.EXPECT().UpdateUnitTaskStatus(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	repo.EXPECT().UpdateTaskStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	testUnitTaskService = NewUnitTaskService(repo, log.GetLogger(), nil, nil)
}

func TestUnitTaskService_GetTask(t *testing.T) {
	ctl := gomock.NewController(t)
	//mock
	defer ctl.Finish()
	InitUnitTaskService(ctl)
	req, _ := http.NewRequest("POST", "http://example.com/ha/getTask?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(`{"taskId":"17304790174071047141"}`))
	req.Header.Add("User-Agent", "my-app/0.0.1")
	result, err := testUnitTaskService.GetTask(nil, req)
	task := result.(map[string]interface{})["content"].(map[string]interface{})["task"].(*domain.UnitTask)
	assert.Nil(t, err)
	assert.Equal(t, task.TaskId, "17304790174071047141")
}

func TestUnitTaskService_GetTasks(t *testing.T) {
	ctl := gomock.NewController(t)
	//mock
	defer ctl.Finish()
	InitUnitTaskService(ctl)
	req, _ := http.NewRequest("POST", "http://example.com/ha/getTasks?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(`{"unit":"pre"}`))
	req.Header.Add("User-Agent", "my-app/0.0.1")
	result, err := testUnitTaskService.GetTasks(nil, req)
	tasks := result.(map[string]interface{})["content"].(map[string]interface{})["taskIds"].([]*domain.UnitTask)
	assert.Nil(t, err)
	assert.Equal(t, len(tasks), 2)
}

func TestUnitTaskService_SetHaStatus(t *testing.T) {
	ctl := gomock.NewController(t)
	//mock
	defer ctl.Finish()
	InitUnitTaskService(ctl)
	req, _ := http.NewRequest("POST", "http://example.com/ha/setStatus?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(`{"status":"true","host":"127.0.0.1"}`))
	req.Header.Add("User-Agent", "my-app/0.0.1")
	defer mockey.Mock((*ha.HAService).SetHaAvailable).Return(true).Build().UnPatch()
	result, err := testUnitTaskService.SetHAStatus(nil, req)
	r := result.(map[string]interface{})
	assert.Nil(t, err)
	assert.Equal(t, r["retCode"], 0)
}

func TestUnitTaskService_UpdateTask(t *testing.T) {
	ctl := gomock.NewController(t)
	//mock
	defer ctl.Finish()
	InitUnitTaskService(ctl)
	req, _ := http.NewRequest("POST", "http://example.com/ha/updateTask?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(`{"taskId":"17304790174071047141"}`))
	req.Header.Add("User-Agent", "my-app/0.0.1")
	result, err := testUnitTaskService.UpdateTask(nil, req)
	r := result.(map[string]interface{})
	assert.Nil(t, err)
	assert.Equal(t, r["retCode"], 0)
}
