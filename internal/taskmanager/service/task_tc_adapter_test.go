package service

import (
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"mpp/internal/taskmanager/biz/tasker"
	"mpp/internal/taskmanager/models"
	"net/http"
	"strings"
	"testing"
)

/**
 * @Author: qinxin
 * @Date: 2024/7/18 14:49
 * @Desc:
 * @Version 1.0
 */

var (
	tcInvokeUrl         = "http://101.133.233.140:80/task/async/invoke"
	tcInvokeRequestBody = `{"jobId":"qinxin-test-invoke","taskId":"qinxin-test-invoke","product":"mpp-integration-test","engineModel":"mpp-integration-test-engine","tag":"mpp-new-test","engineParams":{"sleepTime":20000},"quotaSet":{"cpu":1}}`

	tcSubmitUrl         = "http://101.133.233.140:80/task/async/submit"
	tcSubmitRequestBody = `{"jobId":"qinxin-test-submit","taskId":"qinxin-test-submit","product":"mpp-integration-test","engineModel":"mpp-integration-test-engine","tag":"mpp-new-test","unmigratable":false,"maxRun":6,"engineParams":{"sleepTime":20000},"quotaSet":{"cpu":1},"affinity":{"required":[{"matchExpressions":[{"key":"resourceType","operator":"In","values":["p4"]}]}],"preferred":[{"matchExpressions":[{"key":"disk","operator":"LessThan","values":["200000000000"]}]}]},"dependency":[{"taskId":"a45f19ab1f304352ea21341507598c49","fields":{"ip":"HostIP","name":"merger"}}]}`
	simpleTaskResult    = &models.SimpleTaskResult{
		TaskId: "qinxin-test-invoke",
		Data:   []byte(invokeData),
	}
	simpleTaskUrlResult = &models.SimpleTaskResult{
		TaskId:  "qinxin-test-invoke",
		DataUrl: invokeDataUrl,
	}

	invokeDataUrl = "http://url.test.com/invokeData"

	affinityMock = `{
    "required": [
        {
           "matchExpressions": [
                {
                "key": "resourceType",
                "operator": "In",
                "values": ["p4"]
                }
            ]
        }
    ],
    "preferred":[
        {
            "matchExpressions": [
                {
                    "key": "disk",
                    "operator": "LessThan",
                    "values": ["200000000000"]
                }
            ]
        }
    ]
}`
	invokeData = `{
  "data":{
    "ip":"*************",
    "results":{
      "probe_72fc76c29c0e469994a42d252336dc2c":{
        "errorMessage":"",
        "error_code":0,
        "result":{
          "duration":4550.738333,
          "format":{
            "bit_rate":"959393",
            "duration":"4550.738333",
            "filename":"http://lippi-space-sh.oss-cn-shanghai-internal.aliyuncs.com/yundisk0%2FiAEIAqRmaWxlA6h5dW5kaXNrMATOIVC8DgXNEv0GzSDgB85mmPo-CM0Cmg.file?Expires=1721390811&OSSAccessKeyId=STS.NTTiM4FRAfAn4mfnA3YdaserK&Signature=qhPPJaEBbXcSh8kn1FscARGSVnc%3D&security-token=CAISxAJ1q6Ft5B2yfSjIr5fhIveAq41g0YOFNkvXikFmVetNnKDZqTz2IHpFf3BqB%2B8csP8wlW9R7Pselq1vRoRZHc0fih6UtMY5yxioRqacke7XhOV2pf%2FIMGyXDAGBr622Su7lTdTbV%2B6wYlTf7EFayqf7cjPQND7Mc%2Bf%2B6%2FhdY88QQxOzYBdfGd5SPXECksIBMmbLPvvfWXyDwEioVRUz5lMm2TIut%2F%2Fmm5zNsyCz1gOqlrUnwK3qOYWhYsVWO5Nybsy4xuQedNCain8LskIUq%2F0o1%2FMVpm6d442Hf3BV4gSbNc%2FoyfFiIgZkYrQAHKpJvDViEy7lYGm5fe3VvPUtVYk9O0y3LAuw0V58%2BpXif1C1IQNx39gZTjQjEtiU1U1tKDlQP3hxGKEqCTRTYndXMQqBMcLJtS6MDE7NJKbtZ9i778Qdpz0agAELAfhC1c%2BNpKhr519BUF%2FkXU7BmMmbRCP3GnNcyjMzCAlAb%2B%2F7vIzerlLvBAChr7AXBV4O0wQ3EcunwRhqtXbNvyi6mhIvA0NvBKyOBccNWwO2KOEC0%2B9UdwqFIvj178MNGfqmZEAn6Y%2Bhl7dD%2Fx%2B1sM2LK3UXMlu%2F7jEidkT%2BSyAA",
            "format_long_name":"QuickTime / MOV",
            "format_name":"mov,mp4,m4a,3gp,3g2,mj2",
            "nb_programs":0,
            "nb_streams":2,
            "probe_score":100,
            "size":"545743778",
            "start_time":"0.000000",
            "tags":{
              "compatible_brands":"isom",
              "creation_time":"2024-07-18T11:16:20.000000Z",
              "major_brand":"isom",
              "minor_version":"1"
            }
          },
          "ossStorageType":"Standard",
          "streams":[
            {
              "avg_frame_rate":"25/1",
              "bit_rate":"827419",
              "bits_per_raw_sample":"10",
              "chroma_location":"left",
              "closed_captions":0,
              "codec_long_name":"H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10",
              "codec_name":"h264",
              "codec_tag":"0x31637661",
              "codec_tag_string":"avc1",
              "codec_time_base":"1/50",
              "codec_type":"video",
              "coded_height":1080,
              "coded_width":1920,
              "color_range":"tv",
              "color_space":"bt709",
              "display_aspect_ratio":"16:9",
              "disposition":{
                "attached_pic":0,
                "captions":0,
                "clean_effects":0,
                "comment":0,
                "default":1,
                "dependent":0,
                "descriptions":0,
                "dub":0,
                "forced":0,
                "hearing_impaired":0,
                "karaoke":0,
                "lyrics":0,
                "metadata":0,
                "original":0,
                "still_image":0,
                "timed_thumbnails":0,
                "visual_impaired":0
              },
              "duration":"4550.640000",
              "duration_ts":58248192,
              "extradata_size":51,
              "field_order":"progressive",
              "film_grain":0,
              "has_b_frames":2,
              "height":1080,
              "id":"0x1",
              "index":0,
              "level":40,
              "nb_frames":"113766",
              "nb_read_frames":"113766",
              "pix_fmt":"yuv420p10le",
              "profile":"High 10",
              "r_frame_rate":"25/1",
              "refs":1,
              "sample_aspect_ratio":"1:1",
              "start_pts":0,
              "start_time":"0.000000",
              "tags":{
                "creation_time":"2024-07-18T10:13:05.000000Z",
                "language":"und",
                "mov_stsd_entries":"1",
                "vendor_id":"[0][0][0][0]"
              },
              "time_base":"1/12800",
              "width":1920
            },
            {
              "avg_frame_rate":"0/0",
              "bit_rate":"127963",
              "bits_per_sample":0,
              "channel_layout":"stereo",
              "channels":2,
              "codec_long_name":"AAC (Advanced Audio Coding)",
              "codec_name":"aac",
              "codec_tag":"0x6134706d",
              "codec_tag_string":"mp4a",
              "codec_time_base":"1/44100",
              "codec_type":"audio",
              "disposition":{
                "attached_pic":0,
                "captions":0,
                "clean_effects":0,
                "comment":0,
                "default":1,
                "dependent":0,
                "descriptions":0,
                "dub":0,
                "forced":0,
                "hearing_impaired":0,
                "karaoke":0,
                "lyrics":0,
                "metadata":0,
                "original":0,
                "still_image":0,
                "timed_thumbnails":0,
                "visual_impaired":0
              },
              "duration":"4550.739592",
              "duration_ts":200687616,
              "extradata_size":5,
              "id":"0x2",
              "index":1,
              "nb_frames":"195984",
              "profile":"LC",
              "r_frame_rate":"0/0",
              "sample_fmt":"fltp",
              "sample_rate":"44100",
              "start_pts":0,
              "start_time":"0.000000",
              "tags":{
                "creation_time":"2024-07-18T10:11:57.000000Z",
                "language":"und",
                "mov_stsd_entries":"1",
                "vendor_id":"[0][0][0][0]"
              },
              "time_base":"1/44100"
            }
          ]
        },
        "status":"succeed"
      }
    },
    "startTime":"2024-07-18 12:06:51",
    "timecost":215,
    "traceJobId":"",
    "version":"v0.1.0"
  }
}`
)

func TestTaskService_TCInvokeTask(t *testing.T) {
	var invokeDataJson map[string]interface{}
	err := json.Unmarshal([]byte(invokeData), &invokeDataJson)
	assert.Nil(t, err)
	//invoke接口调用 返回string 格式 data
	//mockey.PatchConvey("TCInvokeTask  return success and invokeData", t, func() {
	//	req, _ := http.NewRequest("POST", tcInvokeUrl, strings.NewReader(tcInvokeRequestBody))
	//	req.Header.Add("User-Agent", "my-app/0.0.1")
	//	//mockey.Mock((*TaskService).UpdateTask).Return(defaultSuccessResult, nil).Build()
	//
	//	simpleTaskResult.Data = invokeDataUrl
	//	mockey.Mock((*tasker.SimpleTaskBiz).Invoke).Return(simpleTaskResult, nil).Build()
	//	result, err := taskService.TCInvokeTask(nil, req)
	//	assert.Nil(t, err)
	//	if resultMap, ok := result.(map[string]interface{}); ok {
	//		assert.Equal(t, resultMap["code"], "Success")
	//		//assert.Equal(t, resultMap["data"], invokeDataJson["data"])
	//	}
	//
	//})

	//invoke接口调用 返回json 格式 data
	mockey.PatchConvey("TCInvokeTask  return success and invokeData", t, func() {
		req, _ := http.NewRequest("POST", tcInvokeUrl, strings.NewReader(tcInvokeRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		//mockey.Mock((*TaskService).UpdateTask).Return(defaultSuccessResult, nil).Build()
		simpleTaskResult.Data = []byte(invokeData)
		mockey.Mock((*tasker.SimpleTaskBiz).Invoke).Return(simpleTaskResult, nil).Build()
		result, err := taskService.TCInvokeTask(nil, req)
		assert.Nil(t, err)
		if resultMap, ok := result.(map[string]interface{}); ok {
			assert.Equal(t, resultMap["code"], "Success")
			//assert.Equal(t, resultMap["data"], invokeDataJson["data"])
		}

	})
	//invoke接口调用 返回dataUrl
	mockey.PatchConvey("TCInvokeTask  return success and invokeData", t, func() {
		req, _ := http.NewRequest("POST", tcInvokeUrl, strings.NewReader(tcInvokeRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		//mockey.Mock((*TaskService).UpdateTask).Return(defaultSuccessResult, nil).Build()
		mockey.Mock((*tasker.SimpleTaskBiz).Invoke).Return(simpleTaskUrlResult, nil).Build()
		result, err := taskService.TCInvokeTask(nil, req)
		assert.Nil(t, err)
		if resultMap, ok := result.(map[string]interface{}); ok {
			assert.Equal(t, resultMap["code"], "Success")
			//assert.Equal(t, resultMap["dataUrl"], invokeDataUrl)
		}

	})
}

//func TestTaskService_TcSubmitTask(t *testing.T) {
//	//返回成功 适配tc的异步任务提交测试
//	mockey.PatchConvey("TcSubmitTask and submitTask return success", t, func() {
//		stdSubmitUrl := "http://101.133.233.140:80/job/async/submit?jobId=003-qinxin-test-notify&scheduleParams=%7B%22quotaSet%22%3A%7B%22cpu%22%3A1%7D%7D&product=mpp-integration-test&engineModel=mpp-integration-test-engine&tag=mpp-new-test&userId=mpp&notify=mns://1426105496228119.mns.cn-shanghai-internal.aliyuncs.com/queues/mpp-qinxin-test,mns://1426105496228119.mns.cn-shanghai-internal.aliyuncs.com/queues/mpp-qinxin-test?vendor=inner&dequeueNotify=mns://1426105496228119.mns.cn-shanghai-internal.aliyuncs.com/queues/mpp-qinxin-test"
//		req, _ := http.NewRequest("POST", stdSubmitUrl, strings.NewReader(tcSubmitRequestBody))
//		req.Header.Add("User-Agent", "my-app/0.0.1")
//		//mockey.Mock((*TaskService).SubmitTask).Return(defaultSuccessResult, nil).Build()
//		mockey.Mock((*tasker.BatchTaskBiz).SubmitTask).Return(nil, nil).Build()
//		mockey.Mock((*quota.QuotaService).GetAnalysisQuota).Return(DefaultQuotaSetTest, nil).Build()
//		_, err := taskService.TCSubmitTask(nil, req)
//		assert.Nil(t, err)
//	})
//
//	//返回成功 适配tc的异步任务提交测试
//	mockey.PatchConvey("TcSubmitTask and no migrateInfo", t, func() {
//		stdSubmitUrl := "http://101.133.233.140:80/job/async/submit?jobId=003-qinxin-test-notify&scheduleParams=%7B%22quotaSet%22%3A%7B%22cpu%22%3A1%7D%7D&product=mpp-integration-test&engineModel=mpp-integration-test-engine&tag=mpp-new-test&userId=mpp&notify=mns://1426105496228119.mns.cn-shanghai-internal.aliyuncs.com/queues/mpp-qinxin-test,mns://1426105496228119.mns.cn-shanghai-internal.aliyuncs.com/queues/mpp-qinxin-test?vendor=inner&dequeueNotify=mns://1426105496228119.mns.cn-shanghai-internal.aliyuncs.com/queues/mpp-qinxin-test"
//		//不带迁移信息的
//		tcTempSubmitRequestBody := `{"jobId":"qinxin-test-submit","taskId":"qinxin-test-submit","product":"mpp-integration-test","engineModel":"mpp-integration-test-engine","tag":"mpp-new-test","engineParams":{"sleepTime":20000},"quotaSet":{"cpu":1},"affinity":{"required":[{"matchExpressions":[{"key":"resourceType","operator":"In","values":["p4"]}]}],"preferred":[{"matchExpressions":[{"key":"disk","operator":"LessThan","values":["200000000000"]}]}]},"dependency":[{"taskId":"a45f19ab1f304352ea21341507598c49","fields":{"ip":"HostIP","name":"merger"}}]}`
//		req, _ := http.NewRequest("POST", stdSubmitUrl, strings.NewReader(tcTempSubmitRequestBody))
//		req.Header.Add("User-Agent", "my-app/0.0.1")
//		//mockey.Mock((*TaskService).SubmitTask).Return(defaultSuccessResult, nil).Build()
//		mockey.Mock((*tasker.BatchTaskBiz).SubmitTask).Return(nil, nil).Build()
//		mockey.Mock((*quota.QuotaService).GetAnalysisQuota).Return(DefaultQuotaSetTest, nil).Build()
//		_, err := taskService.TCSubmitTask(nil, req)
//		assert.Nil(t, err)
//	})
//
//}

func TestTaskService_TcCancelTask(t *testing.T) {
	//返回成功
	mockey.PatchConvey("StdCancelTask and CancelTask return success", t, func() {
		req, _ := http.NewRequest("POST", stdCancelUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		//mockey.Mock((*TaskService).CancelTask).Return(defaultSuccessResult, nil).Build()
		mockey.Mock((*tasker.BatchTaskBiz).CancelTask).Return(nil).Build()
		_, err := taskService.TCCancelTask(nil, req)
		assert.Nil(t, err)
	})
}

func TestTaskService_TcStartTask(t *testing.T) {

	//返回成功
	mockey.PatchConvey("StdStartTask and startTask return success", t, func() {
		req, _ := http.NewRequest("POST", stdStartUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		//mockey.Mock((*TaskService).StartTask).Return(defaultSuccessResult, nil).Build()
		mockey.Mock((*tasker.CommonTaskBiz).StartTask).Return(nil).Build()
		_, err := taskService.TCStartTask(nil, req)
		assert.Nil(t, err)
	})
}

func TestTaskService_TcStopTask(t *testing.T) {

	//返回成功
	mockey.PatchConvey("StdStopTask and stopTask return success", t, func() {
		req, _ := http.NewRequest("POST", stdStopUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		//mockey.Mock((*TaskService).StopTask).Return(defaultSuccessResult, nil).Build()
		mockey.Mock((*tasker.CommonTaskBiz).StopTask).Return(nil).Build()
		_, err := taskService.TCStopTask(nil, req)
		assert.Nil(t, err)
	})

}

func TestTaskService_TcUpdateTask(t *testing.T) {
	//返回成功
	stdUpdateUrl := "http://101.133.233.140:80/job/async/update?jobId=002-817e70f55a255e78ab879aff989a9ab4&product=mpp-integration-test&userId=mpp&AppCode=3cbdb71021954603a282d7bed8f72410"
	mockey.PatchConvey("StdUpdateTask and updateTask return success", t, func() {
		req, _ := http.NewRequest("POST", stdUpdateUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		//mockey.Mock((*TaskService).UpdateTask).Return(defaultSuccessResult, nil).Build()
		mockey.Mock((*tasker.CommonTaskBiz).UpdateTask).Return(nil).Build()
		_, err := taskService.TCUpdateTask(nil, req)
		assert.Nil(t, err)
	})
}
