package service

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/grayscale"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/custom_types"
	"mpp/pkg/tracer/tasker_tracing"
	pb "proto.mpp/api/taskmanager/v1"
	"runtime"
	"strings"
	"time"
)

/**
 * @Author: qinxin
 * @Date: 2024/12/26 10:30
 * @Desc: 混跑灰度服务接口实现
 * @Version 1.0
 */

type GrayscaleService struct {
	pb.UnimplementedGrayscaleServiceServer
	mixRuleConfigBiz *grayscale.MixRuleConfigBiz
	taskTracer       *tasker_tracing.Tracer
	log              *log.Helper
}

func NewGrayscaleService(logger log.Logger, mixRuleConfigBiz *grayscale.MixRuleConfigBiz) (*GrayscaleService, error) {
	return &GrayscaleService{
		mixRuleConfigBiz: mixRuleConfigBiz,
		taskTracer:       tasker_tracing.NewTracer(tasker_tracing.WithProducerSpanKind()),
		log:              log.NewHelper(logger),
	}, nil
}

func (s *GrayscaleService) CreateMixGrayConfig(ctx context.Context, req *pb.CreateMixGrayConfigRequest) (*pb.CreateMixGrayConfigReply, error) {
	var (
		err  error
		resp *pb.CreateMixGrayConfigReply
	)
	//ctx, rpcSpan := tasker_tracing.InjectRPCTrace(ctx, md)
	md := custom_types.Metadata{}
	ctx, span := s.taskTracer.Start(ctx, "/CreateMixGrayConfig", md)
	defer func() {
		s.taskTracer.End(ctx, span, resp, err)
	}()

	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req)
	defer func() {
		s.logApiOut(ctx, f, start, req, resp, err)
	}()

	mixGrayConfigProto := &pb.MixGrayConfig{
		RuleName:      req.RuleName,
		MixRatio:      req.MixRatio,
		Priority:      req.Priority,
		ConfigStatus:  req.ConfigStatus,
		MixRuleConfig: req.MixRuleConfig,
		RuleConfigs:   req.RuleConfigs,
		Extends:       req.Extends,
	}

	//生成唯一id
	mixGrayConfigProto.Id = uuid.NewString()

	createMixGrayConfig, err := models.ProtoToMixGrayConfig(mixGrayConfigProto)
	if err != nil {
		s.log.WithContext(ctx).Errorf("UpdateMixGrayConfig fail in ProtoToMixGrayConfig, req:%+v, err:%v", req, err)
		return nil, err
	}
	err = s.mixRuleConfigBiz.CreateMixGrayConfig(ctx, createMixGrayConfig)
	if err != nil {
		s.log.WithContext(ctx).Errorf("CreateMixGrayConfig fail,mixGrayConfig:%+v err:%v", createMixGrayConfig, err)
		return nil, err
	}

	resp = &pb.CreateMixGrayConfigReply{
		Result: defaultSuccessReplyResult,
	}

	return resp, err
}

func (s *GrayscaleService) UpdateMixGrayConfig(ctx context.Context, req *pb.UpdateMixGrayConfigRequest) (*pb.UpdateMixGrayConfigReply, error) {

	var (
		err  error
		resp *pb.UpdateMixGrayConfigReply
	)
	//ctx, rpcSpan := tasker_tracing.InjectRPCTrace(ctx, md)
	md := custom_types.Metadata{}
	ctx, span := s.taskTracer.Start(ctx, "/UpdateMixGrayConfig", md)
	defer func() {
		s.taskTracer.End(ctx, span, resp, err)
	}()
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req)
	defer func() {
		s.logApiOut(ctx, f, start, req, resp, err)
	}()
	mixGrayConfigProto := &pb.MixGrayConfig{
		RuleName:      req.RuleName,
		MixRatio:      req.MixRatio,
		Priority:      req.Priority,
		ConfigStatus:  req.ConfigStatus,
		MixRuleConfig: req.MixRuleConfig,
		RuleConfigs:   req.RuleConfigs,
		Extends:       req.Extends,
	}

	updataMixGrayConfig, err := models.ProtoToMixGrayConfig(mixGrayConfigProto)
	if err != nil {
		s.log.WithContext(ctx).Errorf("UpdateMixGrayConfig fail in ProtoToMixGrayConfig, req:%+v, err:%v", req, err)
		return nil, err
	}
	err = s.mixRuleConfigBiz.UpdateMixGrayConfig(ctx, updataMixGrayConfig)
	if err != nil {
		s.log.WithContext(ctx).Errorf("UpdateMixGrayConfig fail,updataMixGrayConfig:%+v err:%v", updataMixGrayConfig, err)
		return nil, err
	}

	resp = &pb.UpdateMixGrayConfigReply{
		Result: defaultSuccessReplyResult,
	}
	return resp, err
}

func (s *GrayscaleService) DeleteMixGrayConfig(ctx context.Context, req *pb.DeleteMixGrayConfigRequest) (*pb.DeleteMixGrayConfigReply, error) {
	//trace日志
	var (
		err  error
		resp *pb.DeleteMixGrayConfigReply
	)
	//ctx, rpcSpan := tasker_tracing.InjectRPCTrace(ctx, md)
	md := custom_types.Metadata{}
	ctx, span := s.taskTracer.Start(ctx, "/DeleteMixGrayConfig", md)
	defer func() {
		s.taskTracer.End(ctx, span, resp, err)
	}()
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req)
	defer func() {
		s.logApiOut(ctx, f, start, req, resp, err)
	}()

	//业务处理
	err = s.mixRuleConfigBiz.DeleteMixGrayConfig(ctx, req.RuleName)
	if err != nil {
		s.log.WithContext(ctx).Errorf("DeleteMixGrayConfig fail,ruleName:%s err:%v", req.RuleName, err)
		return nil, err
	}

	resp = &pb.DeleteMixGrayConfigReply{
		Result: defaultSuccessReplyResult,
	}
	return resp, err
}

func (s *GrayscaleService) ListMixGrayConfig(ctx context.Context, req *pb.ListMixGrayConfigRequest) (*pb.ListMixGrayConfigReply, error) {

	//trace日志
	var (
		err  error
		resp *pb.ListMixGrayConfigReply
	)
	//ctx, rpcSpan := tasker_tracing.InjectRPCTrace(ctx, md)
	md := custom_types.Metadata{}
	ctx, span := s.taskTracer.Start(ctx, "/ListMixGrayConfig", md)
	defer func() {
		s.taskTracer.End(ctx, span, resp, err)
	}()
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req)
	defer func() {
		s.logApiOut(ctx, f, start, req, resp, err)
	}()

	//业务处理

	mixGrayConfigList, err := s.mixRuleConfigBiz.ListMixGrayConfig(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("ListMixGrayConfig fail, err:%v", err)
		return nil, err
	}

	var mixGrayConfigListProto []*pb.MixGrayConfig
	for _, v := range mixGrayConfigList {
		mixGrayConfigProto, err := models.MixGrayConfigToProto(v)
		if err != nil {
			s.log.WithContext(ctx).Errorf("ListMixGrayConfig fail in MixGrayConfigToProto, MixGrayConfig:%+v, err:%v", v, err)
			return nil, err
		}
		mixGrayConfigListProto = append(mixGrayConfigListProto, mixGrayConfigProto)
	}
	resp = &pb.ListMixGrayConfigReply{
		Result: defaultSuccessReplyResult,
		Data: &pb.ListMixGrayConfigReply_Data{
			MixGrayConfigs: mixGrayConfigListProto,
		},
	}
	return resp, err

}

func (s *GrayscaleService) GetMixGrayConfig(ctx context.Context, req *pb.GetMixGrayConfigRequest) (*pb.GetMixGrayConfigReply, error) {
	return nil, common.ErrNotImplemented
}

func (s *GrayscaleService) ActiveMixGrayConfig(ctx context.Context, req *pb.ActiveMixGrayConfigRequest) (*pb.ActiveMixGrayConfigReply, error) {

	//trace日志
	var (
		err  error
		resp *pb.ActiveMixGrayConfigReply
	)
	//ctx, rpcSpan := tasker_tracing.InjectRPCTrace(ctx, md)
	md := custom_types.Metadata{}
	ctx, span := s.taskTracer.Start(ctx, "/ActiveMixGrayConfig", md)
	defer func() {
		s.taskTracer.End(ctx, span, resp, err)
	}()
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req)
	defer func() {
		s.logApiOut(ctx, f, start, req, resp, err)
	}()

	//业务处理

	err = s.mixRuleConfigBiz.ActiveMixGrayConfig(ctx, req.RuleName)
	if err != nil {
		s.log.WithContext(ctx).Errorf("ActiveMixGrayConfig fail, ruleName:%s err:%v", req.RuleName, err)
		return nil, err
	}

	resp = &pb.ActiveMixGrayConfigReply{
		Result: defaultSuccessReplyResult,
	}

	return resp, err
}
func (s *GrayscaleService) PauseMixGrayConfig(ctx context.Context, req *pb.PauseMixGrayConfigRequest) (*pb.PauseMixGrayConfigReply, error) {

	//trace日志
	var (
		err  error
		resp *pb.PauseMixGrayConfigReply
	)
	//ctx, rpcSpan := tasker_tracing.InjectRPCTrace(ctx, md)
	md := custom_types.Metadata{}
	ctx, span := s.taskTracer.Start(ctx, "/PauseMixGrayConfig", md)
	defer func() {
		s.taskTracer.End(ctx, span, resp, err)
	}()
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req)
	defer func() {
		s.logApiOut(ctx, f, start, req, resp, err)
	}()

	//业务处理
	err = s.mixRuleConfigBiz.PauseMixGrayConfig(ctx, req.RuleName)
	if err != nil {
		s.log.WithContext(ctx).Errorf("PauseMixGrayConfig fail, ruleName:%s err:%v", req.RuleName, err)
		return nil, err
	}

	resp = &pb.PauseMixGrayConfigReply{
		Result: defaultSuccessReplyResult,
	}

	return resp, err
}

func (s *GrayscaleService) MockMixGrayRuleEngine(ctx context.Context, req *pb.MockMixGrayRuleEngineRequest) (*pb.MockMixGrayRuleEngineReply, error) {
	//trace日志
	var (
		err  error
		resp *pb.MockMixGrayRuleEngineReply
	)
	//ctx, rpcSpan := tasker_tracing.InjectRPCTrace(ctx, md)
	md := custom_types.Metadata{}
	ctx, span := s.taskTracer.Start(ctx, "/MockMixGrayRuleEngine", md)
	defer func() {
		s.taskTracer.End(ctx, span, resp, err)
	}()
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req)
	defer func() {
		s.logApiOut(ctx, f, start, req, resp, err)
	}()

	//业务处理
	s.log.WithContext(ctx).Infof("MockMixGrayRuleEngine start req:%+v", req)
	mockTask := &models.Task{
		Product:     req.Product,
		EngineModel: req.EngineModel,
		Tag:         req.Tag,
		UserId:      req.UserId,
		PipelineId:  req.PipelineId,
		UserData:    req.UserData,
	}
	mixGrayConfig, err := s.mixRuleConfigBiz.ExcuteMixRuleEngine(context.Background(), mockTask)
	if err != nil {
		s.log.WithContext(ctx).Errorf("MockMixGrayRuleEngine fail, err:%v", err)
		return nil, err
	}
	resp = &pb.MockMixGrayRuleEngineReply{
		Result: defaultSuccessReplyResult,
	}
	if mixGrayConfig != nil {
		jsonStr, err := json.Marshal(mixGrayConfig)
		if err != nil {
			s.log.WithContext(ctx).Errorf("MockMixGrayRuleEngine fail in json.Marshal, MixGrayConfig:%+v, err:%v", mixGrayConfig, err)
			return nil, err
		}
		var ruleName string
		if mixGrayConfig != nil {
			ruleName = mixGrayConfig.RuleName
		}
		resp.Data = &pb.MockMixGrayRuleEngineReply_Data{
			RuleName: ruleName,
			Data:     string(jsonStr),
		}
	}
	s.log.WithContext(ctx).Infof("MockMixGrayRuleEngine success req:%v, resp:%+v", req, resp)
	return resp, nil
}

func (s *GrayscaleService) UpdateMixGrayRatio(ctx context.Context, req *pb.UpdateMixGrayRatioRequest) (*pb.UpdateMixGrayRatioReply, error) {

	var (
		err  error
		resp *pb.UpdateMixGrayRatioReply
	)
	//ctx, rpcSpan := tasker_tracing.InjectRPCTrace(ctx, md)
	md := custom_types.Metadata{}
	ctx, span := s.taskTracer.Start(ctx, "/UpdateMixGrayRatio", md)
	defer func() {
		s.taskTracer.End(ctx, span, resp, err)
	}()
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req)
	defer func() {
		s.logApiOut(ctx, f, start, req, resp, err)
	}()
	mixGrayConfigProto := &pb.MixGrayConfig{
		RuleName: req.RuleName,
		MixRatio: req.MixRatio,
	}

	updataMixGrayConfig, err := models.ProtoToMixGrayConfig(mixGrayConfigProto)
	if err != nil {
		s.log.WithContext(ctx).Infof("GrayscaleService/UpdateMixGrayRatio fail in ProtoToMixGrayConfig, req:%+v, err:%v", req, err)
		return nil, err
	}
	err = s.mixRuleConfigBiz.UpdateMixGrayRatio(ctx, updataMixGrayConfig)
	if err != nil {
		s.log.WithContext(ctx).Errorf("GrayscaleService/UpdateMixGrayRatio fail,updataMixGrayConfig:%+v err:%v", updataMixGrayConfig, err)
		return nil, err
	}

	resp = &pb.UpdateMixGrayRatioReply{
		Result: defaultSuccessReplyResult,
	}
	return resp, err
}

func (s *GrayscaleService) logApiIn(ctx context.Context, f string, req interface{}) time.Time {
	s.log.WithContext(ctx).Log(log.LevelInfo,
		//"task.id", taskId,
		"event", "api.in",
		"func", f,
		"request", req)

	return time.Now()
}

func (s *GrayscaleService) logApiOut(ctx context.Context, f string, start time.Time, req interface{}, resp interface{}, err error) {
	s.log.WithContext(ctx).Log(log.LevelInfo,
		//"task.id", taskId,
		"event", "api.out",
		"latency", time.Since(start).Milliseconds(),
		"request", req,
		"response", resp,
		"func", f,
		"error", err)
}

func (s *GrayscaleService) getFunctionName() string {
	pc := make([]uintptr, 1)
	runtime.Callers(2, pc)
	f := runtime.FuncForPC(pc[0])
	return "grayscale" + f.Name()[strings.LastIndex(f.Name(), "."):]
}
