package service

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	"io"
	"mpp/pkg/utils"

	pb "proto.mpp/api/taskmanager/v1"
)

func (s *PipelineService) ResultError(err error) any {
	return map[string]string{
		"code":    "InternalError",
		"message": err.Error(),
	}
}

func (s *PipelineService) Result(reason string, message string, rst map[string]interface{}) any {
	if rst == nil {
		rst = make(map[string]interface{})
	}
	rst["code"] = reason
	rst["message"] = message

	if rst["code"] == "" || rst["code"] == "ok" {
		rst["code"] = "Success"
	}

	return rst
}

func (s *PipelineService) AdapterCreatePipeline(w http.ResponseWriter, r *http.Request) (any, error) {
	log.Infof("AdapterCreatePipeline, url:%s, headers:%+v", r.URL.String(), r.<PERSON>er)
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	log.Infof("AdapterCreatePipeline, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)
	in := &pb.CreatePipelineRequest{
		Product: getBodyOrQueryString(js, query, "product"),
		UserId:  getBodyOrQueryString(js, query, "userId"),
	}

	newCtx := utils.SetCustomRequestId(r.Context(), "")

	out, err := s.CreatePipeline(newCtx, in)
	if err != nil {
		log.Errorf("AdapterCreatePipeline, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
		return s.ResultError(err), err
	}

	data := map[string]interface{}{
		"pipeline": out.Data,
	}
	return s.Result(out.Result.Reason, out.Result.Message, data), nil

}

func (s *PipelineService) AdapterActivatePipeline(w http.ResponseWriter, r *http.Request) (any, error) {
	log.Infof("AdapterCreatePipeline, url:%s, headers:%+v", r.URL.String(), r.Header)
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	log.Infof("AdapterCreatePipeline, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)
	in := &pb.ActivatePipelineRequest{
		Product:    getBodyOrQueryString(js, query, "product"),
		UserId:     getBodyOrQueryString(js, query, "userId"),
		PipelineId: getBodyOrQueryString(js, query, "pipelineId"),
	}

	newCtx := utils.SetCustomRequestId(r.Context(), "")

	out, err := s.ActivatePipeline(newCtx, in)
	if err != nil {
		log.Errorf("AdapterCreatePipeline, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
		return s.ResultError(err), err
	}

	return s.Result(out.Result.Reason, out.Result.Message, map[string]any{}), nil

}

func (s *PipelineService) AdapterPausePipeline(w http.ResponseWriter, r *http.Request) (any, error) {
	log.Infof("AdapterCreatePipeline, url:%s, headers:%+v", r.URL.String(), r.Header)
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	log.Infof("AdapterCreatePipeline, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)
	in := &pb.PausePipelineRequest{
		Product:    getBodyOrQueryString(js, query, "product"),
		UserId:     getBodyOrQueryString(js, query, "userId"),
		PipelineId: getBodyOrQueryString(js, query, "pipelineId"),
	}

	newCtx := utils.SetCustomRequestId(r.Context(), "")

	out, err := s.PausePipeline(newCtx, in)
	if err != nil {
		log.Errorf("AdapterCreatePipeline, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
		return s.ResultError(err), err
	}

	return s.Result(out.Result.Reason, out.Result.Message, map[string]any{}), nil

}

func (s *PipelineService) AdapterUpdateQuotaConfigPipeline(w http.ResponseWriter, r *http.Request) (any, error) {
	log.Infof("AdapterCreatePipeline, url:%s, headers:%+v", r.URL.String(), r.Header)
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	log.Infof("AdapterCreatePipeline, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)
	in := &pb.UpdateQuotaConfigPipelineRequest{
		Product:       getBodyOrQueryString(js, query, "product"),
		UserId:        getBodyOrQueryString(js, query, "userId"),
		PipelineId:    getBodyOrQueryString(js, query, "pipelineId"),
		EngineModel:   getBodyOrQueryString(js, query, "engineModel"),
		Tag:           getBodyOrQueryString(js, query, "tag"),
		ScheduleLevel: getBodyOrQueryString(js, query, "scheduleLevel"),
		RequestId:     getBodyOrQueryString(js, query, "requestId"),
	}

	quotaConfigStr := getBodyOrQueryString(js, query, "quotaConfig")
	if quotaConfigStr != "" {
		quotaConfig := &pb.QuotaConfig{}
		err = json.Unmarshal([]byte(quotaConfigStr), quotaConfig)
		if err != nil {
			log.Errorf("AdapterCreatePipeline faild in Unmarshal QuotaConfig , url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
			return s.ResultError(err), err
		}
		in.QuotaConfig = quotaConfig
	}
	newCtx := utils.SetCustomRequestId(r.Context(), "")

	out, err := s.UpdateQuotaConfigPipeline(newCtx, in)
	if err != nil {
		log.Errorf("AdapterCreatePipeline, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
		return s.ResultError(err), err
	}
	return s.Result(out.Result.Reason, out.Result.Message, map[string]any{}), nil

}
