package service

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"sync"
	"testing"
	"time"

	"golang.org/x/time/rate"

	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/global_param_loader"
	"mpp/internal/taskmanager/biz/ha"
	"mpp/internal/taskmanager/biz/livetranscode"
	lcUtils "mpp/internal/taskmanager/biz/livetranscode"
	"mpp/internal/taskmanager/biz/tasker"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/mocks"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/domain"
	lc "mpp/internal/taskmanager/models/livetranscode"
	"mpp/internal/taskscheduler/biz"
	"mpp/pkg/header"

	pb "proto.mpp/api/taskmanager/v1"

	. "github.com/bytedance/mockey"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

var testLoader *global_param_loader.GlobalParamLoader

var (
	sendCommandRequestBody = `{
  "body":{
    "cmd":"video.sei.add",
    "reqId":"B3F217A7-909B-5442-B19C-08A9EC4BB3AB",
    "sei":{
      "delay":0,
      "pattern":"frame",
      "repeatNum":5,
      "text":{"isCloseLive":1}
    }
  },
  "taskId":"bdf3d17e-d3d4-3b8c-9e27-deed2939be3f",
  "uri":"/lvfmsynccall"
}`
	updateTaskRequestBody = `{
	"taskId":"bdf3d17e-d3d4-3b8c-9e27-deed2939be3f",
	"streams":[
      {
        "destBakVips":[
          "*************:1935"
        ],
        "rtmpDest":"rtmp://*************/stage/stream-11729250232951234_ld?vhost=pull-l3.test.com&live_rtmp_test=on&ali_inter_auth_time=682d3a64&transcode=no&transcoded=true&ali_center_tran_pub=on",
        "vfilter":"no_ph_vf[0:v]blendimages=json={}[vfo]",
        "input_bypass":"-threads 3",
        "fps":"1:30",
        "vprofile":"high",
        "env":"pub",
        "type":"transcode",
        "ffver":"ffv5",
        "sw2main":3,
        "width":-2,
        "output_bypass":"-cqfactor 25 -rule adapt_res_480*852#4d"}]
}`
	statQueryReq = `{
	"tag": "test",
	"domain": "test"
}`
	editPicTaskReq = `{
		"pictures":[
			{"animate":0,"name":"WATERMARK_V_1","normalizedRefMode":0,"normalizedh":0.06666666666666667,"offsetcorner":3,"pictureurl":"https://dfl-watermark.oss.aliyuncs.com/logo/21313444/kanLIVE.png","transparency":255,"visible":1,"xoffset":0.05925925925925926,"yoffset":0.06944444444444445},
			{"animate":1,"name":"WATERMARK_V_2","normalizedRefMode":0,"normalizedh":0.06666666666666667,"offsetcorner":3,"pictureurl":"https://dfl-watermark.oss.aliyuncs.com/logo/21313444/kanLIVE.png","transparency":255,"visible":1,"xoffset":0.05925925925925926,"yoffset":0.06944444444444445}
		],
		"taskId":"ed07771e-d923-3495-ab46-d655def1f648"}`
	migrateTaskReq = `{
	 "app":"stage",
	 "domain":"pull-l3.douyincdn.com",
	 "sameDictator":true,
	 "stream":"stream-116818587919056978",
	 "taskId":"",
	 "token":"ld5",
	 "tag": ""
	}`
	//	migrateTaskReq = `{
	//  "sameDictator":true,
	//  "taskId":"00000001-3ccc-4128-85e5-4a172c1a5170"
	//}`

	startRequestBody = `{
  "appName": "stage",
  "centerPublicAddr": "*************",
  "domain": "pull-l3.test.com",
  "gslbAuthTime": "682d3a64",
  "isCdnEdge": "false",
  "isEdge": "false",
  "params": {
    "streamNum": 1,
    "streamSource": "rtmp://*************:1935/stage/pull-l3.douyincdn.com_stream-11729250232951234?ali_mapstream_ignore=on&ali_ols_play=on&ali_ols_play_type=transcoding&ali_rtmp_quick_start=off&ali_rtmp_retain=0&vhost=pull-l3.test.com",
    "streamSourceBakVips": [
      "*************:1936",
      "*************:1935"
    ],
    "streamSourceBitrate": 2441,
    "streamSourceCodec": "h265",
    "streamSourceFps": 22,
    "streamSourceHeight": 1920,
    "streamSourceType": "stream",
    "streamSourceWidth": 1088,
    "streamTimestampOffset": 0.0,
    "streams": [
      {
        "destBakVips": [
          "*************:1935"
        ],
        "rtmpDest": "rtmp://*************/stage/stream-11729250232951234_ld?vhost=pull-l3.test.com&live_rtmp_test=on&ali_inter_auth_time=682d3a64&transcode=no&transcoded=true&ali_center_tran_pub=on",
        "vfilter": "no_ph_vf[0:v]blendimages=json={}[vfo]",
        "input_bypass": "-threads 3",
        "fps": "1:30",
        "vprofile": "high",
        "env": "pub",
        "type": "transcode",
        "ffver": "ffv5",
        "sw2main": 3,
        "width": -2,
        "output_bypass": "-cqfactor 25 -rule adapt_res_480*852#4d",
        "x264_params": "hierarchical-b=1:aq-strength=0.8:sync-lookahead=2:force-cfr=1:lookahead-threads=1:rc-lookahead=5:bframes=3:threads=3:keyint=infinite:scenecut=0:psy=0:trellis=0:me=dia:merange=16:subme=4:vbv-maxrate=1000:vbv-bufsize=1500:bitrate=1000:ref=2:partitions=i8x8,i4x4:mb-lowres=2:aq-mode=2:opt-lowres-intra=1:bi-skip=20:mbtree-motion-lag=1:bskip-ref=1:b-adapt=0 -vcopybr 200:1:1000 -copyts -force_key_frames source -ignore_subsei_ex 1 -qsd_flush -sei_hold_all -force_avsync -sei_order_by_dts -dec_force_header_on_key -colorinfo_bypass all",
        "aCodec": "copy",
        "height": 852,
        "vCodec": "h264"
      }
    ]
  },
  "publishNode": "cn8413",
  "pushDomain": "push-rtmp-l3.test.com",
  "quota": 300,
  "streamName": "stream-11729250232951234",
  "switchWorker": false,
  "templateName": "test-ld-1000-ffv5",
  "token": "ld"
}`
	stopTaskReq = `{
  "failOnError":false,
  "forceRestart":false,
  "ignoreReleaseFail":false,
  "isEdge":"",
  "schedulerForceRestart":false,
  "soft":false,
  "taskId":"8b4d3983-bead-30a1-b07c-971761220373"
}`
	startRequestBodyWithSource = `{
  "appName": "stage",
  "centerPublicAddr": "*************",
  "domain": "pull-l3-source.test.com",
  "gslbAuthTime": "682d3a64",
  "isCdnEdge": "false",
  "isEdge": "false",
  "params": {
    "streamNum": 1,
    "streamSource": "rtmp://*************:1935/stage/pull-l3.douyincdn.com_stream-11729250232951234?ali_mapstream_ignore=on&ali_ols_play=on&ali_ols_play_type=transcoding&ali_rtmp_quick_start=off&ali_rtmp_retain=0&vhost=pull-l3.test.com",
    "streamSourceBakVips": [
      "*************:1936",
      "*************:1935"
    ],
    "streamSourceBitrate": 2441,
    "streamSourceCodec": "h265",
    "streamSourceFps": 22,
    "streamSourceHeight": 1920,
    "streamSourceType": "stream",
    "streamSourceWidth": 1088,
    "streamTimestampOffset": 0.0,
    "streams": [
      {
        "destBakVips": [
          "*************:1935"
        ],
        "rtmpDest": "rtmp://*************/stage/stream-11729250232951234_ld?vhost=pull-l3.test.com&live_rtmp_test=on&ali_inter_auth_time=682d3a64&transcode=no&transcoded=true&ali_center_tran_pub=on",
        "vfilter": "no_ph_vf[0:v]blendimages=json={}[vfo]",
        "input_bypass": "-threads 3",
        "fps": "1:30",
        "vprofile": "high",
        "env": "pub",
        "type": "transcode",
        "ffver": "ffv5",
        "sw2main": 3,
        "width": -2,
        "output_bypass": "-cqfactor 25 -rule adapt_res_480*852#4d",
        "x264_params": "hierarchical-b=1:aq-strength=0.8:sync-lookahead=2:force-cfr=1:lookahead-threads=1:rc-lookahead=5:bframes=3:threads=3:keyint=infinite:scenecut=0:psy=0:trellis=0:me=dia:merange=16:subme=4:vbv-maxrate=1000:vbv-bufsize=1500:bitrate=1000:ref=2:partitions=i8x8,i4x4:mb-lowres=2:aq-mode=2:opt-lowres-intra=1:bi-skip=20:mbtree-motion-lag=1:bskip-ref=1:b-adapt=0 -vcopybr 200:1:1000 -copyts -force_key_frames source -ignore_subsei_ex 1 -qsd_flush -sei_hold_all -force_avsync -sei_order_by_dts -dec_force_header_on_key -colorinfo_bypass all",
        "aCodec": "copy",
        "height": 852,
        "vCodec": "h264"
      }
    ]
  },
  "publishNode": "cn8413",
  "pushDomain": "push-rtmp-l3.test.com",
  "quota": 300,
  "streamName": "stream-11729250232951234",
  "switchWorker": false,
  "forceRestart": "true",
  "templateName": "test-ld-1000-ffv5",
  "token": "ld"
}`
)

func getRequestBody(r *http.Request) []byte {
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return nil
	}
	return body
}

// 正常启动任务流程，没有切流和兜底配置，并启动成功
func TestStartTaskWithoutSwitchStreamsSuccess(t *testing.T) {
	req, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(startRequestBody))
	req.Header.Add("User-Agent", "my-app/0.0.1")
	defer Mock((*common.SwitchService).SelectScheduler).Return("l2", &common.BackupConfig{CanHoldByPubTag: true, Order: "l22,public,center"}).Build().UnPatch()
	defer Mock((*TaskService).StartTask).Return(&pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}, nil).Build().UnPatch()

	defer Mock((*TaskService).HoldFailedAndRetry).Return(&pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}, nil).Build().UnPatch()
	defer Mock((*ha.HAService).DoHAStartBefore).Return().Build().UnPatch()
	defer Mock((*ha.HAService).DoHAStartAfter).Return().Build().UnPatch()
	defer Mock((*tasker.CommonTaskBiz).AddSourceStreamTask).Return(nil).Build().UnPatch()
	taskService.globalParamLoader = GetGlobalConfig(t)
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	taskService.schedulerSwitcher = livetranscode.NewSchedulerSwitchOperator(log.GetLogger(), switchOperate)
	_, err := taskService.LiveTranscodeStartTask(nil, req, getRequestBody(req))
	assert.Nil(t, err)
}

// 测试限流
func TestLiveTransCodeRateLimiter(t *testing.T) {
	defer Mock((*TaskService).AnimusLiveTranscodeStartTask).Return(&pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}, nil).Build().UnPatch()
	defer Mock((*tasker.CommonTaskBiz).AddSourceStreamTask).Return(nil).Build().UnPatch()
	taskService.globalParamLoader = GetGlobalConfig(t)
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	//defer Mock((*global_param_loader.GlobalParamLoader).GetRateLimiter).Return(rate.NewLimiter(rate.Limit(1), 1)).Build().UnPatch()
	errs := make([]error, 0)
	var wg sync.WaitGroup
	for i := 0; i < 12; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			req, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(startRequestBody))
			req.Header.Add("User-Agent", "my-app/0.0.1")
			_, err := taskService.LiveTranscodeStartTask(nil, req, getRequestBody(req))
			if err != nil {
				errs = append(errs, err)
			}
		}()
	}
	wg.Wait()
	assert.NotEqual(t, 0, len(errs))
	assert.Contains(t, errs[0].Error(), "throttled by qps")
}

// 正常启动任务流程，没有切流和兜底配置，启动失败
func TestStartTaskWithoutSwitchStreamsFailed(t *testing.T) {
	req, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(startRequestBody))
	req.Header.Add("User-Agent", "my-app/0.0.1")
	defer Mock((*common.SwitchService).SelectScheduler).Return("", nil).Build().UnPatch()

	defer Mock((*TaskService).StartTask).Return(&pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}, fmt.Errorf("test error")).Build().UnPatch()

	defer Mock((*TaskService).HoldFailedAndRetry).Return(&pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}, nil).Build().UnPatch()
	defer Mock((*tasker.CommonTaskBiz).AddSourceStreamTask).Return(nil).Build().UnPatch()
	defer Mock((*ha.HAService).DoHAStartBefore).Return().Build().UnPatch()
	defer Mock((*ha.HAService).DoHAStartAfter).Return().Build().UnPatch()
	taskService.globalParamLoader = GetGlobalConfig(t)
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	taskService.schedulerSwitcher = livetranscode.NewSchedulerSwitchOperator(log.GetLogger(), switchOperate)
	_, err := taskService.LiveTranscodeStartTask(nil, req, getRequestBody(req))
	assert.Equal(t, InternalErrorCode, errors.FromError(err).Reason)
}

// 正常启动任务流程，没有切流和兜底配置，源流降级兜底
func TestStartTaskWithoutSwitchStreamsFailedAndBackUp(t *testing.T) {
	req, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(startRequestBodyWithSource))
	req.Header.Add("User-Agent", "my-app/0.0.1")
	defer Mock((*common.SwitchService).SelectScheduler).Return("", nil).Build().UnPatch()

	defer Mock((*TaskService).StartTask).Return(&pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}, fmt.Errorf("test error")).Build().UnPatch()

	defer Mock((*TaskService).HoldFailedAndRetry).Return(&pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}, fmt.Errorf("test error")).Build().UnPatch()
	defer Mock((*tasker.CommonTaskBiz).AddSourceStreamTask).Return(nil).Build().UnPatch()
	defer Mock((*ha.HAService).DoHAStartBefore).Return().Build().UnPatch()
	defer Mock((*ha.HAService).DoHAStartAfter).Return().Build().UnPatch()
	defer Mock((*tasker.CommonTaskBiz).TranscodeTaskBackupToSourceStream).Return(nil).Build().UnPatch()
	taskService.globalParamLoader = GetGlobalConfig(t)
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	taskService.schedulerSwitcher = livetranscode.NewSchedulerSwitchOperator(log.GetLogger(), switchOperate)
	_, err := taskService.LiveTranscodeStartTask(nil, req, getRequestBody(req))
	assert.Nil(t, err)
}

// 启动任务走切流逻辑，走l2,兜底顺序l22,public,center   这个用例用于手动观察switchStream的参数替换是否正确
func TestStartTaskWithSwitchStreamsL2(t *testing.T) {
	req, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(startRequestBody))
	req.Header.Add("User-Agent", "my-app/0.0.1")
	defer Mock((*common.SwitchService).SelectScheduler).Return("l2", &common.BackupConfig{CanHoldByPubTag: false, Order: "l22,public,center"}).Build().UnPatch()

	defer Mock((*TaskService).StartTask).Return(&pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}, fmt.Errorf("test error")).Build().UnPatch()
	defer Mock((*tasker.CommonTaskBiz).AddSourceStreamTask).Return(nil).Build().UnPatch()
	defer Mock((*ha.HAService).DoHAStartBefore).Return().Build().UnPatch()
	defer Mock((*ha.HAService).DoHAStartAfter).Return().Build().UnPatch()
	taskService.globalParamLoader = GetGlobalConfig(t)
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	taskService.schedulerSwitcher = livetranscode.NewSchedulerSwitchOperator(log.GetLogger(), switchOperate)
	_, err := taskService.LiveTranscodeStartTask(nil, req, getRequestBody(req))
	assert.Equal(t, InternalErrorCode, errors.FromError(err).Reason)
}

// HoldFailedAndRetry 测试，这个方法用于自动测试兜底切流期间，task最终参数的正确性
func TestHoldFailedAndRetry(t *testing.T) {
	task := getTask(t)
	switchStreams := make([]map[string]interface{}, 0)
	switchStreams = append(switchStreams, map[string]interface{}{
		"RtmpDest":            "rtmp://********/ali-mmyzpush.v.momocdn.com/momo/m_164134851548389fa5a51930e8b9f183_hevc2avc",
		"StreamSource":        "rtmp://********/ali-mmyzpush.v.momocdn.com/momo/m_164134851548389fa5a51930e8b9f183_hevc2avc",
		"Tag":                 "livetranscode_test_pub",
		"Env":                 "pub",
		"streamSourceBakVips": &[]string{"********"},
		"TaskSchedulerTag":    "l2",
	})

	defer Mock((*TaskService).StartTask).Return(&pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}, nil).Build().UnPatch()
	defer Mock((*ha.HAService).DoHAStartBefore).Return().Build().UnPatch()
	defer Mock((*ha.HAService).DoHAStartAfter).Return().Build().UnPatch()
	_, err := taskService.HoldFailedAndRetry(context.Background(), task, switchStreams, "")
	assert.Nil(t, err)
	assert.Equal(t, GetTranscodeParam(task.EngineParams, "streamSource"), "rtmp://********/ali-mmyzpush.v.momocdn.com/momo/m_164134851548389fa5a51930e8b9f183_hevc2avc")
	assert.Equal(t, GetStream(task.EngineParams)["rtmpDest"].(string), "rtmp://********/ali-mmyzpush.v.momocdn.com/momo/m_164134851548389fa5a51930e8b9f183_hevc2avc")
	assert.Equal(t, task.Tag, "livetranscode_test_pub")
}

func GetStream(param string) map[string]interface{} {
	paramMap := biz.StringToMap(param)
	if paramMap == nil {
		log.Errorf("get transcode stream fail,param:%s", param)
		return nil
	}
	streams := paramMap["streams"].([]interface{})
	if stream, ok := streams[0].(map[string]interface{}); ok {
		return stream
	}
	log.Errorf("get transcode stream fail,streams:%s", streams)
	return nil
}

func GetTranscodeParam(param, key string) interface{} {
	paramMap := biz.StringToMap(param)
	if paramMap == nil {
		log.Errorf("get transcode stream fail,param:%s", param)
		return ""
	}
	if _, ok := paramMap[key]; !ok {
		log.Errorf("get transcode stream fail,key:%s", key)
		return ""
	}
	return paramMap[key]
}

func TestLiveTransCodeStartTaskUseSwitchStreams(t *testing.T) {
	req, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(startRequestBody))
	req.Header.Add("User-Agent", "my-app/0.0.1")
	defer Mock((*lcUtils.SchedulerSwitchOperator).SwitchScheduler).Return([]map[string]interface{}{
		{"center": map[string]interface{}{
			"RtmpDest":            "rtmp://*******/ali-mmyzpush.v.momocdn.com/momo/m_164134851548389fa5a51930e8b9f183_hevc2avc?live_rtmp_test=on&ali_inter_auth_time=61d4fec8&transcode=no&transcoded=true&edge_transcode=yes",
			"StreamSource":        "",
			"class":               "node",
			"Env":                 "cdnEdge",
			"streamSourceBakVips": "test",
		}},
	}).Build().Patch()
	defer Mock((*TaskService).StartTask).Return(&pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}, fmt.Errorf("test")).Build().UnPatch()
	defer Mock((*TaskService).HoldFailedAndRetry).Return(&pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}, nil).Build().UnPatch()
	defer Mock((*tasker.CommonTaskBiz).AddSourceStreamTask).Return(nil).Build().UnPatch()
	defer Mock((*ha.HAService).DoHAStartBefore).Return().Build().UnPatch()
	defer Mock((*ha.HAService).DoHAStartAfter).Return().Build().UnPatch()
	taskService.globalParamLoader = GetGlobalConfig(t)
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	taskService.schedulerSwitcher = livetranscode.NewSchedulerSwitchOperator(log.GetLogger(), switchOperate)
	_, err := taskService.LiveTranscodeStartTask(nil, req, getRequestBody(req))
	assert.Nil(t, err)
}

func TestLiveTransCodeStopTask(t *testing.T) {
	req, _ := http.NewRequest("POST", "http://example.com/stop?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(stopTaskReq))
	defer Mock((*TaskService).StopTask).Return(&pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}, nil).Build().UnPatch()
	defer Mock((*ha.HAService).DoStopBefore).Return().Build().UnPatch()
	defer Mock((*ha.HAService).DoStopAfter).Return().Build().UnPatch()
	taskService.globalParamLoader = GetGlobalConfig(t)
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	taskService.schedulerSwitcher = livetranscode.NewSchedulerSwitchOperator(log.GetLogger(), switchOperate)
	_, err := taskService.LiveTranscodeStopTask(nil, req, getRequestBody(req))
	assert.Nil(t, err)
}

func TestSendCommandTask(t *testing.T) {
	req, _ := http.NewRequest("POST", "http://example.com/sendCommand?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(sendCommandRequestBody))
	taskService.globalParamLoader = GetGlobalConfig(t)
	defer Mock((*tasker.CommonTaskBiz).CommandTask).Return(nil).Build().UnPatch()
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	_, err := taskService.LiveTranscodeSendCommonCommand(nil, req)
	assert.Nil(t, err)
}

func TestLiveTransCodeTaskUpdate(t *testing.T) {
	req, _ := http.NewRequest("POST", "http://example.com/task/update?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(updateTaskRequestBody))
	taskService.globalParamLoader = GetGlobalConfig(t)
	defer Mock((*tasker.CommonTaskBiz).UpdateTask).Return(nil).Build().UnPatch()
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	_, err := taskService.LiveTranscodeUpdateTask(nil, req)
	assert.Nil(t, err)
}

func TestTaskService_LiveTranscodeEditTask(t *testing.T) {
	req, _ := http.NewRequest("POST", "http://example.com/pic/edit?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(editPicTaskReq))
	taskService.globalParamLoader = GetGlobalConfig(t)
	defer Mock((*tasker.CommonTaskBiz).UpdateTaskAttachments).Return(nil).Build().UnPatch()
	defer Mock((*tasker.CommonTaskBiz).GetTransCodeAttachments).Return([]*domain.TranscodeAttachment{
		{
			ID:         371094709174901274,
			Name:       "test.jpg",
			Status:     1,
			Params:     "a:b",
			AttachType: lc.AttachmentTypePIC,
		},
		{
			ID:         112938948,
			Name:       "WATERMARK_V_1",
			Status:     1,
			Params:     `{"animate":0,"name":"WATERMARK_V_1","normalizedRefMode":0,"normalizedh":0.06666666666666667,"offsetcorner":3,"pictureurl":"https://xxxx-dfl-watermark.oss.aliyuncs.com/logo/21313444/kanLIVE1.png","transparency":265,"visible":1,"xoffset":0.05925925925925977,"yoffset":0.06944444444444445}`,
			AttachType: lc.AttachmentTypePIC,
		},
	}).Build().UnPatch()
	defer Mock((*tasker.CommonTaskBiz).CommandTask).Return(nil).Build().UnPatch()
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	_, err := taskService.LiveTranscodeEditPic(nil, req)
	assert.Nil(t, err)
}

func TestTaskService_LiveTranscodeQueryStat(t *testing.T) {
	req, _ := http.NewRequest("POST", "http://example.com/stat/query?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(statQueryReq))
	taskService.globalParamLoader = GetGlobalConfig(t)
	defer Mock((*tasker.CommonTaskBiz).QueryStat).Return(nil, nil).Build().UnPatch()
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	_, err := taskService.LiveTranscodeQueryStat(nil, req)
	assert.Nil(t, err)
}

func TestTaskService_LiveTranscodeGetTasks(t *testing.T) {
	req, _ := http.NewRequest("POST", "http://example.com/task/getTasks?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", nil)
	taskService.globalParamLoader = GetGlobalConfig(t)
	defer Mock((*tasker.CommonTaskBiz).GetRunningTaskIds).Return([]*models.Task{
		{
			TaskId: "00000001-3ccc-4128-85e5-4a172c1a5170",
			Param: &models.TaskEngineParam{
				Param:    "{\"adaptiveHDROutput\":\"\",\"allAudioCopy\":0,\"appName\":\"jingkuan\",\"backupParams\":null,\"bizNotifyAddr\":\"\",\"bizReportMsgAddr\":\"\",\"centerPublicAddr\":\"*************\",\"domain\":\"pulltest-sh-pre.alivecdn.com\",\"imsExtra\":\"\",\"pushDomain\":\"ols_center_push\",\"quota\":300,\"rateCallbackParams\":null,\"streamInterSourceBakAddrs\":null,\"streamL2SourceBakAddrs\":[\"*************:1936\"],\"streamName\":\"test062001\",\"streamNum\":1,\"streamSource\":\"rtmp://************:1935/jingkuan/pulltest-sh-pre.alivecdn.com_test062001?ali_mapstream_ignore=on\\u0026ali_ols_play=on\\u0026ali_ols_play_type=transcoding\\u0026ali_rtmp_quick_start=on\\u0026ali_rtmp_retain=0\\u0026vhost=pulltest-sh-pre.alivecdn.com\",\"streamSourceBakVips\":null,\"streamSourceBitrate\":5526,\"streamSourceCodec\":\"h264\",\"streamSourceFps\":25,\"streamSourceHeight\":null,\"streamSourceType\":\"stream\",\"streamSourceVolatile\":false,\"streamSourceWidth\":null,\"streamTimestampOffset\":0,\"streams\":[{\"aCodec\":\"aac\",\"abr\":\"128\",\"achNum\":\"2\",\"animus\":\"true\",\"aprofile\":\"aac_low\",\"ar\":\"44100\",\"destBakVips\":[\"*************:1935\"],\"env\":\"pub\",\"fps\":\"25\",\"height\":648,\"rtmpDest\":\"rtmp://**********/jingkuan/test062001_hd?vhost=pulltest-sh-pre.alivecdn.com\\u0026live_rtmp_test=on\\u0026ali_center_tran_pub=on\\u0026ali_inter_auth_time=6858f0b6\\u0026transcode=no\\u0026transcoded=true\",\"type\":\"transcode\",\"vCodec\":\"h264\",\"vprofile\":\"high\",\"width\":-1,\"x264_params\":\"aq-strength=0.8:sync-lookahead=2:force-cfr=1:lookahead-threads=1:rc-lookahead=4:bframes=3:threads=4:keyint=125:scenecut=0:psy=0:trellis=0:me=dia:merange=16:subme=4:vbv-maxrate=1320:vbv-bufsize=2200:crf=25:tb-abrmax=1100\"}],\"switchStreams\":null,\"token\":\"hd\"}",
				ParamUrl: "",
			},
		},
	}, nil).Build().UnPatch()
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	result, err := taskService.LiveTranscodeGetTasks(nil, req)
	assert.Nil(t, err)
	task := result.(map[string]interface{})["data"].(map[string]interface{})["tasks"].([]*lc.LiveTransCodeTask)
	//t.Logf("task: %+v", task)
	assert.Equal(t, "hd", task[0].TemplateName)
}

func TestTaskService_LiveTranscodeListTag(t *testing.T) {
	req, _ := http.NewRequest("POST", "http://example.com/tag/list?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(`{"ding_user":"xx"}`))
	taskService.globalParamLoader = GetGlobalConfig(t)
	//taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	_, err := taskService.LiveTranscodeListTag(nil, req)
	assert.Nil(t, err)

}

func TestTaskService_LiveTranscodeTaskStatistics(t *testing.T) {
	var statisticsRequestBody = `{"tag":"livetranscode_test_pub","domain":"ali-mmyzpush.v.momocdn.com","app":"momo"}`
	req, _ := http.NewRequest("POST", "http://example.com/task/statistics?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(statisticsRequestBody))
	taskService.globalParamLoader = GetGlobalConfig(t)
	defer Mock((*pb.LiveBizHTTPClientImpl).GetStatInfo).Return(&pb.GetStatInfoResponse{
		Code: "200",
		Data: &pb.Data{
			Tasks: []*pb.Tasks{
				{
					Domain:     "ali-mmyzpush.v.momocdn.com",
					App:        "momo",
					Quota:      100,
					CurStreams: 100,
					Percentage: 100,
					Template:   "livetranscode_test_pub",
				},
			},
		},
	}, nil).Build().UnPatch()
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	_, err := taskService.LiveTranscodeTaskStatistics(nil, req)
	assert.Nil(t, err)
	//t.Logf("result:%v", result)
}

func TestTaskService_LiveTranscodeMigrateTask(t *testing.T) {
	req, _ := http.NewRequest("POST", "http://example.com/migrate?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(migrateTaskReq))
	defer Mock((*TaskService).StopTask).Return(&pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}, nil).Build().UnPatch()
	defer Mock((*tasker.CommonTaskBiz).GetTask).Return(
		&models.Task{
			TaskId: "00000001-3ccc-4128-85e5-4a172c1a5170",
			Worker: &models.Worker{
				Ip: "127.0.0.1",
			},
			Param: &models.TaskEngineParam{
				Param: "a:b",
			},
		}, nil,
	).Build().UnPatch()
	defer Mock((*tasker.CommonTaskBiz).StartTask).Return(nil).Build().UnPatch()
	taskService.globalParamLoader = GetGlobalConfig(t)
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	_, err := taskService.LiveTranscodeMigrateTask(nil, req)
	assert.Nil(t, err)
}

func TestLiveTranscodeMigrateTaskWithoutTaskId(t *testing.T) {
	req, _ := http.NewRequest("POST", "http://example.com/migrate?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(migrateTaskReq))
	defer Mock((*TaskService).StopTask).Return(&pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}, nil).Build().UnPatch()
	defer Mock((*tasker.CommonTaskBiz).GetTask).Return(
		&models.Task{
			TaskId: "00000001-3ccc-4128-85e5-4a172c1a5170",
			Worker: &models.Worker{
				Ip: "127.0.0.1",
			},
			Param: &models.TaskEngineParam{
				Param: "a:b",
			},
		}, nil,
	).Build().UnPatch()
	defer Mock((*tasker.CommonTaskBiz).StartTask).Return(nil).Build().UnPatch()
	taskService.globalParamLoader = GetGlobalConfig(t)
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	response, err := taskService.LiveTranscodeMigrateTask(nil, req)
	assert.Nil(t, err)
	// 验证golang的uuid生成结果与java是否一致
	assert.Equal(t, response.(map[string]interface{})["retCode"], 0)
}

func GetGlobalConfig(t *testing.T) *global_param_loader.GlobalParamLoader {
	repo := mocks.NewMockKVStoreRepo(gomock.NewController(t))
	repo.EXPECT().ListLike(gomock.Any(), global_param_loader.KEY_REPO_FLOWCONTROL, gomock.Any()).Return([]*domain.KVStore{
		{
			Key:   "task.LiveTranscodeStartTask",
			Value: "10",
		},
	}, nil).AnyTimes()
	repo.EXPECT().List(gomock.Any(), global_param_loader.KEY_REPO_PARAMS, global_param_loader.KeyNameIgnoreReleaseFail).Return([]*domain.KVStore{
		{
			Value: "true",
		},
	}, nil).AnyTimes()
	repo.EXPECT().List(gomock.Any(), global_param_loader.KEY_REPO_PARAMS, global_param_loader.KeyNameParseTagForSyncData).Return([]*domain.KVStore{
		{
			Value: "true",
		},
	}, nil).AnyTimes()
	repo.EXPECT().List(gomock.Any(), global_param_loader.KEY_REPO_QUOTA, gomock.Any()).Return([]*domain.KVStore{
		{
			Key:   "cpu",
			Value: "100",
		},
	}, nil).AnyTimes()
	repo.EXPECT().ListLike(gomock.Any(), global_param_loader.KEY_REPO_QUOTA, gomock.Any()).Return([]*domain.KVStore{
		{
			Key:   "cpu",
			Value: "100",
		},
	}, nil).AnyTimes()
	repo.EXPECT().List(gomock.Any(), global_param_loader.KEY_REPO_PARAMS, global_param_loader.KeyNameStillRunningForceRestart).Return([]*domain.KVStore{
		{
			Value: "true",
		},
	}, nil).AnyTimes()
	repo.EXPECT().List(gomock.Any(), global_param_loader.KEY_REPO_PARAMS, global_param_loader.KeyNameWorkerDomain).Return([]*domain.KVStore{}, nil).AnyTimes()
	repo.EXPECT().List(gomock.Any(), global_param_loader.KEY_REPO_PARAMS, global_param_loader.KeyNameInUseSubnetConfig).Return([]*domain.KVStore{}, nil).AnyTimes()
	repo.EXPECT().List(gomock.Any(), global_param_loader.KEY_REPO_PARAMS, global_param_loader.KeyNameBackupDomains).Return([]*domain.KVStore{
		{
			Value: `["pull-l3-source.test.com"]`,
		},
	}, nil).AnyTimes()
	g := global_param_loader.NewGlobalParamLoader(log.GetLogger(), repo, &conf.Bootstrap{
		Cluster: &conf.Cluster{
			Region: "cn-shanghai-pre",
		},
		LiveTransCodeConfig: &conf.LiveTransCodeConfig{
			Unit: "pre",
			Env:  "pub",
		},
	})
	time.Sleep(1 * time.Millisecond)
	return g
}

func getTask(t *testing.T) *pb.StartTaskRequest {
	defer Mock((*tasker.CommonTaskBiz).AddSourceStreamTask).Return(nil).Build().UnPatch()
	taskService.globalParamLoader = GetGlobalConfig(t)
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	taskService.schedulerSwitcher = livetranscode.NewSchedulerSwitchOperator(log.GetLogger(), switchOperate)
	r, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(startRequestBody))
	// 参数修正
	var streamParams lc.StreamParams
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&streamParams)
	if err != nil {
		log.Errorf("LiveTranscodeStartTask decode request error, err:%v", err)
		return nil
	}
	//参数修正
	taskId := lcUtils.GenerateTaskId(streamParams.Domain, streamParams.AppName, streamParams.StreamName, streamParams.Token)
	// 开启影子链路
	query := r.URL.Query()
	if r.Header.Get(header.HeaderShadow) != "" {
		query.Set("jobId", header.HeaderShadow+uuid.NewString())
	}
	taskService.liveTransCodeParamsAdapter.CorrectLiveTransCodeStartParams(&streamParams)
	//TODO 高可用能力
	//ha.DoBefore(taskId)
	ctx, startTaskRequest, _, err := taskService.liveTransCodeParamsAdapter.AssembleStartTaskRequest(r.Context(), taskId, streamParams)
	if err != nil {
		log.Errorf("LiveTranscodeStartTask assembleStartTaskRequest error, err:%v", err)
		return nil
	}
	err = taskService.streamTaskBiz.AddSourceStreamTask(ctx, streamParams, taskId, lcUtils.GenerateStreamId(streamParams.Domain, streamParams.AppName, streamParams.StreamName))

	return startTaskRequest
}

func TestWrapErrorWithReasonAndMessage(t *testing.T) {
	e2 := errors.New(500, "reason", "message")
	assert.Equal(t, e2.Reason, "reason")
	assert.Equal(t, e2.Message, "message")
}

// TestQueryTasksWithSourceStream_Success 测试查询带源流任务成功的情况
func TestQueryTasksWithSourceStream_Success(t *testing.T) {
	// 准备测试数据
	requestBody := `{
		"domain": "test.com",
		"app": "live",
		"stream": "test-stream"
	}`

	req, _ := http.NewRequest("POST", "http://example.com/queryTasksWithSourceStream", strings.NewReader(requestBody))

	// 设置全局配置
	taskService.globalParamLoader = GetGlobalConfig(t)

	// Mock ListBySourceStreamId方法
	sourceStreamTasks := []*domain.SourceStreamTask{
		{
			ID:             1,
			Domain:         "test.com",
			App:            "live",
			Stream:         "test-stream",
			SourceStreamId: "test-stream-id",
			TaskId:         "task-1",
		},
		{
			ID:             2,
			Domain:         "test.com",
			App:            "live",
			Stream:         "test-stream",
			SourceStreamId: "test-stream-id",
			TaskId:         "task-2",
		},
	}

	defer Mock((*tasker.CommonTaskBiz).ListBySourceStreamId).Return(sourceStreamTasks, nil).Build().UnPatch()

	// Mock GetTask方法
	task1 := &models.Task{
		TaskId: "task-1",
		Worker: &models.Worker{
			Ip: "*************",
		},
		BizStatus: models.TaskStateActive,
	}
	task2 := &models.Task{
		TaskId: "task-2",
		Worker: &models.Worker{
			Ip: "*************",
		},
		BizStatus: models.TaskStateActive,
	}
	taskList := []*models.Task{task1, task2}
	defer Mock((*tasker.CommonTaskBiz).GetRunningTasks).Return(taskList, nil).Build().UnPatch()

	// 执行测试
	result, err := taskService.QueryTasksWithSourceStream(nil, req)

	// 验证结果
	assert.Nil(t, err)
	assert.NotNil(t, result)

	// 验证返回的数据结构
	resultMap, ok := result.(map[string]interface{})
	assert.True(t, ok)

	datas, ok := resultMap["data"].(map[string]interface{})
	assert.True(t, ok)
	tasks, ok := datas["tasks"].([]*lc.TranscodeTask)
	assert.True(t, ok)
	assert.Len(t, tasks, 2)

	// 验证第一个任务
	assert.Equal(t, "task-1", tasks[0].TaskId)
	assert.Equal(t, "test-stream", tasks[0].DestStream)
	assert.Equal(t, "*************", tasks[0].WorkerIp)

	// 验证第二个任务
	assert.Equal(t, "task-2", tasks[1].TaskId)
	assert.Equal(t, "pub", tasks[1].Env)
	assert.Equal(t, "test-stream", tasks[1].DestStream)
	assert.Equal(t, "*************", tasks[1].WorkerIp)
}

// TestQueryTasksWithSourceStream_EmptyResult 测试查询带源流任务返回空结果的情况
func TestQueryTasksWithSourceStream_EmptyResult(t *testing.T) {
	// 准备测试数据
	requestBody := `{
		"domain": "test.com",
		"app": "live",
		"stream": "non-existent-stream"
	}`

	req, _ := http.NewRequest("POST", "http://example.com/queryTasksWithSourceStream", strings.NewReader(requestBody))

	// 设置全局配置
	taskService.globalParamLoader = GetGlobalConfig(t)

	// Mock ListBySourceStreamId方法返回空结果
	defer Mock((*tasker.CommonTaskBiz).ListBySourceStreamId).Return([]*domain.SourceStreamTask{}, nil).Build().UnPatch()

	// 执行测试
	result, err := taskService.QueryTasksWithSourceStream(nil, req)

	// 验证结果
	assert.Nil(t, err)
	assert.NotNil(t, result)

	// 验证返回的数据结构
	resultMap, ok := result.(map[string]interface{})
	assert.True(t, ok)

	datas, ok := resultMap["data"].(map[string]interface{})
	assert.True(t, ok)
	tasks, ok := datas["tasks"].([]*lc.TranscodeTask)
	assert.True(t, ok)
	assert.Len(t, tasks, 0)
}

// TestQueryTasksWithSourceStream_InvalidParams 测试查询带源流任务参数无效的情况
func TestQueryTasksWithSourceStream_InvalidParams(t *testing.T) {
	// 准备测试数据 - 缺少domain参数
	requestBody := `{
		"app": "live",
		"stream": "test-stream"
	}`

	req, _ := http.NewRequest("POST", "http://example.com/queryTasksWithSourceStream", strings.NewReader(requestBody))

	// 设置全局配置
	taskService.globalParamLoader = GetGlobalConfig(t)

	// 执行测试
	result, err := taskService.QueryTasksWithSourceStream(nil, req)

	// 验证结果
	assert.NotNil(t, err)
	assert.Nil(t, result)

	// 验证错误类型
	kratosErr, ok := err.(*errors.Error)
	assert.True(t, ok)
	assert.Equal(t, InvalidParameterNullValue, kratosErr.Reason)
}

// TestQueryTasksWithSourceStream_ListBySourceStreamIdError 测试查询源流任务失败的情况
func TestQueryTasksWithSourceStream_ListBySourceStreamIdError(t *testing.T) {
	// 准备测试数据
	requestBody := `{
		"domain": "test.com",
		"app": "live",
		"stream": "test-stream"
	}`

	req, _ := http.NewRequest("POST", "http://example.com/queryTasksWithSourceStream", strings.NewReader(requestBody))

	// 设置全局配置
	taskService.globalParamLoader = GetGlobalConfig(t)

	// Mock ListBySourceStreamId方法返回错误
	defer Mock((*tasker.CommonTaskBiz).ListBySourceStreamId).Return(nil, fmt.Errorf("database error")).Build().UnPatch()

	// 执行测试
	result, err := taskService.QueryTasksWithSourceStream(nil, req)

	// 验证结果
	assert.NotNil(t, err)
	assert.Nil(t, result)

	// 验证错误类型
	kratosErr, ok := err.(*errors.Error)
	assert.True(t, ok)
	assert.Equal(t, InternalErrorCode, kratosErr.Reason)
}

// TestQueryTasksWithSourceStream_GetTaskError 测试获取任务失败的情况
func TestQueryTasksWithSourceStream_GetTaskError(t *testing.T) {
	// 准备测试数据
	requestBody := `{
		"domain": "test.com",
		"app": "live",
		"stream": "test-stream"
	}`

	req, _ := http.NewRequest("POST", "http://example.com/queryTasksWithSourceStream", strings.NewReader(requestBody))

	// 设置全局配置
	taskService.globalParamLoader = GetGlobalConfig(t)

	// Mock ListBySourceStreamId方法
	sourceStreamTasks := []*domain.SourceStreamTask{
		{
			ID:             1,
			Domain:         "test.com",
			App:            "live",
			Stream:         "test-stream",
			SourceStreamId: "test-stream-id",
			TaskId:         "task-1",
		},
	}

	defer Mock((*tasker.CommonTaskBiz).ListBySourceStreamId).Return(sourceStreamTasks, nil).Build().UnPatch()

	// Mock GetTask方法返回错误
	defer Mock((*tasker.CommonTaskBiz).GetRunningTasks).Return(nil, fmt.Errorf("task not found")).Build().UnPatch()

	// 执行测试
	result, err := taskService.QueryTasksWithSourceStream(nil, req)

	// 验证结果
	assert.NotNil(t, err)
	assert.Nil(t, result)

	// 验证错误类型
	kratosErr, ok := err.(*errors.Error)
	assert.True(t, ok)
	assert.Equal(t, InternalErrorCode, kratosErr.Reason)
}

// TestQueryTasksWithSourceStream_Throttled 测试限流的情况
func TestQueryTasksWithSourceStream_Throttled(t *testing.T) {
	// 准备测试数据
	requestBody := `{
		"domain": "test.com",
		"app": "live",
		"stream": "test-stream"
	}`

	req, _ := http.NewRequest("POST", "http://example.com/queryTasksWithSourceStream", strings.NewReader(requestBody))

	// 设置全局配置
	taskService.globalParamLoader = GetGlobalConfig(t)

	// Mock限流器返回false（被限流）
	defer Mock((*global_param_loader.GlobalParamLoader).GetRateLimiter).Return(rate.NewLimiter(rate.Limit(0), 0)).Build().UnPatch()

	// 执行测试
	result, err := taskService.QueryTasksWithSourceStream(nil, req)

	// 验证结果
	assert.NotNil(t, err)
	assert.Nil(t, result)

	// 验证错误类型
	kratosErr, ok := err.(*errors.Error)
	assert.True(t, ok)
	assert.Equal(t, ThrottledErrorCode, kratosErr.Reason)
}

// ... existing code ...

// TestTaskService_LiveTranscodeGetSyncData_Success_New 测试LiveTranscodeGetSyncData成功场景
//func TestTaskService_LiveTranscodeGetSyncData_Success_New(t *testing.T) {
//	// 设置测试数据
//	tasks := []*models.Task{
//		{
//			TaskId: "task-1",
//			Param: &models.TaskEngineParam{
//				Param: `{"streams":[{"class":"test-class-1"}]}`,
//			},
//			LastModified: time.Now(),
//		},
//		{
//			TaskId: "task-2",
//			Param: &models.TaskEngineParam{
//				Param: `{"streams":[{"class":"test-class-2"}]}`,
//			},
//			LastModified: time.Now().Add(-time.Hour),
//		},
//	}
//
//	// Mock 依赖方法
//	defer Mock((*livetranscode.LiveTransCodeParamsAdapter).AssembleListTranscodeRunningTaskReq).Return(&models.Task{}).Build().UnPatch()
//	defer Mock((*tasker.CommonTaskBiz).GetRunningTaskIds).Return(tasks, nil).Build().UnPatch()
//	defer Mock((*global_param_loader.GlobalParamLoader).GetParseTagForSyncData).Return(true).Build().UnPatch()
//
//	// 设置服务
//	taskService.globalParamLoader = GetGlobalConfig(t)
//	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
//
//	// 创建 HTTP 请求
//	req, _ := http.NewRequest("GET", "/debug/getSyncData", nil)
//	w := httptest.NewRecorder()
//
//	// 执行测试
//	result, err := taskService.LiveTranscodeGetSyncData(w, req)
//
//	// 验证结果
//	assert.NoError(t, err)
//	assert.NotNil(t, result)
//
//	// 验证响应结构
//	response, ok := result.(map[string]interface{})
//	assert.True(t, ok)
//	res, ok := response["result"].(map[string]string)
//	assert.True(t, ok)
//	assert.Equal(t, "Success", res["code"])
//	assert.Equal(t, "Ok", res["message"])
//
//	taskList, ok := response["data"].([]map[string]interface{})
//	assert.True(t, ok)
//	assert.Len(t, taskList, 2)
//
//	// 验证第一个任务
//	assert.Equal(t, "task-1", taskList[0]["task_id"])
//	assert.Equal(t, "test-class-1", taskList[0]["tag"])
//	assert.NotNil(t, taskList[0]["gmt_modified"])
//
//	// 验证第二个任务
//	assert.Equal(t, "task-2", taskList[1]["task_id"])
//	assert.Equal(t, "test-class-2", taskList[1]["tag"])
//	assert.NotNil(t, taskList[1]["gmt_modified"])
//}

// TestTaskService_LiveTranscodeGetSyncData_GetRunningTaskIdsError_New 测试GetRunningTaskIds错误场景
func TestTaskService_LiveTranscodeGetSyncData_GetRunningTaskIdsError_New(t *testing.T) {
	// Mock 依赖方法 - 返回错误
	defer Mock((*livetranscode.LiveTransCodeParamsAdapter).AssembleListTranscodeRunningTaskReq).Return(&models.Task{}).Build().UnPatch()
	defer Mock((*tasker.CommonTaskBiz).GetRunningSimpleTasks).Return(nil, errors.New(400, "database error", "mm")).Build().UnPatch()

	// 设置服务
	taskService.globalParamLoader = GetGlobalConfig(t)
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())

	// 创建 HTTP 请求
	req, _ := http.NewRequest("GET", "/sync-data", nil)
	w := httptest.NewRecorder()

	// 执行测试
	result, err := taskService.LiveTranscodeGetSyncData(w, req)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "LiveTranscodeGetSyncData getRunningTaskIds error")
}

//// TestTaskService_LiveTranscodeGetSyncData_NoParseTag_New 测试不解析标签场景
//func TestTaskService_LiveTranscodeGetSyncData_NoParseTag_New(t *testing.T) {
//	// 设置测试数据
//	tasks := []*models.Task{
//		{
//			TaskId: "task-1",
//			Param: &models.TaskEngineParam{
//				Param: `{"streams":[{"class":"test-class-1"}]}`,
//			},
//			LastModified: time.Now(),
//		},
//	}
//
//	// Mock 依赖方法 - parseTag 为 false
//	defer Mock((*livetranscode.LiveTransCodeParamsAdapter).AssembleListTranscodeRunningTaskReq).Return(&models.Task{}).Build().UnPatch()
//	defer Mock((*tasker.CommonTaskBiz).GetRunningTaskIds).Return(tasks, nil).Build().UnPatch()
//	defer Mock((*global_param_loader.GlobalParamLoader).GetParseTagForSyncData).Return(false).Build().UnPatch()
//
//	// 设置服务
//	taskService.globalParamLoader = GetGlobalConfig(t)
//	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
//
//	// 创建 HTTP 请求
//	req, _ := http.NewRequest("GET", "/sync-data", nil)
//	w := httptest.NewRecorder()
//
//	// 执行测试
//	result, err := taskService.LiveTranscodeGetSyncData(w, req)
//
//	// 验证结果
//	assert.NoError(t, err)
//	assert.NotNil(t, result)
//
//	// 验证响应结构
//	response, ok := result.(map[string]interface{})
//	assert.True(t, ok)
//
//	taskList, ok := response["data"].([]map[string]interface{})
//	assert.True(t, ok)
//	assert.Len(t, taskList, 1)
//
//	// 验证没有 tag 字段
//	assert.Equal(t, "task-1", taskList[0]["task_id"])
//	_, hasTag := taskList[0]["tag"]
//	assert.False(t, hasTag)
//}

// TestTaskService_getTaskTag_Success_New 测试getTaskTag成功场景
func TestTaskService_getTaskTag_Success_New(t *testing.T) {
	// 测试正常情况
	params := `livetranscode_test-class_pre"}]}`
	tag := taskService.getTaskTag(params)
	assert.Equal(t, "test-class", tag)
}

// TestTaskService_getTaskTag_EmptyParams_New 测试空参数场景
func TestTaskService_getTaskTag_EmptyParams_New(t *testing.T) {
	// 测试空参数
	tag := taskService.getTaskTag("")
	assert.Equal(t, "", tag)
}

// TestTaskService_getTaskTag_InvalidJSON_New 测试无效JSON场景
func TestTaskService_getTaskTag_InvalidJSON_New(t *testing.T) {
	// 测试无效 JSON
	tag := taskService.getTaskTag("invalid json")
	assert.Equal(t, "", tag)
}

// TestTaskService_getTaskTag_NoStreams_New 测试无streams字段场景
func TestTaskService_getTaskTag_NoStreams_New(t *testing.T) {
	// 测试没有 streams 字段
	params := `{"other":"value"}`
	tag := taskService.getTaskTag(params)
	assert.Equal(t, "", tag)
}

// TestTaskService_getTaskTag_EmptyStreams_New 测试空streams数组场景
func TestTaskService_getTaskTag_EmptyStreams_New(t *testing.T) {
	// 测试空的 streams 数组
	params := `{"streams":[]}`
	tag := taskService.getTaskTag(params)
	assert.Equal(t, "", tag)
}

// TestTaskService_getTaskTag_NoClass_New 测试无class字段场景
func TestTaskService_getTaskTag_NoClass_New(t *testing.T) {
	// 测试没有 class 字段
	params := `{"streams":[{"other":"value"}]}`
	tag := taskService.getTaskTag(params)
	assert.Equal(t, "", tag)
}

// TestTaskService_getTaskTag_EmptyClass_New 测试空class字段场景
func TestTaskService_getTaskTag_EmptyClass_New(t *testing.T) {
	// 测试空的 class 字段
	params := `{"streams":[{"class":""}]}`
	tag := taskService.getTaskTag(params)
	assert.Equal(t, "", tag)
}

// TestTaskService_UpdateGlobalConfig_Success_New 测试UpdateGlobalConfig成功场景
func TestTaskService_UpdateGlobalConfig_Success_New(t *testing.T) {
	// 设置测试数据
	config := &lc.RateCallbackConfig{
		Contents: []lc.RateCallbackContent{
			{
				Domain:   "test.com",
				App:      "test",
				Token:    "token",
				Interval: 1000,
				Duration: 5000,
			},
		},
	}

	requestBody := map[string]interface{}{
		"rateCallbackConfig": config,
	}
	bodyBytes, _ := json.Marshal(requestBody)

	// Mock 依赖方法
	defer Mock((*global_param_loader.GlobalParamLoader).GetRateLimiter).Return(nil).Build().UnPatch() // 不限流
	defer Mock((*global_param_loader.GlobalParamLoader).SetParams).Return(nil).Build().UnPatch()
	defer Mock((*tasker.CommonTaskBiz).SendConfigToWorker).Return(nil).Build().UnPatch()

	// 设置服务
	taskService.globalParamLoader = GetGlobalConfig(t)

	// 创建 HTTP 请求
	req, _ := http.NewRequest("POST", "/updateConfig", bytes.NewReader(bodyBytes))
	w := httptest.NewRecorder()

	// 执行测试
	result, err := taskService.UpdateGlobalConfig(w, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)

	response, ok := result.(map[string]interface{})
	assert.True(t, ok)
	rr, ok := response["result"].(map[string]string)
	assert.Equal(t, "Success", rr["code"])
	assert.Equal(t, "Ok", rr["message"])
}

// TestTaskService_UpdateGlobalConfig_Throttled_New 测试限流场景
func TestTaskService_UpdateGlobalConfig_Throttled_New(t *testing.T) {
	// 创建限流器 - 不允许任何请求
	limiter := rate.NewLimiter(rate.Limit(0), 0)

	// Mock 依赖方法 - 返回限流器
	defer Mock((*global_param_loader.GlobalParamLoader).GetRateLimiter).Return(limiter).Build().UnPatch()

	// 设置服务
	taskService.globalParamLoader = GetGlobalConfig(t)

	// 创建 HTTP 请求
	req, _ := http.NewRequest("POST", "/update-config", strings.NewReader("{}"))
	w := httptest.NewRecorder()

	// 执行测试
	result, err := taskService.UpdateGlobalConfig(w, req)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, result)

	// 验证错误类型
	kratosErr, ok := err.(*errors.Error)
	assert.True(t, ok)
	assert.Equal(t, ThrottledErrorCode, kratosErr.Reason)
}

// TestTaskService_UpdateGlobalConfig_InvalidJSON_New 测试无效JSON场景
func TestTaskService_UpdateGlobalConfig_InvalidJSON_New(t *testing.T) {
	// Mock 依赖方法
	defer Mock((*global_param_loader.GlobalParamLoader).GetRateLimiter).Return(nil).Build().UnPatch()

	// 设置服务
	taskService.globalParamLoader = GetGlobalConfig(t)

	// 创建 HTTP 请求 - 无效 JSON
	req, _ := http.NewRequest("POST", "/update-config", strings.NewReader("invalid json"))
	w := httptest.NewRecorder()

	// 执行测试
	result, err := taskService.UpdateGlobalConfig(w, req)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "UpdateGlobalConfig unmarshal request error")
}

// TestTaskService_UpdateGlobalConfig_NullConfig_New 测试空配置场景
func TestTaskService_UpdateGlobalConfig_NullConfig_New(t *testing.T) {
	// 设置测试数据 - 空配置
	requestBody := map[string]interface{}{
		"rateCallbackConfig": nil,
	}
	bodyBytes, _ := json.Marshal(requestBody)

	// Mock 依赖方法
	defer Mock((*global_param_loader.GlobalParamLoader).GetRateLimiter).Return(nil).Build().UnPatch()

	// 设置服务
	taskService.globalParamLoader = GetGlobalConfig(t)

	// 创建 HTTP 请求
	req, _ := http.NewRequest("POST", "/update-config", bytes.NewReader(bodyBytes))
	w := httptest.NewRecorder()

	// 执行测试
	result, err := taskService.UpdateGlobalConfig(w, req)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "rateCallbackConfig.contents cannot be null or empty")
}

// TestTaskService_UpdateGlobalConfig_EmptyContents_New 测试空内容场景
func TestTaskService_UpdateGlobalConfig_EmptyContents_New(t *testing.T) {
	// 设置测试数据 - 空内容
	config := &lc.RateCallbackConfig{
		Contents: []lc.RateCallbackContent{},
	}

	requestBody := map[string]interface{}{
		"rateCallbackConfig": config,
	}
	bodyBytes, _ := json.Marshal(requestBody)

	// Mock 依赖方法
	defer Mock((*global_param_loader.GlobalParamLoader).GetRateLimiter).Return(nil).Build().UnPatch()

	// 设置服务
	taskService.globalParamLoader = GetGlobalConfig(t)

	// 创建 HTTP 请求
	req, _ := http.NewRequest("POST", "/update-config", bytes.NewReader(bodyBytes))
	w := httptest.NewRecorder()

	// 执行测试
	result, err := taskService.UpdateGlobalConfig(w, req)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "rateCallbackConfig.contents cannot be null or empty")
}

// TestTaskService_UpdateGlobalConfig_SetParamsError_New 测试SetParams错误场景
func TestTaskService_UpdateGlobalConfig_SetParamsError_New(t *testing.T) {
	// 设置测试数据
	config := &lc.RateCallbackConfig{
		Contents: []lc.RateCallbackContent{
			{
				Domain:   "test.com",
				App:      "test",
				Token:    "token",
				Interval: 1000,
				Duration: 5000,
			},
		},
	}

	requestBody := map[string]interface{}{
		"rateCallbackConfig": config,
	}
	bodyBytes, _ := json.Marshal(requestBody)

	// Mock 依赖方法 - SetParams 返回错误
	defer Mock((*global_param_loader.GlobalParamLoader).GetRateLimiter).Return(nil).Build().UnPatch()
	defer Mock((*global_param_loader.GlobalParamLoader).SetParams).Return(errors.New(400, "kv store error", "mm")).Build().UnPatch()

	// 设置服务
	taskService.globalParamLoader = GetGlobalConfig(t)

	// 创建 HTTP 请求
	req, _ := http.NewRequest("POST", "/update-config", bytes.NewReader(bodyBytes))
	w := httptest.NewRecorder()

	// 执行测试
	result, err := taskService.UpdateGlobalConfig(w, req)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "UpdateGlobalConfig save to kvstore error")
}

// TestTaskService_UpdateGlobalConfig_SendConfigToWorkerError_New 测试SendConfigToWorker错误场景
func TestTaskService_UpdateGlobalConfig_SendConfigToWorkerError_New(t *testing.T) {
	// 设置测试数据
	config := &lc.RateCallbackConfig{
		Contents: []lc.RateCallbackContent{
			{
				Domain:   "test.com",
				App:      "test",
				Token:    "token",
				Interval: 1000,
				Duration: 5000,
			},
		},
	}

	requestBody := map[string]interface{}{
		"rateCallbackConfig": config,
	}
	bodyBytes, _ := json.Marshal(requestBody)

	// Mock 依赖方法 - SendConfigToWorker 返回错误
	defer Mock((*global_param_loader.GlobalParamLoader).GetRateLimiter).Return(nil).Build().UnPatch()
	defer Mock((*global_param_loader.GlobalParamLoader).SetParams).Return(nil).Build().UnPatch()
	defer Mock((*tasker.CommonTaskBiz).SendConfigToWorker).Return(errors.New(400, "network error", "")).Build().UnPatch()

	// 设置服务
	taskService.globalParamLoader = GetGlobalConfig(t)

	// 创建 HTTP 请求
	req, _ := http.NewRequest("POST", "/update-config", bytes.NewReader(bodyBytes))
	w := httptest.NewRecorder()

	// 执行测试
	result, err := taskService.UpdateGlobalConfig(w, req)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "UpdateGlobalConfig send config to worker error")
}
