package service

import (
	"fmt"
	config2 "github.com/cinience/animus/config"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"io"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/livetranscode"
	"mpp/internal/taskmanager/biz/router"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/mocks"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/qproxy/server"
	"mpp/internal/taskmanager/repository"
	"net/http"
	pb "proto.mpp/api/taskmanager/v1"
	"strings"
	"testing"

	. "github.com/bytedance/mockey"
)

var (
	taskService *TaskService

	requestBody = `{
		"appName": "liverecord",
		"taskId": "00000000-7def-372c-a157-998d06eb0f72",
		"weight": 1,
		"params": {
			"callbackUrl": "http://livebiz-record-inside.alivecdn.com/record/callback",
			"ossInfo": [
				{
					"ossBucket": "liveng",
					"ossEndpoint": "oss-cn-shanghai.aliyuncs.com",
					"userId": 123,
					"ramRole": "AliyunMTSDefaultRole"
				}
			],
			"publishTime": 1709704430503,
			"sessionId": "live6.cn6729_82698_2516438564_1709704429716",
			"useInternal": true,
			"userId": 123,
			"slice": [
				{
					"duration": 10,
					"prefix": "mediaplatform/00000000-3ccc-4128-85e5-4a172c1a5170/{Sequence}",
					"format": "ts"
				}
			],
			"avMode": "NIL",
			"rtmpUrl": "rtmp://127.0.0.1/mediaplatform/liveng.alicdn.com_b581e031-3ccc-4128-85e5-4a172c1a5170?vhost=liveng.alicdn.com&ali_ols_play=on"
		}
	}`

	config = &conf.Bootstrap{
		Data: &conf.Data{
			Redis: &conf.Data_Redis{
				Endpoint: "redis://127.0.0.1:6379",
			},
		},
		Router: &conf.Router{
			Percent:         100,
			OldSystemHost:   "old-host",
			OldSystemExist:  true,
			OldAiSystemHost: "old-ai-host",
			EngineTable: []*conf.EngineTable{
				{
					Product:     "ai",
					EngineModel: "ai-video",
					Tag:         "tag",
					Percent:     100,
				},
			},
		},
	}
	queueServer   *server.AsynqServer
	route         *router.Router
	switchOperate *common.SwitchService
)

func init() {
	queueServer, _, _ = server.NewAsynqServer(&conf.Bootstrap{Data: &conf.Data{Redis: &conf.Data_Redis{Endpoint: "redis://127.0.0.1:6379"}}}, nil)
	defer Mock(config2.WatchConfig).Return(nil).Build().UnPatch()
	mockRepo := mocks.NewMockSwitchConfigRepo(gomock.NewController(nil))
	route = router.NewRouter(config, repository.NewRedisClient(config))
	mockRepo.EXPECT().ListAll(gomock.Any()).Return(nil, nil).AnyTimes()
	switchOperate = common.NewSwitchService(mockRepo, nil)
	taskService, _ = NewTaskService(nil, nil, nil, nil, nil, nil, nil, nil, queueServer, log.DefaultLogger, route, nil, nil, nil, nil, nil, nil, nil)
}

func TestRecordHlsStartTaskInNewSystemSuccess(t *testing.T) {
	req, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(requestBody))
	req.Header.Add("User-Agent", "my-app/0.0.1")
	defer Mock((*router.Router).GetRouteData).Return(&router.RouteData{Id: "00000000-7def-372c-a157-998d06eb0f74", System: "new", Status: "running"}).Build().UnPatch()
	defer Mock((*router.Router).AddLock).Return(true).Build().UnPatch()
	defer Mock((*TaskService).StartTask).Return(&pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}, nil).Build().UnPatch()
	defer Mock((*router.Router).SetRouteData).Return().Build().UnPatch()
	taskService.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(taskService.globalParamLoader, log.GetLogger())
	taskService.schedulerSwitcher = livetranscode.NewSchedulerSwitchOperator(log.GetLogger(), switchOperate)
	_, err := taskService.RecordHlsStartTask(nil, req, getRequestBody(req))
	assert.Nil(t, err)
	//assert.NotNil(t, err)
}

func TestRecordHlsStartTaskRouterToNewSystemErrorRetryToOld(t *testing.T) {
	req, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(requestBody))
	req.Header.Add("User-Agent", "my-app/0.0.1")
	defer Mock((*router.Router).GetRouteData).Return(nil).Build().UnPatch()
	defer Mock((*router.Router).AddLock).Return(true).Build().UnPatch()
	defer Mock((*router.Router).GetRouteHitResult).Return(1).Build().UnPatch()
	defer Mock((*TaskService).StartTask).Return(nil, fmt.Errorf("error")).Build().UnPatch()
	defer Mock((*router.Router).SetRouteData).Return().Build().UnPatch()
	_, err := taskService.RecordHlsStartTask(nil, req, getRequestBody(req))
	// 单任务加锁后，这里应该停在锁这里
	assert.Contains(t, err.Error(), "oldSystem http request fail")
}

func TestRecordHlsStartTaskInOldSystemSuccess(t *testing.T) {

	req, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(requestBody))
	req.Header.Add("User-Agent", "my-app/0.0.1")
	defer Mock((*router.Router).GetRouteData).Return(&router.RouteData{Id: "00000000-7def-372c-a157-998d06eb0f72", System: "old", Status: "running"}).Build().UnPatch()
	defer Mock((*router.Router).SetRouteData).Return().Build().UnPatch()

	response := &http.Response{
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(strings.NewReader(`{"result":{"code":"Success","message":""}}`)),
	}
	defer Mock((*http.Client).Do).Return(response, nil).Build().UnPatch()

	_, err := taskService.RecordHlsStartTask(nil, req, getRequestBody(req))
	assert.Nil(t, err)
}

func TestRecordHlsStartTaskRouterToOldSystemError(t *testing.T) {
	req, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(requestBody))
	req.Header.Add("User-Agent", "my-app/0.0.1")

	defer Mock((*router.Router).GetRouteData).Return(nil).Build().UnPatch()
	defer Mock((*router.Router).GetRouteHitResult).Return(0).Build().UnPatch()
	defer Mock((*TaskService).StartTask).Return(&pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}, nil).Build().UnPatch()
	defer Mock((*router.Router).SetRouteData).Return().Build().UnPatch()

	response := &http.Response{
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(strings.NewReader(`{"result":{"code":"Success","message":""}`)),
	}
	defer Mock((*http.Client).Do).Return(response, nil).Build().UnPatch()
	_, err := taskService.RecordHlsStartTask(nil, req, getRequestBody(req))
	assert.Contains(t, err.Error(), "oldSystem resp return json fail")
}

func TestRecordHlsStopTaskInNewSystemSuccess(t *testing.T) {

	req, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(requestBody))
	req.Header.Add("User-Agent", "my-app/0.0.1")
	defer Mock((*router.Router).GetRouteData).Return(&router.RouteData{Id: "00000000-7def-372c-a157-998d06eb0f72", System: "new", Status: "running"}).Build().UnPatch()
	defer Mock((*TaskService).StopTask).Return(&pb.StopTaskReply{
		Result: defaultSuccessReplyResult,
	}, nil).Build().UnPatch()
	defer Mock((*router.Router).SetRouteData).Return().Build().UnPatch()
	_, err := taskService.RecordHlsStopTask(nil, req, getRequestBody(req))
	assert.Nil(t, err)
}

func TestRecordHlsStopTaskInOldSystemSuccess(t *testing.T) {

	req, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(requestBody))
	req.Header.Add("User-Agent", "my-app/0.0.1")
	defer Mock((*router.Router).GetRouteData).Return(&router.RouteData{Id: "00000000-7def-372c-a157-998d06eb0f72", System: "old", Status: "running"}).Build().UnPatch()
	response := &http.Response{
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(strings.NewReader(`{"result":{"code":"Success","message":""}}`)),
	}
	defer Mock((*http.Client).Do).Return(response, nil).Build().UnPatch()
	defer Mock((*router.Router).SetRouteData).Return().Build().UnPatch()
	_, err := taskService.RecordHlsStopTask(nil, req, getRequestBody(req))
	assert.Nil(t, err)
}

func TestRecordHlsStopTaskRouterToOldSystemError(t *testing.T) {

	req, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(requestBody))
	req.Header.Add("User-Agent", "my-app/0.0.1")
	defer Mock((*router.Router).GetRouteData).Return(&router.RouteData{Id: "00000000-7def-372c-a157-998d06eb0f72", System: "old", Status: "running"}).Build().UnPatch()
	response := &http.Response{
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(strings.NewReader(`{"result":{"code":"Success","message":""}}`)),
	}
	defer Mock((*http.Client).Do).Return(response, fmt.Errorf("test error")).Build().UnPatch()
	defer Mock((*router.Router).SetRouteData).Return().Build().UnPatch()
	_, err := taskService.RecordHlsStopTask(nil, req, getRequestBody(req))
	assert.NotNil(t, err)
}

func TestRecordHlsListTask(t *testing.T) {

	req, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(requestBody))
	req.Header.Add("User-Agent", "my-app/0.0.1")
	response := &http.Response{
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(strings.NewReader(`{"data":[{"task_id":"aa","gmt_modified":1715862576,"tag":"livehls"}]}`)),
	}
	defer Mock((*http.Client).Do).Return(response, nil).Build().UnPatch()
	defer Mock((*TaskService).GetRunningTasks).Return([]*models.Task{
		{
			TaskId:      "00000000-7def-372c-a157-998d06eb0f72",
			EngineModel: "livehls",
		},
	}, nil).Build().UnPatch()
	tasks, err := taskService.RecordHlsListTask(nil, req)
	assert.Nil(t, err)
	assert.Equal(t, 2, len(tasks.(ListTaskData).Data))
}

func TestRecordHlsListTaskFailed(t *testing.T) {
	PatchConvey("recordHls listTask from New system fail", t, func() {
		req, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(requestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		response := &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(`{"data":[{"task_id":"aa","gmt_modified":1715862576,"tag":"livehls"}]}`)),
		}
		Mock((*http.Client).Do).Return(response, nil).Build()
		Mock((*TaskService).GetRunningTasks).Return(nil, fmt.Errorf("test error")).Build()
		_, err := taskService.RecordHlsListTask(nil, req)
		assert.Contains(t, err.Error(), "recordHls listTask from new system fail")
	})
	PatchConvey("recordHls listTask from old system fail", t, func() {
		req, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(requestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		response := &http.Response{
			StatusCode: http.StatusBadRequest,
			Body:       io.NopCloser(strings.NewReader(`{"data":[{"task_id":"aa","gmt_modified":1715862576,"tag":"livehls"}]}`)),
		}
		Mock((*http.Client).Do).Return(response, nil).Build()
		_, err := taskService.RecordHlsListTask(nil, req)
		assert.Contains(t, err.Error(), "recordHls listTask from old system fail")
	})
}

func TestRecordHlsListWorker(t *testing.T) {
	PatchConvey("recordHls listWorker from Old system fail", t, func() {
		req, _ := http.NewRequest("POST", "http://example.com/start?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(requestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		response := &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(`{"workers":[{"appGroup":"livehls","workerId":"id1","quota":800}]}`)),
		}
		Mock((*http.Client).Do).Return(response, nil).Build()
		data, err := taskService.RecordHlsListWorker(nil, req)
		assert.Nil(t, err)
		assert.Equal(t, 2, len(data.(map[string]interface{})))
	})
}
