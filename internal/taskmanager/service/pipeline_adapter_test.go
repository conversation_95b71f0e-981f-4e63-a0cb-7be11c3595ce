package service

import (
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"mpp/internal/taskmanager/biz/common"
	"net/http"
	pb "proto.mpp/api/taskmanager/v1"
	"strings"
	"testing"
)

/**
 * @Author: qinxin
 * @Date: 2024/9/26 09:51
 * @Desc:
 * @Version 1.0
 */

func TestPipelineService_AdapterCreatePipeline(t *testing.T) {
	//返回成功
	mockey.PatchConvey("AdapterCreatePipeline and  return success", t, func() {
		req, _ := http.NewRequest("POST", stdStartUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")

		defer mockey.Mock((*PipelineService).CreatePipeline).Return(&pb.CreatePipelineReply{
			Result: defaultSuccessReplyResult,
		}, nil).Build().UnPatch()
		_, err := pipelineService.AdapterCreatePipeline(nil, req)
		assert.Nil(t, err)
	})
	//返回失败
	mockey.PatchConvey("AdapterCreatePipeline and  return error", t, func() {
		req, _ := http.NewRequest("POST", stdStartUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")

		defer mockey.Mock((*PipelineService).CreatePipeline).Return(nil, common.ErrInternalError).Build().UnPatch()
		_, err := pipelineService.AdapterCreatePipeline(nil, req)
		assert.NotNil(t, err)
	})
}

func TestPipelineService_AdapterActivatePipeline(t *testing.T) {
	// 成功激活管道
	mockey.PatchConvey("AdapterActivatePipeline returns success", t, func() {
		req, _ := http.NewRequest("POST", stdStartUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")

		defer mockey.Mock((*PipelineService).ActivatePipeline).Return(&pb.ActivatePipelineReply{
			Result: defaultSuccessReplyResult,
		}, nil).Build().UnPatch()

		_, err := pipelineService.AdapterActivatePipeline(nil, req)
		assert.Nil(t, err)
	})

	// 失败激活管道
	mockey.PatchConvey("AdapterActivatePipeline returns error", t, func() {
		req, _ := http.NewRequest("POST", stdStartUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")

		defer mockey.Mock((*PipelineService).ActivatePipeline).Return(nil, common.ErrInternalError).Build().UnPatch()

		_, err := pipelineService.AdapterActivatePipeline(nil, req)
		assert.NotNil(t, err)
	})
}

func TestPipelineService_AdapterPausePipeline(t *testing.T) {
	// 成功暂停管道
	mockey.PatchConvey("AdapterPausePipeline returns success", t, func() {
		req, _ := http.NewRequest("POST", stdStartUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")

		defer mockey.Mock((*PipelineService).PausePipeline).Return(&pb.PausePipelineReply{
			Result: defaultSuccessReplyResult,
		}, nil).Build().UnPatch()

		_, err := pipelineService.AdapterPausePipeline(nil, req)
		assert.Nil(t, err)
	})

	// 失败暂停管道
	mockey.PatchConvey("AdapterPausePipeline returns error", t, func() {
		req, _ := http.NewRequest("POST", stdStartUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")

		defer mockey.Mock((*PipelineService).PausePipeline).Return(nil, common.ErrInternalError).Build().UnPatch()

		_, err := pipelineService.AdapterPausePipeline(nil, req)
		assert.NotNil(t, err)
	})
}

func TestPipelineService_AdapterUpdateQuotaConfigPipeline(t *testing.T) {
	// 成功更新配额配置
	mockey.PatchConvey("AdapterUpdateQuotaConfigPipeline returns success", t, func() {
		req, _ := http.NewRequest("POST", stdStartUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")

		defer mockey.Mock((*PipelineService).UpdateQuotaConfigPipeline).Return(&pb.UpdateQuotaConfigPipelineReply{
			Result: defaultSuccessReplyResult,
		}, nil).Build().UnPatch()

		_, err := pipelineService.AdapterUpdateQuotaConfigPipeline(nil, req)
		assert.Nil(t, err)
	})

	// 更新配额配置失败
	mockey.PatchConvey("AdapterUpdateQuotaConfigPipeline returns error", t, func() {
		req, _ := http.NewRequest("POST", stdStartUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")

		defer mockey.Mock((*PipelineService).UpdateQuotaConfigPipeline).Return(nil, common.ErrInternalError).Build().UnPatch()

		_, err := pipelineService.AdapterUpdateQuotaConfigPipeline(nil, req)
		assert.NotNil(t, err)
	})
}
