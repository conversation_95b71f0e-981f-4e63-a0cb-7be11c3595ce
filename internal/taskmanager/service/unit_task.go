package service

import (
	"fmt"
	"io"
	"net/http"

	"mpp/internal/taskmanager/biz/global_param_loader"
	"mpp/internal/taskmanager/biz/ha"
	"mpp/internal/taskmanager/repository"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
)

type UnitTaskService struct {
	repo         repository.UnitTaskRepo
	log          *log.Helper
	haService    *ha.HAService
	globalConfig *global_param_loader.GlobalParamLoader
}

func NewUnitTaskService(repo repository.UnitTaskRepo, logger log.Logger, haService *ha.HAService, globalConfig *global_param_loader.GlobalParamLoader) *UnitTaskService {
	return &UnitTaskService{repo: repo, log: log.NewHelper(logger), haService: haService, globalConfig: globalConfig}
}
func (u *UnitTaskService) WrapErrorWithReasonAndMessage(reason string, message string) error {
	err := errors.New(500, reason, message)
	return err
}

func (u *UnitTaskService) LiveTranscodeApiResult(reason string, message string, rst map[string]interface{}) any {
	if rst == nil {
		rst = make(map[string]interface{})
	}
	rst["description"] = message
	if reason == "" || reason == "ok" || reason == "OK" || reason == "Success" {
		rst["retCode"] = 0
	} else {
		rst["retCode"] = -1
	}
	return rst
}

func (u *UnitTaskService) GetTasks(w http.ResponseWriter, r *http.Request) (any, error) {
	ctx := r.Context()
	u.log.WithContext(ctx).Infof("haservice getTasks, url:%s, headers:%+v", r.URL.String(), r.Header)
	body, err := io.ReadAll(r.Body)
	js := json.Get(body)
	unit := js.Get("unit").ToString()
	tasks, err := u.repo.GetUnitTaskAll(ctx, unit)
	if err != nil {
		u.log.WithContext(ctx).Errorf("get tasks failed, unit:%s, err:%v", unit, err)
		return nil, u.WrapErrorWithReasonAndMessage(InternalErrorCode, fmt.Sprintf("get tasks failed, unit:%s, err:%v", unit, err))
	}
	taskIds := make([]string, 0)
	for _, task := range tasks {
		taskIds = append(taskIds, task.TaskId)
	}
	return u.LiveTranscodeApiResult("Success", "Success", map[string]any{"content": map[string]any{"taskIds": tasks}}), nil
}

func (u *UnitTaskService) GetTask(w http.ResponseWriter, r *http.Request) (any, error) {
	ctx := r.Context()
	u.log.WithContext(ctx).Infof("haservice getTask, url:%s, headers:%+v", r.URL.String(), r.Header)
	body, err := io.ReadAll(r.Body)
	js := json.Get(body)
	taskId := js.Get("taskId").ToString()
	task, err := u.repo.GetUnitTask(ctx, taskId)
	if err != nil {
		u.log.WithContext(ctx).Errorf("get task failed, taskId:%s, err:%v", taskId, err)
		return nil, u.WrapErrorWithReasonAndMessage(InternalErrorCode, fmt.Sprintf("get task failed, taskId:%s, err:%v", taskId, err))
	}
	return u.LiveTranscodeApiResult("Success", "Success", map[string]any{"content": map[string]any{"task": task}}), nil
}

func (u *UnitTaskService) UpdateTask(w http.ResponseWriter, r *http.Request) (any, error) {
	ctx := r.Context()
	u.log.WithContext(ctx).Infof("haservice updateTask, url:%s, headers:%+v", r.URL.String(), r.Header)
	body, err := io.ReadAll(r.Body)
	js := json.Get(body)
	taskId := js.Get("taskId").ToString()
	err = u.repo.UpdateUnitTaskStatus(ctx, taskId)
	if err != nil {
		u.log.WithContext(ctx).Errorf("update task failed, taskId:%s, err:%v", taskId, err)
		return nil, u.WrapErrorWithReasonAndMessage(InternalErrorCode, fmt.Sprintf("update task failed, taskId:%s, err:%v", taskId, err))
	}
	// todo 原业务代码存在问题，待确认
	//err = u.repo.DeleteTask(ctx, taskId)
	//if err != nil {
	//	return u.ResultReturn(200, "Fail", err.Error(), nil), err
	//}
	err = u.repo.UpdateTaskStatus(ctx, taskId, 0, 0)
	if err != nil {
		u.log.WithContext(ctx).Errorf("update task failed, taskId:%s, err:%v", taskId, err)
		return nil, u.WrapErrorWithReasonAndMessage(InternalErrorCode, fmt.Sprintf("update task failed, taskId:%s, err:%v", taskId, err))
	}
	return u.LiveTranscodeApiResult("Success", "Success", map[string]any{"content": map[string]any{"task": nil}}), nil
}

func (u *UnitTaskService) SetHAStatus(w http.ResponseWriter, r *http.Request) (any, error) {
	ctx := r.Context()
	u.log.WithContext(ctx).Infof("haservice setHAStatus, url:%s, headers:%+v", r.URL.String(), r.Header)
	body, err := io.ReadAll(r.Body)
	if body == nil {
		u.log.WithContext(ctx).Errorf("set ha status failed due to must specify status.")
		return nil, u.WrapErrorWithReasonAndMessage("FAIL", "must specify status.")
	}
	js := json.Get(body)
	status := js.Get("status").ToBool()
	host := js.Get("host").ToString()
	if status && host == "" {
		u.log.WithContext(ctx).Errorf("set ha status failed, status:%v, host:%v", status, host)
		return nil, u.WrapErrorWithReasonAndMessage("HAHostIsNull", "ha host is null.")
	}

	if !u.haService.SetHaAvailable(status, host) {
		u.log.WithContext(ctx).Warnf("set ha status failed, status:%v, host:%v", status, host)
		return nil, u.WrapErrorWithReasonAndMessage("Fail", err.Error())
	}
	return u.LiveTranscodeApiResult("Success", "Success", nil), nil
}

func (u *UnitTaskService) SyncAllTasks(w http.ResponseWriter, r *http.Request) (any, error) {
	ctx := r.Context()
	u.log.WithContext(ctx).Infof("haservice syncAllTasks, url:%s, headers:%+v", r.URL.String(), r.Header)
	if !u.haService.Status {
		u.log.WithContext(ctx).Errorf("sync all tasks failed, ha service is not in ha status.")
		return nil, u.WrapErrorWithReasonAndMessage("HASatusInvalid", "ha service is not in ha status.")
	}
	if u.haService.Host == "" {
		u.log.WithContext(ctx).Errorf("sync all tasks failed, ha host is null.")
		return nil, u.WrapErrorWithReasonAndMessage("HAHostIsNull", "ha host is null.")
	}

	tasks := u.haService.GetAllTasks(u.globalConfig.GetDefaultUnit())
	if tasks == nil || len(tasks) == 0 {
		u.log.WithContext(ctx).Infof("sync all tasks success, sync task length is 0")
		return u.LiveTranscodeApiResult("Success", "Success", nil), nil
	}
	var taskIds []string
	for _, taskId := range tasks {
		taskIds = append(taskIds, taskId)
		u.haService.RecoverDataFromHAService(taskId)
		u.haService.UpdateTaskToHAService(taskId)
	}
	return u.LiveTranscodeApiResult("Success", "Success", map[string]any{"content": map[string]any{"taskIds": taskIds}}), nil
}
