package service

import (
	"context"
	"github.com/bytedance/mockey"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/pipeline"
	"mpp/internal/taskmanager/biz/sentinel"
	"mpp/internal/taskmanager/models"
	pb "proto.mpp/api/taskmanager/v1"
	"testing"
)

/**
 * @Author: qinxin
 * @Date: 2024/9/26 09:50
 * @Desc:
 * @Version 1.0
 */

var (
	pipelineService *PipelineService
)

func init() {
	pipelineService = NewPipelineService(nil, &pipeline.CommonPipelineBiz{}, &sentinel.SentinelBiz{}, log.DefaultLogger)

}

func TestPipelineService_CreatePipeline(t *testing.T) {
	//返回成功
	mockey.PatchConvey("CreatePipeline and  return success", t, func() {
		defer mockey.Mock((*pipeline.CommonPipelineBiz).CreatePipeline).Return(true, nil).Build().UnPatch()
		_, err := pipelineService.CreatePipeline(context.Background(), &pb.CreatePipelineRequest{
			RequestId: "002-unit-test-requestid",
			Product:   "test-product",
			UserId:    "test-user-id",
		})
		//unsupported tasker type for UpdateTask
		assert.Nil(t, err)
	})

	//返回失败
	mockey.PatchConvey("CreatePipeline and  return err", t, func() {
		defer mockey.Mock((*pipeline.CommonPipelineBiz).CreatePipeline).Return(true, common.ErrInternalError).Build().UnPatch()
		_, err := pipelineService.CreatePipeline(context.Background(), &pb.CreatePipelineRequest{
			RequestId: "002-unit-test-requestid",
			Product:   "test-product",
			UserId:    "test-user-id",
		})
		//unsupported tasker type for UpdateTask
		assert.NotNil(t, err)
	})
}

func TestPipelineService_ActivatePipeline(t *testing.T) {
	// 返回成功
	mockey.PatchConvey("ActivatePipeline and return success", t, func() {
		defer mockey.Mock((*pipeline.CommonPipelineBiz).UpdatePipelineStatus).Return(true, nil).Build().UnPatch()
		_, err := pipelineService.ActivatePipeline(context.Background(), &pb.ActivatePipelineRequest{
			RequestId:  "002-unit-test-requestid",
			UserId:     "test-user-id",
			PipelineId: "test-pipeline-id",
		})
		assert.Nil(t, err)
	})

	// 返回失败
	mockey.PatchConvey("ActivatePipeline and return err", t, func() {
		defer mockey.Mock((*pipeline.CommonPipelineBiz).UpdatePipelineStatus).Return(false, common.ErrInternalError).Build().UnPatch()
		_, err := pipelineService.ActivatePipeline(context.Background(), &pb.ActivatePipelineRequest{
			RequestId:  "002-unit-test-requestid",
			UserId:     "test-user-id",
			PipelineId: "test-pipeline-id",
		})
		assert.NotNil(t, err)
	})
}

func TestPipelineService_PausePipeline(t *testing.T) {
	// 返回成功
	mockey.PatchConvey("PausePipeline and return success", t, func() {
		defer mockey.Mock((*pipeline.CommonPipelineBiz).UpdatePipelineStatus).Return(true, nil).Build().UnPatch()
		_, err := pipelineService.PausePipeline(context.Background(), &pb.PausePipelineRequest{
			RequestId:  "002-unit-test-requestid",
			UserId:     "test-user-id",
			PipelineId: "test-pipeline-id",
		})
		assert.Nil(t, err)
	})

	// 返回失败
	mockey.PatchConvey("PausePipeline and return err", t, func() {
		defer mockey.Mock((*pipeline.CommonPipelineBiz).UpdatePipelineStatus).Return(false, common.ErrInternalError).Build().UnPatch()
		_, err := pipelineService.PausePipeline(context.Background(), &pb.PausePipelineRequest{
			RequestId:  "002-unit-test-requestid",
			UserId:     "test-user-id",
			PipelineId: "test-pipeline-id",
		})
		assert.NotNil(t, err)
	})
}

func TestPipelineService_UpdateQuotaConfigPipeline(t *testing.T) {
	// 返回成功
	mockey.PatchConvey("UpdateQuotaConfigPipeline and return success", t, func() {
		defer mockey.Mock((*pipeline.CommonPipelineBiz).UpdatePipelinesScheduleLevel).Return(true, nil).Build().UnPatch()
		defer mockey.Mock((*sentinel.SentinelBiz).CreateTaskSentinelConfig).Return(true, nil).Build().UnPatch()

		_, err := pipelineService.UpdateQuotaConfigPipeline(context.Background(), &pb.UpdateQuotaConfigPipelineRequest{
			Product:     "test-product",
			UserId:      "test-user-id",
			PipelineId:  "test-pipeline-id",
			EngineModel: "test-engine-model",
			QuotaConfig: &pb.QuotaConfig{MaxParallelNum: 10},
		})
		assert.Nil(t, err)
	})

	// 返回失败
	mockey.PatchConvey("UpdateQuotaConfigPipeline and return err", t, func() {
		defer mockey.Mock((*pipeline.CommonPipelineBiz).UpdatePipelinesScheduleLevel).
			Return(false, common.ErrInternalError).Build().UnPatch()
		defer mockey.Mock((*sentinel.SentinelBiz).CreateTaskSentinelConfig).
			Return(false, common.ErrInternalError).Build().UnPatch()

		_, err := pipelineService.UpdateQuotaConfigPipeline(context.Background(), &pb.UpdateQuotaConfigPipelineRequest{
			Product:     "test-product",
			UserId:      "test-user-id",
			PipelineId:  "test-pipeline-id",
			EngineModel: "test-engine-model",
			QuotaConfig: &pb.QuotaConfig{MaxParallelNum: 10},
		})
		assert.NotNil(t, err)
	})
}

func TestPipelineService_DeleteQuotaConfigPipeline(t *testing.T) {
	// 返回成功
	mockey.PatchConvey("DeleteQuotaConfigPipeline and return success", t, func() {
		defer mockey.Mock((*sentinel.SentinelBiz).DeleteTaskSentinelConfig).Return(nil, nil).Build().UnPatch()
		_, err := pipelineService.DeleteQuotaConfigPipeline(context.Background(), &pb.DeleteQuotaConfigPipelineRequest{
			Product:     "test-product",
			UserId:      "test-user-id",
			PipelineId:  "test-pipeline-id",
			EngineModel: "test-engine-model",
			Tag:         "default-tag",
		})
		assert.Nil(t, err)
	})

	// 返回失败
	mockey.PatchConvey("DeleteQuotaConfigPipeline and return err", t, func() {
		defer mockey.Mock((*sentinel.SentinelBiz).DeleteTaskSentinelConfig).Return(nil, common.ErrInternalError).Build().UnPatch()
		_, err := pipelineService.DeleteQuotaConfigPipeline(context.Background(), &pb.DeleteQuotaConfigPipelineRequest{
			Product:     "test-product",
			UserId:      "test-user-id",
			PipelineId:  "test-pipeline-id",
			EngineModel: "test-engine-model",
			Tag:         "default-tag",
		})
		assert.NotNil(t, err)
	})
}

func TestPipelineService_GetQuotaConfigPipeline(t *testing.T) {
	// 返回成功
	mockey.PatchConvey("GetQuotaConfigPipeline and return success", t, func() {
		defer mockey.Mock((*sentinel.SentinelBiz).GetTaskSentinelConfig).Return(&models.TaskSentinelConfig{}, nil).Build().UnPatch()
		_, err := pipelineService.GetQuotaConfigPipeline(context.Background(), &pb.GetQuotaConfigPipelineRequest{
			Product:     "test-product",
			UserId:      "test-user-id",
			PipelineId:  "test-pipeline-id",
			EngineModel: "test-engine-model",
			Tag:         "default-tag",
		})
		assert.Nil(t, err)
	})

	// 返回失败
	mockey.PatchConvey("GetQuotaConfigPipeline and return err", t, func() {
		defer mockey.Mock((*sentinel.SentinelBiz).GetTaskSentinelConfig).Return(nil, common.ErrInternalError).Build().UnPatch()
		_, err := pipelineService.GetQuotaConfigPipeline(context.Background(), &pb.GetQuotaConfigPipelineRequest{
			Product:     "test-product",
			UserId:      "test-user-id",
			PipelineId:  "test-pipeline-id",
			EngineModel: "test-engine-model",
			Tag:         "default-tag",
		})
		assert.NotNil(t, err)
	})
}
