package service

import (
	"context"
	"fmt"
	"net/http"
	"runtime"
	"strings"
	"time"

	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/global_param_loader"
	"mpp/internal/taskmanager/biz/ha"
	"mpp/internal/taskmanager/biz/livetranscode"
	"mpp/internal/taskmanager/biz/quota"
	"mpp/internal/taskmanager/biz/router"
	"mpp/internal/taskmanager/biz/tasker"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/custom_types"
	"mpp/internal/taskmanager/models/domain"
	lc "mpp/internal/taskmanager/models/livetranscode"
	"mpp/internal/taskmanager/qproxy/server"
	"mpp/internal/taskmanager/qproxy/worker"
	"mpp/internal/taskmanager/tracing/tasker_span"
	"mpp/internal/taskscheduler/biz"
	"mpp/pkg/tracer/tasker_tracing"
	"mpp/pkg/utils"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	v1 "proto.mpp/api/common/v1"
	pb "proto.mpp/api/taskmanager/v1"
)

type TaskService struct {
	pb.UnimplementedTaskServer
	streamTaskBiz    *tasker.CommonTaskBiz
	batchTaskBiz     *tasker.BatchTaskBiz
	recordHlsTaskBiz *tasker.RecordHlsTaskBiz
	simpleTaskBiz    *tasker.SimpleTaskBiz

	taskStatusSync        *tasker.TaskStatusSync
	engineTaskSync        *tasker.EngineTaskSync
	schedulerResourceSync *tasker.SchedulerResourceSync
	queueServer           *server.AsynqServer
	taskTracer            *tasker_tracing.Tracer
	log                   *log.Helper
	router                *router.Router

	sentinelTaskSync *tasker.TaskSentinelSync
	quota            *quota.QuotaService
	batchTaskSync    *tasker.BatchTaskSync

	//直播配置
	globalParamLoader          *global_param_loader.GlobalParamLoader
	schedulerSwitcher          *livetranscode.SchedulerSwitchOperator
	liveTransCodeParamsAdapter *livetranscode.LiveTransCodeParamsAdapter
	haService                  *ha.HAService
}

var (
	defaultSuccessReplyResult = &v1.CommonReplyResult{Code: http.StatusOK, Reason: "Success"}
)

func NewTaskService(bootstrap *conf.Bootstrap, streamTaskBiz *tasker.CommonTaskBiz, batchTaskBiz *tasker.BatchTaskBiz, recordHlsTaskBiz *tasker.RecordHlsTaskBiz,
	simpleTaskBiz *tasker.SimpleTaskBiz, taskStatusSync *tasker.TaskStatusSync, engineTaskSync *tasker.EngineTaskSync, schedulerResourceSync *tasker.SchedulerResourceSync,
	queueServer *server.AsynqServer, logger log.Logger, router *router.Router, sentinelTaskSync *tasker.TaskSentinelSync,
	quota *quota.QuotaService, batchTaskSync *tasker.BatchTaskSync,
	globalParamLoader *global_param_loader.GlobalParamLoader, schedulerSwitcher *livetranscode.SchedulerSwitchOperator, liveTransCodeParamsAdapter *livetranscode.LiveTransCodeParamsAdapter, haService *ha.HAService) (*TaskService, error) {
	queueServer.AddWorker(worker.TaskCommon, worker.NewAsyncWorker(batchTaskBiz))

	err := queueServer.Start()
	if err != nil {
		return nil, err
	}

	return &TaskService{
		streamTaskBiz:              streamTaskBiz,
		batchTaskBiz:               batchTaskBiz,
		recordHlsTaskBiz:           recordHlsTaskBiz,
		simpleTaskBiz:              simpleTaskBiz,
		taskStatusSync:             taskStatusSync,
		engineTaskSync:             engineTaskSync,
		schedulerResourceSync:      schedulerResourceSync,
		queueServer:                queueServer,
		taskTracer:                 tasker_tracing.NewTracer(tasker_tracing.WithProducerSpanKind()),
		log:                        log.NewHelper(logger),
		router:                     router,
		sentinelTaskSync:           sentinelTaskSync,
		quota:                      quota,
		batchTaskSync:              batchTaskSync,
		globalParamLoader:          globalParamLoader,
		schedulerSwitcher:          schedulerSwitcher,
		liveTransCodeParamsAdapter: liveTransCodeParamsAdapter,
		haService:                  haService,
	}, nil
}

func (s *TaskService) InvokeTask(ctx context.Context, req *pb.InvokeTaskRequest) (*pb.InvokeTaskReply, error) {
	var (
		err error
		rst *models.SimpleTaskResult
	)
	quota := req.Quota
	if len(req.QuotaSet) > 0 {
		quota = req.QuotaSet
	}
	RequireResource := &models.RequestResource{
		Quota:    quota,
		Affinity: models.ToBizTaskAffinity(req.Affinity),
	}
	//trace := ""
	task := &models.Task{
		Product:        req.Product,
		TaskId:         req.TaskId,
		JobId:          req.JobId,
		UserId:         req.UserId,
		BizStatus:      models.TaskStateActive,
		InternalStatus: models.TaskStateActive,
		EngineModel:    req.EngineModel,
		Tag:            req.Tag,
		Param: &models.TaskEngineParam{
			Param:    req.EngineParams,
			ParamUrl: req.EngineParamsUrl,
		},
		UserData:        req.UserData,
		Trace:           req.Trace,
		RequestResource: RequireResource,
		TaskType:        models.SimpleTaskType,
		EngineEntrance:  req.EngineEntrance,
	}
	//如果taskId为空，则使用jobId
	if task.TaskId == "" {
		task.TaskId = task.JobId
	}

	//ctx, rpcSpan := tasker_tracing.InjectRPCTrace(ctx, md)
	rpcTracingID := tasker_tracing.RPCTraceID(ctx)
	ctx, span := s.taskTracer.StartWithSpecTraceID(ctx, "/InvokeTask", req.TaskId, nil)
	s.log.WithContext(ctx).Infof("StartTask SpanContext, taskID:%s, rcpTraceID:%s, taskTraceID:%s", req.TaskId, rpcTracingID, span.SpanContext().TraceID())

	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req.TaskId, req)
	defer func() {
		s.logApiOut(ctx, f, req.TaskId, start, req, rst, err)
	}()
	defer func() {
		s.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTaskAndExtendInformation(ctx, "Server.StartTask", task, nil)
		}, err)
	}()

	rst, err = s.simpleTaskBiz.Invoke(ctx, task)
	if err != nil {
		s.log.WithContext(ctx).Errorf("InvokeTask fail, taskId:%v, jobId:%v, quota:%v,err:%v",
			req.TaskId, req.JobId, req.Quota, err)
		return nil, err
	}
	s.log.WithContext(ctx).Infof("InvokeTask success, taskId:%v, jobId:%v, data:%+v,dataUrl:%+v",
		req.TaskId, req.JobId, rst.Data, rst.DataUrl)

	resp := &pb.InvokeTaskReply{
		Result: defaultSuccessReplyResult,
	}
	//if rst != nil {
	//	jsonString, err := json.Marshal(rst)
	//	if err != nil {
	//		s.log.WithContext(ctx).Errorf("TaskService InvokeTask fail in Marshal, taskId:%v, jobId:%v, quota:%v,err:%v",
	//			req.TaskId, req.JobId, req.Quota, err)
	//		return nil, err
	//	}
	//	resp.Job = string(jsonString)
	//}
	if rst != nil {
		resp.Data = string(rst.Data)
	} else if rst != nil && rst.DataUrl != "" {
		resp.DataUrl = rst.DataUrl
	}
	return resp, nil
}

func (s *TaskService) StartTask(ctx context.Context, req *pb.StartTaskRequest) (*pb.StartTaskReply, error) {
	ctx = utils.SetRequestId(ctx)

	var (
		err    error
		task   *models.Task
		taskId string
	)

	md := custom_types.Metadata{}
	if req.Metadata != nil {
		md = req.Metadata
	}
	if req.Tag == "" {
		req.Tag = models.DefaultTaskTag
	}
	if req.EngineModel == "" {
		req.EngineModel = models.DefaultTaskEngineModel
	}
	if req.TaskId != "" {
		taskId = req.TaskId
	} else {
		taskId = req.JobId
	}
	//todo 并发转码：根据ScheduleParams判断任务是否为dag类型
	taskType, err := models.TaskTypeByScheduleParams(req.JobId, req.TaskId, req.ScheduleParams)
	if err != nil {
		log.Errorf("StartTask fail in IsParallelTasByScheduleParams, taskId:%v, jobId:%v, scheduleParams:%v,err:%v",
			req.TaskId, req.JobId, req.ScheduleParams, err)
		return nil, err

	}
	//ctx, rpcSpan := tasker_tracing.InjectRPCTrace(ctx, md)
	rpcTracingID := tasker_tracing.RPCTraceID(ctx)
	ctx, span := s.taskTracer.StartWithSpecTraceID(ctx, "/StartTask", taskId, md)

	ctx = utils.SetTaskId(ctx, req.TaskId)
	var resp *pb.StartTaskReply
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req.TaskId, req)
	defer func() {
		s.logApiOut(ctx, f, req.TaskId, start, req, resp, err)
	}()
	defer func() {
		s.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTaskAndExtendInformation(ctx, "Server.StartTask", task, nil)
		}, err)
	}()

	if req.JobId == "" && req.TaskId == "" {
		req.TaskId = models.NewTaskIDWithTraceID(taskType, tasker_tracing.TraceID(ctx))
		req.JobId = req.TaskId
	} else {
		if req.JobId == "" {
			req.JobId = req.TaskId
		} else if req.TaskId == "" {
			req.TaskId = req.JobId
		}
	}
	//把taskId放入context中
	//ctx = utils.SetJobId(ctx, req.JobId)
	ctx = utils.SetTaskId(ctx, req.TaskId)
	ctx = utils.SetTaskProduct(ctx, req.Product)
	s.log.WithContext(ctx).Infof("StartTask SpanContext, taskID:%s, rcpTraceID:%s, taskTraceID:%s", req.TaskId, rpcTracingID, span.SpanContext().TraceID())
	task = s.toRealtimeBizTask(req, md, taskType)

	s.log.WithContext(ctx).Infof("StartTask, taskId:%v, jobId:%v, quota:%v,tasker:%+v",
		req.TaskId, req.JobId, req.Quota, task)
	if recordHlsTask(task) {
		err = s.recordHlsTaskBiz.StartTask(s.taskTracer.UnsetTraceId(ctx), task)
	} else {
		err = s.streamTaskBiz.StartTask(s.taskTracer.UnsetTraceId(ctx), task)
		if err == nil {
			if task.IsTranscodeJob() {
				s.liveTransCodeTaskLoadAttach(ctx, taskId, lc.AttachmentTypePIC)
				s.liveTransCodeTaskLoadAttach(ctx, taskId, lc.AttachmentTypeTEXT)
			}
		}
	}
	if err != nil {
		s.log.WithContext(ctx).Errorf("StartTask fail, taskId:%v, jobId:%v, quota:%v,err:%v",
			req.TaskId, req.JobId, req.Quota, err)
		return nil, err
	}

	resp = &pb.StartTaskReply{
		Result: defaultSuccessReplyResult,
	}
	if task.Result != nil {
		if metadata, ok := task.Result.Data.(map[string]string); ok {
			resp.Result.Metadata = metadata
		}
	}
	return resp, nil
}

func (s *TaskService) liveTransCodeTaskLoadAttach(ctx context.Context, taskId string, attachType string) {
	//reload pic and text
	taskAtts := s.streamTaskBiz.GetTransCodeAttachments(ctx, taskId, attachType)
	if taskAtts != nil && len(taskAtts) > 0 {
		taskSurfaceAtts := make([]map[string]interface{}, 0)
		for _, taskAtt := range taskAtts {
			surfaceObject := make(map[string]interface{})
			if err := json.Unmarshal([]byte(taskAtt.Params), &surfaceObject); err == nil {
				taskSurfaceAtts = append(taskSurfaceAtts, surfaceObject)
			} else {
				s.log.WithContext(ctx).Errorf("StartTask fail in Unmarshal task Attachment, taskId:%v, jobId:%v,err:%v",
					taskId, taskId, err)
			}
		}
		if attachType == lc.AttachmentTypePIC {
			//no block
			_ = s.streamTaskBiz.CommandTask(ctx, taskId, biz.JsonToString(taskSurfaceAtts), "", lc.PIC_ADD_COMMAND)
		} else {
			_ = s.streamTaskBiz.CommandTask(ctx, taskId, biz.JsonToString(taskSurfaceAtts), "", lc.TEXT_ADD_COMMAND)
		}
	}
}

func (s *TaskService) checkStartParams(ctx context.Context, req *pb.StartTaskRequest) error {

	if req.Product == "" {
		return common.ErrRequestInvalid
	}
	if req.EngineModel == "" {
		req.EngineModel = models.DefaultTaskEngineModel
	}
	if req.Tag == "" {
		req.Tag = models.DefaultTaskTag
	}
	if req.JobId == "" && req.TaskId == "" {
		req.TaskId = models.NewTaskIDWithTraceID(v1.TaskType_Stream, tasker_tracing.TraceID(ctx))
		req.JobId = req.TaskId
	} else {
		if req.JobId == "" {
			req.JobId = req.TaskId
		} else if req.TaskId == "" {
			req.TaskId = req.JobId
		}
	}
	return nil
}

func recordHlsTask(existedTask *models.Task) bool {
	for _, engineModel := range models.PackagerTypeModels {
		if existedTask.EngineModel == engineModel {
			return true
		}
	}
	return false
}

func (s *TaskService) StartTaskLegacy(ctx context.Context, req *pb.StartTaskRequest) (*pb.StartTaskLegacyReply, error) {
	ctx = utils.SetRequestId(ctx)
	reply, err := s.StartTask(ctx, req)
	if err != nil {
		return nil, err
	}

	var code, message string

	if reply != nil && reply.Result != nil {
		code, message = reply.Result.Reason, reply.Result.Message
	}

	return &pb.StartTaskLegacyReply{
		Code:    code,
		Message: message,
	}, err
}

func (s *TaskService) toRealtimeBizTask(req *pb.StartTaskRequest, md custom_types.Metadata, taskType v1.TaskType) *models.Task {
	quota := req.Quota
	if len(req.QuotaSet) > 0 {
		quota = req.QuotaSet
	}
	RequireResource := &models.RequestResource{
		Quota:    quota,
		Affinity: models.ToBizTaskAffinity(req.Affinity),
	}
	trace := ""
	task := &models.Task{
		Product:        req.Product,
		TaskId:         req.TaskId,
		JobId:          req.JobId,
		UserId:         req.UserId,
		BizStatus:      models.TaskStateStopped,
		InternalStatus: models.TaskStateStopped,
		Worker:         nil,
		UserData:       req.UserData,
		Param: &models.TaskEngineParam{
			Param:    req.EngineParams,
			ParamUrl: req.EngineParamsUrl,
		},
		EngineModel:     req.EngineModel,
		Tag:             req.Tag,
		Trace:           trace,
		RequestResource: RequireResource,
		Result:          nil,
		TaskType:        models.TaskType(taskType),
		MigrateConfig:   nil,
		MigrateTimes:    0,
		Notify:          req.Notify,
		Dependencies:    nil,
		LastModified:    time.Now(),
		GmtCreate:       time.Now(),
		Metadata:        md,
		ScheduleParams:  req.ScheduleParams,
	}
	if req.Domain != "" {
		task.Domain = req.Domain
	}
	return task
}

func (s *TaskService) StopTask(ctx context.Context, req *pb.StopTaskRequest) (*pb.StopTaskReply, error) {
	ctx = utils.SetRequestId(ctx)
	var (
		err error
	)
	var resp *pb.StopTaskReply
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req.TaskId, req)
	defer func() {
		s.logApiOut(ctx, f, req.TaskId, start, req, resp, err)
	}()
	taskType := models.GetTaskTypeFromID(req.TaskId)

	ctx = utils.SetTaskId(ctx, req.TaskId)
	switch taskType {
	case v1.TaskType_Async:
		err = s.batchTaskBiz.CancelTask(ctx, req.TaskId)
	case v1.TaskType_NonQueueAsync:
		err = s.batchTaskBiz.CancelTask(ctx, req.TaskId)
	case v1.TaskType_DagAsync:
		//dag主任务
		err = s.streamTaskBiz.StopTask(ctx, req.TaskId, req.Extend)
	default:
		//流式任务 dag子任务
		err = s.streamTaskBiz.StopTask(ctx, req.TaskId, req.Extend)
	}

	if err != nil {
		return nil, err
	}

	resp = &pb.StopTaskReply{
		Result: defaultSuccessReplyResult,
	}
	return resp, nil
}

func (s *TaskService) StopTaskLegacy(ctx context.Context, req *pb.StopTaskRequest) (*pb.StopTaskLegacyReply, error) {
	ctx = utils.SetRequestId(ctx)
	reply, err := s.StopTask(ctx, req)
	if err != nil {
		return nil, err
	}

	var reason, message string
	code := http.StatusOK
	if reply != nil && reply.Result != nil {
		reason, message = reply.Result.Reason, reply.Result.Message
		code = int(reply.Result.Code)
	}

	return &pb.StopTaskLegacyReply{
		Result: &v1.CommonReplyResult{
			Code:    uint32(code),
			Reason:  reason,
			Message: message},
	}, err
}

func (s *TaskService) SubmitTask(ctx context.Context, req *pb.SubmitTaskRequest) (*pb.SubmitTaskReply, error) {
	ctx = utils.SetRequestId(ctx)
	var (
		err    error
		task   *models.Task
		taskId string
	)

	if req.Tag == "" {
		req.Tag = models.DefaultTaskTag
	}
	if req.EngineModel == "" {
		req.EngineModel = models.DefaultTaskEngineModel
	}
	if req.TaskId != "" {
		taskId = req.TaskId
	} else {
		taskId = req.JobId
	}

	var resp *pb.SubmitTaskReply
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req.TaskId, req)
	defer func() {
		s.logApiOut(ctx, f, req.TaskId, start, req, resp, err)
	}()
	md := custom_types.Metadata{}

	rpcTracingID := tasker_tracing.RPCTraceID(ctx)

	ctx, span := s.taskTracer.StartWithSpecTraceID(ctx, "/SubmitTask", taskId, md)
	defer func() {
		s.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTaskAndExtendInformation(ctx, "server.SubmitTask", task, nil)
		}, err)
	}()

	if req.JobId == "" && req.TaskId == "" {
		req.TaskId = models.NewTaskIDWithTraceID(v1.TaskType_Async, tasker_tracing.TraceID(ctx))
		req.JobId = req.TaskId
	} else if req.JobId == "" {
		req.JobId = req.TaskId
	} else if req.TaskId == "" {
		req.TaskId = req.JobId
	}
	ctx = utils.SetTaskId(ctx, req.TaskId)
	ctx = utils.SetTaskProduct(ctx, req.Product)
	ctx = utils.SetTaskEngine(ctx, req.Product, req.EngineModel, req.Tag)
	s.log.WithContext(ctx).Infof("SubmitTask SpanContext,taskID:%s, rcpTraceID:%s, taskTraceID:%s", req.TaskId, rpcTracingID, span.SpanContext().TraceID())

	// TODO: 增加退避逻辑
	if req.Quota == nil || len(req.Quota) == 0 {
		tmpQ, err := s.quota.GetAnalysisQuota(req.JobId, req.Product, req.EngineModel, req.Tag, req.EngineParams, req.UserId, req.ScheduleParams)
		if err != nil {
			s.log.WithContext(ctx).Errorf("SubmitTask GetAnalysisQuota fail, taskID:%s, err:%v", req.TaskId, err)
			return nil, err
		}
		if len(req.Quota) == 0 {
			req.Quota = tmpQ
		}
		s.log.WithContext(ctx).Infof("SubmitTask GetAnalysisQuota, taskID:%s, default quota:%+v, QuotaService Quota:%+v", req.TaskId, req.Quota, tmpQ)

	}

	task = s.toAsyncBizTask(req, md)

	switch task.TaskType {
	case models.AsyncTaskType:
		task, err = s.batchTaskBiz.SubmitTask(ctx, task)
	case models.NonQueueAsyncTaskType:
		task, err = s.batchTaskBiz.SubmitNonQueueTask(ctx, task)
	//case models.DagAsyncTaskType:
	//	_, err = s.batchTaskBiz.SubmitNonQueueTask(ctx, task)
	default:
		task, err = s.batchTaskBiz.SubmitTask(ctx, task)

	}

	if err != nil {
		return nil, err
	}

	resp = &pb.SubmitTaskReply{
		Result: &v1.CommonReplyResult{Code: defaultSuccessReplyResult.Code, Reason: defaultSuccessReplyResult.Reason},
	}
	if task != nil && task.GetTaskMixedConfig() != "" {
		if resp != nil && resp.Result != nil && resp.Result.Metadata == nil {
			resp.Result.Metadata = map[string]string{
				"isMixed":    "true",
				"mixConfigs": task.GetTaskMixedConfig(),
			}
		}

	}
	return resp, nil
}

func (s *TaskService) toAsyncBizTask(req *pb.SubmitTaskRequest, md custom_types.Metadata) *models.Task {
	requestResource := &models.RequestResource{
		Quota:    req.Quota,
		Affinity: models.ToBizTaskAffinity(req.Affinity),
	}

	var dependencies = make([]models.Dependency, 0)
	if req.Dependency != nil {
		for i := range req.Dependency {
			dependencies = append(dependencies, models.Dependency{
				TaskId: req.Dependency[i].TaskId,
				Fields: req.Dependency[i].Fields,
			})
		}
	}
	//任务类型判断
	taskType := models.AsyncTaskType
	if req.TaskType == v1.TaskType_NonQueueAsync {
		taskType = models.NonQueueAsyncTaskType
	}
	//if req.JobId != req.TaskId {
	//	taskType = models.DagAsyncTaskType
	//}

	task := &models.Task{
		Product:        req.Product,
		TaskId:         req.TaskId,
		JobId:          req.JobId,
		UserId:         req.UserId,
		PipelineId:     req.PipelineId,
		BizStatus:      models.TaskStateActive,
		InternalStatus: models.TaskStatePending,
		Worker:         nil,
		UserData:       req.UserData,
		Param: &models.TaskEngineParam{
			Param:    req.EngineParams,
			ParamUrl: req.EngineParamsUrl,
		},
		EngineModel:     req.EngineModel,
		Tag:             req.Tag,
		Trace:           req.Trace,
		RequestResource: requestResource,
		Result:          nil,
		TaskType:        taskType,
		MigrateConfig:   req.MigrateConfig,
		MigrateTimes:    0,
		Notify:          req.Notify,
		Dependencies:    dependencies,
		LastModified:    time.Now(),
		GmtCreate:       time.Now(),
		Metadata:        req.Metadata,
	}

	if task.Metadata == nil {
		task.Metadata = md
	} else {
		//合并metadata
		for k, v := range md {
			task.Metadata[k] = v
		}
	}

	return task
}

func (s *TaskService) CancelTask(ctx context.Context, req *pb.CancelTaskRequest) (*pb.CancelTaskReply, error) {
	ctx = utils.SetRequestId(ctx)
	ctx = utils.SetTaskId(ctx, req.TaskId)
	err := s.batchTaskBiz.CancelTask(ctx, req.TaskId)
	if err != nil {
		return nil, err
	}
	var resp *pb.CancelTaskReply
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req.TaskId, req)
	defer func() {
		s.logApiOut(ctx, f, req.TaskId, start, req, resp, err)
	}()

	resp = &pb.CancelTaskReply{
		Result: defaultSuccessReplyResult,
	}
	return resp, nil
}

func (s *TaskService) UpdateTask(ctx context.Context, req *pb.UpdateTaskRequest) (*pb.UpdateTaskReply, error) {
	ctx = utils.SetRequestId(ctx)
	ctx = utils.SetTaskId(ctx, req.TaskId)
	var (
		err error
	)
	var resp *pb.UpdateTaskReply
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req.TaskId, req)
	defer func() {
		s.logApiOut(ctx, f, req.TaskId, start, req, resp, err)
	}()
	taskType := models.GetTaskTypeFromID(req.TaskId)
	updateEngine := req.TaskUpdateType != v1.TaskUpdateType_IncrementalTaskUpdateType
	switch taskType {
	case v1.TaskType_Async:
		return nil, fmt.Errorf("unsupported tasker type for UpdateTask")
	case v1.TaskType_DagAsync:
		// todo dag任务更新支持
		err = s.streamTaskBiz.UpdateTask(ctx, req.TaskId, req.EngineParams, "", updateEngine)
	default:
		err = s.streamTaskBiz.UpdateTask(ctx, req.TaskId, req.EngineParams, "", updateEngine)
	}

	if err != nil {
		return nil, err
	}

	resp = &pb.UpdateTaskReply{
		Result: defaultSuccessReplyResult,
	}
	return resp, nil
}

func (s *TaskService) UpdateTaskLegacy(ctx context.Context, req *pb.UpdateTaskRequest) (*pb.UpdateTaskLegacyReply, error) {
	reply, err := s.UpdateTask(ctx, req)
	var reason, message string

	code := http.StatusOK
	if reply != nil && reply.Result != nil {
		reason, message = reply.Result.Reason, reply.Result.Message
		code = int(reply.Result.Code)
	}
	return &pb.UpdateTaskLegacyReply{
		Result: &v1.CommonReplyResult{
			Code:    uint32(code),
			Reason:  reason,
			Message: message},
	}, err
}

func (s *TaskService) UpdateTaskStatus(ctx context.Context, req *pb.UpdateTaskStatusRequest) (*pb.UpdateTaskStatusReply, error) {
	ctx = utils.SetRequestId(ctx)
	ctx = utils.SetTaskId(ctx, req.TaskId)
	var (
		err error
	)

	taskType := models.GetTaskTypeFromID(req.TaskId)
	switch taskType {
	case v1.TaskType_Async:
		return nil, fmt.Errorf("unsupported tasker type for UpdateTask")
	case v1.TaskType_DagAsync:
		// todo 还得支持
		return nil, fmt.Errorf("unsupported tasker type for UpdateTask")
	default:
		err = s.streamTaskBiz.UpdateTaskStatus(ctx, req.TaskId, int(req.BizStatus), int(req.InternalStatus))
	}

	if err != nil {
		return nil, err
	}
	return &pb.UpdateTaskStatusReply{
		Result: defaultSuccessReplyResult,
	}, nil
}

func (s *TaskService) CommandTask(ctx context.Context, req *pb.CommandTaskRequest) (*pb.CommandTaskReply, error) {
	ctx = utils.SetRequestId(ctx)
	ctx = utils.SetTaskId(ctx, req.TaskId)
	var (
		err  error
		resp *pb.CommandTaskReply
	)
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req.TaskId, req)
	defer func() {
		s.logApiOut(ctx, f, req.TaskId, start, req, resp, err)
	}()

	err = s.streamTaskBiz.CommandTask(ctx, req.TaskId, req.EngineParams, "", req.CommandType)
	if err != nil {
		return nil, err
	}

	return &pb.CommandTaskReply{
		Result: defaultSuccessReplyResult,
	}, nil
}

func (s *TaskService) GetTask(ctx context.Context, req *pb.GetTaskRequest) (*pb.GetTaskReply, error) {
	ctx = utils.SetRequestId(ctx)
	ctx = utils.SetTaskId(ctx, req.TaskId)
	var (
		err  error
		task *models.Task
		resp *pb.GetTaskReply
	)
	taskType := models.GetTaskTypeFromProductIDOrTaskID(req.Product, req.TaskId)

	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req.TaskId, req)
	defer func() {
		s.logApiOut(ctx, f, req.TaskId, start, req, resp, err)
	}()

	switch taskType {
	case v1.TaskType_Async:
		task, err = s.batchTaskBiz.GetTask(ctx, req.TaskId)
	case v1.TaskType_DagAsync:
		task, err = s.batchTaskBiz.GetTask(ctx, req.TaskId)
	case v1.TaskType_Stream:
		task, err = s.streamTaskBiz.GetTask(ctx, req.TaskId)
	default:
		s.log.WithContext(ctx).Warnf("unknown taskType:[%d] for taskId:%s", taskType, req.TaskId)
		task, err = s.streamTaskBiz.GetTask(ctx, req.TaskId)
		if err != nil && errors.Is(err, common.ErrTaskNotFoundError) {
			task, err = s.batchTaskBiz.GetTask(ctx, req.TaskId)
			if err != nil {
				return nil, err
			}
		}
	}
	if err != nil {
		return nil, err
	}

	if req.TaskId == "" {
		req.TaskId = req.JobId
	}
	if req.JobId == "" {
		req.JobId = req.TaskId
	}

	taskDetail, err := s.BizTaskToDetail(task)
	if err != nil {
		return nil, err
	}

	resp = &pb.GetTaskReply{
		Result: defaultSuccessReplyResult,
		Data: &pb.GetTaskReply_Data{
			Task: taskDetail,
		},
	}
	return resp, nil
}

func (s *TaskService) GetRunningTasks(ctx context.Context, taskType v1.TaskType, engineModels []string) ([]*models.Task, error) {
	ctx = utils.SetRequestId(ctx)
	var (
		err   error
		tasks []*models.Task
	)

	switch taskType {
	case v1.TaskType_Async:
		tasks, err = s.batchTaskBiz.GetRunningTasks(ctx, engineModels)
	case v1.TaskType_DagAsync:
		tasks, err = s.batchTaskBiz.GetRunningTasks(ctx, engineModels)
	case v1.TaskType_Stream:
		tasks, err = s.streamTaskBiz.GetRunningTasks(ctx, engineModels)
	default:
		s.log.WithContext(ctx).Warnf("unknown taskType:[%d] for query running tasks", taskType)
	}
	if err != nil {
		s.log.WithContext(ctx).Warnf("query running tasks error, taskType:[%d], err:%v", taskType, err)
		return nil, err
	}
	return tasks, nil
}

func (s *TaskService) BizTaskToDetail(task *models.Task) (*pb.TaskDetail, error) {
	taskDetail := &pb.TaskDetail{
		Product:        task.Product,
		TaskId:         task.TaskId,
		JobId:          task.JobId,
		BizStatus:      int32(task.BizStatus),
		InternalStatus: int32(task.InternalStatus),
		UserData:       task.UserData,
		EngineModel:    task.EngineModel,
		Tag:            task.Tag,
		Trace:          task.Trace,
		TaskType:       int32(task.TaskType),
		MigrateTimes:   task.MigrateTimes,
		Notify:         task.Notify,
		UserId:         task.UserId,
	}
	if task.Worker != nil {
		taskDetail.Worker = &pb.TaskWorkerDetail{
			WorkerId:    task.Worker.Id,
			Ip:          task.Worker.Ip,
			Port:        task.Worker.Port,
			ConnectMode: task.Worker.ConnectMode,
		}
	}
	if task.Param != nil {
		taskDetail.EngineParams = &pb.TaskEngineParamDetail{
			Param:    task.Param.Param,
			ParamUrl: task.Param.ParamUrl,
		}
	}
	if task.Result != nil {
		taskDetail.Result = &pb.TaskResultDetail{
			DataUrl:  task.Result.DataUrl,
			Code:     task.Result.Code,
			Reason:   task.Result.Reason,
			Message:  task.Result.Message,
			Duration: task.Result.Duration,
		}
		if task.Result.Data != nil {
			// 修复：处理Data可能是map或其他类型的情况，避免直接类型断言导致panic
			switch v := task.Result.Data.(type) {
			case string:
				taskDetail.Result.Data = v
			default:
				// 对于非字符串类型，转换为JSON字符串
				jsonData, err := json.Marshal(v)
				if err != nil {
					log.Errorf("BizTaskToDetail fail in jsonMarshal, taskId:%s,error:%+v,task:%+v", task.TaskId, err, task)
					// 出错时设置为空字符串，避免丢失整个任务详情
					taskDetail.Result.Data = ""
				} else {
					taskDetail.Result.Data = string(jsonData)
				}
			}
		}
	}
	if task.MigrateConfig != nil {
		taskDetail.MigrateConfig = &pb.MigrateConfigDetail{
			MaxRuntime: task.MigrateConfig.MaxRuntime,
			UnMigrate:  task.MigrateConfig.UnMigrate,
		}
	}
	if task.RequestResource != nil {
		taskDetail.RequestResource = &pb.RequestResourceDetail{
			Quota:    task.RequestResource.Quota,
			Affinity: nil,
		}
	}

	taskDetail.GmtCreate = task.GmtCreate.Format(time.DateTime)
	taskDetail.GmtModified = task.LastModified.Format(time.DateTime)
	return taskDetail, nil
}

// 批量转换
func (s *TaskService) BizTasksToDetail(tasks []*models.Task) ([]*pb.TaskDetail, error) {
	var taskDetails []*pb.TaskDetail
	for _, task := range tasks {
		taskDetail, err := s.BizTaskToDetail(task)
		if err != nil {
			log.Errorf("BizTaskToDetail fail, ,taskId:%s,error:%+v,task:%+v", task.TaskId, err, task)
			return nil, err
		}
		taskDetails = append(taskDetails, taskDetail)
	}
	return taskDetails, nil
}

func (s *TaskService) ListTask(ctx context.Context, req *pb.ListTaskRequest) (*pb.ListTaskReply, error) {
	rst := &pb.ListTaskReply{
		Result: defaultSuccessReplyResult,
		Data: &pb.ListTaskReply_Data{
			Tasks: make([]*pb.TaskDetail, 0),
		},
	}
	for _, jobId := range req.JobIds {
		getRst, err := s.GetTask(ctx, &pb.GetTaskRequest{
			RequestId: utils.GetRequestIdFromContext(ctx),
			JobId:     jobId,
			TaskId:    "",
			Product:   "",
			JobIds:    nil,
			UserId:    "",
			Type:      "",
		})
		if err != nil {
			return nil, err
		}
		if getRst.Result.Code != http.StatusOK {
			rst.Result = getRst.Result
			return rst, nil
		}

		rst.Data.Tasks = append(rst.Data.Tasks, getRst.Data.Task)
	}

	return rst, nil
}

func (s *TaskService) ListRunningTaskIds(ctx context.Context, req *pb.ListRunningTaskIdsRequest) (*pb.ListRunningTaskIdsReply, error) {
	ctx = utils.SetCustomRequestId(ctx, req.RequestId)
	var (
		err     error
		tasks   []*models.Task
		taskIds []string
		resp    *pb.ListRunningTaskIdsReply
	)

	taskInfo := &models.Task{
		Product:        req.Product,
		UserId:         req.UserId,
		EngineModel:    req.EngineModel,
		Tag:            req.Tag,
		PipelineId:     req.PipelineId,
		Notify:         req.Notify,
		Trace:          req.Trace,
		BizStatus:      models.TaskStatus(req.BizStatus),
		InternalStatus: models.TaskStatus(req.InternalStatus),
	}
	if req.WorkerId != "" || req.WorkerIp != "" {
		taskInfo.Worker = &models.Worker{
			Id: req.WorkerId,
			Ip: req.WorkerIp,
		}
	}
	//如果任务状态全部为0，默认查运行中任务
	if req.BizStatus == 0 && req.InternalStatus == 0 {
		taskInfo.BizStatus = models.TaskStateActive
		taskInfo.InternalStatus = models.TaskStateActive
	}
	var taskType v1.TaskType
	if req.TaskType == 0 {
		taskType = models.GetTaskTypeFromProductIDOrTaskID(req.Product, "")
	} else {
		taskType = v1.TaskType(req.TaskType)
		//修复传入taskType不进行查询的问题
		taskInfo.TaskType = models.TaskType(taskType)
	}
	switch taskType {
	case v1.TaskType_Async:
		tasks, err = s.batchTaskBiz.GetRunningTaskIds(ctx, taskInfo)
	case v1.TaskType_DagAsync:
		tasks, err = s.batchTaskBiz.GetRunningTaskIds(ctx, taskInfo)
	case v1.TaskType_Stream:
		tasks, err = s.streamTaskBiz.GetRunningTaskIds(ctx, taskInfo)
	default:
		//默认查询流式任务
		s.log.WithContext(ctx).Warnf("unknown taskType:[%d] for ListRunningTaskIdsRequest:%v", taskType, req)
		tasks, err = s.streamTaskBiz.GetRunningTaskIds(ctx, taskInfo)
	}
	if err != nil {
		return nil, err
	}

	for _, task := range tasks {
		taskIds = append(taskIds, task.TaskId)
	}

	if len(taskIds) > 0 {
		log.Infof("ListRunningTaskIds success ,req:%v, task len:%d", req, len(taskIds))
	} else {
		log.Infof("ListRunningTaskIds success ,req:%v", req)
	}

	resp = &pb.ListRunningTaskIdsReply{
		Result: defaultSuccessReplyResult,
		Data: &pb.ListRunningTaskIdsReply_Data{
			TaskIds: taskIds,
		},
	}
	if req.NeedTaskDetail {
		taskDetails, err := s.BizTasksToDetail(tasks)
		if err != nil {
			log.Errorf("ListRunningTaskIds fail in BizTasksToDetail, req:%v, error:%v", req, err)
			return nil, err
		}
		resp.Data.Tasks = taskDetails
	}

	return resp, nil
}

func (s *TaskService) ListRunningTaskIdsSimple(ctx context.Context, req *pb.ListRunningTaskIdsRequest) (*pb.ListRunningTaskIdsReply, error) {
	ctx = utils.SetCustomRequestId(ctx, req.RequestId)
	var (
		err     error
		tasks   []*models.Task
		taskIds []string
		resp    *pb.ListRunningTaskIdsReply
	)

	taskInfo := &models.Task{
		Product:     req.Product,
		EngineModel: req.EngineModel,
		BizStatus:   models.TaskStatus(req.BizStatus),
	}
	//如果任务状态全部为0，默认查运行中任务
	if req.BizStatus == 0 && req.InternalStatus == 0 {
		taskInfo.BizStatus = models.TaskStateActive
		taskInfo.InternalStatus = models.TaskStateActive
	}
	var taskType v1.TaskType
	if req.TaskType == 0 {
		taskType = models.GetTaskTypeFromProductIDOrTaskID(req.Product, "")
	} else {
		taskType = v1.TaskType(req.TaskType)
	}
	switch taskType {
	case v1.TaskType_Async:
		tasks, err = s.batchTaskBiz.GetRunningSimpleTasks(ctx, taskInfo)
	case v1.TaskType_DagAsync:
		tasks, err = s.batchTaskBiz.GetRunningSimpleTasks(ctx, taskInfo)
	case v1.TaskType_Stream:
		tasks, err = s.streamTaskBiz.GetRunningSimpleTasks(ctx, taskInfo)
	default:
		//默认查询流式任务
		s.log.WithContext(ctx).Warnf("unknown taskType:[%d] for ListRunningTaskSimpleRequest:%v", taskType, req)
		tasks, err = s.streamTaskBiz.GetRunningSimpleTasks(ctx, taskInfo)
	}
	if err != nil {
		return nil, err
	}

	for _, task := range tasks {
		taskIds = append(taskIds, task.TaskId)
	}

	if len(taskIds) > 0 {
		log.Infof("ListRunningTaskSimple success ,req:%v, task len:%d", req, len(taskIds))
	} else {
		log.Infof("ListRunningTaskSimple success ,req:%v", req)
	}

	resp = &pb.ListRunningTaskIdsReply{
		Result: defaultSuccessReplyResult,
		Data: &pb.ListRunningTaskIdsReply_Data{
			TaskIds: taskIds,
		},
	}
	if req.NeedTaskDetail {
		taskDetails, err := s.BizTasksToDetail(tasks)
		if err != nil {
			log.Errorf("ListRunningTaskIds fail in BizTasksToDetail, req:%v, error:%v", req, err)
			return nil, err
		}
		resp.Data.Tasks = taskDetails
	}

	return resp, nil
}

/*
MigrateTask
任务迁移接口
分为task维度和worker维度
并行执行stream/batch任务迁移，全部成功，则返回迁移成功
*/
func (s *TaskService) MigrateTask(ctx context.Context, req *pb.MigrateTaskRequest) (*pb.MigrateTaskReply, error) {
	// 设置请求ID
	ctx = utils.SetRequestId(ctx)

	var (
		successReplyResult = &v1.CommonReplyResult{Code: http.StatusOK, Reason: "Success"}
	)

	// 使用通道来接收结果
	streamResultCh := make(chan struct {
		taskIds []string
		err     error
	}, 1)
	batchResultCh := make(chan struct {
		taskIds []string
		err     error
	}, 1)

	// 启动 Goroutine 并行执行
	go func() {
		var taskIds []string
		var err error
		taskIds, err = s.streamTaskBiz.CommonMigrateTask(ctx, req.TaskIds, req.WorkIds, req.WorkIps, req.TryStop)
		if err != nil {
			s.log.WithContext(ctx).Errorf("MigrateTask fail in streamTaskBiz/CommonMigrateTask, taskIds:%v, error:%v", req.TaskIds, err)
		}
		streamResultCh <- struct {
			taskIds []string
			err     error
		}{taskIds, err}
	}()

	go func() {
		var taskIds []string
		var err error
		taskIds, err = s.batchTaskBiz.CommonMigrateTask(ctx, req.TaskIds, req.WorkIds, req.WorkIps, req.TryStop)
		if err != nil {
			s.log.WithContext(ctx).Errorf("MigrateTask fail in batchTaskBiz/CommonMigrateTask, taskIds:%v, error:%v", req.TaskIds, err)
		}
		batchResultCh <- struct {
			taskIds []string
			err     error
		}{taskIds, err}
	}()

	// 等待两个 Goroutine 完成
	streamResult := <-streamResultCh
	batchResult := <-batchResultCh

	// 检查是否有错误
	if streamResult.err != nil {
		return nil, streamResult.err
	}
	if batchResult.err != nil {
		return nil, batchResult.err
	}

	// 合并迁移成功的任务ID
	migrateSuccessTaskIds := append(streamResult.taskIds, batchResult.taskIds...)

	// 使用 strings.Join 将字符串切片转换为逗号分隔的字符串
	if len(migrateSuccessTaskIds) > 0 {
		taskIdsString := strings.Join(migrateSuccessTaskIds, ",")
		successReplyResult.Message = fmt.Sprintf("taskIds:%s", taskIdsString)
	}

	s.log.WithContext(ctx).Infof("MigrateTask success, req:%+v, migrateSuccessTaskIds:%v", req, migrateSuccessTaskIds)
	return &pb.MigrateTaskReply{
		Result: successReplyResult,
	}, nil
}

//func (s *TaskService) MigrateTask(ctx context.Context, req *pb.MigrateTaskRequest) (*pb.MigrateTaskReply, error) {
//	// TODO 这里对taskIds 的数量限制下，然外面分批传入
//	//todo 这里如果同时对stream任务和batch任务进行迁移，如果两表出现相同id会导致正常任务迁移
//	//优先按照taskIds维度进行迁移
//	ctx = utils.SetRequestId(ctx)
//	var (
//		migrateSuccessTaskIds []string
//		err                   error
//		successReplyResult    = &v1.CommonReplyResult{Code: http.StatusOK, Reason: "Success"}
//	)
//	if req.TaskIds != nil && len(req.TaskIds) > 0 {
//		const batchSize = 1000
//		if len(req.TaskIds) > batchSize {
//			return nil, common.ErrRequestInvalid
//		}
//
//		err := s.streamTaskBiz.MigrateTask(ctx, req.TaskIds, req.TryStop)
//		if err != nil {
//			s.log.WithContext(ctx).Errorf("MigrateTask failed ,taskIds:%v, error:%v", req.TaskIds, err)
//			return nil, err
//		}
//	} else if (req.WorkIds != nil && len(req.WorkIds) > 0) || (req.WorkIps != nil && len(req.WorkIps) > 0) {
//		//按worker维度进行迁移
//		migrateSuccessTaskIds, err = s.streamTaskBiz.MigrateTaskByWorker(ctx, req.WorkIds, req.WorkIps, req.TryStop)
//		if err != nil {
//			s.log.WithContext(ctx).Errorf("MigrateTask failed in MigrateTaskByWorker,req:%+v, error:%v", req, err)
//			return nil, err
//		}
//	}
//	// 使用 strings.Join 将字符串切片转换为逗号分隔的字符串
//	if len(migrateSuccessTaskIds) > 0 {
//		taskIdsString := strings.Join(migrateSuccessTaskIds, ",")
//		successReplyResult.Message = fmt.Sprintf("taskIds:%s", taskIdsString)
//	}
//	return &pb.MigrateTaskReply{
//		Result: successReplyResult,
//	}, nil
//}

func (s *TaskService) CompleteTask(ctx context.Context, req *pb.CompleteTaskRequest) (*pb.CompleteTaskReply, error) {
	ctx = utils.SetRequestId(ctx)
	ctx = utils.SetTaskId(ctx, req.TaskId)
	var err error
	var resp *pb.CompleteTaskReply
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req.TaskId, req)
	defer func() {
		s.logApiOut(ctx, f, req.TaskId, start, req, resp, err)
	}()
	in := &tasker.CompleteRequest{
		JobID:    req.JobId,
		TaskID:   req.TaskId,
		Data:     req.Data,
		Duration: req.Duration,
		DataUrl:  req.DataUrl,
	}
	if len(req.Extends) != 0 {
		in.Extends = map[string]interface{}{}
		for k, v := range req.Extends {
			in.Extends[k] = v
		}
	}

	if req.Result != nil {
		in.Code = req.Result.Code
		in.Reason = req.Result.Reason
		in.Message = req.Result.Message
	}

	taskType := req.TaskType
	if taskType == v1.TaskType_Unknown {
		taskType = models.GetTaskTypeFromID(req.TaskId)
	}

	switch taskType {
	case v1.TaskType_Async:
		err = s.batchTaskBiz.CompleteTask(ctx, in)
	case v1.TaskType_NonQueueAsync:
		err = s.batchTaskBiz.CompleteTask(ctx, in)
	case v1.TaskType_DagAsync:
		err = s.batchTaskBiz.CompleteTask(ctx, in)
	case v1.TaskType_Stream:
		_, err = s.streamTaskBiz.CompleteTask(ctx, in)
	default:
		s.log.WithContext(ctx).Warnf("unknown taskType:[%d] for taskId:%s", taskType, req.TaskId)
		_, err = s.streamTaskBiz.CompleteTask(ctx, in)
		if err != nil && errors.Is(err, common.ErrTaskNotFoundError) {
			err = s.batchTaskBiz.CompleteTask(ctx, in)
			if err != nil {
				return nil, err
			}
		}
	}

	if err != nil {
		return nil, err
	}

	resp = &pb.CompleteTaskReply{
		Result: defaultSuccessReplyResult,
	}
	return resp, nil
}

func (s *TaskService) logApiIn(ctx context.Context, f string, taskId interface{}, req interface{}) time.Time {
	s.log.WithContext(ctx).Log(log.LevelInfo,
		//"task.id", taskId,
		"event", "api.in",
		"func", f,
		"request", req)

	return time.Now()
}

func (s *TaskService) logApiOut(ctx context.Context, f string, taskId interface{}, start time.Time, req interface{}, resp interface{}, err error) {
	s.log.WithContext(ctx).Log(log.LevelInfo,
		//"task.id", taskId,
		"event", "api.out",
		"latency", time.Since(start).Milliseconds(),
		"request", req,
		"response", resp,
		"func", f,
		"error", err)
}

func (s *TaskService) getFunctionName() string {
	pc := make([]uintptr, 1)
	runtime.Callers(2, pc)
	f := runtime.FuncForPC(pc[0])
	return "task" + f.Name()[strings.LastIndex(f.Name(), "."):]
}

func (s *TaskService) EditTransCodeTask(ctx context.Context, taskId string, surfaceObject []map[string]interface{}, attachType string) error {
	existAttachments := s.streamTaskBiz.GetTransCodeAttachments(ctx, taskId, attachType)
	newAttachments := make([]map[string]interface{}, 0)
	newAttachNames := make([]string, 0)
	oldAttachments := make([]map[string]interface{}, 0)
	oldAttachNames := make([]string, 0)
	for _, att := range surfaceObject {
		if _, ok := att["name"]; !ok {
			s.log.Errorf("EditTransCodeTask task %v picture name is empty, pictures:%v", taskId, surfaceObject)
			return common.ErrRequestInvalid
		}
		attachment := findAttachment(existAttachments, att["name"].(string))
		if attachment == nil {
			newAttachments = append(newAttachments, att)
			newAttachNames = append(newAttachNames, att["name"].(string))
		} else {
			oldAttachments = append(oldAttachments, att)
			oldAttachNames = append(oldAttachNames, att["name"].(string))
		}
	}
	if len(newAttachments)+len(oldAttachments) > 16 {
		s.log.Errorf("EditTransCodeTask task %v pictures length is too long, pictures:%v", taskId, surfaceObject)
		return common.ErrRequestInvalid
	}
	if len(newAttachments) > 0 {
		var err error
		if attachType == lc.AttachmentTypePIC {
			err = s.streamTaskBiz.CommandTask(ctx, taskId, biz.JsonToString(newAttachments), "", lc.PIC_ADD_COMMAND)
		} else {
			err = s.streamTaskBiz.CommandTask(ctx, taskId, biz.JsonToString(newAttachments), "", lc.TEXT_ADD_COMMAND)
		}
		if err != nil && !errors.Is(err, models.ErrTaskNotRunning) {
			s.log.Errorf("EditTransCodeTask task %v add pictures error, pictures:%v, err:%v", taskId, newAttachments, err)
			return err
		}
		err = s.streamTaskBiz.UpdateTaskAttachments(ctx, attachType, taskId, newAttachments, newAttachNames)
		if err != nil {
			s.log.Errorf("EditTransCodeTask task %v add pictures error, pictures:%v, err:%v", taskId, newAttachments, err)
			return err
		}
	}

	if len(oldAttachments) > 0 {
		var err error
		if attachType == lc.AttachmentTypePIC {
			err = s.streamTaskBiz.CommandTask(ctx, taskId, biz.JsonToString(oldAttachments), "", lc.PIC_EDIT_COMMAND)
		} else {
			err = s.streamTaskBiz.CommandTask(ctx, taskId, biz.JsonToString(oldAttachments), "", lc.TEXT_EDIT_COMMAND)
		}
		if err != nil && !errors.Is(err, models.ErrTaskNotRunning) {
			s.log.Errorf("EditTransCodeTask task %v edit pictures error, pictures:%v, err:%v", taskId, newAttachments, err)
			return err
		}
		err = s.streamTaskBiz.UpdateTaskAttachments(ctx, attachType, taskId, oldAttachments, oldAttachNames)
		if err != nil {
			s.log.Errorf("EditTransCodeTask task %v edit pictures error, pictures:%v, err:%v", taskId, newAttachments, err)
			return err
		}
	}
	return nil
}
func (s *TaskService) TextEditTransCodeTask(ctx context.Context, request lc.LiveTransCodeTextEditTaskRequest) (*pb.CommandTaskReply, error) {
	ctx = utils.SetRequestId(ctx)
	ctx = utils.SetTaskId(ctx, request.TaskId)
	var err error
	var resp *pb.CompleteTaskReply
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, request.TaskId, request)
	defer func() {
		s.logApiOut(ctx, f, request.TaskId, start, request, resp, err)
	}()
	err = s.EditTransCodeTask(ctx, request.TaskId, request.Texts, lc.AttachmentTypeTEXT)
	if err != nil {
		s.log.Errorf("LiveTransCodeTextEditTask EditTransCodeTask error, err:%v", err)
		return nil, err
	}
	return &pb.CommandTaskReply{
		Result: defaultSuccessReplyResult,
	}, nil
}

func (s *TaskService) PicEditTransCodeTask(ctx context.Context, request lc.LiveTransCodePicEditTaskRequest) (*pb.CommandTaskReply, error) {
	ctx = utils.SetRequestId(ctx)
	ctx = utils.SetTaskId(ctx, request.TaskId)
	var err error
	var resp *pb.CompleteTaskReply
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, request.TaskId, request)
	defer func() {
		s.logApiOut(ctx, f, request.TaskId, start, request, resp, err)
	}()
	err = s.EditTransCodeTask(ctx, request.TaskId, request.Pictures, lc.AttachmentTypePIC)
	if err != nil {
		s.log.Errorf("LiveTransCodePicEditTask EditTransCodeTask error, err:%v", err)
		return nil, err
	}
	return &pb.CommandTaskReply{
		Result: defaultSuccessReplyResult,
	}, nil
}

func findAttachment(attachments []*domain.TranscodeAttachment, name string) *domain.TranscodeAttachment {
	if attachments == nil {
		return nil
	}
	for _, att := range attachments {
		if att.Name == name {
			return att
		}
	}
	return nil
}
