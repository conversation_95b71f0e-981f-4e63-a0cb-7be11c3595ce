package service

import (
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"net/http"
	pb "proto.mpp/api/taskmanager/v1"
	"strings"
	"testing"
)

func TestTaskService_POPStartTask(t *testing.T) {
	//返回成功
	mockey.PatchConvey("POPStartTask and startTask return success", t, func() {
		req, _ := http.NewRequest("POST", stdStartUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")

		defer mockey.Mock((*TaskService).StartTask).Return(&pb.StartTaskReply{
			Result: defaultSuccessReplyResult,
		}, nil).Build().UnPatch()
		_, err := taskService.POPStartTask(nil, req)
		assert.Nil(t, err)
	})
}

func TestTaskService_POPStopTask(t *testing.T) {

	//返回成功
	mockey.PatchConvey("POPStopTask and stopTask return success", t, func() {
		req, _ := http.NewRequest("POST", stdStopUrl, strings.NewReader(taskAdapterRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		defer mockey.Mock((*TaskService).StopTask).Return(&pb.StopTaskReply{
			Result: defaultSuccessReplyResult,
		}, nil).Build().UnPatch()
		_, err := taskService.POPStopTask(nil, req)
		assert.Nil(t, err)
	})

}
