package service

import (
	"context"
	"fmt"
	"io"
	"net/url"
	"strings"
	"time"

	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/models"
	"mpp/pkg/header"
	"mpp/pkg/utils"

	v1 "proto.mpp/api/common/v1"
	pb "proto.mpp/api/taskmanager/v1"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/runtime/protoimpl"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

func (s *TaskService) ResultError(err error) any {
	return map[string]string{
		"code":    "InternalError",
		"message": err.<PERSON>rror(),
	}
}

func (s *TaskService) Result(reason string, message string, rst map[string]interface{}) any {
	if rst == nil {
		rst = make(map[string]interface{})
	}
	rst["code"] = reason
	rst["message"] = message

	if rst["code"] == "" || rst["code"] == "ok" {
		rst["code"] = "Success"
	}

	return rst
}

/*
/job/async/start?scheduleParams={"quotaSet":{"cpu":100}}&product=mpp-integration-test&engineModel=mpp-integration-test-engine&tag=mpp-tag-gproxy&userId=mpp&AppCode=3cbdb71021954603a282d7bed8f72410
*/
func getBodyOrQueryString(js jsoniter.Any, query url.Values, key string) string {
	if query.Get(key) == "" {
		return js.Get(key).ToString()
	}
	return query.Get(key)
}

func (s *TaskService) ResultWrapper(ctx http.Context, requestId string, code int, rst interface{}) error {
	ctx.Response().Header().Set("x-requested-id", requestId)
	return ctx.JSON(code, rst)
}

func (s *TaskService) StdStartTask(w http.ResponseWriter, r *http.Request) (any, error) {
	log.Infof("StdStartTask, url:%s, headers:%+v", r.URL.String(), r.Header)
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	// 开启影子链路
	if r.Header.Get(header.HeaderShadow) != "" {
		query.Set("jobId", header.HeaderShadow+uuid.NewString())
	}

	log.Infof("StdStartTask, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)
	in := &pb.StartTaskRequest{
		RequestId:       js.Get("trace").Get("requestId").ToString(),
		TaskId:          getBodyOrQueryString(js, query, "taskId"),
		JobId:           getBodyOrQueryString(js, query, "jobId"),
		Quota:           map[string]int64{},
		Product:         getBodyOrQueryString(js, query, "product"),
		EngineModel:     getBodyOrQueryString(js, query, "engineModel"),
		Tag:             getBodyOrQueryString(js, query, "tag"),
		EngineParams:    getBodyOrQueryString(js, query, "engineParams"),
		EngineParamsUrl: getBodyOrQueryString(js, query, "engineParamsUrl"),
		Notify:          getBodyOrQueryString(js, query, "notify"),
		Trace:           getBodyOrQueryString(js, query, "trace"),
		UserData:        getBodyOrQueryString(js, query, "userData"),
		Affinity:        nil,
		QuotaSet:        nil,
		UserId:          getBodyOrQueryString(js, query, "userId"),
		//并行转码调度参数
		ScheduleParams: getBodyOrQueryString(js, query, "scheduleParams"),
	}
	quotaSetJson := js.Get("quotaSet")
	for _, key := range quotaSetJson.Keys() {
		in.Quota[key] = quotaSetJson.Get(key).ToInt64()
	}

	// tc老方案里 根据EngineModel路由
	if query.Get("scheduleParams") != "" {
		scheduleParamsJson := json.Get([]byte(query.Get("scheduleParams")))
		quotaSetJson := scheduleParamsJson.Get("quotaSet")
		if len(quotaSetJson.Keys()) > 0 {
			for _, key := range quotaSetJson.Keys() {
				in.Quota[key] = quotaSetJson.Get(key).ToInt64()
			}
		}
	}

	//新增metadata参数 Metadata  map[string]string
	if getBodyOrQueryString(js, query, "metadata") != "" {
		//参数转换
		metadataJson := json.Get([]byte(getBodyOrQueryString(js, query, "metadata")))
		in.Metadata = map[string]string{}
		for _, key := range metadataJson.Keys() {
			in.Metadata[key] = metadataJson.Get(key).ToString()
		}
		log.Infof("StdStartTask test meta, metadataJson:%+v ,metadata:%+v,in:%+v", metadataJson, in.Metadata, in)
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.StartTask(newCtx, in)
	if err != nil {
		return nil, err
	}

	rst := map[string]any{
		"jobId": in.JobId,
	}
	if out != nil && out.Result != nil && out.Result.Metadata != nil {
		rst["data"] = out.Result.Metadata
	}

	result := s.Result(out.Result.Reason, out.Result.Message, rst)
	log.Infof("StdStartTask success, jobId:%s, taskId:%s, url:%s, headers:%+v, body:%s,out", in.JobId, in.TaskId, r.URL.String(), r.Header, bodyStr)
	return result, nil
}

func (s *TaskService) StdStopTask(w http.ResponseWriter, r *http.Request) (any, error) {
	log.Infof("StdStopTask, url:%s, headers:%+v", r.URL.String(), r.Header)
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	log.Infof("StdStopTask, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)

	in := &pb.StopTaskRequest{
		RequestId: js.Get("trace").Get("requestId").ToString(),
		JobId:     getBodyOrQueryString(js, query, "jobId"),
		TaskId:    getBodyOrQueryString(js, query, "taskId"),
	}
	if in.RequestId == "" {
		in.RequestId = js.Get("requestId").ToString()
	}

	if in.JobId == "" && in.TaskId != "" {
		in.JobId = in.TaskId
	}
	if in.TaskId == "" {
		in.TaskId = in.JobId
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.StopTask(newCtx, in)
	if err != nil {
		return nil, err
	}

	return s.Result(out.Result.Reason, out.Result.Message, map[string]any{"jobId": in.JobId}), nil
}

func (s *TaskService) StdUpdateTask(w http.ResponseWriter, r *http.Request) (any, error) {
	log.Infof("StdUpdateTask, url:%s, headers:%+v", r.URL.String(), r.Header)
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	log.Infof("StdUpdateTask, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)

	in := &pb.UpdateTaskRequest{
		RequestId:    js.Get("trace").Get("requestId").ToString(),
		JobId:        getBodyOrQueryString(js, query, "jobId"),
		TaskId:       getBodyOrQueryString(js, query, "taskId"),
		EngineParams: getBodyOrQueryString(js, query, "engineParams"),
		Quota:        map[string]int64{},
	}
	if len(getBodyOrQueryString(js, query, "updateEngine")) > 0 {
		in.TaskUpdateType = v1.TaskUpdateType_IncrementalTaskUpdateType
	}
	quotaSetJson := js.Get("quotaSet")
	for _, key := range quotaSetJson.Keys() {
		in.Quota[key] = quotaSetJson.Get(key).ToInt64()
	}

	if query.Get("scheduleParams") != "" {
		scheduleParamsJson := json.Get([]byte(query.Get("scheduleParams")))
		quotaSetJson := scheduleParamsJson.Get("quotaSet")
		if len(quotaSetJson.Keys()) > 0 {
			for _, key := range quotaSetJson.Keys() {
				in.Quota[key] = quotaSetJson.Get(key).ToInt64()
			}
		}
	}

	if in.RequestId == "" {
		in.RequestId = js.Get("requestId").ToString()
	}

	if in.JobId == "" && in.TaskId != "" {
		in.JobId = in.TaskId
	}
	if in.TaskId == "" {
		in.TaskId = in.JobId
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.UpdateTask(newCtx, in)
	if err != nil {
		return nil, err
	}

	return s.Result(out.Result.Reason, out.Result.Message, map[string]any{"jobId": in.JobId}), nil
}

func (s *TaskService) StdSubmitTask(w http.ResponseWriter, r *http.Request) (any, error) {
	log.Infof("StdSubmitTask, url:%s, headers:%+v", r.URL.String(), r.Header)
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	log.Infof("StdSubmitTask, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)

	js := json.Get(body)
	in := &pb.SubmitTaskRequest{
		RequestId:       js.Get("trace").Get("requestId").ToString(),
		TaskId:          getBodyOrQueryString(js, query, "taskId"),
		JobId:           getBodyOrQueryString(js, query, "jobId"),
		UserId:          getBodyOrQueryString(js, query, "userId"),
		PipelineId:      getBodyOrQueryString(js, query, "pipelineId"),
		Quota:           map[string]int64{},
		Product:         getBodyOrQueryString(js, query, "product"),
		EngineModel:     getBodyOrQueryString(js, query, "engineModel"),
		Tag:             getBodyOrQueryString(js, query, "tag"),
		EngineParams:    getBodyOrQueryString(js, query, "engineParams"),
		EngineParamsUrl: getBodyOrQueryString(js, query, "engineParamsUrl"),
		Notify:          getBodyOrQueryString(js, query, "notify"),
		Trace:           getBodyOrQueryString(js, query, "trace"),
		UserData:        getBodyOrQueryString(js, query, "userData"),
		Affinity:        nil,
		Dependency:      nil,
	}
	if in.RequestId == "" {
		in.RequestId = js.Get("requestId").ToString()
	}

	quotaSetJson := js.Get("quotaSet")
	for _, key := range quotaSetJson.Keys() {
		in.Quota[key] = quotaSetJson.Get(key).ToInt64()
	}

	// tc老方案里 根据EngineModel路由
	if scheduleParamsString := getBodyOrQueryString(js, query, "scheduleParams"); scheduleParamsString != "" {
		// todo 完善里面的解析
		//quota解析
		scheduleParamsJson := json.Get([]byte(scheduleParamsString))
		quotaSetJson := scheduleParamsJson.Get("quotaSet")
		if len(quotaSetJson.Keys()) > 0 {
			for _, key := range quotaSetJson.Keys() {
				in.Quota[key] = quotaSetJson.Get(key).ToInt64()
			}
		}
		//quota预估参数解析,用于tc-analysis
		if scheduleParamsJson != nil {
			in.ScheduleParams = scheduleParamsJson.ToString()
		}
		//管道解析
		if in.PipelineId == "" {
			pipelineIdJson := json.Get([]byte(query.Get("scheduleParams"))).Get("pipelineId")
			if pipelineIdJson != nil {
				in.PipelineId = pipelineIdJson.ToString()
			}
		}
	}
	//新增任务出队回调
	dequeueNotify := getBodyOrQueryString(js, query, "dequeueNotify")
	//如果dequeueNotify不为空，将其加入后缀并组装到notify中
	if dequeueNotify != "" {
		//url示例：,mns://1426105496228119.mns.cn-shanghai.aliyuncs.com/queues/test-queue?dequeued=inner
		if in.Notify == "" {
			in.Notify = dequeueNotify + "?" + models.JOB_DEQUEUE_NOTIFY_URL_QUERY + "=inner"
		} else {
			in.Notify = in.Notify + "," + dequeueNotify + "?" + models.JOB_DEQUEUE_NOTIFY_URL_QUERY + "=inner"
		}
	}
	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.SubmitTask(r.Context(), in)
	if err != nil {
		return nil, err
	}

	resultMap := map[string]any{"jobId": in.JobId}
	if out != nil && out.Result != nil && out.Result.Metadata != nil {
		resultMap["metadata"] = out.Result.Metadata
	}
	result := s.Result(out.Result.Reason, out.Result.Message, resultMap)

	return result, nil
}

func (s *TaskService) StdQueryTask(w http.ResponseWriter, r *http.Request) (any, error) {
	log.Infof("StdQueryTask, url:%s, headers:%+v", r.URL.String(), r.Header)

	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	log.Infof("StdQueryTask, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)

	js := json.Get(body)
	in := &pb.GetTaskRequest{
		RequestId: js.Get("trace").Get("requestId").ToString(),
		TaskId:    getBodyOrQueryString(js, query, "taskId"),
		JobId:     getBodyOrQueryString(js, query, "jobId"),
		Product:   getBodyOrQueryString(js, query, "product"),
		UserId:    getBodyOrQueryString(js, query, "userId"),
		Type:      getBodyOrQueryString(js, query, "type"),
	}

	if in.RequestId == "" {
		in.RequestId = js.Get("requestId").ToString()
	}
	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)
	//多任务查询
	if getBodyOrQueryString(js, query, "jobIds") != "" {
		jobIds := strings.Split(getBodyOrQueryString(js, query, "jobIds"), ",")
		log.Infof("StdQueryTask start jobIds query, len:%v, jobIds:%v,:", len(jobIds), jobIds)
		jobs := make([]*pb.AsyncOfflineJobDTO, len(jobIds))
		rstList := make([]*pb.GetTaskReply, len(jobIds))
		for i, jobId := range jobIds {
			in.JobId = jobId
			in.TaskId = jobId
			rst, jobDTO, err := s.ProcessStdQueryTask(r.Context(), in)
			if err != nil {
				return nil, err
			}
			jobs[i] = jobDTO
			rstList[i] = rst
		}
		data := map[string]interface{}{
			"jobs": jobs,
		}
		//err = mapstructure.WeakDecode(jobs, &data)
		if err != nil {
			return nil, err
		}
		return s.Result(rstList[0].Result.Reason, rstList[0].Result.Message, data), nil
	} else {
		//单任务查询
		if in.JobId == "" && in.TaskId != "" {
			in.JobId = in.TaskId
		}
		if in.TaskId == "" {
			in.TaskId = in.JobId
		}
		log.Infof("StdQueryTask start single tasker query, job:%v", in.JobId)
		rst, jobDTO, err := s.ProcessStdQueryTask(r.Context(), in)
		if err != nil {
			return nil, err
		}

		data := map[string]interface{}{
			"job": jobDTO,
		}
		return s.Result(rst.Result.Reason, rst.Result.Message, data), nil
	}

}

// ProcessStdQueryTask 查询单个任务信息
func (s *TaskService) ProcessStdQueryTask(ctx context.Context, in *pb.GetTaskRequest) (*pb.GetTaskReply, *pb.AsyncOfflineJobDTO, error) {
	newCtx := utils.SetCustomRequestId(ctx, in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)
	log.Infof("GetTaskQueryHandler, request: %+v", in)
	rst, err := s.GetTask(newCtx, in)
	if err != nil {
		return nil, nil, err
	}
	// 转换为job接口需要的参数 qinxin
	jobDTO, err := s.TaskDetailCovertToAsyncOfflineJobDTO(rst.Data.GetTask())
	if err != nil {
		log.Errorf("StdQueryTask failed in TaskDetailCovertToAsyncOfflineJobDTO err:%v,taskDetail:%v", err, rst.Data.GetTask())
		return nil, nil, err
	}

	return rst, jobDTO, nil
}

func (s *TaskService) TaskDetailCovertToAsyncOfflineJobDTO(taskDetail *pb.TaskDetail) (*pb.AsyncOfflineJobDTO, error) {
	// todo 转换为job接口需要的参数
	status := "Success"
	trace := ""
	data := ""
	dagFailed := false
	jobDTO := &pb.AsyncOfflineJobDTO{
		JobId: taskDetail.JobId,

		EngineModel: &taskDetail.EngineModel,
		Tag:         &taskDetail.Tag,
		UserData:    &taskDetail.UserData,
		Notify:      &taskDetail.Notify,
		UserId:      &taskDetail.UserId,
		Status:      &status,
		Trace:       &trace,
		Code:        &status,
		Message:     &status,
		Data:        &data,
		DagFailed:   &dagFailed,
	}
	if taskDetail.EngineParams != nil {
		jobDTO.EngineParams = &taskDetail.EngineParams.Param
	}

	if taskDetail.GmtCreate != "" {
		timestamp, err := CSTTStringToTimestamp(taskDetail.GmtCreate, time.DateTime)
		if err != nil {
			log.Errorf("TaskDetailCovertToAsyncOfflineJobDTO failed in StringToTimestamp err:%v,taskDetail:%v", err, taskDetail)
		}

		jobDTO.CreateTime = timestamp
		jobDTO.SubmitTime = &timestamp
	}
	if taskDetail.GmtModified != "" && taskDetail.BizStatus == int32(models.TaskStateStopped) && taskDetail.InternalStatus == int32(models.TaskStateStopped) {
		timestamp, err := CSTTStringToTimestamp(taskDetail.GmtModified, time.DateTime)
		if err != nil {
			log.Errorf("TaskDetailCovertToAsyncOfflineJobDTO failed in StringToTimestamp err:%v,taskDetail:%v", err, taskDetail)
		}
		jobDTO.FinishTime = timestamp
	}

	executeMode := string(TaskTypeAdapter(v1.TaskType(taskDetail.TaskType)))
	jobDTO.ExecuteMode = &executeMode
	jobDTO.ScheduleParams = &pb.ScheduleParams{}

	if taskDetail.RequestResource != nil && taskDetail.RequestResource.Quota != nil {
		jobDTO.ScheduleParams.QuotaSet = taskDetail.RequestResource.Quota

	}
	return jobDTO, nil
}

func (s *TaskService) StdInvokeTask(w http.ResponseWriter, r *http.Request) (any, error) {
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}
	log.Infof("StdInvokeTask, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	createTime := time.Now().UnixMilli()

	js := json.Get(body)
	var in = &pb.InvokeTaskRequest{
		RequestId:       js.Get("trace").Get("requestId").ToString(),
		UserId:          getBodyOrQueryString(js, query, "userId"),
		TaskId:          getBodyOrQueryString(js, query, "taskId"),
		JobId:           getBodyOrQueryString(js, query, "jobId"),
		Quota:           map[string]int64{},
		Product:         getBodyOrQueryString(js, query, "product"),
		EngineModel:     getBodyOrQueryString(js, query, "engineModel"),
		Tag:             getBodyOrQueryString(js, query, "tag"),
		EngineParams:    getBodyOrQueryString(js, query, "engineParams"),
		EngineParamsUrl: getBodyOrQueryString(js, query, "engineParamsUrl"),
		UserData:        getBodyOrQueryString(js, query, "userData"),
		Trace:           getBodyOrQueryString(js, query, "trace"),
		Affinity:        nil,
		QuotaSet:        nil,
	}

	if in.RequestId == "" {
		in.RequestId = js.Get("requestId").ToString()
	}

	quotaSetJson := js.Get("quotaSet")
	for _, key := range quotaSetJson.Keys() {
		in.Quota[key] = quotaSetJson.Get(key).ToInt64()
	}

	//engineEntrance解析
	engineEntranceJson := js.Get("engineEntrance")
	if engineEntranceJson != nil {
		in.EngineEntrance = map[string]string{}
		for _, key := range engineEntranceJson.Keys() {
			in.EngineEntrance[key] = engineEntranceJson.Get(key).ToString()

		}
	}

	// tc老方案里 根据EngineModel路由
	if query.Get("scheduleParams") != "" {
		// todo 完善里面的解析
		scheduleParamsJson := json.Get([]byte(query.Get("scheduleParams")))
		quotaSetJson := scheduleParamsJson.Get("quotaSet")
		if len(quotaSetJson.Keys()) > 0 {
			for _, key := range quotaSetJson.Keys() {
				in.Quota[key] = quotaSetJson.Get(key).ToInt64()
			}
		}

	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)
	if in.JobId == "" {
		if in.TaskId != "" {
			in.JobId = in.TaskId
		} else {
			//in.JobId = models.NewTaskID(v1.TaskType_Async)
			//这里id带 - 号，可能会引发引擎执行错误
			in.JobId = models.NewUUIDString()
		}
	}

	if in.TaskId == "" {
		in.TaskId = in.JobId
	}

	jobResult := &SyncJobDTO{
		CreateTime:     createTime,
		EngineModel:    in.EngineModel,
		Tag:            in.Tag,
		ScheduleParams: query.Get("scheduleParams"),
		UserId:         in.UserId,
		EngineParams:   in.EngineParams,
		JobId:          in.JobId,
		Status:         string(InvokeSuccess), // Init, Success, Fail
		UserData:       in.UserData,
		Code:           "",
		Message:        "",
	}

	rst, err := s.InvokeTask(newCtx, in)
	if err != nil {
		//如果失败，则返回错误信息和job信息
		kratosErr := common.ToKratosError(err)
		e := errors.FromError(kratosErr)
		jobResult.Code = e.Reason
		jobResult.Message = e.Message
		jobResult.Status = string(InvokeFail)
		responseMap := make(map[string]interface{})
		dataBytes, errTemp := json.Marshal(jobResult)
		if errTemp != nil {
			log.Errorf("StdInvokeTask failed in InvokeTask and json.Marshal err:%v, jobResult:%v, errTemp:%v", err, jobResult, errTemp)
			return nil, err
		}
		errTemp = json.Unmarshal(dataBytes, &responseMap)
		if errTemp != nil {
			log.Errorf("StdInvokeTask failed in InvokeTask and json.Unmarshal err:%v, jobResult:%v, errTemp:%v", err, jobResult, errTemp)
			return nil, err
		}
		data := map[string]interface{}{
			"job": responseMap,
		}
		log.Errorf("stdInvokeTask failed in InvokeTask, jobResult:%+v ,err:%v", jobResult, err)
		return s.Result(e.Reason, e.Message, data), err
	}

	jobResult.ScheduleParams = query.Get("scheduleParams")
	jobResult.Code = rst.Result.Reason
	jobResult.Message = rst.Result.Message

	if rst.Data != "" {
		var jsonData map[string]interface{}
		err = json.Unmarshal([]byte(rst.Data), &jsonData)
		if err != nil {
			return nil, err
		}
		jobResult.Data = jsonData
	} else {
		jobResult.Data = rst.DataUrl
	}
	responseMap := make(map[string]interface{})
	dataBytes, err := json.Marshal(jobResult)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(dataBytes, &responseMap)
	if err != nil {
		return nil, err
	}
	data := map[string]interface{}{
		"job": responseMap,
	}
	// todo 转换为job接口需要的参数
	log.Infof("StdInvokeTask success, jobResult:%+v", jobResult)
	return s.Result(rst.Result.Reason, rst.Result.Message, data), nil
}

// 参考mpp-tc-job定义SyncJobDTO消息，用于返回业务层invoke接口结果
type SyncJobDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreateTime     int64       `protobuf:"varint,1,opt,name=createTime,proto3" json:"createTime,omitempty"` // 使用Timestamp类型表示日期时间
	EngineModel    string      `protobuf:"bytes,2,opt,name=engineModel,proto3" json:"engineModel,omitempty"`
	Tag            string      `protobuf:"bytes,3,opt,name=tag,proto3" json:"tag,omitempty"`
	ScheduleParams string      `protobuf:"bytes,4,opt,name=scheduleParams,proto3" json:"scheduleParams,omitempty"`
	UserId         string      `protobuf:"bytes,5,opt,name=userId,proto3" json:"userId,omitempty"`
	EngineParams   string      `protobuf:"bytes,6,opt,name=engineParams,proto3" json:"engineParams,omitempty"`
	JobId          string      `protobuf:"bytes,7,opt,name=jobId,proto3" json:"jobId,omitempty"`
	Status         string      `protobuf:"bytes,8,opt,name=status,proto3" json:"status,omitempty"`
	Data           interface{} `protobuf:"bytes,9,opt,name=data,proto3" json:"data,omitempty"` // data是json格式，考虑使用bytes类型
	UserData       string      `protobuf:"bytes,10,opt,name=userData,proto3" json:"userData,omitempty"`
	Code           string      `protobuf:"bytes,11,opt,name=code,proto3" json:"code,omitempty"`
	Message        string      `protobuf:"bytes,12,opt,name=message,proto3" json:"message,omitempty"`
}

type invokeStatus string

const (
	InvokeSuccess invokeStatus = "Success"
	InvokeFail    invokeStatus = "Fail"
	IvokeInit     invokeStatus = "Init"
)

func (s *TaskService) StdCancelTask(w http.ResponseWriter, r *http.Request) (any, error) {
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}
	log.Infof("StdCancelTask, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)

	in := &pb.CancelTaskRequest{
		RequestId: js.Get("trace").Get("requestId").ToString(),
		UserId:    getBodyOrQueryString(js, query, "userId"),
		TaskId:    getBodyOrQueryString(js, query, "taskId"),
		JobId:     getBodyOrQueryString(js, query, "jobId"),
	}
	if in.RequestId == "" {
		in.RequestId = js.Get("requestId").ToString()
	}

	if query.Get("jobId") != "" {
		in.JobId = query.Get("jobId")
	}
	if query.Get("taskId") != "" {
		in.TaskId = query.Get("taskId")
	}

	if in.JobId == "" {
		if in.TaskId != "" {
			in.JobId = in.TaskId
		}
	}
	if in.TaskId == "" {
		in.TaskId = in.JobId
	}
	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)
	rst, err := s.CancelTask(r.Context(), in)

	if err != nil {
		return nil, err
	}

	// todo 转换为job接口需要的参数
	return s.Result(rst.Result.Reason, rst.Result.Message, nil), nil
}

func (s *TaskService) StdCommandTask(w http.ResponseWriter, r *http.Request) (any, error) {
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}
	log.Infof("StdCommandTask, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)

	in := &pb.CommandTaskRequest{
		RequestId: js.Get("trace").Get("requestId").ToString(),
		JobId:     getBodyOrQueryString(js, query, "jobId"),
		TaskId:    getBodyOrQueryString(js, query, "taskId"),
		//传入参数映射：commandParams -> engineParams
		EngineParams: getBodyOrQueryString(js, query, "engineParams"),
	}

	if in.RequestId == "" {
		in.RequestId = js.Get("requestId").ToString()
	}

	if in.JobId == "" && in.TaskId != "" {
		in.JobId = in.TaskId
	}
	if in.TaskId == "" {
		in.TaskId = in.JobId
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.CommandTask(newCtx, in)
	if err != nil {
		return nil, err
	}

	return s.Result(out.Result.Reason, out.Result.Message, map[string]any{"jobId": in.JobId}), nil
}

func (s *TaskService) LiveStartTask(w http.ResponseWriter, r *http.Request) (any, error) {
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return nil, err
	}
	if body != nil {
		bodyStr = string(body)
	}
	log.Infof("LiveStartTask message, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)
	switch getLiveStartTaskType(js, r.URL.Query(), "token", "weight") {
	case models.LiveTranscode:
		return s.LiveTranscodeStartTask(w, r, body)
	case models.LiveRecord:
		return s.RecordHlsStartTask(w, r, body)
	default:
		return nil, errors.BadRequest("LiveStartTask", "not support task type")
	}
}

func (s *TaskService) LiveStopTask(w http.ResponseWriter, r *http.Request) (any, error) {
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return nil, err
	}
	if body != nil {
		bodyStr = string(body)
	}
	log.Infof("LiveStopTask message, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)
	switch getLiveStartTaskType(js, r.URL.Query(), "soft", "appName") {
	case models.LiveTranscode:
		return s.LiveTranscodeStopTask(w, r, body)
	case models.LiveRecord:
		return s.RecordHlsStopTask(w, r, body)
	default:
		return nil, errors.BadRequest("LiveStopTask", "not support task type")
	}
}

func getLiveStartTaskType(js jsoniter.Any, query url.Values, transcodeKey, recordKey string) string {
	if getBodyOrQueryString(js, query, transcodeKey) != "" {
		return models.LiveTranscode
	} else if getBodyOrQueryString(js, query, recordKey) != "" {
		return models.LiveRecord
	} else {
		return models.UnknownType
	}
}

// 老tc中的任务类型转换
func TaskTypeAdapter(taskType v1.TaskType) TcTaskType {
	switch taskType {
	case v1.TaskType_Unknown:
		return UnAnalysis
	case v1.TaskType_Simple:
		return Single
	case v1.TaskType_Stream:
		return Single
	case v1.TaskType_Async:
		return Single
	case v1.TaskType_DagAsync:
		return DAG
	default:
		return UnAnalysis
	}
}

// TcTaskType 老tc中的任务类型
type TcTaskType string

const (
	// DAG DAG任务
	DAG TcTaskType = "DAG"
	// Single DagAsyncTaskType 其他任务
	Single TcTaskType = "Single"
	// UnAnalysis 其他任务
	UnAnalysis TcTaskType = "UN_ANALYSIS"
)

// CSTTStringToTimestamp StringToTimestamp 将给定的时间字符串（按照指定的格式）转换为Unix时间戳,如果转换失败，则返回错误信息和0作为时间戳。
func CSTTStringToTimestamp(input string, format string) (int64, error) {
	t, err := time.Parse(format, input)
	if err != nil {
		return 0, fmt.Errorf("unexpected format. err：%v", err)
	}
	// 转换为UTC时间
	utcT := t.UTC()
	// 获取毫秒级别的Unix时间戳
	milliTimestamp := utcT.UnixNano() / int64(time.Millisecond)
	return milliTimestamp, nil
}
