package service

import (
	"bytes"
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"io"
	"mpp/internal/taskmanager/biz/common"
	"mpp/pkg/tracer/middleware/tracer"
	"net/url"
	v1 "proto.mpp/api/common/v1"
	"regexp"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	"mpp/internal/taskmanager/models"
	"mpp/pkg/utils"

	stdhttp "net/http"
	pb "proto.mpp/api/taskmanager/v1"
)

type TaskData struct {
	TaskId      string `json:"task_id"`
	GmtModified int64  `json:"gmt_modified"`
	Tag         string `json:"tag"`
}

type TmReturn struct {
	Result TmReturnMsg `json:"result"`
}

type TmReturnMsg struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

type ListTaskData struct {
	Data []TaskData `json:"data"`
}

const (
	// NewSystem 新调度系统（即本调度系统）
	NewSystem string = "new"
	// OldSystem 老调度系统
	OldSystem string = "old"
	// TaskStatusRunning 路由表中任务状态--运行中
	TaskStatusRunning string = "running"
	// TaskStatusStopped 路由表中任务状态--已停止
	TaskStatusStopped string = "stopped"

	httpPrefix     = "http://"
	startPath      = "/task/start"
	pausePath      = "/task/pause"
	taskListPath   = "/monitor/listTask"
	workerListPath = "/worker/list"

	// Success 老响应码
	Success       = "Success"
	InvalidParam  = "InvalidParam"
	InternalError = "InternalError"
	NotFound      = "NotFound"
	Unavailable   = "Unavailable"
	TaskNotFound  = "TaskNotFound"
	Retry         = "Retry"

	// RedisKeyPrefix 路由表数据前缀
	RedisKeyPrefix = "recordhls-"
	LockKeyPrefix  = "lock-recordhls-"
)

// RecordHlsStartTask 1.记录路由表时机--> 新-成功后;老-转发成功时   2. 多次重试失败转到老架构  3.response保持响应一致  4.常用字符串常量化
func (s *TaskService) RecordHlsStartTask(w http.ResponseWriter, r *http.Request, requestBody []byte) (any, error) {
	// 1. 解析请求参数
	// 2. 判断是否在路由表中
	// 3. 调用灰度策略：启动项配置或数据库配置，判定新调度还是老调度
	// 4. 如果是老调度则原参数透传到老的，如果是新调度则转为标准化参数往下调用
	log.Infof("RecordHlsStartTask start, url:%s, headers:%+v", r.URL.String(), r.Header)
	var bodyStr string
	if requestBody != nil {
		bodyStr = string(requestBody)
	}
	log.Infof("RecordHlsStartTask message, url:%s, headers:%+v, body:%s", r.URL.String(), r.Header, bodyStr)
	js := json.Get(requestBody)
	taskId := js.Get("taskId").ToString()
	appName := js.Get("appName").ToString()
	if taskId == "" || appName == "" {
		log.Errorf("RecordHlsStartTask fail with error input,taskId or appName is empty,body:%s", bodyStr)
		return nil, errors.Errorf(stdhttp.StatusBadRequest, InvalidParam, "taskId or appName is empty")
	}
	log.Infof("RecordHlsStartTask start, taskId:%s, appName:%s", taskId, appName)
	query := r.URL.Query()
	newCtx := utils.SetCustomRequestId(r.Context(), getBodyOrQueryString(js, query, "requestId"))
	if !s.router.GetOldSystemExist() || !modelHaveOldSystem(appName) {
		// 没有老调度，则直接使用新调度
		return s.startNewSystemTask(newCtx, requestBody, r)
	}
	routeData := s.router.GetRouteData(RedisKeyPrefix+taskId, newCtx)
	// 在路由表中,是老任务,且当前是运行态则透传往记录的系统
	if routeData != nil && routeData.Status == TaskStatusRunning {
		if routeData.System == NewSystem {
			return s.startNewSystemTask(newCtx, requestBody, r)
		} else {
			// 老调度
			return s.operateOldSystemTask(newCtx, requestBody, r, taskId, "start")
		}
	}

	// 在路由表中且是停止态 或者 不在路由表中，是新任务     由灰度比例路由到目的系统
	rtmpUrl := js.Get("params").Get("rtmpUrl").ToString()
	domain := GetDomainFromRtmpPushUrl(rtmpUrl)
	log.Infof("parse domain from rtmpUrl,domain:%s, rtmpUrl:%s", domain, rtmpUrl)
	if s.router.GetRouteHitResult(taskId, domain) == 1 {
		// 新调度
		return s.startNewSystemTask(newCtx, requestBody, r)
	} else {
		// 老调度
		return s.operateOldSystemTask(newCtx, requestBody, r, taskId, "start")
	}
}

func (s *TaskService) startNewSystemTask(ctx context.Context, body []byte, r *http.Request) (any, error) {
	log.Infof("start task in new system, url:%s", r.URL.String())
	js := json.Get(body)
	requestId := getBodyOrQueryString(js, r.URL.Query(), "requestId")
	// 新调度
	in := &pb.StartTaskRequest{
		RequestId:    requestId,
		TaskId:       js.Get("taskId").ToString(),
		JobId:        js.Get("taskId").ToString(),
		Quota:        map[string]int64{"cpu": js.Get("weight").ToInt64() * 10}, // todo quota
		Product:      "ols",
		EngineModel:  js.Get("appName").ToString(),
		Tag:          "default",
		EngineParams: js.Get("params").ToString(),
		Affinity:     nil,
		QuotaSet:     nil,
	}

	// 截图业务quota不用乘10
	if in.EngineModel == models.LiveSnapshot {
		in.Quota = map[string]int64{"cpu": js.Get("weight").ToInt64()}
	}

	// 老调度并存的时候，单任务加锁，避免类似数据库故障的时候，业务层重试导致一个任务在新调度，一个在老调度.10s取两次重试超时时间5s*2
	if s.router.GetOldSystemExist() && !s.router.AddLock(ctx, LockKeyPrefix+in.TaskId, 10*time.Second) {
		log.Errorf("RecordHlsStartTask fail while add lock, taskId:%s", in.TaskId)
		return nil, errors.Errorf(stdhttp.StatusForbidden, Retry, "task still running")
	}

	out, err := s.StartTask(ctx, in)
	if err != nil {
		// 失败重试
		log.Errorf("start task in new system failed, turn to retry start, taskId:%s, error:%v", in.TaskId, err)
		// 如果是超时失败，需要重设ctx
		newCtx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		newCtx = utils.SetRequestId(newCtx)
		defer cancel()
		return s.retryStart(newCtx, in, body, r)
	}

	// 新架构调度成功，记录路由表
	if s.router.GetOldSystemExist() {
		s.router.SetRouteData(RedisKeyPrefix+in.TaskId, NewSystem, TaskStatusRunning, ctx)
		s.router.DelLock(ctx, LockKeyPrefix+in.TaskId)
	}
	log.Infof("RecordHlsStartTask start task in new system end, taskId:%s, url:%s", in.TaskId, r.URL.String())
	return s.RecordHlsResult(stdhttp.StatusOK, out.Result.Reason, out.Result.Message, nil), nil
}

func (s *TaskService) retryStart(ctx context.Context, in *pb.StartTaskRequest, body []byte, r *http.Request) (any, error) {
	//ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	//defer cancel()
	out, err := s.StartTask(ctx, in)
	if err != nil {
		// 重试失败转到老调度
		log.Errorf("retry start task in new system failed, taskId:%s, error:%v", in.TaskId, err)
		if s.router.GetOldSystemExist() && !errors.Is(err, common.ErrTaskStillRunning) && modelHaveOldSystem(in.EngineModel) {
			// task still running这种不能老系统兜底
			log.Warnf("retry start task in new system failed, turn to old system taskId:%s, error:%v", in.TaskId, err)
			newCtx, cancel := context.WithTimeout(context.Background(), time.Second*5)
			newCtx = utils.SetRequestId(newCtx)
			defer cancel()
			return s.operateOldSystemTask(newCtx, body, r, in.TaskId, "start")
		}
		return dealError(err)
	}

	if s.router.GetOldSystemExist() {
		s.router.SetRouteData(RedisKeyPrefix+in.TaskId, NewSystem, TaskStatusRunning, ctx)
		s.router.DelLock(ctx, LockKeyPrefix+in.TaskId)
	}
	log.Infof("retry start task in new system end, taskId:%s, url:%s", in.TaskId, r.URL.String())
	return s.RecordHlsResult(stdhttp.StatusOK, out.Result.Reason, out.Result.Message, nil), nil
}

func (s *TaskService) RecordHlsStopTask(w http.ResponseWriter, r *http.Request, requestBody []byte) (any, error) {
	// 1. 解析请求参数
	// 2. 查询路由表，有记录则调往记录系统，无记录则调往老调度(默认是在老系统)
	log.Infof("RecordHlsStopTask, url:%s, headers:%+v", r.URL.String(), r.Header)
	var bodyStr string
	if requestBody != nil {
		bodyStr = string(requestBody)
	}

	js := json.Get(requestBody)
	taskId := js.Get("taskId").ToString()
	appName := js.Get("appName").ToString()
	log.Infof("RecordHlsStopTask, taskId:%s, appName:%s, body:%s", taskId, appName, bodyStr)
	if taskId == "" || appName == "" {
		log.Errorf("RecordHlsStopTask error, taskId:%s, appName:%s", taskId, appName)
		return nil, errors.Errorf(stdhttp.StatusBadRequest, InvalidParam, "taskId or appName is empty")
	}

	newCtx := utils.SetCustomRequestId(r.Context(), getBodyOrQueryString(js, r.URL.Query(), "requestId"))
	if !s.router.GetOldSystemExist() || !modelHaveOldSystem(appName) {
		// 老调度已经移除了，直接使用新调度
		return s.stopNewSystemTask(newCtx, requestBody, r)
	}
	if s.router.GetStopBoth() {
		// 配置了双边停止，直接向两边都发起停止
		log.Warnf("RecordHlsStopTask stop both, taskId:%s, appName:%s", taskId, appName)
		// 向两边都发起停止请求
		_, er := s.stopNewSystemTask(newCtx, requestBody, r)
		if er != nil {
			log.Errorf("stop task in new system failed, taskId:%s, error:%v", taskId, er)
		}
		return s.operateOldSystemTask(newCtx, requestBody, r, taskId, "stop")
	}
	routeData := s.router.GetRouteData(RedisKeyPrefix+taskId, newCtx)
	// 在路由表中,是老任务,且当前是运行态则透传往记录的系统
	if routeData != nil {
		if routeData.System == NewSystem {
			return s.stopNewSystemTask(newCtx, requestBody, r)
		} else {
			return s.operateOldSystemTask(newCtx, requestBody, r, taskId, "stop")
		}
	} else {
		log.Warnf("RecordHlsStopTask routeData is null, taskId:%s, appName:%s, routeData:%v", taskId, appName, routeData)
		_, er := s.stopNewSystemTask(newCtx, requestBody, r)
		if er != nil {
			log.Errorf("stop task in new system failed, stop both, taskId:%s, error:%v", taskId, er)
		}
		return s.operateOldSystemTask(newCtx, requestBody, r, taskId, "stop")
	}
}

func (s *TaskService) stopNewSystemTask(ctx context.Context, body []byte, r *http.Request) (any, error) {
	log.Infof("stop task in new system, url:%s", r.URL.String())
	js := json.Get(body)
	query := r.URL.Query()
	requestId := getBodyOrQueryString(js, query, "requestId")
	in := &pb.StopTaskRequest{
		RequestId: requestId,
		TaskId:    js.Get("taskId").ToString(),
		JobId:     js.Get("taskId").ToString(),
	}

	out, err := s.StopTask(ctx, in)
	if err != nil {
		log.Errorf("stop task in new system failed, taskId:%s, error:%v", in.TaskId, err)
		return dealError(err)
	}
	if s.router.GetOldSystemExist() {
		s.router.SetRouteData(RedisKeyPrefix+in.TaskId, NewSystem, TaskStatusStopped, ctx)
	}

	log.Infof("stop task in new system end, taskId:%s, url:%s", in.TaskId, r.URL.String())
	return s.RecordHlsResult(stdhttp.StatusOK, Success, out.Result.Message, nil), nil
}

func dealError(err error) (any, error) {
	if errors.Is(err, common.ErrTaskNotFoundError) {
		return nil, errors.Errorf(stdhttp.StatusForbidden, TaskNotFound, "task not exist")
	} else if errors.Is(err, common.ErrTaskStillRunning) {
		return nil, errors.Errorf(stdhttp.StatusForbidden, Retry, "task still running")
	}
	return nil, errors.Errorf(stdhttp.StatusInternalServerError, err.Error(), "operate task in new system fail:%v", err)
}

func (s *TaskService) operateOldSystemTask(ctx context.Context, body []byte, r *http.Request, taskId, operateType string) (any, error) {
	log.Infof("operate task in old system, operateType:%s, taskId:%s", operateType, taskId)

	// 老调度系统
	js := json.Get(body)
	taskStatus := TaskStatusRunning
	path := startPath
	if operateType == "stop" {
		taskStatus = TaskStatusStopped
		path = pausePath
	}

	urlStr := httpPrefix + s.router.GetOldSystemHost() + path
	reqURL, err := url.Parse(urlStr)
	if err != nil {
		log.Errorf("construct reqURL fail, taskId:%s, error:%v", taskId, err)
		return nil, errors.Errorf(stdhttp.StatusInternalServerError, InternalError, "construct oldSystem url fail")
	}

	reqURL.RawQuery = r.URL.Query().Encode()

	req, err := stdhttp.NewRequest("POST", reqURL.String(), bytes.NewReader(body))
	if err != nil {
		log.Errorf("construct http req fail,taskId:%s, error:%v", taskId, err)
		return nil, errors.Errorf(stdhttp.StatusInternalServerError, InternalError, "construct oldSystem http req fail")
	}
	req.Header.Set("Content-Type", "application/json")
	req.Close = true
	client := &stdhttp.Client{Timeout: time.Second * 5}
	resp, err := client.Do(req)
	if err != nil {
		log.Errorf("operateOldSystemTask http request fail, operateType:%s, taskId:%s, error:%v, resp11:%v", operateType, taskId, err, resp)
		return nil, errors.Errorf(stdhttp.StatusInternalServerError, InternalError, "oldSystem http request fail")
	}
	defer resp.Body.Close()
	// 判断老系统的返回码是否成功，成功则记录路由表
	if resp.StatusCode == 200 {
		s.router.SetRouteData(RedisKeyPrefix+js.Get("taskId").ToString(), OldSystem, taskStatus, ctx)
		s.router.DelLock(ctx, LockKeyPrefix+js.Get("taskId").ToString())
	} else {
		// 老系统也启动失败，暂无其他兜底逻辑，仅打印日志
		s.router.DelLock(ctx, LockKeyPrefix+js.Get("taskId").ToString())
		log.Errorf("operateOldSystemTask http request fail, operateType:%s, taskId:%s, resp11:%v", operateType, taskId, resp)
	}

	// 成功与否都直接透传返回老系统的响应结果
	var bodyByte []byte
	if resp.Body != nil {
		bodyByte, err = io.ReadAll(resp.Body)
		if err != nil {
			log.Errorf("read body fail when send liveTaskManager request, taskId:%s, error:%v", taskId, err)
			return nil, errors.Errorf(stdhttp.StatusInternalServerError, InternalError, "oldSystem resp read fail")
		}
	}

	var result TmReturn
	if err := json.Unmarshal(bodyByte, &result); err != nil {
		return nil, errors.Errorf(stdhttp.StatusInternalServerError, InternalError, "oldSystem resp return json fail")
	}
	// 作error返回是因为老系统的返回码不一定是成功，都塞到error里处理
	log.Infof("operate task in old system end, operateType:%s, taskId:%s, result:%+v", operateType, taskId, result)
	return s.RecordHlsResult(resp.StatusCode, result.Result.Code, result.Result.Message, nil), nil
}

// RecordHlsListTask 查询任务,liveChecker调用
func (s *TaskService) RecordHlsListTask(w http.ResponseWriter, r *http.Request) (any, error) {
	// 查询老系统上的任务  +  查询当前系统上的任务  返回合并结果，任务内容：id，gmt_modified，tag
	log.Infof("RecordHlsListTask, url:%s, headers:%+v", r.URL.String(), r.Header)
	newCtx := utils.SetCustomRequestId(r.Context(), r.URL.Query().Get("requestId"))
	oldRecord, err := s.recordHlsListFromOldSystem(newCtx, r, taskListPath)
	if err != nil {
		log.Errorf("recordHls listTask from old system fail, error:%v", err)
		return nil, errors.Errorf(stdhttp.StatusInternalServerError, InternalError, "recordHls listTask from old system fail")
	}

	tasks, err := s.GetRunningTasks(newCtx, v1.TaskType_Stream, models.PackagerTypeModels)
	if err != nil {
		log.Errorf("get running tasks fail, error:%v", err)
		return nil, errors.Errorf(stdhttp.StatusInternalServerError, InternalError, "recordHls listTask from new system fail")
	}

	// 合并查询结果
	var data ListTaskData
	if oldRecord != nil {
		err = json.Unmarshal(oldRecord, &data)
		if err != nil {
			log.Errorf("unmarshal oldRecord fail, error:%v", err)
			return nil, errors.Errorf(stdhttp.StatusInternalServerError, InternalError, "unmarshal oldRecord fail")
		}
	}

	for _, task := range tasks {
		taskData := &TaskData{
			TaskId:      task.TaskId,
			GmtModified: task.LastModified.Unix(),
			Tag:         task.EngineModel,
		}
		data.Data = append(data.Data, *taskData)
	}
	log.Infof("RecordHlsListTask end, url:%s", r.URL.String())
	return data, nil
}

func (s *TaskService) recordHlsListFromOldSystem(ctx context.Context, r *http.Request, path string) ([]byte, error) {
	log.Infof("recordHls list Task/worker from old system,path:%s, url:%s", path, r.URL.String())
	//return []byte(`{"data":[{"task_id":"123456","gmt_modified":1647192000,"tag":"live"},{"task_id":"123456","gmt_modified":1647192000,"tag":"live"}]}`), nil
	if !s.router.GetOldSystemExist() {
		// 没有老调度，直接返回
		return nil, nil
	}
	urlStr := httpPrefix + s.router.GetOldSystemHost() + path
	reqURL, err := url.Parse(urlStr)
	if err != nil {
		log.Errorf("construct reqURL fail, path:%s, error:%v", path, err)
		return nil, err
	}

	reqURL.RawQuery = r.URL.Query().Encode()
	req, err := stdhttp.NewRequest("POST", reqURL.String(), r.Body)
	if err != nil {
		log.Errorf("construct http req fail, path:%s, error:%v", path, err)
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Close = true
	client := &stdhttp.Client{Timeout: time.Second * 5}
	resp, err := client.Do(req)
	if err != nil {
		log.Errorf("http request fail, path:%s, error:%v", path, err)
		return nil, err
	}
	defer resp.Body.Close()

	// 老调度查询失败
	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		log.Errorf("recordHls list from old system fail with statusCode:%d, responseBody:%s", resp.StatusCode, string(body))
		return nil, errors.Errorf(resp.StatusCode, string(body), "list from old system fail")
	}

	var bodyByte []byte
	if resp.Body != nil {
		bodyByte, err = io.ReadAll(resp.Body)
		if err != nil {
			log.Errorf("read body fail when send listTask request to livetaskmanager, error:%v", err)
			return nil, err
		}
	}

	// todo 删除  调试trace能力
	//connHTTP, err := InitHttpConnection(ctx, s.router.GetOldSystemHost())
	//if err != nil {
	//	log.Infof("xxxxxxxxxxxxxxxx11111,path:%s", path)
	//	//return nil, err
	//}
	//defer connHTTP.Close()
	//replay := make(map[string]interface{})
	//err = connHTTP.Invoke(ctx, "POST", path, nil, &replay)
	//if err != nil {
	//	log.Infof("xxxxxxxxxxxxxxxx2222,path:%s, resp:%+v", path, replay)
	//}
	//log.Infof("xxxxxxxxxxxxxx3333,path:%s, resp:%+v", path, replay)

	log.Infof("recordHls list from old system end,path:%s, url:%s", path, r.URL.String())
	return bodyByte, nil
}

func (s *TaskService) RecordHlsResult(statusCode int, reason string, message string, rst map[string]interface{}) any {
	result := make(map[string]interface{})
	result["result"] = s.Result(reason, message, rst)
	result["statusCode"] = statusCode
	return result
}

// RecordHlsListWorker 查询任务,liveChecker调用
func (s *TaskService) RecordHlsListWorker(w http.ResponseWriter, r *http.Request) (any, error) {
	// 查询老系统上的worker信息  +  查询当前系统上的worker  返回合并结果，任务内容：id，gmt_modified，tag
	log.Infof("RecordHlsListWorker, url:%s, headers:%+v", r.URL.String(), r.Header)
	newCtx := utils.SetCustomRequestId(r.Context(), r.URL.Query().Get("requestId"))
	oldRecord, err := s.recordHlsListFromOldSystem(newCtx, r, workerListPath)
	if err != nil {
		log.Errorf("recordHls listWorker from old system fail, error:%v", err)
		return nil, errors.Errorf(stdhttp.StatusInternalServerError, InternalError, "recordHls listWorker from old system fail")
	}

	// 合并查询结果
	var data map[string]interface{}
	if oldRecord == nil {
		return data, nil
	}
	err = json.Unmarshal(oldRecord, &data)
	if err != nil {
		log.Errorf("unmarshal oldRecord fail, error:%v", err)
		return nil, errors.Errorf(stdhttp.StatusInternalServerError, InternalError, "unmarshal oldRecord fail")
	}
	data["statusCode"] = stdhttp.StatusOK
	log.Infof("RecordHlsListWorker end, data:%s", data)
	return data, nil
}

func InitHttpConnection(ctx context.Context, addr string) (*http.Client, error) {
	connHTTP, err := http.NewClient(
		ctx,
		http.WithEndpoint(fmt.Sprintf("http://%v", addr)),
		http.WithBlock(),
		//arms链路追踪埋点
		http.WithMiddleware(
			tracing.Client(),
			tracer.MPPClientTracer(),
			metadata.Client(),
		),
	)
	if err != nil {
		log.Errorf("init http:%v connection failed:%v ", addr, err)
		return nil, err
	}

	return connHTTP, nil
}

func GetDomainFromRtmpPushUrl(url string) string {
	if url == "" {
		return ""
	}
	pattern := "rtmp\\://[^/]+/([^/]+)/([^\\?]+)\\?.*vhost=([^\\&]+)"
	re := regexp.MustCompile(pattern)
	matches := re.FindStringSubmatch(url)
	//var app string
	//var stream string
	var domain string
	if len(matches) > 0 {
		//app = matches[1]
		pos := strings.Index(matches[2], "_")
		if pos < 0 {
			//stream = matches[2]
			domain = matches[3]
		} else {
			domain = matches[2][:pos]
			//stream = matches[2][pos+1:]
		}
		return domain
	}
	return ""
}

func modelHaveOldSystem(model string) bool {
	switch model {
	case models.LiveHLS, models.LiveRecord:
		return true
	default:
		return false
	}
}
