package service

import (
	"context"
	"github.com/bytedance/mockey"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"mpp/internal/taskmanager/biz/grayscale"
	"mpp/internal/taskmanager/models"
	pb "proto.mpp/api/taskmanager/v1"
	"testing"
)

/**
 * @Author: qinxin
 * @Date: 2025/3/25 16:12
 * @Desc:
 * @Version 1.0
 */

var (
	grayscaleService *GrayscaleService
)

func init() {
	grayscaleService, _ = NewGrayscaleService(log.DefaultLogger, &grayscale.MixRuleConfigBiz{})
}

func TestGrayscaleService_CreateMixGrayConfig(t *testing.T) {
	//返回成功
	mockey.PatchConvey("CreatePipeline and  return success", t, func() {
		defer mockey.Mock((*grayscale.MixRuleConfigBiz).CreateMixGrayConfig).Return(nil).Build().UnPatch()
		_, err := grayscaleService.CreateMixGrayConfig(context.Background(), &pb.CreateMixGrayConfigRequest{
			RuleName:      "qinxin-pop-test-gpu",
			Priority:      3,
			ConfigStatus:  1,
			RuleConfigs:   "[{\"value\": \"mediaai\", \"operate\": \"==\", \"paramName\": \"product\", \"valueType\": \"string\"}, {\"value\": \"algo-speech-trans\", \"operate\": \"==\", \"paramName\": \"engineModel\", \"valueType\": \"string\"}, {\"value\": \"mixed-test,default\", \"operate\": \"in\", \"paramName\": \"tag\", \"valueType\": \"string\"}]",
			MixRatio:      1,
			MixRuleConfig: "[{\"tag\": \"sandbox\", \"ratio\": 100, \"product\": \"mpp-integration-test\", \"engineModel\": \"mpp-integration-test-engine\",\"mixAllocStrategy\":\"TryAbandonMixed\"}]",
		})
		//unsupported tasker type for UpdateTask
		assert.Nil(t, err)
	})
}

func TestGrayscaleService_UpdateMixGrayConfig(t *testing.T) {
	mockey.PatchConvey("UpdatePipeline and  return success", t, func() {
		defer mockey.Mock((*grayscale.MixRuleConfigBiz).UpdateMixGrayConfig).Return(nil).Build().UnPatch()
		_, err := grayscaleService.UpdateMixGrayConfig(context.Background(), &pb.UpdateMixGrayConfigRequest{
			RuleName:     "qinxin-pop-test-gpu",
			Priority:     3,
			ConfigStatus: 1,
		})
		assert.Nil(t, err)
	})

}

func TestGrayscaleService_DeleteMixGrayConfig(t *testing.T) {

	mockey.PatchConvey("DeletePipeline and  return success", t, func() {
		defer mockey.Mock((*grayscale.MixRuleConfigBiz).DeleteMixGrayConfig).Return(nil).Build().UnPatch()
		_, err := grayscaleService.DeleteMixGrayConfig(context.Background(), &pb.DeleteMixGrayConfigRequest{
			RuleName: "qinxin-pop-test-gpu",
		})
		assert.Nil(t, err)
	})

}

func TestGrayscaleService_ListMixGrayConfig(t *testing.T) {
	mockey.PatchConvey("ListPipeline and  return success", t, func() {
		defer mockey.Mock((*grayscale.MixRuleConfigBiz).ListMixGrayConfig).Return([]*models.MixGrayConfig{}, nil).Build().UnPatch()
		_, err := grayscaleService.ListMixGrayConfig(context.Background(), &pb.ListMixGrayConfigRequest{})
		assert.Nil(t, err)
	})

}

//func TestGrayscaleService_GetMixGrayConfig(t *testing.T) {
//
//	mockey.PatchConvey("GetPipeline and  return success", t, func() {
//		defer mockey.Mock((*grayscale.MixRuleConfigBiz).GetMixGrayConfig).Return(&models.MixGrayConfig{}, nil).Build().UnPatch()
//		_, err := grayscaleService.GetMixGrayConfig(context.Background(), &pb.GetMixGrayConfigRequest{
//			RuleName: "qinxin-pop-test-gpu",
//		})
//		assert.Nil(t, err)
//	})
//}

func TestGrayscaleService_ActiveMixGrayConfig(t *testing.T) {
	mockey.PatchConvey("ActiveMixGrayConfig and  return success", t, func() {
		defer mockey.Mock((*grayscale.MixRuleConfigBiz).ActiveMixGrayConfig).Return(nil).Build().UnPatch()
		_, err := grayscaleService.ActiveMixGrayConfig(context.Background(), &pb.ActiveMixGrayConfigRequest{
			RuleName: "qinxin-pop-test-gpu",
		})
		assert.Nil(t, err)
	})

}

func TestGrayscaleService_PauseMixGrayConfig(t *testing.T) {

	mockey.PatchConvey("PauseMixGrayConfig and  return success", t, func() {
		defer mockey.Mock((*grayscale.MixRuleConfigBiz).PauseMixGrayConfig).Return(nil).Build().UnPatch()
		_, err := grayscaleService.PauseMixGrayConfig(context.Background(), &pb.PauseMixGrayConfigRequest{
			RuleName: "qinxin-pop-test-gpu",
		})
		assert.Nil(t, err)
	})
}

func TestGrayscaleService_MockMixGrayRuleEngine(t *testing.T) {
	mockey.PatchConvey("MockMixGrayRuleEngine and  return success", t, func() {
		defer mockey.Mock((*grayscale.MixRuleConfigBiz).ExcuteMixRuleEngine).Return(&models.MixGrayConfig{}, nil).Build().UnPatch()
		_, err := grayscaleService.MockMixGrayRuleEngine(context.Background(), &pb.MockMixGrayRuleEngineRequest{
			Product:     "qinxin-pop-test-gpu",
			EngineModel: "qinxin-pop-test-gpu",
			Tag:         "qinxin-pop-test-gpu",
			UserId:      "qinxin-pop-test-gpu",
		})
		assert.Nil(t, err)
	})

}

func TestGrayscaleService_UpdateMixGrayRatio(t *testing.T) {
	mockey.PatchConvey("UpdateMixGrayRatio and  return success", t, func() {
		defer mockey.Mock((*grayscale.MixRuleConfigBiz).UpdateMixGrayRatio).Return(nil).Build().UnPatch()
		_, err := grayscaleService.UpdateMixGrayRatio(context.Background(), &pb.UpdateMixGrayRatioRequest{
			RuleName: "qinxin-pop-test-gpu",
		})
		assert.Nil(t, err)
	})
}
