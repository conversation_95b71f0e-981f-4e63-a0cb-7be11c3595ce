/**
 * @Author: qinxin
 * @Date: 2024/7/16 10:30
 * @Desc:
 * @Version 1.0
 */

package service

import (
	kratosErrors "github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/google/uuid"
	"io"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/custom_types"
	"mpp/pkg/header"
	"mpp/pkg/utils"
	stdhttp "net/http"
	v1 "proto.mpp/api/common/v1"
	pb "proto.mpp/api/taskmanager/v1"
)

const (
	//tc 接入的默认product参数值
	TC_DEFAULT_PRODUCT = "mps"

	// tc需要适配的错误码
	// 任务不存在 TM错误reason ErrorReason_TASK_NOT_FOUND
	ErrorTaskNotExist = "InvalidParameter.TaskNotExist"
	// 资源不足 TM错误reason ErrorReason_NOT_ENOUGH_QUOTA
	ErrorTaskInsufficientResources = "TaskInsufficientResources"
	// 任务已存在TM返回成功 暂不适配
	ErrorTaskAlreadyExist = "InvalidParameter.TaskAlreadyExist"
)

// return httpCode and error result
func MakeTCResultError(err error) (int, any) {
	kratosErr := common.ToKratosError(err)
	e2 := kratosErrors.FromError(kratosErr)
	httpCode := stdhttp.StatusInternalServerError
	// 任务不存在
	if e2.Reason == v1.ErrorReason_TASK_NOT_FOUND.String() {
		e2.Reason = ErrorTaskNotExist
		httpCode = stdhttp.StatusNotFound
	}
	// 资源不足
	if e2.Reason == v1.ErrorReason_NOT_ENOUGH_QUOTA.String() {
		e2.Reason = ErrorTaskInsufficientResources
		httpCode = stdhttp.StatusNotFound
	}

	// 任务已存在
	if e2.Reason == common.TaskAlreadyExist {
		e2.Reason = ErrorTaskAlreadyExist
		httpCode = stdhttp.StatusNotFound
	}
	result := map[string]string{
		"code":    e2.Reason,
		"message": e2.Message,
	}
	return httpCode, result
}

// TaskDependencySpec define task dependency parameters.
// The key of Fields is custom, and the value is predefined.
type TaskDependencySpec struct {
	TaskId      string            `json:"taskId,omitempty"`      // Task name.
	Phase       string            `json:"phase,omitempty"`       // Task phase.
	Fields      map[string]string `json:"fields,omitempty"`      // Task fields.
	engineModel string            `json:"engineModel,omitempty"` // Task path.
}

// 无错误码适配
func (s *TaskService) TCInvokeTask(w http.ResponseWriter, r *http.Request) (any, error) {
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	// 开启影子链路
	if r.Header.Get(header.HeaderShadow) != "" {
		query.Set("jobId", header.HeaderShadow+uuid.NewString())
	}

	log.Infof("TCInvokeTask, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)
	in := &pb.InvokeTaskRequest{
		RequestId: getBodyOrQueryString(js, query, "requestId"),
		//TaskId:         getBodyOrQueryString(js, query, "taskId"),
		JobId:           getBodyOrQueryString(js, query, "jobId"),
		TaskId:          getBodyOrQueryString(js, query, "taskId"),
		Quota:           map[string]int64{},
		Product:         getBodyOrQueryString(js, query, "product"),
		EngineModel:     getBodyOrQueryString(js, query, "engineModel"),
		Tag:             getBodyOrQueryString(js, query, "tag"),
		EngineParams:    getBodyOrQueryString(js, query, "engineParams"),
		EngineParamsUrl: getBodyOrQueryString(js, query, "engineParamsUrl"),
		Trace:           getBodyOrQueryString(js, query, "trace"),
		QuotaSet:        nil,
		EngineEntrance:  nil,
		UserId:          getBodyOrQueryString(js, query, "userId"),
	}
	//product默认值
	if in.Product == "" {
		in.Product = TC_DEFAULT_PRODUCT
	}

	//quotaSet解析
	quotaSetJson := js.Get("quotaSet")
	for _, key := range quotaSetJson.Keys() {
		in.Quota[key] = quotaSetJson.Get(key).ToInt64()
	}

	//engineEntrance解析
	engineEntranceJson := js.Get("engineEntrance")
	if engineEntranceJson != nil {
		in.EngineEntrance = map[string]string{}
		for _, key := range engineEntranceJson.Keys() {
			in.EngineEntrance[key] = engineEntranceJson.Get(key).ToString()

		}
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.InvokeTask(newCtx, in)
	if err != nil {
		//kratosErr := common.ToKratosError(err)
		//e2 := kratosErrors.FromError(kratosErr)
		//if e2.Reason == ErrorTaskAlreadyExist {
		//	return s.Result(out.Result.Reason, out.Result.Message, nil), err
		//}
		return nil, err
	}
	log.Infof("TCInvokeTask success, in:%+v, out:%+v", in, out)
	data := map[string]interface{}{}
	if out.Data != "" {
		//解析data为map返回
		var jsonData map[string]interface{}
		err = json.Unmarshal([]byte(out.Data), &jsonData)
		if err != nil {
			return nil, err
		}
		data["data"] = jsonData
	} else {
		data["data"] = out.DataUrl

	}
	//var jsonData map[string]interface{}
	//err = json.Unmarshal([]byte(out.Data), &jsonData)
	//if err != nil {
	//	log.Errorf("TCInvokeTask fail in json Unmarshal, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	//	return nil, err
	//}
	return s.Result(out.Result.Reason, out.Result.Message, data), nil
}

// 错误码适配
// code = InvalidParameter.TaskAlreadyExist
func (s *TaskService) TCSubmitTask(w http.ResponseWriter, r *http.Request) (any, error) {
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	log.Infof("TCSubmitTask, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)

	js := json.Get(body)
	in := &pb.SubmitTaskRequest{
		RequestId:       js.Get("trace").Get("requestId").ToString(),
		TaskId:          getBodyOrQueryString(js, query, "taskId"),
		JobId:           getBodyOrQueryString(js, query, "jobId"),
		Quota:           map[string]int64{},
		Product:         getBodyOrQueryString(js, query, "product"),
		EngineModel:     getBodyOrQueryString(js, query, "engineModel"),
		Tag:             getBodyOrQueryString(js, query, "tag"),
		EngineParams:    getBodyOrQueryString(js, query, "engineParams"),
		EngineParamsUrl: getBodyOrQueryString(js, query, "engineParamsUrl"),
		//消息回调地址：如mns:xxxx, http://xxxx
		Notify:        getBodyOrQueryString(js, query, "notify"),
		Trace:         getBodyOrQueryString(js, query, "trace"),
		UserData:      getBodyOrQueryString(js, query, "userData"),
		Affinity:      nil,
		Dependency:    nil,
		MigrateConfig: nil,
		UserId:        getBodyOrQueryString(js, query, "userId"),
		PipelineId:    getBodyOrQueryString(js, query, "pipelineId"),
	}
	//product默认值
	if in.Product == "" {
		in.Product = TC_DEFAULT_PRODUCT
	}
	//添加metaData信息,请求来源为tc
	metadata := map[string]string{}
	metadata[string(custom_types.MetadataKeyRequestSource)] = string(custom_types.RequestSourceTc)
	in.Metadata = metadata
	//tc下发的一些非必填参数
	// waitTimeoutMS int64 任务等待超时，单位为毫秒，提交任务开始计时，在等待超时范围内没有被调度则返回失败
	// maxRun integer 任务的最大执行次数，当maxRun>0时，如果任务异常退出（退出码非0）会被重启，最多重启maxRun-1次，当maxRun=0时，任务会被无限重试，直到调用stop接口停止任务
	//affinity json 包含required和preferred
	//overloadSet json 超跑配置
	//expectCostTime long 期望执行时间
	//unmigratable boolean 是否不可迁移 任务异常时，是否迁移任务；默认空或者false，当指定为true时，通知traffic任务异常
	//labels map[string]string 指定运行worker的label，比如要指定只能运行在包含arch=x86的worker上

	if in.RequestId == "" {
		in.RequestId = js.Get("requestId").ToString()
	}

	quotaSetJson := js.Get("quotaSet")
	for _, key := range quotaSetJson.Keys() {
		in.Quota[key] = quotaSetJson.Get(key).ToInt64()
	}
	if len(in.Quota) == 0 {
		log.Errorf("TCSubmitTask fail in quotaSet is empty, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
		return nil, common.ErrRequestInvalid
	}

	//获取任务依赖
	dependencyJson := js.Get("dependency").ToString()
	if dependencyJson != "" {
		dependencies := make([]*pb.Dependency, 0)
		// 解码JSON数据到taskDependencySpec变量
		err := json.Unmarshal([]byte(dependencyJson), &dependencies)
		if err != nil {
			log.Errorf("TCSubmitTask fail in json Unmarshal [dependencies], url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
			return nil, err
		}
		// 添加依赖到request
		in.Dependency = dependencies
	}

	//获取任务亲和性
	affinityJson := js.Get("affinity").ToString()
	if affinityJson != "" {
		k8sAffinity := &models.K8sAffinity{}
		// 解码JSON数据到taskDependencySpec变量
		err := json.Unmarshal([]byte(affinityJson), k8sAffinity)
		if err != nil {
			log.Errorf("TCSubmitTask fail in json Unmarshal [affinity], url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
			return nil, err
		}
		affinity := models.ConvertK8sToV1TaskAffinity(k8sAffinity)
		// 添加依赖到request
		in.Affinity = affinity
	}

	//获取任务迁移配置
	maxRun := js.Get("maxRun").ToInt32()
	unmigratable := js.Get("unmigratable").ToBool()
	//if maxRun > 0{
	//	in.MigrateConfig = &pb.MigrateConfig{
	//		MaxRuntime: maxRun,
	//		UnMigrate:  unmigratable,
	//	}
	//}
	//设置迁移配置
	in.MigrateConfig = models.GetMigrateConfigs(unmigratable, maxRun)

	//设置任务类型为非排队任务
	in.TaskType = v1.TaskType_NonQueueAsync
	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.SubmitTask(r.Context(), in)
	if err != nil {
		return nil, err
	}
	resultMap := map[string]any{"jobId": in.JobId}
	if in.TaskId != "" {
		resultMap["taskId"] = in.TaskId
	}
	if out != nil && out.Result != nil && out.Result.Metadata != nil {
		resultMap["metadata"] = out.Result.Metadata
		resultMap["data"] = out.Result.Metadata
	}
	result := s.Result(out.Result.Reason, out.Result.Message, resultMap)

	return result, nil
}

// 错误码适配
// 任务已停止:InvalidParameter.TaskNotExist
func (s *TaskService) TCCancelTask(w http.ResponseWriter, r *http.Request) (any, error) {
	log.Infof("TCCancelTask, url:%s, headers:%+v", r.URL.String(), r.Header)
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	log.Infof("TCCancelTask, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)

	in := &pb.CancelTaskRequest{
		RequestId: js.Get("trace").Get("requestId").ToString(),
		JobId:     getBodyOrQueryString(js, query, "jobId"),
		TaskId:    getBodyOrQueryString(js, query, "taskId"),
	}
	//一些非必填参数
	//trace json 链路日志，输出并透传到agent
	//index  int64 请求序号，默认0，表示同一个task不同请求的操作序号，非负

	if in.RequestId == "" {
		in.RequestId = js.Get("requestId").ToString()
	}

	if in.JobId == "" && in.TaskId != "" {
		in.JobId = in.TaskId
	}
	if in.TaskId == "" {
		in.TaskId = in.JobId
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.CancelTask(newCtx, in)
	if err != nil {
		return nil, err
	}

	return s.Result(out.Result.Reason, out.Result.Message, map[string]any{"data": out.Result.Metadata}), nil
}

// 错误码适配
// 资源不足code： TaskInsufficientResources
// 任务已启动：已适配，直接返回成功
func (s *TaskService) TCStartTask(w http.ResponseWriter, r *http.Request) (any, error) {
	log.Infof("TCStartTask, url:%s, headers:%+v", r.URL.String(), r.Header)
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	// 开启影子链路
	if r.Header.Get(header.HeaderShadow) != "" {
		query.Set("jobId", header.HeaderShadow+uuid.NewString())
	}

	log.Infof("TCStartTask, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)
	in := &pb.StartTaskRequest{
		RequestId:       getBodyOrQueryString(js, query, "requestId"),
		TaskId:          getBodyOrQueryString(js, query, "taskId"),
		JobId:           getBodyOrQueryString(js, query, "jobId"),
		Quota:           map[string]int64{},
		Product:         getBodyOrQueryString(js, query, "product"),
		EngineModel:     getBodyOrQueryString(js, query, "engineModel"),
		Tag:             getBodyOrQueryString(js, query, "tag"),
		EngineParams:    getBodyOrQueryString(js, query, "engineParams"),
		EngineParamsUrl: getBodyOrQueryString(js, query, "engineParamsUrl"),
		Notify:          getBodyOrQueryString(js, query, "notify"),
		Trace:           getBodyOrQueryString(js, query, "trace"),
		UserData:        getBodyOrQueryString(js, query, "userData"),
		Affinity:        nil,
		QuotaSet:        nil,
		UserId:          getBodyOrQueryString(js, query, "userId"),
	}
	//product默认值
	if in.Product == "" {
		in.Product = TC_DEFAULT_PRODUCT
	}
	//添加metaData信息,请求来源为tc
	metadata := map[string]string{}
	metadata[string(custom_types.MetadataKeyRequestSource)] = string(custom_types.RequestSourceTc)
	in.Metadata = metadata

	quotaSetJson := js.Get("quotaSet")
	for _, key := range quotaSetJson.Keys() {
		in.Quota[key] = quotaSetJson.Get(key).ToInt64()
	}

	//从trace中解析userId
	if in.Trace != "" && in.UserId == "" {

		traceMap := make(map[string]interface{})
		err = json.Unmarshal([]byte(in.Trace), &traceMap)
		if err != nil {
			log.Errorf("TCStartTask fail in json Unmarshal [trace], url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
		}
		if traceMap["userId"] != nil {
			in.UserId = traceMap["userId"].(string)
		}

	}

	//一些非必填参数
	// maxRun integer 任务的最大执行次数，当maxRun>0时，如果任务异常退出（退出码非0）会被重启，最多重启maxRun-1次，当maxRun=0时，任务会被无限重试，直到调用stop接口停止任务
	//index  int64 请求序号，默认0，表示同一个task不同请求的操作序号，非负
	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.StartTask(newCtx, in)
	if err != nil {
		return nil, err
	}

	return s.Result(out.Result.Reason, out.Result.Message, map[string]any{"data": out.Result.Metadata}), nil
}

// 错误码适配
// 任务已停止:InvalidParameter.TaskNotExist
func (s *TaskService) TCStopTask(w http.ResponseWriter, r *http.Request) (any, error) {
	log.Infof("TCStopTask, url:%s, headers:%+v", r.URL.String(), r.Header)
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	log.Infof("TCStopTask, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)

	in := &pb.StopTaskRequest{
		RequestId: js.Get("trace").Get("requestId").ToString(),
		JobId:     getBodyOrQueryString(js, query, "jobId"),
		TaskId:    getBodyOrQueryString(js, query, "taskId"),
	}
	//一些非必填参数
	//trace json 链路日志，输出并透传到agent
	//index  int64 请求序号，默认0，表示同一个task不同请求的操作序号，非负

	if in.RequestId == "" {
		in.RequestId = js.Get("requestId").ToString()
	}

	if in.JobId == "" && in.TaskId != "" {
		in.JobId = in.TaskId
	}
	if in.TaskId == "" {
		in.TaskId = in.JobId
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.StopTask(newCtx, in)
	if err != nil {
		return nil, err
	}

	return s.Result(out.Result.Reason, out.Result.Message, map[string]any{"data": out.Result.Metadata}), nil
}

// 无错误码适配
func (s *TaskService) TCUpdateTask(w http.ResponseWriter, r *http.Request) (any, error) {
	log.Infof("TCUpdateTask, url:%s, headers:%+v", r.URL.String(), r.Header)
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	log.Infof("TCUpdateTask, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)

	in := &pb.UpdateTaskRequest{
		RequestId:    js.Get("trace").Get("requestId").ToString(),
		JobId:        getBodyOrQueryString(js, query, "jobId"),
		TaskId:       getBodyOrQueryString(js, query, "taskId"),
		EngineParams: getBodyOrQueryString(js, query, "engineParams"),
		Quota:        map[string]int64{},
	}
	quotaSetJson := js.Get("quotaSet")
	for _, key := range quotaSetJson.Keys() {
		in.Quota[key] = quotaSetJson.Get(key).ToInt64()
	}

	//trace json 链路日志，输出并透传到agent

	if in.RequestId == "" {
		in.RequestId = js.Get("requestId").ToString()
	}

	if in.JobId == "" && in.TaskId != "" {
		in.JobId = in.TaskId
	}
	if in.TaskId == "" {
		in.TaskId = in.JobId
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.UpdateTask(newCtx, in)
	if err != nil {
		return nil, err
	}

	return s.Result(out.Result.Reason, out.Result.Message, map[string]any{"data": out.Result.Metadata}), nil
}
