package service

import (
	"github.com/golang/mock/gomock"
	"mpp/internal/taskmanager/mocks"
	"net/http"
	"strings"
	"testing"

	"mpp/internal/taskmanager/repository"

	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

var (
	grayConfigAddUrl                  = "http://101.133.233.140:80/grayconfig/add"
	grayConfigQueryUrl                = "http://101.133.233.140:80/grayconfig/query"
	grayConfigService                 *GrayConfigService
	addGrayConfigRequestBody          = "{\"configs\": [{\"tag\": \"example_tag\",\"product\": \"ExampleProduct\", \"engineModel\": \"ExampleEngineModel\", \"domain\": \"ExampleDomain\", \"app\": \"ExampleApp\", \"token\": \"ExampleToken\", \"edge_cpu\": 4, \"center_cpu\": 8, \"center_gpu\": 2, \"public_cpu\": 6, \"public_gpu\": 3, \"l2_cpu\": 5, \"center_cpu_2\": 9, \"l2_cpu_2\": 10}]}"
	grayConfigQueryRequestSuccessBody = "{\"product\": \"ExampleProduct\", \"engineModel\": \"ExampleEngineModel\", \"domain\": \"ExampleDomain\", \"app\": \"ExampleApp\", \"token\": \"ExampleToken\", \"tag\": \"ExampleTag\"}"
	grayConfigQueryRequestErrorBody   = "{\"product\": \"ExampleProduct\"}"
	grayConfigAddRequestBody          = `{
  "configs": [
    {
      "app": "jingkuan",
      "product": "ols",
      "engineModel": "livetranscode",
      "centerCpu": 0,
      "publicCpu": 0,
      "centerCpu2": 100,
      "centerGpu": 100,
      "canHoldByPubTag": false,
      "l2Cpu": 0,
      "token": "ld",
      "domain": "pulltest-sh-pre.alivecdn.com",
      "tag": "livetranscode_mpp_pre",
      "l2Cpu2": 0,
      "publicGpu": 0,
      "edgeCpu": 0
    }
  ],
  "product": "ols",
  "engineModel": "livetranscode"
}`
)

func TestGrayConfigService_Add(t *testing.T) {
	ctl := gomock.NewController(t)
	mockRepo := mocks.NewMockSwitchConfigRepo(ctl)
	defer ctl.Finish()
	grayConfigService = NewGrayConfigService(mockRepo, nil)
	mockRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockey.PatchConvey("GrayConfigAdd return success", t, func() {
		req, _ := http.NewRequest("POST", grayConfigAddUrl, strings.NewReader(grayConfigAddRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		//mockey.Mock((*TaskService).StartTask).Return(defaultSuccessResult, nil).Build()
		mockey.Mock(repository.SwitchConfigRepo.Insert).Return(nil).Build().Patch()
		_, err := grayConfigService.Add(nil, req)
		assert.Nil(t, err)
	})
}

func TestGrayConfigService_Query(t *testing.T) {
	ctl := gomock.NewController(t)
	mockRepo := mocks.NewMockSwitchConfigRepo(ctl)
	defer ctl.Finish()
	grayConfigService = NewGrayConfigService(mockRepo, nil)
	mockRepo.EXPECT().Query(gomock.Any(), gomock.Any()).Return([]*repository.SwitchConfig{
		{
			App:          "app",
			Domain:       "domain",
			EngineModel:  "engineModel",
			Extend:       "extend",
			Product:      "product",
			Token:        "token",
			SwitchConfig: "switchConfig",
			BackupConfig: "backupConfig",
		},
	}, nil).AnyTimes()
	mockey.PatchConvey("GrayConfigQuery return success", t, func() {
		req, _ := http.NewRequest("GET", grayConfigQueryUrl, strings.NewReader(grayConfigQueryRequestSuccessBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		_, err := grayConfigService.Query(nil, req)
		assert.Nil(t, err)
	})
	mockey.PatchConvey("GrayConfigQuery return error", t, func() {
		req, _ := http.NewRequest("GET", grayConfigQueryUrl, strings.NewReader(grayConfigQueryRequestErrorBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		_, err := grayConfigService.Query(nil, req)
		assert.NotNil(t, err)
	})
}

func TestGrayConfigService_Delete(t *testing.T) {
	ctl := gomock.NewController(t)
	mockRepo := mocks.NewMockSwitchConfigRepo(ctl)
	defer ctl.Finish()
	grayConfigService = NewGrayConfigService(mockRepo, nil)
	mockRepo.EXPECT().Delete(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockey.PatchConvey("GrayConfigDelete return success", t, func() {
		req, _ := http.NewRequest("POST", grayConfigQueryUrl, strings.NewReader(addGrayConfigRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		_, err := grayConfigService.Delete(nil, req)
		assert.Nil(t, err)
	})
}

func TestGrayConfigService_Update(t *testing.T) {
	ctl := gomock.NewController(t)
	mockRepo := mocks.NewMockSwitchConfigRepo(ctl)
	defer ctl.Finish()
	grayConfigService = NewGrayConfigService(mockRepo, nil)
	mockRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockey.PatchConvey("GrayConfigUpdate return success", t, func() {
		req, _ := http.NewRequest("POST", grayConfigQueryUrl, strings.NewReader(addGrayConfigRequestBody))
		req.Header.Add("User-Agent", "my-app/0.0.1")
		_, err := grayConfigService.Update(nil, req)
		assert.Nil(t, err)
	})
}
