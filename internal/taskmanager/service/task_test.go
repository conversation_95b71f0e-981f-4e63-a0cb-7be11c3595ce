package service

import (
	"context"
	"mpp/internal/taskmanager/biz/tasker"
	"mpp/internal/taskmanager/models"
	"testing"
	"time"

	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	v1 "proto.mpp/api/common/v1"
	pb "proto.mpp/api/taskmanager/v1"
)

func TestTaskService_BizTaskToDetail_DataHandling(t *testing.T) {
	// 测试字符串类型的Data字段
	mockey.PatchConvey("BizTaskToDetail with string Data", t, func() {
		task := MockNewTaskWithResultData("test-string-data")
		detail, err := taskService.BizTaskToDetail(task)
		assert.Nil(t, err)
		assert.Equal(t, "test-string-data", detail.Result.Data)
	})

	// 测试map类型的Data字段
	mockey.PatchConvey("BizTaskToDetail with map Data", t, func() {
		mapData := map[string]interface{}{
			"key1": "value1",
			"key2": 123,
			"key3": true,
		}
		task := MockNewTaskWithResultData(mapData)
		detail, err := taskService.BizTaskToDetail(task)
		assert.Nil(t, err)
		assert.NotEmpty(t, detail.Result.Data)
		// 验证结果包含map中的内容
		assert.Contains(t, detail.Result.Data, "key1")
		assert.Contains(t, detail.Result.Data, "value1")
	})

	// 测试数组类型的Data字段
	mockey.PatchConvey("BizTaskToDetail with array Data", t, func() {
		arrayData := []interface{}{"item1", "item2", 123}
		task := MockNewTaskWithResultData(arrayData)
		detail, err := taskService.BizTaskToDetail(task)
		assert.Nil(t, err)
		assert.NotEmpty(t, detail.Result.Data)
		// 验证结果包含数组中的内容
		assert.Contains(t, detail.Result.Data, "item1")
		assert.Contains(t, detail.Result.Data, "item2")
	})

	// 测试数字类型的Data字段
	mockey.PatchConvey("BizTaskToDetail with number Data", t, func() {
		task := MockNewTaskWithResultData(12345)
		detail, err := taskService.BizTaskToDetail(task)
		assert.Nil(t, err)
		assert.Equal(t, "12345", detail.Result.Data)
	})

	// 测试布尔类型的Data字段
	mockey.PatchConvey("BizTaskToDetail with boolean Data", t, func() {
		task := MockNewTaskWithResultData(true)
		detail, err := taskService.BizTaskToDetail(task)
		assert.Nil(t, err)
		assert.Equal(t, "true", detail.Result.Data)
	})

	// 测试nil类型的Data字段
	mockey.PatchConvey("BizTaskToDetail with nil Data", t, func() {
		task := MockNewTaskWithResultData(nil)
		detail, err := taskService.BizTaskToDetail(task)
		assert.Nil(t, err)
		// 当Data为nil时，detail.Result.Data应该为空字符串
		assert.Equal(t, "", detail.Result.Data)
	})
}

// MockNewTaskWithResultData 创建一个带Result.Data的Task用于测试
func MockNewTaskWithResultData(data interface{}) *models.Task {
	task := &models.Task{
		TaskId:         "test-task-id",
		Product:        "mpp",
		EngineModel:    "test-engine-model",
		Tag:            models.DefaultTaskTag,
		UserId:         "test-user",
		TaskType:       models.StreamTaskType,
		BizStatus:      models.TaskStateActive,
		InternalStatus: models.TaskStateActive,
		GmtCreate:      time.Now(),
		LastModified:   time.Now(),
	}

	task.Result = &models.TaskResult{
		Data:     data,
		DataUrl:  "test-data-url",
		Code:     200,
		Reason:   "test-reason",
		Message:  "test-message",
		Duration: 1000,
	}

	return task
}

func TestTaskService_InvokeTask(t *testing.T) {
	//返回成功
	mockey.PatchConvey("InvokeTask and  return success", t, func() {
		mockey.Mock((*tasker.SimpleTaskBiz).Invoke).Return(&models.SimpleTaskResult{
			TaskId:  "001-unit-test",
			DataUrl: "test-type-url",
		}, nil).Build()
		_, err := taskService.InvokeTask(context.Background(), &pb.InvokeTaskRequest{
			TaskId:    "002-unit-test",
			RequestId: "002-unit-test-requestid",
		})
		assert.Nil(t, err)
	})
}

func TestTaskService_StartTaskLegacy(t *testing.T) {

	//返回成功
	mockey.PatchConvey("StartTaskLegacy and  return success", t, func() {
		mockey.Mock((*tasker.CommonTaskBiz).StartTask).Return(nil).Build()
		_, err := taskService.StartTaskLegacy(context.Background(), &pb.StartTaskRequest{
			TaskId:    "002-unit-test",
			RequestId: "002-unit-test-requestid",
		})
		assert.Nil(t, err)
	})
}

func TestTaskService_StopTaskLegacy(t *testing.T) {
	//返回成功
	mockey.PatchConvey("StopTaskLegacy and  return success", t, func() {
		mockey.Mock((*tasker.BatchTaskBiz).CancelTask).Return(nil).Build()
		_, err := taskService.StopTaskLegacy(context.Background(), &pb.StopTaskRequest{
			TaskId:    "003-unit-test",
			JobId:     "003-unit-test",
			RequestId: "003-unit-test-requestid",
		})
		assert.Nil(t, err)
	})
}

func TestTaskService_UpdateTaskLegacy(t *testing.T) {
	//返回失败
	mockey.PatchConvey("UpdateTaskLegacy and  return failed", t, func() {
		_, err := taskService.UpdateTaskLegacy(context.Background(), &pb.UpdateTaskRequest{
			TaskId:    "003-unit-test",
			RequestId: "003-unit-test-requestid",
		})
		//unsupported tasker type for UpdateTask
		assert.NotNil(t, err)
	})
}

func TestTaskService_UpdateTaskStatus(t *testing.T) {
	//返回成功
	mockey.PatchConvey("UpdateTaskStatus and  return success", t, func() {
		mockey.Mock((*tasker.CommonTaskBiz).UpdateTaskStatus).Return(nil).Build()
		_, err := taskService.UpdateTaskStatus(context.Background(), &pb.UpdateTaskStatusRequest{
			TaskId:    "002-unit-test",
			BizStatus: 2,
			RequestId: "002-unit-test-requestid",
		})
		//unsupported tasker type for UpdateTask
		assert.Nil(t, err)
	})
}

func TestTaskService_GetRunningTasks(t *testing.T) {
	//返回成功
	mockey.PatchConvey("GetRunningTasks and  return success", t, func() {
		mockey.Mock((*tasker.CommonTaskBiz).GetRunningTasks).Return(nil, nil).Build()
		engineModels := []string{"mpp-integration-test-engine", "mpp-integration-test-engine2"}
		_, err := taskService.GetRunningTasks(context.Background(), 2, engineModels)
		//unsupported tasker type for UpdateTask
		assert.Nil(t, err)
	})
}

//func TestTaskService_GetTask(t *testing.T) {
//	//返回成功
//	mockey.PatchConvey("ListTask and  return success", t, func() {
//		task1 := MockNewTask(v1.TaskType_Stream)
//		mockey.Mock((*tasker.CommonTaskBiz).GetTask).Return(task1, nil).Build()
//		_, err := taskService.GetTask(context.Background(), &pb.GetTaskRequest{
//			TaskId:    task1.TaskId,
//			RequestId: "002-unit-test-requestid",
//		})
//		//unsupported tasker type for UpdateTask
//		assert.Nil(t, err)
//	})
//}

func TestTaskService_ListTask(t *testing.T) {
	//返回成功
	mockey.PatchConvey("ListTask and  return success", t, func() {
		task1 := MockNewTask(v1.TaskType_Stream)
		task2 := MockNewTask(v1.TaskType_Stream)
		mockey.Mock((*tasker.CommonTaskBiz).GetTask).Return(task1, nil).Build()
		_, err := taskService.ListTask(context.Background(), &pb.ListTaskRequest{
			JobIds:    []string{task1.TaskId, task2.TaskId},
			RequestId: "002-unit-test-requestid",
		})
		//unsupported tasker type for UpdateTask
		assert.Nil(t, err)
	})
}

func TestTaskService_ListRunningTaskIds(t *testing.T) {
	//返回成功
	mockey.PatchConvey("ListRunningTaskIds and  return success", t, func() {
		task1 := MockNewTask(v1.TaskType_Stream)
		task2 := MockNewTask(v1.TaskType_Stream)
		mockey.Mock((*tasker.CommonTaskBiz).GetRunningTaskIds).Return([]*models.Task{task1, task2}, nil).Build()
		_, err := taskService.ListRunningTaskIds(context.Background(), &pb.ListRunningTaskIdsRequest{
			RequestId:      "002-unit-test-requestid",
			NeedTaskDetail: true,
			WorkerIp:       "127.0.0.1",
		})
		//unsupported tasker type for UpdateTask
		assert.Nil(t, err)
	})
}

//func TestTaskService_MigrateTask(t *testing.T) {
//
//	task1 := MockNewTask(v1.TaskType_Stream)
//	task2 := MockNewTask(v1.TaskType_Stream)
//	taskIds := []string{task1.TaskId, task2.TaskId}
//	workerIds := []string{"002-unit-test-worker", "002-unit-test-worker2"}
//	workerIps := []string{"127.0.0.1", "*********"}
//	mockey.PatchConvey("MigrateTask and migrate by taskIds", t, func() {
//		mockey.Mock((*tasker.CommonTaskBiz).MigrateTask).Return(nil).Build()
//		_, err := taskService.MigrateTask(context.Background(), &pb.MigrateTaskRequest{
//			TaskIds:   taskIds,
//			RequestId: "002-unit-test-requestid",
//		})
//		//unsupported tasker type for UpdateTask
//		assert.Nil(t, err)
//	})
//
//	mockey.PatchConvey("MigrateTask and  migrate by workerId", t, func() {
//		mockey.Mock((*tasker.CommonTaskBiz).MigrateTaskByWorker).Return(taskIds, nil).Build()
//		_, err := taskService.MigrateTask(context.Background(), &pb.MigrateTaskRequest{
//			RequestId: "002-unit-test-requestid",
//			WorkIds:   workerIds,
//			WorkIps:   workerIps,
//		})
//		//unsupported tasker type for UpdateTask
//		assert.Nil(t, err)
//	})
//
//	mockey.PatchConvey("MigrateTask and  migrate by workerIp", t, func() {
//		taskIds := []string{"002-unit-test", "002-unit-test2"}
//		mockey.Mock((*tasker.CommonTaskBiz).MigrateTaskByWorker).Return(taskIds, nil).Build()
//		_, err := taskService.MigrateTask(context.Background(), &pb.MigrateTaskRequest{
//			RequestId: "002-unit-test-requestid",
//			WorkIps:   []string{"127.0.0.1", "*********"},
//		})
//		//unsupported tasker type for UpdateTask
//		assert.Nil(t, err)
//	})
//}

func TestTaskService_CompleteTask(t *testing.T) {
	mockey.PatchConvey("CompleteTask and return success", t, func() {
		mockey.Mock((*tasker.CommonTaskBiz).CompleteTask).Return(true, nil).Build()
		_, err := taskService.CompleteTask(context.Background(), &pb.CompleteTaskRequest{
			TaskId:    "002-unit-test",
			RequestId: "002-unit-test-requestid",
		})
		//unsupported tasker type for UpdateTask
		assert.Nil(t, err)
	})
}

func TestTaskService_CheckStartParams(t *testing.T) {
	mockey.PatchConvey("CheckStartParams and check default setting", t, func() {
		req := &pb.StartTaskRequest{
			RequestId: "002-unit-test-requestid",
			TaskId:    "002-unit-test",
			Product:   "mpp",
		}
		err := taskService.checkStartParams(context.Background(), req)
		//unsupported tasker type for UpdateTask
		assert.Nil(t, err)
		assert.NotNil(t, req.TaskId)
		assert.Equal(t, models.DefaultTaskTag, req.Tag)
	})
}

func MockNewTask(taskType v1.TaskType) *models.Task {

	task := &models.Task{
		TaskId:         models.NewTaskID(taskType),
		Product:        "mpp",
		EngineModel:    "mpp-integration-test-engine",
		Tag:            models.DefaultTaskTag,
		UserId:         "test-user",
		TaskType:       models.TaskType(taskType),
		BizStatus:      models.TaskStateActive,
		InternalStatus: models.TaskStateActive,
		GmtCreate:      time.Now(),
		LastModified:   time.Now(),
		Notify:         "mns://1426105496228119.mns.cn-shanghai.aliyuncs.com/queues/mpp-notify-test,mns://1426105496228119.mns.cn-shanghai.aliyuncs.com/queues/mpp-notify-test?dequeued=inner",
	}
	task.Worker = &models.Worker{
		Id:   "002-unit-test-worker",
		Ip:   "127.0.0.1",
		Port: 10882,
	}
	task.RequestResource = &models.RequestResource{
		Quota: map[string]int64{
			"cpu": 1,
		},
	}
	return task
}
