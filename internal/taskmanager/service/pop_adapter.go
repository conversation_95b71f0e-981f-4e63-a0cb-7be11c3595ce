package service

import (
	"context"
	"io"
	"net/http"

	"mpp/internal/taskmanager/models"
	"mpp/pkg/header"
	"mpp/pkg/utils"

	pb "proto.mpp/api/taskmanager/v1"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
)

const ()

// 适配pop的接口
func (s *TaskService) POPResult(code string, message string, requestId string, rst map[string]interface{}) any {
	if rst == nil {
		rst = make(map[string]interface{})
	}
	rst["success"] = true
	rst["code"] = code
	rst["message"] = message
	rst["httpStatusCode"] = http.StatusOK
	rst["requestId"] = requestId

	if rst["code"] == "" || rst["code"] == "ok" {
		rst["code"] = "Success"
	}

	return rst
}

func (s *TaskService) RestultWithRequestIdByCtx(ctx context.Context) map[string]any {
	return map[string]any{"requestId": utils.GetRequestIdFromContext(ctx)}
}

func (s *TaskService) POPStartTask(w http.ResponseWriter, r *http.Request) (any, error) {
	log.Infof("PopStartTask, url:%s, headers:%+v", r.URL.String(), r.Header)
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	// 开启影子链路
	if r.Header.Get(header.HeaderShadow) != "" {
		query.Set("jobId", header.HeaderShadow+uuid.NewString())
	}

	log.Infof("PopStartTask, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)
	in := &pb.StartTaskRequest{
		RequestId:       getBodyOrQueryString(js, query, "requestId"),
		TaskId:          getBodyOrQueryString(js, query, "taskId"),
		JobId:           getBodyOrQueryString(js, query, "jobId"),
		Quota:           map[string]int64{},
		Product:         getBodyOrQueryString(js, query, "product"),
		EngineModel:     getBodyOrQueryString(js, query, "engineModel"),
		Tag:             getBodyOrQueryString(js, query, "tag"),
		EngineParams:    getBodyOrQueryString(js, query, "engineParams"),
		EngineParamsUrl: getBodyOrQueryString(js, query, "engineParamsUrl"),
		Notify:          getBodyOrQueryString(js, query, "notify"),
		Trace:           getBodyOrQueryString(js, query, "trace"),
		UserData:        getBodyOrQueryString(js, query, "userData"),
		Affinity:        nil,
		QuotaSet:        map[string]int64{},
		UserId:          getBodyOrQueryString(js, query, "userId"),
	}
	quotaJson := js.Get("quota")
	for _, key := range quotaJson.Keys() {
		in.Quota[key] = quotaJson.Get(key).ToInt64()
	}

	quotaSetJson := js.Get("quotaSet")
	for _, key := range quotaSetJson.Keys() {
		in.QuotaSet[key] = quotaSetJson.Get(key).ToInt64()
	}
	affinityJson := getBodyOrQueryString(js, query, "affinity")
	if affinityJson != "" {
		in.Affinity = models.StringToV1TaskAffinity(affinityJson)
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.StartTask(newCtx, in)
	if err != nil {
		// 返回requestId
		return s.RestultWithRequestIdByCtx(newCtx), err
	}

	log.Infof("PopStartTask success, out:%+v", out)
	return s.POPResult(out.Result.Reason, out.Result.Message, in.RequestId, map[string]any{"jobId": in.JobId}), nil
}

func (s *TaskService) POPStopTask(w http.ResponseWriter, r *http.Request) (any, error) {
	log.Infof("POPStopTask, url:%s, headers:%+v", r.URL.String(), r.Header)
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	log.Infof("POPStopTask, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
	js := json.Get(body)

	in := &pb.StopTaskRequest{
		RequestId: getBodyOrQueryString(js, query, "requestId"),
		JobId:     getBodyOrQueryString(js, query, "jobId"),
		TaskId:    getBodyOrQueryString(js, query, "taskId"),
	}

	if in.JobId == "" && in.TaskId != "" {
		in.JobId = in.TaskId
	}
	if in.TaskId == "" {
		in.TaskId = in.JobId
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.StopTask(newCtx, in)
	if err != nil {
		// 返回requestId
		return s.RestultWithRequestIdByCtx(newCtx), err
	}

	log.Infof("POPStopTask success, out:%+v", out)
	return s.POPResult(out.Result.Reason, out.Result.Message, in.RequestId, map[string]any{"jobId": in.JobId}), nil
}
