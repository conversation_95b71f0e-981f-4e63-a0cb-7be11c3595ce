package service

const (
	submitPath = "/job/async/submit"
	cancelPath = "/job/async/cancel"
	queryPath  = "/job/async/query"
)

// meddia灰度适配方法，在调度前通过三元组判断算子类型，对不同算子采用不同灰度策略
//func (s *TaskService) AIStdSubmitTask(w http.ResponseWriter, r *http.Request) (any, error) {
//	// 1. 解析请求参数
//	// 2. 判断是否在路由表中
//	// 3. 调用灰度策略：启动项配置或数据库配置，判定新调度还是老调度
//	// 4. 如果是老调度则原参数透传到老的，如果是新调度则转为标准化参数往下调用
//	log.Infof("AiStdSubmitTask submit, url:%s, headers:%+v", r.URL.String(), r.Header)
//	query := r.URL.Query()
//	var bodyStr string
//	body, err := io.ReadAll(r.Body)
//	if body != nil {
//		bodyStr = string(body)
//	}
//	log.Infof("AiStdSubmitTask message, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
//	js := json.Get(body)
//	jobId := getBodyOrQueryString(js, query, "jobId")
//	product := getBodyOrQueryString(js, query, "product")
//	engineModel := getBodyOrQueryString(js, query, "engineModel")
//	tag := getBodyOrQueryString(js, query, "tag")
//	if jobId == "" || product == "" || engineModel == "" {
//		return nil, errors.Errorf(stdhttp.StatusBadRequest, InvalidParam, "jobId or product or engineModel is empty")
//	}
//	newCtx := utils.SetCustomRequestId(r.Context(), getBodyOrQueryString(js, query, "requestId"))
//	routeData := s.router.GetRouteData(router.AIKeySuffix+jobId, newCtx)
//	// 在路由表中,是老任务,且当前是运行态则透传往记录的系统
//	if routeData != nil && routeData.Status == TaskStatusRunning {
//		if routeData.System == NewSystem {
//			result, err := s.StdSubmitTask(w, r)
//			if err != nil {
//				log.Errorf("AIStdSubmitTask start task in new system error, taskId:%s, url:%s, err:%v", jobId, r.URL.String(), err)
//				//新调度失败，使用老调度兜底
//				return s.operateOldAISystemTask(newCtx, body, query, r, jobId, submitPath)
//			}
//			return result, err
//		} else {
//			// 老调度
//			return s.operateOldAISystemTask(newCtx, body, query, r, jobId, submitPath)
//		}
//	}
//
//	// 在路由表中且是停止态 或者 不在路由表中，是新任务     由灰度比例路由到目的系统
//	log.Infof("AiStdSubmitTask router setting,taskId:%s ,product:%s, engineModel:%s, tag:%s", jobId, product, engineModel, tag)
//	if s.router.GetMediaaiRouteHitResult(product, engineModel, tag) == 1 {
//		result, err := s.StdSubmitTask(w, r)
//		if err != nil {
//			log.Errorf("AIStdSubmitTask start task in new system error, taskId:%s, url:%s, err:%v", jobId, r.URL.String(), err)
//			//新调度失败，使用老调度兜底
//			return s.operateOldAISystemTask(newCtx, body, query, r, jobId, submitPath)
//		}
//		// 新架构调度成功，记录路由表
//		s.router.SetRouteData(router.AIKeySuffix+jobId, NewSystem, TaskStatusRunning, context.Background())
//		log.Infof("AIStdSubmitTask start task in new system end, taskId:%s, url:%s", jobId, r.URL.String())
//		return result, err
//	} else {
//		// 老调度
//		return s.operateOldAISystemTask(newCtx, body, query, r, jobId, submitPath)
//	}
//}
//
//func (s *TaskService) AiStdCancelTask(w http.ResponseWriter, r *http.Request) (any, error) {
//	// 1. 解析请求参数
//	// 2. 判断是否在路由表中
//	// 3. 调用灰度策略：启动项配置或数据库配置，判定新调度还是老调度
//	// 4. 如果是老调度则原参数透传到老的，如果是新调度则转为标准化参数往下调用
//	log.Infof("AiStdCancelTask submit, url:%s, headers:%+v", r.URL.String(), r.Header)
//	query := r.URL.Query()
//	var bodyStr string
//	body, err := io.ReadAll(r.Body)
//	if body != nil {
//		bodyStr = string(body)
//	}
//	log.Infof("AiStdCancelTask message, url:%s, headers:%+v, body:%s, err:%v", r.URL.String(), r.Header, bodyStr, err)
//	js := json.Get(body)
//	jobId := getBodyOrQueryString(js, query, "jobId")
//	product := getBodyOrQueryString(js, query, "product")
//	userId := getBodyOrQueryString(js, query, "userId")
//	if jobId == "" || product == "" || userId == "" {
//		return nil, errors.Errorf(stdhttp.StatusBadRequest, InvalidParam, "jobId or product or userId is empty")
//	}
//	newCtx := utils.SetCustomRequestId(r.Context(), getBodyOrQueryString(js, query, "requestId"))
//	routeData := s.router.GetRouteData(router.AIKeySuffix+jobId, newCtx)
//	// 在路由表中,是老任务,且当前是运行态则透传往记录的系统
//	if routeData != nil {
//		if routeData.System == NewSystem {
//			result, err := s.StdCancelTask(w, r)
//			if err != nil {
//				log.Errorf("AiStdCancelTask start task in new system error, taskId:%s, url:%s, err:%v", jobId, r.URL.String(), err)
//				//新调度失败，使用老调度兜底
//				return s.operateOldAISystemTask(newCtx, body, query, r, jobId, cancelPath)
//			}
//			return result, err
//		} else {
//			// 老调度
//			return s.operateOldAISystemTask(newCtx, body, query, r, jobId, cancelPath)
//		}
//	} else {
//		// 老调度
//		return s.operateOldAISystemTask(newCtx, body, query, r, jobId, cancelPath)
//	}
//}
////
////func (s *TaskService) AiStdQueryTask(w http.ResponseWriter, r *http.Request) (any, error) {
////
////	return nil, nil
////}
////func (s *TaskService) operateOldAISystemTask(ctx context.Context, body []byte, query url.Values, r *http.Request, taskId, operateType string) (any, error) {
////	log.Infof("operateOldAISystemTask operate task in old system, operateType:%s, taskId:%s", operateType, taskId)
////	log.Infof("operateOldAISystemTask message, url:%s, headers:%+v, body:%s", r.URL.String(), r.Header, body)
////	// 老调度系统
////	js := json.Get(body)
////	taskStatus := TaskStatusRunning
////	path := submitPath
////	if operateType == cancelPath {
////		taskStatus = TaskStatusStopped
////		path = cancelPath
////	}
////
////	urlStr := httpPrefix + s.router.GetOldAiSystemHost() + path + "?" + query.Encode()
////	log.Infof("operateOldAISystemTask urlStr:%s", urlStr)
////	reqURL, err := url.Parse(urlStr)
////	if err != nil {
////		log.Errorf("operateOldAISystemTask construct reqURL fail, taskId:%s, err:%v", taskId, err)
////		return nil, err
////	}
////
////	reqURL.RawQuery = r.URL.Query().Encode()
////
////	req, err := stdhttp.NewRequest("POST", reqURL.String(), bytes.NewReader(body))
////	if err != nil {
////		log.Errorf("operateOldAISystemTask construct http req fail,taskId:%s, err:%v", taskId, err)
////		return nil, err
////	}
////	req.Header.Set("Content-Type", "application/json")
////	req.Header.Set("Authorization", r.Header.Get("Authorization"))
////	client := &stdhttp.Client{}
////	resp, err := client.Do(req)
////	log.Infof("operateOldAISystemTask, operateType:%s, taskId:%s, resp:%+v", operateType, taskId, resp)
////	if err != nil {
////		log.Errorf("operateOldAISystemTask http request fail, operateType:%s, taskId:%s, error:%+v, resp11:%+v", operateType, taskId, err.Error(), resp)
////		return nil, err
////	}
////	// 判断老系统的返回码是否成功，成功则记录路由表
////	if resp.StatusCode == 200 {
////		s.router.SetRouteData(router.AIKeySuffix+getBodyOrQueryString(js, query, "jobId"), OldSystem, taskStatus, ctx)
////		log.Infof("operateOldAISystemTask operate task in old system success, operateType:%s, taskId:%s", operateType, taskId)
////	}
////	// 成功与否都直接透传返回老系统的响应结果
////	var bodyByte []byte
////	if resp.Body != nil {
////		bodyByte, err = io.ReadAll(resp.Body)
////		if err != nil {
////			log.Errorf("operateOldAISystemTask read body fail when send apigateway request, taskId:%s ,error:%+v", taskId, err.Error())
////			return nil, err
////		}
////	}
////	var data map[string]string
////	err = json.Unmarshal(bodyByte, &data)
////	if err != nil {
////		return nil, err
////	}
////	log.Infof("operateOldAISystemTask operate task in old system end, operateType:%s, taskId:%s, data:%+v", operateType, taskId, data)
////	return data, nil
////}
