package service

import (
	"fmt"
	"io"
	"net/http"

	lc "mpp/internal/taskmanager/models/livetranscode"
	"mpp/internal/taskmanager/repository"
	"mpp/pkg/utils"

	"github.com/go-kratos/kratos/v2/log"
)

const TAG_TRANSCODE = "livetranscode_pub"

func (s *GrayConfigService) ResultError(err error) any {
	return map[string]string{
		"code":    "InternalError",
		"message": err.Error(),
	}
}

func (s *GrayConfigService) ResultSuccess() any {
	return s.Result("Success", "OK", nil)
}
func (s *GrayConfigService) Result(code string, message string, rst map[string]interface{}) any {
	if rst == nil {
		rst = make(map[string]interface{})
	}
	rst["code"] = code
	rst["message"] = message

	if rst["code"] == "" || rst["code"] == "ok" {
		rst["code"] = "Success"
	}

	return rst
}

// GrayConfigService 直播转码切流配置
type GrayConfigService struct {
	repo repository.SwitchConfigRepo
	log  *log.Helper
}

func NewGrayConfigService(repo repository.SwitchConfigRepo, logger log.Logger) *GrayConfigService {
	return &GrayConfigService{repo: repo, log: log.NewHelper(logger)}
}

func (s *GrayConfigService) Add(w http.ResponseWriter, r *http.Request) (any, error) {
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return s.ResultError(fmt.Errorf("InvalidParameter.LiveParams")), fmt.Errorf("InvalidParameter.LiveParams")
	}
	var req lc.GrayConfigRequest
	err = json.Unmarshal(body, &req)
	if err != nil {
		return s.ResultError(err), err
	}
	newCtx := utils.SetCustomRequestId(r.Context(), "")
	switchConfigs := make([]*repository.SwitchConfig, 0)
	for _, config := range req.Configs {
		if config.App == "" || config.Domain == "" || config.EngineModel == "" || config.Product == "" || config.Token == "" || config.Tag == "" {
			return s.ResultError(fmt.Errorf("InvalidParameter.LiveParams")), fmt.Errorf("InvalidParameter.LiveParams")
		}
		switchConfigStr, backupConfigStr, err := convertConfigToStr(config)
		if err != nil {
			return s.ResultError(err), err
		}
		switchConfigs = append(switchConfigs, &repository.SwitchConfig{
			App:          config.App,
			Domain:       config.Domain,
			EngineModel:  config.EngineModel,
			Product:      config.Product,
			Token:        config.Token,
			Tag:          config.Tag,
			BackupConfig: backupConfigStr,
			SwitchConfig: switchConfigStr,
		})
	}
	for _, config := range switchConfigs {
		err := s.repo.Insert(newCtx, config)
		if err != nil {
			return s.ResultError(err), err
		}
	}
	return s.ResultSuccess(), nil
}

func (s *GrayConfigService) Delete(w http.ResponseWriter, r *http.Request) (any, error) {
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return s.ResultError(err), err
	}
	var req lc.GrayConfigRequest
	err = json.Unmarshal(body, &req)
	newCtx := utils.SetCustomRequestId(r.Context(), "")
	if len(req.Configs) != 1 {
		return s.ResultError(fmt.Errorf("InvalidParameter.LiveParams")), fmt.Errorf("InvalidParameter.LiveParams")
	}
	config := req.Configs[0]
	if config.App == "" || config.Domain == "" || config.EngineModel == "" || config.Product == "" || config.Token == "" || config.Tag == "" {
		return s.ResultError(fmt.Errorf("InvalidParameter.LiveParams")), fmt.Errorf("InvalidParameter.LiveParams")
	}
	switchConfig := &repository.SwitchConfig{
		App:         config.App,
		Domain:      config.Domain,
		EngineModel: config.EngineModel,
		Product:     config.Product,
		Token:       config.Token,
		Tag:         config.Tag,
	}
	s.repo.Delete(newCtx, switchConfig)
	return s.ResultSuccess(), nil
}

func (s *GrayConfigService) Update(w http.ResponseWriter, r *http.Request) (any, error) {
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return s.ResultError(err), err
	}
	var req lc.GrayConfigRequest
	err = json.Unmarshal(body, &req)
	newCtx := utils.SetCustomRequestId(r.Context(), "")
	if len(req.Configs) != 1 {
		return s.ResultError(fmt.Errorf("InvalidParameter.LiveParams")), fmt.Errorf("InvalidParameter.LiveParams")
	}
	config := req.Configs[0]
	if config.Tag == "" || config.App == "" || config.Domain == "" || config.EngineModel == "" || config.Product == "" || config.Token == "" {
		return s.ResultError(fmt.Errorf("InvalidParameter.Tag")), fmt.Errorf("InvalidParameter.Tag")
	}
	switchConfigStr, backupConfigStr, err := convertConfigToStr(config)
	if err != nil {
		return s.ResultError(err), err
	}
	switchConfig := &repository.SwitchConfig{
		App:          config.App,
		Domain:       config.Domain,
		EngineModel:  config.EngineModel,
		Product:      config.Product,
		Token:        config.Token,
		Tag:          config.Tag,
		BackupConfig: backupConfigStr,
		SwitchConfig: switchConfigStr,
	}
	err = s.repo.Insert(newCtx, switchConfig)
	if err != nil {
		return s.ResultError(err), err
	}
	return s.ResultSuccess(), nil
}

func (s *GrayConfigService) Query(w http.ResponseWriter, r *http.Request) (any, error) {
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return s.ResultError(err), err
	}
	var req lc.GrayConfigQueryRequest
	err = json.Unmarshal(body, &req)
	newCtx := utils.SetCustomRequestId(r.Context(), "")
	if req.Tag == "" || req.Product == "" || req.Domain == "" || req.EngineModel == "" {
		return s.ResultError(fmt.Errorf("InvalidParameter.Tag")), fmt.Errorf("InvalidParameter.Tag")
	}
	switchConfigs, err := s.repo.Query(newCtx, &repository.SwitchConfig{
		Product:     req.Product,
		EngineModel: req.EngineModel,
		Tag:         req.Tag,
		Domain:      req.Domain,
		Token:       req.Token,
		App:         req.App,
	})
	if err != nil {
		return s.ResultError(err), err
	}
	rst := make(map[string]interface{})
	rst["configs"] = switchConfigs
	return s.Result("Success", "OK", rst), nil
}

func convertConfigToStr(config lc.GrayConfigRequestConfig) (string, string, error) {
	switchConfig := lc.SwitchConfig{}
	backupConfig := lc.BackupConfig{}
	var switchConfigStr, backupConfigStr string
	switchConfig.EdgeCpu = config.EdgeCpu
	switchConfig.CenterCpu = config.CenterCpu
	switchConfig.CenterGpu = config.CenterGpu
	switchConfig.PublicCpu = config.PublicCpu
	switchConfig.PublicGpu = config.PublicGpu
	switchConfig.L2Cpu = config.L2Cpu
	switchConfig.CenterCpu2 = config.CenterCpu2
	switchConfig.L2Cpu2 = config.L2Cpu2
	byteSwitchConfig, err := json.Marshal(&switchConfig)
	if err != nil {
		return switchConfigStr, backupConfigStr, err
	}
	backupConfig.CanHoldByPubTag = config.CanHoldByPubTag
	backupConfig.Order = config.Order
	byteSafetyConfig, err := json.Marshal(&backupConfig)
	if err != nil {
		return switchConfigStr, backupConfigStr, err
	}
	switchConfigStr, backupConfigStr = string(byteSwitchConfig), string(byteSafetyConfig)
	return switchConfigStr, backupConfigStr, nil
}
