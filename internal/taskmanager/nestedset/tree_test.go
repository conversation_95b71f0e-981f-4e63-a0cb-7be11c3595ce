package nestedset

import (
	"database/sql"
	"github.com/stretchr/testify/assert"
	"testing"
)

/**
 * @Author: qinxin
 * @Date: 2024/9/26 14:32
 * @Desc:
 * @Version 1.0
 */

// TestInitTree 测试 initTree 方法
// 测试用例：
// TC1 - 空切片
// TC2 - 包含单个元素的切片
// TC3 - 包含多个元素且有父子关系的切片
func TestInitTree(t *testing.T) {
	// 假设 nestedItem 结构已经定义好

	cases := []struct {
		name     string
		items    []*nestedItem
		expected []*TreeNode
		wantErr  bool
	}{
		{"TC1", []*nestedItem{}, nil, false},
		{"TC2", []*nestedItem{{ID: 1, ParentID: sql.NullInt64{Valid: true, Int64: 0}}}, []*TreeNode{{nestedItem: &nestedItem{ID: 1, ParentID: sql.NullInt64{Valid: true, Int64: 0}}, Children: []*TreeNode{}}}, false},
		{"TC3", []*nestedItem{{ID: 1, ParentID: sql.NullInt64{Valid: true, Int64: 0}}, {ID: 2, ParentID: sql.NullInt64{Valid: true, Int64: 1}}, {ID: 3, ParentID: sql.NullInt64{Valid: true, Int64: 1}}}, []*TreeNode{{nestedItem: &nestedItem{ID: 1, ParentID: sql.NullInt64{Valid: true, Int64: 0}}, Children: []*TreeNode{{nestedItem: &nestedItem{ID: 2, ParentID: sql.NullInt64{Valid: true, Int64: 1}}, Children: []*TreeNode{}}, {nestedItem: &nestedItem{ID: 3, ParentID: sql.NullInt64{Valid: true, Int64: 1}}, Children: []*TreeNode{}}}}}, false},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			tree := initTree(tc.items)
			if tc.wantErr && tree != nil {
				t.Errorf("Expected error but got a non-nil tree")
			}

			// 这里需要更详细的断言来验证树是否正确构建
			assert.Equal(t, len(tc.expected), len(tree.Children))
			for i, child := range tree.Children {
				assert.Equal(t, child.nestedItem.ID, tc.expected[i].nestedItem.ID)
				assert.Equal(t, len(child.Children), len(tc.expected[i].Children))
			}
		})
	}
}

// TestGetNode 测试 getNode 方法
// 测试用例：
// TC1 - 查询不存在的节点
// TC2 - 查询存在的节点
func TestGetNode(t *testing.T) {
	// 假定一个已经初始化好的树
	items := []*nestedItem{{ID: 1, ParentID: sql.NullInt64{Valid: true, Int64: 0}}}
	tree := initTree(items)

	tests := []struct {
		name     string
		id       int64
		expected *TreeNode
		found    bool
	}{
		{"TC1", 2, nil, false},
		{"TC2", 1, &TreeNode{nestedItem: items[0], Children: []*TreeNode{}}, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			node, found := tree.getNode(tt.id)
			if tt.found != found || (!tt.found && node != nil) || (tt.found && node.nestedItem.ID != tt.expected.nestedItem.ID) {
				t.Errorf("TestGetNode(%d): Expected %v with value %v, got %v with value %v", tt.id, tt.found, tt.expected, found, node)
			}
		})
	}
}

// todo 修正
// TestAddNestedItem 测试 addNestedItem 方法
// 测试用例：
// TC1 - 添加根节点
// TC2 - 添加子节点
//func TestAddNestedItem(t *testing.T) {
//	// 假定一个已经初始化好的树
//	items := []*nestedItem{{ID: 1, ParentID: sql.NullInt64{Valid: true, Int64: 0}}}
//	tree := initTree(items)
//
//	newItems := []*nestedItem{{ID: 2, ParentID: sql.NullInt64{Valid: true, Int64: 0}}, {ID: 3, ParentID: sql.NullInt64{Valid: true, Int64: 1}}}
//
//	for _, newItem := range newItems {
//		tree.addNestedItem(newItem)
//	}
//
//	// 检查添加后的树状态
//	assert.Len(t, tree.Children, 2)
//	assert.NotNil(t, tree.data[newItems[0].ID])
//	assert.NotNil(t, tree.data[newItems[1].ID])
//	assert.Contains(t, tree.Children[0].Children, tree.data[newItems[1].ID])
//}
