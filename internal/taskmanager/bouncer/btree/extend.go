package btree

// FindNextNode searches recursively down the tree starting at the startNode
func (tree *Tree) FindNextNode(startNode *Node, key interface{}) (node *Node, index int, found bool) {
	if tree.Empty() {
		return nil, -1, false
	}
	node = startNode

	index, found = tree.search(node, key)
	if found {
		return node, index, true
	}

	return nil, -1, false
}

func (node *Node) SetData(value any) {
	entry := &Entry{Key: "default", Value: value}
	if node.Entries == nil {
		node.Entries = make([]*Entry, 0)
	}
	node.Entries = append(node.Entries, entry)
}

func (node *Node) GetData() any {
	if node.Entries == nil || len(node.Entries) == 0 {
		return nil
	}
	for _, entry := range node.Entries {
		if entry.Key == "default" {
			return entry.Value
		}
	}

	return nil
}
