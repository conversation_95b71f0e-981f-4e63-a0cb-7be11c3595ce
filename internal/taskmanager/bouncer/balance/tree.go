package balance

import (
	"time"

	"mpp/internal/taskmanager/bouncer/btree"
)

/*
*
一些调度算法的文章：
https://xie.infoq.cn/article/749a713bcb2c200803a030b5a
*/
type TreeBalance struct {
	tree *btree.Tree
}

func NewTreeBalance() *TreeBalance {
	t := &TreeBalance{}
	t.tree = btree.NewWithStringComparator(1000)
	return t
}

func (t *TreeBalance) Judge(node *BalanceNode) int {
	n := t.tree.GetNode(node.Product)
	if n == nil {
		// todo 增加node
	}

	for i := 1; i < LayerMaxLimit; i++ {
		var key string
		switch i {
		case LayerResourceProduct:
			key = node.Product
		case LayerResourceTag:
			key = node.Tag
		case LayerResourceModel:
			key = node.Model
		case LayerResourceLabel:
			key = node.Label
		case LayerTenant:
			key = node.Tenant
		case LayerPipe:
			key = node.Pipe
		}
		childTree, _, found := t.tree.FindNextNode(n, key)
		if !found {
			break
		}
		if childTree == nil || len(childTree.Children) == 0 {
			break
		}

		n = childTree
	}
	return 0
}

// 计算任务的综合优先级
func calculatePriority(node *BalanceNode) float64 {
	weightCoefficient := 0.4
	waitTimeCoefficient := 0.3
	backlogCoefficient := 0.3

	priority := (float64(node.Weight) * weightCoefficient) + (float64(time.Now().Sub(node.LastRunning)) * waitTimeCoefficient) + (float64(node.Queued) * backlogCoefficient)
	return priority
}
