package balance

import "time"

const (
	LayerResourceRoot = iota
	LayerResourceProduct
	LayerResourceTag
	LayerResourceModel
	LayerResourceLabel
	LayerTenant
	LayerPipe

	// LayerMaxLimit 保持在最后一位
	LayerMaxLimit
)

type BalanceNode struct {
	Type  string
	Key   string
	Layer int

	Product string
	Tag     string
	Model   string
	Label   string
	Tenant  string
	Pipe    string

	// 权重
	Weight int
	// 最后一次任务开始运行时间
	LastRunning   time.Time
	LastRunningID uint64

	// 最后一次任务开始运行时间
	LastEnqueue time.Time

	// 当前载荷
	Load int

	// 任务平均消耗时间
	Average time.Time
	// Min
	Min time.Time
	// Max
	Max time.Time

	// 当前正在运行的任务
	Running uint64
	// 当前排队的任务
	Queued uint64

	Metadata map[string]any
	// todo 可以外挂一个OPA策略吗？？？？？
}
