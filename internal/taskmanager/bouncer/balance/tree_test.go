package balance

import (
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestNewTreeBalance(t *testing.T) {
	tb := NewTreeBalance()
	if tb == nil {
		t.<PERSON><PERSON><PERSON>("NewTreeBalance() returned nil, want non-nil")
	}
	if tb.tree == nil {
		t.<PERSON><PERSON><PERSON>("tb.tree is nil, want non-nil")
	}
}

func TestJudge(t *testing.T) {
	tb := NewTreeBalance()

	// Create a mock node
	mockNode := &BalanceNode{
		Product:     "product1",
		Tag:         "tag1",
		Model:       "model1",
		Label:       "label1",
		Tenant:      "tenant1",
		Pipe:        "pipe1",
		Weight:      10,
		Queued:      5,
		LastRunning: time.Now().Add(-1 * time.Hour),
	}

	// Test the Judge method
	result := tb.Judge(mockNode)
	if result != 0 {
		t.<PERSON><PERSON>rf("Judge() = %d, want 0", result)
	}
}

func TestCalculatePriority(t *testing.T) {
	node := &BalanceNode{
		Weight:      10,
		Queued:      5,
		LastRunning: time.Now().Add(-1 * time.Hour),
	}

	expectedPriority := (float64(node.Weight) * 0.4) + (float64(time.Now().Sub(node.LastRunning)) * 0.3) + (float64(node.Queued) * 0.3)
	actualPriority := calculatePriority(node)

	expected := int(expectedPriority) / 10000
	actual := int(actualPriority) / 10000
	assert.Equal(t, expected, actual)
}
