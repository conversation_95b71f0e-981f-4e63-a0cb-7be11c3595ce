package tasker_span

import (
	"context"
	"fmt"
	"time"

	"mpp/internal/taskmanager/models"
	"mpp/pkg/tracer/tasker_tracing"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
)

func SpanRecordWithTask(ctx context.Context, event string, task *models.Task) {
	SpanRecordWithTaskAndExtendInformation(ctx, event, task, nil)
}

func SpanRecordWithTaskAndExtendInformation(ctx context.Context, event string, task *models.Task, extends map[string]string) {
	span := tasker_tracing.SpanFromContext(ctx)
	SpanRecordWithTaskAndExtendInformationWithSpan(ctx, span, event, task, extends)
}

func SpanRecordWithTaskAndExtendInformationWithSpan(ctx context.Context, span trace.Span, event string, task *models.Task, extends map[string]string) {
	var eventAttrs []attribute.KeyValue

	rpcTraceId := tasker_tracing.RPCTraceID(ctx)
	if rpcTraceId != "" {
		eventAttrs = append(eventAttrs,
			attribute.String("rpc.trace.id", rpcTraceId),
		)
	}

	if task != nil {
		eventAttrs = append(eventAttrs,
			attribute.String("job.id", task.JobId),
			attribute.String("task.id", task.TaskId),
			attribute.String("task.product", task.Product),
			attribute.String("task.tag", task.Tag),
			attribute.String("task.model", task.EngineModel),
			attribute.String("task.taskType", task.TaskType.String()),
			attribute.String("task.uid", task.UserId),
			attribute.String("task.pipeline", task.PipelineId),
			attribute.String("task.createAt", task.GmtCreate.Format(time.RFC3339)),
			attribute.String("task.bizStatus", task.BizStatus.String()),
			attribute.String("task.inrStatus", task.InternalStatus.String()),
			attribute.Int64("task.runSeconds", int64(time.Now().Sub(task.GmtCreate).Seconds())),
		)

		if task.Worker != nil {
			eventAttrs = append(eventAttrs,
				attribute.String("task.workerId", task.Worker.Id),
				attribute.String("task.worker", fmt.Sprintf("%s:%d", task.Worker.Ip, task.Worker.Port)),
			)
		}

		span.SetAttributes(eventAttrs...)

		eventAttrs = append(eventAttrs,
			attribute.String("task.notify", task.Notify),
			attribute.String("task.metadata", fmt.Sprintf("%v", task.Metadata)),
		)

		if task.RequestResource != nil {
			eventAttrs = append(eventAttrs,
				attribute.String("task.requestQuota", fmt.Sprintf("%v", task.RequestResource.Quota)),
			)
		}
		if task.Param != nil {
			var suffix string
			dataIdx := len(task.Param.Param)
			if dataIdx > 50 {
				dataIdx = 50
				suffix = "***"
			}
			eventAttrs = append(eventAttrs,
				attribute.String("task.param", task.Param.Param[0:dataIdx]+suffix),
				attribute.String("task.paramURL", task.Param.ParamUrl),
			)
		}

		if task.Result != nil {
			data, ok := task.Result.Data.(string)
			if ok {
				var suffix string
				dataIdx := len(data)
				if dataIdx > 50 {
					dataIdx = 50
					suffix = "***"
				}
				eventAttrs = append(eventAttrs,
					attribute.Int("task.result.dataSize", len(data)),
					attribute.String("task.result.data", data[0:dataIdx]+suffix),
				)
			}
			eventAttrs = append(eventAttrs,
				attribute.String("task.result.dataURL", task.Result.DataUrl),
				attribute.String("task.result.message", task.Result.Message),
				attribute.String("task.result.reason", task.Result.Reason),
				attribute.Int("task.result.code", int(task.Result.Code)),
			)
		}
	}

	if len(extends) > 0 {
		for k, v := range extends {
			eventAttrs = append(eventAttrs,
				attribute.String(k, v),
			)
		}
	}

	if event == "" {
		event = "default"
	}
	span.AddEvent(event, trace.WithAttributes(eventAttrs...))

}
