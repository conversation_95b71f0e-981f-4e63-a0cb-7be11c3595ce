package biz

import (
	"mpp/internal/taskmanager/biz/category"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/dag"
	"mpp/internal/taskmanager/biz/gengine"
	"mpp/internal/taskmanager/biz/global_param_loader"
	"mpp/internal/taskmanager/biz/grayscale"
	"mpp/internal/taskmanager/biz/ha"
	"mpp/internal/taskmanager/biz/leader"
	"mpp/internal/taskmanager/biz/livetranscode"
	"mpp/internal/taskmanager/biz/pipeline"
	"mpp/internal/taskmanager/biz/quota"
	"mpp/internal/taskmanager/biz/router"
	"mpp/internal/taskmanager/biz/sentinel"
	"mpp/internal/taskmanager/biz/tasker"

	"github.com/google/wire"
)

// ProviderSet is biz providers.
var ProviderSet = wire.NewSet(leader.NewLeaderBiz,
	category.NewCategory,
	tasker.NewTaskBiz,
	tasker.NewTaskStatusSync,
	tasker.NewEngineTaskSync,
	tasker.NewSchedulerResourceSync,
	tasker.NewBatchTaskBiz,
	tasker.NewSimpleTaskBiz,
	tasker.NewRecordHlsTaskBiz,
	router.NewRouter,
	pipeline.NewCommonPipelineBiz,
	// 流控
	sentinel.NewSentinelBiz,
	tasker.NewTaskSentinelSync,
	quota.NewQuotaService,
	dag.NewTaskDependency,

	tasker.NewBatchTaskSync,
	common.NewSwitchService,
	grayscale.NewMixRuleConfigBiz,

	//混跑引擎
	//gengine.NewMixGrayRuleEngineService,
	gengine.NewRuleEngineService,

	//直播限流设置
	global_param_loader.NewGlobalParamLoader,

	livetranscode.NewSchedulerSwitchOperator,
	livetranscode.NewLiveTransCodeParamsAdapter,
	ha.NewHAService,
)
