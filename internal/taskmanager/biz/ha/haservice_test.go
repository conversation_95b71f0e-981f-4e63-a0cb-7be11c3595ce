package ha

import (
	"database/sql"
	"io"
	"net/http"
	"strings"
	"testing"
	"time"

	"mpp/internal/taskmanager/biz/global_param_loader"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/mocks"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/domain"
	"mpp/internal/taskmanager/repository"

	"github.com/bytedance/mockey"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
)

var testHaService = &HAService{}
var mockTaskRepo *mocks.MockTaskRepo

func InitHaService(ctl *gomock.Controller) {
	mockTaskRepo = mocks.NewMockTaskRepo(ctl)
	mockTaskRepo.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(&models.Task{
		TaskId:         "123",
		BizStatus:      models.TaskStateActive,
		InternalStatus: models.TaskStateActive,
		EngineModel:    models.LiveTranscode,
		LastModified:   time.Now().Add(1 * time.Hour),
	}, nil).AnyTimes()
	mockTaskRepo.EXPECT().CloneWithSpecifiedTable(gomock.Any()).Return(mockTaskRepo).AnyTimes()
	testHaService.taskRepo = mockTaskRepo
	mockUnitRepo := mocks.NewMockUnitTaskRepo(ctl)
	mockUnitRepo.EXPECT().SyncTask(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockUnitRepo.EXPECT().InsertUnitTask(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	testHaService.unitTaskRepo = mockUnitRepo
	testHaService = NewHAService(log.GetLogger(), mockTaskRepo, mockUnitRepo, &conf.Bootstrap{
		LiveTransCodeConfig: &conf.LiveTransCodeConfig{
			Unit: "pre",
		},
	}, nil)
	testHaService.SetHaAvailable(true, "127.0.0.1")
}

func TestSetHaAvailable(t *testing.T) {
	ctl := gomock.NewController(t)
	//mock
	defer ctl.Finish()
	InitHaService(ctl)
	testHaService.SetHaAvailable(true, "127.0.0.1")
}

func TestDoHAStartBefore(t *testing.T) {
	ctl := gomock.NewController(t)
	//mock
	defer ctl.Finish()
	InitHaService(ctl)
	defer mockey.Mock((*http.Client).Do).Return(&http.Response{
		StatusCode: 200,
		Body: io.NopCloser(strings.NewReader(`{"content":{
			"task":{
                "taskId":"123",
                "BizStatus":1,
                "InternalStatus":1,
                "EngineModel":"livetranscode"
            }
		}}`)),
	}, nil).Build().UnPatch()
	testHaService.DoHAStartBefore("123")
}

func TestDoHAStartAfter(t *testing.T) {
	ctl := gomock.NewController(t)
	//mock
	defer ctl.Finish()
	InitHaService(ctl)
	testHaService.SetHaAvailable(false, "")
	defer mockey.Mock((*global_param_loader.GlobalParamLoader).GetDefaultUnit).Return("pre").Build().UnPatch()
	testHaService.DoHAStartAfter("pre", "123")
}

func TestGetAllTasks(t *testing.T) {
	ctl := gomock.NewController(t)
	//mock
	defer ctl.Finish()
	InitHaService(ctl)
	defer mockey.Mock((*http.Client).Do).Return(&http.Response{
		StatusCode: 200,
		Body: io.NopCloser(strings.NewReader(`{"content":{
			"taskIds":[
                "123",
                "234",
                "345"
            ]
		}}`)),
	}, nil).Build().UnPatch()
	result := testHaService.GetAllTasks("pre")
	assert.NotNil(t, result)
	assert.Equal(t, 3, len(result))
}

func TestGetTaskFromUnitDb(t *testing.T) {
	ctl := gomock.NewController(t)
	//mock
	defer ctl.Finish()
	InitHaService(ctl)
	conn := "root:secret@tcp(localhost:3306)/mysql?parseTime=true"
	_, err := sql.Open("mysql", conn)

	config := &conf.Data{Database: &conf.Data_Database{Driver: "mysql", Source: conn, AutoMigrate: true}}
	db, _ := repository.NewDB(config, &conf.LiveTransCodeConfig{})
	d, err := db.DB()
	assert.Nil(t, err)
	task := &domain.Task{
		TaskId:         "mocked-task-id",
		BizStatus:      1,
		InternalStatus: 1,
		EngineModel:    models.LiveTranscode,
		EngineParam:    nil,
	}
	_, err = d.Exec(
		"INSERT INTO stream_tasks (gmt_create, gmt_modified, job_id, task_id, pipeline_id, task_type, biz_status, internal_status, product, engine_model, tag, "+
			"request_resource, user_data, trace, notify, engine_param, migrate_config, dependency, migrate_times, metadata, user_id, schedule_param)"+
			"VALUES(now(), now(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, ?, ?) "+
			"ON DUPLICATE KEY UPDATE product = ?, engine_model = ?, tag = ?, biz_status = ?, request_resource = ?, user_data = ?, user_id = ?, schedule_param = ?, "+
			"trace = ?, notify = ?, engine_param = ?, migrate_config = ?, metadata = ?, dependency = ?, "+
			"result = NULL, worker_id = NULL, worker_ip = NULL, worker_port = NULL, migrate_times = 0, gmt_modified = now()",
		task.JobId, task.TaskId, task.PipelineId, task.TaskType, task.BizStatus, task.InternalStatus, task.Product, task.EngineModel, task.Tag,
		task.RequestResource, task.UserData, task.Trace, task.Notify, task.EngineParam, task.MigrateConfig, task.Dependency, task.Metadata, task.UserId, task.ScheduleParam,
		task.Product, task.EngineModel, task.Tag, task.BizStatus, task.RequestResource, task.UserData, task.UserId, task.ScheduleParam,
		task.Trace, task.Notify, task.EngineParam, task.MigrateConfig, task.Metadata, task.Dependency,
	)
	assert.Nil(t, err)
	defer mockey.Mock((*global_param_loader.GlobalParamLoader).GetDatasource).Return(conn).Build().UnPatch()
	task1 := testHaService.GetTaskFromUnitDb("pre", "mocked-task-id")
	assert.Equal(t, task1.TaskId, "mocked-task-id")
}

func TestHAService_RecoverDataFromDB(t *testing.T) {
	ctl := gomock.NewController(t)
	//mock
	defer ctl.Finish()
	InitHaService(ctl)
	defer mockey.Mock((*global_param_loader.GlobalParamLoader).GetDefaultUnit).Return("pub").Build().UnPatch()
	defer mockey.Mock((*HAService).GetTaskFromUnitDb).Return(&domain.Task{
		TaskId:         "123",
		BizStatus:      int32(models.TaskStateActive),
		InternalStatus: int32(models.TaskStateActive),
		EngineModel:    models.LiveTranscode,
		GmtModified:    time.Now(),
	}).Build().UnPatch()
	testHaService.RecoverDataFromDB("pre", "123")
}
