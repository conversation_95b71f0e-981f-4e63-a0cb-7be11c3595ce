package ha

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/global_param_loader"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/domain"
	"mpp/internal/taskmanager/repository"
	"mpp/internal/taskmanager/util"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

const (
	QueryUri    = "/ha/getTask"
	QueryAllUri = "/ha/getTasks"
	UpdateUri   = "/ha/updateTask"
)

type HAService struct {
	log               *log.Helper
	taskRepo          repository.TaskRepo
	unitTaskRepo      repository.UnitTaskRepo
	GlobalParamLoader *global_param_loader.GlobalParamLoader

	Host        string
	Status      bool
	DefaultUnit string
	dbClientMap map[string]*gorm.DB
	//http客户端池
	httpClientPool *util.HTTPClientPool
}

func NewHAService(logger log.Logger, taskRepo repository.TaskRepo, unitTaskRepo repository.UnitTaskRepo, conf *conf.Bootstrap, globalParamLoader *global_param_loader.GlobalParamLoader) *HAService {
	if conf.LiveTransCodeConfig == nil {
		return nil
	}
	ha := &HAService{
		// todo 配置为空验证
		DefaultUnit:       conf.LiveTransCodeConfig.Unit,
		Status:            false,
		GlobalParamLoader: globalParamLoader,
		Host:              "",
		taskRepo:          taskRepo,
		unitTaskRepo:      unitTaskRepo,
		log:               log.NewHelper(logger),
		dbClientMap:       make(map[string]*gorm.DB),
		httpClientPool:    util.NewHTTPClientPool(),
	}
	ha.taskRepo = taskRepo.CloneWithSpecifiedTable(models.StreamTaskTableName)
	return ha
}

func (ha *HAService) Init() error {
	return nil
}

func (ha *HAService) SetHaAvailable(status bool, host string) bool {
	ha.Status = status
	if status {
		if strings.TrimSpace(host) == "" {
			return false
		}
		ha.Host = host
	}
	return true
}

func (ha *HAService) DoHAStartBefore(taskId string) {
	if ha.Status {
		ha.RecoverDataFromHAService(taskId)
	}
}

func (ha *HAService) DoHAStartAfter(unit, taskId string) {
	if ha.Status {
		ha.UpdateTaskToHAService(taskId)
	} else {
		ha.DoOtherUnitHATask(unit, taskId)
	}
}

func (ha *HAService) DoStopBefore(unit, taskId string) {
	if ha.Status {
		ha.RecoverDataFromHAService(taskId)
	} else {
		ha.RecoverDataFromDB(unit, taskId)
	}
}

func (ha *HAService) DoStopAfter(unit, taskId string) {
	if ha.Status {
		ha.UpdateTaskToHAService(taskId)
	} else {
		ha.DoOtherUnitHATask(unit, taskId)
	}
}

func (ha *HAService) UpdateTaskToHAService(taskId string) {
	resp, err := ha.Call(UpdateUri, map[string]interface{}{"taskId": taskId})
	if err != nil {
		log.Warnf("update task to HA service fail, taskId:%s, resp:%v, err:%v", taskId, resp, err)
	}
}

func (ha *HAService) DoOtherUnitHATask(unit, taskId string) {
	if unit == "" {
		unit = ha.DefaultUnit
	}
	if ha.GlobalParamLoader.GetDefaultUnit() == unit {
		return
	}

	task := &domain.UnitTask{
		TaskId: taskId,
		Unit:   unit,
		Status: 1,
	}
	err := ha.unitTaskRepo.InsertUnitTask(context.Background(), task)
	if err != nil {
		log.Errorf("doOtherUnitHATask fail, taskId:%s, unit:%s, err:%v", taskId, unit, err)
	}
}

func (ha *HAService) RecoverDataFromDB(unit, taskId string) {
	if unit == "" {
		unit = ha.DefaultUnit
	}
	if ha.GlobalParamLoader.GetDefaultUnit() == unit {
		return
	}

	// 从原单元的DB中获取任务
	unitTask := ha.GetTaskFromUnitDb(unit, taskId)
	log.Infof("get task from unit db. taskId:%s, dbTask:%v", taskId, unitTask)
	if unitTask != nil {
		dbTask, err := ha.taskRepo.FindByID(context.Background(), taskId)
		if err != nil {
			log.Errorf("RecoverDataFromHAService fail in repo find task, taskId:%s, err:%v", taskId, err)
			return
		}
		if dbTask != nil && !dbTask.LastModified.After(unitTask.GmtModified) {
			log.Infof("recover data is old than self. taskId:%s this:%v other:%v", taskId, dbTask, unitTask)
			return
		}
		// 恢复到数据库
		err = ha.unitTaskRepo.SyncTask(context.Background(), unitTask)
		if err != nil {
			log.Errorf("RecoverDataFromHAService fail in synctask, taskId:%s, err:%v", taskId, err)
		}
	}
}

func (ha *HAService) RecoverDataFromHAService(taskId string) {
	haTask := ha.GetTaskFromHAService(taskId)
	if haTask == nil {
		log.Warnf("record data from ha service not found, taskId:%s", taskId)
		return
	}

	dbTask, err := ha.taskRepo.FindByID(context.Background(), taskId)
	if err != nil && !errors.Is(err, common.ErrTaskNotFoundError) {
		log.Errorf("RecoverDataFromHAService fail in repo find, taskId:%s, err:%v", taskId, err)
		return
	}
	if dbTask != nil && dbTask.LastModified.After(haTask.LastModified) {
		log.Infof("recore data is old than self. task, taskId:%s, this dbTask:%v, haTask:%v", taskId, dbTask, haTask)
		return
	}
	log.Infof("RecoverDataFromHAService start to sync task from ha service, taskId:%s, dbTask:%v,haTask:%v", taskId, dbTask, haTask)

	domainHaTask, err := models.ToTaskModel(haTask)
	if err != nil {
		log.Errorf("RecoverDataFromHAService fail in toTaskModel, taskId:%s, err:%v", taskId, err)
		return
	}
	err = ha.unitTaskRepo.SyncTask(context.Background(), domainHaTask)
	if err != nil {
		log.Errorf("RecoverDataFromHAService fail in synctask, taskId:%s, err:%v", taskId, err)
	}
}

func (ha *HAService) GetTaskFromHAService(taskId string) *models.Task {
	if !ha.Status {
		return nil
	}

	req := map[string]interface{}{"taskId": taskId}
	resp, err := ha.Call(QueryUri, req)
	if err != nil {
		log.Errorf("GetTaskFromHAService fail, taskId:%s, resp:%v, err:%v", taskId, resp, err)
		return nil
	}
	if resp == nil {
		log.Warnf("GetTaskFromHAService return empty response, taskId:%s, resp:%v, err:%v", taskId, resp, err)
		return nil
	}

	content := resp["content"]
	if content == nil {
		log.Warnf("GetTaskFromHAService return empty content, taskId:%s, resp:%v, err:%v", taskId, resp, err)
		return nil
	}
	contentMap := content.(map[string]interface{})
	taskMap := contentMap["task"]
	if taskMap == nil {
		log.Warnf("GetTaskFromHAService return empty task, taskId:%s, resp:%v, err:%v", taskId, resp, err)
		return nil
	}
	var task models.Task
	data, _ := json.Marshal(taskMap)
	err = json.Unmarshal(data, &task)
	if err != nil {
		log.Errorf("GetTaskFromHAService fail in unmarshal task, taskId:%s, resp:%v, err:%v", taskId, resp, err)
		return nil
	}
	return &task
}

func (ha *HAService) GetAllTasks(unit string) []string {
	if unit == "" {
		log.Warnf("GetTaskFromUnitDb unit is empty")
		return nil
	}
	req := map[string]interface{}{"unit": unit}
	resp, err := ha.Call(QueryAllUri, req)
	if err != nil {
		log.Errorf("unit getAllTasks fail, unit:%s, resp:%v, err:%v", unit, resp, err)
		return nil
	}
	if resp == nil {
		log.Warnf("unit getAllTasks return empty response, unit:%s, resp:%v, err:%v", unit, resp, err)
		return nil
	}

	content := resp["content"]
	if content == nil {
		log.Warnf("unit getAllTasks return empty content, unit:%s, resp:%v, err:%v", unit, resp, err)
		return nil
	}
	contentMap := content.(map[string]interface{})
	taskIds := contentMap["taskIds"]
	if taskIds == nil {
		log.Warnf("unit getAllTasks return empty task, unit:%s, resp:%v, err:%v", unit, resp, err)
		return nil
	}
	var taskIdList []string
	data, _ := json.Marshal(taskIds)
	err = json.Unmarshal(data, &taskIdList)
	if err != nil {
		log.Errorf("GetTaskFromHAService fail in unmarshal task, unit:%s, resp:%v, err:%v", unit, resp, err)
		return nil
	}
	return taskIdList
}

func (ha *HAService) GetTaskFromUnitDb(unit, taskId string) *domain.Task {
	if unit == "" {
		log.Warnf("GetTaskFromUnitDb unit is empty")
		return nil
	}

	dbClient := ha.getDbClient(unit)
	if dbClient == nil {
		log.Warnf("GetTaskFromUnitDb dbClient is nil")
		return nil
	}
	var task domain.Task
	result := dbClient.Model(&domain.Task{}).Table("stream_tasks").Where("task_id = ?", taskId).Take(&task)
	if result.Error != nil {
		log.Errorf("GetTaskFromUnitDb fail in repo find task, taskId:%s, err:%v", taskId, result.Error)
		return nil
	}
	return &task
}

func (ha *HAService) Call(uri string, data map[string]interface{}) (map[string]interface{}, error) {
	url := fmt.Sprintf("http://%s%s", ha.Host, uri)
	body, err := json.Marshal(data)
	if err != nil {
		log.Errorf("construct ha request param fail, param:%s, err:%v ", data, err)
		return nil, err
	}

	req, err := http.NewRequest("POST", url, bytes.NewReader(body))
	if err != nil {
		log.Errorf("construct ha request fail, err:%v", err)
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json; charset=UTF-8")
	req.Close = true

	client := ha.httpClientPool.GetClient(true)
	response, err := client.Do(req)
	if err != nil {
		log.Errorf("call ha api fail, err:%v", err)
		return nil, err
	}

	var result map[string]interface{}
	if err := json.NewDecoder(response.Body).Decode(&result); err != nil {
		log.Errorf("decode ha response fail, err:%v", err)
		return nil, err
	}
	log.Infof("call ha api success, result:%v", result)
	return result, nil
}

func (ha *HAService) getDbClient(unit string) *gorm.DB {
	if db, ok := ha.dbClientMap[unit]; ok {
		return db
	} else {
		datasource := ha.GlobalParamLoader.GetDatasource(unit)
		if datasource == "" {
			return nil
		}
		dbClient, _ := initDbClient(datasource)
		if dbClient == nil {
			return nil
		}
		ha.dbClientMap[unit] = dbClient
		return dbClient
	}
}

func initDbClient(datasource string) (*gorm.DB, error) {
	db, err := gorm.Open(mysql.Open(datasource), &gorm.Config{
		// 数据库日志开关
		Logger: logger.Default.LogMode(logger.Error),
		// 关闭默认事务
		SkipDefaultTransaction: true,
		// 开启预编译
		PrepareStmt: true,
	})

	if err != nil {
		return nil, fmt.Errorf("failed to open unit db: %v", err)
	}

	sqlDb, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to config unit db: %v", err)
	}
	// 设置连接池
	// 空闲
	sqlDb.SetMaxIdleConns(50)
	// 打开
	sqlDb.SetMaxOpenConns(100)
	// 超时
	sqlDb.SetConnMaxLifetime(time.Second * 2)
	return db, nil
}

func (ha *HAService) Close() {
	if ha.httpClientPool != nil {
		ha.httpClientPool.Close()
	}
}
