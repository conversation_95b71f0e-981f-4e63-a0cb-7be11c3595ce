package grayscale

import (
	"context"
	"github.com/cinience/animus/safe"
	kratosErr "github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"math/rand"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/gengine"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"
	"mpp/pkg/tracer/tasker_tracing"
	"time"
)

/**
 * @Author: qinxin
 * @Date: 2024/12/25 17:51
 * @Desc:
 * @Version 1.0
 */

type MixRuleConfigBiz struct {
	repo       repository.MixGrayConfigRepo
	log        *log.Helper
	taskTracer *tasker_tracing.Tracer
	conf       *conf.Bootstrap

	mixGrayRuleEngine *gengine.MixGrayRuleEngine

	//ruleEngineService *gengine.RuleEngineService
}

func NewMixRuleConfigBiz(logger log.Logger, conf *conf.Bootstrap, repo repository.MixGrayConfigRepo) (*MixRuleConfigBiz, error) {
	t := &MixRuleConfigBiz{conf: conf, repo: repo, log: log.NewHelper(logger)}
	t.taskTracer = tasker_tracing.NewTracer(tasker_tracing.WithConsumerSpanKind())

	//加载所有混跑规则
	list, err := t.ListActiveMixGrayConfig(context.Background())
	if err != nil {
		log.Errorf("NewMixRuleConfigBiz fail, err:%v", err)
	}
	mixGrayRuleEngine := gengine.NewMixGrayRuleEngine(logger, list)
	t.mixGrayRuleEngine = mixGrayRuleEngine

	////初始化混跑引擎
	//mixGrayRuleEngine := gengine.NewMixGrayRuleEngine()
	////加载混跑引擎
	//ruleEngineService := gengine.NewRuleEngineService(mixGrayRuleEngine)
	//t.ruleEngineService = ruleEngineService

	//启动定时同步
	safe.Go(func() {
		t.StartSync()
	})

	return t, nil
}

// StartSync 定时同步规则引擎
func (m *MixRuleConfigBiz) StartSync() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			safe.Go(func() {
				// todo 这里如果执行时间过长，可能会导致多个线程同时执行
				ctx := context.Background()
				err := m.SyncMixGrayRuleEngine(ctx)
				if err != nil {
					m.log.WithContext(ctx).Errorf("MixRuleConfigBiz/StartSync SyncMixGrayRuleEngine fail, err:%v", err)
				}
			})
			//todo 等待执行完成

		}
	}
}

func (m *MixRuleConfigBiz) GetMixGrayConfig(ctx context.Context, ruleName string) (*models.MixGrayConfig, error) {

	mixGrayConfigDomain, err := m.repo.GetMixGrayConfigByName(ctx, ruleName)
	if err != nil {
		m.log.WithContext(ctx).Errorf("GetMixGrayConfig fail in GetMixGrayConfigByName, ruleName:%s, err:%v", ruleName, err)
		return nil, err
	}

	mixGrayConfig, err := models.DomainToMixGrayConfigBiz(mixGrayConfigDomain)
	if err != nil {
		m.log.WithContext(ctx).Errorf("DomainToMixGrayConfigBiz fail,mixGrayConfigDomain:%+v err:%v", mixGrayConfigDomain, err)
		return nil, err
	}
	return mixGrayConfig, nil
}
func (m *MixRuleConfigBiz) CreateMixGrayConfig(ctx context.Context, mixGrayConfig *models.MixGrayConfig) error {

	//existMixGrayConfig, err := m.GetMixGrayConfig(ctx, mixGrayConfig.RuleName)
	//if err != nil {
	//	m.log.WithContext(ctx).Errorf("GetMixGrayConfigByName fail, mixGrayConfig:%+v, err:%v", mixGrayConfig, err)
	//	return err
	//}
	//if existMixGrayConfig != nil {
	//	m.log.WithContext(ctx).Errorf("ruleName:%s already exist", mixGrayConfig.RuleName)
	//	return common.ErrTaskAlreadyExist
	//}

	mixGrayConfigDomain, err := models.MixGrayConfigBizToDomain(mixGrayConfig)
	if err != nil {
		m.log.WithContext(ctx).Errorf("MixGrayConfigBizToDomain fail,mixGrayConfig:%+v err:%v", mixGrayConfig, err)
		return err
	}
	err = m.repo.Create(ctx, mixGrayConfigDomain)
	if err != nil {
		m.log.WithContext(ctx).Errorf("CreateMixGrayConfig fail, err:%v", err)
		return err
	}
	//err = m.SyncMixGrayRuleEngine(ctx)
	//if err != nil {
	//	m.log.WithContext(ctx).Errorf("MixRuleConfigBiz/CreateMixGrayConfig SyncMixGrayRuleEngine fail, err:%v", err)
	//}
	log.Infof("CreateMixGrayConfig success, mixGrayConfig:%+v", mixGrayConfig)
	return nil
}
func (m *MixRuleConfigBiz) UpdateMixGrayConfig(ctx context.Context, mixGrayConfig *models.MixGrayConfig) error {

	mixGrayConfigDomain, err := models.MixGrayConfigBizToDomain(mixGrayConfig)
	if err != nil {
		m.log.WithContext(ctx).Errorf("MixGrayConfigBizToDomain fail,mixGrayConfig:%+v err:%v", mixGrayConfig, err)
		return err
	}

	// 如果查询失败，则返回错误
	existMixGrayConfig, err := m.GetMixGrayConfig(ctx, mixGrayConfig.RuleName)
	if err != nil {
		m.log.WithContext(ctx).Errorf("GetMixGrayConfigByName fail, err:%v", err)
		return err
	}
	if existMixGrayConfig == nil {
		m.log.WithContext(ctx).Errorf("ruleName:%s not exist", mixGrayConfig.RuleName)
		return common.ErrRecordNotFoundError
	}

	err = m.repo.Update(ctx, mixGrayConfigDomain)
	if err != nil {
		m.log.WithContext(ctx).Errorf("UpdateMixGrayConfig fail, err:%v", err)
		return err
	}

	//更新规则
	//err = m.SyncMixGrayRuleEngine(ctx)
	//if err != nil {
	//	m.log.WithContext(ctx).Errorf("MixRuleConfigBiz/UpdateMixGrayConfig SyncMixGrayRuleEngine fail, err:%v", err)
	//}

	log.Infof("UpdateMixGrayConfig success, mixGrayConfig:%+v", mixGrayConfig)
	return nil
}

func (m *MixRuleConfigBiz) UpdateMixGrayRatio(ctx context.Context, mixGrayConfig *models.MixGrayConfig) error {

	mixGrayConfigDomain, err := models.MixGrayConfigBizToDomain(mixGrayConfig)
	if err != nil {
		m.log.WithContext(ctx).Errorf("MixRuleConfigBiz/UpdateMixGrayRatio fail in MixGrayConfigBizToDomain,mixGrayConfig:%+v err:%v", mixGrayConfig, err)
		return err
	}

	// 如果查询失败，则返回错误
	existMixGrayConfig, err := m.GetMixGrayConfig(ctx, mixGrayConfig.RuleName)
	if err != nil {
		m.log.WithContext(ctx).Errorf("MixRuleConfigBiz/UpdateMixGrayRatio fail in GetMixGrayConfig, err:%v", err)
		return err
	}
	if existMixGrayConfig == nil {
		m.log.WithContext(ctx).Errorf("MixRuleConfigBiz/UpdateMixGrayRatio fail in ruleName:%s not exist", mixGrayConfig.RuleName)
		return common.ErrRecordNotFoundError
	}

	err = m.repo.UpdateMixRatio(ctx, mixGrayConfigDomain)
	if err != nil {
		m.log.WithContext(ctx).Errorf("MixRuleConfigBiz/UpdateMixGrayRatio fail, err:%v", err)
		return err
	}

	//更新规则
	//err = m.SyncMixGrayRuleEngine(ctx)
	//if err != nil {
	//	m.log.WithContext(ctx).Errorf("MixRuleConfigBiz/UpdateMixGrayConfig SyncMixGrayRuleEngine fail, err:%v", err)
	//}
	return nil
}

func (m *MixRuleConfigBiz) ActiveMixGrayConfig(ctx context.Context, ruleName string) error {

	// 如果查询失败，则返回错误
	existMixGrayConfig, err := m.GetMixGrayConfig(ctx, ruleName)
	if err != nil {
		m.log.WithContext(ctx).Errorf("ActiveMixGrayConfig GetMixGrayConfigByName fail, err:%v", err)
		return err
	}
	if existMixGrayConfig == nil {
		m.log.WithContext(ctx).Errorf("ActiveMixGrayConfig ruleName:%s not exist", ruleName)
		return common.ErrRecordNotFoundError
	}
	// 更新数据库中状态
	existMixGrayConfig.ConfigStatus = models.MixGrayConfigStatusActive
	mixGrayConfigDomain, err := models.MixGrayConfigBizToDomain(existMixGrayConfig)
	if err != nil {
		m.log.WithContext(ctx).Errorf("ActiveMixGrayConfig MixGrayConfigBizToDomain fail,mixGrayConfig:%+v err:%v", existMixGrayConfig, err)
		return err
	}

	err = m.repo.Update(ctx, mixGrayConfigDomain)
	if err != nil {
		m.log.WithContext(ctx).Errorf("ActiveMixGrayConfig UpdateMixGrayConfig fail, err:%v", err)
		return err
	}
	log.Infof("ActiveMixGrayConfig success, mixGrayConfig:%+v", existMixGrayConfig)

	//更新规则
	//err = m.SyncMixGrayRuleEngine(ctx)
	//if err != nil {
	//	m.log.WithContext(ctx).Errorf("MixRuleConfigBiz/ActiveMixGrayConfig SyncMixGrayRuleEngine fail, err:%v", err)
	//}
	return nil
}

func (m *MixRuleConfigBiz) PauseMixGrayConfig(ctx context.Context, ruleName string) error {
	// 如果查询失败，则返回错误
	existMixGrayConfig, err := m.GetMixGrayConfig(ctx, ruleName)
	if err != nil {
		m.log.WithContext(ctx).Errorf("PauseMixGrayConfig GetMixGrayConfigByName fail, err:%v", err)
		return err
	}
	if existMixGrayConfig == nil {
		m.log.WithContext(ctx).Errorf("PauseMixGrayConfig ruleName:%s not exist", ruleName)
		return common.ErrRecordNotFoundError
	}
	// 更新数据库中状态
	existMixGrayConfig.ConfigStatus = models.MixGrayConfigStatusPause
	mixGrayConfigDomain, err := models.MixGrayConfigBizToDomain(existMixGrayConfig)
	if err != nil {
		m.log.WithContext(ctx).Errorf("PauseMixGrayConfig MixGrayConfigBizToDomain fail,mixGrayConfig:%+v err:%v", existMixGrayConfig, err)
		return err
	}
	err = m.repo.Update(ctx, mixGrayConfigDomain)
	if err != nil {
		m.log.WithContext(ctx).Errorf("PauseMixGrayConfig UpdateMixGrayConfig fail, err:%v", err)
		return err
	}
	log.Infof("PauseMixGrayConfig success, mixGrayConfig:%+v", existMixGrayConfig)

	//更新缓存中规则
	//err = m.SyncMixGrayRuleEngine(ctx)
	//if err != nil {
	//	m.log.WithContext(ctx).Errorf("MixRuleConfigBiz/PauseMixGrayConfig SyncMixGrayRuleEngine fail, err:%v", err)
	//}
	return nil
}

func (m *MixRuleConfigBiz) DeleteMixGrayConfig(ctx context.Context, ruleName string) error {
	err := m.repo.Delete(ctx, ruleName)
	if err != nil {
		m.log.WithContext(ctx).Errorf("DeleteMixGrayConfig fail, err:%v", err)
		return err
	}

	//更新规则
	//err = m.SyncMixGrayRuleEngine(ctx)
	//if err != nil {
	//	m.log.WithContext(ctx).Errorf("MixRuleConfigBiz/DeleteMixGrayConfig SyncMixGrayRuleEngine fail, err:%v", err)
	//}
	log.Infof("DeleteMixGrayConfig success, ruleName:%s", ruleName)
	return nil
}

func (m *MixRuleConfigBiz) ListMixGrayConfig(ctx context.Context) ([]*models.MixGrayConfig, error) {

	mixGrayConfigDomainList, err := m.repo.List(ctx)
	if err != nil {
		m.log.WithContext(ctx).Errorf("ListMixGrayConfig fail, err:%v", err)
		return nil, err
	}
	mixGrayConfigList, err := models.DomainListToMixGrayConfigBizList(mixGrayConfigDomainList)
	return mixGrayConfigList, nil
}

func (m *MixRuleConfigBiz) ListActiveMixGrayConfig(ctx context.Context) ([]*models.MixGrayConfig, error) {
	mixGrayConfigDomainList, err := m.repo.ListActiveMixGrayConfig(ctx)
	if err != nil {
		m.log.WithContext(ctx).Errorf("ListActiveMixGrayConfig fail, err:%v", err)
		return nil, err
	}

	mixGrayConfigList, err := models.DomainListToMixGrayConfigBizList(mixGrayConfigDomainList)
	if err != nil {
		m.log.WithContext(ctx).Errorf("DomainListToMixGrayConfigBizList fail, err:%v", err)
		return nil, err
	}
	return mixGrayConfigList, nil
}

// 更新规则引擎
func (m *MixRuleConfigBiz) SyncMixGrayRuleEngine(ctx context.Context) error {

	log.Infof("SyncMixGrayRuleEngine start")

	list, err := m.ListActiveMixGrayConfig(ctx)
	if err != nil {
		log.Errorf("ListActiveMixGrayConfig fail, err:%v", err)
		return err
	}

	err = m.mixGrayRuleEngine.LoadMixGrayConfigs(list)
	if err != nil {
		log.Errorf("LoadMixGrayConfigs fail, err:%v", err)
		return err
	}

	err = m.mixGrayRuleEngine.UpdateMixEnginePool()
	if err != nil {
		log.Errorf("SyncMixGrayRuleEngine fail, err:%v", err)
		return err
	}
	return nil
}

func (m *MixRuleConfigBiz) ExcuteMixRuleEngine(ctx context.Context, task *models.Task) (*models.MixGrayConfig, error) {

	rule, err := m.mixGrayRuleEngine.Execute(ctx, task)
	if err != nil {
		m.log.WithContext(ctx).Errorf("ExecuteMixRuleEngine fail, err:%v", err)
		return nil, err
	}
	//获取命中的规则名称
	var rulename string
	if rule != nil {
		for key, _ := range rule {
			rulename = key
		}
	}
	if rulename == "" {
		// 没有命中规则，正常返回空
		return nil, nil
	}
	//mixGrayConfig, err := m.mixRuleConfigBiz.GetMixGrayConfig(ctx, rulename)
	//if err != nil {
	//	m.log.WithContext(ctx).Errorf("ExcuteMixRuleEngine fail in GetMixGrayConfigByName, err:%v", err)
	//	return nil, err
	//}
	//从缓存中获取规则
	mixGrayConfig := m.mixGrayRuleEngine.RuleConfigData[rulename]

	return mixGrayConfig, nil

}

// 检查混跑规则，返回混跑配置
func (m *MixRuleConfigBiz) CheckHitMixRule(ctx context.Context, task *models.Task) (bool, bool, *models.MixConfig, error) {
	//是否命中混跑规则
	var isHitMixRule = false
	//是否混跑
	var isMixed = false
	var mixConfigs []*models.MixConfig
	mixGrayConfig, err := m.ExcuteMixRuleEngine(ctx, task)
	if err != nil {
		m.log.WithContext(ctx).Errorf("CheckMixRule fail in ExcuteMixRuleEngine, err:%v", err)
		return isHitMixRule, isMixed, nil, err
	}
	if mixGrayConfig == nil {
		// 没有命中规则，正常返回空
		return isHitMixRule, isMixed, nil, nil
	}
	//命中规则 根据mixRatio 按比例灰度
	isHitMixRule = true
	isMixed, err = m.IsMixed(ctx, mixGrayConfig)
	if err != nil {
		m.log.WithContext(ctx).Errorf("CheckMixRule fail in IsMixed, err:%v", err)
		return isHitMixRule, isMixed, nil, err
	}

	mixConfigs = mixGrayConfig.MixConfigs
	hitMixConfig, err := m.selectWeightedRandomMixConfig(mixConfigs)
	if err != nil {
		m.log.WithContext(ctx).Errorf("CheckMixRule fail in selectWeightedRandomMixConfig, err:%v", err)
		return isHitMixRule, isMixed, nil, err
	}

	return isHitMixRule, isMixed, hitMixConfig, nil

}

// 加权随机选择 MixConfig
func (m *MixRuleConfigBiz) selectWeightedRandomMixConfig(mixConfigs []*models.MixConfig) (*models.MixConfig, error) {
	if len(mixConfigs) == 0 {
		return nil, nil
	}

	// 设置随机种子
	rand.Seed(time.Now().UnixNano())

	// 计算总权重
	totalWeight := 0
	for _, config := range mixConfigs {
		totalWeight += int(config.Ratio)
	}

	if totalWeight <= 0 {
		return nil, nil
	}

	// 生成一个在 [0, totalWeight) 范围内的随机数
	randomValue := rand.Intn(totalWeight)

	// 选择 MixConfig
	cumulativeWeight := 0
	for _, config := range mixConfigs {
		cumulativeWeight += int(config.Ratio)
		if randomValue < cumulativeWeight {
			return config, nil
		}
	}

	// 如果没有选择到，返回最后一个 MixConfig（理论上不会发生）
	return mixConfigs[len(mixConfigs)-1], nil
}

// 按mixRatio确定是否混跑
func (m *MixRuleConfigBiz) IsMixed(ctx context.Context, mixGrayConfig *models.MixGrayConfig) (bool, error) {
	if mixGrayConfig == nil {
		log.Errorf("IsMixed fail, mixGrayConfig is nil")
		return false, kratosErr.NotFound("TASKMANAGER_MIXED_ERROR", "mixGrayConfig not found.")
	}
	if mixGrayConfig.MixRatio <= 0 {
		return false, nil
	}
	if mixGrayConfig.MixRatio >= 1 {
		return true, nil
	}

	// 获取 mixRatio
	mixRatio := mixGrayConfig.MixRatio

	// 设置随机种子
	rand.Seed(time.Now().UnixNano())

	// 生成一个 0 到 1 之间的随机数
	randomValue := rand.Float32()

	// 判断是否混跑
	isMixed := randomValue < mixRatio

	return isMixed, nil
}
