package grayscale

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"math/rand"
	"mpp/internal/taskmanager/mocks"
	"mpp/internal/taskmanager/models"
	"testing"
	"time"
)

/**
 * @Author: qinxin
 * @Date: 2025/3/25 19:12
 * @Desc:
 * @Version 1.0
 */

func TestMixRuleConfigBiz_GetMixGrayConfig(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mocks.NewMockMixGrayRuleEngineInterface(ctrl)
	//logger := log.NewHelper(log.With(log.GetLogger(), "module", "taskmanager/pipeline"))
	ctx := context.Background()

	biz := &MixRuleConfigBiz{repo: mockRepo, log: log.NewHelper(log.DefaultLogger)}

	mixGrayConfig := &models.MixGrayConfig{
		RuleName:     "test",
		MixRatio:     0.5,
		ConfigStatus: models.MixGrayConfigStatusActive,
		Priority:     3,
		MixConfigs: []*models.MixConfig{
			{
				Product:          "testProduct",
				Version:          "testVersion",
				EngineModel:      "testEngine",
				Tag:              "testTag",
				MixAllocStrategy: models.TryAbandonMixed,
			},
		},
		RuleConfigs: []*models.MixRuleConfig{
			{
				Operate:   string(models.MixOperateEqual),
				ParamName: "engineModel",
				Value:     "mps-transcode-new",
				ValueType: "string",
			},
		}}
	mixGrayConfigDomain, _ := models.MixGrayConfigBizToDomain(mixGrayConfig)

	t.Run("success", func(t *testing.T) {
		mockRepo.EXPECT().Create(ctx, mixGrayConfigDomain).Return(nil).Times(1)

		err := biz.CreateMixGrayConfig(ctx, mixGrayConfig)
		require.NoError(t, err)
	})
}

func TestMixRuleConfigBiz_UpdateMixGrayConfig(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mocks.NewMockMixGrayRuleEngineInterface(ctrl)
	//logger := log.NewHelper(log.With(log.GetLogger(), "module", "taskmanager/pipeline"))
	ctx := context.Background()

	biz := &MixRuleConfigBiz{repo: mockRepo, log: log.NewHelper(log.DefaultLogger)}
	mixGrayConfig := &models.MixGrayConfig{
		RuleName:     "test",
		MixRatio:     0.5,
		ConfigStatus: models.MixGrayConfigStatusActive,
		Priority:     3,
		MixConfigs: []*models.MixConfig{
			{
				Product:          "testProduct",
				Version:          "testVersion",
				EngineModel:      "testEngine",
				Tag:              "testTag",
				MixAllocStrategy: models.TryAbandonMixed,
			},
		},
		RuleConfigs: []*models.MixRuleConfig{
			{
				Operate:   string(models.MixOperateEqual),
				ParamName: "engineModel",
				Value:     "mps-transcode-new",
				ValueType: "string",
			},
		}}
	mixGrayConfigDomain, _ := models.MixGrayConfigBizToDomain(mixGrayConfig)
	t.Run("success", func(t *testing.T) {
		mockRepo.EXPECT().GetMixGrayConfigByName(ctx, mixGrayConfig.RuleName).Return(mixGrayConfigDomain, nil).Times(1)
		mockRepo.EXPECT().Update(ctx, mixGrayConfigDomain).Return(nil).Times(1)
		err := biz.UpdateMixGrayConfig(ctx, mixGrayConfig)
		require.NoError(t, err)
	})
}

func TestMixRuleConfigBiz_UpdateMixGrayRatio(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mocks.NewMockMixGrayRuleEngineInterface(ctrl)
	//logger := log.NewHelper(log.With(log.GetLogger(), "module", "taskmanager/pipeline"))
	ctx := context.Background()

	biz := &MixRuleConfigBiz{repo: mockRepo, log: log.NewHelper(log.DefaultLogger)}
	mixGrayConfig := &models.MixGrayConfig{
		RuleName:     "test",
		MixRatio:     0.5,
		ConfigStatus: models.MixGrayConfigStatusActive,
		Priority:     3,
		MixConfigs: []*models.MixConfig{
			{
				Product:          "testProduct",
				Version:          "testVersion",
				EngineModel:      "testEngine",
				Tag:              "testTag",
				MixAllocStrategy: models.TryAbandonMixed,
			},
		},
		RuleConfigs: []*models.MixRuleConfig{
			{
				Operate:   string(models.MixOperateEqual),
				ParamName: "engineModel",
				Value:     "mps-transcode-new",
				ValueType: "string",
			},
		}}
	mixGrayConfigDomain, _ := models.MixGrayConfigBizToDomain(mixGrayConfig)
	t.Run("success", func(t *testing.T) {
		mockRepo.EXPECT().GetMixGrayConfigByName(ctx, mixGrayConfig.RuleName).Return(mixGrayConfigDomain, nil).Times(1)
		mockRepo.EXPECT().UpdateMixRatio(ctx, mixGrayConfigDomain).Return(nil).Times(1)
		err := biz.UpdateMixGrayRatio(ctx, mixGrayConfig)
		require.NoError(t, err)

	})
}

func TestMixRuleConfigBiz_ActiveMixGrayConfig(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mocks.NewMockMixGrayRuleEngineInterface(ctrl)
	//logger := log.NewHelper(log.With(log.GetLogger(), "module", "taskmanager/pipeline"))
	ctx := context.Background()

	biz := &MixRuleConfigBiz{repo: mockRepo, log: log.NewHelper(log.DefaultLogger)}
	mixGrayConfig := &models.MixGrayConfig{
		RuleName:     "test",
		MixRatio:     0.5,
		ConfigStatus: models.MixGrayConfigStatusActive,
		Priority:     3,
		MixConfigs: []*models.MixConfig{
			{
				Product:          "testProduct",
				Version:          "testVersion",
				EngineModel:      "testEngine",
				Tag:              "testTag",
				MixAllocStrategy: models.TryAbandonMixed,
			},
		},
		RuleConfigs: []*models.MixRuleConfig{
			{
				Operate:   string(models.MixOperateEqual),
				ParamName: "engineModel",
				Value:     "mps-transcode-new",
				ValueType: "string",
			},
		}}
	mixGrayConfigDomain, _ := models.MixGrayConfigBizToDomain(mixGrayConfig)
	t.Run("success", func(t *testing.T) {
		mockRepo.EXPECT().GetMixGrayConfigByName(ctx, mixGrayConfig.RuleName).Return(mixGrayConfigDomain, nil).Times(1)
		mockRepo.EXPECT().Update(ctx, mixGrayConfigDomain).Return(nil).Times(1)
		err := biz.ActiveMixGrayConfig(ctx, mixGrayConfig.RuleName)
		require.NoError(t, err)

	})
}
func TestMixRuleConfigBiz_PauseMixGrayConfig(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mocks.NewMockMixGrayRuleEngineInterface(ctrl)
	//logger := log.NewHelper(log.With(log.GetLogger(), "module", "taskmanager/pipeline"))
	ctx := context.Background()

	biz := &MixRuleConfigBiz{repo: mockRepo, log: log.NewHelper(log.DefaultLogger)}
	mixGrayConfig := &models.MixGrayConfig{
		RuleName: "test",

		MixRatio:     0.5,
		ConfigStatus: models.MixGrayConfigStatusPause,
		Priority:     3,
		MixConfigs: []*models.MixConfig{
			{
				Product:          "testProduct",
				Version:          "testVersion",
				EngineModel:      "testEngine",
				Tag:              "testTag",
				MixAllocStrategy: models.TryAbandonMixed,
			},
		},
		RuleConfigs: []*models.MixRuleConfig{
			{
				Operate:   string(models.MixOperateEqual),
				ParamName: "engineModel",
				Value:     "mps-transcode-new",
				ValueType: "string",
			},
		}}
	mixGrayConfigDomain, _ := models.MixGrayConfigBizToDomain(mixGrayConfig)
	t.Run("success", func(t *testing.T) {
		mockRepo.EXPECT().GetMixGrayConfigByName(ctx, mixGrayConfig.RuleName).Return(mixGrayConfigDomain, nil).Times(1)
		mockRepo.EXPECT().Update(ctx, mixGrayConfigDomain).Return(nil).Times(1)
		err := biz.PauseMixGrayConfig(ctx, mixGrayConfig.RuleName)
		require.NoError(t, err)

	})
}

func TestMixRuleConfigBiz_DeleteMixGrayConfig(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mocks.NewMockMixGrayRuleEngineInterface(ctrl)
	//logger := log.NewHelper(log.With(log.GetLogger(), "module", "taskmanager/pipeline"))
	ctx := context.Background()

	biz := &MixRuleConfigBiz{repo: mockRepo, log: log.NewHelper(log.DefaultLogger)}
	mixGrayConfig := &models.MixGrayConfig{
		RuleName: "test",

		MixRatio:     0.5,
		ConfigStatus: models.MixGrayConfigStatusPause,
		Priority:     3,
		MixConfigs: []*models.MixConfig{
			{
				Product:          "testProduct",
				Version:          "testVersion",
				EngineModel:      "testEngine",
				Tag:              "testTag",
				MixAllocStrategy: models.TryAbandonMixed,
			},
		},
		RuleConfigs: []*models.MixRuleConfig{
			{
				Operate:   string(models.MixOperateEqual),
				ParamName: "engineModel",
				Value:     "mps-transcode-new",
				ValueType: "string",
			},
		}}
	t.Run("success", func(t *testing.T) {
		mockRepo.EXPECT().Delete(ctx, mixGrayConfig.RuleName).Return(nil).Times(1)
		err := biz.DeleteMixGrayConfig(ctx, mixGrayConfig.RuleName)
		require.NoError(t, err)

	})
}
func TestMixRuleConfigBiz_ListMixGrayConfig(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mocks.NewMockMixGrayRuleEngineInterface(ctrl)
	//logger := log.NewHelper(log.With(log.GetLogger(), "module", "taskmanager/pipeline"))
	ctx := context.Background()

	biz := &MixRuleConfigBiz{repo: mockRepo, log: log.NewHelper(log.DefaultLogger)}

	t.Run("success", func(t *testing.T) {
		mockRepo.EXPECT().List(ctx).Return(nil, nil).Times(1)
		_, err := biz.ListMixGrayConfig(ctx)
		require.NoError(t, err)

	})
}
func TestMixRuleConfigBiz_ListActiveMixGrayConfig(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mocks.NewMockMixGrayRuleEngineInterface(ctrl)
	//logger := log.NewHelper(log.With(log.GetLogger(), "module", "taskmanager/pipeline"))
	ctx := context.Background()

	biz := &MixRuleConfigBiz{repo: mockRepo, log: log.NewHelper(log.DefaultLogger)}

	t.Run("success", func(t *testing.T) {
		mockRepo.EXPECT().ListActiveMixGrayConfig(ctx).Return(nil, nil).Times(1)
		_, err := biz.ListActiveMixGrayConfig(ctx)
		require.NoError(t, err)

	})
}

func Test_IsMixed(t *testing.T) {
	tests := []struct {
		name           string
		mixGrayConfig  *models.MixGrayConfig
		expectedResult bool
		expectedError  error
	}{
		{
			name:           "MixGrayConfig is nil",
			mixGrayConfig:  nil,
			expectedResult: false,
			expectedError:  errors.NotFound("TASKMANAGER_MIXED_ERROR", "mixGrayConfig not found."),
		},
		{
			name: "MixRatio <= 0",
			mixGrayConfig: &models.MixGrayConfig{
				MixRatio: -0.1,
			},
			expectedResult: false,
			expectedError:  nil,
		},
		{
			name: "MixRatio >= 1",
			mixGrayConfig: &models.MixGrayConfig{
				MixRatio: 1.1,
			},
			expectedResult: true,
			expectedError:  nil,
		},
		{
			name: "MixRatio between 0 and 1",
			mixGrayConfig: &models.MixGrayConfig{
				MixRatio: 0.5,
			},
			expectedResult: false, // 或 true，取决于随机数
			expectedError:  nil,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			rand.Seed(time.Now().UnixNano())
			mixRuleConfigBiz := &MixRuleConfigBiz{}
			result, err := mixRuleConfigBiz.IsMixed(context.Background(), test.mixGrayConfig)
			if test.expectedError != nil {
				assert.Equal(t, test.expectedError, err)
			} else {
				assert.NoError(t, err)
				if test.name == "MixRatio between 0 and 1" {
					// 对于这个特定测试，我们不能确定结果，所以我们检查它是否是预期的两个结果之一
					assert.True(t, result == true || result == false)
				} else {
					assert.Equal(t, test.expectedResult, result)
				}
			}
		})
	}
}

func TestIsMixed_GrayRatioValidation(t *testing.T) {
	tests := []struct {
		name      string
		mixRatio  float32
		numCalls  int
		tolerance float64 // 允许的误差范围
	}{
		{
			name:      "MixRatio 0.1",
			mixRatio:  0.1,
			numCalls:  1000,
			tolerance: 0.5,
		},
		{
			name:      "MixRatio 0.5",
			mixRatio:  0.5,
			numCalls:  1000,
			tolerance: 0.05,
		},
		{
			name:      "MixRatio 0.9",
			mixRatio:  0.9,
			numCalls:  1000,
			tolerance: 0.05,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			rand.Seed(time.Now().UnixNano())
			mixRuleConfigBiz := &MixRuleConfigBiz{}
			mixGrayConfig := &models.MixGrayConfig{
				MixRatio: test.mixRatio,
			}

			trueCount := 0
			for i := 0; i < test.numCalls; i++ {
				result, err := mixRuleConfigBiz.IsMixed(context.Background(), mixGrayConfig)
				require.NoError(t, err)
				if result {
					trueCount++
				}
			}

			expectedTrueCount := float64(test.numCalls) * float64(test.mixRatio)
			actualTrueCount := float64(trueCount)
			expectedLowerBound := expectedTrueCount * (1 - test.tolerance)
			expectedUpperBound := expectedTrueCount * (1 + test.tolerance)

			fmt.Printf("ratio:%v ,numCalls:%v,Expected true count: %v, Actual true count: %v\n", test.mixRatio, test.numCalls, expectedTrueCount, actualTrueCount)
			assert.True(t, actualTrueCount >= expectedLowerBound && actualTrueCount <= expectedUpperBound,
				"Expected true count to be within %v and %v, but got %v", expectedLowerBound, expectedUpperBound, actualTrueCount)
		})
	}
}
