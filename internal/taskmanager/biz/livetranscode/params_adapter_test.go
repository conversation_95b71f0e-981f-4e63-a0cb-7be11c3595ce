package livetranscode

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"mpp/internal/taskmanager/biz/global_param_loader"
	"mpp/internal/taskmanager/models"
	lc "mpp/internal/taskmanager/models/livetranscode"
	"mpp/internal/taskscheduler/biz"

	. "github.com/bytedance/mockey"
	"github.com/patrickmn/go-cache"
	"github.com/stretchr/testify/assert"
)

var testP *LiveTransCodeParamsAdapter

var (
	testDomain                   = "testDomain"
	testToken                    = "testTpl"
	startRequestBodyNoneQuotaSet = `{
  "appName": "stage",
  "centerPublicAddr": "*************",
  "domain": "pull-l3.test.com",
  "gslbAuthTime": "682d3a64",
  "isCdnEdge": "false",
  "isEdge": "false",
  "params": {
    "streamNum": 1,
    "streamSource": "rtmp://*************:1935/stage/pull-l3.douyincdn.com_stream-11729250232951234?ali_mapstream_ignore=on&ali_ols_play=on&ali_ols_play_type=transcoding&ali_rtmp_quick_start=off&ali_rtmp_retain=0&vhost=pull-l3.test.com",
    "streamSourceBakVips": [
      "*************:1936",
      "*************:1935"
    ],
    "streamSourceBitrate": 2441,
    "streamSourceCodec": "h265",
    "streamSourceFps": 22,
    "streamSourceHeight": 1920,
    "streamSourceType": "stream",
    "streamSourceWidth": 1088,
    "streamTimestampOffset": 0.0,
    "streams": [
      {
        "destBakVips": [
          "*************:1935"
        ],
        "rtmpDest": "rtmp://*************/stage/stream-11729250232951234_ld?vhost=pull-l3.test.com&live_rtmp_test=on&ali_inter_auth_time=682d3a64&transcode=no&transcoded=true&ali_center_tran_pub=on",
        "vfilter": "no_ph_vf[0:v]blendimages=json={}[vfo]",
        "input_bypass": "-threads 3",
        "fps": "1:30",
        "vprofile": "high",
        "env": "pub",
        "type": "transcode",
        "ffver": "ffv5",
        "sw2main": 3,
        "width": -2,
        "output_bypass": "-cqfactor 25 -rule adapt_res_480*852#4d",
        "x264_params": "hierarchical-b=1:aq-strength=0.8:sync-lookahead=2:force-cfr=1:lookahead-threads=1:rc-lookahead=5:bframes=3:threads=3:keyint=infinite:scenecut=0:psy=0:trellis=0:me=dia:merange=16:subme=4:vbv-maxrate=1000:vbv-bufsize=1500:bitrate=1000:ref=2:partitions=i8x8,i4x4:mb-lowres=2:aq-mode=2:opt-lowres-intra=1:bi-skip=20:mbtree-motion-lag=1:bskip-ref=1:b-adapt=0 -vcopybr 200:1:1000 -copyts -force_key_frames source -ignore_subsei_ex 1 -qsd_flush -sei_hold_all -force_avsync -sei_order_by_dts -dec_force_header_on_key -colorinfo_bypass all",
        "aCodec": "copy",
        "height": 852,
        "vCodec": "h264"
      }
    ]
  },
  "publishNode": "cn8413",
  "pushDomain": "push-rtmp-l3.test.com",
  "quota": 300,
  "streamName": "stream-11729250232951234",
  "switchWorker": false,
  "templateName": "test-ld-1000-ffv5",
  "token": "ld"
}`
	startRequestBody = `{
  "appName": "stage",
  "centerPublicAddr": "*************",
  "domain": "pull-l3.test.com",
  "gslbAuthTime": "682d3a64",
  "isCdnEdge": "false",
  "isEdge": "false",
  "params": {
    "streamNum": 1,
    "streamSource": "rtmp://*************:1935/stage/pull-l3.douyincdn.com_stream-11729250232951234?ali_mapstream_ignore=on&ali_ols_play=on&ali_ols_play_type=transcoding&ali_rtmp_quick_start=off&ali_rtmp_retain=0&vhost=pull-l3.test.com",
    "streamSourceBakVips": [
      "*************:1936",
      "*************:1935"
    ],
    "streamSourceBitrate": 2441,
    "streamSourceCodec": "h265",
    "streamSourceFps": 22,
    "streamSourceHeight": 1920,
    "streamSourceType": "stream",
    "streamSourceWidth": 1088,
    "streamTimestampOffset": 0.0,
    "streams": [
      {
        "quotaSet": {
		  "cpu": 300,
          "gpu": 60,
          "mem": 70
        },	
        "destBakVips": [
          "*************:1935"
        ],
        "rtmpDest": "rtmp://*************/stage/stream-11729250232951234_ld?vhost=pull-l3.test.com&live_rtmp_test=on&ali_inter_auth_time=682d3a64&transcode=no&transcoded=true&ali_center_tran_pub=on",
        "vfilter": "no_ph_vf[0:v]blendimages=json={}[vfo]",
        "input_bypass": "-threads 3",
        "fps": "1:30",
        "vprofile": "high",
        "env": "pub",
        "type": "transcode",
        "ffver": "ffv5",
        "sw2main": 3,
        "width": -2,
        "output_bypass": "-cqfactor 25 -rule adapt_res_480*852#4d",
        "x264_params": "hierarchical-b=1:aq-strength=0.8:sync-lookahead=2:force-cfr=1:lookahead-threads=1:rc-lookahead=5:bframes=3:threads=3:keyint=infinite:scenecut=0:psy=0:trellis=0:me=dia:merange=16:subme=4:vbv-maxrate=1000:vbv-bufsize=1500:bitrate=1000:ref=2:partitions=i8x8,i4x4:mb-lowres=2:aq-mode=2:opt-lowres-intra=1:bi-skip=20:mbtree-motion-lag=1:bskip-ref=1:b-adapt=0 -vcopybr 200:1:1000 -copyts -force_key_frames source -ignore_subsei_ex 1 -qsd_flush -sei_hold_all -force_avsync -sei_order_by_dts -dec_force_header_on_key -colorinfo_bypass all",
        "aCodec": "copy",
        "height": 852,
        "vCodec": "h264"
      }
    ]
  },
  "publishNode": "cn8413",
  "pushDomain": "push-rtmp-l3.test.com",
  "quota": 300,
  "streamName": "stream-11729250232951234",
  "switchWorker": false,
  "templateName": "test-ld-1000-ffv5",
  "token": "ld"
}`
	stopTaskReq = `{
  "failOnError":false,
  "forceRestart":false,
  "ignoreReleaseFail":false,
  "isEdge":"",
  "schedulerForceRestart":false,
  "soft":false,
  "taskId":"8b4d3983-bead-30a1-b07c-971761220373"
}`
)

func init() {
	testP = NewLiveTransCodeParamsAdapter(nil, nil)
	testP.globalParamLoader = &global_param_loader.GlobalParamLoader{
		QuotaCache: cache.New(1*time.Minute, 5*time.Minute),
	}
	testP.globalParamLoader.QuotaCache.Set(toKey(testDomain, testToken, ""), map[string]int{
		toKey(testDomain, testToken, ""):      99,
		toKey(testDomain, testToken, "SD"):    55,
		toKey(testDomain, testToken, "SD265"): 66,
		toKey(testDomain, testToken, "HD"):    77,
		toKey(testDomain, testToken, "HD265"): 88,
		"pull-l3.test.com_ld":                 59,
	}, cache.NoExpiration)
	testP.globalParamLoader.QuotaCache.Set(toKey(testDomain, testToken, "SD"), map[string]int{
		toKey(testDomain, testToken, "SD"):    55,
		toKey(testDomain, testToken, "SD265"): 66,
	}, cache.NoExpiration)
	testP.globalParamLoader.QuotaCache.Set(toKey(testDomain, testToken, "SD265"), map[string]int{
		toKey(testDomain, testToken, "SD265"): 66,
	}, cache.NoExpiration)
	testP.globalParamLoader.QuotaCache.Set(toKey(testDomain, testToken, "HD"), map[string]int{
		toKey(testDomain, testToken, "HD"):    77,
		toKey(testDomain, testToken, "HD265"): 88,
	}, cache.NoExpiration)
	testP.globalParamLoader.QuotaCache.Set(toKey(testDomain, testToken, "HD265"), map[string]int{
		toKey(testDomain, testToken, "HD265"): 88,
	}, cache.NoExpiration)
	testP.globalParamLoader.QuotaCache.Set("pull-l3.test.com_ld", map[string]int{
		"pull-l3.test.com_ld": 59,
	}, cache.NoExpiration)
}
func GetTestParams(icodec string, iw, ih interface{}, ow, oh int) lc.TranscodeParams {
	rtmpDest := fmt.Sprintf("rtmp://video-center.alivecdn.com/live/xptest_%s?vhost=%s", testToken, testDomain)
	streams := map[string]interface{}{}
	streams["width"] = ow
	streams["height"] = oh
	streams["rtmpDest"] = rtmpDest
	streams["output_bypass"] = "-rule yy450p -cqfactor 25"

	tParams := lc.TranscodeParams{}
	tParams.StreamSourceCodec = icodec
	tParams.StreamSourceWidth = iw
	tParams.StreamSourceHeight = ih
	tParams.StreamNum = 1
	tParams.Streams = make([]interface{}, 0)
	tParams.Streams = append(tParams.Streams, streams)
	tParams.Domain = testDomain
	tParams.AppName = "live"
	tParams.StreamName = "xptest"
	tParams.Token = testToken
	return tParams
}

func TestRefineQuota(t *testing.T) {
	p1 := GetTestParams("", nil, nil, 1280, 720)
	assert.Equal(t, 99, testP.refineQuota(100, p1))
	//###########################################################
	p2 := GetTestParams("", nil, nil, 1280, 720)
	p2.StreamSourceVolatile = true
	assert.Equal(t, 100, testP.refineQuota(100, p2))
	//###########################################################
	p3 := GetTestParams("", 0, 0, 1280, -1)
	assert.Equal(t, 99, testP.refineQuota(100, p3))
	//###########################################################
	p4 := GetTestParams("", 0, 0, 1280, 720)
	assert.Equal(t, 55, testP.refineQuota(100, p4))
	//###########################################################
	p5 := GetTestParams("", 1280, 720, -1, 360)
	assert.Equal(t, 99, testP.refineQuota(100, p5))
	//###########################################################
	p6 := GetTestParams("", 1920, 1080, 1280, -1)
	assert.Equal(t, 55, testP.refineQuota(100, p6))
	//###########################################################
	p7 := GetTestParams("", 1920, 1080, -1, 720)
	assert.Equal(t, 55, testP.refineQuota(100, p7))
	//###########################################################
	p8 := GetTestParams("h265", 1920, 1080, -1, -1)
	assert.Equal(t, 88, testP.refineQuota(100, p8))
	//###########################################################
	p9 := GetTestParams("", 1280, 720, 1920, 1080)
	assert.Equal(t, 55, testP.refineQuota(100, p9))
}

func TestGenerateTaskId(t *testing.T) {
	assert.Equal(t, "75fc3fbd-cfe5-361a-93c3-3230659dfc85", GenerateTaskId("qt1.alivecdn.com", "live", "xptest", "unitest"))
}

func TestGenerateStreamId(t *testing.T) {
	assert.Equal(t, "b4a908e2-3459-36bb-a485-e57625e00afb", GenerateStreamId("qt1.alivecdn.com", "live", "xptest"))
}

func TestLiveTransCodeParamsAdapter_AssembleStartTaskRequest_MultiQuota(t *testing.T) {
	var streamParams lc.StreamParams
	err := json.Unmarshal([]byte(startRequestBody), &streamParams)
	assert.Nil(t, err)
	//参数修正
	taskId := GenerateTaskId(streamParams.Domain, streamParams.AppName, streamParams.StreamName, streamParams.Token)
	defer Mock((*global_param_loader.GlobalParamLoader).GetDefaultTag).Return("livetranscode_pub").Build().UnPatch()
	defer Mock((*global_param_loader.GlobalParamLoader).GetDefaultUnit).Return("cn-shanghai").Build().UnPatch()
	testP.CorrectLiveTransCodeStartParams(&streamParams)
	_, startTask, _, err := testP.AssembleStartTaskRequest(context.Background(), taskId, streamParams)
	assert.Nil(t, err)
	//t.Logf("startTask:%+v", startTask)
	assert.Equal(t, startTask.Quota["cpu"], int64(1))
}

func TestLiveTransCodeParamsAdapter_AssembleStartTaskRequest_NoneMultiQuota(t *testing.T) {
	var streamParams lc.StreamParams
	err := json.Unmarshal([]byte(startRequestBodyNoneQuotaSet), &streamParams)
	assert.Nil(t, err)
	//参数修正
	taskId := GenerateTaskId(streamParams.Domain, streamParams.AppName, streamParams.StreamName, streamParams.Token)
	defer Mock((*global_param_loader.GlobalParamLoader).GetDefaultTag).Return("livetranscode_pub").Build().UnPatch()
	defer Mock((*global_param_loader.GlobalParamLoader).GetDefaultUnit).Return("cn-shanghai").Build().UnPatch()
	testP.CorrectLiveTransCodeStartParams(&streamParams)
	_, startTask, _, err := testP.AssembleStartTaskRequest(context.Background(), taskId, streamParams)
	assert.Nil(t, err)
	//t.Logf("startTask:%+v", startTask)
	assert.Equal(t, startTask.Quota["cpu"], int64(590))
}

func TestLiveTransCodeParamsAdapter_AssembleStopTaskRequest(t *testing.T) {
	var stopRequest lc.LiveTransCodeStopRequest
	err := json.Unmarshal([]byte(stopTaskReq), &stopRequest)
	assert.Nil(t, err)
	defer Mock((*global_param_loader.GlobalParamLoader).GetDefaultUnit).Return("cn-shanghai").Build().UnPatch()
	_, stopTask := testP.AssembleStopTaskRequest(context.Background(), stopRequest)
	assert.Nil(t, err)
	//t.Logf("stopTask:%+v", stopTask)
	assert.Equal(t, stopTask.JobId, "8b4d3983-bead-30a1-b07c-971761220373")
	m, err := biz.ParseToMap(stopTask.Extend)
	assert.Nil(t, err)
	assert.Equal(t, m["unit"].(string), "cn-shanghai")
}

func TestLiveTransCodeParamsAdapter_AssembleStopTaskRequestOnMigrate(t *testing.T) {
	_, req := testP.AssembleStopTaskRequestOnMigrate(context.Background(), lc.LiveTransCodeMigrateRequest{
		TaskId: "8b4d3983-bead-30a1-b07c-971761220373",
		Domain: "pull-l3.test.com",
		App:    "stage",
		Token:  "testTpl",
		Stream: "stream-11729250232951234",
	})
	extend := biz.StringToMap(req.Extend)
	assert.Equal(t, extend["soft"], true)
}

func TestLiveTransCodeParamsAdapter_AssembleUpdateTaskRequest(t *testing.T) {
	_, result, err := testP.AssembleUpdateTaskRequest(context.Background(), "8b4d3983-bead-30a1-b07c-971761220373", "{\"streams\":[{\"width\":1280,\"height\":720,\"rtmpDest\":\"rtmp://video-center.alivecdn.com/live/xptest_testTpl?vhost=pull-l3.test.com\"}],\"streamSourceCodec\":\"\",\"streamSourceWidth\":0,\"streamSourceHeight\":0,\"streamNum\":1}")
	assert.Nil(t, err)
	assert.Equal(t, result.JobId, "8b4d3983-bead-30a1-b07c-971761220373")
}

func TestLiveTransCodeParamsAdapter_AssembleSendCommonCommand(t *testing.T) {
	_, req, err := testP.AssembleSendCommonCommand(context.Background(), "8b4d3983-bead-30a1-b07c-971761220373", "{\"streams\":[{\"width\":1280,\"height\":720,\"rtmpDest\":\"rtmp://video-center.alivecdn.com/live/xptest_testTpl?vhost=pull-l3.test.com\"}],\"streamSourceCodec\":\"\",\"streamSourceWidth\":0,\"streamSourceHeight\":0,\"streamNum\":1}", 1)
	assert.Nil(t, err)
	assert.Equal(t, req.CommandType, lc.COMMAN_COMMAND)
}

func TestLiveTransCodeParamsAdapter_AssembleListTranscodeRunningTaskReqe(t *testing.T) {
	req := testP.AssembleListTranscodeRunningTaskReq()
	assert.Equal(t, req.Product, "ols")
	assert.Equal(t, req.BizStatus, models.TaskStateActive)
}

func TestLiveTransCodeParamsAdapter_AssembleStartTaskRequestOnMigrate(t *testing.T) {
	task := &models.Task{
		TaskId: "8b4d3983-bead-30a1-b07c-971761220373",
		Metadata: map[string]string{
			"extend": "{\"taskSchedulerTag\":\"l22\"}",
		},
		Worker: &models.Worker{
			Ip: "*******",
		},
		Param: &models.TaskEngineParam{
			Param: "{\"streamInterSourceBakAddrs\":\"test\"}",
		},
	}
	testP.AssembleStartTaskRequestOnMigrate(context.Background(), task, false)
	m, err := biz.ParseToMap(task.Metadata["extend"])
	assert.Nil(t, err)
	assert.Equal(t, m["lastWorkerIp"], "*******")
	assert.Nil(t, m["taskSchedulerTag"])
	m, err = biz.ParseToMap(task.Param.Param)
	assert.NotNil(t, m["streamSourceBakVips"])
}

func TestLiveTransCodeParamsAdapter_CorrectLiveTransCodeCommandParams(t *testing.T) {
	task := &models.Task{
		TaskId: "8b4d3983-bead-30a1-b07c-971761220373",
		Metadata: map[string]string{
			"extend": "{\"taskSchedulerTag\":\"l22\"}",
		},
		Worker: &models.Worker{
			Ip: "*******",
		},
		Param: &models.TaskEngineParam{
			Param: "{\"streamInterSourceBakAddrs\":\"test\",    \"streams\": [\n      {\n        \"destBakVips\": [\n          \"*************:1935\"\n        ],\n        \"rtmpDest\": \"rtmp://*************/stage/stream-11729250232951234_ld?vhost=pull-l3.test.com&live_rtmp_test=on&ali_inter_auth_time=682d3a64&transcode=no&transcoded=true&ali_center_tran_pub=on\",\n        \"vfilter\": \"no_ph_vf[0:v]blendimages=json={}[vfo]\",\n        \"input_bypass\": \"-threads 3\",\n        \"fps\": \"1:30\",\n        \"vprofile\": \"high\",\n        \"env\": \"pub\",\n        \"type\": \"youku\",\n        \"ffver\": \"ffv5\",\n        \"sw2main\": 3,\n        \"width\": -2,\n        \"output_bypass\": \"-cqfactor 25 -rule adapt_res_480*852#4d\",\n        \"x264_params\": \"hierarchical-b=1:aq-strength=0.8:sync-lookahead=2:force-cfr=1:lookahead-threads=1:rc-lookahead=5:bframes=3:threads=3:keyint=infinite:scenecut=0:psy=0:trellis=0:me=dia:merange=16:subme=4:vbv-maxrate=1000:vbv-bufsize=1500:bitrate=1000:ref=2:partitions=i8x8,i4x4:mb-lowres=2:aq-mode=2:opt-lowres-intra=1:bi-skip=20:mbtree-motion-lag=1:bskip-ref=1:b-adapt=0 -vcopybr 200:1:1000 -copyts -force_key_frames source -ignore_subsei_ex 1 -qsd_flush -sei_hold_all -force_avsync -sei_order_by_dts -dec_force_header_on_key -colorinfo_bypass all\",\n        \"aCodec\": \"copy\",\n        \"height\": 852,\n        \"vCodec\": \"h264\"\n      }\n    ]}",
		},
	}
	params := biz.JsonToString(lc.LiveTransCodeSendCommandRequest{
		TaskId: "8b4d3983-bead-30a1-b07c-971761220373",
		Uri:    "/s/x",
		Body:   "{\"streamInterSourceBakAddrs\":\"test\"}",
	})
	err := testP.CorrectLiveTransCodeCommandParams(task, params)
	assert.Nil(t, err)
	m, err := biz.ParseToMap(task.Param.Param)
	assert.Nil(t, err)
	assert.Equal(t, m["agent"], "youku")
	task.Param.Param = "{\"streamInterSourceBakAddrs\":\"test\",    \"streams\": [\n      {\n        \"destBakVips\": [\n          \"*************:1935\"\n        ],\n        \"rtmpDest\": \"rtmp://*************/stage/stream-11729250232951234_ld?vhost=pull-l3.test.com&live_rtmp_test=on&ali_inter_auth_time=682d3a64&transcode=no&transcoded=true&ali_center_tran_pub=on\",\n        \"vfilter\": \"no_ph_vf[0:v]blendimages=json={}[vfo]\",\n        \"input_bypass\": \"-threads 3\",\n        \"fps\": \"1:30\",\n        \"vprofile\": \"high\",\n        \"env\": \"pub\",\n        \"type\": \"transcode\",\n        \"ffver\": \"ffv5\",\n        \"sw2main\": 3,\n        \"width\": -2,\n        \"output_bypass\": \"-cqfactor 25 -rule adapt_res_480*852#4d\",\n        \"x264_params\": \"hierarchical-b=1:aq-strength=0.8:sync-lookahead=2:force-cfr=1:lookahead-threads=1:rc-lookahead=5:bframes=3:threads=3:keyint=infinite:scenecut=0:psy=0:trellis=0:me=dia:merange=16:subme=4:vbv-maxrate=1000:vbv-bufsize=1500:bitrate=1000:ref=2:partitions=i8x8,i4x4:mb-lowres=2:aq-mode=2:opt-lowres-intra=1:bi-skip=20:mbtree-motion-lag=1:bskip-ref=1:b-adapt=0 -vcopybr 200:1:1000 -copyts -force_key_frames source -ignore_subsei_ex 1 -qsd_flush -sei_hold_all -force_avsync -sei_order_by_dts -dec_force_header_on_key -colorinfo_bypass all\",\n        \"aCodec\": \"copy\",\n        \"height\": 852,\n        \"vCodec\": \"h264\"\n      }\n    ]}"
	err = testP.CorrectLiveTransCodeCommandParams(task, params)
	assert.Nil(t, err)
	m, err = biz.ParseToMap(task.Param.Param)
	assert.Nil(t, err)
	assert.Equal(t, m["uri"], "/lvfmRPC")
}

func TestLiveTransCodeParamsAdapter_CorrectLiveTransCodeUpdateParams(t *testing.T) {
	task := &models.Task{
		TaskId: "8b4d3983-bead-30a1-b07c-971761220373",
		Metadata: map[string]string{
			"extend": "{\"taskSchedulerTag\":\"l22\"}",
		},
		Worker: &models.Worker{
			Ip: "*******",
		},
		Param: &models.TaskEngineParam{
			Param: "{\"streamInterSourceBakAddrs\":\"test\",    \"streams\": [\n      {\n        \"destBakVips\": [\n          \"*************:1935\"\n        ],\n        \"rtmpDest\": \"rtmp://*************/stage/stream-11729250232951234_ld?vhost=pull-l3.test.com&live_rtmp_test=on&ali_inter_auth_time=682d3a64&transcode=no&transcoded=true&ali_center_tran_pub=on\",\n        \"vfilter\": \"no_ph_vf[0:v]blendimages=json={}[vfo]\",\n        \"input_bypass\": \"-threads 3\",\n        \"fps\": \"1:30\",\n        \"vprofile\": \"high\",\n        \"env\": \"pub\",\n        \"type\": \"youku\",\n        \"ffver\": \"ffv5\",\n        \"sw2main\": 3,\n        \"width\": -2,\n        \"output_bypass\": \"-cqfactor 25 -rule adapt_res_480*852#4d\",\n        \"x264_params\": \"hierarchical-b=1:aq-strength=0.8:sync-lookahead=2:force-cfr=1:lookahead-threads=1:rc-lookahead=5:bframes=3:threads=3:keyint=infinite:scenecut=0:psy=0:trellis=0:me=dia:merange=16:subme=4:vbv-maxrate=1000:vbv-bufsize=1500:bitrate=1000:ref=2:partitions=i8x8,i4x4:mb-lowres=2:aq-mode=2:opt-lowres-intra=1:bi-skip=20:mbtree-motion-lag=1:bskip-ref=1:b-adapt=0 -vcopybr 200:1:1000 -copyts -force_key_frames source -ignore_subsei_ex 1 -qsd_flush -sei_hold_all -force_avsync -sei_order_by_dts -dec_force_header_on_key -colorinfo_bypass all\",\n        \"aCodec\": \"copy\",\n        \"height\": 852,\n        \"vCodec\": \"h264\"\n      }\n    ]}",
		},
	}
	params := biz.JsonToString(lc.LiveTransCodeUpdateRequest{
		TaskId: "8b4d3983-bead-30a1-b07c-971761220373",
		Streams: []interface{}{
			map[string]interface{}{
				"destBakVips": []interface{}{
					"*************:1935",
				},
				"rtmpDest":     "rtmp://*************/stage/stream-11729250232951234_ld?vhost=pull-l3}",
				"input_bypass": "-threads 4",
			},
		},
	})
	err := testP.CorrectLiveTransCodeUpdateParams(task, params)
	assert.Nil(t, err)
	m, err := biz.ParseToMap(task.Param.Param)
	assert.Nil(t, err)
	a := m["streams"].([]interface{})[0].(map[string]interface{})
	assert.Equal(t, a["input_bypass"], "-threads 4")
}

func TestLiveTransCodeParamsAdapter_CorrectLiveTransCodeAttachParams(t *testing.T) {
	task := &models.Task{
		TaskId: "8b4d3983-bead-30a1-b07c-971761220373",
		Metadata: map[string]string{
			"extend": "{\"taskSchedulerTag\":\"l22\"}",
		},
		Worker: &models.Worker{
			Ip: "*******",
		},
		Param: &models.TaskEngineParam{
			Param: "{\"streamInterSourceBakAddrs\":\"test\",    \"streams\": [\n      {\n        \"destBakVips\": [\n          \"*************:1935\"\n        ],\n        \"rtmpDest\": \"rtmp://*************/stage/stream-11729250232951234_ld?vhost=pull-l3.test.com&live_rtmp_test=on&ali_inter_auth_time=682d3a64&transcode=no&transcoded=true&ali_center_tran_pub=on\",\n        \"vfilter\": \"no_ph_vf[0:v]blendimages=json={}[vfo]\",\n        \"input_bypass\": \"-threads 3\",\n        \"fps\": \"1:30\",\n        \"vprofile\": \"high\",\n        \"env\": \"pub\",\n        \"type\": \"youku\",\n        \"ffver\": \"ffv5\",\n        \"sw2main\": 3,\n        \"width\": -2,\n        \"output_bypass\": \"-cqfactor 25 -rule adapt_res_480*852#4d\",\n        \"x264_params\": \"hierarchical-b=1:aq-strength=0.8:sync-lookahead=2:force-cfr=1:lookahead-threads=1:rc-lookahead=5:bframes=3:threads=3:keyint=infinite:scenecut=0:psy=0:trellis=0:me=dia:merange=16:subme=4:vbv-maxrate=1000:vbv-bufsize=1500:bitrate=1000:ref=2:partitions=i8x8,i4x4:mb-lowres=2:aq-mode=2:opt-lowres-intra=1:bi-skip=20:mbtree-motion-lag=1:bskip-ref=1:b-adapt=0 -vcopybr 200:1:1000 -copyts -force_key_frames source -ignore_subsei_ex 1 -qsd_flush -sei_hold_all -force_avsync -sei_order_by_dts -dec_force_header_on_key -colorinfo_bypass all\",\n        \"aCodec\": \"copy\",\n        \"height\": 852,\n        \"vCodec\": \"h264\"\n      }\n    ]}",
		},
	}
	testP.CorrectLiveTransCodeAttachParams(task, "", lc.AttachmentTypePIC, "add")
	testP.CorrectLiveTransCodeAttachParams(task, "", lc.AttachmentTypeTEXT, "add")
	testP.CorrectLiveTransCodeAttachParams(task, "", "opt", "add")
}
