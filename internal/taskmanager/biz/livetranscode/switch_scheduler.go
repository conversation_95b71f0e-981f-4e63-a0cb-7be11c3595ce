package livetranscode

import (
	"context"
	"strings"

	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/custom_types"
	"mpp/internal/taskscheduler/biz"

	"github.com/go-kratos/kratos/v2/log"

	pb "proto.mpp/api/taskmanager/v1"
)

type SchedulerSwitchOperator struct {
	log            *log.Helper
	switchOperator *common.SwitchService
}

func NewSchedulerSwitchOperator(logger log.Logger, switchOperator *common.SwitchService) *SchedulerSwitchOperator {
	return &SchedulerSwitchOperator{
		log:            log.NewHelper(logger),
		switchOperator: switchOperator,
	}
}

func (s *SchedulerSwitchOperator) SwitchScheduler(ctx context.Context, task *pb.StartTaskRequest) []map[string]interface{} {
	// 任务调度标签
	//tags := t.GetAllSchedulerTags()
	if task.Metadata == nil {
		task.Metadata = custom_types.Metadata{}
	}
	task.Metadata["taskSchedulerTag"] = "default"

	// 非直播直接返回默认资源池
	if task.EngineModel != models.LiveTranscode {
		return nil
	}

	// paramMap提前转换为临时变量，避免多次用到多次转换
	paramMap := biz.StringToMap(task.EngineParams)
	if paramMap == nil {
		log.Errorf("get transcode stream fail,paramMap is nil, taskId:%s", task.TaskId)
		return nil
	}

	// 提取 domain/app/streamName/token
	streamMetas, err := ExtractStreamStrMeta(task.TaskId, task.EngineParams)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Failed to extract stream meta due to err %s, taskId:%s", err, task.TaskId)
		return nil
	}
	taskSwitchParam := &common.SwitchParam{
		Product:     task.Product,
		EngineModel: task.EngineModel,
		Tag:         task.Tag,
		Domain:      streamMetas[0],
		App:         streamMetas[1],
		Stream:      streamMetas[2],
		Token:       streamMetas[3],
		TaskId:      task.TaskId,
	}

	// 资源池选择 & 兜底配置查询
	tsTag, backupConfig := s.SelectScheduler(ctx, taskSwitchParam)
	task.Metadata["taskSchedulerTag"] = tsTag

	switchStreams := make([]map[string]interface{}, 0)
	switchStreamsOri := getTranscodeParamByMap(paramMap, "switchStreams")
	if switchStreamsOri != nil {
		switchStreamsArray := switchStreamsOri.([]interface{})
		for _, v := range switchStreamsArray {
			switchStreams = append(switchStreams, v.(map[string]interface{}))
		}
	}

	// 切流更换参数 & 兜底数据保存
	if tsTag == "l2" || tsTag == "l22" {
		switchStreams = s.switchFromCenterToL2(task, switchStreams, paramMap, backupConfig, tsTag)
	} else if tsTag == "public" {
		switchStreams = s.switchFromCenterToPublic(switchStreams, paramMap, backupConfig)
	} else {
		switchStreams = s.switchStreamForCenter(switchStreams, paramMap, backupConfig)
	}
	return switchStreams
}

func (s *SchedulerSwitchOperator) SelectScheduler(ctx context.Context, switchParam *common.SwitchParam) (string, *common.BackupConfig) {
	// 判断三元组 + domian、app、token是否支持下沉  （三元组里包含了标签，不需要增加标签是否下沉的配置）
	// 根据下沉比例选择scheduler
	// 匹配验证
	schedulerTag, backupConfig := s.switchOperator.SelectScheduler(switchParam)
	if schedulerTag == "" {
		s.log.WithContext(ctx).Warnf("select scheduler fail, switchParam:%+v", switchParam)
		schedulerTag = "default"
	}
	// 将切流值做个映射与资源池key做个映射
	if strings.Contains(schedulerTag, "l2Cpu2") {
		schedulerTag = "l22"
	} else if strings.Contains(schedulerTag, "l2") {
		schedulerTag = "l2"
	} else if strings.Contains(schedulerTag, "public") {
		schedulerTag = "public"
	} else if strings.Contains(schedulerTag, "edge") {
		schedulerTag = "edge"
	} else {
		schedulerTag = "default"
	}

	// 没有配置兜底策略时，默认为空  --- 改为
	if backupConfig == nil {
		s.log.WithContext(ctx).Warnf("select scheduler backupConfig is nil, switchParam:%+v", switchParam)
		backupConfig = &common.BackupConfig{
			CanHoldByPubTag: false,
			Order:           "",
		}
	}
	return schedulerTag, backupConfig
}

func (s *SchedulerSwitchOperator) switchStreamForCenter(switchStreams []map[string]interface{}, paramMap map[string]interface{}, backupConfig *common.BackupConfig) []map[string]interface{} {
	sf := strings.Split(backupConfig.Order, ",")
	sf = removeElem(sf, "center")
	sf = removeElem(sf, "default")
	for _, v := range sf {
		switchStream := s.assembleSwitchStream(v, backupConfig, paramMap)
		if switchStream != nil {
			switchStreams = append(switchStreams, switchStream)
		}
	}
	return switchStreams
}

func (s *SchedulerSwitchOperator) switchFromCenterToPublic(switchStreams []map[string]interface{}, paramMap map[string]interface{}, backupConfig *common.BackupConfig) []map[string]interface{} {
	sf := strings.Split(backupConfig.Order, ",")
	sf = removeElem(sf, "public")
	for _, v := range sf {
		switchStream := s.assembleSwitchStream(v, backupConfig, paramMap)
		if switchStream != nil {
			switchStreams = append(switchStreams, switchStream)
		}
	}
	return switchStreams
}

func (s *SchedulerSwitchOperator) switchFromCenterToL2(task *pb.StartTaskRequest, switchStreams []map[string]interface{}, paramMap map[string]interface{}, backupConfig *common.BackupConfig, tsType string) []map[string]interface{} {
	sf := strings.Split(backupConfig.Order, ",")
	sf = removeElem(sf, tsType)
	stream := GetStreamByMap(paramMap)
	for _, v := range sf {
		switchStream := s.assembleSwitchStream(v, backupConfig, paramMap)
		if switchStream != nil {
			switchStreams = append(switchStreams, switchStream)
		}
	}
	// 切L2替换流地址
	streamSource := getTranscodeParamByMap(paramMap, "streamSource").(string)
	if isInnerStream(streamSource) {
		centerPublicAddr := getTranscodeParamByMap(paramMap, "centerPublicAddr")
		if centerPublicAddr != nil {
			streamSource = strings.Replace(streamSource, strings.Split(streamSource, "/")[2], centerPublicAddr.(string)+":1936", 1)
		} else {
			log.Errorf("get centerPublicAddr fail ,paramMap:%+v", paramMap)
		}
	}
	streamDest := stream["rtmpDest"].(string)
	if isInnerStream(streamDest) {
		centerPublicAddr := getTranscodeParamByMap(paramMap, "centerPublicAddr")
		if centerPublicAddr != nil {
			streamDest = strings.Replace(streamDest, strings.Split(streamDest, "/")[2], centerPublicAddr.(string), 1)
		} else {
			log.Errorf("get centerPublicAddr fail ,paramMap:%+v", paramMap)
		}
	}
	streamDest += "&l2_transcode=yes"
	stream["rtmpDest"] = streamDest
	// stream塞回task
	//paramMapNew := biz.StringToMap(task.EngineParams)
	paramMap["streamSource"] = streamSource
	paramMap["streams"] = []interface{}{stream}
	paramMap["streamSourceBakVips"] = paramMap["streamL2SourceBakAddrs"]
	task.EngineParams = biz.JsonToString(paramMap)
	return switchStreams
}

func GetStreamByMap(paramMap map[string]interface{}) map[string]interface{} {
	streams := paramMap["streams"].([]interface{})
	if stream, ok := streams[0].(map[string]interface{}); ok {
		return stream
	}
	log.Errorf("get transcode stream fail,streams:%s", streams)
	return nil
}

func getTranscodeParamByMap(paramMap map[string]interface{}, key string) interface{} {
	if _, ok := paramMap[key]; !ok {
		log.Errorf("get transcode stream fail,key:%s", key)
		return nil
	}
	return paramMap[key]
}

func removeElem(ori []string, elem string) []string {
	var result []string
	for _, v := range ori {
		if v != elem {
			result = append(result, v)
		}
	}
	return result
}

func isInnerStream(stream string) bool {
	if stream == "" {
		log.Warnf("stream is empty for judge inner stream")
		return false
	}
	if strings.HasPrefix(stream, "rtmp://10.") || strings.HasPrefix(stream, "rtmp://11.") || strings.HasPrefix(stream, "rtmp://33.") {
		return true
	}
	return false
}

func (s *SchedulerSwitchOperator) assembleSwitchStream(backupType string, backupConfig *common.BackupConfig, paramMap map[string]interface{}) map[string]interface{} {
	stream := GetStreamByMap(paramMap)
	switch backupType {
	case "center", "default", "public":
		// 注意，edge场景，业务层传入switchStream是指定大驼峰的，保持一直
		switchStream := make(map[string]interface{}, 0)
		switchStream["StreamSource"] = getTranscodeParamByMap(paramMap, "streamSource")
		switchStream["RtmpDest"] = stream["rtmpDest"]
		switchStream["Env"] = "pub"
		switchStream["StreamSourceBakVips"] = getTranscodeParamByMap(paramMap, "streamInterSourceBakAddrs")
		switchStream["TaskSchedulerTag"] = backupType
		if backupConfig.CanHoldByPubTag {
			switchStream["Tag"] = "livetranscode_pub"
		}
		return switchStream
	case "l2", "l22":
		// todo 增加一层下沉判断
		switchStream := make(map[string]interface{}, 0)
		streamSource := getTranscodeParamByMap(paramMap, "streamSource").(string)
		if isInnerStream(streamSource) {
			centerPublicAddr := getTranscodeParamByMap(paramMap, "centerPublicAddr")
			if centerPublicAddr != nil {
				streamSource = strings.Replace(streamSource, strings.Split(streamSource, "/")[2], centerPublicAddr.(string)+":1936", 1)
			} else {
				log.Errorf("get centerPublicAddr fail ,paramMap:%+v", paramMap)
			}
		}
		streamDest := stream["rtmpDest"].(string)
		if isInnerStream(streamDest) {
			centerPublicAddr := getTranscodeParamByMap(paramMap, "centerPublicAddr")
			if centerPublicAddr != nil {
				streamDest = strings.Replace(streamDest, strings.Split(streamDest, "/")[2], centerPublicAddr.(string), 1)
			} else {
				log.Errorf("get centerPublicAddr fail ,paramMap:%+v", paramMap)
			}
		}
		switchStream["StreamSource"] = streamSource
		switchStream["RtmpDest"] = streamDest
		switchStream["Env"] = "pub"
		switchStream["StreamSourceBakVips"] = getTranscodeParamByMap(paramMap, "streamL2SourceBakAddrs")
		if backupConfig.CanHoldByPubTag {
			switchStream["Tag"] = "livetranscode_pub"
		}
		switchStream["TaskSchedulerTag"] = backupType
		return switchStream
	}
	return nil
}
