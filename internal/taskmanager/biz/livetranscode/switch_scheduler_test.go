package livetranscode

import (
	"context"
	"encoding/json"
	"testing"

	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskscheduler/biz"

	pb "proto.mpp/api/taskmanager/v1"

	. "github.com/bytedance/mockey"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
)

var (
	switchSchedulerOperator       = &SchedulerSwitchOperator{}
	requestLiveTransCodeStartBody = `{
		"appName": "stage",
		"domain": "pull-l3.douyincdn.com",
		"engineModel": "livetranscode",
		"product":     "live",
		"tag":         "test",
		"extend": {
			"resourcePoolType": "DEFAULT"
		},
		"gslbAuthTime": "61d4ff85",
		"isCdnEdge": "false", 
		"isEdge": "false", 
  		"engineParams": "{\"domain\":\"pull-l3.douyincdn.com\",\"appName\":\"stage\",\"token\":\"hd5\",\"streamName\":\"stream-110149065950888018\",\"centerPublicAddr\":\"centerPublicAddr\",\"streamNum\":1,\"streamSource\":\"rtmp://***********/stage/pull-l3.douyincdn.com_stream-110149065950888018?ali_ols_play=on&ali_rtmp_quick_start=on&ali_rtmp_retain=0&vhost=pull-l3.douyincdn.com\",\"streamSourceBitrate\":2441,\"streamSourceCodec\":\"h265\",\"streamSourceFps\":20,\"streamSourceHeight\":1920,\"streamSourceType\":\"stream\",\"streamSourceWidth\":1088,\"streamTimestampOffset\":0.0,\"streams\":[{\"output_bypass\":\"-cqfactor 25 -annexb 0 -rule adapt_res_1280*720\",\"aCodec\":\"copy\",\"rtmpDest\":\"rtmp://**********/stage/stream-110149065950888018_hd5?vhost=pull-l3.douyincdn.com&live_rtmp_test=on&ali_inter_auth_time=61d4ff85&transcode=no&transcoded=true&ali_center_tran_pub=on\",\"height\":720,\"x265_params\":\"rc-lookahead=3:frame-threads=4:b-adapt=0:open-gop=0:hierarchical-b=0:b-pyramid=0:bframes=1:info=0:weightp=0:weightb=0:ref=2:subme=2:me=dia:keyint=-1:scenecut=0:bitrate=800:vbv-maxrate=800:vbv-bufsize=1000:ipratio=1.0:qcom=1.0 -vcopybr 200:0.9:2000:10:0.1 -copyts -vadjust_ts '(1,1)' -force_key_frames source -ignore_subsei\",\"vfilter\":\"no_ph_vf[0:v]blendimages=json={}[vfo]\",\"afilter\":\"\",\"vCodec\":\"libs265\",\"width\":-2,\"fps\":\"1:15\",\"env\":\"pub\",\"type\":\"transcode\"}],\"switchStreams\":[]}",
		"publishNode": "cn684",
		"pushDomain": "push-rtmp-l3.douyincdn.com",
		"streamName": "stream-110149065950888018",
		"switchWorker": false,
		"templateName": "douyin-h265-hd5",
		"token": "hd5"
	}`
)

func TestSwitchScheduler(t *testing.T) {
	//defer Mock((*SchedulerSwitchOperator).extractDomainAppToken).Return("domain", "app", "template", "stream").Build().UnPatch()
	defer Mock((*common.SwitchService).SelectScheduler).Return("l2", &common.BackupConfig{
		CanHoldByPubTag: true,
		Order:           "l22",
	}).Build().UnPatch()
	//defer Mock((*SchedulerSwitchOperator).switchFromCenterToL2).Return([]map[string]interface{}{
	//	{
	//		"RtmpDest":            "rtmp://*******/ali-mmyzpush.v.momocdn.com/momo/m_164134851548389fa5a51930e8b9f183_hevc2avc?live_rtmp_test=on&ali_inter_auth_time=61d4fec8&transcode=no&transcoded=true&edge_transcode=yes",
	//		"StreamSource":        "",
	//		"Tag":                 "node",
	//		"Env":                 "cdnEdge",
	//		"StreamSourceBakVips": "test",
	//	},
	//}).Build().UnPatch()
	req := &pb.StartTaskRequest{}
	err := json.Unmarshal([]byte(requestLiveTransCodeStartBody), req)
	assert.Nil(t, err)
	s := switchSchedulerOperator.SwitchScheduler(context.Background(), req)
	assert.NotNil(t, s[0])
}

func TestSwitchFromCenterToPublic(t *testing.T) {
	task := &pb.StartTaskRequest{}
	err := json.Unmarshal([]byte(requestLiveTransCodeStartBody), task)
	assert.Nil(t, err)
	backupConfig := &common.BackupConfig{
		CanHoldByPubTag: true,
		Order:           "l2",
	}

	switchStreams := switchSchedulerOperator.switchFromCenterToPublic(nil, biz.StringToMap(task.EngineParams), backupConfig)
	assert.NotEmpty(t, switchStreams)
}

func TestSwitchStreamForCenter(t *testing.T) {
	task := &pb.StartTaskRequest{}
	err := json.Unmarshal([]byte(requestLiveTransCodeStartBody), task)
	assert.Nil(t, err)
	backupConfig := &common.BackupConfig{
		CanHoldByPubTag: true,
		Order:           "public",
	}

	switchStreams := switchSchedulerOperator.switchStreamForCenter(nil, biz.StringToMap(task.EngineParams), backupConfig)
	assert.NotEmpty(t, switchStreams)
}

func GetTranscodeParam(param, key string) interface{} {
	paramMap := biz.StringToMap(param)
	if paramMap == nil {
		log.Errorf("get transcode stream fail,param:%s", param)
		return ""
	}
	if _, ok := paramMap[key]; !ok {
		log.Errorf("get transcode stream fail,key:%s", key)
		return ""
	}
	return paramMap[key]
}

func TestGetTranscodeParam(t *testing.T) {
	task := &pb.StartTaskRequest{}
	err := json.Unmarshal([]byte(requestLiveTransCodeStartBody), task)
	assert.Nil(t, err)
	v := GetTranscodeParam(task.EngineParams, "centerPublicAddr")
	assert.Equal(t, v.(string), "centerPublicAddr")
}
