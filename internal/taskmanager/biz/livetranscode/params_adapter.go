package livetranscode

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"mpp/internal/taskmanager/biz/global_param_loader"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/custom_types"
	lc "mpp/internal/taskmanager/models/livetranscode"
	"mpp/internal/taskscheduler/biz"
	"mpp/pkg/utils"

	pb "proto.mpp/api/taskmanager/v1"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type LiveTransCodeParamsAdapter struct {
	globalParamLoader *global_param_loader.GlobalParamLoader
	log               *log.Helper
}

func NewLiveTransCodeParamsAdapter(globalParamLoader *global_param_loader.GlobalParamLoader, logger log.Logger) *LiveTransCodeParamsAdapter {
	return &LiveTransCodeParamsAdapter{
		globalParamLoader: globalParamLoader,
		log:               log.NewHelper(logger),
	}
}

func (l *LiveTransCodeParamsAdapter) CorrectLiveTransCodeStartParams(streamParams *lc.StreamParams) {
	//参数修正
	//类型校验
	if streamParams.Params.StreamSourceWidth != nil {
		if _, ok := streamParams.Params.StreamSourceWidth.(int); !ok {
			streamParams.Params.StreamSourceWidth = nil
		}
	}
	if streamParams.Params.StreamSourceHeight != nil {
		if _, ok := streamParams.Params.StreamSourceHeight.(int); !ok {
			streamParams.Params.StreamSourceHeight = nil
		}
	}
	//空值校验
	//if streamParams.Params.AllAudioCopy == nil {
	//	streamParams.Params.AllAudioCopy = 0
	//}
	// 直播转码融合架构适配
	streamParams.Params.CenterPublicAddr = streamParams.CenterPublicAddr
	// net降本需求，历史默认传入的streamSourceBakVips即是streamL2SourceBakAddrs，新增streamL2SourceBakAddrs字段用以保存
	streamParams.Params.StreamL2SourceBakAddrs = streamParams.Params.StreamSourceBakVips
	streamParams.Params.StreamSourceBakVips = streamParams.Params.StreamInterSourceBakAddrs

	// 字节码率实时回调增加参数补充
	streamParams.Params.PushDomain = streamParams.PushDomain
	streamParams.Params.Domain = streamParams.Domain
	streamParams.Params.AppName = streamParams.AppName
	streamParams.Params.StreamName = streamParams.StreamName
	streamParams.Params.Token = streamParams.Token

	// 上云L2共用适配 如果是云上区域，不传unit，则取默认unit
	if len(streamParams.Unit) == 0 {
		streamParams.Unit = l.globalParamLoader.GetDefaultUnit()
	} else {
		streamParams.Unit = streamParams.Unit + "-cloud"
	}
	// 保存备用推流地址，不需要持久化
	//List<Object> switchStreams = streamParams.Params.SwitchStreams;
	//streamParams.Params.SwitchStreams = new ArrayList<Object>();
	streamParams.Extend = lc.StreamInfoExtend{}
	streamParams.Extend.Token = streamParams.Token
	streamParams.Extend.Unit = streamParams.Unit
	//参考老调度InitTask方法
}

func (l *LiveTransCodeParamsAdapter) AssembleStartTaskRequest(ctx context.Context, taskId string, streamParams lc.StreamParams) (context.Context, *pb.StartTaskRequest, map[string]interface{}, error) {
	quota := streamParams.Quota
	stream, err := biz.ParseToMap(streamParams.Params.Streams[0])
	if err != nil {
		return nil, nil, nil, err
	}
	taskInfo := map[string]interface{}{
		"taskId":                taskId,
		"token":                 streamParams.Token,
		"isEdge":                streamParams.IsEdge,
		"schedulerForceRestart": l.globalParamLoader.StillRunningForceRestart,
		"soft":                  false,
		"ignoreReleaseFail":     false,
		"failOnError":           streamParams.Params.Streams != nil && len(streamParams.Params.Streams) > 0,
		"forceRestart":          false,
	}
	tag := l.globalParamLoader.GetDefaultTag()
	if class, ok := stream["class"]; ok && len(class.(string)) > 0 {
		tag = "livetranscode_" + class.(string) + "_" + l.globalParamLoader.GetEnv()
	}
	task := &pb.StartTaskRequest{
		//老调度无该参数传递
		//RequestId:       js.Get("trace").Get("requestId").ToString(),
		TaskId:      taskId,
		JobId:       taskId,
		Quota:       map[string]int64{"cpu": int64(l.refineQuota(quota, streamParams.Params) * 10)}, // todo 确认是否影响GPU调度
		Product:     "ols",
		EngineModel: models.LiveTranscode,
		Tag:         tag,
		//老调度无该参数传递
		//EngineParamsUrl: getBodyOrQueryString(js, query, "engineParamsUrl"),
		//Notify:   getBodyOrQueryString(js, query, "notify"),
		//Trace:    getBodyOrQueryString(js, query, "trace"),
		//UserData: getBodyOrQueryString(js, query, "userData"),
		Affinity: nil,
		QuotaSet: nil,
		Domain:   streamParams.Domain,
		//UserId:   getBodyOrQueryString(js, query, "userId"),
		//并行转码调度参数
		//ScheduleParams: getBodyOrQueryString(js, query, "scheduleParams"),
	}
	forceRestart := false
	if f, err := strconv.ParseBool(streamParams.ForceRestart); err == nil {
		forceRestart = f
		taskInfo["forceRestart"] = forceRestart
	}
	extend := map[string]string{
		"taskId":                taskId,
		"token":                 streamParams.Token,
		"isEdge":                streamParams.IsEdge,
		"forceRestart":          strconv.FormatBool(forceRestart),
		"schedulerForceRestart": strconv.FormatBool(l.globalParamLoader.StillRunningForceRestart),
		"traceId":               streamParams.TraceId,
	}
	if task.Metadata == nil {
		task.Metadata = custom_types.Metadata{}
	}
	task.Metadata["extend"] = biz.JsonToString(extend)

	for _, transCodeStream := range streamParams.Params.Streams {
		stream, err = biz.ParseToMap(transCodeStream)
		if err != nil {
			log.Errorf("parse extend data quotaSet failed while start task:%s, error:%v", taskId, err)
			return nil, nil, nil, err
		}
		if q, ok := stream["quotaSet"]; ok {
			quotaSet, err := biz.ParseToMap(q)
			if err != nil {
				log.Errorf("parse extend data quotaSet failed while start task:%s, error:%v", taskId, err)
				return nil, nil, taskInfo, err
			}
			for quotaType, v := range quotaSet {
				quotaValue := int64(v.(float64))
				if quotaType == "cpu" {
					quotaValue = quotaValue * 10
				}
				if _, exist := task.Quota[quotaType]; exist {
					task.Quota[quotaType] += quotaValue
				} else {
					task.Quota[quotaType] = quotaValue
				}
			}
		}
	}
	if len(task.Quota) > 1 {
		task.Quota["cpu"] = 1
	}
	if ok, parallelNum := isDistributedTaskCheck(streamParams); ok {
		task.ScheduleParams = biz.JsonToString(map[string]int{
			"parallelNum": parallelNum,
		})
	}
	streamParams.Params.Quota = int(task.Quota["cpu"] / 10)
	task.EngineParams = biz.JsonToString(streamParams.Params)
	if task.EngineParams == "" {
		l.log.WithContext(ctx).Errorf("livetranscode engineParams is empty, taskId:%s", taskId)
	}
	newCtx := utils.SetCustomRequestId(ctx, task.RequestId)
	task.RequestId = utils.GetRequestIdFromContext(newCtx)
	return newCtx, task, taskInfo, nil
}

func (l *LiveTransCodeParamsAdapter) AssembleStopTaskRequestOnMigrate(ctx context.Context, request lc.LiveTransCodeMigrateRequest) (context.Context, *pb.StopTaskRequest) {
	if len(request.TaskId) == 0 {
		request.TaskId = GenerateTaskId(request.Domain, request.App, request.Stream, request.Token)
	}
	extend := make(map[string]interface{})
	extend["soft"] = true

	stopTaskRequest := &pb.StopTaskRequest{
		JobId:  request.TaskId,
		TaskId: request.TaskId,
		Extend: biz.JsonToString(extend),
	}
	newCtx := utils.SetCustomRequestId(ctx, stopTaskRequest.RequestId)
	stopTaskRequest.RequestId = utils.GetRequestIdFromContext(newCtx)
	return newCtx, stopTaskRequest
}

func (l *LiveTransCodeParamsAdapter) AssembleStopTaskRequest(ctx context.Context, request lc.LiveTransCodeStopRequest) (context.Context, *pb.StopTaskRequest) {
	if len(request.Unit) == 0 {
		request.Unit = l.globalParamLoader.GetDefaultUnit()
	} else {
		request.Unit = request.Unit + "-cloud"
	}
	request.IgnoreReleaseFail = l.globalParamLoader.IgnoreReleaseFail
	stopTaskRequest := &pb.StopTaskRequest{
		TaskId: request.TaskId,
		JobId:  request.TaskId,
	}
	if stopTaskRequest.JobId == "" && stopTaskRequest.TaskId != "" {
		stopTaskRequest.JobId = stopTaskRequest.TaskId
	}
	if stopTaskRequest.TaskId == "" {
		stopTaskRequest.TaskId = stopTaskRequest.JobId
	}
	stopTaskRequest.Extend = biz.JsonToString(request)
	newCtx := utils.SetCustomRequestId(ctx, stopTaskRequest.RequestId)
	stopTaskRequest.RequestId = utils.GetRequestIdFromContext(newCtx)
	return newCtx, stopTaskRequest
}

func (l *LiveTransCodeParamsAdapter) AssembleUpdateTaskRequest(ctx context.Context, taskId string, reqJson string) (context.Context, *pb.UpdateTaskRequest, error) {
	commandTaskReq := &pb.UpdateTaskRequest{
		JobId:        taskId,
		TaskId:       taskId,
		EngineParams: reqJson,
	}

	if commandTaskReq.JobId == "" && commandTaskReq.TaskId != "" {
		commandTaskReq.JobId = commandTaskReq.TaskId
	}
	if commandTaskReq.TaskId == "" {
		commandTaskReq.TaskId = commandTaskReq.JobId
	}

	newCtx := utils.SetCustomRequestId(ctx, commandTaskReq.RequestId)
	commandTaskReq.RequestId = utils.GetRequestIdFromContext(newCtx)
	return newCtx, commandTaskReq, nil
}

func (l *LiveTransCodeParamsAdapter) AssembleSendCommonCommand(ctx context.Context, taskId string, reqJson string, commandType int32) (context.Context, *pb.CommandTaskRequest, error) {
	commandTaskReq := &pb.CommandTaskRequest{
		JobId:        taskId,
		TaskId:       taskId,
		EngineParams: reqJson,
		CommandType:  commandType,
	}

	if commandTaskReq.JobId == "" && commandTaskReq.TaskId != "" {
		commandTaskReq.JobId = commandTaskReq.TaskId
	}
	if commandTaskReq.TaskId == "" {
		commandTaskReq.TaskId = commandTaskReq.JobId
	}

	newCtx := utils.SetCustomRequestId(ctx, commandTaskReq.RequestId)
	commandTaskReq.RequestId = utils.GetRequestIdFromContext(newCtx)
	return newCtx, commandTaskReq, nil
}

func (l *LiveTransCodeParamsAdapter) AssembleListTranscodeRunningTaskReq() *models.Task {
	taskInfo := &models.Task{
		Product:     "ols",
		EngineModel: models.LiveTranscode,
	}
	if taskInfo.BizStatus == 0 && taskInfo.InternalStatus == 0 {
		taskInfo.BizStatus = models.TaskStateActive
		taskInfo.InternalStatus = models.TaskStateActive
	}
	return taskInfo
}

func (l *LiveTransCodeParamsAdapter) AssembleStartTaskRequestOnMigrate(ctx context.Context, task *models.Task, sameTag bool) {
	extend := make(map[string]interface{})
	extend["soft"] = true
	extend["lastWorkerIp"] = task.Worker.Ip
	if task.Metadata == nil {
		task.Metadata = custom_types.Metadata{}
	}
	if task.RequestResource == nil {
		task.RequestResource = &models.RequestResource{}
	}
	if task.RequestResource.Affinity == nil {
		task.RequestResource.Affinity = &models.TaskAffinity{}
	}
	task.RequestResource.Affinity.DeleteAffinityInTaskAffinity(string(models.AffinityKey_KEY_WORKER_ID), string(metav1.LabelSelectorOpNotIn), "", true)
	task.RequestResource.Affinity.AddMigrateAffinity(task.Worker.Id, true)
	task.Metadata["extend"] = biz.JsonToString(extend)
	if !sameTag {
		//reset scheduler tag and nat
		delete(task.Metadata, "taskSchedulerTag")
		paramsMap := biz.StringToMap(task.Param.Param)
		if v, ok := paramsMap["streamInterSourceBakAddrs"]; ok {
			paramsMap["streamSourceBakVips"] = v
		}
		task.Param.Param = biz.JsonToString(paramsMap)
	}
}

func (l *LiveTransCodeParamsAdapter) CorrectLiveTransCodeCommandParams(task *models.Task, params string) error {
	var commandParams lc.LiveTransCodeSendCommandRequest
	if err := json.Unmarshal([]byte(params), &commandParams); err != nil {
		log.Errorf("LiveTranscodeSendCommand decode request error, err:%v", err)
		return err
	}
	taskParams := make(map[string]interface{})
	taskParams["taskId"] = task.TaskId
	taskParams["domain"] = task.Domain
	taskParams["http_header_host"] = l.globalParamLoader.GetWorkerDomain()
	streamType := GetTemplateType(biz.StringToMap(task.Param.Param))
	//优酷模版特殊处理
	if streamType == "youku" {
		log.Infof("streamType is youku, direct to agent [youku]")
		taskParams["uri"] = "/sendAgentCommand"
		taskParams["agent"] = "youku"
		taskParams["params"] = commandParams.Body
		log.Infof("task %v sendAgentCommand request:%v", task.TaskId, taskParams)
	} else {
		taskParams["uri"] = "/lvfmRPC"
		taskParams["cmdUri"] = commandParams.Uri
		taskParams["body"] = commandParams.Body
		log.Infof("task %v lvfmRPC request:%v", task.TaskId, taskParams)
	}
	task.Param.Param = biz.JsonToString(taskParams)
	return nil
}

func (l *LiveTransCodeParamsAdapter) CorrectLiveTransCodeUpdateParams(task *models.Task, params string) error {
	var updateParams lc.LiveTransCodeUpdateRequest
	if err := json.Unmarshal([]byte(params), &updateParams); err != nil {
		log.Errorf("LiveTranscodeUpdateTask decode request error, err:%v", err)
		return err
	}
	//new
	streams := updateParams.Streams
	//old
	taskParams := biz.StringToMap(task.Param.Param)
	taskStreams, ok := taskParams["streams"].([]interface{})
	if !ok {
		return errors.New("invalid task streams format")
	}
	taskParams["region"] = l.globalParamLoader.GetRegion()
	taskParams["uri"] = "/updateTask"
	taskParams["taskId"] = task.TaskId
	taskParams["jobId"] = task.TaskId
	//stream update
	if len(taskStreams) != len(streams) {
		log.Errorf("sendCommand[%v] fail: unexcept status = %v", task.TaskId, task.InternalStatus)
		return errors.New("streams length is not equal")
	}
	for idx := 0; idx < len(streams); idx++ {
		newStream, ok := streams[idx].(map[string]interface{})
		if !ok {
			return errors.New("invalid new stream format")
		}
		oldStreamInterface := taskStreams[idx]
		oldStream, ok := oldStreamInterface.(map[string]interface{})
		if !ok {
			return errors.New("invalid old stream format")
		}
		for key, value := range newStream {
			if key == "rtmpDest" || key == "liveDictator" {
				continue
			}
			oldStream[key] = value
		}
	}
	task.Param.Param = biz.JsonToString(taskParams)
	return nil
}

func (l *LiveTransCodeParamsAdapter) CorrectLiveTransCodeAttachParams(task *models.Task, params string, attachType string, opt string) {
	taskParams := make(map[string]interface{})
	taskParams["taskId"] = task.TaskId
	taskParams["domain"] = task.Domain
	taskParams["http_header_host"] = l.globalParamLoader.GetWorkerDomain()
	if attachType == lc.AttachmentTypePIC {
		taskParams["pictures"] = params
		taskParams["uri"] = "/" + opt + "Picture"
	} else if attachType == lc.AttachmentTypeTEXT {
		taskParams["texts"] = params
		taskParams["uri"] = "/" + opt + "Text"
	} else {
		taskParams["objects"] = params
		taskParams["uri"] = "/" + opt + "Object"
	}
	taskParams["product"] = "ols"
	task.Param.Param = biz.JsonToString(taskParams)
}

func (l *LiveTransCodeParamsAdapter) GetCallBizUrl(path string, params string) string {
	if params == "" {
		return "http://" + l.globalParamLoader.GetBizHost() + path
	} else {
		return fmt.Sprintf("http://%s%s?%s", l.globalParamLoader.GetGslbHost(), path, params)
	}
}

func (l *LiveTransCodeParamsAdapter) GetCallLiveTocUrl(path string) string {
	return fmt.Sprintf("http://%s%s", l.globalParamLoader.GetLiveTocHost(), path)
}

func (l *LiveTransCodeParamsAdapter) refineQuota(quota int, streamParams lc.TranscodeParams) int {
	if streamParams.StreamSourceVolatile {
		log.Warn("streamSourceVolatile is true input stream is volatile, skip quota refining")
		return quota
	}
	if streamParams.Streams == nil || len(streamParams.Streams) != 1 {
		log.Warn("streams is nil or length is not 1,no output params, skip quota refining")
		return quota
	}
	stream, err := biz.ParseToMap(streamParams.Streams[0])
	if err != nil {
		log.Errorf("parse extend data quotaSet failed while start, error:%v", err)
		return quota
	}
	//rtmpDest := biz.ParseMapStrValue(stream, "rtmpDest")
	outputBypass := biz.ParseMapStrValue(stream, "output_bypass")
	//_, info := lc.FromRtmpPushUrl(rtmpDest)
	ow := biz.ParseMapIntValue(stream, "width")
	oh := biz.ParseMapIntValue(stream, "height")
	icodec := streamParams.StreamSourceCodec
	templateId := streamParams.Token
	//token := info.TemplateId
	if _, ok := stream["class"]; ok {
		clz := stream["class"].(string)
		log.Warnf("class = %s", clz)
		templateId = templateId + ("-" + clz)
	}
	catalog := toCatalog(ow, oh, streamParams.StreamSourceWidth, streamParams.StreamSourceHeight, icodec, outputBypass)
	refinedQuota := l.getQuota(streamParams.Domain, templateId, catalog)
	if refinedQuota > 0 {
		return refinedQuota
	} else {
		return quota
	}
}

func (l *LiveTransCodeParamsAdapter) getQuota(domain, token, catalog string) int {
	quota := 0
	defaultKey := toKey(domain, token, "")
	preciseKey := toKey(domain, token, catalog)
	cacheQuotaMap, err := l.globalParamLoader.Get(defaultKey)
	if err != nil {
		log.Errorf("get quota from cache failed, error:%v", err)
		return quota
	}
	if cacheQuota, ok := cacheQuotaMap[preciseKey]; ok {
		quota = cacheQuota
		log.Infof("refine[%s] quota = %d", preciseKey, quota)
	} else if defaultCacheQuota, ok1 := cacheQuotaMap[defaultKey]; ok1 {
		quota = defaultCacheQuota
		log.Infof("refine[%s] quota = %d", defaultKey, quota)
	}
	return quota
}

func (l *LiveTransCodeParamsAdapter) CovertToLiveTransCodeTask(task *models.Task) *lc.LiveTransCodeTask {
	liveTransCodeTask := ToLiveTransCodeTask(task)
	liveTransCodeTask.Env = l.globalParamLoader.GetEnv()
	return liveTransCodeTask
}

func ToLiveTransCodeTask(task *models.Task) *lc.LiveTransCodeTask {
	liveTransCodeTask := &lc.LiveTransCodeTask{
		Domain:       task.Domain,
		TaskID:       task.TaskId,
		Params:       task.Param.Param,
		Quota:        0,
		Status:       int(task.BizStatus),
		Running:      int(task.InternalStatus),
		Env:          "UNKNOWN",
		TemplateName: "UNKNOWN",
		WorkerUUID:   "",
		WorkerIP:     "",
		Extend:       biz.JsonToString(task.Metadata),
		GmtCreate:    task.GmtCreate,
		GmtModified:  task.LastModified,
		UserData:     task.UserData,
	}
	if task.Worker != nil {
		liveTransCodeTask.WorkerUUID = task.Worker.Id
		liveTransCodeTask.WorkerIP = task.Worker.Ip
	}
	if task.RequestResource != nil && task.RequestResource.Quota != nil {
		if quota, ok := task.RequestResource.Quota["cpu"]; ok {
			liveTransCodeTask.Quota = int(quota)
		}
	}
	params := biz.StringToMap(task.Param.Param)
	if params != nil {
		if token, ok := params["token"]; ok {
			if templateName, ok := token.(string); ok {
				liveTransCodeTask.TemplateName = templateName
			}
		}
	}
	return liveTransCodeTask
}

func ExtractStreamStrMeta(taskId string, paramStr string) ([]string, error) {
	// 解析转码参数
	var transcodeParams map[string]interface{}
	if err := json.Unmarshal([]byte(paramStr), &transcodeParams); err != nil {
		log.Errorf("failed to parse transcode params, taskId:%s, err:%v", taskId, err)
		return nil, err
	}

	// 获取streams数组
	streams, ok := transcodeParams["streams"].([]interface{})
	if !ok || len(streams) == 0 {
		return nil, fmt.Errorf("%s failed to ExtractStreamMeta get streams", taskId)
	}
	return ExtractStreamMeta(taskId, transcodeParams)
}

func ExtractStreamMeta(taskId string, transcodeParams map[string]interface{}) ([]string, error) {
	metaOpts := []string{"domain", "appName", "streamName", "token"}
	streamMetas := make([]string, 0)
	for _, metaOpt := range metaOpts {
		if v, ok := transcodeParams[metaOpt]; ok {
			streamMetas = append(streamMetas, v.(string))
		} else {
			return nil, fmt.Errorf("%s failed to ExtractStreamMeta get %s", taskId, metaOpt)
		}
	}
	return streamMetas, nil
}

func ExtractTranscodeStartConfig(extend map[string]interface{}) (bool, bool) {
	forceRestart, schedulerForceRestart := false, false
	if v, ok := extend["forceRestart"]; ok {
		forceRestart = v.(bool)
	}
	if v, ok := extend["schedulerForceRestart"]; ok {
		schedulerForceRestart = v.(bool)
	}
	return forceRestart, schedulerForceRestart
}

func isDistributedTaskCheck(streamParams lc.StreamParams) (bool, int) {
	log.Infof("isDistributedTaskCheck time:%v", time.Now().Unix())
	streams := streamParams.Params.Streams
	if streams != nil && len(streams) > 0 {
		stream, ok := streams[0].(map[string]interface{})
		if !ok || stream == nil {
			return false, -1
		}
		parallel, ok := stream["parallel"]
		if !ok || parallel == nil {
			return false, -1
		} else {
			return true, parallel.(int)
		}
	}
	return false, -1
}

func toCatalog(ow, oh int, streamSourceWidth, streamSourceHeight interface{}, icodec, outputBypass string) string {
	var catalog string
	if streamSourceWidth == nil || streamSourceHeight == nil {
		return catalog
	}
	iw, ih := 0, 0
	if w, ok := streamSourceWidth.(int); ok {
		iw = w
	} else {
		return catalog
	}

	if h, ok := streamSourceHeight.(int); ok {
		ih = h
	} else {
		return catalog
	}
	if iw <= 0 || ih <= 0 {
		if ow <= 0 || oh <= 0 {
			return catalog
		}
	}
	log.Infof("input(%s) [%d x %d] => output[%d x %d]", icodec, iw, ih, ow, oh)
	pixels := 0
	if ow <= 0 && oh <= 0 {
		pixels = iw * ih
	} else if ow <= 0 {
		owReal := iw * oh / ih
		pixels = owReal * oh
	} else if oh <= 0 {
		ohReal := ih * ow / iw
		pixels = ow * ohReal
	} else {
		pixels = ow * oh
	}
	if IsKeepsize(outputBypass) {
		if iw > 0 && ih > 0 {
			inputPixels := iw * ih
			if inputPixels < pixels {
				pixels = inputPixels
			}
		}
	}
	if pixels <= 854*480 {
		catalog = "LD"
	} else if pixels <= 1280*720 {
		catalog = "SD"
	} else if pixels <= 1920*1080 {
		catalog = "HD"
	} else {
		catalog = "UD"
	}
	if "h265" == icodec || "hevc" == icodec {
		catalog += "265"
	}
	return catalog
}

func toKey(domain, token, catalog string) string {
	key := domain + "_" + token
	if len(catalog) > 0 {
		key = key + ("." + catalog)
	}
	return key
}
func IsKeepsize(outputBypass string) bool {
	if len(outputBypass) > 0 {
		rules := []string{
			"yy", "adapt_res_",
			"adapt_revertv2_",
		}
		for _, rule := range rules {
			if strings.Contains(outputBypass, "-rule "+rule) {
				return true
			}
		}
	}
	return false
}

func GetUuidFromBytes(uuidKey string) string {
	// 和java中UUID.nameUUIDFromBytes 保持一致的uuid生成方式，跳过github.com/google/uuid的命名空间默认拼接问题
	hash := md5.Sum([]byte(uuidKey))

	// 手动构造 UUID（设置版本和变体位）
	var u uuid.UUID
	copy(u[:], hash[:])         // 复制哈希值到 UUID 字节
	u[6] = (u[6] & 0x0f) | 0x30 // 设置版本为 3
	u[8] = (u[8] & 0x3f) | 0x80 // 设置变体为 RFC 4122
	return u.String()
}

func GenerateTaskId(domain, appName, streamName, token string) string {
	mixStr := domain + "/" + appName + "/" + streamName + "/" + token
	return GetUuidFromBytes(mixStr)
}

func GenerateStreamId(domain, app, stream string) string {
	mixStr := domain + "/" + app + "/" + stream
	return GetUuidFromBytes(mixStr)
}

func GetTemplateType(taskParams map[string]any) string {
	if taskParams["streams"] == nil {
		log.Errorf("LiveTranscodeSendCommand decode request error, err:%v", errors.New("streams is nil or length is not 1,no output params, cannot get template type"))
		return ""
	}
	streams := taskParams["streams"].([]interface{})
	if len(streams) == 0 {
		log.Errorf("LiveTranscodeSendCommand decode request error, err:%v", errors.New("streams is nil or length is not 1,no output params, cannot get template type"))
		return ""
	}
	var streamType string
	if stream, ok := streams[0].(map[string]interface{}); !ok {
		log.Errorf("LiveTranscodeSendCommand decode request error, err:%v", errors.New("streams is nil or length is not 1,no output params, cannot get template type"))
		return ""
	} else {
		streamType = biz.ParseMapStrValue(stream, "type")
	}
	return streamType
}
