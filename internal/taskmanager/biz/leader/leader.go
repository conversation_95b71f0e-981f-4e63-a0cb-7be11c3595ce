package leader

import (
	"time"

	"mpp/pkg/hostinfo"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
)

/// todo 后面看看用这个实现  https://github.com/go-redsync/redsync

var (
	ErrLeaderNotExist = errors.NotFound("LEADER_NOT_FOUND", "leader not exist.")
)

const (
	KEY            = "taskmanager"
	OutDateSeconds = 4
)

type Leader struct {
	Key          string
	Instance     string
	LastTickTime time.Time
}

func (l *Leader) isOutDated() bool {
	if time.Now().Sub(l.LastTickTime) > time.Second*OutDateSeconds {
		return true
	}
	return false
}

type LeaderRepo interface {
	Create(Leader) error
	QueryLeader(key string) (*Leader, error)
	KeepAlive(Leader) (bool, error)
	UpdateLeader(Leader, int) (bool, error)
}

type LeaderBiz struct {
	repo     LeaderRepo
	log      *log.Helper
	isLeader bool
	ip       string
}

func NewLeaderBiz(repo LeaderRepo, logger log.Logger) *LeaderBiz {
	leaderBiz := &LeaderBiz{repo: repo, log: log.NewHelper(logger)}
	leaderBiz.isLeader = false
	leaderBiz.ip = hostinfo.GetHostIP()
	go func() {
		leaderBiz.Start()
	}()

	return leaderBiz
}

func (l *LeaderBiz) Start() {
	leader := Leader{
		Key:      KEY,
		Instance: l.ip,
	}
	log.Infof("LeaderBiz start")
	for {
		if l.isLeader {
			changed, err := l.asLeader(leader)
			if err != nil {
				log.Errorf("asLeader err: %v", err)
			} else if changed {
				log.Infof("asLeader, leader changed")
				l.isLeader = false
			}
		} else {
			changed, err := l.asFollower()
			if err != nil {
				log.Errorf("asFollower err: %v", err)
			} else if changed {
				log.Infof("asFollower, leader changed, new Leader:%v", leader)
				l.isLeader = true
			}
		}
		time.Sleep(time.Second)
	}
}

// asLeader keepAlive
func (l *LeaderBiz) asLeader(leader Leader) (bool, error) {
	result, err := l.repo.KeepAlive(leader)
	if err != nil {
		l.log.Errorf("KeepAlive err: %v", err)
		return false, err
	}
	return !result, nil
}

// asFollower,
// query current leader
// if outdated, try update to become new leader
// if not exist, create new leader
func (l *LeaderBiz) asFollower() (bool, error) {
	leader, err := l.repo.QueryLeader(KEY)
	if err != nil && !errors.Is(err, ErrLeaderNotExist) {
		l.log.Errorf("QueryLeader err: %v", err)
		return false, err
	}

	if leader != nil && !leader.isOutDated() {
		return false, nil
	}

	if leader != nil && leader.isOutDated() {
		result, err := l.repo.UpdateLeader(Leader{Key: KEY, Instance: l.ip}, OutDateSeconds)
		if err != nil {
			l.log.Errorf("DeleteLeader err: %v", err)
			return false, err
		}
		return result, nil
	}

	if leader == nil {
		newLeader := Leader{Key: KEY, Instance: l.ip}
		err = l.repo.Create(newLeader)
		if err != nil {
			l.log.Errorf("Create err: %v", err)
			return false, err
		}

		leader, err = l.repo.QueryLeader(KEY)
		if err != nil {
			log.Errorf("QueryLeader err: %v", err)
			return false, err
		}
		return leader.Instance == l.ip, nil
	}

	return false, nil
}

func (l *LeaderBiz) IsLeader() bool {
	return l.isLeader
}

func (l *LeaderBiz) SetLeader() {
	l.isLeader = true
}
