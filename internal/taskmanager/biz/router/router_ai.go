package router

import "math/rand"

const AIKeySuffix = "mediaai-"

func (r *Router) GetOldAiSystemHost() string {
	return r.oldAiSystemHost
}

func (r *Router) GetMediaaiRouteHitResult(product string, engineModel string, tag string) int32 {
	//灰度策略percent=0 默认为全部走老调度
	randNum := rand.Int31n(100)
	percent := int32(0)
	if product == "" || engineModel == "" || tag == "" {
		return 0
	}
	for _, slice := range r.engineTable {
		if slice.Product == product && slice.EngineModel == engineModel && slice.Tag == tag {
			percent = slice.Percent
			break
		}
	}
	if randNum < percent {
		return 1
	}
	return 0
}
