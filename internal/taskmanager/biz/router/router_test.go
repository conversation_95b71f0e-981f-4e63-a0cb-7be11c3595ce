package router

import (
	"context"
	"github.com/alicebob/miniredis/v2"
	. "github.com/bytedance/mockey"
	config2 "github.com/cinience/animus/config"
	"github.com/stretchr/testify/assert"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/repository"
	"testing"
)

func TestRouter(t *testing.T) {
	s := miniredis.RunT(t)
	defer s.Close()
	config := &conf.Bootstrap{
		Data: &conf.Data{
			Redis: &conf.Data_Redis{
				Endpoint: "redis://" + s.Addr(),
			},
		},
		Router: &conf.Router{
			Percent:         100,
			OldSystemHost:   "old-host",
			OldSystemExist:  true,
			OldAiSystemHost: "old-ai-host",
			EngineTable: []*conf.EngineTable{
				{
					Product:     "ai",
					EngineModel: "ai-video",
					Tag:         "tag",
					Percent:     100,
				},
			},
		},
	}

	// config2.WatchConfig 是在go 协程里，这里必须在NewRouter之前执行，保证mock在goroutine之前
	// gomonkey m芯片不兼容，内联优化问题已解决，但是跑不了覆盖率
	//patches := gomonkey.ApplyFunc(config2.WatchConfig, func(_ string, _ interface{}, _ func()) error {
	//	return nil
	//})
	//defer patches.Reset()

	// xgo 或可解决，但是需要运行命令改为xgo test 而不能是go test
	//mock.Patch(config2.WatchConfig, func(_ string, _ interface{}, _ func()) error {
	//	return nil
	//})

	// 字节开源的mockey 实测可以兼容覆盖率的问题  效果等同上文的gomonkey.ApplyFunc，暂采用
	defer Mock(config2.WatchConfig).Return(nil).Build().UnPatch()
	// Mock((*Router).DataExists).Return(true).Build()  // mock结构体方法，有效

	//patches1 := gomonkey.ApplyMethod(reflect.TypeOf(router), "DataExists", func(_ *Router, _ string, _ context.Context) bool {
	//	return true
	//})  // 此mock写法有效---mock结构体方法

	redis := repository.NewRedisClient(config)
	router := NewRouter(config, redis)
	assert.Equal(t, false, router.DataExists("123", context.Background()))
	{
		err := s.Set("123", "123")
		if err != nil {
			return
		}
		assert.Equal(t, true, router.DataExists("123", context.Background()))
	}
	{
		err := s.Set("123", "123")
		if err != nil {
			return
		}
		assert.Equal(t, false, router.DataExists("321", context.Background()))
	}
	{
		// 验证同key更新逻辑
		router.SetRouteData("recordHls-test", "new", "running", context.Background())
		router.SetRouteData("recordHls-test", "old", "running", context.Background())
		assert.Equal(t, "old", router.GetRouteData("recordHls-test", context.Background()).System)
	}
	{
		// 验证getData为空
		var rr *RouteData
		assert.Equal(t, rr, router.GetRouteData("recordHls-empty", context.Background()))
	}
	{
		// 验证getData解析异常为空
		//var rr *RouteData
		data := &RouteData{
			Id:         "",
			System:     "",
			Status:     "",
			CreateTime: "",
			UpdateTime: "",
		}
		assert.Equal(t, data, router.GetRouteData("123", context.Background()))
	}
	{
		// 基础验证
		// 新系统 percent 100% ，返回1
		assert.Equal(t, int32(1), router.GetRouteHitResult("taskId", ""))
		assert.Equal(t, "old-host", router.GetOldSystemHost())
		assert.Equal(t, "old-ai-host", router.GetOldAiSystemHost())
		assert.Equal(t, int32(0), router.GetMediaaiRouteHitResult("", "", ""))
		assert.Equal(t, int32(1), router.GetMediaaiRouteHitResult("ai", "ai-video", "tag"))
		assert.Equal(t, int32(0), router.GetMediaaiRouteHitResult("ai", "ai-video", "tag1"))
		assert.Equal(t, true, router.GetOldSystemExist())
	}
	{
		router.SetRouteData("recordHls-test2", "new", "stopped", context.Background())
		router.dataAnalyze("recordHls")
	}
}

func TestRouter_dataAnalyze(t *testing.T) {
	s := miniredis.RunT(t)
	defer s.Close()
	config := &conf.Bootstrap{
		Data: &conf.Data{
			Redis: &conf.Data_Redis{
				Endpoint: "redis://" + s.Addr(),
			},
		},
		Router: &conf.Router{
			Percent:         100,
			OldSystemHost:   "old-host",
			OldSystemExist:  true,
			OldAiSystemHost: "old-ai-host",
			EngineTable: []*conf.EngineTable{
				{
					Product:     "ai",
					EngineModel: "ai-video",
					Tag:         "tag",
					Percent:     100,
				},
			},
		},
	}
	// 字节开源的mockey 实测可以兼容覆盖率的问题  效果等同上文的gomonkey.ApplyFunc，暂采用
	Mock(config2.WatchConfig).Return(nil).Build()
	// Mock((*Router).DataExists).Return(true).Build()  // mock结构体方法，有效

	//patches1 := gomonkey.ApplyMethod(reflect.TypeOf(router), "DataExists", func(_ *Router, _ string, _ context.Context) bool {
	//	return true
	//})  // 此mock写法有效---mock结构体方法

	redis := repository.NewRedisClient(config)
	router := NewRouter(config, redis)
	assert.Equal(t, int32(1), router.GetRouteHitResult("taskId", ""))
}
