package router

import (
	"context"
	"encoding/json"
	"github.com/cinience/animus/config"
	"github.com/cinience/animus/safe"
	"hash/fnv"
	"math/rand"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/repository"
)

type Router struct {
	redis            *repository.RedisClient
	newSystemPercent int32
	oldSystemHost    string
	oldSystemExist   bool
	stopBoth         bool
	oldAiSystemHost  string
	engineTable      []*conf.EngineTable
	domainTable      []*conf.DomainTable
}

type RouteData struct {
	Id         string `json:"id"`
	System     string `json:"system"`
	Status     string `json:"status"`
	CreateTime string `json:"create_time"`
	UpdateTime string `json:"update_time"`
}

const KeyPrefix = "recordhls-"

func NewRouter(conf *conf.Bootstrap, redis *repository.RedisClient) *Router {

	route := &Router{
		redis:            redis,
		newSystemPercent: conf.Router.Percent,
		oldSystemHost:    conf.Router.OldSystemHost,
		oldSystemExist:   conf.Router.OldSystemExist,
		stopBoth:         conf.Router.StopBoth,
		oldAiSystemHost:  conf.Router.OldAiSystemHost,
		engineTable:      conf.Router.EngineTable,
		domainTable:      conf.Router.DomainTable,
	}

	safe.Go(func() {
		route.reloadConfig()
	})
	safe.Go(func() {
		route.dataAnalyzeScheduler()
	})
	return route
}

func (r *Router) reloadConfig() {
	var rr conf.Router
	err := config.WatchConfig("router", &rr, func() {
		log.Infof("router config changed, percent:%d,OldSystemExist:%t,domainTable:%v", rr.Percent, rr.OldSystemExist, rr.DomainTable)
		r.newSystemPercent = rr.Percent
		r.oldSystemHost = rr.OldSystemHost
		r.oldSystemExist = rr.OldSystemExist
		r.stopBoth = rr.StopBoth
		r.domainTable = rr.DomainTable

		//AI相关配置
		r.oldAiSystemHost = rr.OldAiSystemHost
		r.engineTable = rr.EngineTable
	})
	if err != nil {
		log.Errorf("router config changed err:%v", err)
		return
	}
}

// DataExists 路由表中存在数据
func (r *Router) DataExists(key string, ctx context.Context) bool {
	if data := r.redis.GetClient().Get(ctx, key); data != nil && data.Val() != "" {
		return true
	}
	return false
}

func (r *Router) GetRouteData(key string, ctx context.Context) *RouteData {
	var routeData *RouteData
	data := r.redis.GetClient().Get(ctx, key)
	if data == nil || data.Val() == "" {
		log.Warnf("GetRouteData data is nil or empty, key:%s", key)
		return routeData
	}
	err := json.Unmarshal([]byte(data.Val()), &routeData)
	if err != nil {
		log.Errorf("GetRouteData err:%v", err)
	}
	return routeData
}

func (r *Router) SetRouteData(key, system, status string, ctx context.Context) {
	routeData := &RouteData{
		Id:         key,
		System:     system,
		Status:     status,
		CreateTime: time.Now().Format("2006-01-02 15:04:05"),
		UpdateTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	data := r.redis.GetClient().Get(ctx, key)
	var oldData *RouteData
	if data.Val() != "" {
		_ = json.Unmarshal([]byte(data.Val()), &oldData)
		if oldData != nil {
			routeData.CreateTime = oldData.CreateTime
		}
	}

	newData, err := json.Marshal(routeData)
	if err != nil {
		log.Errorf("SetRouteData json marshal  err:%v", err)
	}
	log.Infof("SetRouteData start, id:%s, newData:%s", key, string(newData))

	expireTime := 365 * 24 * time.Hour
	if status == "stopped" {
		expireTime = 24 * time.Hour
	}
	result := r.redis.GetClient().Set(context.Background(), key, newData, expireTime)
	reStr, err := result.Result()
	if err != nil {
		log.Errorf("SetRouteData err:%v", err)
	}
	log.Infof("SetRouteData end, id:%s, result:%s, err:%v", key, reStr, result.Err())
}

func (r *Router) GetRouteHitResult(taskId, domain string) int32 {
	// 如果域名灰度未配置，使用大百分比灰度
	var usePercent int32
	usePercent = r.newSystemPercent
	if r.domainTable == nil || len(r.domainTable) == 0 || domain == "" {
		return r.GetRouteHitById(taskId)
	} else {
		// 在域名列表里，使用域名灰度；不在列表里不灰度； 支持域名为*匹配全部
		for _, domainTable := range r.domainTable {
			if domainTable.Domain == domain {
				usePercent = domainTable.Percent
				break
			}
			if domainTable.Domain == "*" {
				usePercent = domainTable.Percent
				continue
			}
		}
	}
	randNum := getHashCode(taskId)
	if randNum < usePercent {
		return 1
	}
	return 0
}

func (r *Router) GetRouteHitById(taskId string) int32 {
	// 使用hash分配任务，同一个id保证hash不变，避免同一个任务并发启动，分到新老任务各一个任务导致异常
	randNum := getHashCode(taskId)
	if randNum < r.newSystemPercent {
		return 1
	}
	return 0
}

func getHashCode(taskId string) int32 {
	h := fnv.New32()
	_, err := h.Write([]byte(taskId))
	if err != nil {
		log.Errorf("getHashCode err:%v", err)
		return rand.Int31n(100)
	}
	return int32(h.Sum32() % 100)
}

func (r *Router) GetOldSystemHost() string {
	return r.oldSystemHost
}

func (r *Router) GetOldSystemExist() bool {
	return r.oldSystemExist
}

func (r *Router) AddLock(ctx context.Context, key string, expiration time.Duration) bool {
	if !r.redis.GetClient().SetNX(ctx, key, "1", expiration).Val() {
		return false
	}
	return true
}

func (r *Router) DelLock(ctx context.Context, key string) {
	r.redis.GetClient().Del(ctx, key)
}

func (r *Router) GetStopBoth() bool {
	return r.stopBoth
}

func (r *Router) dataAnalyzeScheduler() {
	ticker := time.NewTicker(1 * time.Minute)
	for {
		select {
		case <-ticker.C:
			log.Infof("DataAnalyze start")
			r.dataAnalyze(KeyPrefix)
			r.dataAnalyze(AIKeySuffix)
			log.Infof("DataAnalyze end")
		}
	}

}

func (r *Router) dataAnalyze(prefix string) {
	if !r.redis.GetClient().SetNX(context.Background(), "dataAnalyze", "1", 50*time.Second).Val() {
		log.Infof("DataAnalyze is running by other process")
		return
	}
	var newSystemRunningId []string
	var oldSystemRunningId []string
	var newSystemStoppedId []string
	var oldSystemStoppedId []string
	keys := r.redis.GetClient().Keys(context.Background(), prefix+"*").Val()
	for _, key := range keys {
		data := r.redis.GetClient().Get(context.Background(), key)
		var routeData *RouteData
		if data.Val() != "" {
			_ = json.Unmarshal([]byte(data.Val()), &routeData)
			if routeData != nil {
				if routeData.Status == "running" {
					if routeData.System == "new" {
						newSystemRunningId = append(newSystemRunningId, routeData.Id)
					} else {
						oldSystemRunningId = append(oldSystemRunningId, routeData.Id)
					}
				} else {
					if routeData.System == "new" {
						newSystemStoppedId = append(newSystemStoppedId, routeData.Id)
					} else {
						oldSystemStoppedId = append(oldSystemStoppedId, routeData.Id)
					}
				}
			}
		}
	}
	log.Infof("%s DataAnalyze newSystemRunningCount:%v, oldSystemRunningCount:%v, newSystemStoppedCount:%v, oldSystemStoppedCount:%v", prefix, len(newSystemRunningId), len(oldSystemRunningId), len(newSystemStoppedId), len(oldSystemStoppedId))
	log.Infof("%s DataAnalyze newSystemRunningId:%v, oldSystemRunningId:%v", prefix, newSystemRunningId, oldSystemRunningId)
}
