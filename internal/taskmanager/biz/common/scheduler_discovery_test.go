package common

import (
	"context"
	_ "encoding/json"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"mpp/internal/taskmanager/mocks"
	"mpp/internal/taskmanager/models"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

// 测试用例：
// TC1 - 正常情况下的服务获取
// TC3 - 获取工作者列表失败的情况
func TestGetService(t *testing.T) {
	tests := []struct {
		name          string
		serviceName   string
		expectedError bool
	}{
		{
			name:        "TC1",
			serviceName: "product,engineModel,tag",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctl := gomock.NewController(t)
			adapterMock := mocks.NewMockScheduleAdapter(ctl)
			workers := []*models.Worker{
				{
					Id:   "id1",
					Ip:   "localhost",
					Port: 8080,
				},
			}

			ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
			defer cancel()
			adapterMock.EXPECT().GetAllSchedulerTags().Return([]string{"l2"}).Times(1)
			adapterMock.EXPECT().ListWorker(ctx, "product", "engineModel", "tag", []string{"l2"}).Return(workers, nil).Times(1)

			scheduler := NewSchedulerDiscovery(adapterMock)

			services, err := scheduler.GetService(ctx, tt.serviceName)

			assert.Nil(t, err)
			assert.NotEmpty(t, services)

			serviceStrs, err := scheduler.GetServiceList(ctx, tt.serviceName, "")
			assert.NotNil(t, err)
			assert.Empty(t, serviceStrs)

		})
	}
}

// 测试用例：
// TC1 - 检查PickService是否返回了预期错误
func TestPickService(t *testing.T) {
	ctl := gomock.NewController(t)
	adapterMock := mocks.NewMockScheduleAdapter(ctl)
	scheduler := NewSchedulerDiscovery(adapterMock)
	ctx := context.Background()
	_, err := scheduler.PickService(ctx, "any_service_name")

	assert.EqualError(t, err, "unexepcted method")
}

// 测试用例：
// TC1 - 检查Watch是否返回了预期错误
func TestWatch(t *testing.T) {
	ctl := gomock.NewController(t)
	adapterMock := mocks.NewMockScheduleAdapter(ctl)
	scheduler := NewSchedulerDiscovery(adapterMock)
	ctx := context.Background()
	_, err := scheduler.Watch(ctx, "any_service_name")

	assert.EqualError(t, err, "unexepcted method")
}

func TestOverhaul(t *testing.T) {
	ctl := gomock.NewController(t)
	adapterMock := mocks.NewMockScheduleAdapter(ctl)
	scheduler := NewSchedulerDiscovery(adapterMock)
	req := httptest.NewRequest("GET", "http://example.com/foo", nil)
	w := httptest.NewRecorder()
	scheduler.Overhaul(w, req)

	assert.Equal(t, w.Code, http.StatusNotFound)
}
