package common

import (
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	"github.com/patrickmn/go-cache"
	"github.com/stretchr/testify/assert"
	"mpp/internal/taskmanager/mocks"
	"mpp/internal/taskmanager/repository"
	"testing"
)

var MockSwitchService *SwitchService
var mockSwitchConfigKey string
var mockBackupConfigKey string

func initMock() {
	defer mockey.Mock((*SwitchService).loadData).Return().Build().UnPatch()
	defer mockey.Mock((*SwitchService).schedulerLoadData).Return().Build().UnPatch()
	defer mockey.Mock((*SwitchService).GetSwitchConfig).When(func(p string, s string) bool { return "switch-global|global|global|global|global|global" == p }).Return(nil).Build().UnPatch()
	MockSwitchService = NewSwitchService(nil, log.GetLogger())
	//MockSwitchService.cache.Set(GlobalConfig, "{\"edgeCpu\":12,\"innerCpu\":88}", cache.DefaultExpiration)
	mockSwitchConfigKey = "switch-live|livetranscode|tag|pull-l3.douyincdn.com|stage|auhd"
	mockBackupConfigKey = "backup-live|livetranscode|tag|pull-l3.douyincdn.com|stage|auhd"
	MockSwitchService.cache.Set(mockSwitchConfigKey, "{\"edgeCpu\":0,\"innerCpu\":0,\"innerCpu2\":0,\"innerGpu\":0,\"l2Cpu\":8,\"l2Cpu2\":92,\"publicCpu\":0,\"publicGpu\":0}", cache.DefaultExpiration)
	MockSwitchService.cache.Set(mockBackupConfigKey, "{\"canHoldByPubTag\":true,\"order\":\"L2,L2-2\"}", cache.DefaultExpiration)
}
func TestSelectSchedulerWithToken(t *testing.T) {
	initMock()
	tag, backupConfig := MockSwitchService.SelectScheduler(&SwitchParam{
		Product:     "live",
		EngineModel: "livetranscode",
		Tag:         "tag",
		Domain:      "pull-l3.douyincdn.com",
		App:         "stage",
		Token:       "auhd",
	})
	assert.Equal(t, tag, "l2Cpu2")
	assert.Equal(t, backupConfig.Order, "L2,L2-2")

	tag, backupConfig = MockSwitchService.SelectScheduler(&SwitchParam{
		Product:     "live",
		EngineModel: "livetranscode",
		Tag:         "tag",
		Domain:      "pull-l3.douyincdn.com",
	})
	assert.Equal(t, tag, "default")
	assert.Equal(t, backupConfig.Order, "default")
}
func TestGetSwitchConfig(t *testing.T) {
	initMock()
	config := MockSwitchService.GetSwitchConfig(mockSwitchConfigKey, "test-2173001740710241")
	assert.NotNil(t, config)
	assert.Equal(t, config["l2Cpu"], 8)
}

func TestGetBackupConfig(t *testing.T) {
	initMock()
	config := MockSwitchService.GetBackupConfig(mockBackupConfigKey)
	assert.NotNil(t, config)
	assert.Equal(t, config.CanHoldByPubTag, true)
}

func TestLoadData(t *testing.T) {
	repo := mocks.NewMockSwitchConfigRepo(gomock.NewController(t))
	repo.EXPECT().ListAll(gomock.Any()).Return([]*repository.SwitchConfig{
		{
			App:          "live",
			Domain:       "pull-l3.douyincdn.com",
			EngineModel:  "livetranscode",
			Tag:          "tag",
			SwitchConfig: "{\"l2Cpu\":8}",
			Product:      "stage",
			Token:        "auhd",
		},
	}, nil).AnyTimes()
	defer mockey.Mock((*SwitchService).schedulerLoadData).Return().Build().UnPatch()
	mockSwitchService := NewSwitchService(repo, log.GetLogger())
	mockSwitchService.loadData()
	v, ok := mockSwitchService.cache.Get(fmt.Sprintf("switch-stage|livetranscode|tag|pull-l3.douyincdn.com|live|auhd"))
	assert.True(t, ok)
	assert.Equal(t, v, "{\"l2Cpu\":8}")
}
