package common

import (
	"errors"
	"fmt"
	kratosErr "github.com/go-kratos/kratos/v2/errors"
	"net/http"
	v1 "proto.mpp/api/common/v1"
)

// 预定义可以安全透露的错误代码
var mpp_safe_errors = map[string]bool{
	v1.ErrorReason_TASK_NOT_FOUND.String():   true,
	v1.ErrorReason_TASK_NOT_RUNNING.String(): true,
	//v1.ErrorReason_NOT_ENOUGH_QUOTA.String():   true,
	//v1.ErrorReason_RESOURCE_NOT_FOUND.String(): true,
}

// 错误码
const (

	//任务驱逐 chaos错误码 当任务多次迁移失败，最终调度失败时返回该错误消息
	ErrorTaskEvicted = "InvalidStatus.Evicted"
	//SCHEDULE ERR
	ErrorScheduleAllocResourceFailed = "ScheduleError.AllocResourceErr"

	//ENGINE ERR
	ErrorEngineHttpFailed = "EngineError.HttpError"

	//DB ERR
	ErrorDb                           = "DbError"
	ErrorDbUpdateScheduleWorkerFailed = "DbError.UpdateScheduleWorkerErr"

	InternalError = "InternalError"
)

const (
	NotImplemented              = "NOT_IMPLEMENTED"
	ServiceUnavailable          = "SERVICE_UNAVAILABLE"
	MissingParameter            = "MISSING_PARAMETER"
	InvalidParameters           = "INVALID_PARAMETERS"
	AccountVerificationFailed   = "ACCOUNT_VERIFICATION_FAILED"
	UnauthorizedOperation       = "UNAUTHORIZED_OPERATION"
	ResourceAuthorizationFailed = "RESOURCE_AUTHORIZATION_FAILED"
	SqlError                    = "SQL_ERROR"
	RateLimitExceeded           = "RATE_LIMIT_EXCEEDED"
	ResourceIssue               = "RESOURCE_ISSUE"
	IdempotencyViolation        = "IDEMPOTENCY_VIOLATION"
	IllegalParameterCombination = "ILLEGAL_PARAMETER_COMBINATION"
	ResourceNotFound            = "RESOURCE_NOT_FOUND"
	DependencyNotReady          = "DEPENDENCY_NOT_READY"

	TaskAlreadyExist = "TASK_ALREADY_EXIST"
)

var (
	ErrTaskEvictedError  = kratosErr.New(http.StatusInternalServerError, ErrorTaskEvicted, ErrorTaskEvicted)
	ErrInternalError     = kratosErr.New(http.StatusInternalServerError, v1.ErrorReason_INTERNAL_ERROR.String(), "internal error.")
	ErrTaskNotFoundError = kratosErr.NotFound(v1.ErrorReason_TASK_NOT_FOUND.String(), "tasker not found.")
	ErrTaskNotRunning    = kratosErr.NotFound(v1.ErrorReason_TASK_NOT_RUNNING.String(), "tasker not running.")
	ErrRequestInvalid    = kratosErr.NotFound(InvalidParameters, "Invalid parameters.")

	ErrTaskAlreadyExist = kratosErr.NotFound(TaskAlreadyExist, "tasker is already exist.")
	ErrTaskStillRunning = kratosErr.NotFound("TaskStillRunning", "tasker is still running.")

	ErrMigrateParams      = kratosErr.NotFound("INVALID_MIGRATE_PARAMETERS", "taskIds length surpasses the batchSize limit.")
	ErrWorkerNotFound     = kratosErr.NotFound(v1.ErrorReason_WORKER_NOT_FOUND.String(), "worker not found")
	ErrDependencyNotReady = kratosErr.NotFound(DependencyNotReady, "dependency not ready")

	ErrRecordNotFoundError = kratosErr.NotFound(v1.ErrorReason_TASK_NOT_FOUND.String(), "record not found.")
	// 预留错误码 暂未使用
	// ErrNotImplemented 表示服务器不支持请求的功能，无法完成请求。
	// 这通常是由于客户端尝试使用尚未实现或不再支持的服务功能。
	ErrNotImplemented = kratosErr.New(http.StatusNotImplemented, NotImplemented, "The server does not support the requested functionality.")
	// ErrServiceUnavailable 表示网关调用后端服务失败，后端服务不可用。
	// 这可能是因为后端服务暂时关闭维护，或者出现了临时故障。
	ErrServiceUnavailable = kratosErr.New(http.StatusServiceUnavailable, ServiceUnavailable, "The backend service is currently unavailable.")

	// ErrMissingParameter 表示必传参数缺失。
	// 当客户端请求缺少必需的参数时触发此错误，包括那些在特定业务场景下被认为是必需的可选参数。
	ErrMissingParameter = kratosErr.New(http.StatusBadRequest, MissingParameter, "A required parameter is missing from the request.")

	// ErrIllegalParameter 表示参数格式不正确或特定业务参数校验失败。
	// 包括但不限于日期、数字、大小写不正确的格式，长度问题，以及JSON或重复列表格式的参数不符合接口要求。
	ErrIllegalParameter = kratosErr.New(http.StatusBadRequest, InvalidParameters, "One or more parameters are in an illegal format or do not meet business requirements.")

	// ErrAccountVerificationFailed 表示账户验证失败。
	// 可能是由于账户欠费、余额不足或风控问题导致的。
	ErrAccountVerificationFailed = kratosErr.New(http.StatusBadRequest, AccountVerificationFailed, "Account payment status issues or risk control problems prevent operation.")

	// ErrUnauthorizedOperation 表示RAM校验不通过访问控制或用户无权执行此操作。
	// 这意味着用户权限不足以执行所请求的操作。
	ErrUnauthorizedOperation = kratosErr.New(http.StatusForbidden, UnauthorizedOperation, "Access denied due to insufficient permissions.")

	// ErrResourceAuthorizationFailed 表示资源鉴权不通过。
	// 资源存在但不属于当前账户，或者引用的资源实际上不存在。
	ErrResourceAuthorizationFailed = kratosErr.New(http.StatusForbidden, ResourceAuthorizationFailed, "Attempted access to a resource that belongs to another account or does not exist.")

	// ErrSQLError 表示产品内部系统的SQL错误。
	// 注意：此类错误不应直接透漏给用户，应统一处理为500错误。
	ErrSQLError = kratosErr.New(http.StatusInternalServerError, SqlError, "An internal SQL error occurred and should be handled as a 500 error.")

	// ErrRateLimitExceeded 表示系统流控、用户流控、业务规则流控或IP流控。
	// 当超过整体流控阈值、单位时间内单个用户访问次数达到上限、违反业务方规则限制或IP访问频率过高时触发。
	ErrRateLimitExceeded = kratosErr.New(http.StatusTooManyRequests, RateLimitExceeded, "The system has exceeded its rate limit for requests.")

	// ErrResourceIssue 表示资源规格错误或库存问题。
	// 包括资源规格已下线、库存不足或违反业务规则，如只能购买特定范围内的资源。
	ErrResourceIssue = kratosErr.New(http.StatusBadRequest, ResourceIssue, "Issues with resource specifications or inventory levels violate business rules.")

	// ErrIdempotencyViolation 表示同一个ClientToken对应不同的请求。
	// 这通常是在幂等性检查中发现同一标识符关联了不同的操作。
	ErrIdempotencyViolation = kratosErr.New(http.StatusBadRequest, IdempotencyViolation, "Different operations are associated with the same ClientToken.")

	// ErrIllegalParameterCombination 表示多个参数同时生效时，其中某个参数非法。
	// 比如在选择计费方式时，参数之间存在依赖关系，其中一个参数的值不合法。
	ErrIllegalParameterCombination = kratosErr.New(http.StatusBadRequest, IllegalParameterCombination, "When multiple parameters take effect simultaneously, one of them is invalid.")

	// ErrResourceNotFound 表示资源不存在。
	// 在尝试获取单个资源信息或删除不存在的资源时触发。
	ErrResourceNotFound = kratosErr.New(http.StatusNotFound, ResourceNotFound, "The requested resource does not exist.")
)

type ManagerError struct {
	Code    int32
	Reason  string
	Message string
}

func (e *ManagerError) Error() string {
	return fmt.Sprintf("Code: %d, Reason: %s, Message: %s", e.Code, e.Reason, e.Message)
}

type EngineError struct {
	Code    int32
	Reason  string
	Message string
}

func (e *EngineError) Error() string {
	return fmt.Sprintf("Code: %d, Reason: %s, Message: %s", e.Code, e.Reason, e.Message)
}

type ScheduleError struct {
	Code    int32
	Reason  string
	Message string
}

func (e *ScheduleError) Error() string {
	return fmt.Sprintf("Code: %d, Reason: %s, Message: %s", e.Code, e.Reason, e.Message)
}

type DbError struct {
	Code    int32
	Reason  string
	Message string
}

func (e *DbError) Error() string {
	return fmt.Sprintf("Code: %d, Reason: %s, Message: %s", e.Code, e.Reason, e.Message)
}

func IsEngineTaskNotExist(err error) bool {
	var e *EngineError
	if errors.As(err, &e) && e.Reason == "InvalidParameter.TaskNotExist" {
		return true
	}
	return false
}

func IsEngineTaskAlreadyExist(err error) bool {
	var e *EngineError
	if errors.As(err, &e) && e.Reason == "InvalidParameter.AlreadyExist" {
		return true
	}
	return false
}

func IsScheduleReourceNotExist(err error) bool {
	var e *ScheduleError
	if errors.As(err, &e) && e.Reason == v1.ErrorReason_RESOURCE_NOT_FOUND.String() {
		return true
	}
	return false
}

// 转换为kratos框架标准错误类型
func ToKratosError(err error) error {
	if err == nil {
		return nil
	}
	// 检查是否为自定义错误类型
	switch err.(type) {
	case *ManagerError:
		return kratosErr.New(int(err.(*ManagerError).Code), err.(*ManagerError).Reason, err.(*ManagerError).Message)
	case *EngineError:
		return kratosErr.New(int(err.(*EngineError).Code), err.(*EngineError).Reason, err.(*EngineError).Message)
	case *ScheduleError:
		return kratosErr.New(int(err.(*ScheduleError).Code), err.(*ScheduleError).Reason, err.(*ScheduleError).Message)
	case *DbError:
		return kratosErr.New(int(err.(*DbError).Code), err.(*DbError).Reason, err.(*DbError).Message)
	default:
		// kratos错误类型 或 未知错误类型
		return err
	}
	return nil
}

// ShouldExposeError 判断错误是否应该对外暴露
// 它检查错误代码是否属于可以安全透露给外部调用者的一组预定义错误之一。
// 并对部分错误码做透出改造
func MakeExposeError(code int32, reason string, message string) (int32, string, string) {
	var exposeCode, exposeReason, exposeMessage = code, reason, message
	// 检查错误代码是否在安全范围内
	if code > http.StatusInternalServerError {
		code = http.StatusInternalServerError
	}

	// 检查错误reason是否在安全透出列表内
	if _, exists := mpp_safe_errors[reason]; exists {
		return code, reason, message
	}
	//对部分错误码做透出改造
	// 资源调度错误
	//if reason == ErrorScheduleAllocResourceFailed {
	//	if strings.Contains(message, v1.ErrorReason_NOT_ENOUGH_QUOTA.String()) {
	//		exposeCode = http.StatusTooManyRequests
	//		exposeReason = v1.ErrorReason_NOT_ENOUGH_QUOTA.String()
	//		exposeMessage = message
	//	}
	//	return exposeCode, exposeReason, exposeMessage
	//}

	// 返回默认错误 500
	exposeCode = ErrInternalError.Code
	exposeReason = ErrInternalError.Reason
	exposeMessage = ErrInternalError.Message

	return exposeCode, exposeReason, exposeMessage
}
