package common

import (
	"context"
	"github.com/cinience/animus/registry"

	"mpp/internal/taskmanager/models"
)

type EngineAdapter interface {
	Submit(ctx context.Context, task *models.Task, worker *models.Worker) error
	Cancel(ctx context.Context, task *models.Task, worker *models.Worker) error
	Start(ctx context.Context, task *models.Task, worker *models.Worker) error
	Stop(ctx context.Context, task *models.Task, worker *models.Worker) error
	Invoke(ctx context.Context, task *models.Task, worker *models.Worker) (*models.SimpleTaskResult, error)
	List(ctx context.Context, worker *models.Worker) ([]models.SimpleTask, error)
	//按任务类型查询，录制时移未实现，内层仍然调用List
	ListByTask(ctx context.Context, worker *models.Worker, task *models.Task) ([]models.SimpleTask, error)
	Check(ctx context.Context, task *models.Task, worker *models.Worker) error
	Update(ctx context.Context, task *models.Task, worker *models.Worker) error
	//引擎命令接口
	Command(ctx context.Context, task *models.Task, worker *models.Worker) error
}

type ScheduleAdapter interface {
	AllocResource(ctx context.Context, task *models.Task) (*models.Worker, error)

	FreeResource(ctx context.Context, taskId string, metaData map[string]string) error

	ListAllWorker(ctx context.Context, schedulerTags []string) ([]*models.Worker, error)

	GetWorker(ctx context.Context, workerId string, metaData map[string]string) (*models.Worker, error)

	ListWorker(ctx context.Context, product string, modelId string, tag string, schedulerTags []string) ([]*models.Worker, error)

	ListResource(ctx context.Context, schedulerTags []string, isLost bool) ([]*models.Resource, error)

	GetResource(ctx context.Context, taskId string, metaData map[string]string) (*models.Resource, error)

	GetAllSchedulerTags() []string

	RecycleGetSchedulerServices(ctx context.Context, dis registry.Discovery)
}

type WorkflowAdapter interface {
	StartDagJob(ctx context.Context, task *models.Task, dagGraph map[string]interface{}) error

	StopDagJob(ctx context.Context, task *models.Task) error

	CommandDagJob(ctx context.Context, task *models.Task) error

	UpdateDagJob(ctx context.Context, task *models.Task) error
}
