package common

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/cinience/animus/registry"
	"github.com/go-kratos/kratos/v2/log"
)

type SchedulerDiscovery struct {
	scheduleAdapter ScheduleAdapter
}

func NewSchedulerDiscovery(scheduleAdapter ScheduleAdapter) registry.Discovery {
	return &SchedulerDiscovery{scheduleAdapter: scheduleAdapter}
}

// GetService return the service instances in memory according to the service name.
func (t *SchedulerDiscovery) GetService(ctx context.Context, serviceName string) ([]*registry.Service, error) {
	ss := strings.Split(serviceName, ",")
	product := ss[0]
	engineModel := ss[1]
	tag := ss[2]
	schedulerTags := t.scheduleAdapter.GetAllSchedulerTags()
	workerList, err := t.scheduleAdapter.ListWorker(ctx, product, engineModel, tag, schedulerTags)
	if err != nil {
		return nil, err
	}
	//测试用log
	for count, worker := range workerList {
		log.Infof("SchedulerDiscovery GetService success and serviceName:%s, count:%d, worker:%+v", serviceName, count, worker)
	}
	if len(workerList) < 10 {
		log.Infof("scheduleAdapter ListWorker serviceName:%s, size:%d, items:%+v", serviceName, len(workerList), workerList)
	} else {
		log.Infof("scheduleAdapter ListWorker serviceName:%s, size:%d", serviceName, len(workerList))
	}

	var services []*registry.Service
	for _, worker := range workerList {
		bs, err := json.Marshal(worker)
		if err != nil {
			return nil, err
		}

		svr, err := registry.NewSingleServiceInstance(serviceName,
			worker.Id,
			"1.0",
			map[string]string{"worker": string(bs)},
			fmt.Sprintf("%s://%s:%d", "http", worker.Ip, worker.Port),
		)

		if err != nil {
			return nil, err
		}
		services = append(services, svr)
	}
	for count, service := range services {
		log.Infof("SchedulerDiscovery GetService success and serviceName:%s, count:%d, service:%+v", serviceName, count, service)
	}

	return services, nil
}

// PickService pick one service
func (t *SchedulerDiscovery) PickService(ctx context.Context, serviceName string) (*registry.Service, error) {
	return nil, fmt.Errorf("unexepcted method")
}

// Watch creates a watcher according to the service name.
func (t *SchedulerDiscovery) Watch(ctx context.Context, serviceName string) (registry.Watcher, error) {
	return nil, fmt.Errorf("unexepcted method")
}

// Overhaul http request whit check
func (t *SchedulerDiscovery) Overhaul(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotFound)
}

func (t *SchedulerDiscovery) GetServiceList(_ context.Context, uScheme, serviceNamePrefix string) ([]string, error) {
	return nil, fmt.Errorf("unexpected method")
}
