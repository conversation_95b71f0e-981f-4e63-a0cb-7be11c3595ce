package common

import (
	stdhttp "net/http"
	"strings"
)

/**
 * @Author: qinxin
 * @Date: 2024/7/30 20:06
 * @Desc: 镇元pop产品信息
 * @Version 1.0
 */

const (
	MppPopProductName = "MppInner"
	// PopHeaderProductInfo for MppInner
	PopHeaderXACSProduct  = "AcsProduct"
	PopHeaderXACSProduct2 = "X-Acs-Product"
	// PopHeaderCodeInfo
	PopHeaderCodeXACSApiName = "Action"
	// PopHeaderCodeVersion
	PopHeaderCodeXACSApiVersion = "Version"
	// PopHeaderCodeCallerType
	PopHeaderCodeXACSCallerType = "callerType"
	// PopHeaderCodeCallerUid
	PopHeaderCodeXACSCallerUid = "callerUid"
)

// 判断是否是pop镇元请求
func IsPopReq(req *stdhttp.Request) bool {
	product := req.Header.Get(PopHeaderXACSProduct)
	if product == "" {
		product = req.Header.Get(PopHeaderXACSProduct2)
	}
	if product != "" && strings.Contains(product, MppPopProductName) {
		return true
	}
	return false
}
