package common

import (
	"context"
	"encoding/json"
	"fmt"
	"hash/fnv"
	"math/rand"
	"strings"
	"time"

	"mpp/internal/taskmanager/repository"

	"github.com/cinience/animus/safe"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/patrickmn/go-cache"
)

type SwitchService struct {
	cache *cache.Cache
	repo  repository.SwitchConfigRepo
	log   *log.Helper
}

type SwitchParam struct {
	Product     string `json:"product"`
	EngineModel string `json:"engineModel"`
	Tag         string `json:"tag"`
	Domain      string `json:"domain"`
	App         string `json:"app"`
	Token       string `json:"token"`
	Stream      string `json:"stream"`
	TaskId      string `json:"taskId"`
}

type BackupConfig struct {
	CanHoldByPubTag bool   `json:"canHoldByPubTag"`
	Order           string `json:"order"`
}

var GlobalConfig = "switch-global|global|global|global|global|global"

func NewSwitchService(switchConfigRepo repository.SwitchConfigRepo, logger log.Logger) *SwitchService {
	c := cache.New(1*time.Minute, 1*time.Minute) // 设置过期时间为1分钟，清理时间为2分钟
	s := &SwitchService{
		cache: c,
		repo:  switchConfigRepo,
		log:   log.NewHelper(logger),
	}
	s.loadData()
	safe.Go(func() {
		s.schedulerLoadData()
	})
	GlobalConfig = fmt.Sprintf("switch-%s|%s|%s|%s|%s|%s", "global", "global", "global", "global", "global", "global")
	return s
}

func (c *SwitchService) schedulerLoadData() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			c.loadData()
		}
	}
}

func (c *SwitchService) loadData() {
	switchConfigs, err := c.repo.ListAll(context.Background())
	if err != nil {
		c.log.Errorf("load switch data err: %v", err)
		return
	}
	for _, switchConfig := range switchConfigs {
		switchKey := fmt.Sprintf("switch-%s|%s|%s|%s|%s|%s", switchConfig.Product, switchConfig.EngineModel, switchConfig.Tag,
			switchConfig.Domain, switchConfig.App, switchConfig.Token)
		// 设置失效时间
		c.cache.Set(switchKey, switchConfig.SwitchConfig, 65*time.Second)
		backupKey := fmt.Sprintf("backup-%s|%s|%s|%s|%s|%s", switchConfig.Product, switchConfig.EngineModel, switchConfig.Tag,
			switchConfig.Domain, switchConfig.App, switchConfig.Token)
		c.cache.Set(backupKey, switchConfig.BackupConfig, 65*time.Second)
	}
}

func (c *SwitchService) SelectScheduler(switchParam *SwitchParam) (string, *BackupConfig) {
	percentMap := c.GetSwitchConfig(GlobalConfig, switchParam.TaskId)
	// 如果全局配置不为空，则直接返回默认资源池  --- 优化为如果哪个资源池大于0就返回哪个资源池，全0不生效
	if percentMap != nil {
		for k, v := range percentMap {
			if v > 0 {
				c.log.Infof("task hit global switch config, key: %s, dest schedulerTag:%s", switchParam.TaskId, k)
				return k, &BackupConfig{CanHoldByPubTag: false, Order: k}
			}
		}
	}
	// 精确匹配
	key := fmt.Sprintf("switch-%s|%s|%s|%s|%s|%s", switchParam.Product, switchParam.EngineModel, switchParam.Tag,
		switchParam.Domain, switchParam.App, switchParam.Token)
	percentMap = c.GetSwitchConfig(key, switchParam.TaskId)
	if percentMap != nil {
		return checkHit(percentMap, switchParam.Stream), c.GetBackupConfig(strings.Replace(key, "switch-", "backup-", -1))
	}
	// token 通配
	key = fmt.Sprintf("switch-%s|%s|%s|%s|%s|%s", switchParam.Product, switchParam.EngineModel, switchParam.Tag,
		switchParam.Domain, switchParam.App, "*")
	percentMap = c.GetSwitchConfig(key, switchParam.TaskId)
	if percentMap != nil {
		return checkHit(percentMap, switchParam.Stream), c.GetBackupConfig(strings.Replace(key, "switch-", "backup-", -1))
	}
	// app 通配，token不通配
	key = fmt.Sprintf("switch-%s|%s|%s|%s|%s|%s", switchParam.Product, switchParam.EngineModel, switchParam.Tag, switchParam.Domain, "*", switchParam.Token)
	percentMap = c.GetSwitchConfig(key, switchParam.TaskId)
	if percentMap != nil {
		return checkHit(percentMap, switchParam.Stream), c.GetBackupConfig(strings.Replace(key, "switch-", "backup-", -1))
	}
	// app、token 通配
	key = fmt.Sprintf("switch-%s|%s|%s|%s|%s|%s", switchParam.Product, switchParam.EngineModel, switchParam.Tag, switchParam.Domain, "*", "*")
	percentMap = c.GetSwitchConfig(key, switchParam.TaskId)
	if percentMap != nil {
		return checkHit(percentMap, switchParam.Stream), c.GetBackupConfig(strings.Replace(key, "switch-", "backup-", -1))
	}
	// 默认资源池兜底  todo 是否增加兜底
	return "default", &BackupConfig{CanHoldByPubTag: false, Order: "default"}
}

func checkHit(percentMap map[string]int, stream string) string {
	// 随机数
	total := 0
	for _, v := range percentMap {
		total += v
	}
	hashcode := getHashCode(stream, uint32(total))
	percent := 0
	for k, v := range percentMap {
		percent += v
		if hashcode < percent {
			return k
		}
	}
	return "default"
}

func getHashCode(stream string, total uint32) int {
	if total <= 0 {
		log.Errorf("getHashCode err: total is 0")
		return rand.Intn(100)
	}
	h := fnv.New32()
	_, err := h.Write([]byte(stream))
	if err != nil {
		log.Errorf("getHashCode err:%v", err)
		return rand.Intn(100)
	}
	return int(h.Sum32() % total)
}
func (c *SwitchService) GetSwitchConfig(key string, taskId string) map[string]int {
	if key == "" {
		return nil
	}
	config, ok := c.cache.Get(key)
	var percentsMap map[string]int
	if ok {
		err := json.Unmarshal([]byte(config.(string)), &percentsMap)
		if err == nil {
			c.log.Infof("task %s with switch config found, key: %s", taskId, key)
			return percentsMap
		} else {
			c.log.Errorf("task %s with switch config is not map, key: %s", taskId, key)
			return nil
		}
	}
	c.log.Infof("task %s with switch config not found, key: %s", taskId, key)
	return nil
}

func (c *SwitchService) GetBackupConfig(key string) *BackupConfig {
	config, ok := c.cache.Get(key)
	var backupConfig *BackupConfig
	if ok {
		err := json.Unmarshal([]byte(config.(string)), &backupConfig)
		if err == nil {
			c.log.Infof("backup config found, key: %s", key)
			return backupConfig
		} else {
			c.log.Errorf("backup config is not map, key: %s", key)
			return nil
		}
	}

	c.log.Infof("backup config not found, key: %s", key)
	return nil
}
