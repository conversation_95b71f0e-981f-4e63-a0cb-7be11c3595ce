package common

import (
	"fmt"
	kratosErr "github.com/go-kratos/kratos/v2/errors"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestEngineError(t *testing.T) {
	tests := []struct {
		name     string
		inputErr error
		want     bool
	}{
		{"case1", &EngineError{Reason: "InvalidParameter.AlreadyExist"}, true},
		{"case2", &EngineError{Reason: "OtherReason"}, false},
		{"case3", kratosErr.New(0, "some other error", ""), false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsEngineTaskAlreadyExist(tt.inputErr); got != tt.want {
				t.Errorf("IsEngineTaskAlreadyExist(%v) = %v, want %v", tt.inputErr, got, tt.want)
			}
		})
	}

	tests2 := []struct {
		name     string
		inputErr error
		want     bool
	}{
		{"case4", &EngineError{Reason: "InvalidParameter.TaskNotExist"}, true},
		{"case5", &EngineError{Reason: "OtherReason"}, false},
		{"case6", kratosErr.New(0, "some other error", ""), false},
	}

	for _, tt := range tests2 {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsEngineTaskNotExist(tt.inputErr); got != tt.want {
				t.Errorf("IsEngineTaskAlreadyExist(%v) = %v, want %v", tt.inputErr, got, tt.want)
			}
		})
	}
}

func TestScheduleError(t *testing.T) {
	tests := []struct {
		name     string
		inputErr error
		want     bool
	}{
		{"case1", &ScheduleError{Reason: "RESOURCE_NOT_FOUND"}, true},
		{"case2", &ScheduleError{Reason: "OtherReason"}, false},
		{"case3", kratosErr.New(0, "some other error", ""), false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsScheduleReourceNotExist(tt.inputErr); got != tt.want {
				t.Errorf("IsEngineTaskAlreadyExist(%v) = %v, want %v", tt.inputErr, got, tt.want)
			}
		})
	}
}

// TestToKratosError 测试 ToKratosError 函数
// 测试案例：
// TC1: 输入nil错误，期望返回nil
// TC2: 输入ManagerError类型的错误，期望转换成kratos错误并返回
// TC3: 输入EngineError类型的错误，期望转换成kratos错误并返回
// TC4: 输入ScheduleError类型的错误，期望转换成kratos错误并返回
// TC5: 输入DbError类型的错误，期望转换成kratos错误并返回
// TC6: 输入非上述类型的错误，期望直接返回原错误
func TestToKratosError(t *testing.T) {

	tests := []struct {
		name     string
		inputErr error
		want     error
	}{
		{"TC1", nil, nil},
		{"TC2", &ManagerError{1, "manager reason", "manager message"}, kratosErr.New(1, "manager reason", "manager message")},
		{"TC3", &EngineError{2, "engine reason", "engine message"}, kratosErr.New(2, "engine reason", "engine message")},
		{"TC4", &ScheduleError{3, "schedule reason", "schedule message"}, kratosErr.New(3, "schedule reason", "schedule message")},
		{"TC5", &DbError{4, "db reason", "db message"}, kratosErr.New(4, "db reason", "db message")},
		{"TC6", fmt.Errorf("unknown error"), fmt.Errorf("unknown error")},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ToKratosError(tt.inputErr)
			assert.Equal(t, tt.want, got, "Test case %s failed with input %v", tt.name, tt.inputErr)
		})
	}
}
