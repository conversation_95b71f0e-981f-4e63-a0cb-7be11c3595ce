package gengine

/**
 * @Author: qinxin
 * @Date: 2024/12/23 17:39
 * @Desc:
 * @Version 1.0
 */

import (
	"github.com/bilibili/gengine/engine"
	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/taskmanager/models"
)

type RuleEnginePool struct {
	pool *engine.GenginePool
}

const (
	MixRuleStruct = "Task"
	// 默认规则 仅作规则池创建兜底
	DefaultRuleStr = `rule "defaultRuleStr" "rule-describtion" salience  0
begin
    return
end
`
)

func NewRuleEnginePool(poolMinLen, poolMaxLen int64, em int, rulesStr string, apiOuter map[string]interface{}) *RuleEnginePool {

	// rulesStr不能为空
	if rulesStr == "" {
		rulesStr = DefaultRuleStr
	}
	enginePool, e := engine.NewGenginePool(poolMinLen, poolMaxLen, em, rulesStr, apiOuter)
	if e != nil {
		log.Errorf("taskmanager new gengine pool, rulesStr:%s, error:%v", rulesStr, e)
	}
	ruleEnginePool := &RuleEnginePool{
		pool: enginePool,
	}
	return ruleEnginePool
}

func (r *RuleEnginePool) Execute(task *models.Task) (map[string]interface{}, error) {

	data := make(map[string]interface{})
	data["Task"] = task

	err, resp := r.pool.Execute(data, true)
	if err != nil {
		log.Error("execute gengine error", err)
		return nil, err
	}

	log.Infof("execute gengine result:%v", resp)
	return resp, err
}

func (r *RuleEnginePool) ExecuteWithStopTag(task *models.Task) error {
	data := make(map[string]interface{})
	data[MixRuleStruct] = task
	stag := &engine.Stag{StopTag: false}
	data["stag"] = stag
	log.Infof("execute gengine withStopTag, data:%+v", task)
	e, result := r.pool.ExecuteWithStopTagDirect(data, true, stag)
	if e != nil {
		log.Error("execute gengine withStopTag error", e)
	}
	if result != nil {
		log.Infof("execute gengine withStopTag result:%v", result)
	}
	return e
}
func (r *RuleEnginePool) UpdatePoolRules(ruleStr string) error {
	if ruleStr == "" {
		ruleStr = DefaultRuleStr
	}
	err := r.pool.UpdatePooledRules(ruleStr)
	if err != nil {
		log.Errorf("update pooled rules ,ruleStr:%v ,error:%v", ruleStr, err)
		return err
	}
	return nil
}
func (r *RuleEnginePool) ClearPoolRules() {
	log.Infof("clear pool rules")
	r.pool.ClearPoolRules()
}

// SetExecModel 设置执行模式
/*
1 sort model (default)
2 concurrent model	并发执行模式
3 mix model			混合执行模式
4 inverse mix model	逆混合执行模式
*/
func (r *RuleEnginePool) SetExecModel(execModel int) error {
	log.Infof("set exec model:%v", execModel)
	err := r.pool.SetExecModel(execModel)
	if err != nil {
		log.Errorf("set exec model ,execModel:%v ,error:%v", execModel, err)
	}
	return err
}
func (r *RuleEnginePool) IsExit(ruleName string) bool {

	ruleNames := []string{ruleName}
	isExist := r.pool.IsExist(ruleNames)

	if isExist != nil && len(isExist) > 0 {
		return isExist[0]
	}
	return false
}

func (r *RuleEnginePool) GetRuleSalience(ruleName string) (int64, error) {

	salience, err := r.pool.GetRuleSalience(ruleName)
	if err != nil {
		log.Errorf("get rule salience ,ruleName:%v ,error:%v", ruleName, err)
	}
	return salience, err

}

func (r *RuleEnginePool) GetRuleDesc(ruleName string) (string, error) {
	result, err := r.pool.GetRuleDesc(ruleName)
	if err != nil {
		log.Errorf("get rule desc ,ruleName:%v ,error:%v", ruleName, err)
	}
	return result, err

}

func (r *RuleEnginePool) GetRulesNumber() int {
	return r.pool.GetRulesNumber()
}
