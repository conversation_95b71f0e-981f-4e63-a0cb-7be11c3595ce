package gengine

/**
 * @Author: qinxin
 * @Date: 2024/12/23 17:24
 * @Desc:
 * @Version 1.0
 */

import (
	"github.com/bilibili/gengine/engine"
	"mpp/internal/taskmanager/models"
)

type RuleEngine struct {
	eng        *engine.Gengine
	ruleString string
}

func NewRuleEngine() (r *RuleEngine, err error) {
	return &RuleEngine{}, nil
}

func (r *RuleEngine) Reload(ruleString string) {
	r.ruleString = ruleString
}

// 运行规则引擎，返回匹配的灰度配置
// input: task
// output: 灰度配置
func (r *RuleEngine) MatchMixRule(task *models.Task) (*models.MixGrayConfig, error) {

	//dataContext := context.NewDataContext()
	////注入初始化的结构体
	//dataContext.Add("Task", task)
	//
	//ruleBuilder := builder.NewRuleBuilder(dataContext)
	//ruleBuilder.BuildRuleFromString(r.ruleString)
	//eng := engine.NewGengine() // todo 线程池
	////dataContext.Add("Event", "aa")   // 可生效
	////执行规则
	//err := eng.Execute(ruleBuilder, true)
	//if err != nil {
	//	log.Error("execute rule error: %v", err)
	//	return nil, err
	//}
	//
	////mixRuleName := dataContext.Get("MixRuleName").([]interface{})[0].(string)
	//
	////todo  如何返回匹配到的灰度规则？
	return nil, nil
}
