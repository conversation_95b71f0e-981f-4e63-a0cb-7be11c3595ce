package gengine

/**
 * @Author: qinxin
 * @Date: 2024/12/27 11:10
 * @Desc:
 * @Version 1.0
 */

// 混跑规则 从数据库读取RuleConfigData
//type MixGrayRuleEngineService struct {
//
//	// 混跑biz实例
//	mixRuleConfigBiz *grayscale.MixRuleConfigBiz
//
//	//日志
//	log *log.Helper
//}
//
//func NewMixGrayRuleEngineService(mixRuleConfigBiz *grayscale.MixRuleConfigBiz, logger log.Logger) *MixGrayRuleEngineService {
//	mixGrayRuleEngineService := &MixGrayRuleEngineService{mixRuleConfigBiz: mixRuleConfigBiz, log: log.NewHelper(logger)}
//
//	return mixGrayRuleEngineService
//}
//
//func (m *MixGrayRuleEngineService) ExcuteMixRuleEngine(ctx context.Context, task *models.Task) (*models.MixGrayConfig, error) {
//
//	mixGrayConfig, err := m.mixRuleConfigBiz.ExcuteMixRuleEngine(ctx, task)
//	if err != nil {
//		m.log.WithContext(ctx).Errorf("ExecuteMixRuleEngine fail, err:%v", err)
//		return nil, err
//	}
//
//	return mixGrayConfig, nil
//
//}
//
//func (m *MixGrayRuleEngineService) UpdateMixRuleEngine(ctx context.Context) (bool, error) {
//	err := m.mixRuleConfigBiz.UpdatemixGrayRuleEngine(ctx)
//	if err != nil {
//		m.log.WithContext(ctx).Errorf("UpdateMixRule fail, err:%v", err)
//		return false, err
//	}
//	return true, nil
//}
//
//// 检查混跑规则，返回混跑配置
//func (m *MixGrayRuleEngineService) CheckHitMixRule(ctx context.Context, task *models.Task) (*models.MixConfig, error) {
//
//	var mixConfigs []*models.MixConfig
//	mixGrayConfig, err := m.ExcuteMixRuleEngine(ctx, task)
//	if err != nil {
//		m.log.WithContext(ctx).Errorf("CheckMixRule fail in ExcuteMixRuleEngine, err:%v", err)
//		return nil, err
//	}
//	if mixGrayConfig == nil {
//		// 没有命中规则，正常返回空
//		return nil, nil
//	}
//	mixConfigs = mixGrayConfig.MixConfigs
//	hitMixConfig, err := m.selectWeightedRandomMixConfig(mixConfigs)
//	if err != nil {
//		m.log.WithContext(ctx).Errorf("CheckMixRule fail in selectWeightedRandomMixConfig, err:%v", err)
//		return nil, err
//	}
//
//	return hitMixConfig, nil
//
//}
//
//// 加权随机选择 MixConfig
//func (m *MixGrayRuleEngineService) selectWeightedRandomMixConfig(mixConfigs []*models.MixConfig) (*models.MixConfig, error) {
//	if len(mixConfigs) == 0 {
//		return nil, nil
//	}
//
//	// 设置随机种子
//	rand.Seed(time.Now().UnixNano())
//
//	// 计算总权重
//	totalWeight := 0
//	for _, config := range mixConfigs {
//		totalWeight += int(config.Ratio)
//	}
//
//	if totalWeight <= 0 {
//		return nil, nil
//	}
//
//	// 生成一个在 [0, totalWeight) 范围内的随机数
//	randomValue := rand.Intn(totalWeight)
//
//	// 选择 MixConfig
//	cumulativeWeight := 0
//	for _, config := range mixConfigs {
//		cumulativeWeight += int(config.Ratio)
//		if randomValue < cumulativeWeight {
//			return config, nil
//		}
//	}
//
//	// 如果没有选择到，返回最后一个 MixConfig（理论上不会发生）
//	return mixConfigs[len(mixConfigs)-1], nil
//}
