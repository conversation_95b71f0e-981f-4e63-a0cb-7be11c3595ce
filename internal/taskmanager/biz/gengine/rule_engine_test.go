package gengine

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"mpp/internal/taskmanager/models"
	"testing"
)

/**
 * @Author: qinxin
 * @Date: 2024/12/24 17:51
 * @Desc:
 * @Version 1.0
 */

// 定义规则

var (
	TestTask1 = &models.Task{
		TaskId:      "test_task_id",
		JobId:       "test_job_id",
		Product:     "mps",
		EngineModel: "mps-transcode-new",
		Tag:         "test_tag",
	}

	TestTask2 = &models.Task{
		TaskId:      "test_task_id",
		JobId:       "test_job_id",
		Product:     "mps",
		EngineModel: "mps-transcode",
		Tag:         "test_tag",
	}
	TestTask3 = &models.Task{
		TaskId:      "test_task_id",
		JobId:       "test_job_id",
		Product:     "mediaai",
		EngineModel: "algo-delogo",
		Tag:         "default",
	}
	TestTask4 = &models.Task{
		TaskId:      "test_task_id",
		JobId:       "test_job_id",
		Product:     "mediaai",
		EngineModel: "algo-notIn",
		Tag:         "default",
	}
	TestTask5 = &models.Task{
		TaskId:      "test_task_id",
		JobId:       "test_job_id",
		Product:     "notIntest",
		EngineModel: "mps-transcode-new",
		Tag:         "default",
	}
)

var (
	TestMixRuleConfigAndProductMps = &models.MixRuleConfig{
		Operate:   string(models.MixOperateEqual),
		ParamName: "product",
		Value:     "mps",
		ValueType: "string",
	}

	TestMixRuleConfigAndProductMediaai = &models.MixRuleConfig{
		Operate:   string(models.MixOperateEqual),
		ParamName: "product",
		Value:     "mediaai",
		ValueType: "string",
	}
	// 测试规则 等于
	TestMixGrayConfigAndEqual = &models.MixGrayConfig{
		RuleName:     "mps_mps-transcode-new_beta_gray_strag_equal",
		Priority:     20,
		ConfigStatus: 1,
		RuleConfigs: []*models.MixRuleConfig{
			TestMixRuleConfigAndProductMps,
			{
				Operate:   string(models.MixOperateEqual),
				ParamName: "engineModel",
				Value:     "mps-transcode-new",
				ValueType: "string",
			},
		},
		MixRatio: 0.50,
	}
	// 测试规则 不等于
	TestMixGrayConfigAndNotEqual = &models.MixGrayConfig{
		RuleName:     "mps_mps-transcode-new_beta_gray_strag_NotEqual",
		Priority:     19,
		ConfigStatus: 1,
		RuleConfigs: []*models.MixRuleConfig{
			TestMixRuleConfigAndProductMps,
			{
				Operate:   string(models.MixOperateNotEqual),
				ParamName: "engineModel",
				Value:     "mps-transcode-new",
				ValueType: "string",
			},
		},
		MixRatio: 0.50,
	}
	// 测试规则 in
	TestMixGrayConfigAndIn = &models.MixGrayConfig{
		RuleName:     "mediaai_algo-delogo_default_gray_strag_in",
		Priority:     18,
		ConfigStatus: 1,
		RuleConfigs: []*models.MixRuleConfig{
			TestMixRuleConfigAndProductMediaai,
			{
				Operate:   string(models.MixOperateIn),
				ParamName: "engineModel",
				Value:     "algo-delogo,algo-test",
				ValueType: "string",
			},
		},
		MixRatio: 0.50,
	}
	// 测试规则 not in
	TestMixGrayConfigAndNotIn = &models.MixGrayConfig{
		RuleName:     "mediaai_algo-delogo_default_gray_strag_NotIn",
		Priority:     17,
		ConfigStatus: 1,
		RuleConfigs: []*models.MixRuleConfig{
			TestMixRuleConfigAndProductMediaai,
			{
				Operate:   string(models.MixOperateNotIn),
				ParamName: "engineModel",
				Value:     "algo-delogo,algo-test",
				ValueType: "string",
			},
		},
		MixRatio: 0.50,
	}
)

// 规则初始化执行
func TestRuleEnginePoolInit(t *testing.T) {
	//初始化规则
	testList := []*models.MixGrayConfig{
		TestMixGrayConfigAndEqual,
		TestMixGrayConfigAndIn,
		TestMixGrayConfigAndNotIn,
	}
	//初始化混跑引擎
	mixGrayRuleEngine := NewMixGrayRuleEngine(log.DefaultLogger, testList)
	rule, err := mixGrayRuleEngine.mixRuleEnginePool.Execute(TestTask1)
	if err != nil {
		t.Error(err)
	}
	log.Infof("rule:%v", rule)
	var rulename string
	if rule != nil {
		for key, _ := range rule {
			rulename = key
		}
	}
	assert.Equal(t, rulename, TestMixGrayConfigAndEqual.RuleName)

}

// 规则更新测试
func TestRuleEnginePoolUpdate(t *testing.T) {
	//初始化规则
	testList := []*models.MixGrayConfig{
		TestMixGrayConfigAndEqual,
		TestMixGrayConfigAndIn,
	}
	//初始化混跑引擎
	mixGrayRuleEngine := NewMixGrayRuleEngine(log.DefaultLogger, testList)
	err := mixGrayRuleEngine.UpdateMixEnginePool()
	assert.Nil(t, err)
}

// 各类操作符匹配规则测试
func TestRuleEnginePoolOperate(t *testing.T) {
	//初始化规则
	testList := []*models.MixGrayConfig{
		TestMixGrayConfigAndEqual,
		TestMixGrayConfigAndNotEqual,
		TestMixGrayConfigAndIn,
		TestMixGrayConfigAndNotIn,
	}
	//初始化混跑引擎
	mixGrayRuleEngine := NewMixGrayRuleEngine(log.DefaultLogger, testList)

	//等于操作符规则测试 ==
	rule, err := mixGrayRuleEngine.mixRuleEnginePool.Execute(TestTask1)
	if err != nil {
		t.Error(err)
	}
	log.Infof("rule:%v", rule)
	var rulename1 string
	if rule != nil {
		for key, _ := range rule {
			rulename1 = key
		}
	}
	assert.Equal(t, rulename1, TestMixGrayConfigAndEqual.RuleName)

	//等于操作符规则测试 !=
	ruleNotEqual, err := mixGrayRuleEngine.mixRuleEnginePool.Execute(TestTask2)
	if err != nil {
		t.Error(err)
	}
	log.Infof("rule:%v", rule)
	var rulename2 string
	if ruleNotEqual != nil {
		for key, _ := range ruleNotEqual {
			rulename2 = key
		}
	}
	assert.Equal(t, rulename2, TestMixGrayConfigAndNotEqual.RuleName)

	//in操作符规则测试
	ruleIn, err := mixGrayRuleEngine.mixRuleEnginePool.Execute(TestTask3)
	if err != nil {
		t.Error(err)
	}
	log.Infof("rule:%v", rule)
	var rulename3 string

	if ruleIn != nil {
		for key, _ := range ruleIn {
			rulename3 = key
		}
	}
	assert.Equal(t, rulename3, TestMixGrayConfigAndIn.RuleName)

	//not in操作符规则测试
	ruleNotIn, err := mixGrayRuleEngine.mixRuleEnginePool.Execute(TestTask4)
	if err != nil {
		t.Error(err)
	}
	log.Infof("rule:%v", rule)
	var rulename4 string
	if ruleNotIn != nil {
		for key, _ := range ruleNotIn {
			rulename4 = key
		}
	}
	assert.Equal(t, rulename4, TestMixGrayConfigAndNotIn.RuleName)

	//测试规则 不存在能够匹配的规则
	ruleNotExist, err := mixGrayRuleEngine.mixRuleEnginePool.Execute(TestTask5)
	if err != nil {
		t.Error(err)
	}
	log.Infof("rule:%v", rule)
	var rulename5 string
	if ruleNotExist != nil && len(ruleNotExist) > 0 {
		for key, _ := range ruleNotExist {
			rulename5 = key
		}
	}
	assert.Equal(t, rulename5, "")

}

//执行模式确认
/**
期望执行模式：
1.优先级命中最高的混跑规则
2.命中之后直接返回规则名称
3.如果未命中，则返回nil
*/
func TestRuleEnginePoolExecute(t *testing.T) {
	//验证优先级匹配最高的混跑规则
	//初始化MPS兜底规则 低优先级
	TestMixGrayConfigAndMpsDefault := &models.MixGrayConfig{
		RuleName:     "mps_mps-transcode-new_beta_gray_strag_equal",
		Priority:     1,
		ConfigStatus: 1,
		RuleConfigs: []*models.MixRuleConfig{
			TestMixRuleConfigAndProductMps,
		},
		MixRatio: 0.50,
	}

	//初始化规则
	testList := []*models.MixGrayConfig{
		TestMixGrayConfigAndEqual,
		TestMixGrayConfigAndIn,
		TestMixGrayConfigAndNotIn,
		TestMixGrayConfigAndMpsDefault,
	}
	//初始化混跑引擎
	mixGrayRuleEngine := NewMixGrayRuleEngine(log.DefaultLogger, testList)

	//期望命中高优先级规则
	rule, err := mixGrayRuleEngine.mixRuleEnginePool.Execute(TestTask1)
	if err != nil {
		t.Error(err)
	}
	log.Infof("rule:%v", rule)
	var rulename1 string
	if rule != nil {
		for key, _ := range rule {
			rulename1 = key
		}
	}
	assert.Equal(t, rulename1, TestMixGrayConfigAndEqual.RuleName)

	//期望命中低优先级规则
	TestTaskMps := &models.Task{
		TaskId:      "test_task_id",
		JobId:       "test_job_id",
		Product:     "mps",
		EngineModel: "test_engineModel",
		Tag:         "test_tag",
	}
	ruleMps, err := mixGrayRuleEngine.mixRuleEnginePool.Execute(TestTaskMps)
	if err != nil {
		t.Error(err)
	}
	log.Infof("rule:%v", rule)
	var rulenameMps string
	if ruleMps != nil {
		for key, _ := range ruleMps {
			rulenameMps = key
		}
	}
	assert.Equal(t, rulenameMps, TestMixGrayConfigAndMpsDefault.RuleName)
}

// 测试不存在的参数规则
/*
期望的报错信息：
[rule: "not_exist_task_param_1" executed, error:
 line 3, column 3, code: Task.NotExistParam, evaluate Expression err! ]
*/
func TestRuleEnginePoolNotExistParam(t *testing.T) {

	//初始化MPS兜底规则 低优先级
	TestMixGrayConfigAndMpsNotExistParam := &models.MixGrayConfig{
		RuleName:     "not_exist_task_param_1",
		Priority:     100,
		ConfigStatus: 1,
		RuleConfigs: []*models.MixRuleConfig{
			{
				Operate:   string(models.MixOperateEqual),
				ParamName: "notExistParam",
				Value:     "mps",
				ValueType: "string",
			},
		},
		MixRatio: 0.5,
	}
	//初始化规则
	testList := []*models.MixGrayConfig{
		TestMixGrayConfigAndEqual,
		TestMixGrayConfigAndIn,
		TestMixGrayConfigAndNotIn,
		TestMixGrayConfigAndMpsNotExistParam,
	}
	//初始化混跑引擎
	mixGrayRuleEngine := NewMixGrayRuleEngine(log.DefaultLogger, testList)

	ruleNotExistParam, err := mixGrayRuleEngine.mixRuleEnginePool.Execute(TestTask1)
	//if err != nil {
	//	log.Errorf("Execute fail , err:%v", err)
	//}
	assert.Nil(t, ruleNotExistParam)
	assert.NotNil(t, err)

}

// 并发性能测试 基准测试
/*
模拟多个任务：在每次循环中，模拟多个任务并执行 Execute 方法
本地结果：（poolMaxLen为enginePool的最大实例数）
poolMaxLen: 10 | BenchmarkRuleEnginePoolExecute-10    	   19666	     53882 ns/op
poolMaxLen: 5  | BenchmarkRuleEnginePoolExecute-10    	   15678	     70095 ns/op
poolMaxLen: 2  | BenchmarkRuleEnginePoolExecute-10    	    9198	    115200 ns/op
*/
func BenchmarkRuleEnginePoolExecute(b *testing.B) {
	// 初始化规则
	testList := []*models.MixGrayConfig{
		TestMixGrayConfigAndEqual,
		TestMixGrayConfigAndIn,
		TestMixGrayConfigAndNotIn,
	}

	// 初始化混跑引擎
	mixGrayRuleEngine := NewMixGrayRuleEngine(log.DefaultLogger, testList)

	// 并发执行测试
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			// 模拟多个任务
			tasks := []*models.Task{
				TestTask1,
				TestTask2,
				TestTask3,
				TestTask4,
				TestTask5,
			}

			for _, task := range tasks {
				rule, err := mixGrayRuleEngine.mixRuleEnginePool.Execute(task)
				if err != nil {
					b.Errorf("Execute fail, err:%v", err)
				}
				if rule == nil {
					b.Errorf("Expected a rule, got nil")
				}
			}
		}
	})
}
