package gengine

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/models"
	"strconv"
	"strings"
)

/**
 * @Author: qinxin
 * @Date: 2024/12/23 17:40
 * @Desc:
 * @Version 1.0
 */

// 混跑规则 从数据库读取RuleConfigData
/*
引擎池中实例的最大个数设置规则参考：https://github.com/bilibili/gengine/wiki/%E5%BC%95%E6%93%8E%E6%B1%A0
poolMinLen = poolMaxLen / 2
poolMaxLen = cpu_core * 5 到 cpu_core * 10*/
type MixGrayRuleEngine struct {
	//规则信息
	EngineRuleName string
	RulesStr       string
	RuleEngine     *RuleEngine

	//数据中规则信息
	RuleConfigData map[string]*models.MixGrayConfig
	createTime     int64
	updateTime     int64

	//规则引擎池信息
	PoolMinLen int64
	PoolMaxLen int64
	Em         int
	ApiOuter   map[string]interface{}

	log               *log.Helper
	mixRuleEnginePool *RuleEnginePool
}

const (
	MixGrayRuleName                     = "mix_gray_rule"
	MixGrayRuleEngineDefaultPoolMinSize = 1
	MixGrayRuleEngineDefaultPoolMaxSize = 10
)

func NewMixGrayRuleEngine(logger log.Logger, mixGrayConfigList []*models.MixGrayConfig) *MixGrayRuleEngine {
	//test 加载mix灰度规则
	ruleConfigData := make(map[string]*models.MixGrayConfig)
	//ruleConfigData[TestMixGrayConfig.RuleName] = TestMixGrayConfig
	//ruleConfigData[TestMixGrayConfig2.RuleName] = TestMixGrayConfig2
	//初始化
	mixGrayRuleEngine := &MixGrayRuleEngine{
		EngineRuleName: MixGrayRuleName,
		RulesStr:       "",
		RuleConfigData: ruleConfigData,
		PoolMinLen:     MixGrayRuleEngineDefaultPoolMinSize,
		PoolMaxLen:     MixGrayRuleEngineDefaultPoolMaxSize,
		Em:             1,
		ApiOuter:       map[string]interface{}{},
		log:            log.NewHelper(logger),
	}
	//加载规则
	err := mixGrayRuleEngine.LoadMixGrayConfigs(mixGrayConfigList)
	if err != nil {
		log.Errorf("LoadMixGrayConfigs fail, err:%v", err)
	}
	//初始化引擎池
	mixGrayRuleEngine.InitMixGrayRuleEnginePool()

	return mixGrayRuleEngine
}

// 混跑规则返回结果
type MixGrayResp struct {
	MixRuleName   string
	MixGrayConfig *models.MixGrayConfig
}

var ctx = context.Background()

// 加载所有混跑规则
// 1.from 数据库混跑规则表
// 2.from 比较本地配置的混跑规则
// 3.a.如果存在则更新: 更新本地配置，更新规则引擎池 b.如果不存在更新：则忽略
func (r *MixGrayRuleEngine) LoadMixGrayConfigs(mixGrayConfigList []*models.MixGrayConfig) error {

	mixGrayConfigMap := make(map[string]*models.MixGrayConfig)
	for _, mixGrayConfig := range mixGrayConfigList {
		mixGrayConfigMap[mixGrayConfig.RuleName] = mixGrayConfig
	}
	//todo 比较本地配置map和数据库配置map，如果不一致则更新本地配置

	var newRuleStr string
	for _, ruleConfig := range mixGrayConfigMap {
		ruleStr := r.parseMixRule(ruleConfig)
		newRuleStr += ruleStr
	}
	r.RuleConfigData = mixGrayConfigMap
	r.RulesStr = newRuleStr

	log.Infof("loadMixEngineRules RulesStr:%s", r.RulesStr)
	return nil
}

// 混跑灰度规则解析 单规则
func (r *MixGrayRuleEngine) parseMixRule(ruleConfig *models.MixGrayConfig) string {
	// 将配置的条件和执行动作组装为策略引擎的Rule。单rule执行后设置stag.StopTag = true跳过后续rule
	var ruleHead = "rule \"" + ruleConfig.RuleName + "\" salience " + strconv.Itoa(ruleConfig.Priority) + " \nbegin\n"
	var ruleBody = ""

	ruleBody += "if "
	for index, value := range ruleConfig.RuleConfigs {
		if index > 0 {
			ruleBody += " && "
		}
		//支持操作符 = ,!= >,>=,<,<=,in,not in
		paramName := strings.Title(value.ParamName) // 将首字母大写
		//判断valueType ,根据valueType判断是否需要转换为字符串
		prefix := ""
		if models.MixValueTypeString == models.MixValueType(value.ValueType) {
			//value前后加双引号
			prefix = "\""
		}

		switch models.MixOperateType(value.Operate) {
		case models.MixOperateIn:
			// 处理 in 操作符，假设 Value 是逗号分隔的字符串
			values := strings.Split(fmt.Sprintf("%v", value.Value), ",")
			var inConditions []string
			for _, v := range values {
				inConditions = append(inConditions, fmt.Sprintf("Task.%s == "+prefix+"%s"+prefix, paramName, strings.TrimSpace(v)))
			}
			ruleBody += "(" + strings.Join(inConditions, " || ") + ")"
		case models.MixOperateNotIn:
			// 处理 not in 操作符，假设 Value 是逗号分隔的字符串
			values := strings.Split(fmt.Sprintf("%v", value.Value), ",")
			var notInConditions []string
			for _, v := range values {
				notInConditions = append(notInConditions, fmt.Sprintf("Task.%s != "+prefix+"%s"+prefix, paramName, strings.TrimSpace(v)))
			}
			ruleBody += "(" + strings.Join(notInConditions, " && ") + ")"
		default:
			// 处理其他简单操作符 == ,!= >,>=,<,<=
			ruleBody += fmt.Sprintf("Task.%s %s "+prefix+"%v"+prefix, paramName, value.Operate, value.Value)
		}

	}
	ruleBody += "{\n"
	ruleBody += "return " + "\"" + ruleConfig.RuleName + "\"" + "\n}"
	var result = ruleHead + ruleBody + "\nend\n"
	log.Infof("parseMixRule result:%s", result)
	return result
}

// 以下为引擎池的初始化，当前仅加载混跑规则
func (r *MixGrayRuleEngine) InitMixGrayRuleEnginePool() {
	//通用规则池加载
	//for _, engineRule := range r.RuleEngineConfigs {
	//	//初始化引擎池
	//	pool := NewRuleEnginePool(engineRule.PoolMinLen, engineRule.PoolMaxLen, engineRule.Em, engineRule.RulesStr, engineRule.ApiOuter)
	//	r.EnginePoolArray[engineRule.EngineRuleName] = pool
	//}
	//
	//混跑规则加载
	pool := NewRuleEnginePool(r.PoolMinLen, r.PoolMaxLen, r.Em, r.RulesStr, r.ApiOuter)
	r.mixRuleEnginePool = pool
}

func (r *MixGrayRuleEngine) UpdateMixEnginePool() error {

	//如果混跑引擎池为空，则返回错误
	//todo 考虑是否需要重新初始化
	if r.mixRuleEnginePool == nil {
		log.Errorf("UpdateMixEnginePool fail, enginePool is nil")
		return common.ErrInternalError
	}
	err := r.mixRuleEnginePool.UpdatePoolRules(r.RulesStr)
	if err != nil {
		log.Errorf("UpdateMixEnginePool error:%v", err)
		return err
	}
	return err
}

func (m *MixGrayRuleEngine) Execute(ctx context.Context, task *models.Task) (map[string]interface{}, error) {

	data := make(map[string]interface{})
	data["Task"] = task

	resp, err := m.mixRuleEnginePool.Execute(task)
	if err != nil {
		log.Error("execute gengine error", err)
		return nil, err
	}
	return resp, err

}
