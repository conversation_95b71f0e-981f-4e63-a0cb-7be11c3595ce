package gengine

import (
	"github.com/go-kratos/kratos/v2/log"
)

/**
 * @Author: qinxin
 * @Date: 2024/12/23 17:25
 * @Desc:
 * @Version 1.0
 */

// salience = priority 优先级

const stuck_rule1 = `
rule "mps_mps-transcode-new_default_mixed_rules"  salience 10
begin
		if  Task.product == “mps” & Task.engineModel == “mps-transcode-new” & Task.tag == "beta"{
			return mix_gray_1
		} 
end
`

const stuck_rule2 = `
rule "pull-l3-spc.douyincdn.com"  salience 0
begin
		if  Task.product == “mps” & Task.engineModel == “mps-transcode-new” & Task.tag == "gamma"{
			return mix_gray_2
		} 
end
`

//type RuleConfig struct {
//	EventsRuleConfig map[string]map[string]EventRuleConfig `json:"eventsRuleConfig"`
//}

type RuleEngineService struct {
	EngineArray       map[string]*RuleEngine
	EnginePoolArray   map[string]*RuleEnginePool
	RuleEngineConfigs []*RuleEngineConfig `json:"ruleEngineConfig"`

	MixGrayRuleEngine *MixGrayRuleEngine
	//日志
	log *log.Helper
}

type RuleEngineConfig struct {
	EngineRuleName string
	PoolMinLen     int64
	PoolMaxLen     int64
	Em             int
	RulesStr       string
	ApiOuter       map[string]interface{}
}

func NewRuleEngineService(logger log.Logger) *RuleEngineService {

	ruleEngineService := &RuleEngineService{
		EngineArray:     make(map[string]*RuleEngine),
		EnginePoolArray: make(map[string]*RuleEnginePool),
	}

	//ruleEngineService.getRuleConfig(data)
	//初始化混跑引擎
	//mixGrayRuleEngine := NewMixGrayRuleEngine(logger, mixRuleConfigBiz)
	//ruleEngineService.MixGrayRuleEngine = mixGrayRuleEngine

	//规则池初始化
	//ruleEngineService.initEnginePool()

	return ruleEngineService
}

// 以下为引擎池的初始化，当前仅加载混跑规则
func (r *RuleEngineService) initEnginePool() {
	if r.EnginePoolArray == nil {
		r.EnginePoolArray = make(map[string]*RuleEnginePool)
	}

	//通用规则池加载
	//for _, engineRule := range r.RuleEngineConfigs {
	//	//初始化引擎池
	//	pool := NewRuleEnginePool(engineRule.PoolMinLen, engineRule.PoolMaxLen, engineRule.Em, engineRule.RulesStr, engineRule.ApiOuter)
	//	r.EnginePoolArray[engineRule.EngineRuleName] = pool
	//}
	//
	//混跑规则加载
	pool := NewRuleEnginePool(r.MixGrayRuleEngine.PoolMinLen, r.MixGrayRuleEngine.PoolMaxLen, r.MixGrayRuleEngine.Em, r.MixGrayRuleEngine.RulesStr, r.MixGrayRuleEngine.ApiOuter)
	r.EnginePoolArray[r.MixGrayRuleEngine.EngineRuleName] = pool
}

func (r *RuleEngineService) loadEnginePool() {
	for _, engineRule := range r.RuleEngineConfigs {
		r.EnginePoolArray[engineRule.EngineRuleName].UpdatePoolRules(engineRule.EngineRuleName)
	}
}

//func (r *RuleEngineService) ReloadMixGray() error {
//	log.Infof("reloadMixGrayRuleEngine")
//
//	r.MixGrayRuleEngine.LoadMixGrayConfigs()
//	err := r.EnginePoolArray[r.MixGrayRuleEngine.EngineRuleName].UpdatePoolRules(r.MixGrayRuleEngine.RulesStr)
//	if err != nil {
//		log.Errorf("reloadMixGrayRuleEngine error:%v", err)
//	}
//	return err
//}
