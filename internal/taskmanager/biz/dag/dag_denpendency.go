package dag

import (
	"context"
	"encoding/json"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"
	"time"

	"github.com/cinience/animus/safe"
	"github.com/go-kratos/kratos/v2/log"
)

/**
 * @Author: qinxin
 * @Date: 2024/8/27 20:41
 * @Desc: DAG任务依赖关系
 * @Version 1.0
 */

//参考rdb.Enqueue中方法
//任务依赖get/set方法

type TaskDependency struct {
	redis  *repository.RedisClient
	log    *log.Helper
	locker *RedisLocker
}

/*
结构体
任务ID
任务状态
依赖任务ID的SET
被依赖任务ID的SET
依赖任务状态
被依赖任务状态
创建时间
更新时间
*/

const (
	TaskDependencyKeyPrefix            = "task-dependency-"
	TaskDependencyLockKeyPrefix        = "task-dependency-lock-"
	DependencyTaskSetKeyPrefix         = "dependency-task-set-"
	DependentOnTaskSetKeyPrefix        = "dependent-on-task-set-"
	DependencyTaskStatusHashKeyPrefix  = "dependency-task-status-hash-"
	DependentOnTaskStatusHashKeyPrefix = "dependent-on-task-status-hash-"

	TaskDependencyKeyAnalysisPrefix = "task-dependency-analysis-"
	//超时时间 7天
	KeyExpireTime = 7 * 24 * 60 * 60 * time.Second
	//停止任务超时时间 3天
	KeyStopTaskExpireTime = 3 * 24 * 60 * 60 * time.Second

	DependencyAnalysisEvent = "dependency.analysis"
)

type TaskDependencyRedis struct {
	TaskId                       string `json:"task_id"` //任务ID
	JobId                        string `json:"job_id"`
	BizStatus                    int32  `json:"biz_status"`                        //任务调度状态
	InternalStatus               int32  `json:"internal_status"`                   //任务内部状态
	DependencyTaskSetKey         string `json:"dependency_task_set_key"`           //当前任务依赖的任务set
	DependentOnTaskSetKey        string `json:"dependent_on_task_set_key"`         //依赖于当前任务的任务set
	DependencyTaskStatusHashKey  string `json:"dependency_task_status_hash_key"`   //当前任务依赖的任务状态hash
	DependentOnTaskStatusHashKey string `json:"dependent_on_task_status_hash_key"` //依赖于当前任务的任务状态hash
	CreateTime                   string `json:"create_time"`
	UpdateTime                   string `json:"update_time"`
}

type TaskDependencyData struct {
	TaskId                    string              `json:"task_id"`
	JobId                     string              `json:"job_id"`
	BizStatus                 int32               `json:"biz_status"`      //任务调度状态
	InternalStatus            int32               `json:"internal_status"` //任务内部状态
	DependencyTaskSet         map[string]struct{} `json:"dependency_task_set"`
	DependentOnTaskSet        map[string]struct{} `json:"dependent_on_task_set"`
	DependencyTaskStatusHash  map[string]int32    `json:"dependency_task_status_hash"`
	DependentOnTaskStatusHash map[string]int32    `json:"dependent_on_task_status_hash"`
	CreateTime                string              `json:"create_time"`
	UpdateTime                string              `json:"update_time"`
}

func NewTaskDependency(redis *repository.RedisClient, logger log.Logger) *TaskDependency {

	taskDependency := &TaskDependency{
		redis:  redis,
		log:    log.NewHelper(logger),
		locker: NewRedisLock(redis, lockTtl, tryLockInterval),
	}
	safe.Go(func() {
		taskDependency.TaskDependencyAnalyzeScheduler()
	})
	return taskDependency
}

// 获取所有任务依赖关系和任务状态信息
// 并发问题-考虑加锁或者使用lua脚本解决
func (t *TaskDependency) GetTaskDependencyDataByLock(ctx context.Context, taskId string) (*TaskDependencyData, error) {
	var taskDependencyData *TaskDependencyData
	//加锁 & 释放锁
	taskLockString := TaskDependencyLockKeyPrefix + taskId
	lock := t.locker.GetLock(taskLockString)
	err := lock.Lock(ctx)
	if err != nil {
		log.Errorf("GetTaskDependencyData lock err:%v", err)
		return nil, err
	}
	defer lock.Unlock(ctx)

	taskDependencyData, err = t.GetTaskDependencyData(ctx, taskId)
	if err != nil {
		log.Errorf("GetTaskDependencyDataByLock fail in GetTaskDependencyData,taskId:%s, err:%v", taskId, err)
		return nil, err
	}
	return taskDependencyData, nil
}

func (t *TaskDependency) GetTaskDependencyData(ctx context.Context, taskId string) (*TaskDependencyData, error) {
	var taskDependencyData *TaskDependencyData
	log.Infof("GetTaskDependencyData start, taskId:%s", taskId)

	//获取任务状态
	//获取任务状态
	taskDependencyRedis, err := t.GetTaskDependencyRedis(ctx, taskId)
	if err != nil {
		log.Errorf("GetTaskDependencyRedisByLock fail in GetTaskDependencyRedis, taskid:%s, err:%v", taskId, err)
		return nil, err
	}

	//获取依赖的任务set
	dependencyTaskSet, err := t.GetDependencyTaskSet(ctx, taskId)
	if err != nil {
		log.Errorf("GetTaskDependencyData err:%v", err)
		return nil, err
	}

	//获取被依赖的任务set
	dependentOnTaskSet, err := t.GetDependentOnTaskSet(ctx, taskId)
	if err != nil {
		log.Errorf("GetTaskDependencyData err:%v", err)
		return nil, err
	}
	////获取依赖的任务状态hash
	//dependencyTaskStatusHash := make(map[string]int32)
	//data := t.redis.GetClient().Get(ctx, DependencyTaskStatusHashKeyPrefix+taskId)
	//if data != nil && data.Val() != "" {
	//	err = json.Unmarshal([]byte(data.Val()), &dependencyTaskStatusHash)
	//	if err != nil {
	//		log.Errorf("GetTaskDependencyData err:%v", err)
	//	}
	//}
	//
	////获取被依赖的任务状态hash
	//dependentOnTaskStatusHash := make(map[string]int32)
	//data = t.redis.GetClient().Get(ctx, DependentOnTaskStatusHashKeyPrefix+taskId)
	//if data != nil && data.Val() != "" {
	//	err = json.Unmarshal([]byte(data.Val()), &dependentOnTaskStatusHash)
	//	if err != nil {
	//		log.Errorf("GetTaskDependencyData err:%v", err)
	//	}
	//}

	//组装返回
	if taskDependencyRedis != nil {
		taskDependencyData = &TaskDependencyData{
			TaskId:             taskDependencyRedis.TaskId,
			JobId:              taskDependencyRedis.JobId,
			BizStatus:          taskDependencyRedis.BizStatus,
			InternalStatus:     taskDependencyRedis.InternalStatus,
			DependencyTaskSet:  dependencyTaskSet,
			DependentOnTaskSet: dependentOnTaskSet,
			//DependencyTaskStatusHash:  dependencyTaskStatusHash,
			//DependentOnTaskStatusHash: dependentOnTaskStatusHash,
			CreateTime: taskDependencyRedis.CreateTime,
			UpdateTime: taskDependencyRedis.UpdateTime,
		}
	}

	return taskDependencyData, nil
}

// 任务提交时设置任务依赖关系
// 添加全部依赖的任务set和状态、在被依赖任务中添加当前任务和状态
// 需要加锁解决并发问题
func (t *TaskDependency) SetTaskDependencyDataByLock(ctx context.Context, task *models.Task) (bool, error) {
	//获取当前任务的锁
	taskLockString := TaskDependencyLockKeyPrefix + task.TaskId
	lock := t.locker.GetLock(taskLockString)
	err := lock.Lock(ctx)
	if err != nil {
		log.Errorf("SetTaskDependencyDataByLock fail in lock,taskId: err:%v", err)
		return false, err
	}
	defer lock.Unlock(ctx)

	result, err := t.SetTaskDependencyData(ctx, task)
	if err != nil {
		log.Errorf("SetTaskDependencyDataByLock fail in SetTaskDependencyData,jobId:%s,taskId:%s ,err:%+v", task.JobId, task.TaskId, err)
		return false, err
	}

	return result, nil
}

func (t *TaskDependency) SetTaskDependencyData(ctx context.Context, task *models.Task) (bool, error) {
	if task == nil || task.TaskId == "" {
		log.Errorf("SetTaskDependencyData fail in task is nil or taskId is nil, task:%+v ", task)
		return false, nil
	}
	log.Infof("SetTaskDependencyData start, taskId:%s,dependency:%+v", task.TaskId, task.Dependencies)

	//存任务状态
	_, err := t.SetTaskDependencyRedis(ctx, task)
	if err != nil {
		log.Errorf("SetTaskDependencyData fail in SetTaskDependencyRedis,jobId:%s,taskId:%s ,err:%+v", task.JobId, task.TaskId, err)
		return false, err
	}

	if task != nil && task.Dependencies != nil && len(task.Dependencies) > 0 {

		//存储依赖的任务
		_, err = t.AddDependencyTaskSet(ctx, task.TaskId, task)
		if err != nil {
			log.Errorf("SetTaskDependencyData fail in AddDependencyTaskSet,jobId:%s,taskId:%s ,err:%+v", task.JobId, task.TaskId, err)
			return false, err
		}

		//在被依赖任务set中添加当前任务
		_, err = t.AddDependentOnTaskSet(ctx, task.TaskId, task)
		if err != nil {
			log.Errorf("SetTaskDependencyData fail in AddDependentOnTaskSet, jobId:%s, taskId:%s ,err:%+v", task.JobId, task.TaskId, err)
			return false, err
		}
	}

	log.Infof("SetTaskDependencyData success, taskId:%s,dependency:%+v", task.TaskId, task.Dependencies)
	return true, nil
}

func (t *TaskDependency) AddTaskDependencyInfo(ctx context.Context, taskId string, task *models.Task) (bool, error) {
	return true, nil

}

func (t *TaskDependency) DelTaskDependencyData(ctx context.Context, taskId string) error {
	return nil
}

// 1.检查依赖任务是否ready-by redis
func (t *TaskDependency) CheckDependencyByRedis(ctx context.Context, taskId string) (bool, error) {
	dependencyData, err := t.GetTaskDependencyDataByLock(ctx, taskId)
	if err != nil {
		log.Errorf("CheckDependencyByRedis falil in GetTaskDependencyData err:%v", err)
		return false, err
	}
	t.LogDependencyDataInfo(context.Background(), dependencyData)
	//检查所有依赖的任务状态是否ready
	if dependencyData != nil && dependencyData.DependencyTaskSet != nil && len(dependencyData.DependencyTaskSet) > 0 {
		for dependencyTaskId := range dependencyData.DependencyTaskSet {
			taskDependencyRedis, err := t.GetTaskDependencyRedis(ctx, dependencyTaskId)
			if err != nil {
				log.Errorf("CheckDependencyByRedis fail in GetTaskDependencyRedis, taskid:%s, err:%v", dependencyTaskId, err)
				return false, err
			}
			if taskDependencyRedis == nil {
				log.Errorf("CheckDependencyByRedis fail in GetTaskDependencyRedis and taskDependency not in redis, taskId:%s, dependencyTaskId:%s, err:%v", taskId, dependencyTaskId, err)
				return false, nil
			}
			//当任务调度状态和内部状态不为1时，返回
			if taskDependencyRedis != nil && taskDependencyRedis.BizStatus != 1 && taskDependencyRedis.InternalStatus != 1 {
				log.Infof("CheckDependencyByRedis and dependency not ready, taskId:%s, DependencyTaskSet:%+v", taskId, dependencyData.DependencyTaskSet)
				return false, nil
			}
		}
	}

	//if dependencyData.DependencyTaskStatusHash != nil && len(dependencyData.DependencyTaskStatusHash) > 0 {
	//	for _, status := range dependencyData.DependencyTaskStatusHash {
	//		if status != 1 {
	//			log.Infof("CheckDependencyByRedis and dependency not ready, taskId:%s, DependencyTaskStatusHash:%+v", task.TaskId, dependencyData.DependencyTaskStatusHash)
	//			return false, nil
	//		}
	//	}
	//}
	log.Infof("CheckDependencyByRedis success, taskId:%s", taskId)
	return true, nil
}

func (t *TaskDependency) GetTaskDependencyRedisByLock(ctx context.Context, taskId string) (*TaskDependencyRedis, error) {
	log.Infof("GetTaskDependencyRedisByLock start, taskId:%s", taskId)
	//获取当前任务的锁
	taskLockString := TaskDependencyLockKeyPrefix + taskId
	lock := t.locker.GetLock(taskLockString)
	err := lock.Lock(ctx)
	if err != nil {
		log.Errorf("GetTaskDependencyRedisByLock lock err:%v", err)
		return nil, err
	}
	defer lock.Unlock(ctx)

	//获取任务状态
	taskDependencyRedis, err := t.GetTaskDependencyRedis(ctx, taskId)
	if err != nil {
		log.Errorf("GetTaskDependencyRedisByLock falil in GetTaskDependencyRedis, taskid:%s, err:%v", taskId, err)
		return nil, err
	}

	return taskDependencyRedis, nil
}

// 2.更新任务状态 加锁更新
func (t *TaskDependency) SetTaskDependencyRedisByLock(ctx context.Context, taskId string, task *models.Task) (bool, error) {
	log.Infof("updateTaskStatusSet start, taskId:%s", taskId)
	//获取当前任务的锁
	taskLockString := TaskDependencyLockKeyPrefix + taskId
	lock := t.locker.GetLock(taskLockString)
	err := lock.Lock(ctx)
	if err != nil {
		log.Errorf("updateTaskStatusSet lock err:%v", err)
		return false, err
	}
	defer lock.Unlock(ctx)

	_, err = t.SetTaskDependencyRedis(ctx, task)
	if err != nil {
		log.Errorf("SetTaskDependencyRedisByLock fail in SetTaskDependencyRedis, jobId:%s, taskid:%s, err:%v", task.JobId, taskId, err)
		return false, err
	}

	return true, nil
}

func (t *TaskDependency) DataExist(ctx context.Context, taskId string) bool {
	key := TaskDependencyKeyPrefix + taskId
	data := t.redis.GetClient().Get(ctx, key)
	if data != nil && data.Val() != "" {
		return true
	}
	return false
}

func (t *TaskDependency) GetTaskDependencyRedis(ctx context.Context, taskId string) (*TaskDependencyRedis, error) {

	var taskDependencyRedis *TaskDependencyRedis
	//taskDependencyRedis = &TaskDependencyRedis{}
	data := t.redis.GetClient().Get(ctx, TaskDependencyKeyPrefix+taskId)
	if data != nil && data.Val() != "" {
		valByte := []byte(data.Val())
		err := json.Unmarshal(valByte, &taskDependencyRedis)
		if err != nil {
			log.Errorf("GetTaskDependencyData ,taskId:%v, err:%v, data.val:%+v", taskId, err, data.Val())
			return nil, err
		}
	}
	return taskDependencyRedis, nil
}

func (t *TaskDependency) SetTaskDependencyRedis(ctx context.Context, task *models.Task) (bool, error) {

	var taskDependencyRedis *TaskDependencyRedis
	//检查数据是否已存在，如果存在 更新任务状态和updateTime
	taskDependencyRedis, err := t.GetTaskDependencyRedis(ctx, task.TaskId)
	if err != nil {
		log.Errorf("SetTaskDependencyRedis json marshal, jobId:%s, taskId:%s, err:%v", task.JobId, task.TaskId, err)
		return false, err
	}

	if taskDependencyRedis != nil && taskDependencyRedis.TaskId != "" {
		taskDependencyRedis.BizStatus = int32(task.BizStatus)
		taskDependencyRedis.InternalStatus = int32(task.InternalStatus)
		taskDependencyRedis.UpdateTime = time.Now().Format("2006-01-02 15:04:05")
	} else {
		//存任务状态
		taskDependencyRedis = &TaskDependencyRedis{
			TaskId:                       task.TaskId,
			JobId:                        task.JobId,
			BizStatus:                    int32(task.BizStatus),
			InternalStatus:               int32(task.InternalStatus),
			DependencyTaskSetKey:         DependencyTaskSetKeyPrefix + task.TaskId,
			DependentOnTaskSetKey:        DependentOnTaskSetKeyPrefix + task.TaskId,
			DependencyTaskStatusHashKey:  DependencyTaskStatusHashKeyPrefix + task.TaskId,
			DependentOnTaskStatusHashKey: DependentOnTaskStatusHashKeyPrefix + task.TaskId,
			CreateTime:                   time.Now().Format("2006-01-02 15:04:05"),
			UpdateTime:                   time.Now().Format("2006-01-02 15:04:05"),
		}
	}

	redisData, err := json.Marshal(taskDependencyRedis)
	if err != nil {
		log.Errorf("SetTaskDependencyRedis json marshal, jobId:%s, taskId:%s, err:%v", task.JobId, task.TaskId, err)
		return false, err
	}
	key := TaskDependencyKeyPrefix + task.TaskId
	expireTime := KeyExpireTime
	if task.IsBizStatusStopped() {
		expireTime = KeyStopTaskExpireTime

	}
	setResult := t.redis.GetClient().Set(ctx, key, redisData, expireTime)
	reStr, err := setResult.Result()
	if err != nil {
		log.Errorf("SetTaskDependencyRedis err:%v", err)
		return false, err
	}
	log.Infof("SetTaskDependencyRedis end, id:%s, result:%s, err:%v", key, reStr, setResult.Err())

	return true, nil
}

func (t *TaskDependency) GetDependencyTaskSet(ctx context.Context, taskId string) (map[string]struct{}, error) {

	dependencyTaskSet := make(map[string]struct{})
	data := t.redis.GetClient().SMembers(ctx, DependencyTaskSetKeyPrefix+taskId)
	if data != nil && data.Val() != nil {
		for _, depTaskId := range data.Val() {
			dependencyTaskSet[depTaskId] = struct{}{}
		}
	}
	return dependencyTaskSet, nil
}

// 存储依赖的任务
// add为原子操作，不需要加锁
func (t *TaskDependency) AddDependencyTaskSet(ctx context.Context, taskId string, task *models.Task) (bool, error) {
	//存储依赖的任务
	dependencyTaskList := make([]string, len(task.Dependencies))
	for i, dep := range task.Dependencies {
		dependencyTaskList[i] = dep.TaskId
	}
	result := t.redis.GetClient().SAdd(ctx, DependencyTaskSetKeyPrefix+taskId, dependencyTaskList)
	if result.Err() != nil {
		log.Errorf("AddDependencyTaskSet fail in add dependencyTaskSet,taskId:%s err:%v", taskId, result.Err())
		return false, result.Err()
	}
	//设置过期时间
	expireResult := t.redis.GetClient().Expire(ctx, DependencyTaskSetKeyPrefix+taskId, KeyExpireTime)
	if expireResult.Err() != nil {
		log.Errorf("AddDependencyTaskSet fail in set Expire time,taskId:%s err:%v", taskId, result.Err())
	}

	return true, nil
}

func (t *TaskDependency) GetDependentOnTaskSet(ctx context.Context, taskId string) (map[string]struct{}, error) {
	dependentOnTaskSet := make(map[string]struct{})
	data := t.redis.GetClient().SMembers(ctx, DependentOnTaskSetKeyPrefix+taskId)
	if data != nil && data.Val() != nil {
		for _, depTaskId := range data.Val() {
			dependentOnTaskSet[depTaskId] = struct{}{}
		}
	}
	log.Infof("GetDependentOnTaskSet success, taskId:%s, dependentOnTaskSet:%+v", taskId, dependentOnTaskSet)
	return dependentOnTaskSet, nil
}

// 在被依赖任务set中添加当前任务
// add为原子操作，不需要加锁
func (t *TaskDependency) AddDependentOnTaskSet(ctx context.Context, taskId string, task *models.Task) (bool, error) {

	dependencyTaskList := make([]string, len(task.Dependencies))
	for i, dep := range task.Dependencies {
		dependencyTaskList[i] = dep.TaskId
	}

	for i, depTaskId := range dependencyTaskList {
		taskLockDepString := DependentOnTaskSetKeyPrefix + depTaskId
		result := t.redis.GetClient().SAdd(ctx, taskLockDepString, taskId)
		if result.Err() != nil {
			log.Errorf("AddDependentOnTaskSet fail in add dep dependencyTaskSet,taskId:%s depTaskId:%s,index:%d, err:%v", taskId, depTaskId, i, result.Err())
			return false, result.Err()
		}
		//设置过期时间
		expireResult := t.redis.GetClient().Expire(ctx, taskLockDepString, KeyExpireTime)
		if expireResult.Err() != nil {
			log.Errorf("AddDependentOnTaskSet fail in set Expire time,taskId:%s depTaskId:%s,index:%d, err:%v", taskId, depTaskId, i, result.Err())
		}
	}
	return true, nil
}

func (t *TaskDependency) TaskDependencyAnalyzeScheduler() {
	ticker := time.NewTicker(1 * time.Minute)
	for {
		select {
		case <-ticker.C:
			log.Infof("TaskDependencyAnalyzeScheduler start")
			//启用协程运行
			safe.Go(func() {
				_ = t.TaskDependencyDataAnalyze(DependencyTaskSetKeyPrefix)
			})
			log.Infof("TaskDependencyAnalyzeScheduler end")
		}
	}
}

func (t *TaskDependency) TaskDependencyDataAnalyze(prefix string) bool {
	if !t.redis.GetClient().SetNX(context.Background(), TaskDependencyKeyAnalysisPrefix, "1", 50*time.Second).Val() {
		log.Infof("DataAnalyze is running by other process")
		return false
	}
	//不再定时输出任务依赖状态，改为check依赖时主动输出

	//taskDependencyDataList := make([]*TaskDependencyData, 0)
	//keys := t.redis.GetClient().Keys(context.Background(), prefix+"*").Val()
	//for _, key := range keys {
	//	//去掉前缀，获取任务id
	//	taskId := strings.TrimPrefix(key, prefix)
	//	taskDependencyData, err := t.GetTaskDependencyData(context.Background(), taskId)
	//	if err != nil {
	//		log.Errorf("TaskDependencyDataAnalyze fail in GetTaskDependencyData, taskId:%s, err:%v", taskId, err)
	//		continue
	//	}
	//	t.LogDependencyDataInfo(context.Background(), taskDependencyData)
	//	log.Infof("TaskDependencyDataAnalyze check success, taskId:%s, taskDependencyData:%+v", taskId, taskDependencyData)
	//	taskDependencyDataList = append(taskDependencyDataList, taskDependencyData)
	//}
	//log.Infof("TaskDependencyDataAnalyze success, taskDependencyDataList:%+v", taskDependencyDataList)

	return true
}

func (t *TaskDependency) LogDependencyDataInfo(ctx context.Context, taskDependencyData *TaskDependencyData) {
	if taskDependencyData == nil {
		return
	}
	dependencyTaskSet := make([]string, 0)
	for depTaskId := range taskDependencyData.DependencyTaskSet {
		dependencyTaskSet = append(dependencyTaskSet, depTaskId)
	}
	dependentOnTaskSet := make([]string, 0)
	for depTaskId := range taskDependencyData.DependentOnTaskSet {
		dependentOnTaskSet = append(dependentOnTaskSet, depTaskId)
	}
	t.log.WithContext(ctx).Log(log.LevelInfo,
		"taskId", taskDependencyData.TaskId,
		"jobId", taskDependencyData.JobId,
		"bizStatus", taskDependencyData.BizStatus,
		"internalStatus", taskDependencyData.InternalStatus,
		"createTime", taskDependencyData.CreateTime,
		"updateTime", taskDependencyData.UpdateTime,
		"dependencyTaskSet", dependencyTaskSet,
		"dependentOnTaskSet", dependentOnTaskSet,
		"event", DependencyAnalysisEvent)
}
