package dag

import (
	"context"
	"errors"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"mpp/internal/taskmanager/repository"
	"time"
)

/**
 * @Author: qinxin
 * @Date: 2024/8/28 14:03
 * @Desc: redis锁实现  参考代码：https://github.com/jiaxwu/lock
 * @Version 1.0
 */
type RedisLocker struct {
	client          *repository.RedisClient // Redis客户端
	script          *redis.Script           // 解锁脚本
	ttl             time.Duration           // 过期时间
	tryLockInterval time.Duration           // 重新获取锁间隔
}

const (
	// 过期时间
	lockTtl = time.Second * 30
	// 重置过期时间间隔
	resetTTLInterval = lockTtl / 3
	// 重新获取锁间隔
	tryLockInterval = time.Second
	// 解锁脚本
	unlockScript = `
if redis.call("get",KEYS[1]) == ARGV[1] then
    return redis.call("del",KEYS[1])
else
    return 0
end`
)

var (
	// ErrLockFailed 加锁失败
	ErrLockFailed = errors.New("lock failed")
	// ErrTimeout 加锁超时
	ErrTimeout = errors.New("timeout")
)

func NewRedisLock(client *repository.RedisClient, ttl, tryLockInterval time.Duration) *RedisLocker {
	return &RedisLocker{
		client:          client,
		script:          redis.NewScript(unlockScript),
		ttl:             ttl,
		tryLockInterval: tryLockInterval,
	}
}

func (l *RedisLocker) GetLock(resource string) *Lock {
	return &Lock{
		client:          l.client,
		script:          l.script,
		resource:        resource,
		randomValue:     uuid.NewString(),
		watchDog:        make(chan struct{}),
		ttl:             l.ttl,
		tryLockInterval: l.tryLockInterval,
	}
}

// Lock 不可重复使用
type Lock struct {
	client          *repository.RedisClient // Redis客户端
	script          *redis.Script           // 解锁脚本
	resource        string                  // 锁定的资源
	randomValue     string                  // 随机值
	watchDog        chan struct{}           // 看门狗
	ttl             time.Duration           // 过期时间
	tryLockInterval time.Duration           // 重新获取锁间隔
}

func (l *Lock) Lock(ctx context.Context) error {
	// 尝试加锁
	err := l.TryLock(ctx)
	if err == nil {
		return nil
	}
	if !errors.Is(err, ErrLockFailed) {
		return err
	}
	// 加锁失败，不断尝试
	ticker := time.NewTicker(l.tryLockInterval)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			// 超时
			return ErrTimeout
		case <-ticker.C:
			// 重新尝试加锁
			err := l.TryLock(ctx)
			if err == nil {
				return nil
			}
			if !errors.Is(err, ErrLockFailed) {
				return err
			}
		}
	}
}

func (l *Lock) TryLock(ctx context.Context) error {
	success, err := l.client.GetClient().SetNX(ctx, l.resource, l.randomValue, l.ttl).Result()
	if err != nil {
		return err
	}
	// 加锁失败
	if !success {
		return ErrLockFailed
	}
	// 加锁成功，启动看门狗
	go l.startWatchDog()
	return nil
}

func (l *Lock) startWatchDog() {
	ticker := time.NewTicker(l.ttl / 3)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			// 延长锁的过期时间
			ctx, cancel := context.WithTimeout(context.Background(), l.ttl/3*2)
			ok, err := l.client.GetClient().Expire(ctx, l.resource, l.ttl).Result()
			cancel()
			// 异常或锁已经不存在则不再续期
			if err != nil || !ok {
				return
			}
		case <-l.watchDog:
			// 已经解锁
			return
		}
	}
}

func (l *Lock) Unlock(ctx context.Context) error {
	err := l.script.Run(ctx, l.client.GetClient(), []string{l.resource}, l.randomValue).Err()
	// 关闭看门狗
	close(l.watchDog)
	return err
}
