package dag

import (
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/repository"
)

/**
 * @Author: qinxin
 * @Date: 2024/8/29 13:40
 * @Desc:
 * @Version 1.0
 */

var (
	client   *repository.RedisClient
	confTest = &conf.Bootstrap{
		Data: &conf.Data{
			Redis: &conf.Data_Redis{
				Endpoint: "redis://127.0.0.1:6379",
			},
		},
	}
)

func init() {

}

// todo 修正

// 模拟并发场景下对同一个资源进行加锁的情况，检查是否能够正确地实现互斥访问
//func TestNewLock(t *testing.T) {
//	client = repository.NewRedisClient(confTest)
//	locker := NewRedisLock(client, lockTtl, tryLockInterval)
//	var wg sync.WaitGroup
//	wg.Add(2)
//	go func() {
//		defer wg.Done()
//		l := locker.GetLock("qinxin-redis-lock-test")
//		err := l.TryLock(context.Background())
//		if err != nil {
//			t.Error(err)
//		}
//		time.Sleep(lockTtl)
//		err = l.Unlock(context.Background())
//		if err != nil {
//			t.Error(err)
//		}
//	}()
//	time.Sleep(time.Second)
//	go func() {
//		defer wg.Done()
//		l := locker.GetLock("qinxin-redis-lock-test")
//		err := l.TryLock(context.Background())
//		if err != nil && !errors.Is(err, ErrLockFailed) {
//			t.Error(err)
//		}
//	}()
//	wg.Wait()
//}

// 检验了在高并发情况下锁的表现
// 在两个协程中循环对同一个资源进行加锁解锁操作，计数器记录加锁次数，最后判断计数器是否为count*2
// 结果：计数器为10000*2
//func TestNewLock2(t *testing.T) {
//
//	client = repository.NewRedisClient(confTest)
//
//	locker := NewRedisLock(client, lockTtl, tryLockInterval)
//	var wg sync.WaitGroup
//	wg.Add(2)
//	count := 0
//	times := 10000
//	go func() {
//		defer wg.Done()
//		for i := 0; i < times; i++ {
//			l := locker.GetLock("qinxin-redis-lock-test-2")
//			err := l.Lock(context.Background())
//			if err != nil {
//				t.Error(err)
//			}
//			count++
//			err = l.Unlock(context.Background())
//			if err != nil {
//				t.Error(err)
//			}
//		}
//	}()
//	go func() {
//		defer wg.Done()
//		for i := 0; i < times; i++ {
//			l := locker.GetLock("qinxin-redis-lock-test-2")
//			err := l.Lock(context.Background())
//			if err != nil {
//				t.Error(err)
//			}
//			count++
//			err = l.Unlock(context.Background())
//			if err != nil {
//				t.Error(err)
//			}
//		}
//	}()
//	wg.Wait()
//	if count != times*2 {
//		t.Errorf("count = %d", count)
//	}
//}
