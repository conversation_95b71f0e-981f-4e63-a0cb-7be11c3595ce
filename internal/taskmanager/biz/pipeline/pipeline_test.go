package pipeline

import (
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"mpp/internal/taskmanager/mocks"
	"mpp/internal/taskmanager/models"
	v1 "proto.mpp/api/common/v1"
)

func TestCommonPipelineBiz_CreatePipeline(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mocks.NewMockAsyncTaskPipelineRepo(ctrl)
	//logger := log.NewHelper(log.With(log.GetLogger(), "module", "taskmanager/pipeline"))
	ctx := context.Background()

	biz := NewCommonPipelineBiz(mockRepo, nil, log.DefaultLogger)

	pipeline := &models.Pipeline{
		PipelineId: "testPipelineId",
		Type:       v1.PipelineType_RESERVED,
	}

	t.Run("success", func(t *testing.T) {
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(nil, nil)
		mockRepo.EXPECT().Create(ctx, pipeline).Return(true, nil)

		success, err := biz.CreatePipeline(ctx, pipeline)
		require.NoError(t, err)
		assert.True(t, success)
	})

	t.Run("already exists", func(t *testing.T) {
		existingPipeline := &models.Pipeline{
			PipelineId: "testPipelineId",
			Type:       v1.PipelineType_RESERVED,
		}
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(existingPipeline, nil)

		success, err := biz.CreatePipeline(ctx, pipeline)
		assert.Error(t, err)
		assert.False(t, success)
	})

	t.Run("repo create error", func(t *testing.T) {
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(nil, nil)
		mockRepo.EXPECT().Create(ctx, pipeline).Return(false, errors.New("create error"))

		success, err := biz.CreatePipeline(ctx, pipeline)
		assert.Error(t, err)
		assert.False(t, success)
	})
}

func TestCommonPipelineBiz_GetPipeline(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mocks.NewMockAsyncTaskPipelineRepo(ctrl)
	//logger := log.NewHelper(log.With(log.GetLogger(), "module", "taskmanager/pipeline"))
	ctx := context.Background()

	biz := NewCommonPipelineBiz(mockRepo, nil, log.DefaultLogger)

	pipelineId := "testPipelineId"
	pipelineType := v1.PipelineType_RESERVED

	t.Run("success", func(t *testing.T) {
		pipeline := &models.Pipeline{
			PipelineId: pipelineId,
			Type:       pipelineType,
		}
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipelineId, pipelineType).Return(pipeline, nil)

		result, err := biz.GetPipeline(ctx, pipelineId, pipelineType)
		require.NoError(t, err)
		assert.Equal(t, pipeline, result)
	})

	t.Run("repo find error", func(t *testing.T) {
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipelineId, pipelineType).Return(nil, errors.New("find error"))

		_, err := biz.GetPipeline(ctx, pipelineId, pipelineType)
		assert.Error(t, err)
	})
}

func TestCommonPipelineBiz_UpdatePipeline(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mocks.NewMockAsyncTaskPipelineRepo(ctrl)
	//logger := log.NewHelper(log.With(log.GetLogger(), "module", "taskmanager/pipeline"))
	ctx := context.Background()

	biz := NewCommonPipelineBiz(mockRepo, nil, log.DefaultLogger)

	pipeline := &models.Pipeline{
		PipelineId: "testPipelineId",
		Type:       v1.PipelineType_RESERVED,
	}

	t.Run("success", func(t *testing.T) {
		existingPipeline := &models.Pipeline{
			PipelineId: "testPipelineId",
			Type:       v1.PipelineType_RESERVED,
		}
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(existingPipeline, nil)
		mockRepo.EXPECT().Update(ctx, pipeline).Return(true, nil)

		success, err := biz.UpdatePipeline(ctx, pipeline)
		require.NoError(t, err)
		assert.True(t, success)
	})

	t.Run("pipeline not exist", func(t *testing.T) {
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(nil, nil)

		success, err := biz.UpdatePipeline(ctx, pipeline)
		assert.Error(t, err)
		assert.False(t, success)
	})

	t.Run("repo update error", func(t *testing.T) {
		existingPipeline := &models.Pipeline{
			PipelineId: "testPipelineId",
			Type:       v1.PipelineType_RESERVED,
		}
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(existingPipeline, nil)
		mockRepo.EXPECT().Update(ctx, pipeline).Return(false, errors.New("update error"))

		success, err := biz.UpdatePipeline(ctx, pipeline)
		assert.Error(t, err)
		assert.False(t, success)
	})
}

func TestCommonPipelineBiz_DeletePipeline(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mocks.NewMockAsyncTaskPipelineRepo(ctrl)
	//logger := log.NewHelper(log.With(log.GetLogger(), "module", "taskmanager/pipeline"))
	ctx := context.Background()

	biz := NewCommonPipelineBiz(mockRepo, nil, log.DefaultLogger)

	pipeline := &models.Pipeline{
		PipelineId: "testPipelineId",
		Type:       v1.PipelineType_RESERVED,
	}

	t.Run("success", func(t *testing.T) {
		existingPipeline := &models.Pipeline{
			PipelineId: "testPipelineId",
			Type:       v1.PipelineType_RESERVED,
		}
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(existingPipeline, nil)
		mockRepo.EXPECT().Delete(ctx, pipeline.PipelineId, pipeline.Type).Return(true, nil)

		success, err := biz.DeletePipeline(ctx, pipeline)
		require.NoError(t, err)
		assert.True(t, success)
	})

	t.Run("pipeline not exist", func(t *testing.T) {
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(nil, nil)

		success, err := biz.DeletePipeline(ctx, pipeline)
		assert.Error(t, err)
		assert.False(t, success)
	})

	t.Run("repo delete error", func(t *testing.T) {
		existingPipeline := &models.Pipeline{
			PipelineId: "testPipelineId",
			Type:       v1.PipelineType_RESERVED,
		}
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(existingPipeline, nil)
		mockRepo.EXPECT().Delete(ctx, pipeline.PipelineId, pipeline.Type).Return(false, errors.New("delete error"))

		success, err := biz.DeletePipeline(ctx, pipeline)
		assert.Error(t, err)
		assert.False(t, success)
	})
}

func TestCommonPipelineBiz_UpdatePipelineStatus(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mocks.NewMockAsyncTaskPipelineRepo(ctrl)
	//logger := log.NewHelper(log.With(log.GetLogger(), "module", "taskmanager/pipeline"))
	ctx := context.Background()

	biz := NewCommonPipelineBiz(mockRepo, nil, log.DefaultLogger)

	pipeline := &models.Pipeline{
		PipelineId: "testPipelineId",
		Type:       v1.PipelineType_RESERVED,
	}

	t.Run("success", func(t *testing.T) {
		existingPipeline := &models.Pipeline{
			PipelineId: "testPipelineId",
			Type:       v1.PipelineType_RESERVED,
		}
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(existingPipeline, nil).AnyTimes()
		mockRepo.EXPECT().UpdatePipelineStatus(ctx, pipeline).Return(true, nil).AnyTimes()

		success, err := biz.UpdatePipelineStatus(ctx, pipeline)
		require.NoError(t, err)
		assert.True(t, success)
	})

	t.Run("pipeline not exist and create success", func(t *testing.T) {
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(nil, nil).AnyTimes()
		mockRepo.EXPECT().Create(ctx, pipeline).Return(true, nil).AnyTimes()
		mockRepo.EXPECT().UpdatePipelineStatus(ctx, pipeline).Return(true, nil).AnyTimes()

		success, err := biz.UpdatePipelineStatus(ctx, pipeline)
		require.NoError(t, err)
		assert.True(t, success)
	})

	//t.Run("pipeline not exist and create error", func(t *testing.T) {
	//	mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(nil, nil).Times(1)
	//	mockRepo.EXPECT().Create(ctx, pipeline).Return(false, errors.New("create error")).Times(1)
	//
	//	success, err := biz.UpdatePipelineStatus(ctx, pipeline)
	//	assert.Error(t, err)
	//	assert.False(t, success)
	//})

	//t.Run("repo update status error", func(t *testing.T) {
	//	existingPipeline := &models.Pipeline{
	//		PipelineId: "testPipelineId",
	//		Type:       v1.PipelineType_RESERVED,
	//	}
	//	mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(existingPipeline, nil).Times(1)
	//	mockRepo.EXPECT().UpdatePipelineStatus(ctx, pipeline).Return(false, errors.New("update error")).Times(1)
	//
	//	success, err := biz.UpdatePipelineStatus(ctx, pipeline)
	//	assert.Error(t, err)
	//	assert.False(t, success)
	//})
}

func TestCommonPipelineBiz_UpdatePipelineStatusError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mocks.NewMockAsyncTaskPipelineRepo(ctrl)
	//logger := log.NewHelper(log.With(log.GetLogger(), "module", "taskmanager/pipeline"))
	ctx := context.Background()

	biz := NewCommonPipelineBiz(mockRepo, nil, log.DefaultLogger)

	pipeline := &models.Pipeline{
		PipelineId: "testPipelineId",
		Type:       v1.PipelineType_RESERVED,
	}

	t.Run("pipeline not exist and create error", func(t *testing.T) {
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(nil, nil).Times(2)
		mockRepo.EXPECT().Create(ctx, pipeline).Return(false, errors.New("create error")).Times(1)

		success, err := biz.UpdatePipelineStatus(ctx, pipeline)
		assert.Error(t, err)
		assert.False(t, success)
	})

	t.Run("repo update status error", func(t *testing.T) {
		existingPipeline := &models.Pipeline{
			PipelineId: "testPipelineId",
			Type:       v1.PipelineType_RESERVED,
		}
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(existingPipeline, nil).Times(1)
		mockRepo.EXPECT().UpdatePipelineStatus(ctx, pipeline).Return(false, errors.New("update error")).Times(1)

		success, err := biz.UpdatePipelineStatus(ctx, pipeline)
		assert.Error(t, err)
		assert.False(t, success)
	})
}

func TestCommonPipelineBiz_UpdatePipelinesScheduleLevel(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mocks.NewMockAsyncTaskPipelineRepo(ctrl)
	ctx := context.Background()

	biz := NewCommonPipelineBiz(mockRepo, nil, log.DefaultLogger)

	pipeline := &models.Pipeline{
		PipelineId: "testPipelineId",
		Type:       v1.PipelineType_RESERVED,
		UserId:     "testUserId",
	}

	t.Run("pipeline exists and update success", func(t *testing.T) {
		existingPipeline := &models.Pipeline{
			PipelineId: "testPipelineId",
			Type:       v1.PipelineType_RESERVED,
			UserId:     "testUserId",
		}
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(existingPipeline, nil).Times(1)
		mockRepo.EXPECT().UpdatePipelineScheduleLevel(ctx, pipeline).Return(true, nil).Times(1)

		success, err := biz.UpdatePipelinesScheduleLevel(ctx, pipeline)
		require.NoError(t, err)
		assert.True(t, success)
	})

	t.Run("pipeline does not exist", func(t *testing.T) {
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(nil, nil).Times(1)

		success, err := biz.UpdatePipelinesScheduleLevel(ctx, pipeline)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "pipeline not exist")
		assert.False(t, success)
	})

	t.Run("update fails", func(t *testing.T) {
		existingPipeline := &models.Pipeline{
			PipelineId: "testPipelineId",
			Type:       v1.PipelineType_RESERVED,
			UserId:     "testUserId",
		}
		mockRepo.EXPECT().FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type).Return(existingPipeline, nil).Times(1)
		mockRepo.EXPECT().UpdatePipelineScheduleLevel(ctx, pipeline).Return(false, errors.New("update error")).Times(1)

		success, err := biz.UpdatePipelinesScheduleLevel(ctx, pipeline)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "update error")
		assert.False(t, success)
	})
}

func TestCommonPipelineBiz_UpdatePipelinesScheduleLevelByUserId(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mocks.NewMockAsyncTaskPipelineRepo(ctrl)
	ctx := context.Background()

	biz := NewCommonPipelineBiz(mockRepo, nil, log.DefaultLogger)

	pipeline := &models.Pipeline{
		PipelineId: "testPipelineId",
		Type:       v1.PipelineType_RESERVED,
		UserId:     "testUserId",
	}

	t.Run("pipeline exists and update success", func(t *testing.T) {
		existingPipelines := []*models.Pipeline{
			{
				PipelineId: "testPipelineId",
				Type:       v1.PipelineType_RESERVED,
				UserId:     "testUserId",
			},
		}
		mockRepo.EXPECT().QueryByUserId(ctx, pipeline.UserId).Return(existingPipelines, nil).Times(1)
		mockRepo.EXPECT().UpdatePipelineScheduleLevelByUserId(ctx, pipeline).Return(true, nil).Times(1)

		success, err := biz.UpdatePipelinesScheduleLevelByUserId(ctx, pipeline)
		require.NoError(t, err)
		assert.True(t, success)
	})

	t.Run("pipeline does not exist", func(t *testing.T) {
		mockRepo.EXPECT().QueryByUserId(ctx, pipeline.UserId).Return(nil, nil).Times(1)

		success, err := biz.UpdatePipelinesScheduleLevelByUserId(ctx, pipeline)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "pipeline not exist")
		assert.False(t, success)
	})

	t.Run("update fails", func(t *testing.T) {
		existingPipelines := []*models.Pipeline{
			{
				PipelineId: "testPipelineId",
				Type:       v1.PipelineType_RESERVED,
				UserId:     "testUserId",
			},
		}
		mockRepo.EXPECT().QueryByUserId(ctx, pipeline.UserId).Return(existingPipelines, nil).Times(1)
		mockRepo.EXPECT().UpdatePipelineScheduleLevelByUserId(ctx, pipeline).Return(false, errors.New("update error")).Times(1)

		success, err := biz.UpdatePipelinesScheduleLevelByUserId(ctx, pipeline)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "update error")
		assert.False(t, success)
	})
}
