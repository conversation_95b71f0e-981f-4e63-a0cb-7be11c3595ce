package pipeline

import (
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"
	v1 "proto.mpp/api/common/v1"
)

type CommonPipelineBiz struct {
	repo         repository.AsyncTaskPipelineRepo
	sentinelRepo repository.TaskSentinelConfigRepo
	log          *log.Helper
}

func NewCommonPipelineBiz(repo repository.AsyncTaskPipelineRepo, sentinelRepo repository.TaskSentinelConfigRepo, logger log.Logger) *CommonPipelineBiz {
	return &CommonPipelineBiz{repo: repo, sentinelRepo: sentinelRepo, log: log.NewHelper(logger)}
}

func (p *CommonPipelineBiz) CreatePipeline(ctx context.Context, pipeline *models.Pipeline) (bool, error) {
	var (
		err error
	)
	existPipeline, err := p.repo.FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type)
	if existPipeline != nil {
		p.log.WithContext(ctx).Warnf("CreatePipeline failed,pipeline already exist, existPipeline:%v, pipeline:%v", existPipeline, pipeline)
		return false, errors.New("pipeline already exist,existPipelineId:" + existPipeline.PipelineId)
	}
	hasCreateSuccess, err := p.repo.Create(ctx, pipeline)
	if err != nil {
		p.log.WithContext(ctx).Errorf("CreatePipeline failed,err:%v", err)
		return false, err
	}
	p.log.WithContext(ctx).Infof("CreatePipeline success,pipeline:%v", pipeline)
	return hasCreateSuccess, nil
}

func (p *CommonPipelineBiz) GetPipeline(ctx context.Context, pipelineId string, pipelineType v1.PipelineType) (*models.Pipeline, error) {
	var (
		err error
	)
	pipeline, err := p.repo.FindByPipelineAndType(ctx, pipelineId, pipelineType)
	if err != nil {
		p.log.WithContext(ctx).Errorf("GetPipeline failed,err:%v", err)
		return nil, err
	}

	return pipeline, nil
}
func (p *CommonPipelineBiz) UpdatePipeline(ctx context.Context, pipeline *models.Pipeline) (bool, error) {
	var (
		err error
	)

	existPipeline, err := p.repo.FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type)
	if existPipeline == nil {
		p.log.WithContext(ctx).Warnf("UpdatePipeline failed,pipeline not exist, pipeline:%v", pipeline)
		return false, errors.New("pipeline not exist,pipelineId:" + pipeline.PipelineId)
	}

	_, err = p.repo.Update(ctx, pipeline)
	if err != nil {
		p.log.WithContext(ctx).Errorf("UpdatePipeline failed,err:%v", err)
		return false, err

	}
	return true, nil
}
func (p *CommonPipelineBiz) DeletePipeline(ctx context.Context, pipeline *models.Pipeline) (bool, error) {
	var (
		err error
	)
	existPipeline, err := p.repo.FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type)
	if existPipeline == nil {
		p.log.WithContext(ctx).Warnf("DeletePipeline failed,pipeline not exist, pipeline:%v", pipeline)
		return false, errors.New("pipeline not exist,pipelineId:" + pipeline.PipelineId)
	}

	_, err = p.repo.Delete(ctx, pipeline.PipelineId, pipeline.Type)
	if err != nil {
		p.log.WithContext(ctx).Errorf("DeletePipeline failed,err:%v", err)
		return false, err

	}
	return true, nil
}

func (p *CommonPipelineBiz) UpdatePipelineStatus(ctx context.Context, pipeline *models.Pipeline) (bool, error) {
	var (
		err error
	)
	existPipeline, err := p.repo.FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type)
	if existPipeline == nil {
		_, err = p.CreatePipeline(ctx, pipeline)
		if err != nil {
			p.log.WithContext(ctx).Errorf("UpdatePipelineStatus failed in CreatePipeline, pipelineId:%s ,err:%v ,pipeline:%v", pipeline.PipelineId, err, pipeline)
			return false, err
		}
	}
	_, err = p.repo.UpdatePipelineStatus(ctx, pipeline)
	if err != nil {
		p.log.WithContext(ctx).Errorf("UpdatePipelineStatus failed, pipelineId:%s ,err:%v ,pipeline:%v", pipeline.PipelineId, err, pipeline)
		return false, err

	}
	return true, nil
}

func (p *CommonPipelineBiz) UpdatePipelinesScheduleLevel(ctx context.Context, pipeline *models.Pipeline) (bool, error) {
	var (
		err error
	)
	existPipelines, err := p.repo.FindByPipelineAndType(ctx, pipeline.PipelineId, pipeline.Type)
	if existPipelines == nil {
		p.log.WithContext(ctx).Errorf("UpdatePipelinesScheduleLevel failed,pipeline not exist, pipeline:%v", pipeline)
		return false, errors.New("pipeline not exist,userId:" + pipeline.UserId)
	}

	_, err = p.repo.UpdatePipelineScheduleLevel(ctx, pipeline)
	if err != nil {
		p.log.WithContext(ctx).Errorf("UpdatePipelineScheduleLevel failed, pipelineId:%s ,err:%v ,pipeline:%v", pipeline.PipelineId, err, pipeline)
		return false, err

	}
	return true, nil
}

func (p *CommonPipelineBiz) UpdatePipelinesScheduleLevelByUserId(ctx context.Context, pipeline *models.Pipeline) (bool, error) {
	var (
		err error
	)
	existPipelines, err := p.repo.QueryByUserId(ctx, pipeline.UserId)
	if existPipelines == nil {
		p.log.WithContext(ctx).Errorf("UpdatePipelinesScheduleLevelByUserId failed,pipeline not exist, pipeline:%v", pipeline)
		return false, errors.New("pipeline not exist,userId:" + pipeline.UserId)
	}

	_, err = p.repo.UpdatePipelineScheduleLevelByUserId(ctx, pipeline)
	if err != nil {
		p.log.WithContext(ctx).Errorf("UpdatePipelinesScheduleLevelByUserId failed, userId:%s ,err:%v ,pipeline:%v", pipeline.UserId, err, pipeline)
		return false, err

	}
	return true, nil
}
