package sentinel

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/alicebob/miniredis/v2"
	"github.com/bytedance/mockey"
	config2 "github.com/cinience/animus/config"
	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/mocks"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	_ "github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/golang/mock/gomock"
)

var biz *SentinelBiz
var mockRepo *mocks.MockTaskSentinelConfigRepo
var redis *repository.RedisClient
var watch *mockey.Mocker
var mini *miniredis.Miniredis

func initTest(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mini = miniredis.RunT(t)

	config := &conf.Bootstrap{
		Data: &conf.Data{
			Redis: &conf.Data_Redis{
				Endpoint: "redis://" + mini.Addr(),
			},
		},
	}

	mockRepo = mocks.NewMockTaskSentinelConfigRepo(ctrl)
	redis = repository.NewRedisClient(config)
	logger := log.NewStdLogger(os.Stdout)

	watch = mockey.Mock(config2.WatchConfig).Return(nil).Build()
	biz = NewSentinelBiz(config, mockRepo, logger, redis)
}

func TestCreateTaskSentinelConfig(t *testing.T) {
	initTest(t)
	defer watch.UnPatch()
	defer mini.Close()

	taskSentinelConfig := &models.TaskSentinelConfig{
		SentinelConfigId: "1",
		UserId:           "1",
		PipelineId:       "1",
		TaskType:         1,
		Product:          "product",
		EngineModel:      "model",
		Tag:              "tag",
	}

	t.Run("TestCreateTaskSentinelConfig_success", func(t *testing.T) {
		// 设置模拟对象的行为
		mockRepo.EXPECT().InsertUpdateOnDuplicate(context.Background(), taskSentinelConfig).Return(nil)

		defer mockey.Mock((*SentinelBiz).UpdateSentinelConfigInRedis).Return(nil).Build().UnPatch()

		// 执行测试
		result, err := biz.CreateTaskSentinelConfig(context.Background(), taskSentinelConfig)
		require.NoError(t, err)
		assert.Equal(t, true, result)
	})

	t.Run("TestCreateTaskSentinelConfig_WithInsertUpdateOnDuplicateError", func(t *testing.T) {
		// 设置模拟对象的行为
		mockRepo.EXPECT().InsertUpdateOnDuplicate(context.Background(), taskSentinelConfig).Return(errors.New("insert update on duplicate error"))

		// 执行测试
		result, err := biz.CreateTaskSentinelConfig(context.Background(), taskSentinelConfig)
		require.Error(t, err)
		assert.False(t, result)
	})

	t.Run("TestCreateTaskSentinelConfig_WithUpdateSentinelConfigInRedisError", func(t *testing.T) {
		// 设置模拟对象的行为
		mockRepo.EXPECT().InsertUpdateOnDuplicate(context.Background(), taskSentinelConfig).Return(nil)
		defer mockey.Mock((*SentinelBiz).UpdateSentinelConfigInRedis).Return(errors.New("update sentinel config in redis error")).Build().UnPatch()
		// 执行测试
		result, err := biz.CreateTaskSentinelConfig(context.Background(), taskSentinelConfig)
		require.Error(t, err)
		assert.False(t, result)
	})
}

func TestDeleteTaskSentinelConfig(t *testing.T) {
	initTest(t)
	defer watch.UnPatch()
	defer mini.Close()

	taskSentinelConfig := &models.TaskSentinelConfig{
		SentinelConfigId: "1",
		UserId:           "1",
		PipelineId:       "1",
		TaskType:         1,
		Product:          "product",
		EngineModel:      "model",
		Tag:              "tag",
	}

	t.Run("TestDeleteTaskSentinelConfig_success", func(t *testing.T) {
		mockRepo.EXPECT().GetTaskSentinelConfig(context.Background(), "1").Return(taskSentinelConfig, nil)
		mockRepo.EXPECT().Delete(context.Background(), "1").Return(nil)

		// 执行测试
		result, err := biz.DeleteTaskSentinelConfig(context.Background(), taskSentinelConfig)
		require.NoError(t, err)
		assert.NotNil(t, result)
	})

	t.Run("TestDeleteTaskSentinelConfig_WithGetTaskSentinelConfigError", func(t *testing.T) {
		mockRepo.EXPECT().GetTaskSentinelConfig(context.Background(), "1").Return(nil, errors.New("get task sentinel config error"))

		// 执行测试
		result, err := biz.DeleteTaskSentinelConfig(context.Background(), taskSentinelConfig)
		require.Error(t, err)
		assert.Nil(t, result)
	})

	t.Run("TestDeleteTaskSentinelConfig_WithGetTaskSentinelConfigNil", func(t *testing.T) {
		mockRepo.EXPECT().GetTaskSentinelConfig(context.Background(), "1").Return(nil, nil)

		// 执行测试
		result, err := biz.DeleteTaskSentinelConfig(context.Background(), taskSentinelConfig)
		require.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, "DeleteTaskSentinelConfig failed and taskSentinelConfig is nil", err.Error())
	})

	t.Run("TestDeleteTaskSentinelConfig_WithDeleteError", func(t *testing.T) {
		mockRepo.EXPECT().GetTaskSentinelConfig(context.Background(), "1").Return(taskSentinelConfig, nil)
		mockRepo.EXPECT().Delete(context.Background(), "1").Return(errors.New("delete error"))

		// 执行测试
		result, err := biz.DeleteTaskSentinelConfig(context.Background(), taskSentinelConfig)
		require.Error(t, err)
		assert.Nil(t, result)
	})
}

func TestGetTaskSentinelConfig(t *testing.T) {
	initTest(t)
	defer watch.UnPatch()
	defer mini.Close()

	taskSentinelConfig := &models.TaskSentinelConfig{
		SentinelConfigId: "1",
		UserId:           "1",
		PipelineId:       "1",
		TaskType:         1,
		Product:          "product",
		EngineModel:      "model",
		Tag:              "tag",
	}

	t.Run("TestGetTaskSentinelConfig_success", func(t *testing.T) {
		mockRepo.EXPECT().GetTaskSentinelConfig(context.Background(), "1").Return(taskSentinelConfig, nil)

		// 执行测试
		result, err := biz.GetTaskSentinelConfig(context.Background(), taskSentinelConfig)
		require.NoError(t, err)
		assert.Equal(t, taskSentinelConfig, result)
	})

	t.Run("TestGetTaskSentinelConfig_WithError", func(t *testing.T) {
		mockRepo.EXPECT().GetTaskSentinelConfig(context.Background(), "1").Return(nil, errors.New("get task sentinel config error"))

		// 执行测试
		result, err := biz.GetTaskSentinelConfig(context.Background(), taskSentinelConfig)
		require.Error(t, err)
		assert.Nil(t, result)
	})
}

//func TestUpdateTaskSentinelConfig(t *testing.T) {
//	initTest(t)
//	defer watch.UnPatch()
//	defer mini.Close()
//
//	taskSentinelConfig := &models.TaskSentinelConfig{
//		//SentinelConfigId: "1",
//		UserId:      "1",
//		PipelineId:  "1",
//		TaskType:    1,
//		Product:     "product",
//		EngineModel: "model",
//		Tag:         "tag",
//	}
//
//	t.Run("TestUpdateTaskSentinelConfig_success", func(t *testing.T) {
//		mockRepo.EXPECT().UpdateTaskSentinelConfig(context.Background(), taskSentinelConfig).Return(true, nil)
//
//		// 执行测试
//		result, err := biz.UpdateTaskSentinelConfig(context.Background(), taskSentinelConfig)
//		require.NoError(t, err)
//		assert.True(t, result)
//	})
//
//	t.Run("TestUpdateTaskSentinelConfig_WithError", func(t *testing.T) {
//		mockRepo.EXPECT().UpdateTaskSentinelConfig(context.Background(), taskSentinelConfig).Return(false, errors.New("update task sentinel config error"))
//
//		// 执行测试
//		result, err := biz.UpdateTaskSentinelConfig(context.Background(), taskSentinelConfig)
//		require.Error(t, err)
//		assert.False(t, result)
//	})
//}

func TestUpdateSentinelStatus(t *testing.T) {
	initTest(t)
	defer watch.UnPatch()
	defer mini.Close()

	taskSentinelConfig := &models.TaskSentinelConfig{
		SentinelConfigId: "1",
		UserId:           "1",
		PipelineId:       "1",
		TaskType:         1,
		Product:          "product",
		EngineModel:      "model",
		Tag:              "tag",
	}

	t.Run("TestUpdateSentinelStatus_success", func(t *testing.T) {
		mockRepo.EXPECT().UpdateSentinelStatus(context.Background(), taskSentinelConfig).Return(true, nil)

		// 执行测试
		result, err := biz.UpdateSentinelStatus(context.Background(), taskSentinelConfig)
		require.NoError(t, err)
		assert.True(t, result)
	})

	t.Run("TestUpdateSentinelStatus_WithError", func(t *testing.T) {
		mockRepo.EXPECT().UpdateSentinelStatus(context.Background(), taskSentinelConfig).Return(false, errors.New("update task sentinel config error"))

		// 执行测试
		result, err := biz.UpdateSentinelStatus(context.Background(), taskSentinelConfig)
		require.Error(t, err)
		assert.False(t, result)
	})
}

func TestDataExists(t *testing.T) {
	initTest(t)
	defer watch.UnPatch()
	defer mini.Close()

	// 测试键存在的情况
	keyExists := "test_key_exists"
	valExists := "some_value"
	redis.GetClient().Set(context.Background(), keyExists, valExists, 0)
	exists := biz.DataExists(context.Background(), keyExists)
	assert.True(t, exists)

	// 测试键不存在的情况
	keyNotExists := "test_key_not_exists"

	notExists := biz.DataExists(context.Background(), keyNotExists)
	assert.False(t, notExists)
}

func TestGetSentinelData(t *testing.T) {
	initTest(t)
	defer watch.UnPatch()
	defer mini.Close()

	taskSentinelData := &models.TaskSentinelData{
		Id:               "1",
		SentinelConfigId: "1",
	}

	// 测试键存在的情况
	keyExists := "test_key_exists"
	val, _ := json.Marshal(taskSentinelData)
	redis.GetClient().Set(context.Background(), keyExists, val, 0)
	exists, err := biz.GetSentinelData(context.Background(), keyExists)
	assert.Nil(t, err)
	assert.Equal(t, taskSentinelData.SentinelConfigId, exists.SentinelConfigId)

	// 测试键不存在的情况
	keyNotExists := "test_key_not_exists"

	notExists, err := biz.GetSentinelData(context.Background(), keyNotExists)
	assert.Nil(t, err)
	assert.Nil(t, notExists)
}

func TestInsertSentinelData(t *testing.T) {
	initTest(t)
	defer watch.UnPatch()
	defer mini.Close()

	taskSentinelData := &models.TaskSentinelData{
		Id:               "1",
		SentinelConfigId: "11111111",
	}

	// 测试键存在的情况
	keyExists := "test_key_exists"
	exists, err := biz.InsertSentinelData(context.Background(), keyExists, taskSentinelData)
	assert.Nil(t, err)
	assert.Equal(t, true, exists)
	data := redis.GetClient().Get(context.Background(), keyExists)
	val, _ := json.Marshal(taskSentinelData)
	assert.Equal(t, data.Val(), string(val))
}

func TestSetSentinelDataAndInsertTaskSentinelNumData(t *testing.T) {
	initTest(t)
	defer watch.UnPatch()
	defer mini.Close()

	// 测试键不存在的情况
	keyNotExists := "test_key_not_exists"
	err := biz.SetSentinelData(context.Background(), keyNotExists, 1, "id111", 1, true)
	assert.Nil(t, err)
	data := redis.GetClient().Get(context.Background(), keyNotExists)
	assert.Contains(t, data.Val(), "id111")

	// 测试键存在的情况
	taskSentinelData := &models.TaskSentinelData{
		Id:               "1",
		SentinelConfigId: "1",
	}
	keyExists := "test_key_exists"
	val, _ := json.Marshal(taskSentinelData)
	redis.GetClient().Set(context.Background(), keyExists, val, 0)
	err = biz.SetSentinelData(context.Background(), keyExists, 1, "id222", 1, true)
	assert.Nil(t, err)
	data = redis.GetClient().Get(context.Background(), keyExists)
	assert.Contains(t, data.Val(), "id222")

	result, err := biz.InsertTaskSentinelNumData(context.Background(), "key_insert", 1)
	assert.Nil(t, err)
	assert.Equal(t, true, result)
}

func TestUpdateSentinelConfigInRedis(t *testing.T) {
	initTest(t)
	defer watch.UnPatch()
	defer mini.Close()

	TaskSentinelConfig := &models.TaskSentinelConfig{
		SentinelConfigId: "11111111",
		SentinelConfig: &models.SentinelConfig{
			MaxParallelNum: 1,
		},
	}

	err := biz.UpdateSentinelConfigInRedis(context.Background(), TaskSentinelConfig)
	assert.Nil(t, err)

	TaskSentinelConfig.SentinelStatus = models.SentinelStatusStateActive
	err = biz.UpdateSentinelConfigInRedis(context.Background(), TaskSentinelConfig)
	assert.Nil(t, err)
}

func TestIncreaseTaskRunningNumAndDecreaseTaskRunningNum(t *testing.T) {
	initTest(t)
	defer watch.UnPatch()
	defer mini.Close()

	task := &models.Task{
		Product:     "product",
		TaskId:      "taskId",
		JobId:       "jobId",
		UserId:      "userId",
		PipelineId:  "pipelineId",
		EngineModel: "engineModel",
		Tag:         "tag",
		TaskType:    1,
	}

	// 没数据，不创建
	err := biz.DecreaseTaskRunningNum(context.Background(), task)
	assert.Nil(t, err)

	// 没数据，创建
	err = biz.IncreaseTaskRunningNum(context.Background(), task)
	assert.Nil(t, err)

	// 有数据，更新
	err = biz.IncreaseTaskRunningNum(context.Background(), task)
	assert.Nil(t, err)
	err = biz.DecreaseTaskRunningNum(context.Background(), task)
	assert.Nil(t, err)
}

func TestCheckTaskIsEnableEnqueue(t *testing.T) {
	initTest(t)
	defer watch.UnPatch()
	defer mini.Close()

	task := &models.Task{
		Product:     "product",
		TaskId:      "taskId",
		JobId:       "jobId",
		UserId:      "userId",
		PipelineId:  "pipelineId",
		EngineModel: "engineModel",
		Tag:         "tag",
		TaskType:    1,
	}

	bo, err := biz.CheckTaskIsEnableEnqueue(context.Background(), task)
	assert.Nil(t, err)
	assert.Equal(t, true, bo)

	err = biz.SetSentinelData(context.Background(), "taskSentinel-1-userId-product-engineModel-tag-pipelineId", 1, "id222", 1, true)
	bo, err = biz.CheckTaskIsEnableEnqueue(context.Background(), task)
	assert.Nil(t, err)
	assert.Equal(t, false, bo)

	err = biz.SetSentinelData(context.Background(), "taskSentinel-1-userId-product-engineModel-tag-pipelineId", 1, "id222", 3, true)
	bo, err = biz.CheckTaskIsEnableEnqueue(context.Background(), task)
	assert.Nil(t, err)
	assert.Equal(t, true, bo)
}

func TestSentinelDataAnalyze(t *testing.T) {
	initTest(t)
	defer watch.UnPatch()
	defer mini.Close()

	err := biz.SetSentinelData(context.Background(), "taskSentinel-1-userId-product-engineModel-tag-pipelineId", 1, "id222", 1, true)
	biz.sentinelDataAnalyze("taskSentinel-1-userId-product-engineModel")
	assert.Nil(t, err)
}
