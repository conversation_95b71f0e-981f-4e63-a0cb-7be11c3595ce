package sentinel

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/cinience/animus/config"
	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"
	"time"
)

const (
	sentinel_data_analyze = "sentinel-data-analyze"
	// 默认最大并发
	default_max_parallel_num = 200
)

// 默认流控配置，从nacos配置中获取最新配置
var default_sentinel_config *models.SentinelConfig

// 默认流控配置
var default_sentinel_config_enable bool = true

type SentinelBiz struct {
	repo repository.TaskSentinelConfigRepo
	log  *log.Helper

	redis *repository.RedisClient
	conf  *conf.Bootstrap
}

func NewSentinelBiz(conf *conf.Bootstrap, repo repository.TaskSentinelConfigRepo, logger log.Logger, redis *repository.RedisClient) *SentinelBiz {
	sentinelBiz := &SentinelBiz{conf: conf, repo: repo, log: log.NewHelper(logger), redis: redis}

	//设置默认流控配置
	default_sentinel_config = &models.SentinelConfig{
		MaxParallelNum: default_max_parallel_num,
	}
	if conf.DefaultSentinelConfig != nil {
		default_sentinel_config_enable = conf.DefaultSentinelConfig.Enable
		default_sentinel_config.MaxParallelNum = conf.DefaultSentinelConfig.DefaultMaxParallelNum

	}
	log.Infof("NewSentinelBiz set default sentinelConfig, sentinelConfig:%+v,default_sentinel_config_enable:%v", default_sentinel_config, default_sentinel_config_enable)
	//启动定时分析任务
	go sentinelBiz.sentinelDataAnalyzeScheduler()
	//启动配置监听
	go sentinelBiz.reloadConfig()

	return sentinelBiz
}

// 动态加载sentinel默认配置
func (s *SentinelBiz) reloadConfig() {
	var ss conf.DefaultSentinelConfig
	err := config.WatchConfig("defaultSentinelConfig", &ss, func() {
		log.Infof("sentinel config changed, defaultSentinelConfig:%+v", ss)
		default_sentinel_config.MaxParallelNum = ss.DefaultMaxParallelNum
		default_sentinel_config_enable = ss.Enable
	})
	if err != nil {
		return
	}
}
func (s *SentinelBiz) CreateTaskSentinelConfig(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) (bool, error) {
	//根据流控配置生成sentinelConfigId
	taskSentinelConfig.GenerateSentinelConfigId()
	//设置sentinel状态为启用
	taskSentinelConfig.SentinelStatus = models.SentinelStatusStateActive
	//在数据库中创建流控配置
	err := s.repo.InsertUpdateOnDuplicate(ctx, taskSentinelConfig)
	if err != nil {
		s.log.WithContext(ctx).Errorf("CreateTaskSentinelConfig failed in InsertUpdateOnDuplicate, taskSentinelConfig:%+v, error: %v", taskSentinelConfig, err)
		return false, err
	}
	//更新redis中数据库配置
	err = s.UpdateSentinelConfigInRedis(ctx, taskSentinelConfig)
	if err != nil {
		s.log.WithContext(ctx).Errorf("CreateTaskSentinelConfig failed in UpdateSentinelConfigInRedis, taskSentinelConfig:%+v, error: %v", taskSentinelConfig, err)
		return false, err
	}

	s.log.WithContext(ctx).Infof("CreateTaskSentinelConfig success, taskSentinelConfig:%+v", taskSentinelConfig)
	return true, nil
}

func (s *SentinelBiz) DeleteTaskSentinelConfig(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) (*models.TaskSentinelConfig, error) {
	if taskSentinelConfig.SentinelConfigId == "" {
		taskSentinelConfig.GenerateSentinelConfigId()
	}
	sentinelConfigId := taskSentinelConfig.SentinelConfigId
	getTaskSentinelConfig, err := s.repo.GetTaskSentinelConfig(ctx, sentinelConfigId)
	if err != nil {
		s.log.WithContext(ctx).Errorf("DeleteTaskSentinelConfig failed in GetTaskSentinelConfig,taskSentinelConfig:%+v error: %v", taskSentinelConfig, err)
		return nil, err
	}
	if getTaskSentinelConfig == nil {
		s.log.WithContext(ctx).Errorf("DeleteTaskSentinelConfig failed in GetTaskSentinelConfig,taskSentinelConfig:%+v error: %v", taskSentinelConfig, err)
		return nil, errors.New("DeleteTaskSentinelConfig failed and taskSentinelConfig is nil")
	}
	err = s.repo.Delete(ctx, sentinelConfigId)
	if err != nil {
		s.log.WithContext(ctx).Errorf("DeleteTaskSentinelConfig failed in UpdateSentinelStatus,taskSentinelConfig:%+v error: %v", taskSentinelConfig, err)
		return nil, err

	}
	return taskSentinelConfig, nil
}

func (s *SentinelBiz) GetTaskSentinelConfig(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) (*models.TaskSentinelConfig, error) {
	if taskSentinelConfig.SentinelConfigId == "" {
		taskSentinelConfig.GenerateSentinelConfigId()
	}
	sentinelConfigId := taskSentinelConfig.SentinelConfigId

	taskSentinelConfig, err := s.repo.GetTaskSentinelConfig(ctx, sentinelConfigId)
	if err != nil {
		s.log.WithContext(ctx).Errorf("GetTaskSentinelConfig failed  error: %v", err)
		return nil, err

	}
	return taskSentinelConfig, nil
}

func (s *SentinelBiz) UpdateTaskSentinelConfig(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) (bool, error) {
	//根据更新后的配置生成新的sentinelConfigId
	if taskSentinelConfig.SentinelConfigId == "" {
		taskSentinelConfig.GenerateSentinelConfigId()
	}
	_, err := s.repo.UpdateTaskSentinelConfig(ctx, taskSentinelConfig)
	if err != nil {
		s.log.WithContext(ctx).Errorf("UpdateTaskSentinelConfig failed  error: %v", err)
		return false, err
	}
	return true, nil
}

func (s *SentinelBiz) UpdateSentinelStatus(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) (bool, error) {
	//根据更新后的配置生成新的sentinelConfigId
	if taskSentinelConfig.SentinelConfigId == "" {
		taskSentinelConfig.GenerateSentinelConfigId()
	}
	_, err := s.repo.UpdateSentinelStatus(ctx, taskSentinelConfig)
	if err != nil {
		s.log.WithContext(ctx).Errorf("UpdateSentinelStatus failed  error: %v", err)
		return false, err
	}
	return true, nil
}

// DataExists 路由表中存在数据
func (r *SentinelBiz) DataExists(ctx context.Context, key string) bool {
	data := r.redis.GetClient().Get(ctx, key)
	if data.Val() != "" {
		return true
	}
	return false
}

func (r *SentinelBiz) GetSentinelData(ctx context.Context, key string) (*models.TaskSentinelData, error) {

	data := r.redis.GetClient().Get(ctx, key)
	if data != nil && data.Val() != "" {
		var sentinelData *models.TaskSentinelData
		err := json.Unmarshal([]byte(data.Val()), &sentinelData)
		if err != nil {
			log.Errorf("GetSentinelData err:%v", err)
			return nil, err
		}
		return sentinelData, nil
	} else {
		log.Infof("GetSentinelData data is nil, key:%s", key)
		return nil, nil
	}

}

func (r *SentinelBiz) InsertSentinelData(ctx context.Context, key string, routeData *models.TaskSentinelData) (bool, error) {

	newData, err := json.Marshal(routeData)
	if err != nil {
		log.Errorf("InsertSentinelData json marshal  err:%v", err)
		return false, err
	}
	log.Infof("InsertSentinelData start, id:%s, newData:%v", key, newData)

	//流控信息保存十分钟
	expireTime := 10 * 60 * time.Second

	result := r.redis.GetClient().Set(context.Background(), key, newData, expireTime)
	reStr, err := result.Result()
	if err != nil {
		log.Errorf("InsertSentinelData err:%v", err)
		return false, err
	}
	log.Infof("InsertSentinelData end,x id:%s, result:%s, err:%v", key, reStr, result.Err())
	return true, nil
}

func (r *SentinelBiz) SetSentinelData(ctx context.Context, key string, runningNum int32, sentinelConfigId string, maxParallelNum int32, EnableSentinel bool) error {
	routeData := &models.TaskSentinelData{
		Id:               key,
		SentinelConfigId: sentinelConfigId,
		TaskRunningNum:   runningNum,
		// 流控最大并发
		MaxParallelNum: maxParallelNum,
		// 是否启用流控
		EnableSentinel: EnableSentinel,
		CreateTime:     time.Now().Format("2006-01-02 15:04:05"),
		UpdateTime:     time.Now().Format("2006-01-02 15:04:05"),
	}

	data := r.redis.GetClient().Get(ctx, key)
	var oldData *models.TaskSentinelData
	if data.Val() != "" {
		_ = json.Unmarshal([]byte(data.Val()), &oldData)
		if oldData != nil {
			routeData.CreateTime = oldData.CreateTime
		}
	}
	//在redis中插入流控信息
	_, err := r.InsertSentinelData(ctx, key, routeData)
	if err != nil {
		log.Errorf("SetSentinelData failed in InsertSentinelData,key:%v,routeData:%+v, err:%v", key, routeData, err)
	}
	return nil
}

//func (r *SentinelBiz) GetTaskSentinelNumData(ctx context.Context, key string) (int64, error) {
//
//	data := r.redis.GetClient().Get(ctx, key).Result()
//	if data != nil && data.Val() != "" {
//		var taskSentinelNum int64
//		strconv.Atoi(data)
//		return 0, nil
//	} else {
//		log.Infof("GetSentinelData data is nil, key:%s", key)
//		return 0, nil
//	}
//
//}

func (r *SentinelBiz) InsertTaskSentinelNumData(ctx context.Context, key string, taskRunningNum int64) (bool, error) {

	log.Infof("InsertSentinelData start, id:%s, newData:%v", key, taskRunningNum)

	//流控信息保存十分钟
	expireTime := 10 * 60 * time.Second

	result := r.redis.GetClient().Set(context.Background(), key, taskRunningNum, expireTime)
	reStr, err := result.Result()
	if err != nil {
		log.Errorf("InsertTaskSentinelNumData err:%v", err)
		return false, err
	}
	log.Infof("InsertTaskSentinelNumData end,x id:%s, result:%s, err:%v", key, reStr, result.Err())
	return true, nil
}

// 更新redis中流控配置，新建或更新流控配置时调用
func (r *SentinelBiz) UpdateSentinelConfigInRedis(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) error {
	var err error
	//构造流控配置结构
	redisKey := models.GetSentinelKey(taskSentinelConfig.UserId, taskSentinelConfig.PipelineId, int32(taskSentinelConfig.TaskType), taskSentinelConfig.Product, taskSentinelConfig.EngineModel, taskSentinelConfig.Tag)
	sentinelConfigId := models.GetSentinelConfigId(redisKey)
	data := r.redis.GetClient().Get(ctx, redisKey)
	routeData := &models.TaskSentinelData{
		Id:               redisKey,
		SentinelConfigId: sentinelConfigId,
		TaskRunningNum:   0,
		// 流控最大并发
		MaxParallelNum: 0,
		// 是否启用流控
		EnableSentinel: false,
		CreateTime:     time.Now().Format("2006-01-02 15:04:05"),
		UpdateTime:     time.Now().Format("2006-01-02 15:04:05"),
	}
	//如果sentinel配置生效，EnableSentinel置为true
	//否则直接退出
	if taskSentinelConfig.SentinelStatus == models.SentinelStatusStateActive {
		routeData.EnableSentinel = true
	} else {
		log.Infof("UpdateSentinelConfigInRedis and SentinelStatus is not active, taskSentinelConfig:%+v", taskSentinelConfig)
		return nil
	}
	//设置流控配置
	routeData.MaxParallelNum = taskSentinelConfig.SentinelConfig.MaxParallelNum

	var oldData *models.TaskSentinelData
	//更新创建时间和运行任务数
	if data.Val() != "" {
		_ = json.Unmarshal([]byte(data.Val()), &oldData)
		if oldData != nil {
			routeData.CreateTime = oldData.CreateTime
		}
		if oldData.TaskRunningNum >= 0 {
			routeData.TaskRunningNum = oldData.TaskRunningNum
		}
	}
	//更新redis中数据
	_, err = r.InsertSentinelData(ctx, redisKey, routeData)
	if err != nil {
		log.Errorf("UpdateSentinelConfigInRedis failed in InsertSentinelData,key:%v,routeData:%+v, err:%v", redisKey, routeData, err)
	}
	log.Infof("UpdateSentinelConfigInRedis success ,key:%v,routeData:%+v, err:%v", redisKey, routeData, err)

	return nil
}

func (r *SentinelBiz) IncreaseTaskRunningNum(ctx context.Context, task *models.Task) error {
	var err error
	redisKey := models.GetSentinelKey(task.UserId, task.PipelineId, int32(task.TaskType), task.Product, task.EngineModel, task.Tag)
	data := r.redis.GetClient().Get(ctx, redisKey)
	//如果redis中没有数据，创建
	//如果redis中有数据,更新
	if data.Val() == "" {
		err = r.SetSentinelData(ctx, redisKey, 1, "", 0, false)
		return nil
	} else {
		var taskData *models.TaskSentinelData
		err = json.Unmarshal([]byte(data.Val()), &taskData)
		if err != nil {
			log.Errorf("IncreaseTaskRunningNum failed in ModifyTaskRunningNum unmarshal err: %v", err)
			return err
		}
		r.modifyTaskRunningNum(ctx, taskData, true)
		log.Infof("IncreaseTaskRunningNum success key:%+v, taskData: %v ,task: %+v", task, taskData, task)
	}
	log.Infof("IncreaseTaskRunningNum success key:%+v, task: %+v", task, task)
	return nil
}

func (r *SentinelBiz) DecreaseTaskRunningNum(ctx context.Context, task *models.Task) error {
	var err error
	redisKey := models.GetSentinelKey(task.UserId, task.PipelineId, int32(task.TaskType), task.Product, task.EngineModel, task.Tag)
	data := r.redis.GetClient().Get(ctx, redisKey)
	//如果redis中没有数据直接返回
	//如果redis中有数据,更新
	if data.Val() == "" {
		return nil
	} else {
		var taskData *models.TaskSentinelData
		err = json.Unmarshal([]byte(data.Val()), &taskData)
		if err != nil {
			log.Errorf("ModifyTaskRunningNum unmarshal err: %v", err)
			return err
		}
		if taskData.TaskRunningNum > 0 {
			r.modifyTaskRunningNum(ctx, taskData, true)
		} else {
			log.Warnf("decreaseTaskRunningNum taskRunningNum is 0, id:%s", taskData.Id)
		}

	}
	log.Infof("DecreaseTaskRunningNum success task: %+v, key:%s， err: %v", task, redisKey, err)
	return nil
}

func (r *SentinelBiz) modifyTaskRunningNum(ctx context.Context, taskData *models.TaskSentinelData, isIncrement bool) error {
	if taskData == nil {
		return errors.New("taskData is nil")

	}
	var newTaskRunningNum int32
	if isIncrement {
		newTaskRunningNum = taskData.TaskRunningNum + 1
	} else {
		newTaskRunningNum = taskData.TaskRunningNum - 1
	}
	taskData.TaskRunningNum = newTaskRunningNum
	taskData.UpdateTime = time.Now().Format("2006-01-02 15:04:05")

	err := r.SetSentinelData(ctx, taskData.Id, newTaskRunningNum, taskData.SentinelConfigId, taskData.MaxParallelNum, taskData.EnableSentinel)
	if err != nil {
		log.Errorf("ModifyTaskRunningNum failed in SetSentinelData,key:%v,taskData:%+v, err:%v", taskData.Id, taskData, err)
		return err
	}
	log.Infof("ModifyTaskRunningNum success, taskData:%+v,isIncrement:%v", taskData, isIncrement)
	return nil
}

// 检查任务的sentinel流控配置，判断任务是否可以入队
func (t *SentinelBiz) CheckTaskIsEnableEnqueue(ctx context.Context, task *models.Task) (bool, error) {

	key := models.GetSentinelKey(task.UserId, task.PipelineId, int32(task.TaskType), task.Product, task.EngineModel, task.Tag)
	sentinelConfigId := models.GetSentinelConfigId(key)
	//测试用log
	log.Infof("CheckTaskIsEnableEnqueue start,key:%s,sentinelConfigId:%s,taskId:%s", key, sentinelConfigId, task.TaskId)
	taskSentinelData, err := t.GetSentinelData(ctx, key)
	if err != nil {
		log.Errorf("CheckTaskIsEnableEnqueue GetSentinelData key:%s, err:%v", key, err)
		return false, err
	}
	//获取默认流控配置
	var taskRunningNum int32 = 0
	maxParallelNum := default_sentinel_config.MaxParallelNum

	//检查redis中是否有任务数量
	if taskSentinelData != nil {
		//如果redis中有当前运行任务数
		taskRunningNum = taskSentinelData.TaskRunningNum
	}

	//检查redis中是否开启流控配置
	if taskSentinelData != nil && taskSentinelData.EnableSentinel {
		//如果流控开启则获取流控最大并发
		maxParallelNum = taskSentinelData.MaxParallelNum
		//如果流控开启则判断当前运行任务数是否小于流控最大并发
		if maxParallelNum <= taskRunningNum {
			log.Warnf("CheckTaskIsEnableEnqueue and MaxParallelNum <= TaskRunningNum . taskId:%v, key:%v, task:%+v, MaxParallelNum:%d, TaskRunningNum:%d",
				task.TaskId, key, task, maxParallelNum, taskRunningNum)
			return false, nil
		} else {
			log.Infof("CheckTaskIsEnableEnqueue success. taskId:%v, key:%s, task:%+v, MaxParallelNum:%d, TaskRunningNum:%d",
				task.TaskId, key, task, maxParallelNum, taskRunningNum)
			return true, nil
		}
	}
	//如果没有流控配置，则检查检查默认流控是否开启
	if default_sentinel_config_enable {
		//如果流控开启则判断当前运行任务数是否小于流控最大并发
		if maxParallelNum <= taskRunningNum {
			log.Warnf("CheckTaskIsEnableEnqueue and MaxDefaultParallelNum <= TaskRunningNum . taskId:%v, key:%s, task:%+v, MaxDefaultParallelNum:%d, TaskRunningNum:%d",
				task.TaskId, key, task, maxParallelNum, taskRunningNum)
			return false, nil
		}
	}
	log.Infof("CheckTaskIsEnableEnqueue success. taskId:%v, key:%s, task:%+v, MaxParallelNum:%d, TaskRunningNum:%d",
		task.TaskId, key, task, maxParallelNum, taskRunningNum)
	return true, nil
}

func (r *SentinelBiz) sentinelDataAnalyzeScheduler() {
	ticker := time.NewTicker(5 * time.Minute)
	for {
		select {
		case <-ticker.C:
			log.Infof("DataAnalyze start")
			r.sentinelDataAnalyze(models.TaskSentinelKeyPrex)
			log.Infof("DataAnalyze end")
		}
	}

}

// 定时扫描redis中sentinel数据，在日志中打印流控数据
func (t *SentinelBiz) sentinelDataAnalyze(prefix string) {
	if !t.redis.GetClient().SetNX(context.Background(), sentinel_data_analyze, "1", 4*time.Minute).Val() {
		log.Infof("sentinel_data_analyze is running by other process")
		return
	}
	var sentinelEnableDatas []*models.TaskSentinelData
	var sentinelDatas []*models.TaskSentinelData
	keys := t.redis.GetClient().Keys(context.Background(), prefix+"*").Val()
	for _, key := range keys {
		data := t.redis.GetClient().Get(context.Background(), key)
		var sentinelData *models.TaskSentinelData
		if data.Val() != "" {
			_ = json.Unmarshal([]byte(data.Val()), &sentinelData)
			if sentinelData != nil {
				if sentinelData.EnableSentinel == true {
					//如果流控开启则放入sentinelEnableDatas
					sentinelEnableDatas = append(sentinelEnableDatas, sentinelData)
					log.Infof("sentinelDataAnalyze and sentinelEnableData:%+v", sentinelData)
				}
				//放入sentinelDatas
				sentinelDatas = append(sentinelDatas, sentinelData)
				log.Infof("sentinelDataAnalyze and sentinelData:%+v", sentinelData)
			}
		}
	}
	//
	log.Infof("sentinelDataAnalyze sentinelEnableDatasLen:%d, sentinelDatasLen:%d", len(sentinelEnableDatas), len(sentinelDatas))
}
