package tasker

import (
	"context"
	"errors"
	"testing"
	"time"

	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/grayscale"
	"mpp/internal/taskmanager/biz/sentinel"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/qproxy/client"
	"mpp/internal/taskmanager/qproxy/worker"
	"mpp/pkg/types/anyvalue"

	"github.com/bytedance/mockey"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	"github.com/mohae/deepcopy"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

//func TestTimer(t *testing.T) {
//	duration := time.Second * time.Duration(3)
//	timer := time.NewTicker(duration)
//	defer timer.Stop()
//
//	for {
//		select {
//		case <-timer.C:
//			fmt.Println("ttt")
//		}
//	}
//}

var (
	batchTaskTestBiz *BatchTaskBiz
	workerTask       *worker.AsyncTask
	mockTask         = defaultModelTask
)

func init() {
	mockTask.TaskType = models.AsyncTaskType

}

var _ = Describe("Test execute task", func() {
	It("should be return stop", func() {

		mockTask.Dependencies = make([]models.Dependency, 0)

		fieldsMap1 := make(map[string]string)
		fieldsMap1["ip"] = "HostIP"
		fieldsMap1["name"] = "encoder_1"

		mockTask.Dependencies = append(mockTask.Dependencies, models.Dependency{
			TaskId: "001-unit-test",
			Fields: fieldsMap1,
		})

		fieldsMap2 := make(map[string]string)
		fieldsMap2["ip"] = "HostIP"
		fieldsMap2["name"] = "encoder_1"

		mockTask.Dependencies = append(mockTask.Dependencies, models.Dependency{
			TaskId: "001-unit-test",
			Fields: fieldsMap1,
		})
		// 构造异步任务
		mockTask.BizStatus = 1
		mockTask.InternalStatus = 3
		mockTask.TaskType = models.DagAsyncTaskType
		bs, _ := mockTask.EncodeLiteToBytes()

		workerTask = worker.NewAsyncTask("4", "123test", bs)

		batchTaskTestBiz.repo = taskRepoMock
		//taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask, nil).Times(1)

		mockTask2 := deepcopy.Copy(mockTask).(*models.Task)
		mockTask2.Worker = &models.Worker{Id: "host_ip_test_1", Ip: "*********"}
		mockTask2.InternalStatus = 2
		mockTask2.BizStatus = 2
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).AnyTimes()
		//defer mockey.Mock((*CommonTaskBiz).scheduleAndDispatchTask).Return(nil).Build().UnPatch()
		//defer mockey.Mock((*CommonTaskBiz).sendDequeueNotify).Return(nil).Build().UnPatch()
		//defer mockey.Mock((*CommonTaskBiz).sendCompleteNotify).Return(nil).Build().UnPatch()
		err := batchTaskTestBiz.Execute(context.Background(), workerTask)
		Ω(err).ShouldNot(HaveOccurred())
	})

})

var _ = Describe("Test checkTask", func() {

	BeforeEach(func() {
		mockTask.Dependencies = make([]models.Dependency, 0)

		fieldsMap1 := make(map[string]string)
		fieldsMap1["ip"] = "HostIP"
		fieldsMap1["name"] = "encoder_1"

		mockTask.Dependencies = append(mockTask.Dependencies, models.Dependency{
			TaskId: "001-unit-test",
			Fields: fieldsMap1,
		})

		fieldsMap2 := make(map[string]string)
		fieldsMap2["ip"] = "HostIP"
		fieldsMap2["name"] = "encoder_1"

		mockTask.Dependencies = append(mockTask.Dependencies, models.Dependency{
			TaskId: "001-unit-test",
			Fields: fieldsMap1,
		})
	})
	It("dee should be return error with both running status", func() {
		// 构造异步任务
		mockTask.BizStatus = 1
		mockTask.InternalStatus = 3
		mockTask.TaskType = models.DagAsyncTaskType
		bs, _ := mockTask.EncodeLiteToBytes()

		workerTask = worker.NewAsyncTask("4", "123test", bs)

		batchTaskTestBiz.repo = taskRepoMock
		mockTask2 := deepcopy.Copy(mockTask).(*models.Task)
		mockTask2.Worker = &models.Worker{Id: "host_ip_test_1", Ip: "*********"}
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 3
		mockTask2.LastModified = time.Now().Add(-6 * time.Hour)
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).Times(2)
		//taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).Times(1)
		defer mockey.Mock((*CommonTaskBiz).scheduleAndDispatchTask).Return(nil).Build().UnPatch()
		defer mockey.Mock((*CommonTaskBiz).sendDequeueNotify).Return(common.ErrInternalError).Build().UnPatch()
		//defer mockey.Mock((*CommonTaskBiz).sendCompleteNotify).Return(nil).Build().UnPatch()

		_, _, err := batchTaskTestBiz.checkTask(context.Background(), make(map[string]anyvalue.Value), "test-id", 12)
		Ω(err).ShouldNot(BeNil())
	})

})

var _ = Describe("Test SchedulerPendingSubmitTask", func() {

	// 准备 mock 数据
	mockTask2 := &models.Task{
		TaskId:         "test-task-id",
		BizStatus:      models.TaskStatePending,
		InternalStatus: models.TaskStatePending,
		Product:        "product-name",
		Dependencies:   make([]models.Dependency, 0),
		MigrateConfig:  models.GetMigrateConfigByProduct("product-name"),
		Notify:         "mns://test",
	}

	It("should successfully SchedulerPendingSubmitTask a new task when it does not exist yet", func() {
		// 调用方法并验证结果
		defer mockey.Mock((*CommonTaskBiz).scheduleAndDispatchTask).Return(nil).Build().UnPatch()
		defer mockey.Mock((*CommonTaskBiz).sendDequeueNotify).Return(nil).Build().UnPatch()
		_, err := batchTaskTestBiz.SchedulerPendingSubmitTask(context.Background(), mockTask2, "")
		Ω(err).ShouldNot(HaveOccurred())

	})

	//It("should successfully SchedulerPendingSubmitTask sendDequeueNotify return nil", func() {
	//	// 调用方法并验证结果
	//	defer mockey.Mock((*BatchTaskBiz).CheckDependency).Return(true).Build().UnPatch()
	//	defer mockey.Mock((*CommonTaskBiz).scheduleAndDispatchTask).Return(nil).Build().UnPatch()
	//	defer mockey.Mock((*CommonTaskBiz).sendDequeueNotify).Return(common.ErrInternalError).Build().UnPatch()
	//	_, err := batchTaskTestBiz.SchedulerPendingSubmitTask(context.Background(), mockTask2, "")
	//	Ω(err).ShouldNot(HaveOccurred())
	//
	//})

	It("should successfully SchedulerPendingSubmitTask and scheduleAndDispatchTask return nil", func() {
		// 调用方法并验证结果
		defer mockey.Mock((*BatchTaskBiz).CheckDependency).Return(true).Build().UnPatch()
		defer mockey.Mock((*CommonTaskBiz).scheduleAndDispatchTask).Return(&common.EngineError{404, "", ""}).Build().UnPatch()
		defer mockey.Mock((*BatchTaskBiz).CancelTask).Return(nil).Build().UnPatch()
		defer mockey.Mock((*CommonTaskBiz).sendCompleteNotify).Return(common.ErrInternalError).Build().UnPatch()
		_, err := batchTaskTestBiz.SchedulerPendingSubmitTask(context.Background(), mockTask2, "")
		Ω(err).Should(HaveOccurred())

	})

})

var _ = Describe("Test isTaskRealRunning", func() {

	// 准备 mock 数据
	mockTask1 := &models.Task{
		TaskId:         "test-task-id",
		BizStatus:      models.TaskStateActive,
		InternalStatus: models.TaskStateActive,
		Product:        "product-name",
		Dependencies:   make([]models.Dependency, 0),
		MigrateConfig:  models.GetMigrateConfigByProduct("product-name"),
		Worker:         defaultWorker,
	}

	It("should successfull and err is not nil", func() {
		// 调用方法并验证结果

		batchTaskTestBiz.scheduler = schedulerAdapterMock
		batchTaskTestBiz.engine = engineAdapterMock
		schedulerAdapterMock.EXPECT().GetWorker(gomock.Any(), gomock.Any(), gomock.Any()).Return(defaultWorker, nil).Times(1)
		engineAdapterMock.EXPECT().Check(gomock.Any(), gomock.Any(), gomock.Any()).Return(common.ErrTaskNotFoundError).Times(1)
		_, err := batchTaskTestBiz.isTaskRealRunning(context.Background(), make(map[string]anyvalue.Value), mockTask1)
		Ω(err).Should(HaveOccurred())

	})

	It("test isTaskRealRunning  and GetWorker return err ", func() {
		// 调用方法并验证结果

		batchTaskTestBiz.scheduler = schedulerAdapterMock
		batchTaskTestBiz.engine = engineAdapterMock
		schedulerAdapterMock.EXPECT().GetWorker(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, common.ErrInternalError).Times(1)
		_, err := batchTaskTestBiz.isTaskRealRunning(context.Background(), make(map[string]anyvalue.Value), mockTask1)
		Ω(err).Should(HaveOccurred())

	})

})

var _ = Describe("Test Submit Task", func() {

	// 准备 mock 数据
	mockTask = &models.Task{
		TaskId:         "test-task-id",
		BizStatus:      models.TaskStatePending,
		InternalStatus: models.TaskStatePending,
		Product:        "product-name",
		Dependencies:   make([]models.Dependency, 0),
		MigrateConfig:  models.GetMigrateConfigByProduct("product-name"),
		Metadata:       make(map[string]string),
	}

	It("should successfully submit a new task", func() {
		// 调用方法并验证结果
		batchTaskTestBiz.repo = taskRepoMock
		batchTaskTestBiz.CommonTaskBiz.repo = taskRepoMock
		commonTaskTestBiz.repo = taskRepoMock
		taskRepoMock.EXPECT().InsertIgnoreDuplicate(gomock.Any(), gomock.Any()).Return(true, nil).Times(1)
		defer mockey.Mock((*sentinel.SentinelBiz).CheckTaskIsEnableEnqueue).Return(true, nil).Build().UnPatch()
		//AsyncQueueClient
		defer mockey.Mock((*client.AsyncQueueClient).Enqueue).Return(nil).Build().UnPatch()
		defer mockey.Mock((*sentinel.SentinelBiz).IncreaseTaskRunningNum).Return(nil).Build().UnPatch()
		defer mockey.Mock((*grayscale.MixRuleConfigBiz).CheckHitMixRule).Return(true, true, &models.MixConfig{
			Product:     "mpp-integration-test",
			EngineModel: "mpp-integration-test",
		}, nil).Build().UnPatch()
		_, err := batchTaskTestBiz.SubmitTask(context.Background(), mockTask)
		Ω(err).ShouldNot(HaveOccurred())

	})

})

var _ = Describe("Test cancel Task", func() {
	var mockTask2 *models.Task
	BeforeEach(func() {
		mockTask2 = deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask2.BizStatus = models.TaskStateActive
		mockTask2.InternalStatus = models.TaskStatePending
		mockTask2.TaskType = models.AsyncTaskType
		batchTaskTestBiz.repo = taskRepoMock
	})
	//
	It("Test cancel Task and return successfully", func() {
		// 调用方法并验证结果
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).Times(1)
		taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		defer mockey.Mock((*CommonTaskBiz).cancelTask).Return(nil).Build().UnPatch()

		err := batchTaskTestBiz.CancelTask(context.Background(), "test-id")
		Ω(err).ShouldNot(HaveOccurred())

	})
	It("Test cancel Task and FindByID return err", func() {
		// 调用方法并验证结果
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(nil, common.ErrInternalError).Times(1)
		err := batchTaskTestBiz.CancelTask(context.Background(), "test-id")
		Ω(err).Should(HaveOccurred())
	})

	It("Test cancel Task and task already stop", func() {
		// 调用方法并验证结果
		mockTask2.BizStatus = 2
		mockTask2.InternalStatus = 2
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).Times(1)
		err := batchTaskTestBiz.CancelTask(context.Background(), "test-id")
		Ω(err).ShouldNot(HaveOccurred())
	})
	It("Test cancel Task and UpdateBizStatus return err", func() {
		// 调用方法并验证结果
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).Times(1)
		taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(common.ErrInternalError).Times(1)
		err := batchTaskTestBiz.CancelTask(context.Background(), "test-id")
		Ω(err).Should(Equal(common.ErrInternalError))
	})

	It("Test cancel Task and task is internal stop", func() {
		// 调用方法并验证结果
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 2
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).Times(1)
		taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)

		err := batchTaskTestBiz.CancelTask(context.Background(), "test-id")
		Ω(err).ShouldNot(HaveOccurred())

	})

})

var _ = Describe("Test CancelEnqueueTask", func() {
	var mockTask2 *models.Task
	BeforeEach(func() {
		mockTask2 = deepcopy.Copy(mockTask).(*models.Task)
		batchTaskTestBiz.repo = taskRepoMock
		batchTaskTestBiz.sentinelBiz = &sentinel.SentinelBiz{}
		batchTaskTestBiz.asyncQueueClient = &client.AsyncQueueClient{}
	})

	It("should return failed when task is enqueued", func() {
		// 假设任务处于排队状态
		mockTask.InternalStatus = models.TaskStatePending
		defer mockey.Mock((*client.AsyncQueueClient).CancelTask).Return(common.ErrInternalError).Build().UnPatch()
		defer mockey.Mock((*sentinel.SentinelBiz).DecreaseTaskRunningNum).Return(common.ErrInternalError).Build().UnPatch()
		defer mockey.Mock((*CommonTaskBiz).updateInternalStopped).Return(nil, common.ErrInternalError).Build().UnPatch()
		err := batchTaskTestBiz.CancelEnqueueTask(context.Background(), mockTask2)
		Expect(err).ToNot(BeNil())
	})

})

var _ = Describe("Test GetTask", func() {

	BeforeEach(func() {
		batchTaskTestBiz.repo = taskRepoMock
	})
	It("Test GetTask should handle repository error", func() {
		taskId := "some-task-id"
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(nil, common.ErrInternalError).Times(1)
		_, err := batchTaskTestBiz.GetTask(context.Background(), taskId)
		Expect(err).ToNot(BeNil())
	})
})

var _ = Describe("Test GetRunningTasks", func() {

	BeforeEach(func() {
		batchTaskTestBiz.repo = taskRepoMock
	})
	It("Test GetRunningTasks should handle repository error", func() {
		engineModels := []string{"model1", "model2"}
		taskRepoMock.EXPECT().FindRunning(gomock.Any(), gomock.Any()).Return(nil, common.ErrInternalError).Times(1)

		_, err := batchTaskTestBiz.GetRunningTasks(context.Background(), engineModels)
		Expect(err).ToNot(BeNil())
	})
})

var _ = Describe("Test CompleteTask", func() {
	BeforeEach(func() {
		batchTaskTestBiz.asyncQueueClient = &client.AsyncQueueClient{}
		batchTaskTestBiz.repo = taskRepoMock
	})

	It("should handle completion error", func() {
		requestId := &CompleteRequest{TaskID: "task-id"}
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(&models.Task{}, nil).Times(1)
		defer mockey.Mock((*CommonTaskBiz).CompleteTask).Return(true, nil).Build().UnPatch()
		defer mockey.Mock((*client.AsyncQueueClient).CancelTask).Return(common.ErrInternalError).Build().UnPatch()

		err := batchTaskTestBiz.CompleteTask(context.Background(), requestId)
		Expect(err).To(BeNil())
	})
})

func TestTimer2(t *testing.T) {
	var err error
	err = &common.EngineError{
		Code:    400,
		Message: "123",
		Reason:  "321",
	}
	log.Infof("err:%v", err)

	var (
		engineError   *common.EngineError
		scheduleError *common.ScheduleError
	)
	isEngineError := errors.As(err, &engineError)
	isScheduleError := errors.As(err, &engineError)

	if engineError != nil && engineError.Code == 400 {
		log.Infof("engineError:%v", engineError)
	}
	if scheduleError != nil && scheduleError.Code == 400 {
		log.Infof("scheduleError:%v", scheduleError)
	}
	log.Infof("isEngineError:%v,isScheduleError:%v", isEngineError, isScheduleError)

}
