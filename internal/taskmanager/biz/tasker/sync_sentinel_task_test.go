package tasker

import (
	"context"
	"github.com/golang/mock/gomock"
	"github.com/mohae/deepcopy"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/sentinel"
	"mpp/internal/taskmanager/models"
)

/**
 * @Author: qinxin
 * @Date: 2024/9/25 02:46
 * @Desc:
 * @Version 1.0
 */

var taskSentinelSyncBiz *TaskSentinelSync

var _ = Describe("TaskSentinelSync Tests", func() {
	var (
		ctx context.Context

		//mockTempQueryTask      []*models.TempQueryTask
		//mockTaskSentinelConfig []*models.TaskSentinelConfig
	)

	BeforeEach(func() {
		taskSentinelSyncBiz.taskRepo = taskRepoMock
		taskSentinelSyncBiz.sentinelRepo = sentinelRepoMock
		taskSentinelSyncBiz.sentinelBiz = &sentinel.SentinelBiz{}
		ctx = context.Background()

		mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1

		//mockTempQueryTask = []*models.TempQueryTask{
		//	{
		//		UserId:           "test-123",
		//		SentinelConfigId: "test-123",
		//	},
		//}
		//mockTaskSentinelConfig = []*models.TaskSentinelConfig{
		//	{
		//		SentinelConfigId: "test-123",
		//		SentinelStatus:   models.SentinelStatusStateActive,
		//		SentinelConfig:   &models.SentinelConfig{},
		//	},
		//}
	})
	It("test TaskSentinelSync and return nil", func() {

		taskRepoMock.EXPECT().CountAggregatedTasks(gomock.Any()).Return(nil, nil).Times(1)
		sentinelRepoMock.EXPECT().ListActiveTaskSentinelConfig(gomock.Any()).Return(nil, nil).Times(1)

		//defer mockey.Mock((*models.Task).IsRecentlyUpdated).Return(false).Build().UnPatch()

		err := taskSentinelSyncBiz.RedisTaskSentinelDataSync(ctx) //
		Ω(err).ShouldNot(HaveOccurred())

	})
	//todo 待修复
	//It("test TaskSentinelSync and return SetSentinelData return err", func() {
	//
	//	taskRepoMock.EXPECT().CountAggregatedTasks(gomock.Any()).Return(mockTempQueryTask, nil).Times(1)
	//	sentinelRepoMock.EXPECT().ListActiveTaskSentinelConfig(gomock.Any()).Return(mockTaskSentinelConfig, nil).Times(1)
	//
	//	defer mockey.Mock((*sentinel.SentinelBiz).SetSentinelData).Return(common.ErrInternalError).Build().UnPatch()
	//
	//	err := taskSentinelSyncBiz.RedisTaskSentinelDataSync(ctx) //
	//	Ω(err).ShouldNot(HaveOccurred())
	//
	//})

	It("test TaskSentinelSync and CountAggregatedTasks return err", func() {

		taskRepoMock.EXPECT().CountAggregatedTasks(gomock.Any()).Return(nil, common.ErrInternalError).Times(1)
		//sentinelRepoMock.EXPECT().ListActiveTaskSentinelConfig(gomock.Any()).Return(nil, nil).Times(1)

		//defer mockey.Mock((*models.Task).IsRecentlyUpdated).Return(false).Build().UnPatch()

		err := taskSentinelSyncBiz.RedisTaskSentinelDataSync(ctx) //
		Ω(err).ShouldNot(HaveOccurred())

	})

	It("test TaskSentinelSync and ListActiveTaskSentinelConfig return err", func() {

		taskRepoMock.EXPECT().CountAggregatedTasks(gomock.Any()).Return(nil, nil).Times(1)
		sentinelRepoMock.EXPECT().ListActiveTaskSentinelConfig(gomock.Any()).Return(nil, common.ErrInternalError).Times(1)

		//defer mockey.Mock((*models.Task).IsRecentlyUpdated).Return(false).Build().UnPatch()

		err := taskSentinelSyncBiz.RedisTaskSentinelDataSync(ctx) //
		Ω(err).ShouldNot(HaveOccurred())

	})

})
