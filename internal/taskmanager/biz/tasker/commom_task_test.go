package tasker

//http://1426105496228119.mns.cn-shanghai.aliyuncs.com/queues/mpp-notify-test

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"testing"
	"time"

	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/global_param_loader"
	"mpp/internal/taskmanager/biz/livetranscode"
	"mpp/internal/taskmanager/biz/sentinel"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/mocks"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/domain"
	lc "mpp/internal/taskmanager/models/livetranscode"
	"mpp/internal/taskmanager/util"
	"mpp/internal/taskscheduler/biz"
	"mpp/pkg/notify"
	"mpp/pkg/notify/mns"
	"mpp/pkg/tracer/tasker_tracing"

	pb "proto.mpp/api/taskmanager/v1"

	"github.com/bytedance/mockey"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	"github.com/mohae/deepcopy"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/assert"
)

type mockNotifier struct{}

var (
	dagSchedulerParams = `{
  "quotaSet": {
    "cpu": 1
  },
  "parallelNum":3
}`

	dagEngineParams = `{
  "streamSourceBitrate": 2441,
  "streamSourceWidth": 1088,
  "streams": [
    {
      "rtmpDest": "rtmp://3.3.3.3/test/stream-111_sd5?key=value",
      "vfilter": "no_ph_vf[0:v]blendimages=json={}[vfo]",
      "input_bypass": "-threads 2",
      "fps": "1:30",
      "env": "pub",
      "type": "transcode",
      "ffver": "ffv5",
      "liveDictator": "livedictator-l2-cn-beijing-inner.alivecdn.com",
      "sw2main": 3,
      "width": -2,
      "x265_params": "qpmax=45:all-p-refresh=1:max-merge=5:ref=2:refreshframe-rc=1:cutree-motion-beta=1:rc-lookahead=5:frame-threads=2:numtl=1:info=0:bframes=3:ctb3-fix=1:scenecut=0:open-gop=0:aq-mode=4:aq-strength=1.2:keyint=-1:bitrate=1000:vbv-maxrate=1200:vbv-bufsize=1500 -preset faster -tune ssim -vcopybr 200:1:1600 -copyts -optimize_cts -force_key_frames source -enable_hevcdec_opt 1 -enable_avx2_opt 1 -ignore_subsei_ex 1 -qsd_flush -sei_hold_all -force_avsync -sei_order_by_dts -colorinfo_bypass all -global_setopt scalemaxthreads=8",
      "afilter": "",
      "output_bypass": "-cqfactor 25 -annexb 0 -rule adapt_revertv2_540",
      "aCodec": "copy",
      "height": 540,
      "vCodec": "librs265",
      "parallel":"3"
    }
  ],
  "streamNum": 1,
  "streamSource": "rtmp://*******/test/mpp.aliyun.com_stream-111?key=value",
  "streamSourceCodec": "h265",
  "streamSourceFps": 22,
  "streamSourceHeight": 1920,
  "streamTimestampOffset": 0.0,
  "region": "cn-beijing",
  "switchStreams": [],
  "taskId": "mpp.aliyun.com/18ad03fa-b8bc-3592-b572-5715a44fa0e2",
  "streamSourceType": "stream",
  "http_header_host": "livetransworker.alivecdn.com",
  "quota": 375
}`
	commonTaskBiz     *CommonTaskBiz
	commonTaskTestBiz *CommonTaskBiz
)

func init() {
	//commonTaskBiz, _ = NewTaskBiz(nil, taskRepoMock, nil, nil, nil, log.DefaultLogger)
	NewEngineLostResult(&models.Worker{})
	NewEngineParamErrorResult(&models.Worker{})
}

func (m *mockNotifier) Send(_ context.Context, _ string, data []byte) error {
	var req models.AsyncJobNotifyRequest
	err := json.Unmarshal(data, &req)
	if err != nil {
		return err
	}
	return nil
}

type appConf struct {
	Url             string `json:"url"`
	AccessKeyId     string `json:"access_key_id"`
	AccessKeySecret string `json:"access_key_secret"`
}

func mockModelTask() *models.Task {
	task := &models.Task{
		TaskId:         "123test",
		JobId:          "123test",
		Product:        "mps",
		EngineModel:    "test",
		Tag:            "test",
		UserId:         "mpp",
		BizStatus:      models.TaskStateActive,
		InternalStatus: models.TaskStateStopped,
		UserData:       "userData",
		GmtCreate:      time.Now(),
		LastModified:   time.Now(),
		Notify:         "mns://1426105496228119.mns.cn-shanghai.aliyuncs.com/queues/mpp-notify-test,mns://1426105496228119.mns.cn-shanghai.aliyuncs.com/queues/mpp-notify-test?dequeued=inner",
	}

	return task
}

func TestCommonTask_StartTask(t *testing.T) {

	//commonTaskBiz, _ = NewTaskBiz(nil, nil, nil, nil, nil, nil, nil, nil)
	commonTaskBiz = &CommonTaskBiz{log: log.NewHelper(log.DefaultLogger)}
	commonTaskBiz.taskTracer = tasker_tracing.NewTracer()
	ctl = gomock.NewController(t)
	//mock
	defer ctl.Finish()
	cleaner = ctl.Finish
	taskRepoMock2 := mocks.NewMockTaskRepo(ctl)
	commonTaskBiz.repo = taskRepoMock2
	existTask := mockModelTask()
	existTask.Param = &models.TaskEngineParam{}
	existTask.Product = "ols"
	existTask.EngineModel = models.LiveTranscode
	//任务重试流程测试
	mockey.PatchConvey("StartTask, dispatch faild and should retry start ", t, func() {
		task := mockModelTask()
		task.ScheduleParams = dagSchedulerParams
		task.Param = &models.TaskEngineParam{
			Param: dagEngineParams,
		}
		existTask.BizStatus = models.TaskStateStopped
		existTask.InternalStatus = models.TaskStateActive
		//mockey.Mock((*TaskService).StartTask).Return(defaultSuccessResult, nil).Build()
		//mockey.Mock((*impl.TaskRepo).FindByID).Return(nil).Build()
		defer mockey.Mock((*CommonTaskBiz).saveAndUpdateOnDuplicate).Return(existTask, nil).Build().UnPatch()
		defer mockey.Mock((*CommonTaskBiz).allocResourceNotUpdateTask).When(func(ctx context.Context, task *models.Task) bool {
			task.Worker = &models.Worker{
				Ip:   "127.0.0.1",
				Id:   "test_id",
				Port: 8080,
			}
			return true // 返回 true 表示继续执行 mock
		}).Return(nil).Build().UnPatch()
		defer mockey.Mock((*CommonTaskBiz).dispatchTask).Return(common.ErrInternalError).Build().UnPatch()
		defer mockey.Mock((*CommonTaskBiz).freeTaskResource).Return(nil).Build().UnPatch()
		defer mockey.Mock((*CommonTaskBiz).StopTask).Return(nil).Build().UnPatch()
		taskRepoMock2.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//taskRepoMock2.EXPECT().UpdateScheduleWorkerAndInternalStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		err := commonTaskBiz.StartTask(context.Background(), task)
		assert.NotNil(t, err)
		metadata := map[string]bool{
			"forceRestart": true,
		}
		existTask.Metadata = map[string]string{
			"extend": biz.JsonToString(metadata),
		}
		err = commonTaskBiz.StartTask(context.Background(), task)
		assert.NotNil(t, err)
	})
	//任务正常启动流程测试
	mockey.PatchConvey("StartTask and should return success ", t, func() {

		task := mockModelTask()
		task.ScheduleParams = dagSchedulerParams
		task.Param = &models.TaskEngineParam{
			Param: dagEngineParams,
		}
		//mockey.Mock((*TaskService).StartTask).Return(defaultSuccessResult, nil).Build()
		//mockey.Mock((*impl.TaskRepo).FindByID).Return(nil).Build()
		defer mockey.Mock((*CommonTaskBiz).saveAndUpdateOnDuplicate).Return(task, nil).Build().UnPatch()
		defer mockey.Mock((*CommonTaskBiz).allocResourceNotUpdateTask).Return(nil).Build().UnPatch()
		defer mockey.Mock((*CommonTaskBiz).dispatchTask).Return(nil).Build().UnPatch()
		taskRepoMock2.EXPECT().UpdateScheduleWorkerAndInternalStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		err := commonTaskBiz.StartTask(context.Background(), task)
		assert.Nil(t, err)
	})

}

var _ = Describe("Test scheduleAndDispatchTask ", func() {

	//todo 待修复
	//It("should be return nil with both running status", func() {
	//	mockTask.Dependencies = make([]models.Dependency, 0)
	//
	//	fieldsMap1 := make(map[string]string)
	//	fieldsMap1["ip"] = "HostIP"
	//	fieldsMap1["name"] = "encoder_1"
	//
	//	mockTask.Dependencies = append(mockTask.Dependencies, models.Dependency{
	//		TaskId: "001-unit-test",
	//		Fields: fieldsMap1,
	//	})
	//
	//	fieldsMap2 := make(map[string]string)
	//	fieldsMap2["ip"] = "HostIP"
	//	fieldsMap2["name"] = "encoder_1"
	//
	//	mockTask.Dependencies = append(mockTask.Dependencies, models.Dependency{
	//		TaskId: "001-unit-test",
	//		Fields: fieldsMap1,
	//	})
	//	// 构造异步任务
	//	mockTask.BizStatus = 1
	//	mockTask.InternalStatus = 3
	//	mockTask.TaskType = 5
	//
	//	commonTaskTestBiz.repo = taskRepoMock
	//	taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask, nil).Times(1)
	//
	//	mockTask2 := deepcopy.Copy(mockTask).(*models.Task)
	//	mockTask2.Worker = &models.Worker{Id: "host_ip_test_1", Ip: "*********"}
	//	mockTask2.InternalStatus = 1
	//	mockTask2.TaskType = 4
	//	taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).Times(1)
	//	taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).Times(1)
	//	defer mockey.Mock((*CommonTaskBiz).allocResource).Return(nil).Build().UnPatch()
	//	defer mockey.Mock((*CommonTaskBiz).dispatchTask).Return(nil).Build().UnPatch()
	//	defer mockey.Mock((*CommonTaskBiz).CheckAndCompletedDependencyByDB).Return(true, nil).Build().UnPatch()
	//
	//	taskRepoMock.EXPECT().UpdateInternalStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
	//
	//	err := commonTaskTestBiz.scheduleAndDispatchTask(context.Background(), "/test", mockTask, false)
	//	Ω(err).ShouldNot(HaveOccurred())
	//})

	//SendTCCompleteNotify

	var _ = Describe("Test SendTCCompleteNotify", func() {

		It("Test SendTCCompleteNotify and return success", func() {
			// 创建模拟的任务数据
			mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)
			//测试迁移任务返回信息
			mockTask.Result = NewMigrateResult()
			// 配置模拟行为
			//schedulerAdapterMock.EXPECT().AllocResource(gomock.Any(), gomock.Any()).Return(defaultWorker, nil).Times(1)

			defer mockey.Mock(util.HTTPPostErrorOnly).Return(nil).Build().UnPatch()
			//defer mockey.Mock((*leader.LeaderBiz).IsLeader).Return(true).Build().UnPatch()

			// 执行待测功能
			err := commonTaskTestBiz.SendTCCompleteNotify(context.Background(), mockTask)

			// 验证结果（此处主要是日志记录，可以通过日志验证）
			Ω(err).ShouldNot(HaveOccurred())
		})

		It("Test SendTCCompleteNotify and return err", func() {
			// 创建模拟的任务数据
			mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)

			// 配置模拟行为
			//schedulerAdapterMock.EXPECT().AllocResource(gomock.Any(), gomock.Any()).Return(defaultWorker, nil).Times(1)

			defer mockey.Mock(util.HTTPPostErrorOnly).Return(common.ErrInternalError).Build().UnPatch()
			//defer mockey.Mock((*leader.LeaderBiz).IsLeader).Return(true).Build().UnPatch()

			// 执行待测功能
			err := commonTaskTestBiz.SendTCCompleteNotify(context.Background(), mockTask)

			// 验证结果（此处主要是日志记录，可以通过日志验证）
			Ω(err).Should(HaveOccurred())
		})

	})

})

var _ = Describe("Test dispatch ", func() {
	It("mock submit and start dispatch ", func() {

		commonTaskTestBiz.engine = engineAdapterMock
		mockTask2 := deepcopy.Copy(mockTask).(*models.Task)
		mockTask2.InternalStatus = 1
		mockTask2.TaskType = 2
		mockTask2.Notify = "http://test"
		engineAdapterMock.EXPECT().Start(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		err := commonTaskTestBiz.dispatchTask(context.Background(), mockTask2)
		Ω(err).ShouldNot(HaveOccurred())
		mockTask2.TaskType = 3
		engineAdapterMock.EXPECT().Submit(gomock.Any(), gomock.Any(), gomock.Any()).Return(common.ErrInternalError).Times(1)
		err = commonTaskTestBiz.dispatchTask(context.Background(), mockTask2)
		Expect(err).Should(Equal(common.ErrInternalError))

	})

})

var _ = Describe("Test StopTask ", func() {

	BeforeEach(
		func() {
			commonTaskTestBiz.engine = engineAdapterMock
			commonTaskTestBiz.repo = taskRepoMock
			commonTaskTestBiz.scheduler = schedulerAdapterMock
		},
	)
	//todo 待修复
	//It("mock StopTask and success ", func() {
	//
	//	mockTask2 := deepcopy.Copy(mockTask).(*models.Task)
	//	mockTask2.BizStatus = 1
	//	mockTask2.InternalStatus = 1
	//	mockTask2.TaskType = 2
	//	mockTask2.Notify = "http://test"
	//	taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).Times(1)
	//	taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
	//	engineAdapterMock.EXPECT().Stop(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
	//	schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any()).Return(nil).Times(1)
	//	taskRepoMock.EXPECT().UpdateInternalStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
	//	err := commonTaskTestBiz.StopTask(context.Background(), mockTask2.TaskId)
	//	Ω(err).ShouldNot(HaveOccurred())
	//
	//})

	It("mock StopTask and bad case 1 ", func() {

		mockTask2 := deepcopy.Copy(mockTask).(*models.Task)
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		mockTask2.TaskType = 2
		mockTask2.Notify = "http://test"
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(nil, common.ErrInternalError).Times(1)
		//taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		//engineAdapterMock.EXPECT().Stop(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		//schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		//taskRepoMock.EXPECT().UpdateInternalStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		err := commonTaskTestBiz.StopTask(context.Background(), mockTask2.TaskId, "")
		Expect(err).Should(Equal(common.ErrInternalError))

	})

	It("mock StopTask and bad case 2 ", func() {
		mockTask2 := deepcopy.Copy(mockTask).(*models.Task)
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		mockTask2.TaskType = 2
		mockTask2.Notify = "http://test"
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).Times(1)
		taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(common.ErrInternalError).Times(1)
		err := commonTaskTestBiz.StopTask(context.Background(), mockTask2.TaskId, "")
		Expect(err).Should(Equal(common.ErrInternalError))
	})

	It("mock StopTask and bad case 3 ", func() {
		mockTask2 := deepcopy.Copy(mockTask).(*models.Task)
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		mockTask2.TaskType = 2
		mockTask2.Notify = "http://test"
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).Times(1)
		taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		engineAdapterMock.EXPECT().Stop(gomock.Any(), gomock.Any(), gomock.Any()).Return(common.ErrInternalError).Times(1)
		err := commonTaskTestBiz.StopTask(context.Background(), mockTask2.TaskId, "")
		Expect(err).Should(Equal(common.ErrInternalError))
	})

	It("mock StopTask and bad case 4 ", func() {
		mockTask2 := deepcopy.Copy(mockTask).(*models.Task)
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		mockTask2.TaskType = 2
		mockTask2.Notify = "http://test"
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).Times(1)
		taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		engineAdapterMock.EXPECT().Stop(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(common.ErrInternalError).Times(1)
		err := commonTaskTestBiz.StopTask(context.Background(), mockTask2.TaskId, "")
		Expect(err).ShouldNot(BeNil())
	})
	//todo 待修复
	//It("mock StopTask and bad case 5 ", func() {
	//	mockTask2 := deepcopy.Copy(mockTask).(*models.Task)
	//	mockTask2.BizStatus = 1
	//	mockTask2.InternalStatus = 1
	//	mockTask2.TaskType = 2
	//	mockTask2.Notify = "http://test"
	//	taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).Times(1)
	//	taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
	//	engineAdapterMock.EXPECT().Stop(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
	//	schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any()).Return(nil).Times(1)
	//	taskRepoMock.EXPECT().UpdateInternalStatus(gomock.Any(), gomock.Any()).Return(common.ErrInternalError).Times(1)
	//	err := commonTaskTestBiz.StopTask(context.Background(), mockTask2.TaskId)
	//	Ω(err).Should(HaveOccurred())
	//})

})

// UpdateTask
var _ = Describe("Test UpdateTask ", func() {
	BeforeEach(
		func() {
			commonTaskTestBiz.engine = engineAdapterMock
			commonTaskTestBiz.repo = taskRepoMock
			commonTaskTestBiz.scheduler = schedulerAdapterMock
		},
	)

	It("mock success UpdateTask and should return nil ", func() {
		mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		mockTask2.TaskType = 2
		mockTask2.Notify = "http://test"
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).Times(1)
		engineAdapterMock.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		taskRepoMock.EXPECT().UpdateEngineParam(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		err := commonTaskTestBiz.UpdateTask(context.Background(), mockTask2.TaskId, mockTask2.Param.Param, mockTask2.Param.ParamUrl, true)
		Ω(err).ShouldNot(HaveOccurred())

	})

})

// UpdateTaskStatus
var _ = Describe("Test UpdateTaskStatus ", func() {
	It("mock submit and start dispatch ", func() {

		commonTaskTestBiz.engine = engineAdapterMock
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		mockTask2.TaskType = 2
		mockTask2.Notify = "http://test"
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).Times(1)
		taskRepoMock.EXPECT().UpdateInternalAndBizStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		err := commonTaskTestBiz.UpdateTaskStatus(context.Background(), mockTask2.TaskId, 2, 1)
		Ω(err).ShouldNot(HaveOccurred())
	})

})

// GetTask
var _ = Describe("Test GetTask ", func() {
	It("mock GetTask and return err", func() {

		commonTaskTestBiz.engine = engineAdapterMock
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		mockTask2.TaskType = 2
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(nil, common.ErrInternalError).Times(1)
		_, err := commonTaskTestBiz.GetTask(context.Background(), mockTask2.TaskId)
		Expect(err).Should(Equal(common.ErrInternalError))
	})

})

// GetRunningTaskIds
var _ = Describe("Test GetRunningTaskIds ", func() {
	It("mock GetRunningTaskIds and return err", func() {

		commonTaskTestBiz.engine = engineAdapterMock
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		mockTask2.TaskType = 2
		taskRepoMock.EXPECT().ListTasks(gomock.Any(), gomock.Any()).Return(nil, common.ErrInternalError).Times(1)
		_, err := commonTaskTestBiz.GetRunningTaskIds(context.Background(), mockTask2)
		Expect(err).Should(Equal(common.ErrInternalError))
	})

})

// GetRunningTasks
var _ = Describe("Test GetRunningTasks ", func() {
	It("mock GetRunningTaskIds and return err", func() {

		commonTaskTestBiz.engine = engineAdapterMock
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		mockTask2.TaskType = 2
		taskRepoMock.EXPECT().FindRunning(gomock.Any(), gomock.Any()).Return(nil, common.ErrInternalError).Times(1)
		_, err := commonTaskTestBiz.GetRunningTasks(context.Background(), nil)
		Expect(err).Should(Equal(common.ErrInternalError))
	})

})

// GetRunningTasks
var _ = Describe("Test GetRunningSimpleTasks ", func() {
	It("mock GetRunningSimpleTasks and return err", func() {

		commonTaskTestBiz.engine = engineAdapterMock
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		mockTask2.TaskType = 2
		taskRepoMock.EXPECT().ListTasksOptimized(gomock.Any(), gomock.Any()).Return(nil, common.ErrInternalError).Times(1)
		_, err := commonTaskTestBiz.GetRunningSimpleTasks(context.Background(), nil)
		Expect(err).Should(Equal(common.ErrInternalError))
	})

})

// GetRunningTasks
var _ = Describe("Test GetRunningSimpleTasks ", func() {
	It("mock GetRunningSimpleTasks and return tasks", func() {

		commonTaskTestBiz.engine = engineAdapterMock
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		mockTask2.TaskType = 2
		taskRepoMock.EXPECT().ListTasksOptimized(gomock.Any(), gomock.Any()).Return([]*models.Task{mockTask2}, nil).Times(1)
		tasks, err := commonTaskTestBiz.GetRunningSimpleTasks(context.Background(), nil)
		Expect(err).Should(BeNil())
		Expect(mockTask2.TaskId).Should(Equal(tasks[0].TaskId))
	})

})

// CompleteTask
var _ = Describe("Test CompleteTask ", func() {

	It("abnormal result complete and task migrate reach max time ", func() {
		commonTaskTestBiz.engine = engineAdapterMock
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		mockTask2.TaskType = 3
		mockTask2.MigrateConfig = &pb.MigrateConfig{
			MaxRuntime: 1,
			UnMigrate:  false,
		}
		mockTask2.MigrateTimes = 1
		in := &CompleteRequest{
			JobID:    mockTask2.JobId,
			TaskID:   mockTask2.TaskId,
			Code:     200,
			Reason:   models.EngineRunShellErrorCode,
			Message:  "run shell command fail due to exit status. err;exit status 1",
			Data:     nil,
			Extends:  nil,
			Duration: 1000,
			DataUrl:  "",
		}

		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).Times(1)
		schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		defer mockey.Mock((*mns.NotifyMNS).Send).Return("", nil).Build().UnPatch()
		//defer mockey.Mock((*CommonTaskBiz).MigrateTask).Return(nil, nil).Build().UnPatch()
		//defer mockey.Mock((*util).HTTPPostErrorOnly).Return("", common.ErrInternalError).Build().UnPatch()
		//mns.Send
		defer mockey.Mock((*sentinel.SentinelBiz).DecreaseTaskRunningNum).Return(nil).Build().UnPatch()

		taskRepoMock.EXPECT().UpdateResultAndAllStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		_, err := commonTaskTestBiz.CompleteTask(context.Background(), in)
		Expect(err).ShouldNot(HaveOccurred())
	})

	It("mock CompleteTask and return nl", func() {

		commonTaskTestBiz.engine = engineAdapterMock
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		mockTask2.TaskType = 2
		in := &CompleteRequest{
			JobID:    mockTask2.JobId,
			TaskID:   mockTask2.TaskId,
			Data:     nil,
			Extends:  nil,
			Duration: 1000,
			DataUrl:  "",
		}

		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(mockTask2, nil).Times(1)
		schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		defer mockey.Mock((*mns.NotifyMNS).Send).Return("", nil).Build().UnPatch()
		//defer mockey.Mock((*util).HTTPPostErrorOnly).Return("", common.ErrInternalError).Build().UnPatch()
		//mns.Send

		taskRepoMock.EXPECT().UpdateResultAndAllStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		_, err := commonTaskTestBiz.CompleteTask(context.Background(), in)
		Expect(err).ShouldNot(HaveOccurred())
	})

})

var _ = Describe("Test MigrateTask", func() {
	It("should migrate tasks and BatchUpdateAbnormalStopped retun nil", func() {
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		// 创建模拟的任务列表
		mockTasks := []*models.Task{
			{
				TaskId:         "task1",
				BizStatus:      models.TaskStateActive,
				InternalStatus: models.TaskStateActive,
			},
			{
				TaskId:         "task2",
				BizStatus:      models.TaskStateActive,
				InternalStatus: models.TaskStateActive,
			},
		}

		// 构造输入参数
		taskIds := []string{"task1", "task2"}
		tryStop := true

		// 配置模拟行为
		taskRepoMock.EXPECT().FindByIDList(gomock.Any(), taskIds).Return(mockTasks, nil).Times(1)
		taskRepoMock.EXPECT().BatchUpdateAbnormalStopped(gomock.Any(), taskIds, gomock.Any()).Return(common.ErrInternalError).Times(1)

		// 执行待测功能
		_, err := commonTaskTestBiz.MigrateTask(context.Background(), taskIds, tryStop)
		// 验证结果
		Expect(err).Should(Equal(common.ErrInternalError))
	})

	It("should handle migration and FindByIDList return err", func() {
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		// 构造输入参数
		taskIds := []string{"task1", "task2"}
		tryStop := false

		// 配置模拟行为
		taskRepoMock.EXPECT().FindByIDList(gomock.Any(), taskIds).Return(nil, common.ErrInternalError).Times(1)
		//taskRepoMock.EXPECT().BatchUpdateAbnormalStopped(gomock.Any(), taskIds, gomock.Any()).Return(nil).Times(1)
		//defer mockey.Mock((*CommonTaskBiz).processAbnormalResult).Return().Build().UnPatch()

		// 执行待测功能
		_, err := commonTaskTestBiz.MigrateTask(context.Background(), taskIds, tryStop)

		// 验证结果
		Expect(err).Should(Equal(common.ErrInternalError))
	})

})

var _ = Describe("Test MigrateTaskByWorker", func() {
	It("should migrate tasks by worker ID successfully", func() {
		// 初始化测试环境
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		// 创建模拟的任务列表
		mockTasks1 := []*models.Task{
			{TaskId: "task1"},
			{TaskId: "task2"},
		}

		mockTasks2 := []*models.Task{
			{TaskId: "task3"},
			{TaskId: "task4"},
		}

		// 构造输入参数
		workerIds := []string{"worker1", "worker2"}
		tryStop := true

		// 配置模拟行为
		taskRepoMock.EXPECT().ListRunningTaskByWorkerId(gomock.Any(), "worker1").Return(mockTasks1, nil).Times(1)
		taskRepoMock.EXPECT().ListRunningTaskByWorkerId(gomock.Any(), "worker2").Return(mockTasks2, nil).Times(1)
		defer mockey.Mock((*CommonTaskBiz).MigrateTask).Return(nil, nil).Build().UnPatch()

		// 执行待测功能
		_, err := commonTaskTestBiz.MigrateTaskByWorker(context.Background(), workerIds, nil, tryStop)

		// 验证结果
		//expectedTaskIds := []string{"task1", "task2", "task3", "task4"}
		Expect(err).Should(BeNil())
		//Expect(taskIds).Should(Equal(expectedTaskIds))
	})

	It("should handle empty worker IDs", func() {
		// 初始化测试环境
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		// 构造输入参数（无效的情况）
		workerIds := []string{}
		tryStop := true

		// 配置模拟行为（无需配置，因为参数无效）

		// 执行待测功能
		_, err := commonTaskTestBiz.MigrateTaskByWorker(context.Background(), workerIds, nil, tryStop)

		// 验证结果（应该返回错误）
		Expect(err).Should(HaveOccurred())
	})
	//todo 待修复
	//It("should migrate tasks by IP successfully", func() {
	//	// 初始化测试环境
	//	commonTaskTestBiz.repo = taskRepoMock
	//	commonTaskTestBiz.scheduler = schedulerAdapterMock
	//
	//	// 创建模拟的任务列表
	//	mockTasks1 := []*models.Task{
	//		{TaskId: "task1"},
	//		{TaskId: "task2"},
	//	}
	//
	//	mockTasks2 := []*models.Task{
	//		{TaskId: "task3"},
	//		{TaskId: "task4"},
	//	}
	//
	//	// 构造输入参数
	//	workIps := []string{"***********", "***********"}
	//	tryStop := true
	//
	//	// 配置模拟行为
	//	taskRepoMock.EXPECT().ListRunningTaskByWorkerIp(gomock.Any(), "***********").Return(mockTasks1, nil).Times(1)
	//	taskRepoMock.EXPECT().ListRunningTaskByWorkerIp(gomock.Any(), "***********").Return(mockTasks2, nil).Times(1)
	//	defer mockey.Mock((*CommonTaskBiz).MigrateTask).Return(nil).Build().UnPatch()
	//
	//	// 执行待测功能
	//	taskIds, err := commonTaskTestBiz.MigrateTaskByWorker(context.Background(), nil, workIps, tryStop)
	//
	//	// 验证结果
	//	expectedTaskIds := []string{"task1", "task2", "task3", "task4"}
	//	Expect(err).Should(BeNil())
	//	Expect(taskIds).Should(Equal(expectedTaskIds))
	//})

	It("should handle empty IPs", func() {
		// 初始化测试环境
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		// 构造输入参数（无效的情况）
		workIps := []string{}
		tryStop := true

		// 配置模拟行为（无需配置，因为参数无效）

		// 执行待测功能
		_, err := commonTaskTestBiz.MigrateTaskByWorker(context.Background(), nil, workIps, tryStop)

		// 验证结果（应该返回错误）
		Expect(err).Should(HaveOccurred())
	})

	It("should migrate taskIds is nil ", func() {
		// 初始化测试环境
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		tryStop := true
		// 配置模拟行为
		//taskRepoMock.EXPECT().ListRunningTaskByWorkerIp(gomock.Any(), "***********").Return(mockTasks1, nil).Times(1)
		//defer mockey.Mock((*CommonTaskBiz).MigrateTask).Return(nil).Build().UnPatch()

		// 执行待测功能
		taskIds, err := commonTaskTestBiz.MigrateTaskByWorker(context.Background(), nil, nil, tryStop)

		// 验证结果
		Expect(err).Should(HaveOccurred())
		Expect(taskIds).Should(BeNil())

	})
})

var _ = Describe("Test processAbnormalResult", func() {
	var ctrl *gomock.Controller

	BeforeEach(func() {
		ctrl = gomock.NewController(GinkgoT())
	})

	AfterEach(func() {
		ctrl.Finish()
	})

	It("should handle successful processing but ClearResultAndAddMigrateTimes return nil", func() {
		commonTaskTestBiz.engine = engineAdapterMock
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		// 创建模拟的任务数据
		mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask.BizStatus = models.TaskStateActive
		mockTask.InternalStatus = models.TaskStateActive
		mockTask.TaskType = models.StreamTaskType

		// 构造输入参数
		source := "source-test"
		forceSchedule := true
		tryStop := true

		// 配置模拟行为
		engineAdapterMock.EXPECT().Stop(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		//taskRepoMock.EXPECT().ClearResultAndAddMigrateTimes(gomock.Any(), gomock.Any()).Return(common.ErrInternalError).Times(1)
		taskRepoMock.EXPECT().IncreaseMigrateTimesAndClearResult(gomock.Any(), gomock.Any()).Return(nil).Times(1)

		defer mockey.Mock((*CommonTaskBiz).scheduleAndDispatchTask).Return(nil).Build().UnPatch()

		// 执行待测功能
		commonTaskTestBiz.processAbnormalResult(context.Background(), source, mockTask, forceSchedule, tryStop)

		// 验证结果（此处主要是日志记录，可以通过日志验证）
		Expect(true).To(BeTrue())

	})

	It("should handle stopping task failure and freeing resources failure", func() {
		commonTaskTestBiz.engine = engineAdapterMock
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		// 创建模拟的任务数据
		mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask.BizStatus = models.TaskStateActive
		mockTask.InternalStatus = models.TaskStateActive
		mockTask.TaskType = models.StreamTaskType

		// 构造输入参数
		source := "source-test"
		forceSchedule := true
		tryStop := true

		// 配置模拟行为
		engineAdapterMock.EXPECT().Stop(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New(500, "StoppingFailed", "failed to stop")).Times(1)
		schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(common.ErrInternalError).Times(1)

		// 执行待测功能
		commonTaskTestBiz.processAbnormalResult(context.Background(), source, mockTask, forceSchedule, tryStop)

		// 验证结果（此处主要是日志记录，可以通过日志验证）
	})

	It("should handle non-running task", func() {
		commonTaskTestBiz.engine = engineAdapterMock
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		// 创建模拟的任务数据
		mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask.BizStatus = models.TaskStateStopped
		mockTask.TaskType = models.StreamTaskType

		// 构造输入参数
		source := "source-test"
		forceSchedule := true
		tryStop := false

		// 配置模拟行为（无需配置，因为任务已停止）
		schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)

		// 执行待测功能
		commonTaskTestBiz.processAbnormalResult(context.Background(), source, mockTask, forceSchedule, tryStop)

		// 验证结果（此处主要是日志记录，可以通过日志验证）
	})

	It("scheduleAndDispatchTask return failure", func() {
		commonTaskTestBiz.engine = engineAdapterMock
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		// 创建模拟的任务数据
		mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask.BizStatus = models.TaskStateActive
		mockTask.InternalStatus = models.TaskStateActive
		mockTask.TaskType = models.StreamTaskType

		// 构造输入参数
		source := "source-test"
		forceSchedule := true
		tryStop := true

		// 配置模拟行为
		engineAdapterMock.EXPECT().Stop(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		defer mockey.Mock((*CommonTaskBiz).scheduleAndDispatchTask).Return(common.ErrInternalError).Build().UnPatch()

		// 执行待测功能
		commonTaskTestBiz.processAbnormalResult(context.Background(), source, mockTask, forceSchedule, tryStop)

		// 验证结果（此处主要是日志记录，可以通过日志验证）
	})

	It("should handle updating result failure", func() {
		commonTaskTestBiz.engine = engineAdapterMock
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock

		// 创建模拟的任务数据
		mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask.BizStatus = models.TaskStateActive
		mockTask.InternalStatus = models.TaskStateActive
		mockTask.TaskType = models.StreamTaskType

		// 构造输入参数
		source := "source-test"
		forceSchedule := true
		tryStop := true

		// 配置模拟行为
		engineAdapterMock.EXPECT().Stop(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		taskRepoMock.EXPECT().IncreaseMigrateTimesAndClearResult(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		//taskRepoMock.EXPECT().ClearResultAndAddMigrateTimes(gomock.Any(), gomock.Any()).Return(common.ErrInternalError).Times(1)
		defer mockey.Mock((*CommonTaskBiz).scheduleAndDispatchTask).Return(nil).Build().UnPatch()

		// 执行待测功能
		commonTaskTestBiz.processAbnormalResult(context.Background(), source, mockTask, forceSchedule, tryStop)

		// 验证结果（此处主要是日志记录，可以通过日志验证）
	})

	//It("should handle max migrations reached", func() {
	//	commonTaskTestBiz.engine = engineAdapterMock
	//	commonTaskTestBiz.repo = taskRepoMock
	//	commonTaskTestBiz.scheduler = schedulerAdapterMock
	//
	//	// 创建模拟的任务数据
	//	mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)
	//	mockTask.BizStatus = models.TaskStateActive
	//	mockTask.InternalStatus = models.TaskStateActive
	//	mockTask.TaskType = models.StreamTaskType
	//
	//	mockTask.MigrateConfig = &pb.MigrateConfig{MaxRuntime: 20, UnMigrate: false}
	//	mockTask.MigrateTimes = 200
	//
	//	// 构造输入参数
	//	source := "source-test"
	//	forceSchedule := true
	//	tryStop := false
	//
	//	// 配置模拟行为
	//	engineAdapterMock.EXPECT().Stop(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
	//	schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any()).Return(nil).Times(2)
	//	taskRepoMock.EXPECT().UpdateInternalStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
	//	taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(common.ErrInternalError).Times(1)
	//	defer mockey.Mock((*CommonTaskBiz).scheduleAndDispatchTask).Return(nil).Build().UnPatch()
	//	defer mockey.Mock((*CommonTaskBiz).sendCompleteNotify).Return(nil).Build().UnPatch()
	//
	//	// 执行待测功能
	//	commonTaskTestBiz.processAbnormalResult(context.Background(), source, mockTask, forceSchedule, tryStop)
	//
	//	// 验证结果（此处主要是日志记录，可以通过日志验证）
	//})
})

var _ = Describe("Test allocResource", func() {
	BeforeEach(func() {
		commonTaskTestBiz.engine = engineAdapterMock
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock
	})
	It("Test allocResource and return success", func() {
		// 创建模拟的任务数据
		mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask.BizStatus = models.TaskStateActive
		mockTask.InternalStatus = models.TaskStateActive

		// 配置模拟行为
		schedulerAdapterMock.EXPECT().AllocResource(gomock.Any(), gomock.Any()).Return(defaultWorker, nil).Times(1)
		taskRepoMock.EXPECT().UpdateScheduleWorker(gomock.Any(), gomock.Any()).Return(nil).Times(1)

		// 执行待测功能
		err := commonTaskTestBiz.allocResource(context.Background(), mockTask)

		// 验证结果（此处主要是日志记录，可以通过日志验证）
		Ω(err).ShouldNot(HaveOccurred())
	})
	It("Test allocResource and return RESOURCE_LOST error", func() {
		// 模拟资源丢失错误
		schedulerAdapterMock.EXPECT().AllocResource(gomock.Any(), gomock.Any()).Return(nil, biz.ErrResourceLost).Times(1)
		schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)

		// 执行待测功能
		err := commonTaskTestBiz.allocResource(context.Background(), mockTask)

		// 验证结果
		Ω(err).Should(HaveOccurred())
	})

	It("Test allocResource and return other errors from scheduler", func() {
		// 创建模拟的任务数据
		mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask.BizStatus = models.TaskStateActive
		mockTask.InternalStatus = models.TaskStateActive

		// 模拟其他类型的错误
		schedulerAdapterMock.EXPECT().AllocResource(gomock.Any(), gomock.Any()).Return(nil, common.ErrInternalError).Times(1)

		// 执行待测功能
		err := commonTaskTestBiz.allocResource(context.Background(), mockTask)

		// 验证结果
		Ω(err).Should(HaveOccurred())
	})

	It("Test allocResource and return error when updating schedule worker", func() {
		// 创建模拟的任务数据
		mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask.BizStatus = models.TaskStateActive
		mockTask.InternalStatus = models.TaskStateActive
		// 模拟成功分配但更新时出错
		schedulerAdapterMock.EXPECT().AllocResource(gomock.Any(), gomock.Any()).Return(defaultWorker, nil).Times(1)
		taskRepoMock.EXPECT().UpdateScheduleWorker(gomock.Any(), gomock.Any()).Return(common.ErrInternalError).Times(1)

		// 执行待测功能
		err := commonTaskTestBiz.allocResource(context.Background(), mockTask)

		// 验证结果
		Ω(err).Should(HaveOccurred())
	})
})

//allocResourceNotUpdateTask

var _ = Describe("Test allocResourceNotUpdateTask", func() {
	BeforeEach(func() {
		commonTaskTestBiz.engine = engineAdapterMock
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock
	})
	It("Test allocResourceNotUpdateTask and return success", func() {
		// 创建模拟的任务数据
		mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask.BizStatus = models.TaskStateActive
		mockTask.InternalStatus = models.TaskStateActive

		// 配置模拟行为
		schedulerAdapterMock.EXPECT().AllocResource(gomock.Any(), gomock.Any()).Return(defaultWorker, nil).Times(1)

		// 执行待测功能
		err := commonTaskTestBiz.allocResourceNotUpdateTask(context.Background(), mockTask)

		// 验证结果（此处主要是日志记录，可以通过日志验证）
		Ω(err).ShouldNot(HaveOccurred())
	})
	It("Test allocResourceNotUpdateTask and return RESOURCE_LOST error", func() {
		// 模拟资源丢失错误
		schedulerAdapterMock.EXPECT().AllocResource(gomock.Any(), gomock.Any()).Return(nil, biz.ErrResourceLost).Times(1)
		schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)

		// 执行待测功能
		err := commonTaskTestBiz.allocResourceNotUpdateTask(context.Background(), mockTask)

		// 验证结果
		Ω(err).Should(HaveOccurred())
	})

	It("Test allocResourceNotUpdateTask and return other errors from scheduler", func() {
		// 创建模拟的任务数据
		mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask.BizStatus = models.TaskStateActive
		mockTask.InternalStatus = models.TaskStateActive

		// 模拟其他类型的错误
		schedulerAdapterMock.EXPECT().AllocResource(gomock.Any(), gomock.Any()).Return(nil, common.ErrInternalError).Times(1)

		// 执行待测功能
		err := commonTaskTestBiz.allocResourceNotUpdateTask(context.Background(), mockTask)

		// 验证结果
		Ω(err).Should(HaveOccurred())
	})

})

var _ = Describe("Test Common Start task", func() {
	task := deepcopy.Copy(defaultModelTask).(*models.Task)
	//todo
	//It("should be err with first db operate", func() {
	//	err := commonTaskTestBiz.StartTask(context.Background(), &models.Task{TaskId: "aa"})
	//	Expect(err.Error()).Should(ContainSubstring("no such table"))
	//})

	//It(" Start task and should be return nil ", func() {
	//
	//	//Mock(recordHlsTaskBiz.repo.FindByID).Return(task, nil).Build() // 无效
	//	//patches := gomonkey.ApplyPrivateMethod(reflect.TypeOf(recordHlsTaskBiz.repo), "FindByID", func(_ *impl.taskRepo, _ context.Context, _ string) (*models.Task, error) {
	//	//	return task, nil
	//	//})
	//	//defer patches.Reset()   // 无效
	//
	//	//m.EXPECT().CloneWithSpecifiedTable(gomock.Any()).Return(m).Times(1)
	//	commonTaskTestBiz.repo = taskRepoMock
	//	taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(task, nil).Times(1)
	//	err := commonTaskTestBiz.StartTask(context.Background(), task)
	//	Ω(err).ShouldNot(HaveOccurred())
	//})
	//todo 待修复
	//It("test add affinity success", func() {
	//	commonTaskTestBiz.repo = taskRepoMock
	//	taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(&models.Task{BizStatus: 1, Worker: &models.Worker{Id: "workerId"}}, nil).Times(1)
	//
	//	mock1 := mockey.Mock((*CommonTaskBiz).allocResourceNotUpdateTask).Return(&common.ScheduleError{Code: 1, Reason: common.ErrorScheduleAllocResourceFailed, Message: "AllocResourceErr"}).Build()
	//	defer mock1.UnPatch()
	//	taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
	//	err := commonTaskTestBiz.StartTask(context.Background(), task)
	//	Expect(err.Error()).Should(Equal("Code: 1, Reason: ScheduleError.AllocResourceErr, Message: AllocResourceErr"))
	//	Expect(task.RequestResource.Affinity.Preferred[0].Key).Should(Equal("workerId"))
	//})

	It("test alloc resource error", func() {

		commonTaskTestBiz.repo = taskRepoMock
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(&models.Task{BizStatus: 0}, nil).Times(1)
		taskRepoMock.EXPECT().InsertUpdateOnDuplicate(gomock.Any(), gomock.Any()).Return(nil).Times(1)

		mock1 := mockey.Mock((*CommonTaskBiz).allocResourceNotUpdateTask).Return(&common.ScheduleError{Code: 1, Reason: common.ErrorScheduleAllocResourceFailed, Message: "AllocResourceErr"}).Build()
		defer mock1.UnPatch()
		taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		err := commonTaskTestBiz.StartTask(context.Background(), task)
		Expect(err.Error()).Should(Equal("All attempts fail:\n#1: Code: 1, Reason: ScheduleError.AllocResourceErr, Message: AllocResourceErr\n#2: Code: 1, Reason: ScheduleError.AllocResourceErr, Message: AllocResourceErr"))
	})

	It("test dispatchTask error", func() {
		commonTaskTestBiz.repo = taskRepoMock
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(&models.Task{BizStatus: 0}, nil).Times(1)
		taskRepoMock.EXPECT().InsertUpdateOnDuplicate(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		taskRepoMock.EXPECT().UpdateScheduleWorkerAndInternalStatus(gomock.Any(), gomock.Any()).Return(nil).Times(0)

		mock1 := mockey.Mock((*CommonTaskBiz).allocResourceNotUpdateTask).Return(nil).Build()
		defer mock1.UnPatch()
		taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		mock2 := mockey.Mock((*CommonTaskBiz).dispatchTask).Return(&common.EngineError{Code: 2, Reason: common.ErrorEngineHttpFailed, Message: "HttpError"}).Build()
		defer mock2.UnPatch()
		mock3 := mockey.Mock((*CommonTaskBiz).freeTaskResource).Return(nil).Build()
		defer mock3.UnPatch()
		err := commonTaskTestBiz.StartTask(context.Background(), task)
		Expect(err.Error()).Should(Equal("All attempts fail:\n#1: Code: 2, Reason: EngineError.HttpError, Message: HttpError\n#2: Code: 2, Reason: EngineError.HttpError, Message: HttpError"))
	})

	It("test StartTask return ok", func() {
		commonTaskTestBiz.repo = taskRepoMock
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(&models.Task{BizStatus: 0}, nil).Times(1)
		taskRepoMock.EXPECT().InsertUpdateOnDuplicate(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(nil).Times(0)
		taskRepoMock.EXPECT().UpdateScheduleWorkerAndInternalStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)

		mock1 := mockey.Mock((*CommonTaskBiz).allocResourceNotUpdateTask).Return(nil).Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*CommonTaskBiz).dispatchTask).Return(nil).Build()
		defer mock2.UnPatch()
		mock3 := mockey.Mock((*CommonTaskBiz).freeTaskResource).Return(nil).Build()
		defer mock3.UnPatch()
		err := commonTaskTestBiz.StartTask(context.Background(), task)
		Ω(err).ShouldNot(HaveOccurred())
	})

})

//sendDequeueNotify

var _ = Describe("Test sendDequeueNotify", func() {

	It("Test sendDequeueNotify and return success", func() {
		// 创建模拟的任务数据
		mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)

		// 配置模拟行为
		//schedulerAdapterMock.EXPECT().AllocResource(gomock.Any(), gomock.Any()).Return(defaultWorker, nil).Times(1)
		notifyer := &mns.NotifyMNS{}
		defer mockey.Mock((*mns.NotifyMNS).Send).Return("", nil).Build().UnPatch()
		defer mockey.Mock(notify.GetNotifyProvider).Return(notifyer, nil).Build().UnPatch()

		// 执行待测功能
		err := commonTaskTestBiz.sendDequeueNotify(context.Background(), mockTask)

		// 验证结果（此处主要是日志记录，可以通过日志验证）
		Ω(err).ShouldNot(HaveOccurred())
	})

	It("Test sendDequeueNotify with empty notify URL", func() {
		mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask.Notify = "" // 设置为空的通知URL

		notifyer := &mns.NotifyMNS{}
		defer mockey.Mock(notify.GetNotifyProvider).Return(notifyer, nil).Build().UnPatch()

		err := commonTaskTestBiz.sendDequeueNotify(context.Background(), mockTask)
		Ω(err).ShouldNot(HaveOccurred())
		// 可以通过日志验证或其他方式确认没有发送通知
	})

	It("Test sendDequeueNotify and fail to get notify provider", func() {
		mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)

		defer mockey.Mock(notify.GetNotifyProvider).Return(nil, common.ErrInternalError).Build().UnPatch()

		err := commonTaskTestBiz.sendDequeueNotify(context.Background(), mockTask)
		Ω(err).Should(HaveOccurred())
	})

	It("Test sendDequeueNotify and fail to send notification", func() {
		mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)

		notifyer := &mns.NotifyMNS{}
		defer mockey.Mock((*mns.NotifyMNS).Send).Return("", common.ErrInternalError).Build().UnPatch()
		defer mockey.Mock(notify.GetNotifyProvider).Return(notifyer, nil).Build().UnPatch()

		err := commonTaskTestBiz.sendDequeueNotify(context.Background(), mockTask)
		Ω(err).Should(HaveOccurred())

	})

	It("Test sendDequeueNotify and fail to close notifier", func() {
		mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)

		notifyer := &mns.NotifyMNS{}
		defer mockey.Mock((*mns.NotifyMNS).Send).Return("", nil).Build().UnPatch()
		defer mockey.Mock(notify.GetNotifyProvider).Return(notifyer, nil).Build().UnPatch()

		notifyerCloseError := common.ErrInternalError
		defer mockey.Mock((*mns.NotifyMNS).Close).Return(notifyerCloseError).Build().UnPatch()

		err := commonTaskTestBiz.sendDequeueNotify(context.Background(), mockTask)
		Ω(err).ShouldNot(HaveOccurred())
		// 预期应该会有记录说明关闭 notifier 时出错，具体可通过日志验证
	})

})

//SendTCCompleteNotify

var _ = Describe("Test SendTCCompleteNotify", func() {

	It("Test SendTCCompleteNotify and return success", func() {
		// 创建模拟的任务数据
		mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)

		// 配置模拟行为
		//schedulerAdapterMock.EXPECT().AllocResource(gomock.Any(), gomock.Any()).Return(defaultWorker, nil).Times(1)

		defer mockey.Mock(util.HTTPPostErrorOnly).Return(nil).Build().UnPatch()
		//defer mockey.Mock((*leader.LeaderBiz).IsLeader).Return(true).Build().UnPatch()

		// 执行待测功能
		err := commonTaskTestBiz.SendTCCompleteNotify(context.Background(), mockTask)

		// 验证结果（此处主要是日志记录，可以通过日志验证）
		Ω(err).ShouldNot(HaveOccurred())
	})

	It("Test SendTCCompleteNotify and return err", func() {
		// 创建模拟的任务数据
		mockTask := deepcopy.Copy(defaultModelTask).(*models.Task)

		// 配置模拟行为
		//schedulerAdapterMock.EXPECT().AllocResource(gomock.Any(), gomock.Any()).Return(defaultWorker, nil).Times(1)

		defer mockey.Mock(util.HTTPPostErrorOnly).Return(common.ErrInternalError).Build().UnPatch()
		//defer mockey.Mock((*leader.LeaderBiz).IsLeader).Return(true).Build().UnPatch()

		// 执行待测功能
		err := commonTaskTestBiz.SendTCCompleteNotify(context.Background(), mockTask)

		// 验证结果（此处主要是日志记录，可以通过日志验证）
		Ω(err).Should(HaveOccurred())
	})

})

var _ = Describe("Test cancelTask", func() {
	var (
		ctx  context.Context
		task *models.Task
	)

	BeforeEach(func() {
		ctx = context.Background()
		task = deepcopy.Copy(defaultModelTask).(*models.Task)

		// Initialize CommonTaskBiz with mocked dependencies
		commonTaskTestBiz.engine = engineAdapterMock
		commonTaskTestBiz.repo = taskRepoMock
		commonTaskTestBiz.scheduler = schedulerAdapterMock
	})

	It("should cancel task successfully", func() {
		// Mocking Cancel method to return no error
		engineAdapterMock.EXPECT().Cancel(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		// Mocking freeTaskResource to return no error
		schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		// Mocking updateInternalStopped to return no error
		taskRepoMock.EXPECT().UpdateInternalStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)

		err := commonTaskTestBiz.cancelTask(ctx, task)

		Ω(err).ShouldNot(HaveOccurred())
	})

	It("should log warning if Cancel fails with an EngineError", func() {
		// Mocking Cancel to return an EngineError
		engineAdapterMock.EXPECT().Cancel(ctx, task, task.Worker).Return(common.ErrInternalError).Times(1)

		// Add additional checks for logging
		// You would typically capture the logs and assert they contain the expected output here

		err := commonTaskTestBiz.cancelTask(ctx, task)
		Ω(err).Should(HaveOccurred())
	})

	It("should log error if freeTaskResource returns a non-scheduled resource error", func() {
		// Mocking Cancel to succeed
		engineAdapterMock.EXPECT().Cancel(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		// Mocking freeTaskResource to return a non-scheduled resource error
		schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(common.ErrInternalError).Times(1)

		err := commonTaskTestBiz.cancelTask(ctx, task)

		Ω(err).Should(HaveOccurred())
		// Check if the appropriate log was created
		// e.g., check logs for "resource does not exist"
	})

	It("should return error if updateInternalStopped fails", func() {
		// Mocking Cancel to succeed
		engineAdapterMock.EXPECT().Cancel(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		// Mocking updateInternalStopped to return an error
		taskRepoMock.EXPECT().UpdateInternalStatus(gomock.Any(), gomock.Any()).Return(common.ErrInternalError).Times(1)

		err := commonTaskTestBiz.cancelTask(ctx, task)

		Ω(err).Should(HaveOccurred())
	})
})

func TestCommonTask_CompleteTask(t *testing.T) {
	//task := defaultModelTask

	mockey.PatchConvey("test TC CompleteTask and return success", t, func() {

		//task.Metadata = map[string]string{string(custom_types.MetadataKeyRequestSource): string(custom_types.RequestSourceTc)}
		//tchttp://21.43.81.124:8080/cluster/task/callback
		//task.Notify = "http://qinxin.notify.test"
		//todo 数据库操作的mock存在问题 qinxin
		//taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(task, nil).Times(1)
		//mockey.Mock((*repository.TaskRepo)).Return(service, nil).Build()
		//err := commonTaskBiz.CompleteTask(context.Background(), &CompleteRequest{
		//	JobID:  "002-unit-test",
		//	TaskID: "002-unit-test",
		//})
		//unsupported tasker type for UpdateTask
		//assert.Nil(t, err)
	})
}

func TestCommonTaskBiz_AddSourceStreamTask(t *testing.T) {
	ctl := gomock.NewController(t)
	sourceStreamTaskRepo := mocks.NewMockSourceStreamTaskRepo(ctl)
	transcodeAttachmentRepo := mocks.NewMockTranscodeAttachmentRepo(ctl)
	transcodeStatInfoRepo := mocks.NewMockTranscodeStatInfoRepo(ctl)
	defer ctl.Finish()
	sourceStreamTaskRepo.EXPECT().Insert(context.Background(), gomock.Any()).Return(nil).Times(1)
	sourceStreamTaskRepo.EXPECT().Insert(context.TODO(), gomock.Any()).Return(common.ErrInternalError).Times(1)
	transcodeAttachmentRepo.EXPECT().SelectByTaskType(context.Background(), "test_taskId", "pic").Return([]*domain.TranscodeAttachment{
		{
			TaskID:     "test_taskId",
			AttachType: "pic",
			Name:       "wulv_test_2025-0624",
		},
	}, nil).AnyTimes()
	transcodeAttachmentRepo.EXPECT().SelectByTaskType(context.TODO(), "test_taskId", "pic").Return(nil, common.ErrInternalError).Times(1)
	transcodeAttachmentRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(1)
	transcodeAttachmentRepo.EXPECT().Delete(gomock.Any(), gomock.Any()).Return(nil).Times(1)
	transcodeStatInfoRepo.EXPECT().GetLatest(gomock.Any(), "livetranscode_pub").Return(
		&domain.TranscodeStatInfo{
			ID:            1,
			CreateTime:    time.Now(),
			ModifiedTime:  time.Now(),
			Domain:        "example.com",
			Tag:           "mock-tag",
			StatTime:      time.Now(),
			StatInfo:      "mock-stat-info",
			TotalQuota:    100,
			TotalTaskNum:  50,
			InnerQuota:    30,
			InnerTaskNum:  20,
			CloudQuota:    40,
			CloudTaskNum:  25,
			L2Quota:       10,
			L2TaskNum:     5,
			L1Quota:       5,
			L1TaskNum:     2,
			L22Quota:      8,
			L22TaskNum:    4,
			Inner2Quota:   15,
			Inner2TaskNum: 10,
			Extend:        "mock-extend-info",
		}, nil).AnyTimes()
	transcodeStatInfoRepo.EXPECT().Query(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*domain.TranscodeStatInfo{
		{
			ID:            1,
			CreateTime:    time.Now(),
			ModifiedTime:  time.Now(),
			Domain:        "example.com",
			Tag:           "mock-tag",
			StatTime:      time.Now(),
			StatInfo:      "mock-stat-info",
			TotalQuota:    100,
			TotalTaskNum:  50,
			InnerQuota:    30,
			InnerTaskNum:  20,
			CloudQuota:    40,
			CloudTaskNum:  25,
			L2Quota:       10,
			L2TaskNum:     5,
			L1Quota:       5,
			L1TaskNum:     2,
			L22Quota:      8,
			L22TaskNum:    4,
			Inner2Quota:   15,
			Inner2TaskNum: 10,
			Extend:        "mock-extend-info",
		},
	}, nil).Times(1)
	commonTaskTestBiz.sourceStreamTaskRepo = sourceStreamTaskRepo
	commonTaskTestBiz.transcodeAttachmentRepo = transcodeAttachmentRepo
	commonTaskTestBiz.transcodeStatInfoRepo = transcodeStatInfoRepo
	err := commonTaskTestBiz.AddSourceStreamTask(context.Background(), lc.StreamParams{}, "test_taskId", "wulv_test_2025-0624")
	assert.Nil(t, err)
	err = commonTaskTestBiz.AddSourceStreamTask(context.TODO(), lc.StreamParams{}, "test_taskId", "wulv_test_2025-0624")
	assert.NotNil(t, err)
	atts := commonTaskTestBiz.GetTransCodeAttachments(context.Background(), "test_taskId", lc.AttachmentTypePIC)
	assert.NotNil(t, atts)
	atts = commonTaskTestBiz.GetTransCodeAttachments(context.TODO(), "test_taskId", lc.AttachmentTypePIC)
	assert.Nil(t, atts)
	attNames := []string{"wulv_test_2025-0624"}
	attachments := []map[string]interface{}{
		{
			"Name": "wulv_test_2025-0624",
		},
	}
	err = commonTaskTestBiz.UpdateTaskAttachments(ctx, lc.AttachmentTypePIC, "test_taskId", attachments, attNames)
	assert.Nil(t, err)
	commonTaskTestBiz.CleanTranscodeAttachment(context.Background(), &models.Task{
		TaskId: "test_taskId",
	}, lc.AttachmentTypePIC)
	stats, err := commonTaskTestBiz.QueryStat(context.Background(), "livetranscode_pub", "live")
	assert.Nil(t, err)
	assert.Equal(t, stats[0].TotalQuota, int32(100))
}

// 调试时需要将ak,sk更新为正确的值，见预发环境diamond mns配置项
//func TestCommonTaskBiz_sendCDequeueNotify(t *testing.T) {
//	task := &models.Task{
//		TaskId:       "123test",
//		JobId:        "123test",
//		UserId:       "mpp",
//		UserData:     "userData",
//		Result:       &models.TaskResult{},
//		GmtCreate:    time.Now(),
//		LastModified: time.Now(),
//		Notify:       "mns://1426105496228119.mns.cn-shanghai.aliyuncs.com/queues/mpp-notify-test,mns://1426105496228119.mns.cn-shanghai.aliyuncs.com/queues/mpp-notify-test?dequeued=inner",
//	}
//
//	// 测试用例1: 成功发送通知，任务结果成功
//	task.Result.Message = "Task completed successfully"
//	task.Result.Reason = "Success"
//	task.Result.Data = "short_data"
//	task.Result.DataUrl = "test"
//
//	commonTaskBiz.conf = &conf.Bootstrap{
//		Mns: &conf.Mns{
//			AccessKey: "123",
//			SecretKey: "123",
//		},
//	}
//	commonTaskBiz.log = log.NewHelper(log.DefaultLogger)
//
//	notify.SetNotifyProvider("mns", mns.NewNotifyMNS)
//	err := commonTaskBiz.sendDequeueNotify(context.Background(), task)
//	log.Errorf("sendCompleteNotify, err:%v", err)
//	assert.NoError(t, err)
//}
//
//// 调试时需要将ak,sk更新为正确的值，见预发环境diamond mns配置项
//func TestCommonTaskBiz_sendCompleteNotify(t *testing.T) {
//	task := &models.Task{
//		TaskId:       "123test",
//		JobId:        "123test",
//		UserId:       "mpp",
//		UserData:     "userData",
//		Result:       &models.TaskResult{},
//		GmtCreate:    time.Now(),
//		LastModified: time.Now(),
//		Notify:       "mns://1426105496228119.mns.cn-shanghai.aliyuncs.com/queues/mpp-notify-test,mns://1426105496228119.mns.cn-shanghai.aliyuncs.com/queues/mpp-notify-test?dequeued=inner",
//	}
//
//	// 测试用例1: 成功发送通知，任务结果成功
//	task.Result.Message = "Task completed successfully"
//	task.Result.Reason = "Success"
//	task.Result.Data = "short_data"
//	task.Result.DataUrl = "test"
//
//	commonTaskBiz.conf = &conf.Bootstrap{
//		Mns: &conf.Mns{
//			AccessKey: "123",
//			SecretKey: "123",
//		},
//	}
//	commonTaskBiz.log = log.NewHelper(log.DefaultLogger)
//	notify.SetNotifyProvider("mns", mns.NewNotifyMNS)
//	err := commonTaskBiz.sendCompleteNotify(context.Background(), task)
//	log.Errorf("sendCompleteNotify, err:%v", err)
//	assert.NoError(t, err)
//}
//
//// 调试时需要将ak,sk更新为正确的值
//func TestCommonTaskBiz_sendCompleteNotify2(t *testing.T) {
//	client := ali_mns.NewAliMNSClient(
//		"mns://1426105496228119.mns.cn-shanghai.aliyuncs.com",
//		"**",
//		"**")
//	queueManager := ali_mns.NewMNSQueueManager(client)
//	// 创建队列，队列名称为test。
//	err := queueManager.CreateQueue("test", 0, 65536, 345600, 30, 0, 3)
//
//	time.Sleep(time.Duration(2) * time.Second)
//
//	if err != nil && !ali_mns.ERR_MNS_QUEUE_ALREADY_EXIST_AND_HAVE_SAME_ATTR.IsEqual(err) {
//		fmt.Println(err)
//		return
//	}
//	msg := ali_mns.MessageSendRequest{
//		MessageBody:  "hello <\"aliyun-mns-go-sdk\">",
//		DelaySeconds: 0,
//		Priority:     8}
//	queue := ali_mns.NewMNSQueue("mpp-notify-test", client)
//
//	ret, err := queue.SendMessage(msg)
//	go func() {
//		fmt.Println(queue.QPSMonitor().QPS())
//	}()
//
//	if err != nil {
//		fmt.Println(err)
//	} else {
//		log.Infof("response:%v", ret)
//	}
//
//	endChan := make(chan int)
//	respChan := make(chan ali_mns.MessageReceiveResponse)
//	errChan := make(chan error)
//	go func() {
//		select {
//		case resp := <-respChan:
//			{
//				logs.Info("response:", resp)
//				logs.Debug("change the visibility: ", resp.ReceiptHandle)
//				if ret, e := queue.ChangeMessageVisibility(resp.ReceiptHandle, 5); e != nil {
//					fmt.Println(e)
//				} else {
//					logs.Info("visibility changed", ret)
//					logs.Debug("delete it now: ", ret.ReceiptHandle)
//					if e := queue.DeleteMessage(ret.ReceiptHandle); e != nil {
//						fmt.Println(e)
//					}
//					endChan <- 1
//				}
//			}
//		case err := <-errChan:
//			{
//				fmt.Println(err)
//				endChan <- 1
//			}
//		}
//	}()
//	//接收消息。
//	queue.ReceiveMessage(respChan, errChan, 30)
//	<-endChan
//}
//
//// 调试时需要将ak,sk更新为正确的值
//func TestCommonTaskBiz_sendCompleteNotify3(t *testing.T) {
//	task := &models.Task{
//		TaskId:       "123test",
//		JobId:        "123test",
//		UserId:       "mpp",
//		UserData:     "userData",
//		Result:       &models.TaskResult{},
//		GmtCreate:    time.Now(),
//		LastModified: time.Now(),
//		Notify:       "mns://1426105496228119.mns.cn-shanghai.aliyuncs.com/queues/mpp-qinxin-test,mns://1426105496228119.mns.cn-shanghai.aliyuncs.com/queues/mpp-notify-test?vendor=inner",
//	}
//
//	// 测试用例1: 成功发送通知，任务结果成功
//	task.Result.Message = "Task completed successfully"
//	task.Result.Reason = "Success"
//	task.Result.Data = "short_data"
//	task.Result.DataUrl = "test"
//
//	commonTaskBiz.conf = &conf.Bootstrap{
//		Mns: &conf.Mns{
//			AccessKey: "123",
//			SecretKey: "123",
//		},
//	}
//	commonTaskBiz.log = log.NewHelper(log.DefaultLogger)
//	notify.SetNotifyProvider("mns", mns.NewNotifyMNS)
//	err := commonTaskBiz.sendCompleteNotify(context.Background(), task)
//	log.Errorf("sendCompleteNotify, err:%v", err)
//	assert.NoError(t, err)
//}

func TestReplayTranscodeTaskAndTranscodeTaskBackupToSourceStream(t *testing.T) {
	repo := mocks.NewMockKVStoreRepo(gomock.NewController(t))
	repo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	repo.EXPECT().ListLike(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	taskRepo := mocks.NewMockTaskRepo(gomock.NewController(t))
	taskRepo.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(&models.Task{
		TaskId: "test_taskId",
		Param: &models.TaskEngineParam{
			Param: "{\"streamSource\":\"rtmp://************:1935/jingkuan/pulltest-sh-pre.alivecdn.com_test062001?ali_mapstream_ignore=on\\u0026ali_ols_play=on\\u0026ali_ols_play_type=transcoding\\u0026ali_rtmp_quick_start=on\\u0026ali_rtmp_retain=0\\u0026vhost=pulltest-sh-pre.alivecdn.com\",\"streamSourceType\":\"stream\",\"streamSourceCodec\":\"h264\",\"streamSourceWidth\":null,\"streamSourceHeight\":null,\"streamSourceFps\":25,\"streamSourceBitrate\":5526,\"streamSourceVolatile\":false,\"streamNum\":1,\"streamTimestampOffset\":0,\"streams\":[{\"aCodec\":\"aac\",\"abr\":\"128\",\"achNum\":\"2\",\"animus\":\"true\",\"aprofile\":\"aac_low\",\"ar\":\"44100\",\"destBakVips\":[\"*************:1935\"],\"env\":\"pub\",\"fps\":\"25\",\"height\":648,\"rtmpDest\":\"rtmp://**********/jingkuan/test062001_hd?vhost=pulltest-sh-pre.alivecdn.com\\u0026live_rtmp_test=on\\u0026ali_inter_auth_time=685a4261\\u0026transcode=no\\u0026transcoded=true\\u0026ali_center_tran_pub=on\",\"type\":\"transcode\",\"vCodec\":\"h264\",\"vprofile\":\"high\",\"width\":-1,\"x264_params\":\"aq-strength=0.8:sync-lookahead=2:force-cfr=1:lookahead-threads=1:rc-lookahead=4:bframes=3:threads=4:keyint=125:scenecut=0:psy=0:trellis=0:me=dia:merange=16:subme=4:vbv-maxrate=1320:vbv-bufsize=2200:crf=25:tb-abrmax=1100\"}],\"switchStreams\":null,\"backupParams\":null,\"streamSourceBakVips\":null,\"streamInterSourceBakAddrs\":null,\"streamL2SourceBakAddrs\":[\"*************:1936\"],\"imsExtra\":\"\",\"adaptiveHDROutput\":\"\",\"quota\":3000,\"allAudioCopy\":0,\"bizNotifyAddr\":\"\",\"bizReportMsgAddr\":\"\",\"rateCallbackParams\":null,\"pushDomain\":\"ols_center_push\",\"domain\":\"pulltest-sh-pre.alivecdn.com\",\"appName\":\"jingkuan\",\"streamName\":\"test062001\",\"token\":\"hd\",\"centerPublicAddr\":\"*************\"}",
		},
		Metadata: map[string]string{"extend": "{\"aa\":\"bb\",\"lostResouce\":true,\"lostRetryCount\":2}"},
	}, nil).AnyTimes()
	taskRepo.EXPECT().UpdateMetadata(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	commonTaskTestBiz.repo = taskRepo
	commonTaskTestBiz.scheduler = schedulerAdapterMock
	commonTaskTestBiz.liveTransCodeParamsAdapter = livetranscode.NewLiveTransCodeParamsAdapter(global_param_loader.NewGlobalParamLoader(log.GetLogger(), repo, &conf.Bootstrap{
		Cluster: &conf.Cluster{
			Region: "cn-shanghai",
		},
		LiveTransCodeConfig: &conf.LiveTransCodeConfig{
			Unit:        "pre",
			Env:         "pub",
			BizHost:     "livetranscode.cn-shanghai.aliyuncs.com",
			GslbHost:    "livetranscode.cn-shanghai.aliyuncs.com",
			LiveTocHost: "livetranscode.cn-shanghai.aliyuncs.com",
			UnitDatabase: []*conf.LiveTransCodeConfig_UnitDatabase{
				{
					Unit:   "pre",
					Driver: "mysql",
					Source: "1",
				},
				{
					Unit:   "pub",
					Driver: "mysql",
					Source: "2",
				},
			},
		},
	}), log.DefaultLogger)
	defer mockey.Mock((*http.Client).Do).Return(&http.Response{
		StatusCode: 200,
		Body: io.NopCloser(strings.NewReader(`{"content":{
			"taskIds":[
                "123"
            ]
		}}`)),
	}, nil).Build().UnPatch()

	commonTaskTestBiz.ReplayTranscodeTask(context.Background(), []*models.Task{
		{
			TaskId: "test_taskId",
			Param: &models.TaskEngineParam{
				Param: "{\"streamSource\":\"rtmp://************:1935/jingkuan/pulltest-sh-pre.alivecdn.com_test062001?ali_mapstream_ignore=on\\u0026ali_ols_play=on\\u0026ali_ols_play_type=transcoding\\u0026ali_rtmp_quick_start=on\\u0026ali_rtmp_retain=0\\u0026vhost=pulltest-sh-pre.alivecdn.com\",\"streamSourceType\":\"stream\",\"streamSourceCodec\":\"h264\",\"streamSourceWidth\":null,\"streamSourceHeight\":null,\"streamSourceFps\":25,\"streamSourceBitrate\":5526,\"streamSourceVolatile\":false,\"streamNum\":1,\"streamTimestampOffset\":0,\"streams\":[{\"aCodec\":\"aac\",\"abr\":\"128\",\"achNum\":\"2\",\"animus\":\"true\",\"aprofile\":\"aac_low\",\"ar\":\"44100\",\"destBakVips\":[\"*************:1935\"],\"env\":\"pub\",\"fps\":\"25\",\"height\":648,\"rtmpDest\":\"rtmp://**********/jingkuan/test062001_hd?vhost=pulltest-sh-pre.alivecdn.com\\u0026live_rtmp_test=on\\u0026ali_inter_auth_time=685a4261\\u0026transcode=no\\u0026transcoded=true\\u0026ali_center_tran_pub=on\",\"type\":\"transcode\",\"vCodec\":\"h264\",\"vprofile\":\"high\",\"width\":-1,\"x264_params\":\"aq-strength=0.8:sync-lookahead=2:force-cfr=1:lookahead-threads=1:rc-lookahead=4:bframes=3:threads=4:keyint=125:scenecut=0:psy=0:trellis=0:me=dia:merange=16:subme=4:vbv-maxrate=1320:vbv-bufsize=2200:crf=25:tb-abrmax=1100\"}],\"switchStreams\":null,\"backupParams\":null,\"streamSourceBakVips\":null,\"streamInterSourceBakAddrs\":null,\"streamL2SourceBakAddrs\":[\"*************:1936\"],\"imsExtra\":\"\",\"adaptiveHDROutput\":\"\",\"quota\":3000,\"allAudioCopy\":0,\"bizNotifyAddr\":\"\",\"bizReportMsgAddr\":\"\",\"rateCallbackParams\":null,\"pushDomain\":\"ols_center_push\",\"domain\":\"pulltest-sh-pre.alivecdn.com\",\"appName\":\"jingkuan\",\"streamName\":\"test062001\",\"token\":\"hd\",\"centerPublicAddr\":\"*************\"}",
			},
			Metadata: map[string]string{"extend": "{\"aa\":\"bb\",\"lostResouce\":true,\"lostRetryCount\":2}"},
		},
	})
	err := commonTaskTestBiz.TranscodeTaskBackupToSourceStream(context.Background(), "test_taskId")
	assert.Nil(t, err)
}

func TestCommandTask_LiveTranscodeCommands(t *testing.T) {
	taskRepo := mocks.NewMockTaskRepo(gomock.NewController(t))
	task := mockModelTask()
	task.Param = &models.TaskEngineParam{}
	task.InternalStatus = models.TaskStateActive
	taskRepo.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(task, nil).AnyTimes()
	commonTaskTestBiz.repo = taskRepo
	mockEngine := mocks.NewMockEngineAdapter(gomock.NewController(t))
	mockEngine.EXPECT().Command(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	commonTaskTestBiz.engine = mockEngine
	defer mockey.Mock((*livetranscode.LiveTransCodeParamsAdapter).CorrectLiveTransCodeCommandParams).Return(nil).Build().UnPatch()
	err := commonTaskTestBiz.CommandTask(context.Background(), "test_taskId", "", "", lc.COMMAN_COMMAND)
	assert.Nil(t, err)
	//进入直播case
	task.EngineModel = models.LiveTranscode
	//COMMON_COMMAND
	err = commonTaskTestBiz.CommandTask(context.Background(), "test_taskId", "", "", lc.COMMAN_COMMAND)
	assert.Nil(t, err)
	//COMMON_COMMAND
	err = commonTaskTestBiz.CommandTask(context.Background(), "test_taskId", "", "", lc.PIC_ADD_COMMAND)
	assert.Nil(t, err)
	//PIC_ADD_COMMAND
	err = commonTaskTestBiz.CommandTask(context.Background(), "test_taskId", "", "", lc.PIC_EDIT_COMMAND)
	assert.Nil(t, err)
	//PIC_EDIT_COMMAND
	err = commonTaskTestBiz.CommandTask(context.Background(), "test_taskId", "", "", lc.TEXT_ADD_COMMAND)
	assert.Nil(t, err)
	//TEXT_ADD_COMMAND
	err = commonTaskTestBiz.CommandTask(context.Background(), "test_taskId", "", "", lc.TEXT_EDIT_COMMAND)
	assert.Nil(t, err)
	//TEXT_EDIT_COMMAND
	err = commonTaskTestBiz.CommandTask(context.Background(), "test_taskId", "", "", lc.COMMAN_COMMAND)
	assert.Nil(t, err)

}

func TestCallBizHost(t *testing.T) {
	defer mockey.Mock((*pb.LiveBizHTTPClientImpl).GetStatInfo).Return(&pb.GetStatInfoResponse{
		Code: "200",
		Data: &pb.Data{
			Tasks: []*pb.Tasks{
				{
					Domain:     "ali-mmyzpush.v.momocdn.com",
					App:        "momo",
					Quota:      100,
					CurStreams: 100,
					Percentage: 100,
					Template:   "livetranscode_test_pub",
				},
			},
		},
	}, nil).Build().UnPatch()
	result, err := commonTaskTestBiz.CallBizHost(context.Background(), "", "domain", "app")
	assert.Nil(t, err)
	statisticsResult, ok := result.([]lc.LiveStatisticsTaskStat)
	assert.True(t, ok)
	assert.Equal(t, statisticsResult[0].Domain, "ali-mmyzpush.v.momocdn.com")
}

func TestSendConfigToWorker(t *testing.T) {
	mockScheduler := mocks.NewMockScheduleAdapter(gomock.NewController(t))
	mockScheduler.EXPECT().GetAllSchedulerTags().Return([]string{"pub"}).AnyTimes()
	defer mockey.Mock((*http.Client).Do).Return(&http.Response{
		StatusCode: 200,
		Body:       io.NopCloser(strings.NewReader(`{"result":{"code":"Success"}}`)),
		Header:     make(http.Header),
	}, nil).Build().UnPatch()
	mockScheduler.EXPECT().ListAllWorker(gomock.Any(), gomock.Any()).Return([]*models.Worker{
		{
			Ip:          "************",
			Port:        8080,
			Id:          "test_workerId",
			EngineModel: "transcode",
			Tag:         "pub",
			Product:     "transcode",
		},
	}, nil).AnyTimes()
	commonTaskTestBiz.scheduler = mockScheduler
	err := commonTaskTestBiz.SendConfigToWorker(context.Background(), "test_taskId")
	assert.Nil(t, err)
}
