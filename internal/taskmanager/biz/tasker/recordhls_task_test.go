package tasker

import (
	"context"
	. "github.com/bytedance/mockey"
	"github.com/glebarez/sqlite"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/golang/mock/gomock"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"gorm.io/gorm"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/leader"
	"mpp/internal/taskmanager/biz/sentinel"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/infra/client"
	"mpp/internal/taskmanager/infra/engine"
	"mpp/internal/taskmanager/infra/scheduler"
	"mpp/internal/taskmanager/mocks"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"
	"mpp/internal/taskmanager/repository/impl"
	"time"
)

var recordHlsTaskBiz *RecordHlsTaskBiz

func init() {
	config := &conf.Bootstrap{
		Mns: &conf.Mns{
			AccessKey: "123",
			SecretKey: "123",
		},
	}

	ctl := gomock.NewController(nil)
	defer ctl.Finish()
	//m = mocks.NewMockTaskRepo(ctl)
	//m.EXPECT().CloneWithSpecifiedTable(gomock.Any()).Return(m)

	db, _ := gorm.Open(sqlite.Open("test.db"), &gorm.Config{})

	repositoryData, _, _ := repository.NewData(log.DefaultLogger, db, nil, &conf.Data{})
	taskRepo := impl.NewTaskRepo(repositoryData, log.DefaultLogger)

	engineAdapter := engine.NewAdapter(&conf.Notifier{
		Address: "",
	}, log.DefaultLogger, nil)

	//discovery := server.NewDiscovery(&conf.Registry{
	//	Endpoint: "",
	//})
	//本地测试服务发现需注释
	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	Mock(client.InitHttpClient).Return(conn, nil).Build()
	rd := mocks.NewMockDiscovery(ctl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"mpp-taskscheduler-l2.http"}, nil).Times(1)
	scheduleAdapter, _, _ := scheduler.NewScheduleAdapter(context.Background(), rd)
	recordHlsTaskBiz, _ = NewRecordHlsTaskBiz(config, taskRepo, log.DefaultLogger, engineAdapter, scheduleAdapter, nil, nil, &sentinel.SentinelBiz{})
	batchTaskTestBiz, _ = NewBatchTaskBiz(config, taskRepo, log.DefaultLogger, scheduleAdapter, engineAdapter, nil, nil, nil, nil, &sentinel.SentinelBiz{}, nil, nil, nil)
	commonTaskTestBiz, _ = NewTaskBiz(config, taskRepo, nil, scheduleAdapter, nil, nil, nil, &sentinel.SentinelBiz{}, nil, nil, nil, log.DefaultLogger)
	mockLeaderBiz := &leader.LeaderBiz{}
	mockLeaderBiz.SetLeader()
	engineTaskSyncBiz = NewEngineTaskSync(context.Background(), taskRepo, commonTaskTestBiz, mockLeaderBiz, scheduleAdapter, engineAdapter, log.DefaultLogger)
	schedulerResourceSyncBiz = NewSchedulerResourceSync(context.Background(), taskRepo, commonTaskTestBiz, mockLeaderBiz, scheduleAdapter, log.DefaultLogger)
	taskStatusSyncBiz, _ = NewTaskStatusSync(context.Background(), config, taskRepo, engineAdapter, commonTaskTestBiz, mockLeaderBiz, log.DefaultLogger)
	taskSentinelSyncBiz = NewTaskSentinelSync(context.Background(), taskRepo, nil, commonTaskTestBiz, batchTaskTestBiz, mockLeaderBiz, nil, log.DefaultLogger)
	//Mock((*TaskDBChecker).Check).Return().Build().Patch()
	taskDBCheckerBiz, _ = NewTaskDBChecker(taskRepo, mockLeaderBiz, true)
	batchTaskSyncTest, _ = NewBatchTaskSync(context.Background(), config, taskRepo, engineAdapter, commonTaskTestBiz, batchTaskTestBiz, mockLeaderBiz, scheduleAdapter, &sentinel.SentinelBiz{}, nil, nil, log.DefaultLogger)
}

var _ = Describe("Test Start task", func() {
	//todo 待修复
	//It("should be err with first db operate", func() {
	//	err := recordHlsTaskBiz.StartTask(context.Background(), &models.Task{TaskId: "aa"})
	//	Expect(err.Error()).Should(ContainSubstring("no such table"))
	//})

	It("should be return nil with both running status", func() {
		task := &models.Task{
			InternalStatus: 1,
			BizStatus:      1,
		}
		//Mock(recordHlsTaskBiz.repo.FindByID).Return(task, nil).Build() // 无效
		//patches := gomonkey.ApplyPrivateMethod(reflect.TypeOf(recordHlsTaskBiz.repo), "FindByID", func(_ *impl.taskRepo, _ context.Context, _ string) (*models.Task, error) {
		//	return task, nil
		//})
		//defer patches.Reset()   // 无效

		//m.EXPECT().CloneWithSpecifiedTable(gomock.Any()).Return(m).Times(1)
		recordHlsTaskBiz.repo = taskRepoMock
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(task, nil).Times(1)
		err := recordHlsTaskBiz.StartTask(context.Background(), &models.Task{TaskId: "aa"})
		Ω(err).ShouldNot(HaveOccurred())
	})

	It("test add affinity success", func() {
		recordHlsTaskBiz.repo = taskRepoMock
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(&models.Task{BizStatus: 1, Worker: &models.Worker{Id: "workerId"}}, nil).Times(1)

		mock1 := Mock((*CommonTaskBiz).allocResourceNotUpdateTask).Return(&common.ScheduleError{Code: 1, Reason: common.ErrorScheduleAllocResourceFailed, Message: "AllocResourceErr"}).Build()
		defer mock1.UnPatch()
		taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		task := &models.Task{TaskId: "aa"}
		err := recordHlsTaskBiz.StartTask(context.Background(), task)
		Expect(err.Error()).Should(Equal("Code: 1, Reason: ScheduleError.AllocResourceErr, Message: AllocResourceErr"))
		Expect(task.RequestResource.Affinity.Preferred[0].Key).Should(Equal("workerId"))
	})

	It("test alloc resource error", func() {

		recordHlsTaskBiz.repo = taskRepoMock
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(&models.Task{BizStatus: 0}, nil).Times(1)
		taskRepoMock.EXPECT().InsertUpdateOnDuplicate(gomock.Any(), gomock.Any()).Return(nil).Times(1)

		mock1 := Mock((*CommonTaskBiz).allocResourceNotUpdateTask).Return(&common.ScheduleError{Code: 1, Reason: common.ErrorScheduleAllocResourceFailed, Message: "AllocResourceErr"}).Build()
		defer mock1.UnPatch()
		taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		err := recordHlsTaskBiz.StartTask(context.Background(), &models.Task{TaskId: "aa"})
		Expect(err.Error()).Should(Equal("Code: 1, Reason: ScheduleError.AllocResourceErr, Message: AllocResourceErr"))
	})

	It("test dispatchTask error", func() {
		recordHlsTaskBiz.repo = taskRepoMock
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(&models.Task{BizStatus: 0}, nil).Times(1)
		taskRepoMock.EXPECT().InsertUpdateOnDuplicate(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		taskRepoMock.EXPECT().UpdateScheduleWorkerAndInternalStatus(gomock.Any(), gomock.Any()).Return(nil).Times(0)

		mock1 := Mock((*CommonTaskBiz).allocResourceNotUpdateTask).Return(nil).Build()
		defer mock1.UnPatch()
		taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		mock2 := Mock((*CommonTaskBiz).dispatchTask).Return(&common.EngineError{Code: 2, Reason: common.ErrorEngineHttpFailed, Message: "HttpError"}).Build()
		defer mock2.UnPatch()
		mock3 := Mock((*CommonTaskBiz).freeTaskResource).Return(nil).Build()
		defer mock3.UnPatch()
		err := recordHlsTaskBiz.StartTask(context.Background(), &models.Task{TaskId: "aa"})
		Expect(err.Error()).ShouldNot(BeNil())
	})

	It("test StartTask return ok", func() {
		recordHlsTaskBiz.repo = taskRepoMock
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(&models.Task{BizStatus: 0}, nil).Times(1)
		taskRepoMock.EXPECT().InsertUpdateOnDuplicate(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		taskRepoMock.EXPECT().UpdateBizStatus(gomock.Any(), gomock.Any()).Return(nil).Times(0)
		taskRepoMock.EXPECT().UpdateScheduleWorkerAndInternalStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)

		mock1 := Mock((*CommonTaskBiz).allocResourceNotUpdateTask).Return(nil).Build()
		defer mock1.UnPatch()
		mock2 := Mock((*CommonTaskBiz).dispatchTask).Return(nil).Build()
		defer mock2.UnPatch()
		mock3 := Mock((*CommonTaskBiz).freeTaskResource).Return(nil).Build()
		defer mock3.UnPatch()
		err := recordHlsTaskBiz.StartTask(context.Background(), &models.Task{TaskId: "aa"})
		Ω(err).ShouldNot(HaveOccurred())
	})
})
