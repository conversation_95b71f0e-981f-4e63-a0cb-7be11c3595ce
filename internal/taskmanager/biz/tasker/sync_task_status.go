package tasker

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/cinience/animus/safe"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/leader"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"
	"mpp/internal/taskmanager/tracing/tasker_span"
	"mpp/pkg/redis"
	"mpp/pkg/tracer/tasker_tracing"
	"mpp/pkg/utils"

	"github.com/go-kratos/kratos/v2/log"
	goredis "github.com/redis/go-redis/v9"
	gocron "github.com/robfig/cron/v3"
	"golang.org/x/sync/errgroup"
)

type TaskStatusSync struct {
	repo          repository.TaskRepo
	log           *log.Helper
	taskBiz       *CommonTaskBiz
	leaderBiz     *leader.LeaderBiz
	taskDbChecker *TaskDBChecker
	engine        common.EngineAdapter
	cron          *gocron.Cron
	cacheDb       goredis.UniversalClient
	taskTracer    *tasker_tracing.Tracer
}

func NewTaskStatusSync(ctx context.Context, conf *conf.Bootstrap, repo repository.TaskRepo, engine common.EngineAdapter, taskBiz *CommonTaskBiz, leaderBiz *leader.LeaderBiz, logger log.Logger) (*TaskStatusSync, error) {
	var err error
	t := &TaskStatusSync{repo: repo, taskBiz: taskBiz, leaderBiz: leaderBiz, engine: engine,
		log: log.NewHelper(logger)}
	t.repo = repo.CloneWithSpecifiedTable(models.StreamTaskTableName)
	//taskBiz.SetTracer(tasker_tracing.NewTracer(tasker_tracing.WithConsumerSpanKind()))
	t.taskTracer = tasker_tracing.NewTracer(tasker_tracing.WithTracerName("mppTaskChecker"))
	if conf.GetRedisUri() != "" {
		t.cacheDb, err = redis.GetRedisUniversalClientWithURL(conf.Data.Redis.GetEndpoint())
		if err != nil {
			return nil, err
		}
	}
	t.cron = gocron.New()

	t.taskDbChecker, err = NewTaskDBChecker(t.repo, leaderBiz, false)
	if err != nil {
		return nil, err
	}

	safe.Go(func() {
		// todo 外部状态仍在运行中，但内部发现其已经停止
		t.startSyncMissingRunningTasks(ctx)
	})

	safe.Go(func() {
		// 处理偷跑的任务，即外部已经停止，但内部发现任务仍在运行中
		t.startSyncUnexpectedRunningTasks(ctx)
	})

	safe.Go(func() {
		// 定期检查路由任务与数据库任务对比
		t.startSyncRouterTasks(ctx)
	})

	_, err = t.cron.AddFunc("@every 60s", func() {
		t.checkStreamTask()
	})

	t.cron.Start()

	return t, nil
}

func (ts *TaskStatusSync) checkStreamTask() {
	if !ts.leaderBiz.IsLeader() {
		return
	}
	if ts.cacheDb == nil {
		return
	}

	var (
		err    error
		cursor uint64
		count  int
	)

	startTime := time.Now()
	countMap := map[string]uint64{}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()
	for {
		var keys []string
		// *扫描所有key，每次20条
		keys, cursor, err = ts.cacheDb.Scan(ctx, cursor, models.StreamTaskType.String()+"*", 20).Result()
		if err != nil {
			log.Errorf("redis scan failed. err: %v", err)
			break
		}

		count += len(keys)
		log.Infof("found %d keys", count)
		var value string

		g, ctx := errgroup.WithContext(ctx)
		for _, key := range keys {
			value, err = ts.cacheDb.Get(ctx, key).Result()
			if err != nil {
				log.Errorf("redis get failed. key:%s, err: %v", key, err)
				continue
			}

			task := &models.Task{}
			err = task.DecodeFromBytes([]byte(value))
			if err != nil {
				log.Errorf("redis get failed. value:%s err: %v", value, err)
				continue
			}

			taskId := task.TaskId
			task, err = ts.repo.FindByID(ctx, taskId)
			if err != nil {
				log.Errorf("repo FindByID failed. taskId:%s, err: %v", taskId, err)
				continue
			}

			// 30s内的任务暂时不check
			if time.Now().Sub(task.GmtCreate).Seconds() < 30 {
				continue
			}
			// 最近更新的任务，暂时不check
			if task.IsRecentlyUpdated() {
				continue
			}

			countKey := task.EncodeBaseElem()
			itemValue, ok := countMap[countKey]
			if ok {
				countMap[countKey] = itemValue + 1
			} else {
				countMap[countKey] = 1
			}

			// 任务已经停止，不再检查
			if task.IsBizStatusStopped() {
				_, err = ts.cacheDb.Del(ctx, task.EncodeKVKey()).Result()
				if err != nil {
					log.Errorf("redis del failed. err: %v", err)
				}
				continue
			} else {
				// ttl 续期
				_, err = ts.cacheDb.Expire(ctx, task.EncodeKVKey(), time.Minute*5).Result()
				if err != nil {
					log.Errorf("redis del failed. err: %v", err)
				}
			}

			g.Go(func() error {
				spanCtx, span := ts.taskTracer.Start(context.Background(), "Check", task.Metadata)
				chkErr := ts.engine.Check(spanCtx, task, task.Worker)
				// 防止tracing过多，只在任务量较少或者有异常的时候提交tracing
				if count < 100 || chkErr != nil {
					ts.taskTracer.EndWithLogEvent(spanCtx, span, func() {
						tasker_span.SpanRecordWithTask(spanCtx, "server.TaskCheck", task)
					}, chkErr)
				}

				return chkErr
			})
		}

		if waitErr := g.Wait(); waitErr != nil {
			log.Errorf("wait all task check failed. err:%v", waitErr)
		}

		if cursor == 0 {
			break
		}
	}

	// todo 增加下 失败任务的统计， worker纬度的统计
	extend := map[string]string{
		"check.taskCount": fmt.Sprintf("%d", count),
		"check.costMs":    fmt.Sprintf("%d", time.Now().Sub(startTime).Milliseconds()),
	}
	for k, v := range countMap {
		extend[k] = fmt.Sprintf("%d", v)
	}

	spanCtx, span := ts.taskTracer.Start(context.Background(), "Summary", nil)
	defer func() {
		ts.taskTracer.EndWithLogEvent(spanCtx, span, func() {
			tasker_span.SpanRecordWithTaskAndExtendInformationWithSpan(spanCtx, span, "server.TaskChecker.Summary", nil, extend)
		}, err)
	}()

	if err != nil {
		log.Errorf("checkTask total found %d keys, cost:%v, err:%v", count, time.Now().Sub(startTime), err)
	} else {
		log.Infof("checkTask total found %d keys, cost:%v", count, time.Now().Sub(startTime))
	}
}

func (ts *TaskStatusSync) startSyncMissingRunningTasks(ctx context.Context) {
	count := 0
	for {
		count++
		if count%10 == 0 {
			log.Infof("enter startSyncMissingRunningTasks, count:%d, isLeader:%v (if not leader, will do nothing)", count, ts.leaderBiz.IsLeader())
		}

		if !ts.leaderBiz.IsLeader() {
			time.Sleep(time.Second * 1)
			continue
		}
		ctx := utils.ResetRequestId(ctx)

		ts.SyncMissingRunningTasks(ctx, count)

		if ctx.Err() != nil {
			log.Error("startSyncMissingRunningTasks fail", ctx.Err())
			return
		}

		// 休眠一段时间，以避免过于频繁的执行
		time.Sleep(time.Second * 3)
	}
}

func (ts *TaskStatusSync) startSyncUnexpectedRunningTasks(ctx context.Context) {
	count := 0
	for {
		count++
		if count%10 == 0 {
			log.Infof("enter startSyncUnexpectedRunningTasks, count:%d, isLeader:%v (if not leader, will do nothing)", count, ts.leaderBiz.IsLeader())
		}

		if !ts.leaderBiz.IsLeader() {
			time.Sleep(time.Second * 10)
			continue
		}

		ctx := utils.ResetRequestId(ctx)
		ts.SyncUnexpectedRunningTasks(ctx, count)

		if ctx.Err() != nil {
			log.Error("startSyncUnexpectedRunningTasks fail", ctx.Err())
			return
		}

		// 休眠一段时间，以避免过于频繁的执行
		time.Sleep(time.Second * 30)
	}
}

// SyncMissingRunningTasks sync internal not running tasks
// (1) call repo to get all biz running but internal not running tasks
// (2) for each tasker try restart it if not update recently
func (ts *TaskStatusSync) SyncMissingRunningTasks(ctx context.Context, count int) {
	log.Infof("enter SyncMissingRunningTasks, count:%d", count)
	tasks, err := ts.repo.ListMissingRunningTasks(ctx)
	if err != nil {
		log.Error("SyncMissingRunningTasks fail in ListInternalNotRunningTasks", err)
		return
	}
	if len(tasks) == 0 {
		return
	}

	if count%10 == 0 {
		log.Infof("SyncMissingRunningTasks tasks num %v", len(tasks))
	}
	for i := range tasks {
		task := tasks[i]
		log.Infof("start SyncMissingRunningTasks tasker , taskId:%v, taskType:%d", task.TaskId, task.TaskType)
		if task.IsRecentlyUpdated() {
			log.Debugf("SyncMissingRunningTasks tasker %v is recently updated", task.TaskId)
			continue
		}
		//todo 确认工作流启动任务是否幂等
		if task.IsDagJob() {
			log.Infof("SyncMissingRunningTasks tasker %v is dag job", task.TaskId)
			err = ts.taskBiz.startDagJob(ctx, task)
			if err != nil {
				log.Errorf("SyncMissingRunningTasks tasker %v fail in startDagJob %v", task.TaskId, err)
			}
		} else {
			if task.IsAbnormalStopped() {
				log.Warnf("SyncMissingRunningTasks tasker %v is abnormal stopped", task.TaskId)
				ts.taskBiz.processAbnormalResult(ctx, "TaskStatusSync/SyncMissingRunningTasks", task, false, false)
			} else {
				//该日志用于sls中融合调度任务大盘统计，如日志内容变动需要同步更新sls大盘配置
				ctx = utils.SetTaskId(ctx, task.TaskId)
				ctx = utils.SetTaskProduct(ctx, task.Product)
				ts.log.WithContext(ctx).Infof("SyncMissingRunningTasks tasker %v, try scheduleAndDispatchTask",
					task.TaskId)
				err := ts.taskBiz.scheduleAndDispatchTask(ctx, "TaskStatusSync/SyncMissingRunningTasks", task, false)
				if err != nil {
					log.Errorf("SyncMissingRunningTasks tasker %v fail in scheduleAndDispatchTask %v", task.TaskId, err)
				}
			}
		}
	}
	log.Infof("SyncMissingRunningTasks end, count:%d", count)
}

// SyncUnexpectedRunningTasks sync internal unexpected running tasks
func (ts *TaskStatusSync) SyncUnexpectedRunningTasks(ctx context.Context, count int) {
	tasks, err := ts.repo.ListUnexpectedRunningTasks(ctx)
	if err != nil {
		log.Error("SyncUnexpectedRunningTasks fail in ListUnexpectedRunningTasks", err)
		return
	}

	if len(tasks) == 0 {
		return
	}

	if count%10 == 0 {
		log.Infof("SyncUnexpectedRunningTasks tasks num %v", len(tasks))
	}

	for i := range tasks {
		task := tasks[i]
		log.Infof("SyncUnexpectedRunningTasks tasker %v", task.TaskId)
		if task.IsRecentlyUpdated() {
			log.Debugf("SyncUnexpectedRunningTasks tasker %v not recently updated", task.TaskId)
		} else if task.IsEngineAlreadyStopped() {
			log.Infof("SyncUnexpectedRunningTasks tasker %v already stopped", task.TaskId)
			err = ts.taskBiz.replyTaskAndUpdateInternalStopped(ctx, task)
			if err != nil {
				log.Errorf("SyncUnexpectedRunningTasks tasker %v fail in replyTaskAndUpdateInternalStopped, err:%v",
					task.TaskId, err)
			}
		} else if !task.IsRecentlyUpdated() {
			//该日志用于sls中融合调度任务大盘统计，如日志内容变动需要同步更新sls大盘配置
			ctx = utils.SetTaskId(ctx, task.TaskId)
			ctx = utils.SetTaskProduct(ctx, task.Product)
			ts.log.WithContext(ctx).Infof("SyncUnexpectedRunningTasks tasker %v, tasker still running", task.TaskId)
			err = ts.taskBiz.stopJob(ctx, task)
			//stop任务失败处理
			if err != nil {
				log.Errorf("SyncUnexpectedRunningTasks tasker %v fail in stopJob, err:%v", task.TaskId, err)
				//如果停止失败，则不再重试，直接将任务状态置为已停止
				task.InternalStatus = models.TaskStateStopped
				err = ts.repo.UpdateInternalStatus(ctx, task)
				if err != nil {
					log.Errorf("SyncUnexpectedRunningTasks fail in UpdateInternalStatus, taskId:%v, err:%v",
						task.TaskId, err)
				}
			}
		}
	}

}

func (ts *TaskStatusSync) startSyncRouterTasks(ctx context.Context) {
	count := 0
	for {
		count++
		if count%10 == 0 {
			log.Infof("enter startSyncRouterTasks, count:%d, isLeader:%v (if not leader, will do nothing)", count, ts.leaderBiz.IsLeader())
		}

		if !ts.leaderBiz.IsLeader() {
			time.Sleep(time.Second * 10)
			continue
		}

		ctx := utils.ResetRequestId(ctx)
		ts.SyncRouterTasks(ctx, count)

		if ctx.Err() != nil {
			log.Error("startSyncRouterTasks fail", ctx.Err())
			return
		}

		// 休眠一段时间，以避免过于频繁的执行
		time.Sleep(time.Second * 60 * 5)
	}
}

type RouteData struct {
	Id         string `json:"id"`
	System     string `json:"system"`
	Status     string `json:"status"`
	CreateTime string `json:"create_time"`
	UpdateTime string `json:"update_time"`
}

func (ts *TaskStatusSync) SyncRouterTasks(ctx context.Context, count int) {
	recordTaskInfo := &models.Task{Product: "ols", EngineModel: "liverecord"}
	tasks, err := ts.repo.ListBizRunningTasks(ctx, recordTaskInfo)
	if err != nil {
		log.Error("SyncRouterTasks fail in ListBizRunningTasks", err)
		return
	}

	hlsTaskInfo := &models.Task{Product: "ols", EngineModel: "livehls"}
	hlsTasks, err := ts.repo.ListBizRunningTasks(ctx, hlsTaskInfo)
	if err != nil {
		log.Error("SyncRouterTasks fail in ListBizRunningTasks", err)
		return
	}
	tasks = append(tasks, hlsTasks...)

	if count%10 == 0 {
		log.Infof("SyncRouterTasks tasks num %v", len(tasks))
	}
	if len(tasks) == 0 {
		return
	}

	var newSystemRunningId []string
	var oldSystemRunningId []string
	keys := ts.cacheDb.Keys(ctx, "recordhls-*").Val()
	for _, key := range keys {
		data := ts.cacheDb.Get(ctx, key)
		var routeData *RouteData
		if data.Val() != "" {
			_ = json.Unmarshal([]byte(data.Val()), &routeData)
			if routeData != nil {
				if routeData.Status == "running" {
					if routeData.System == "new" {
						newSystemRunningId = append(newSystemRunningId, routeData.Id)
					} else {
						oldSystemRunningId = append(oldSystemRunningId, routeData.Id)
					}
				}
			}
		}
	}

	setNew := make(map[string]struct{})
	for _, v := range newSystemRunningId {
		setNew[v] = struct{}{}
	}

	setOld := make(map[string]struct{})
	for _, v := range oldSystemRunningId {
		setOld[v] = struct{}{}
	}

	var shouldInNewSystemId []string
	var shouldNotInOldSystemId []string
	for _, task := range tasks {
		if _, found := setNew[task.TaskId]; !found {
			shouldInNewSystemId = append(shouldInNewSystemId, task.TaskId)

		}
		if _, found := setOld[task.TaskId]; found {
			shouldNotInOldSystemId = append(shouldNotInOldSystemId, task.TaskId)
		}
	}

	if len(shouldInNewSystemId) > 0 {
		ts.log.WithContext(ctx).Warnf("SyncRouterTasks shouldInNewSystemId %v", shouldInNewSystemId)
	}
	if len(shouldNotInOldSystemId) > 0 {
		ts.log.WithContext(ctx).Warnf("SyncRouterTasks shouldNotInOldSystemId %v", shouldNotInOldSystemId)
	}

	if count%10 == 0 {
		log.Infof("SyncRouterTasks end.")
	}
}
