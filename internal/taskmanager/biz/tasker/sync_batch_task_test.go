package tasker

import (
	"context"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/mohae/deepcopy"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/mocks"
	"mpp/internal/taskmanager/models"
	v1 "proto.mpp/api/common/v1"
	"testing"
)

/**
 * @Author: qinxin
 * @Date: 2025/2/7 20:57
 * @Desc:
 * @Version 1.0
 */

var (
	batchTaskSyncTest *BatchTaskSync
)

func TestSyncBatchWaitDagDependencyTasks(t *testing.T) {

	// Mock leader check

	// Mock ListTasks
	ctl = gomock.NewController(t)
	defer ctl.Finish()
	cleaner = ctl.Finish
	taskRepoMock = mocks.NewMockTaskRepo(ctl)
	schedulerAdapterMock = mocks.NewMockScheduleAdapter(ctl)
	engineAdapterMock = mocks.NewMockEngineAdapter(ctl)
	sentinelRepoMock = mocks.NewMockTaskSentinelConfigRepo(ctl)
	ctx = context.Background()

	batchTaskSyncTest.engine = engineAdapterMock
	batchTaskSyncTest.repo = taskRepoMock
	batchTaskSyncTest.scheduler = schedulerAdapterMock

	mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
	mockTask2.BizStatus = 1
	mockTask2.InternalStatus = 1
	mockTask2.TaskType = 2
	// case 1 ListTasks查询结果为空 不需要进行状态同步
	taskRepoMock.EXPECT().ListTasks(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	batchTaskSyncTest.SyncBatchWaitDagDependencyTasks(context.Background(), 1, v1.TaskType_NonQueueAsync)
	//case 2 ListTasks返回error
	taskRepoMock.EXPECT().ListTasks(gomock.Any(), gomock.Any()).Return(nil, models.ErrTaskNotFoundError).Times(1)
	batchTaskSyncTest.SyncBatchWaitDagDependencyTasks(context.Background(), 1, v1.TaskType_NonQueueAsync)
	//case 3 正常进入调度，调度失败返回error
	mockTask2.TaskType = models.NonQueueAsyncTaskType
	tasks := []*models.Task{mockTask2}
	taskRepoMock.EXPECT().ListTasks(gomock.Any(), gomock.Any()).Return(tasks, nil).Times(1)
	defer mockey.Mock((*BatchTaskBiz).ScheduleNonQueueTask).Return(common.ErrInternalError).Build().UnPatch()
	batchTaskSyncTest.SyncBatchWaitDagDependencyTasks(context.Background(), 1, v1.TaskType_NonQueueAsync)

	//验证 没有返回结果 人工测试验证
}

func TestSyncBatchMissingRunningTasks(t *testing.T) {

	// Mock leader check

	// Mock ListTasks
	ctl = gomock.NewController(t)
	defer ctl.Finish()
	cleaner = ctl.Finish
	taskRepoMock = mocks.NewMockTaskRepo(ctl)
	schedulerAdapterMock = mocks.NewMockScheduleAdapter(ctl)
	engineAdapterMock = mocks.NewMockEngineAdapter(ctl)
	sentinelRepoMock = mocks.NewMockTaskSentinelConfigRepo(ctl)
	ctx = context.Background()

	batchTaskSyncTest.engine = engineAdapterMock
	batchTaskSyncTest.repo = taskRepoMock
	batchTaskSyncTest.scheduler = schedulerAdapterMock

	mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
	mockTask2.BizStatus = 1
	mockTask2.InternalStatus = 1
	mockTask2.TaskType = 2
	// case 1 ListTasks查询结果为空 不需要进行状态同步
	taskRepoMock.EXPECT().ListTasks(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	batchTaskSyncTest.SyncBatchMissingRunningTasks(context.Background(), 1, v1.TaskType_NonQueueAsync)
	//case 2 ListTasks返回error
	taskRepoMock.EXPECT().ListTasks(gomock.Any(), gomock.Any()).Return(nil, models.ErrTaskNotFoundError).Times(1)
	batchTaskSyncTest.SyncBatchMissingRunningTasks(context.Background(), 1, v1.TaskType_NonQueueAsync)
	//case 3 正常进入调度，调度失败返回error
	tasks := []*models.Task{mockTask2}
	taskRepoMock.EXPECT().ListTasks(gomock.Any(), gomock.Any()).Return(tasks, nil).Times(1)
	defer mockey.Mock((*BatchTaskBiz).ScheduleNonQueueTask).Return(common.ErrInternalError).Build().UnPatch()
	batchTaskSyncTest.SyncBatchMissingRunningTasks(context.Background(), 1, v1.TaskType_NonQueueAsync)

	//验证 没有返回结果 人工测试验证
}

func TestSyncBatchUnexpectedRunningTasks(t *testing.T) {

	// Mock leader check

	// Mock ListTasks
	ctl = gomock.NewController(t)
	defer ctl.Finish()
	cleaner = ctl.Finish
	taskRepoMock = mocks.NewMockTaskRepo(ctl)
	schedulerAdapterMock = mocks.NewMockScheduleAdapter(ctl)
	engineAdapterMock = mocks.NewMockEngineAdapter(ctl)
	sentinelRepoMock = mocks.NewMockTaskSentinelConfigRepo(ctl)
	ctx = context.Background()

	batchTaskSyncTest.engine = engineAdapterMock
	batchTaskSyncTest.repo = taskRepoMock
	batchTaskSyncTest.scheduler = schedulerAdapterMock

	mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
	mockTask2.BizStatus = 1
	mockTask2.InternalStatus = 1
	mockTask2.TaskType = 2
	// case 1 ListTasks查询结果为空 不需要进行状态同步
	taskRepoMock.EXPECT().ListTasks(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
	batchTaskSyncTest.SyncBatchUnexpectedRunningTasks(context.Background(), 1, v1.TaskType_NonQueueAsync)
	//case 2 ListTasks返回error
	taskRepoMock.EXPECT().ListTasks(gomock.Any(), gomock.Any()).Return(nil, models.ErrTaskNotFoundError).Times(1)
	batchTaskSyncTest.SyncBatchUnexpectedRunningTasks(context.Background(), 1, v1.TaskType_NonQueueAsync)
	//case 3 正常进入调度，调度失败返回error
	tasks := []*models.Task{mockTask2}
	taskRepoMock.EXPECT().ListTasks(gomock.Any(), gomock.Any()).Return(tasks, nil).Times(1)
	defer mockey.Mock((*CommonTaskBiz).stopTask).Return(common.ErrInternalError).Build().UnPatch()
	batchTaskSyncTest.SyncBatchUnexpectedRunningTasks(context.Background(), 1, v1.TaskType_NonQueueAsync)

	//验证 没有返回结果 人工测试验证
}
