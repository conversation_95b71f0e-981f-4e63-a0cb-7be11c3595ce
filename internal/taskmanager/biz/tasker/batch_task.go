package tasker

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/cinience/animus/safe"
	"mpp/internal/taskmanager/biz/dag"
	"mpp/internal/taskmanager/biz/grayscale"

	"mpp/internal/taskmanager/biz/sentinel"
	"mpp/internal/taskmanager/mesh"
	"mpp/pkg/utils"
	"time"

	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/leader"
	"mpp/internal/taskmanager/bouncer/balance"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/qproxy/client"
	"mpp/internal/taskmanager/qproxy/worker"
	"mpp/internal/taskmanager/repository"
	"mpp/internal/taskmanager/tracing/tasker_span"
	"mpp/pkg/tracer/tasker_tracing"
	"mpp/pkg/types/anyvalue"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
)

type BatchTaskBiz struct {
	*CommonTaskBiz
	log  *log.Helper
	repo repository.TaskRepo

	scheduler        common.ScheduleAdapter
	engine           common.EngineAdapter
	tb               *balance.TreeBalance
	leader           *leader.LeaderBiz
	asyncQueueClient *client.AsyncQueueClient
	taskTracer       *tasker_tracing.Tracer
	taskDbChecker    *TaskDBChecker

	sentinelBiz    *sentinel.SentinelBiz
	taskDependency *dag.TaskDependency

	mixRuleConfigBiz *grayscale.MixRuleConfigBiz
}

func NewBatchTaskBiz(conf *conf.Bootstrap, repo repository.TaskRepo, logger log.Logger, scheduler common.ScheduleAdapter, engine common.EngineAdapter, workflow common.WorkflowAdapter,
	mesh *mesh.MeshService, tb *balance.TreeBalance, leader *leader.LeaderBiz, sentinelBiz *sentinel.SentinelBiz, mixRuleConfigBiz *grayscale.MixRuleConfigBiz,
	asyncQueueClient *client.AsyncQueueClient, taskDependency *dag.TaskDependency) (*BatchTaskBiz, error) {
	var err error
	t := &BatchTaskBiz{
		log:              log.NewHelper(logger),
		engine:           engine,
		scheduler:        scheduler,
		tb:               tb,
		leader:           leader,
		sentinelBiz:      sentinelBiz,
		asyncQueueClient: asyncQueueClient,
		taskTracer:       tasker_tracing.NewTracer(tasker_tracing.WithConsumerSpanKind()),
		taskDependency:   taskDependency,
		mixRuleConfigBiz: mixRuleConfigBiz,
	}

	t.repo = repo.CloneWithSpecifiedTable(models.BatchTaskTableName)
	t.CommonTaskBiz, err = NewTaskBizWithSpecifiedRepo(conf, t.repo, nil, scheduler, engine, workflow, mesh, sentinelBiz, nil, nil, nil, logger)
	if err != nil {
		return nil, err
	}

	t.taskDbChecker, err = NewTaskDBChecker(t.repo, leader, false)
	if err != nil {
		return nil, err
	}

	return t, nil
}

func (t *BatchTaskBiz) Execute(ctx context.Context, at *worker.AsyncTask) error {
	var (
		err       error
		taskId    string
		isStopped bool
	)

	propagator := map[string]anyvalue.Value{}

	reservedCtx := ctx
	ctx = context.Background()
	task := &models.Task{}
	err = task.DecodeFromBytes(at.GetData())
	if err != nil {
		return err
	}
	taskId = task.TaskId
	var count uint64 = 0

	if len(task.Metadata) == 0 {
		task, err = t.repo.FindByID(ctx, taskId)
		if err != nil {
			return err
		}
	}

	var exitReason string
	ctx, span := t.taskTracer.Start(ctx, "BatchTaskBiz/Execute", task.Metadata)
	defer func() {
		t.taskTracer.EndWithLogEvent(ctx, span, func() {
			if exitReason == "" && err != nil {
				exitReason = "err." + err.Error()
			}
			tasker_span.SpanRecordWithTaskAndExtendInformation(ctx, "server.BatchTask.Execute", task, map[string]string{
				"exitReason": exitReason,
			})
		}, err)
	}()

	log.Infof("batchTask.Execute taskId: %s, taskTraceID:%s, metadata:%v", taskId, span.SpanContext().TraceID(), task.Metadata)

	// 防止task变量被置为nil
	var tmpTask *models.Task
	// 判断下这是几次执行，是不是重试，如果是重试，则先去查询该信息
	isStopped, tmpTask, err = t.checkTask(ctx, propagator, taskId, count)
	if tmpTask != nil {
		task = tmpTask
	}
	if isStopped {
		exitReason = "task stopped"
		return nil
	}

	duration := 5 * time.Second
	timer := time.NewTicker(duration)
	defer timer.Stop()
	for {
		count++
		select {
		// todo 增加个redis 对taskid订阅，增加实时性
		case <-reservedCtx.Done():
			err = reservedCtx.Err()
			if errors.Is(context.Canceled, err) {
				log.Infof("context canceled. taskId: %s", taskId)
				exitReason = "context done.canceled"
				err = nil
				return nil
			}
			log.Errorf("context done. taskId: %s, err:%v", taskId, err)
			return err
		case <-timer.C:
			isStopped, tmpTask, err = t.checkTask(ctx, propagator, taskId, count)
			if tmpTask != nil {
				task = tmpTask
			}
			if isStopped {
				log.Infof("task stopped. taskId: %s", taskId)
				exitReason = "task stopped"
				return nil
			}
			if err != nil {
				log.Errorf("checkTask failed. taskId: %s, err:%v", taskId, err)
			}
		}
	}
}

func (t *BatchTaskBiz) checkTask(ctx context.Context, propagator map[string]anyvalue.Value, taskId string, count uint64) (isStopped bool, task *models.Task, err error) {
	source := "BatchTaskBiz/checkTask"
	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()

	task, err = t.repo.FindByID(ctx, taskId)
	if err != nil {
		isStopped = false
		return
	}

	// 用来专门记录span的错误
	var spanRecordErr error
	if count == 0 || count%10 == 0 {
		traceCtx, span := t.taskTracer.Start(ctx, "BatchTaskBiz/checkTask", task.Metadata)
		// 手动开启整个链路的tracing
		ctx = t.taskTracer.Extract(traceCtx, task.Metadata)
		defer func() {
			if err != nil && !errors.Is(err, context.Canceled) {
				spanRecordErr = err
			}

			t.taskTracer.EndWithLogEvent(ctx, span, func() {
				tasker_span.SpanRecordWithTaskAndExtendInformation(ctx, "checkTask", task, map[string]string{"runCount": fmt.Sprintf("%d", count)})
			}, spanRecordErr)
		}()
	} else {
		// 除异常的tracing外都抛弃
		ctx = tasker_tracing.WithTraceOnDemandContext(ctx)
	}

	if task.IsBizStatusStopped() && task.IsInternalStopped() {
		isStopped = true
		err = nil
		return
	}

	// 正常状态
	if !task.IsBizStatusStopped() {
		if task.IsInternalPending() {
			isStopped, err = t.SchedulerPendingSubmitTask(ctx, task, source+"/one")
			if err != nil {
				log.Errorf("BatchTaskBiz/checkTask fail in SchedulerPendingSubmitTask. taskId:%s, task:%+v, err:%v", taskId, task, err)
				// 退出，等待后续再次被check
				return
			}
			log.Infof("BatchTaskBiz/checkTask success. taskId:%s, task:%+v", taskId, task)
			// 调度成功，发出dequeue通知
			err = t.sendDequeueNotify(ctx, task)
			if err != nil {
				log.Errorf("BatchTaskBiz/checkTask sendDequeueNotify failed. taskId:%s, task:%+v, err:%v", taskId, task, err)
			}
			// 流控信息添加
			//err = t.sentinelBiz.IncreaseTaskRunningNum(ctx, task)
			if err != nil {
				//打印错误日志，不返回错误
				log.Errorf("checkTask failed in IncreaseTaskRunningNum. taskId:%s ,task:%+v, err:%v", task.TaskId, task, err)
			}
			// 退出，等待后续被check
			return
		}

		runHours := time.Now().Sub(task.LastModified).Hours()
		if runHours > 6 {
			spanRecordErr = fmt.Errorf("task runs too long.(>6 hours)")
			log.Errorf("task runs too long.(>6 hours) taskId:%s, tasker:%+v", task.TaskId, task)
		} else if runHours > 3 {
			spanRecordErr = fmt.Errorf("task runs too long.(>3 hours)")
			log.Warnf("task runs too long.(>3 hours) taskId:%s, tasker:%+v", task.TaskId, task)
		}
		// 后面增加任务平均统计，如果大于平均值

		var isAbnormalTask = false
		if task.Worker == nil {
			// 没被成功调度到，重试调度
			isAbnormalTask = true
			err = fmt.Errorf("worker is nil")
		} else {
			// todo 检查下任务运行情况   t.engine.List()
			var isRunning bool
			isRunning, err = t.isTaskRealRunning(ctx, propagator, task)
			if !isRunning {
				isAbnormalTask = true
				err = fmt.Errorf("engine task lost. stackErr: %v", err)
			}
		}

		if isAbnormalTask && !errors.Is(ctx.Err(), context.Canceled) {
			// 重新调度任务
			t.processAbnormalResult(ctx, source+"/abnormal", task, true, false)
			isStopped = false
			return
		}
	}

	// 上层通知关闭任务
	if task.IsBizStatusStopped() {
		if task.IsInternalRunning() {
			err = t.CancelTask(ctx, task.TaskId)
			isStopped = true
			return
		} else {
			err = nil
			isStopped = true
			return
		}
	}

	if task.IsBizStatusRunning() && !task.IsInternalRunning() {
		// todo 这里重试逻辑都得优化，得增加重试次数，还有对opa tree的进入判断
		err = t.scheduleAndDispatchTask(ctx, source+"/last", task, false)
		if err != nil {
			isStopped = false
			return
		}
		// 流控信息添加
		//err = t.sentinelBiz.IncreaseTaskRunningNum(ctx, task)
		if err != nil {
			//打印错误日志，不返回错误
			log.Errorf("checkTask failed in IncreaseTaskRunningNum. taskId:%s ,task:%+v, err:%v", task.TaskId, task, err)
		}
	}

	return
}

// 调度排队任务
func (t *BatchTaskBiz) SchedulerPendingSubmitTask(ctx context.Context, task *models.Task, source string) (isStopped bool, err error) {
	isStopped = false
	// 检查依赖
	if !t.CheckDependency(ctx, task) {
		log.Infof("BatchTaskBiz/SchedulerPendingSubmitTask failed in CheckDependency and dependency not ready. taskId:%s, task:%+v", task.TaskId, task)
		time.Sleep(time.Second * 1)
		isStopped = false
		err = nil
		return
	}
	// 排队任务进入被执行状态，此时需要开启任务执行和更新状态
	err = t.scheduleAndDispatchTask(ctx, source, task, false)
	//错误处理逻辑：
	//引擎返回结果code是4xx（任务无法正常执行）: 停止任务，发送任务失败通知
	//其余情况: 返回，等待后续被check
	if err != nil {
		t.log.WithContext(ctx).Errorf("SchedulerPendingSubmitTask fail in scheduleAndDispatchTask, task:%v, error:%v", task, err)
		// 如果引擎返回结果code是4xx（任务无法正常执行）
		var engineErr *common.EngineError
		if errors.As(err, &engineErr) && (400 <= engineErr.Code && engineErr.Code < 500) {
			//停止任务，发送任务失败通知
			t.log.WithContext(ctx).Errorf("SchedulerPendingSubmitTask fail in scheduleAndDispatchTask and enter EngineParamError process, task:%v, error:%v", task, err)
			errTemp := t.CancelTask(ctx, task.TaskId)
			if errTemp != nil {
				log.Errorf("SchedulerPendingSubmitTask StopTask failed. taskId:%s, task:%+v, err:%v", task.TaskId, task, errTemp)
				return
			}
			//发送异常结果回调
			//直接返回engine的错误详情
			if task.Notify != "" {
				//task.Result = NewEngineParamErrorResult(task.Worker)
				task.Result = &models.TaskResult{}
				task.Result.Code = uint32(engineErr.Code)
				task.Result.Reason = engineErr.Reason
				task.Result.Message = engineErr.Message

				errTemp = t.sendCompleteNotify(ctx, task)
				if errTemp != nil {
					t.log.WithContext(ctx).Errorf("SchedulerPendingSubmitTask fail in sendCompleteNotify, task:%v, error:%v", task, errTemp)
					return
				}
			}
			isStopped = true
			return
		}
		return
		//错误处理结束
	}
	// 调度成功，发出dequeue通知
	err = t.sendDequeueNotify(ctx, task)
	if err != nil {
		log.Errorf("BatchTaskBiz/checkTask sendDequeueNotify failed. taskId:%s, task:%+v, err:%v", task.TaskId, task, err)
	}
	return
}

func (t *BatchTaskBiz) isTaskRealRunning(ctx context.Context, propagator map[string]anyvalue.Value, task *models.Task) (bool, error) {
	ctx, cancel := context.WithTimeout(ctx, time.Second*3)
	defer cancel()
	_, err := t.scheduler.GetWorker(ctx, task.Worker.Id, task.Metadata)
	if errors.IsNotFound(err) {
		return false, err
	}

	if err == nil {
		err = t.engine.Check(ctx, task, task.Worker)
		if errors.IsNotFound(err) {
			return false, err
		}
	}

	if err != nil {
		var count int64 = 1
		const KeyEngineCheckFailedCount = "engineCheckFailedCount"
		v, ok := propagator[KeyEngineCheckFailedCount]
		if ok {
			count = v.AsInt64() + count
		}

		log.Errorf("engine.List failed. taskId:%s, failedCount:%d, worker:%+v, err:%v", task.TaskId, count, task.Worker, err)
		// 如果check次数超过10次失败，不再容忍错误
		if count > 10 {
			propagator[KeyEngineCheckFailedCount] = anyvalue.Int64Value(0)
			return false, err
		}

		propagator[KeyEngineCheckFailedCount] = anyvalue.Int64Value(count)
		// 容忍一定的错误，防止是worker短暂网络异常
		return true, err
	}

	return true, nil
}

func (t *BatchTaskBiz) SubmitTask(ctx context.Context, task *models.Task) (*models.Task, error) {
	t.log.Infof("submit tasker. %v", task)

	//流控检查
	isEableEnqueue, err := t.sentinelBiz.CheckTaskIsEnableEnqueue(ctx, task)
	if err != nil {
		log.Errorf("CheckTaskIsEnableEnqueue failed. taskId:%v, err:%v", task.TaskId, err)
		return nil, err

	}
	if !isEableEnqueue {
		return nil, errors.New(400, "CheckTaskIsEnableEnqueueFailed", "CheckTaskIsEnableEnqueue failed ")
	}

	// 获取迁移配置
	if task.MigrateConfig == nil {
		task.MigrateConfig = models.GetMigrateConfigByProduct(task.Product)
	}
	// 生成混跑规则配置
	err = t.generateMixRuleConfig(ctx, task)
	if err != nil {
		log.Errorf("SubmitNonQueueTask fail in generateMixRuleConfig, taskId:%v, err:%v", task.TaskId, err)
	}

	notExist, err := t.CommonTaskBiz.saveAndIgnoreOnDuplicate(ctx, task)
	if err != nil {
		log.Errorf("SubmitTask fail in repo save, taskId:%v, err:%v", task.TaskId, err)
		return nil, err
	}

	if !notExist {
		return nil, common.ErrTaskAlreadyExist
	}

	if task.IsInternalRunning() {
		log.Warnf("SubmitTask already running, taskId:%v", task.TaskId)
		return task, nil
	}

	// 判断树状态
	//// todo 入队成功，后续来完善：是否能容忍入队失败，变成内部处理
	//// todo 后面来解决优化成多维度的排队优先级，公平性的问题
	bs, err := task.EncodeLiteToBytes()
	if err != nil {
		return nil, err
	}

	err = t.asyncQueueClient.Enqueue(worker.NewAsyncTask(worker.TaskCommon, task.TaskId, bs))
	if err != nil {
		log.Errorf("SubmitTask Enqueue failed. taskId:%v, err:%v", task.TaskId, err)
		return nil, err
	}
	// 流控信息添加
	err = t.sentinelBiz.IncreaseTaskRunningNum(ctx, task)
	if err != nil {
		//打印错误日志，不返回错误
		log.Errorf("checkTask failed in IncreaseTaskRunningNum. taskId:%s ,task:%+v, err:%v", task.TaskId, task, err)
	}

	return task, nil
}

// 适配TC的submit提交
// 1 不进入排队队列
func (t *BatchTaskBiz) SubmitNonQueueTask(ctx context.Context, task *models.Task) (*models.Task, error) {
	//t.log.Infof("SubmitNonQueueTask tasker. %+v", task)

	// 获取迁移配置
	if task.MigrateConfig == nil {
		task.MigrateConfig = models.GetMigrateConfigByProduct(task.Product)
	}

	// 生成混跑规则配置
	err := t.generateMixRuleConfig(ctx, task)
	if err != nil {
		log.Errorf("SubmitNonQueueTask fail in generateMixRuleConfig, taskId:%v, err:%v", task.TaskId, err)
	}

	//非排队任务直接执行 internal_status : 3-> 2
	task.InternalStatus = 2
	//notExist, err := t.CommonTaskBiz.saveAndIgnoreOnDuplicate(ctx, task)
	existedTask, err := t.CommonTaskBiz.saveAndUpdateOnDuplicate(ctx, task)
	if err != nil {
		log.Errorf("SubmitTask fail in repo save, taskId:%v, err:%v", task.TaskId, err)
		return nil, err
	}
	if existedTask != nil {
		return nil, common.ErrTaskAlreadyExist
	}

	if task.IsInternalRunning() {
		log.Warnf("SubmitTask already running, taskId:%v", task.TaskId)
		return task, nil
	}

	//非排队任务调度
	err = t.ScheduleNonQueueTask(ctx, task, "/SubmitNonQueueTask")
	if err != nil {
		log.Errorf("SubmitNonQueueTask fail in scheduleAndDispatchTask. taskId:%v, err:%v", task.TaskId, err)
		return nil, err
	}
	return task, nil
}

// 生成混跑规则配置
func (t *BatchTaskBiz) generateMixRuleConfig(ctx context.Context, task *models.Task) error {

	//todo 获取混跑灰度配置信息
	isHitMixRule, isMixed, hitRuleConfig, err := t.mixRuleConfigBiz.CheckHitMixRule(ctx, task)
	if err != nil {
		log.Errorf("generateMixRuleConfig fail in mixRuleConfigBiz.ExcuteMixRuleEngine, taskId:%v , isHitMixRule:%v, isMixed:%v, err:%v", task.TaskId, isHitMixRule, isMixed, err)
		return err
	}

	if isHitMixRule && isMixed && hitRuleConfig != nil {
		//hitRuleConfig转string
		hitRuleConfigStr, err := json.Marshal(hitRuleConfig)
		if err != nil {
			log.Errorf("SubmitNonQueueTask fail in hitRuleConfig json.Marshal, taskId:%v, hitRuleConfig:%+v, err:%v", task.TaskId, hitRuleConfig, err)
		}
		task.Metadata[string(models.MetaDataFieldMixConfig)] = string(hitRuleConfigStr)

	} else {
		log.Infof("generateMixRuleConfig not mixed and hitRuleConfig is nil,isHitMixRule:%v, isMixed:%v,taskId:%v ", isHitMixRule, isMixed, task.TaskId)
	}
	return nil
}

// 非排队任务调度
// 1. 维护redis任务依赖表
// 2. 任务下发
// 3. 被依赖任务check拉起
func (t *BatchTaskBiz) ScheduleNonQueueTask(ctx context.Context, task *models.Task, source string) error {
	var err error
	//如果任务是dag任务 添加redis任务依赖表
	if task.IsDagSubTask() {
		//添加redis任务依赖表
		_, err = t.taskDependency.SetTaskDependencyDataByLock(ctx, task)
		if err != nil {
			//todo 1.如果set失败怎么处理？ 2.需要做告警监控
			log.Errorf("ScheduleNonQueueTask fail in SetTaskDependencyDataByLock. jobId:%s, taskId:%v, err:%v", task.JobId, task.TaskId, err)
		}

		//检查任务依赖表中依赖任务状态
		checkResult, err := t.taskDependency.CheckDependencyByRedis(ctx, task.TaskId)
		if err != nil {
			log.Errorf("ScheduleNonQueueTask fail in CheckDependencyByRedis. taskId:%v, err:%v", task.TaskId, err)
			return err
		}
		//依赖任务未就绪,返回成功，等待依赖任务就绪后同步启动任务
		if !checkResult {
			log.Infof("ScheduleNonQueueTask fail in CheckDependencyByRedis and enter dependency not ready process, jobId:%s, taskId:%s, task:%+v", task.JobId, task.TaskId, task)
			//todo 更新任务状态为wait_dependency
			err = t.UpdateTaskToWaitDependency(ctx, task)
			if err != nil {
				log.Errorf("ScheduleNonQueueTask fail in UpdateTaskToWaitDependency. taskId:%v, err:%v", task.TaskId, err)
			}
			return nil
		}
	}

	err = t.scheduleAndDispatchTask(ctx, source+"/ScheduleNonQueueTask", task, false)
	if err != nil {
		// 依赖任务未就绪 修改任务状态为wait_dependency 返回任务提交成功，等待依赖任务就绪
		//kratosErr := common.ToKratosError(err)
		if errors.Is(err, models.ErrDependencyNotReady) {
			log.Infof("ScheduleNonQueueTask fail in scheduleAndDispatchTask and enter dependency not ready process, jobId:%s, taskId:%s, task:%+v", task.JobId, task.TaskId, task)
			//todo 更新任务状态为wait_dependency
			tempErr := t.UpdateTaskToWaitDependency(ctx, task)
			if tempErr != nil {
				log.Errorf("ScheduleNonQueueTask fail in UpdateTaskToWaitDependency. taskId:%v, err:%v", task.TaskId, err)
			}
			return nil
		}
		//其他错误，返回任务提交成功，等待任务内部重试
		log.Warnf("ScheduleNonQueueTask fail in scheduleAndDispatchTask and return success, Waiting for internal retry. taskId:%v, err:%v", task.TaskId, err)
		//return err
	}
	//任务下发成功 更新redis中任务状态 internal_status : 3 -> 1
	if task.IsDagSubTask() {
		_, err = t.taskDependency.SetTaskDependencyRedisByLock(ctx, task.TaskId, task)
		if err != nil {
			//todo 1.如果set失败怎么处理？ 2.需要做告警监控
			log.Errorf("ScheduleNonQueueTask fail in SetTaskDependencyDataByLock. jobId:%s, taskId:%s, err:%v", task.JobId, task.TaskId, err)
		}

		//启协程检查依赖任务是否全部就绪，全部就绪则启动任务
		safe.Go(func() {
			//这里要创建新的ctx，避免超时导致任务被取消
			t.CheckAndScheduleDependentOnTask(context.Background(), task)
		})
	}
	return nil
}

// UpdateTaskToWaitDependency
// 1.更新数据库中任务状态为 wait_dependency
// 2.Redis 中更新任务依赖表中的状态为wait_dependency
func (t *BatchTaskBiz) UpdateTaskToWaitDependency(ctx context.Context, task *models.Task) error {
	// 更新任务状态为 wait_dependency
	task.InternalStatus = models.TaskStatePending
	err := t.repo.UpdateInternalStatus(ctx, task)
	if err != nil {
		log.Errorf("Failed to update task status to wait_dependency. taskId: %v, err: %v", task.TaskId, err)
		return err
	}

	// 更新任务依赖表中任务状态
	_, err = t.taskDependency.SetTaskDependencyRedisByLock(ctx, task.TaskId, task)
	if err != nil {
		log.Errorf("Failed to set task dependency data in Redis. taskId: %v, err: %v", task.TaskId, err)
		return err
	}

	return nil
}

// 任务完成触发被依赖任务检查
// 如果这些任务的依赖已全部就绪，则启动该任务
func (t *BatchTaskBiz) CheckAndScheduleDependentOnTask(ctx context.Context, task *models.Task) error {
	var err error
	depOnSet, err := t.taskDependency.GetDependentOnTaskSet(ctx, task.TaskId)
	if err != nil {
		log.Errorf("CheckAndScheduleDependentOnTask fail in GetDependentOnTaskSet. taskId:%v, err:%v", task.TaskId, err)
		return err
	}
	if len(depOnSet) <= 0 {
		log.Infof("CheckAndScheduleDependentOnTask taskId:%v, depOnSet is empty", task.TaskId)
		return nil
	}
	log.Infof("CheckAndScheduleDependentOnTask start taskId:%v, depOnSet:%v", task.TaskId, depOnSet)

	for depOntaskId := range depOnSet {
		checkResult, err := t.taskDependency.CheckDependencyByRedis(ctx, depOntaskId)
		if err != nil {
			log.Errorf("CheckAndScheduleDependentOnTask fail in CheckDependencyByRedis. taskId:%s, depOntaskId:%s, jobId:%s, err:%v", task.TaskId, depOntaskId, task.JobId, err)
			return err
		}
		if checkResult {
			//查询数据库中任务
			depOnTask, err := t.repo.FindByID(ctx, depOntaskId)
			if err != nil {
				log.Errorf("CheckAndScheduleDependentOnTask fail in FindByID. taskId:%s, depOntaskId:%s, jobId:%s, err:%v", task.TaskId, depOntaskId, task.JobId, err)
				return err
			}
			if depOnTask.IsBizStatusStopped() {
				log.Infof("CheckAndScheduleDependentOnTask taskId:%v, depOnTask is stil stopped", depOntaskId)
				continue
			} else if depOnTask.IsBizStatusRunning() && depOnTask.IsInternalRunning() {
				log.Infof("CheckAndScheduleDependentOnTask taskId:%v, depOnTask is stil running", depOntaskId)
				continue
			}

			//启动任务
			log.Infof("CheckAndScheduleDependentOnTask taskId:%s jobId:%s, start running depOnTask", depOntaskId, task.JobId)
			err = t.scheduleAndDispatchTask(ctx, "/CheckAndScheduleDependentOnTask", depOnTask, false)
			if err != nil {
				log.Errorf("CheckAndScheduleDependentOnTask fail in scheduleAndDispatchTask. taskId:%v, err:%v", depOntaskId, err)
				return nil
			}
		}

	}

	return nil
}

func (t *BatchTaskBiz) CancelTask(ctx context.Context, taskId string) error {

	var err error
	t.log.WithContext(ctx).Infof("Cancel tasker, taskId:%s", taskId)

	task, err := t.repo.FindByID(ctx, taskId)
	if err != nil {
		t.log.WithContext(ctx).Errorf("CancelTask fail in FindByID, taskId:%s", taskId)
		return err
	}
	ctx, span := t.taskTracer.Start(ctx, "CancelTask", task.Metadata)
	defer func() {
		t.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTaskAndExtendInformation(ctx, "", task, nil)
		}, err)
	}()

	ctx = utils.SetTaskProduct(ctx, task.Product)
	if !task.IsBizStatusRunning() && !task.IsInternalRunning() {
		t.log.WithContext(ctx).Warnf("CancelTask tasker already stopped, taskId:%s", taskId)
		return nil
	}
	//如果任务状态为running，更新任务biz状态为stopped
	if task.IsBizStatusRunning() {
		task.SetBizStatus(models.TaskStateStopped)

		err = t.repo.UpdateBizStatus(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Errorf("CancelTask fail in repo update biz stopped, taskId:%s", taskId)
			return err
		}
	}

	//如果任务正在排队
	if task.IsTaskEnqueue() {
		err = t.CancelEnqueueTask(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Errorf("CancelTask fail in CancelEnqueueTask, taskId:%s", taskId)
			return err
		}
	}

	if !task.IsInternalRunning() {
		t.log.WithContext(ctx).Warnf("CancelTask tasker already stooped, taskId:%s", taskId)
		return nil
	}

	err = t.cancelTask(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Errorf("CancelTask failed in cancelTask, taskId:%s, err:%v", task.TaskId, err)

		return err
	}

	if t.cacheDb != nil {
		rst := t.cacheDb.Del(ctx, task.EncodeKVKey())
		if rst != nil && rst.Err() != nil {
			t.log.Errorf("CancelTask fail in cacheDb, taskId:%s, err:%v", task.TaskId, rst.Err())
		}
	}

	//如果任务是dag任务 更新redis中任务状态
	if task.IsDagSubTask() {
		//修改中redis中任务状态
		_, err = t.taskDependency.SetTaskDependencyRedisByLock(ctx, task.TaskId, task)
		if err != nil {
			//todo 1.如果set失败怎么处理？ 2.需要做告警监控 qinxin
			log.Errorf("CancelTask fail in SetTaskDependencyDataByLock. taskId:%v, err:%v", task.TaskId, err)
		}
	}

	return nil
}

// 取消排队任务 internal_status=3
// 1.任务出队
// 2.更新internal状态为stopped
func (t *BatchTaskBiz) CancelEnqueueTask(ctx context.Context, task *models.Task) error {
	var (
		err error
	)
	// 取消排队任务
	//todo 这里如果因为队列中没有任务失败，需要做进一步处理
	err = t.asyncQueueClient.CancelTask(task.TaskId)
	if err != nil {
		log.Errorf("CancelEnqueueTask fail in asyncQueueClient.CancelTask. taskId:%v, err:%v", task.TaskId, err)
		//继续停止任务
	}
	// 流控信息更新
	err = t.sentinelBiz.DecreaseTaskRunningNum(ctx, task)
	if err != nil {
		//打印错误日志，不返回错误
		log.Errorf("checkTask failed in DecreaseTaskRunningNum. taskId:%s ,task:%+v, err:%v", task.TaskId, task, err)
	}

	// 更新internal状态为stopped
	task.InternalStatus = models.TaskStateStopped
	task, err = t.updateInternalStopped(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Warn("stopTask fail in updateInternalStopped")
		e := errors.FromError(err)
		return &common.DbError{
			Code:    e.Code,
			Reason:  "UpdateInternalStoppedFail",
			Message: err.Error(),
		}
	}
	if err != nil {
		log.Errorf("CancelEnqueueTask fail in stopTask. taskId:%v, err:%v", task.TaskId, err)
		return err
	}
	return nil
}

func (t *BatchTaskBiz) GetTask(ctx context.Context, taskId string) (*models.Task, error) {
	task, err := t.repo.FindByID(ctx, taskId)
	if err != nil {
		log.Errorf("GetTask failed. taskId:%v, err:%v", taskId, err)
		return nil, err
	}

	return task, nil
}

func (t *BatchTaskBiz) GetRunningTasks(ctx context.Context, engineModels []string) ([]*models.Task, error) {
	tasks, err := t.repo.FindRunning(ctx, engineModels)
	if err != nil {
		t.log.WithContext(ctx).Errorf("GetRunningTasks fail in FindRunning, err:%v", err)
		return nil, err
	}
	return tasks, nil
}

func (t *BatchTaskBiz) CompleteTask(ctx context.Context, request *CompleteRequest) error {
	taskId := request.TaskID
	isCompleted, err := t.CommonTaskBiz.CompleteTask(ctx, request)
	//如果任务回调完成，则取消redis队列
	if err == nil && isCompleted {
		qcErr := t.asyncQueueClient.CancelTask(taskId)
		if qcErr != nil {
			log.Errorf("asyncQueueClient.CancelTask failed. taskId:%v, err:%v", taskId, qcErr)
		}
		log.Infof("completeTask cancel redisQueue success. taskId:%v", taskId)
	}

	//如果任务类型是noQueue且是dag子任务，更新redis中任务依赖表
	task, err := t.repo.FindByID(ctx, request.TaskID)
	if err != nil {
		t.log.WithContext(ctx).Error("batch/CompleteTask fail in FindByID ", err)
		return err
	}
	if task.TaskType == models.NonQueueAsyncTaskType && task.IsDagSubTask() {
		_, err = t.taskDependency.SetTaskDependencyRedis(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Errorf("completeTask fail in UpdateDagTask, taskId:%s, err:%v", task.TaskId, err)
		}
	}
	return err
}

// check task dependency is ready or not.
// UT@TestCheckDependency
// 1. 依赖任务是否存在
// 2. 依赖任务是否处于running状态
func (t *BatchTaskBiz) CheckDependency(ctx context.Context, task *models.Task) bool {
	// Create dependencies for task.
	if len(task.Dependencies) != 0 {
		// If all upstream tasks have not reached the expected state, they cannot be scheduled。
		for i := range task.Dependencies {
			if !t.checkTaskDependency(ctx, task, i) {
				if time.Now().Sub(task.GmtCreate) > time.Minute {
					t.log.WithContext(ctx).Warnf("checkDependency and task wait too long , taskId:%s，TaskDependency:%s, CreatedTime:%v", task.TaskId, task.Dependencies[i].TaskId, task.GmtCreate)
				}
				return false
			}
		}

	}
	return true
}

//	get task dependencies info.
//
// UT@TestCheckDependency
func (t *BatchTaskBiz) checkTaskDependency(ctx context.Context, task *models.Task, i int) bool {
	log.Infof("start checkTaskDependency, taskId:%s，count:%d, TaskDependency:%+v", task.TaskId, i, task.Dependencies[i])
	dependencyTaskId := task.Dependencies[i].TaskId
	if dependencyTaskId != "" {
		existTask, err := t.repo.FindByID(ctx, dependencyTaskId)
		if err != nil && !errors.Is(err, common.ErrTaskNotFoundError) {
			//依赖任务查询失败
			t.log.WithContext(ctx).Errorf("checkTaskDependency fail in repo find, taskId:%s, TaskDependency:%s, err:%v", task.TaskId, dependencyTaskId, err)
			return false
		}
		if existTask == nil {
			//依赖任务不存在
			log.Infof("checkTaskDependency and dependency Task not exist, taskId:%s, TaskDependency:%s, err:%v", task.TaskId, task.Dependencies[i].TaskId, err)
			return false
		} else {
			//依赖任务存在,且任务状态为running，则可以调度
			if existTask.IsBizStatusRunning() && existTask.IsInternalRunning() {
				log.Infof("checkTaskDependency success and dependency Task is running, taskId:%s，TaskDependency:%s, err:%v", task.TaskId, task.Dependencies[i].TaskId, err)
				return true
			}
		}
	}
	log.Infof("checkTaskDependency and no dependency Task, taskId:%s, TaskDependency:%+v", task.TaskId, task.Dependencies[i])
	return false
}
