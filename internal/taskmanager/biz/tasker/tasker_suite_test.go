package tasker

import (
	"context"
	"mpp/internal/taskmanager/mocks"
	"testing"

	"github.com/golang/mock/gomock"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

func TestTasker(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "tasker test")
}

var ctl *gomock.Controller
var cleaner func()
var ctx context.Context
var taskRepoMock *mocks.MockTaskRepo
var schedulerAdapterMock *mocks.MockScheduleAdapter
var engineAdapterMock *mocks.MockEngineAdapter
var sentinelRepoMock *mocks.MockTaskSentinelConfigRepo

var _ = BeforeEach(func() {
	ctl = gomock.NewController(GinkgoT())
	cleaner = ctl.Finish
	taskRepoMock = mocks.NewMockTaskRepo(ctl)
	schedulerAdapterMock = mocks.NewMockScheduleAdapter(ctl)
	schedulerAdapterMock.EXPECT().GetAllSchedulerTags().Return([]string{"mpp-taskscheduler.http"}).AnyTimes()
	engineAdapterMock = mocks.NewMockEngineAdapter(ctl)
	sentinelRepoMock = mocks.NewMockTaskSentinelConfigRepo(ctl)
	ctx = context.Background()
})
var _ = AfterEach(func() {
	// remove any mocks
	cleaner()
})
