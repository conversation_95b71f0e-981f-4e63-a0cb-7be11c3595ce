package tasker

import (
	"context"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/mohae/deepcopy"
	. "github.com/onsi/ginkgo/v2"
	goredis "github.com/redis/go-redis/v9"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/models"
	"time"
)

/**
 * @Author: qinxin
 * @Date: 2024/9/25 01:36
 * @Desc:
 * @Version 1.0
 */

var taskStatusSyncBiz *TaskStatusSync

var _ = Describe("taskStatusSyncBiz Tests", func() {
	var (
		ctx context.Context

		missTasks []*models.Task
	)

	BeforeEach(func() {
		taskStatusSyncBiz.repo = taskRepoMock
		taskStatusSyncBiz.engine = engineAdapterMock
		ctx = context.Background()

		mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		missTasks = []*models.Task{
			mockTask2,
		}
	})
	It("test taskStatusSync and scheduleAndDispatchTask return fail", func() {

		taskRepoMock.EXPECT().ListMissingRunningTasks(ctx).Return(missTasks, nil).Times(1)

		defer mockey.Mock((*models.Task).IsRecentlyUpdated).Return(false).Build().UnPatch()
		defer mockey.Mock((*CommonTaskBiz).scheduleAndDispatchTask).Return(common.ErrInternalError).Build().UnPatch()

		taskStatusSyncBiz.SyncMissingRunningTasks(ctx, 10) //

	})
	It("test taskStatusSync and ListMissingRunningTasks return fail", func() {
		// Mock ListAllWorker to return workers
		taskRepoMock.EXPECT().ListMissingRunningTasks(ctx).Return(nil, common.ErrInternalError).Times(1)
		taskStatusSyncBiz.SyncMissingRunningTasks(ctx, 10)
	})

	It("test taskStatusSync and ListMissingRunningTasks return nil", func() {

		taskRepoMock.EXPECT().ListMissingRunningTasks(ctx).Return(nil, nil).Times(1)
		taskStatusSyncBiz.SyncMissingRunningTasks(ctx, 10)
	})

	It("test taskStatusSync and IsRecentlyUpdated return true", func() {
		//
		taskRepoMock.EXPECT().ListMissingRunningTasks(ctx).Return(missTasks, nil).Times(1)
		defer mockey.Mock((*models.Task).IsRecentlyUpdated).Return(true).Build().UnPatch()
		taskStatusSyncBiz.SyncMissingRunningTasks(ctx, 10)
	})
})

var _ = Describe("SyncUnexpectedRunningTasks Tests", func() {
	var (
		ctx context.Context

		missTasks []*models.Task
	)

	BeforeEach(func() {
		taskStatusSyncBiz.repo = taskRepoMock
		taskStatusSyncBiz.engine = engineAdapterMock
		ctx = context.Background()

		mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask2.BizStatus = 2
		mockTask2.InternalStatus = 1
		missTasks = []*models.Task{
			mockTask2,
		}
	})
	It("test SyncUnexpectedRunningTasks and scheduleAndDispatchTask return fail", func() {

		taskRepoMock.EXPECT().ListUnexpectedRunningTasks(gomock.Any()).Return(missTasks, nil).Times(1)
		taskRepoMock.EXPECT().UpdateInternalStatus(gomock.Any(), gomock.Any()).Return(common.ErrInternalError).Times(1)

		defer mockey.Mock((*models.Task).IsRecentlyUpdated).Return(false).Build().UnPatch()
		defer mockey.Mock((*models.Task).IsEngineAlreadyStopped).Return(false).Build().UnPatch()
		defer mockey.Mock((*CommonTaskBiz).stopTask).Return(common.ErrInternalError).Build().UnPatch()

		//defer mockey.Mock((*CommonTaskBiz).scheduleAndDispatchTask).Return(common.ErrInternalError).Build().UnPatch()

		taskStatusSyncBiz.SyncUnexpectedRunningTasks(ctx, 10) //
	})

	It("test SyncUnexpectedRunningTasks and ListUnexpectedRunningTasks return fail", func() {
		// Mock ListAllWorker to return workers
		taskRepoMock.EXPECT().ListUnexpectedRunningTasks(gomock.Any()).Return(nil, common.ErrInternalError).Times(1)
		taskStatusSyncBiz.SyncUnexpectedRunningTasks(ctx, 10)
	})
	It("test SyncUnexpectedRunningTasks and ListUnexpectedRunningTasks return nil", func() {
		taskRepoMock.EXPECT().ListUnexpectedRunningTasks(gomock.Any()).Return(nil, nil).Times(1)
		taskStatusSyncBiz.SyncUnexpectedRunningTasks(ctx, 10)
	})
	It("test SyncUnexpectedRunningTasks and IsRecentlyUpdated return true", func() {
		taskRepoMock.EXPECT().ListUnexpectedRunningTasks(gomock.Any()).Return(missTasks, nil).Times(1)
		defer mockey.Mock((*models.Task).IsRecentlyUpdated).Return(true).Build().UnPatch()
		taskStatusSyncBiz.SyncUnexpectedRunningTasks(ctx, 10)
	})
	It("test SyncUnexpectedRunningTasks and IsEngineAlreadyStopped return true", func() {
		taskRepoMock.EXPECT().ListUnexpectedRunningTasks(gomock.Any()).Return(missTasks, nil).Times(1)
		defer mockey.Mock((*models.Task).IsRecentlyUpdated).Return(false).Build().UnPatch()
		defer mockey.Mock((*CommonTaskBiz).replyTaskAndUpdateInternalStopped).Return(common.ErrInternalError).Build().UnPatch()
		taskStatusSyncBiz.SyncUnexpectedRunningTasks(ctx, 10)
	})
})

var _ = Describe("SyncRouterTasks Tests", func() {
	var (
		ctx context.Context

		missTasks []*models.Task
	)

	BeforeEach(func() {
		taskStatusSyncBiz.repo = taskRepoMock
		taskStatusSyncBiz.engine = engineAdapterMock
		taskStatusSyncBiz.cacheDb = goredis.NewUniversalClient(&goredis.UniversalOptions{})

		ctx = context.Background()

		mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask2.BizStatus = 2
		mockTask2.InternalStatus = 1
		missTasks = []*models.Task{
			mockTask2,
		}
	})
	It("test SyncRouterTasks ", func() {

		taskRepoMock.EXPECT().ListBizRunningTasks(gomock.Any(), gomock.Any()).Return(missTasks, nil).Times(1)
		taskRepoMock.EXPECT().ListBizRunningTasks(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)

		//defer mockey.Mock((*CommonTaskBiz).scheduleAndDispatchTask).Return(common.ErrInternalError).Build().UnPatch()

		taskStatusSyncBiz.SyncRouterTasks(ctx, 10) //
	})

	It("test SyncRouterTasks  and return ListBizRunningTasks fail 1", func() {

		taskRepoMock.EXPECT().ListBizRunningTasks(gomock.Any(), gomock.Any()).Return(nil, common.ErrInternalError).Times(1)

		//defer mockey.Mock((*CommonTaskBiz).scheduleAndDispatchTask).Return(common.ErrInternalError).Build().UnPatch()

		taskStatusSyncBiz.SyncRouterTasks(ctx, 10) //
	})

	It("test SyncRouterTasks  and return ListBizRunningTasks fail 2", func() {

		taskRepoMock.EXPECT().ListBizRunningTasks(gomock.Any(), gomock.Any()).Return(missTasks, nil).Times(1)
		taskRepoMock.EXPECT().ListBizRunningTasks(gomock.Any(), gomock.Any()).Return(nil, common.ErrTaskNotFoundError).Times(1)
		//defer mockey.Mock((*CommonTaskBiz).scheduleAndDispatchTask).Return(common.ErrInternalError).Build().UnPatch()

		taskStatusSyncBiz.SyncRouterTasks(ctx, 10) //
	})

	It("test SyncRouterTasks  and return ListBizRunningTasks nil", func() {

		taskRepoMock.EXPECT().ListBizRunningTasks(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
		taskRepoMock.EXPECT().ListBizRunningTasks(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
		//defer mockey.Mock((*CommonTaskBiz).scheduleAndDispatchTask).Return(common.ErrInternalError).Build().UnPatch()

		taskStatusSyncBiz.SyncRouterTasks(ctx, 10) //
	})

})

//
//var _ = Describe("startSyncMissingRunningTasks Tests", func() {
//	var (
//		ctx context.Context
//	)
//
//	BeforeEach(func() {
//		ctx = context.Background()
//	})
//
//	It("startSyncMissingRunningTasks and ctx return fail", func() {
//		// Mock ListAllWorker to return workers
//
//		defer mockey.Mock((*leader.LeaderBiz).IsLeader).Return(true).Build().UnPatch()
//		defer mockey.Mock((*TaskStatusSync).SyncMissingRunningTasks).Return().Build().UnPatch()
//		ctx, _ := context.WithTimeout(ctx, 1*time.Millisecond)
//		time.Sleep(1 * time.Millisecond)
//
//		taskStatusSyncBiz.startSyncMissingRunningTasks(ctx) // Should succeed
//		// You can add more assertions as necessary
//	})
//
//})

//var _ = Describe("startSyncUnexpectedRunningTasks Tests", func() {
//	var (
//		ctx context.Context
//	)
//
//	BeforeEach(func() {
//		ctx = context.Background()
//	})
//
//	It("startSyncMissingRunningTasks and ctx return fail", func() {
//		// Mock ListAllWorker to return workers
//
//		defer mockey.Mock((*leader.LeaderBiz).IsLeader).Return(true).Build().UnPatch()
//		defer mockey.Mock((*TaskStatusSync).SyncUnexpectedRunningTasks).Return().Build().UnPatch()
//		ctx, _ := context.WithTimeout(ctx, 1*time.Millisecond)
//		time.Sleep(1 * time.Millisecond)
//		taskStatusSyncBiz.startSyncUnexpectedRunningTasks(ctx) // Should succeed
//		// You can add more assertions as necessary
//	})
//
//})

var _ = Describe("startSyncRouterTasks Tests", func() {
	var (
		ctx context.Context
	)

	BeforeEach(func() {
		ctx = context.Background()
	})

	It("startSyncRouterTasks and ctx return fail", func() {
		// Mock ListAllWorker to return workers

		//defer mockey.Mock((*leader.LeaderBiz).IsLeader).Return(true).Build().UnPatch()
		defer mockey.Mock((*TaskStatusSync).SyncRouterTasks).Return().Build().UnPatch()
		ctx, _ := context.WithTimeout(ctx, 1*time.Millisecond)
		time.Sleep(1 * time.Millisecond)
		taskStatusSyncBiz.startSyncRouterTasks(ctx) // Should succeed
		// You can add more assertions as necessary
	})

})
