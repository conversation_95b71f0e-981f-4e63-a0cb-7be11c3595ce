package tasker

import (
	"context"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/mohae/deepcopy"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/models"
	v1 "proto.mpp/api/common/v1"
	"time"
)

/**
 * @Author: qinxin
 * @Date: 2024/9/24 16:21
 * @Desc:
 * @Version 1.0
 */

var (
	schedulerResourceSyncBiz *SchedulerResourceSync
)

var _ = Describe("schedulerResourceSync Tests", func() {
	var (
		ctx                context.Context
		mockResources      []*models.Resource
		workerRunningTasks []*models.Task
	)

	BeforeEach(func() {
		schedulerResourceSyncBiz.repo = taskRepoMock
		schedulerResourceSyncBiz.scheduler = schedulerAdapterMock

		mockResources = []*models.Resource{
			{JobId: "123test", TaskId: "123test", WorkerId: "worker-1"},
		}
		workerRunningTasks = []*models.Task{
			defaultModelTask,
		}
		schedulerAdapterMock.EXPECT().GetAllSchedulerTags().Return(nil).AnyTimes()
	})
	It("should synchronize resource task status successfully", func() {
		// Mock ListAllWorker to return workers
		schedulerAdapterMock.EXPECT().ListResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockResources, nil).Times(2)
		taskRepoMock.EXPECT().ListBizRunningTasksExceptDagJob(ctx).Return(workerRunningTasks, nil).Times(1)

		defer mockey.Mock((*SchedulerResourceSync).processMissFreeResources).Return().Build().UnPatch()
		defer mockey.Mock((*SchedulerResourceSync).processUnExpectedRunningResources).Return().Build().UnPatch()

		err := schedulerResourceSyncBiz.SyncResource(ctx, v1.TaskType_Stream) // Should succeed
		// You can add more assertions as necessary
		Ω(err).ShouldNot(HaveOccurred())
	})

	It("should synchronize resource and ListResource return err", func() {
		schedulerAdapterMock.EXPECT().ListResource(ctx, gomock.Any(), gomock.Any()).Return(nil, common.ErrInternalError).Times(2)
		err := schedulerResourceSyncBiz.SyncResource(ctx, v1.TaskType_Stream) // Should succeed
		// You can add more assertions as necessary
		Ω(err).Should(HaveOccurred())

	})
	It("should synchronize resource and ListAllBizRunningTasks return err", func() {
		schedulerAdapterMock.EXPECT().ListResource(ctx, gomock.Any(), gomock.Any()).Return(mockResources, nil).Times(2)
		taskRepoMock.EXPECT().ListBizRunningTasksExceptDagJob(ctx).Return(nil, common.ErrInternalError).Times(1)
		err := schedulerResourceSyncBiz.SyncResource(ctx, v1.TaskType_Stream) // Should succeed
		// You can add more assertions as necessary
		Ω(err).Should(HaveOccurred())

	})

})

var _ = Describe("processMissFreeResources Tests", func() {
	var (
		ctx                context.Context
		mockResources      []*models.Resource
		workerRunningTasks []*models.Task
		resourceMap        map[string]*models.Resource
		runningTaskMap     map[string]*models.Task
		taskRepoMock2      *models.Task
	)

	BeforeEach(func() {
		ctx = context.Background()
		schedulerResourceSyncBiz.repo = taskRepoMock
		schedulerResourceSyncBiz.scheduler = schedulerAdapterMock

		mockResources = []*models.Resource{
			{JobId: "123test", TaskId: "123test", WorkerId: "worker-1"},
		}
		taskRepoMock2 = deepcopy.Copy(defaultModelTask).(*models.Task)
		workerRunningTasks = []*models.Task{
			taskRepoMock2,
		}
		resourceMap = make(map[string]*models.Resource)
		runningTaskMap = make(map[string]*models.Task)
		for _, resource := range mockResources {
			resourceMap[resource.TaskId] = resource
		}
		for _, task := range workerRunningTasks {
			runningTaskMap[task.TaskId] = task
		}
	})
	It("processMissFreeResources and FreeResource return fail", func() {
		// Mock ListAllWorker to return workers
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(taskRepoMock2, nil).Times(1)
		schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(common.ErrInternalError).Times(1)

		defer mockey.Mock((*models.Task).IsRecentlyUpdated).Return(false).Build().UnPatch()
		schedulerResourceSyncBiz.processMissFreeResources(ctx, resourceMap, make(map[string]*models.Task)) // Should succeed
		// You can add more assertions as necessary
	})

	It("processMissFreeResources and FindByID return fail", func() {
		// Mock ListAllWorker to return workers
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(nil, common.ErrInternalError).Times(1)

		schedulerResourceSyncBiz.processMissFreeResources(ctx, resourceMap, make(map[string]*models.Task)) // Should succeed
		// You can add more assertions as necessary
	})

	It("processMissFreeResources and FindByID return nil", func() {
		// Mock ListAllWorker to return workers
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
		schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(common.ErrInternalError).Times(1)

		defer mockey.Mock((*models.Task).IsRecentlyUpdated).Return(false).Build().UnPatch()
		schedulerResourceSyncBiz.processMissFreeResources(ctx, resourceMap, make(map[string]*models.Task)) // Should succeed
		// You can add more assertions as necessary
	})

})

var _ = Describe("processUnExpectedRunningResources Tests", func() {
	var (
		ctx                context.Context
		mockResources      []*models.Resource
		workerRunningTasks []*models.Task
		resourceMap        map[string]*models.Resource
		runningTaskMap     map[string]*models.Task
		taskRepoMock2      *models.Task
		lostResourceMap    map[string]*models.Resource
	)

	BeforeEach(func() {
		ctx = context.Background()
		schedulerResourceSyncBiz.repo = taskRepoMock
		schedulerResourceSyncBiz.scheduler = schedulerAdapterMock

		mockResources = []*models.Resource{
			{JobId: "123test", TaskId: "123test", WorkerId: "worker-1"},
		}
		taskRepoMock2 = deepcopy.Copy(defaultModelTask).(*models.Task)
		taskRepoMock2.EngineModel = models.LiveTranscode
		taskRepoMock2.InternalStatus = 1
		workerRunningTasks = []*models.Task{
			taskRepoMock2,
		}
		resourceMap = make(map[string]*models.Resource)
		lostResourceMap = make(map[string]*models.Resource)
		runningTaskMap = make(map[string]*models.Task)
		for _, resource := range mockResources {
			resourceMap[resource.TaskId] = resource
		}
		for _, task := range workerRunningTasks {
			runningTaskMap[task.TaskId] = task
		}
		for _, resource := range mockResources {
			lostResourceMap[resource.TaskId] = resource
		}
	})
	It("processUnExpectedRunningResources and UpdateInternalStatus return fail", func() {
		// Mock ListAllWorker to return workers
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(taskRepoMock2, nil).Times(1)
		taskRepoMock.EXPECT().UpdateInternalStatus(gomock.Any(), gomock.Any()).Return(common.ErrInternalError).Times(1)

		defer mockey.Mock((*models.Task).IsRecentlyUpdated).Return(false).Build().UnPatch()
		schedulerResourceSyncBiz.processUnExpectedRunningResources(ctx, make(map[string]*models.Resource), runningTaskMap) // Should succeed
		// You can add more assertions as necessary
	})

	It("processUnExpectedRunningResources and FindByID return fail", func() {
		// Mock ListAllWorker to return workers
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(nil, common.ErrInternalError).Times(1)

		schedulerResourceSyncBiz.processUnExpectedRunningResources(ctx, make(map[string]*models.Resource), runningTaskMap) // Should succeed
		// You can add more assertions as necessary
	})

	It("processUnExpectedRunningResources and FindByID return nil", func() {
		// Mock ListAllWorker to return workers
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)

		schedulerResourceSyncBiz.processUnExpectedRunningResources(ctx, make(map[string]*models.Resource), runningTaskMap) // Should succeed
		// You can add more assertions as necessary
	})

	It("processUnExpectedRunningResources and IsRecentlyUpdated return true", func() {
		// Mock ListAllWorker to return workers
		taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(taskRepoMock2, nil).Times(1)

		defer mockey.Mock((*models.Task).IsRecentlyUpdated).Return(true).Build().UnPatch()
		schedulerResourceSyncBiz.processUnExpectedRunningResources(ctx, make(map[string]*models.Resource), runningTaskMap) // Should succeed
		// You can add more assertions as necessary
	})

	It("processLostResources and IsRecentlyUpdated return false", func() {
		// Mock ListAllWorker to return workers
		//taskRepoMock.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(taskRepoMock2, nil).Times(1)
		taskRepoMock.EXPECT().UpdateInternalStatus(gomock.Any(), gomock.Any()).Return(nil).Times(1)

		defer mockey.Mock((*models.Task).IsRecentlyUpdated).Return(false).Build().UnPatch()
		schedulerAdapterMock.EXPECT().FreeResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		schedulerResourceSyncBiz.processLostResources(ctx, lostResourceMap, runningTaskMap) // Should succeed
		// You can add more assertions as necessary
	})

})

var _ = Describe("BackgroundMethod Tests", func() {
	var (
		ctx context.Context
	)

	BeforeEach(func() {
		ctx = context.Background()
	})
	It("BackgroundMethod and SyncResource return fail", func() {
		// Mock ListAllWorker to return workers

		//defer mockey.Mock((*leader.LeaderBiz).IsLeader).Return(true).Build().UnPatch()
		defer mockey.Mock((*SchedulerResourceSync).SyncResource).Return(common.ErrInternalError).Build().UnPatch()

		schedulerResourceSyncBiz.BackgroundMethod(ctx, v1.TaskType_Stream, 1) // Should succeed
		// You can add more assertions as necessary
	})

	It("BackgroundMethod and ctx return fail", func() {
		// Mock ListAllWorker to return workers

		//defer mockey.Mock((*leader.LeaderBiz).IsLeader).Return(true).Build().UnPatch()
		defer mockey.Mock((*SchedulerResourceSync).SyncResource).Return(nil).Build().UnPatch()
		ctx, _ := context.WithTimeout(ctx, 1*time.Millisecond)
		time.Sleep(1 * time.Millisecond)
		schedulerResourceSyncBiz.BackgroundMethod(ctx, v1.TaskType_Stream, 1) // Should succeed
		// You can add more assertions as necessary
	})

})
