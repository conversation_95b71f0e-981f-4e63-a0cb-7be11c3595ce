package tasker

import (
	"context"
	"encoding/json"
	"mpp/internal/taskmanager/biz/grayscale"
	"strings"
	"time"

	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/models"

	"github.com/avast/retry-go"
	"github.com/cinience/animus/registry"
	"github.com/go-kratos/kratos/v2/log"
)

type SimpleTaskBiz struct {
	log              *log.Helper
	engine           common.EngineAdapter
	scheduler        common.ScheduleAdapter
	mixRuleConfigBiz *grayscale.MixRuleConfigBiz

	discovery registry.Discovery
}

func NewSimpleTaskBiz(logger log.Logger, engine common.EngineAdapter, scheduler common.ScheduleAdapter, mixRuleConfigBiz *grayscale.MixRuleConfigBiz) (*SimpleTaskBiz, error) {
	t := &SimpleTaskBiz{log: log.NewHelper(logger), engine: engine, scheduler: scheduler, mixRuleConfigBiz: mixRuleConfigBiz}
	t.discovery = registry.NewFailOverDiscovery(common.NewSchedulerDiscovery(scheduler))
	return t, nil
}

func (t *SimpleTaskBiz) Invoke(ctx context.Context, task *models.Task) (*models.SimpleTaskResult, error) {
	var err error
	var rst *models.SimpleTaskResult
	serviceNameList := []string{task.Product, task.EngineModel, task.Tag}

	//混跑引擎测试
	//if strings.Contains(task.Tag, common.Mixed_Tag_Prefix) {
	//	serviceNameList = []string{common.Mixed_Product, common.Mixed_Engine, common.Mixed_Agent_Tag}
	//	log.Infof("Invoke mix test, update serviceNameList:%s, taskId:%s", serviceNameList, task.TaskId)
	//}

	//todo 获取混跑灰度配置信息
	isHit, isMixed, hitRuleConfig, err := t.mixRuleConfigBiz.CheckHitMixRule(ctx, task)
	if err != nil {
		log.Errorf("Invoke fail in CheckHitMixRule, taskId:%v, err:%v", task.TaskId, err)
		return nil, err
	}
	if isHit && isMixed && hitRuleConfig != nil {
		mixProduct := hitRuleConfig.Product
		mixEngineModel := hitRuleConfig.EngineModel
		mixTag := hitRuleConfig.Tag
		if mixProduct != "" && mixEngineModel != "" && mixTag != "" {
			serviceNameList = []string{mixProduct, mixEngineModel, mixTag}
			log.Infof("Invoke mix test, update serviceNameList:%s, taskId:%s", serviceNameList, task.TaskId)
		}
	}

	serviceName := strings.Join(serviceNameList, ",")
	err = retry.Do(
		func() error {
			item, err := t.discovery.PickService(ctx, serviceName)
			if err != nil {
				log.Errorf("Invoke fail in pick service. taskId:%s, service:%s, err:%v", task.TaskId, serviceName, err)
				return err
			}
			log.Infof("Invoke success in pick service. serviceName:%s, taskId:%s, service:%+v", serviceName, task.TaskId, item)
			if item == nil || len(item.Nodes) != 1 {
				log.Errorf("Invoke fail in pick service and no service. taskId:%s, service:%s, err:%v", task.TaskId, serviceName, err)
				return common.ErrWorkerNotFound
			} else {
				if item.Nodes != nil {
					log.Infof("Invoke success in pick service. serviceName:%s, taskId:%s, service:%+v, node:%+v", serviceName, task.TaskId, item, item.Nodes[0])
				}
			}
			workerByte, exists := item.Metadata["worker"]
			if !exists || workerByte == "" {
				log.Errorf("Invoke fail in pick worker is nil. taskId:%s, service:%+v, err:%v", task.TaskId, item, err)
				return common.ErrWorkerNotFound
			}

			var worker models.Worker
			err = json.Unmarshal([]byte(workerByte), &worker)
			if err != nil {
				log.Errorf("Invoke fail in unmarshal worker.taskId:%s, worker:%+v, err:%v", task.TaskId, worker, err)
				return err
			}

			log.Infof("Invoke in engine invoke. taskId:%s, worker:%+v, workerByte:%+v", task.TaskId, worker, workerByte)

			rst, err = t.engine.Invoke(ctx, task, &worker)
			if err != nil {
				log.Errorf("Invoke fail in engine invoke. taskId:%s, worker:%+v, workerByte:%+v, err:%+v", task.TaskId, worker, workerByte, err)
				return err
			}
			return err
		},
		retry.Delay(time.Millisecond),
		retry.Attempts(5),
		retry.DelayType(retry.FixedDelay),
		retry.LastErrorOnly(true),
	)
	return rst, err
}
