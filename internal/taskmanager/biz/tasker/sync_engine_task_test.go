package tasker

import (
	"context"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/mohae/deepcopy"
	. "github.com/onsi/ginkgo/v2"
	"github.com/stretchr/testify/assert"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/mocks"
	"mpp/internal/taskmanager/models"
	v1 "proto.mpp/api/common/v1"
	"testing"
	"time"
)

/**
 * @Author: qinxin
 * @Date: 2024/9/24 02:16
 * @Desc:
 * @Version 1.0
 */

var (
	engineTaskSyncBiz *EngineTaskSync
)

func TestSyncEngineTaskStatus(t *testing.T) {

	// Mock leader check

	// Mock ListTasks
	ctl = gomock.NewController(t)
	defer ctl.Finish()
	cleaner = ctl.Finish
	taskRepoMock = mocks.NewMockTaskRepo(ctl)
	schedulerAdapterMock = mocks.NewMockScheduleAdapter(ctl)
	engineAdapterMock = mocks.NewMockEngineAdapter(ctl)
	sentinelRepoMock = mocks.NewMockTaskSentinelConfigRepo(ctl)
	ctx = context.Background()

	engineTaskSyncBiz.engine = engineAdapterMock
	engineTaskSyncBiz.repo = taskRepoMock
	engineTaskSyncBiz.scheduler = schedulerAdapterMock

	mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
	mockTask2.BizStatus = 1
	mockTask2.InternalStatus = 1
	mockTask2.TaskType = 2
	// case 1 ListTasks查询结果为空 不需要进行状态同步
	schedulerAdapterMock.EXPECT().ListAllWorker(gomock.Any(), gomock.Any()).Return(nil, nil).Times(2)
	schedulerAdapterMock.EXPECT().GetAllSchedulerTags().Return([]string{"l2"}).AnyTimes()
	engineTaskSyncBiz.SyncEngineTaskStatus(context.Background(), v1.TaskType_Stream, schedulerAdapterMock.GetAllSchedulerTags())
	//case 2 ListTasks返回error
	schedulerAdapterMock.EXPECT().ListAllWorker(gomock.Any(), gomock.Any()).Return(nil, common.ErrInternalError).Times(1)
	engineTaskSyncBiz.SyncEngineTaskStatus(context.Background(), v1.TaskType_Stream, schedulerAdapterMock.GetAllSchedulerTags())
	//case 3 正常进入调度，调度失败返回error
	mockTask2.TaskType = models.NonQueueAsyncTaskType
	mockWorker := &models.Worker{
		Id:          "worker-1",
		Ip:          "127.0.0.1",
		Port:        8080,
		Product:     "test",
		EngineModel: "test",
		Tag:         "test",
	}
	workers := []*models.Worker{mockWorker}
	//defer mockey.Mock((*BatchTaskBiz).ScheduleNonQueueTask).Return(common.ErrInternalError).Build().UnPatch()
	schedulerAdapterMock.EXPECT().ListAllWorker(gomock.Any(), gomock.Any()).Return(workers, nil).Times(1)
	engineAdapterMock.EXPECT().ListByTask(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, common.ErrInternalError).Times(1)
	engineTaskSyncBiz.SyncEngineTaskStatus(context.Background(), v1.TaskType_Stream, schedulerAdapterMock.GetAllSchedulerTags())

	//case 4 正常进入调度,返回的misstask列表为空
	mockTask2.TaskType = models.NonQueueAsyncTaskType
	//defer mockey.Mock((*BatchTaskBiz).ScheduleNonQueueTask).Return(common.ErrInternalError).Build().UnPatch()
	engineTaskSyncBiz.SyncEngineTaskStatus(context.Background(), v1.TaskType_Stream, schedulerAdapterMock.GetAllSchedulerTags())

	//验证 没有返回结果 人工测试验证
}

func TestCompareTasksWithWorker(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	worker := &models.Worker{
		Id:   "worker1",
		Ip:   "127.0.0.1",
		Port: 12345,
	}

	// Case 1: No tasks in workerActualRunningTasks and workerRunningTasks
	//mockTask2:= deepcopy.Copy(defaultModelTask).(*models.Task)
	//mockSimpleTask:= models.SimpleTask{
	//	TaskId: mockTask2.TaskId,
	//}
	workerActualRunningTasks := []models.SimpleTask{}
	workerRunningTasks := []*models.Task{}
	stolenTasks, missedTasks, err := engineTaskSyncBiz.compareTasksWithWorker(context.Background(), worker, workerActualRunningTasks, workerRunningTasks)
	assert.NoError(t, err)
	assert.Empty(t, stolenTasks)
	assert.Empty(t, missedTasks)

	// Case 2: workerActualRunningTasks has tasks not in workerRunningTasks
	workerActualRunningTasks = []models.SimpleTask{
		{TaskId: "task1"},
		{TaskId: "task2"},
	}
	workerRunningTasks = []*models.Task{}
	stolenTasks, missedTasks, err = engineTaskSyncBiz.compareTasksWithWorker(context.Background(), worker, workerActualRunningTasks, workerRunningTasks)
	assert.NoError(t, err)
	assert.ElementsMatch(t, stolenTasks, []string{"task1", "task2"})
	assert.Empty(t, missedTasks)

	// Case 3: workerRunningTasks has tasks not in workerActualRunningTasks
	workerActualRunningTasks = []models.SimpleTask{}
	workerRunningTasks = []*models.Task{
		{
			TaskId:       "task3",
			LastModified: time.Now().Add(-2 * time.Hour),
		},
		{
			TaskId:       "task4",
			LastModified: time.Now().Add(-2 * time.Hour),
		},
	}
	stolenTasks, missedTasks, err = engineTaskSyncBiz.compareTasksWithWorker(context.Background(), worker, workerActualRunningTasks, workerRunningTasks)
	assert.NoError(t, err)
	assert.Empty(t, stolenTasks)
	assert.ElementsMatch(t, missedTasks, []string{"task3", "task4"})

	// Case 4: Mixed tasks, some in both, some not
	workerActualRunningTasks = []models.SimpleTask{
		{TaskId: "task1"},
		{TaskId: "task2"},
	}
	workerRunningTasks = []*models.Task{
		{
			TaskId:       "task1",
			LastModified: time.Now().Add(-2 * time.Hour),
		},
		{
			TaskId:       "task3",
			LastModified: time.Now().Add(-2 * time.Hour),
		},
	}
	stolenTasks, missedTasks, err = engineTaskSyncBiz.compareTasksWithWorker(context.Background(), worker, workerActualRunningTasks, workerRunningTasks)
	assert.NoError(t, err)
	assert.ElementsMatch(t, stolenTasks, []string{"task2"})
	assert.ElementsMatch(t, missedTasks, []string{"task3"})

	// Case 5: Tasks with execution time too long
	workerActualRunningTasks = []models.SimpleTask{
		{TaskId: "task1"},
		{TaskId: "task2"},
	}
	workerRunningTasks = []*models.Task{
		{
			TaskId:       "task1",
			LastModified: time.Now().Add(-2 * time.Hour),
			Product:      "test-product",
		},
		{
			TaskId:       "task3",
			LastModified: time.Now().Add(-2 * time.Hour),
			Product:      "test-product",
		},
	}
	stolenTasks, missedTasks, err = engineTaskSyncBiz.compareTasksWithWorker(context.Background(), worker, workerActualRunningTasks, workerRunningTasks)
	assert.NoError(t, err)
	assert.ElementsMatch(t, stolenTasks, []string{"task2"})
	assert.ElementsMatch(t, missedTasks, []string{"task3"})
}

//var _ = Describe("EngineTaskSync Tests", func() {
//	var (
//		ctx                context.Context
//		actualRunningTasks []models.SimpleTask
//		workerRunningTasks []*models.Task
//		workers            []*models.Worker
//	)
//
//	BeforeEach(func() {
//		engineTaskSyncBiz.engine = engineAdapterMock
//		engineTaskSyncBiz.repo = taskRepoMock
//		engineTaskSyncBiz.scheduler = schedulerAdapterMock
//
//		actualRunningTasks = []models.SimpleTask{
//			{TaskId: "123test"},
//		}
//		workerRunningTasks = []*models.Task{
//			defaultModelTask,
//		}
//		workers = []*models.Worker{
//			{Id: "worker-1"},
//		}
//	})
//	It("should synchronize engine task status successfully", func() {
//		// Mock ListAllWorker to return workers
//
//		schedulerAdapterMock.EXPECT().ListAllWorker(ctx).Return(workers, nil).Times(1)
//		engineAdapterMock.EXPECT().ListByTask(ctx, gomock.Any(), gomock.Any()).Return(actualRunningTasks, nil).Times(1)
//		taskRepoMock.EXPECT().ListRunningTaskByWorkerId(ctx, gomock.Any()).Return(workerRunningTasks, nil).Times(1)
//
//		//defer mockey.Mock((*EngineTaskSync).processMissingTasks).Return().Build().UnPatch()
//		//defer mockey.Mock((*EngineTaskSync).processingStolenTasks).Return().Build().UnPatch()
//
//		engineTaskSyncBiz.SyncEngineTaskStatus(ctx, v1.TaskType_Simple) // Should succeed
//
//		// You can add more assertions as necessary
//	})
//
//})

var _ = Describe("tests EngineTaskSync processMissingTasks ", func() {
	var (
		missedTasks []string
	)

	BeforeEach(func() {

		ctx = context.Background()
		engineTaskSyncBiz.engine = engineAdapterMock
		engineTaskSyncBiz.repo = taskRepoMock
		engineTaskSyncBiz.scheduler = schedulerAdapterMock
		missedTasks = []string{"123test"}
	})

	//It("should synchronize engine task status successfully", func() {
	//	//构造mockTask
	//	// 模拟 如果任务内部状态为running，则更新任务内部状态和result，等待任务状态同步sync_task_status进行处理
	//	mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
	//	mockTask2.Worker = defaultWorker
	//
	//	mockTask2.InternalStatus = models.TaskStateActive
	//	mockTask2.TaskType = models.StreamTaskType
	//	mockTasks := []*models.Task{
	//		mockTask2,
	//	}
	//	// Mock ListAllWorker to return workers
	//	taskRepoMock.EXPECT().FindByIDList(gomock.Any(), gomock.Any()).Return(mockTasks, nil).Times(1)
	//	taskRepoMock.EXPECT().UpdateAbnormalStopped(gomock.Any(), gomock.Any()).Return(nil).Times(1)
	//
	//	//defer mockey.Mock((*EngineTaskSync).processMissingTasks).Return().Build().UnPatch()
	//	//defer mockey.Mock((*EngineTaskSync).processingStolenTasks).Return().Build().UnPatch()
	//
	//	engineTaskSyncBiz.processMissingTasks(ctx, defaultWorker, missedTasks) // Should succeed
	//
	//	// You can add more assertions as necessary
	//})

	It("should handle empty missedTasks list gracefully", func() {
		missedTasks = []string{}

		engineTaskSyncBiz.processMissingTasks(ctx, defaultWorker, missedTasks)

		// No action should be taken for an empty list
		taskRepoMock.EXPECT().FindByIDList(ctx, gomock.Any()).Times(0)
		taskRepoMock.EXPECT().UpdateAbnormalStopped(ctx, gomock.Any()).Times(0)
	})

	It("should handle error from FindByIDList", func() {
		mockError := common.ErrInternalError
		taskRepoMock.EXPECT().FindByIDList(ctx, gomock.Any()).Return(nil, mockError).Times(1)

		engineTaskSyncBiz.processMissingTasks(ctx, defaultWorker, missedTasks)

		// Expect a log message indicating the failure
		// This assumes that logging is done in the method being tested.
	})

	It("should handle no tasks found", func() {
		mockTasks := []*models.Task{}
		taskRepoMock.EXPECT().FindByIDList(ctx, gomock.Any()).Return(mockTasks, nil).Times(1)

		engineTaskSyncBiz.processMissingTasks(ctx, defaultWorker, missedTasks)

		// Expect a warning log message indicating no tasks were found
		// This assumes that logging is done in the method being tested.
	})

	//It("should handle UpdateAbnormalStopped error", func() {
	//	mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
	//	mockTask2.Worker = defaultWorker
	//	mockTask2.InternalStatus = 1
	//	mockTasks := []*models.Task{mockTask2}
	//
	//	mockError := common.ErrInternalError
	//	taskRepoMock.EXPECT().FindByIDList(ctx, gomock.Any()).Return(mockTasks, nil).Times(1)
	//	taskRepoMock.EXPECT().UpdateAbnormalStopped(ctx, gomock.Any()).Return(mockError).Times(1)
	//
	//	engineTaskSyncBiz.processMissingTasks(ctx, defaultWorker, missedTasks)
	//
	//	// Expect a log message indicating the failure to update the task
	//	// This assumes that logging is done in the method being tested.
	//})

})

var _ = Describe("tests EngineTaskSync processingStolenTasks ", func() {
	var (
		stolenTasks []string
	)

	BeforeEach(func() {

		ctx = context.Background()
		engineTaskSyncBiz.engine = engineAdapterMock
		engineTaskSyncBiz.repo = taskRepoMock
		engineTaskSyncBiz.scheduler = schedulerAdapterMock
		stolenTasks = []string{"123test"}
	})

	//It("should synchronize engine task status processingStolenTasks", func() {
	//	//构造mockTask
	//	// 模拟 如果任务内部状态为running，则更新任务内部状态和result，等待任务状态同步sync_task_status进行处理
	//	mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
	//	mockTask2.Worker = defaultWorker
	//
	//	mockTask2.InternalStatus = 1
	//	mockTasks := []*models.Task{
	//		mockTask2,
	//	}
	//	// Mock ListAllWorker to return workers
	//	taskRepoMock.EXPECT().FindByIDList(gomock.Any(), gomock.Any()).Return(mockTasks, nil).Times(1)
	//	engineAdapterMock.EXPECT().Stop(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
	//
	//	//defer mockey.Mock((*EngineTaskSync).processMissingTasks).Return().Build().UnPatch()
	//	//defer mockey.Mock((*EngineTaskSync).processingStolenTasks).Return().Build().UnPatch()
	//
	//	engineTaskSyncBiz.processingStolenTasks(ctx, defaultWorker, stolenTasks) // Should succeed
	//
	//	// You can add more assertions as necessary
	//})

	It(" processingStolenTasks and tasker is biz status running", func() {
		//构造mockTask
		// 模拟 如果任务内部状态为running，则更新任务内部状态和result，等待任务状态同步sync_task_status进行处理
		mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask2.Worker = defaultWorker
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		mockTasks := []*models.Task{
			mockTask2,
		}
		// Mock ListAllWorker to return workers
		taskRepoMock.EXPECT().FindByIDList(ctx, gomock.Any()).Return(mockTasks, nil).Times(1)
		//engineAdapterMock.EXPECT().Stop(ctx, gomock.Any(),gomock.Any()).Return(nil).Times(1)

		//defer mockey.Mock((*EngineTaskSync).processMissingTasks).Return().Build().UnPatch()
		//defer mockey.Mock((*EngineTaskSync).processingStolenTasks).Return().Build().UnPatch()

		engineTaskSyncBiz.processingStolenTasks(ctx, defaultWorker, stolenTasks) // Should succeed

		// You can add more assertions as necessary
	})

})

var _ = Describe("BackgroundMethod Tests", func() {
	var (
		ctx context.Context
	)

	BeforeEach(func() {
		ctx = context.Background()
	})

	It("BackgroundMethod and ctx return fail", func() {
		// Mock ListAllWorker to return workers

		//defer mockey.Mock((*leader.LeaderBiz).IsLeader).Return(true).Build().UnPatch()
		defer mockey.Mock((*EngineTaskSync).SyncEngineTaskStatus).Return(nil).Build().UnPatch()
		ctx, _ := context.WithTimeout(ctx, 1*time.Millisecond)
		time.Sleep(1 * time.Millisecond)
		engineTaskSyncBiz.BackgroundMethod(ctx, v1.TaskType_Simple, 1) // Should succeed
		// You can add more assertions as necessary
	})

})
