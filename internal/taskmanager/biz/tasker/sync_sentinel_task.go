package tasker

import (
	"context"
	"github.com/cinience/animus/safe"
	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/taskmanager/biz/leader"
	"mpp/internal/taskmanager/biz/sentinel"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"
	"mpp/pkg/tracer/tasker_tracing"
	"mpp/pkg/utils"
	"time"
)

//流控设计：初版采用redis记录当前任务数量，任务入队时查询redis，如果数量超过配置，则拒绝入队
//定时扫描运行中任务，同步redis和task_sentinel_config中当前运行任务数量
//redis中用key=sentiel-pipeleline-userId-taskType-product-engineModel+tag
//后续可以考虑改为使用sentinel 官方中文文档：https://sentinelguard.io/zh-cn/docs/introduction.html

type TaskSentinelSync struct {
	taskRepo     repository.TaskRepo
	sentinelRepo repository.TaskSentinelConfigRepo

	taskBiz      *CommonTaskBiz
	batchTaskBiz *BatchTaskBiz
	leaderBiz    *leader.LeaderBiz
	sentinelBiz  *sentinel.SentinelBiz
	taskTracer   *tasker_tracing.Tracer
	log          *log.Helper
}

func NewTaskSentinelSync(ctx context.Context, taskRepo repository.TaskRepo, sentinelRepo repository.TaskSentinelConfigRepo,
	taskBiz *CommonTaskBiz, batchTaskBiz *BatchTaskBiz, leaderBiz *leader.LeaderBiz, sentinelBiz *sentinel.SentinelBiz, logger log.Logger) *TaskSentinelSync {

	ts := &TaskSentinelSync{taskRepo: taskRepo, sentinelRepo: sentinelRepo,
		taskBiz: taskBiz, batchTaskBiz: batchTaskBiz, leaderBiz: leaderBiz, sentinelBiz: sentinelBiz, log: log.NewHelper(logger)}
	ts.taskRepo = taskRepo.CloneWithSpecifiedTable(models.BatchTaskTableName)
	ts.taskTracer = tasker_tracing.NewTracer(tasker_tracing.WithConsumerSpanKind())
	safe.Go(func() {
		ts.StartRedisTaskSentinelDataSync(ctx)
	})

	return ts
}

func (et *TaskSentinelSync) StartRedisTaskSentinelDataSync(ctx context.Context) {
	count := 0
	for {
		count++
		if count%10 == 0 {
			log.Infof("StartRedisTaskSentinelDataSync, count:%d, isLeader:%v (if not leader, will do nothing)", count, et.leaderBiz.IsLeader())
		}

		if !et.leaderBiz.IsLeader() {
			time.Sleep(time.Second * 10)
			continue
		}

		ctx := utils.ResetRequestId(ctx)
		err := et.RedisTaskSentinelDataSync(ctx)
		if err != nil {
			log.Errorf("StartRedisTaskSentinelDataSync fail, err:%v", err)
		}

		if ctx.Err() != nil {
			log.Errorf("StartRedisTaskSentinelDataSync BackgroundMethod fail,ctx.err:%v", ctx.Err())
			return
		}
		// 休眠一段时间，以避免过于频繁的执行
		//测试时休眠时间1min,线上时休眠时间5min
		time.Sleep(time.Second * 60)
	}
}

// 从数据库batch_task表中查询当前运行任务数量，更新redis中的任务信息
func (et *TaskSentinelSync) RedisTaskSentinelDataSync(ctx context.Context) error {

	// 查询数据库中当前运行任务数量和流控配置
	runningTaskCountInfoByKey, activeSentinelConfigsById, err := et.getRunningTaskCountInfoAndActiveSentinelConfigs(ctx)

	//将没有运行中任务的流控配置更新到redis中
	for sentinelConfigId, sentinelConfig := range activeSentinelConfigsById {
		key := models.GetSentinelKey(sentinelConfig.UserId, sentinelConfig.PipelineId, int32(sentinelConfig.TaskType), sentinelConfig.Product, sentinelConfig.EngineModel, sentinelConfig.Tag)
		if runningTaskCountInfoByKey[key] == nil {
			if sentinelConfig != nil && sentinelConfig.SentinelStatus == models.SentinelStatusStateActive &&
				sentinelConfig.SentinelConfig != nil {
				err = et.sentinelBiz.SetSentinelData(ctx, key, 0, sentinelConfigId,
					sentinelConfig.SentinelConfig.MaxParallelNum, true)
				if err != nil {
					log.Errorf("RedisTaskSentinelDataSync failed, key:%s, taskNum:%d, sentinelConfigId:%s, maxParallelNum:%d, enableSentinel:%v, err:%v",
						key, 0, sentinelConfigId, sentinelConfig.SentinelConfig.MaxParallelNum, true, err)
				}
				log.Infof("RedisTaskSentinelDataSync success and no running task , key:%s, taskNum:%d, sentinelConfigId:%s, maxParallelNum:%d, enableSentinel:%v",
					key, 0, sentinelConfigId, sentinelConfig.SentinelConfig.MaxParallelNum, true)
			}
		}
	}
	//遍历runningTaskCountInfoByKey组装流控数据,更新redis
	for key, singleTaskCountInfo := range runningTaskCountInfoByKey {
		sentinelConfigId := models.GetSentinelConfigId(key)
		taskSentinelConfig := activeSentinelConfigsById[sentinelConfigId]

		if taskSentinelConfig != nil && taskSentinelConfig.SentinelStatus == models.SentinelStatusStateActive &&
			taskSentinelConfig.SentinelConfig != nil {
			//如果流控配置表中有流控配置且状态为激活，则设置任务运行数量
			err = et.sentinelBiz.SetSentinelData(ctx, key, singleTaskCountInfo.TaskNum, sentinelConfigId,
				taskSentinelConfig.SentinelConfig.MaxParallelNum, true)
			if err != nil {
				log.Errorf("RedisTaskSentinelDataSync failed, key:%s, taskNum:%d, sentinelConfigId:%s, maxParallelNum:%d, enableSentinel:%v, err:%v",
					key, singleTaskCountInfo.TaskNum, sentinelConfigId, taskSentinelConfig.SentinelConfig.MaxParallelNum, true, err)
			}
			log.Infof("RedisTaskSentinelDataSync success, key:%s, taskNum:%d, sentinelConfigId:%s, maxParallelNum:%d, enableSentinel:%v",
				key, singleTaskCountInfo.TaskNum, sentinelConfigId, taskSentinelConfig.SentinelConfig.MaxParallelNum, true)
		} else {
			//如果流控配置表中没有流控配置或状态不为激活，则只设置任务运行数量
			err = et.sentinelBiz.SetSentinelData(ctx, key, singleTaskCountInfo.TaskNum, sentinelConfigId,
				0, false)
			if err != nil {
				log.Errorf("RedisTaskSentinelDataSync failed, key:%s, taskNum:%d, sentinelConfigId:%s, maxParallelNum:%d, enableSentinel:%v, err:%v",
					key, singleTaskCountInfo.TaskNum, sentinelConfigId, taskSentinelConfig.SentinelConfig.MaxParallelNum, false, err)
			}
		}
	}
	return nil
}

// 从数据库batch_task表中查询当前运行任务数量和流控配置
func (et *TaskSentinelSync) getRunningTaskCountInfoAndActiveSentinelConfigs(ctx context.Context) (map[string]*models.TempQueryTask, map[string]*models.TaskSentinelConfig, error) {
	// 查询数据库中当前运行任务数量
	runningTaskCountInfo, err := et.taskRepo.CountAggregatedTasks(ctx)
	if err != nil {
		log.Errorf("getRunningTaskCountInfoAndActiveSentinelConfigs failed in CountAggregatedTasks, err:%v", err)
		return nil, nil, err
	}
	// 创建一个map来存储按key分组的任务计数信息
	runningTaskCountInfoByKey := make(map[string]*models.TempQueryTask)
	// 将runningTaskCountInfo切片中的元素按key放入map
	for _, countInfo := range runningTaskCountInfo {
		key := models.GetSentinelKey(countInfo.UserId, countInfo.PipelineId, countInfo.TaskType, countInfo.Product, countInfo.EngineModel, countInfo.Tag)
		runningTaskCountInfoByKey[key] = countInfo
		//测试用log
		log.Infof("getRunningTaskCountInfoAndActiveSentinelConfigs success, key:%s, taskNum:%d", key, countInfo.TaskNum)
	}
	//查询数据库中流控配置表
	activeSentinelConfigs, err := et.sentinelRepo.ListActiveTaskSentinelConfig(ctx)
	if err != nil {
		log.Errorf("getRunningTaskCountInfoAndActiveSentinelConfigs failed in ListActiveTaskSentinelConfig , err:%v", err)
		return nil, nil, err
	}
	// 创建一个map来存储按SentinelConfigId分组的流控配置
	activeSentinelConfigsById := make(map[string]*models.TaskSentinelConfig)
	// 将activeSentinelConfigs切片中的元素按SentinelConfigId放入map
	for _, config := range activeSentinelConfigs {
		activeSentinelConfigsById[config.SentinelConfigId] = config
		//测试用log
		log.Infof("getRunningTaskCountInfoAndActiveSentinelConfigs success, key:%s, taskNum:%d", config.SentinelConfigId, config.SentinelConfig.MaxParallelNum)
	}
	log.Infof("getRunningTaskCountInfoAndActiveSentinelConfigs success, runningTaskCountInfoByKeyLen:%v, activeSentinelConfigsByIdLen:%v", len(runningTaskCountInfoByKey), len(activeSentinelConfigsById))
	return runningTaskCountInfoByKey, activeSentinelConfigsById, nil
}
