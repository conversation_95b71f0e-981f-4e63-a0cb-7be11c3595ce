package tasker

import (
	"context"
	"github.com/golang/mock/gomock"
	"github.com/mohae/deepcopy"
	. "github.com/onsi/ginkgo/v2"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/models"
	"time"
)

/**
 * @Author: qinxin
 * @Date: 2024/9/25 20:41
 * @Desc:
 * @Version 1.0
 */

var taskDBCheckerBiz *TaskDBChecker

var _ = Describe("TaskDBChecker Tests", func() {
	var (
		mockTask2 *models.Task
	)

	BeforeEach(func() {
		taskStatusSyncBiz.repo = taskRepoMock
		taskStatusSyncBiz.engine = engineAdapterMock
		ctx = context.Background()

		mockTask2 := deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		mockTask2.LastModified = time.Now()
		mockTask2.GmtCreate = time.Now()
		taskDBCheckerBiz.repo = taskRepoMock

	})
	It("test check success", func() {
		mockTask2 = deepcopy.Copy(defaultModelTask).(*models.Task)
		mockTask2.BizStatus = 1
		mockTask2.InternalStatus = 1
		mockTask2.LastModified = time.Now()
		mockTask2.GmtCreate = time.Now()
		//defer mockey.Mock((*leader.LeaderBiz).IsLeader).Return(true).Build().UnPatch()
		taskRepoMock.EXPECT().FindFirstID(gomock.Any()).Return(mockTask2, nil).Times(1)
		taskDBCheckerBiz.Check() //
		//验证

	})

	It("test check and FindFirstID return err", func() {
		taskRepoMock.EXPECT().FindFirstID(gomock.Any()).Return(nil, common.ErrInternalError).Times(1)
		//defer mockey.Mock((*leader.LeaderBiz).IsLeader).Return(true).Build().UnPatch()
		taskDBCheckerBiz.Check() //

	})
})
