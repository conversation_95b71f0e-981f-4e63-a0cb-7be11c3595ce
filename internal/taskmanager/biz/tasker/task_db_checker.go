package tasker

import (
	"context"
	"fmt"
	"time"

	"mpp/internal/taskmanager/biz/leader"
	"mpp/internal/taskmanager/repository"
	"mpp/internal/taskmanager/tracing/tasker_span"
	"mpp/pkg/tracer/tasker_tracing"

	"github.com/go-kratos/kratos/v2/log"
	gocron "github.com/robfig/cron/v3"
)

type TaskDBChecker struct {
	repo   repository.TaskRepo
	leader *leader.LeaderBiz
	cron   *gocron.Cron

	taskTracer *tasker_tracing.Tracer
}

func NewTaskDBChecker(repo repository.TaskRepo, leader *leader.LeaderBiz, isTestMode bool) (*TaskDBChecker, error) {
	t := &TaskDBChecker{
		repo:   repo,
		leader: leader,
	}
	t.taskTracer = tasker_tracing.NewTracer(tasker_tracing.WithTracerName("mppDBChecker"))
	if isTestMode {
		t.Check()
		return t, nil
	} else {
		t.cron = gocron.New()
		_, err := t.cron.AddFunc("@every 30s", func() {
			t.Check()
		})
		if err != nil {
			return nil, err
		}

		t.cron.Start()

		return t, nil
	}
}

func (b *TaskDBChecker) Check() {
	if !b.leader.IsLeader() {
		return
	}

	// 检查批量异步任务是否有定期删除
	task, err := b.repo.FindFirstID(context.Background())
	if err != nil {
		log.Errorf("findFirstID err:%v", err)
		return
	}

	// 生产环境存储90天,大于100天的数据未删除则warn
	reservedTime := 100 * 24
	if int(time.Now().Sub(task.LastModified).Hours()) > reservedTime {
		err = fmt.Errorf("no cron db clean, task id:%s, last-modified:%s > %d Hours", task.TaskId, task.LastModified.Format(time.RFC3339), reservedTime)
		log.Warn(err)
	} else {
		log.Infof("db frist tasker is %v", task)
	}

	ctx, span := b.taskTracer.Start(context.Background(), "Check", task.Metadata)
	defer func() {
		b.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTask(ctx, "server.DBCheck", task)
		}, err)
	}()
}
