package tasker

import (
	"context"
	"errors"
	"mpp/pkg/utils"
	v1 "proto.mpp/api/common/v1"
	"time"

	"github.com/cinience/animus/safe"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/leader"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"

	"github.com/go-kratos/kratos/v2/log"
)

const (
	//任务日志事件 用于监控大盘
	//报告资源缺失
	sync_event_resource_missfree = "sync.resource.missfree"
	//报告未返还资源
	sync_event_resource_unexpected = "sync.resource.unexpected"
)

type SchedulerResourceSync struct {
	repo      repository.TaskRepo
	log       *log.Helper
	taskBiz   *CommonTaskBiz
	leaderBiz *leader.LeaderBiz
	scheduler common.ScheduleAdapter
}

func NewSchedulerResourceSync(ctx context.Context, repo repository.TaskRepo, taskBiz *CommonTaskBiz, leaderBiz *leader.LeaderBiz, scheduler common.ScheduleAdapter, logger log.Logger) *SchedulerResourceSync {
	ts := &SchedulerResourceSync{repo: repo, taskBiz: taskBiz, leaderBiz: leaderBiz,
		scheduler: scheduler, log: log.NewHelper(logger)}
	ts.repo = repo.CloneWithSpecifiedTable(models.StreamTaskTableName)

	// 启动后台线程, 定期同步资源
	//TODO 2 or 5 min
	safe.Go(func() {
		ts.BackgroundMethod(ctx, v1.TaskType_Stream, 2*time.Minute)
	})

	return ts
}

func NewSchedulerResourceSyncWithSpecifiedRepo(ctx context.Context, repo repository.TaskRepo, taskBiz *CommonTaskBiz, leaderBiz *leader.LeaderBiz, scheduler common.ScheduleAdapter, logger log.Logger) *SchedulerResourceSync {
	ts := &SchedulerResourceSync{repo: repo, taskBiz: taskBiz, leaderBiz: leaderBiz,
		scheduler: scheduler, log: log.NewHelper(logger)}

	return ts
}

func (s *SchedulerResourceSync) BackgroundMethod(ctx context.Context, taskType v1.TaskType, sleepTime time.Duration) {
	count := 0
	for {
		count++
		if count%10 == 0 {
			log.Infof("start SchedulerResourceSync, count:%d, isLeader:%v (if not leader, will do nothing)", count, s.leaderBiz.IsLeader())
		}

		if !s.leaderBiz.IsLeader() {
			time.Sleep(time.Second * 10)
			continue
		}

		err := s.SyncResource(ctx, taskType)
		if err != nil {
			log.Errorf("SchedulerResourceSync BackgroundMethod fail, err:%v", err)
			return
		}

		if ctx.Err() != nil {
			log.Error("SchedulerResourceSync BackgroundMethod fail", ctx.Err())
			return
		}

		// 休眠一段时间，以避免过于频繁的执行
		//TODO 2 or 5 min
		time.Sleep(sleepTime)
	}
}

// SyncResource synchronize the resource status with the tasker scheduler to avoid inconsistency
// (1) call scheduler list resource to get all using resources;
// (2) call tasker repo ListAllBizRunningTasks to query all running tasks
// (3) compare resource with tasker status, if tasker not running but resource is in using, free resource
// this routine will not update tasker status, only free resource to scheduler
func (s *SchedulerResourceSync) SyncResource(ctx context.Context, taskType v1.TaskType) error {
	schedulerTags := s.scheduler.GetAllSchedulerTags()
	resources, err := s.scheduler.ListResource(ctx, schedulerTags, false)
	lostResources, err := s.scheduler.ListResource(ctx, schedulerTags, true)
	if err != nil {
		log.Error("SyncResource fail in ListResource", err)
		return err
	}
	log.Infof("SyncResource listResource success, len:%v", len(resources))
	if len(resources) == 0 {
		return nil
	}

	runningTasks, err := s.repo.ListBizRunningTasksExceptDagJob(ctx)

	if err != nil {
		log.Error("SyncResource fail in ListAllBizRunningTasks", err)
		return err
	}
	runningTasks = FilterTasksByTypes(runningTasks, models.TaskType(taskType), models.UnknownTaskType)

	log.Infof("SyncResource ListAllBizRunningTasks success, len:%v", len(runningTasks))

	runningTaskMap := convertToMap(runningTasks)
	//resourceMap := convertToResourceMap(resources)
	resourceMap := ConvertToResourceMapAndFilterByTypes(resources, models.TaskType(taskType), models.UnknownTaskType)
	lostResourceMap := ConvertToResourceMapAndFilterByTypes(lostResources, models.TaskType(taskType), models.UnknownTaskType)

	s.processMissFreeResources(ctx, resourceMap, runningTaskMap)
	s.processUnExpectedRunningResources(ctx, resourceMap, runningTaskMap)

	// 直播转码增加，其他场景应该也要？先加场景限制
	s.processLostResources(ctx, lostResourceMap, runningTaskMap)

	return nil
}

// 比较资源和任务状态（biz_status），如果任务未运行（biz_status=stopped）但资源正在使用中，则释放资源。此程序不会更新任务状态，仅将资源释放给调度器。
func (s *SchedulerResourceSync) processMissFreeResources(ctx context.Context, resourceMap map[string]*models.Resource, runningTaskMap map[string]*models.Task) {

	for _, runningResource := range resourceMap {
		if _, ok := runningTaskMap[runningResource.TaskId]; ok {
			continue
		}
		task, err := s.repo.FindByID(ctx, runningResource.TaskId)
		if err != nil && !errors.Is(err, common.ErrTaskNotFoundError) {
			log.Error("SyncResource fail in FindByID", err)
			return
		}

		if task == nil {
			log.Infof("SyncResource tasker not found, taskId:%v", runningResource.TaskId)
			err = s.scheduler.FreeResource(ctx, runningResource.TaskId, map[string]string{"taskSchedulerTag": runningResource.SchedulerTag})
			if err != nil {
				log.Errorf("SyncResource fail in FreeResource, taskId:%v, err:%v",
					runningResource.TaskId, err)
			}
			continue
		}

		//maybe tasker is completed concurrently, use IsRecentlyUpdated to skip
		if task.IsRecentlyUpdated() {
			log.Infof("SyncResource tasker is recently updated, taskId:%v, now:%v, lastModified:%v",
				task.TaskId, time.Now(), task.LastModified)
			continue
		}
		//该日志用于sls中融合调度任务大盘统计，如日志内容变动需要同步更新sls大盘配置
		ctx = utils.SetTaskId(ctx, task.TaskId)
		ctx = utils.SetTaskProduct(ctx, task.Product)
		s.log.WithContext(ctx).Log(log.LevelWarn,
			"SyncResource free resource, taskId:%v, lastUpdateTime:%v",
			runningResource.TaskId, task.LastModified)
		err = s.scheduler.FreeResource(ctx, task.TaskId, task.Metadata)
		if err != nil {
			log.Errorf("SyncResource fail in FreeResource, taskId:%v, err:%v",
				runningResource.TaskId, err)
		}
	}
}

// 比较资源和任务状态(biz_status)，如果任务正在运行（biz_status=1）但没有相关资源信息且internal_status=1，则将内部状态internal_status更新为stopped。
func (s *SchedulerResourceSync) processUnExpectedRunningResources(ctx context.Context, resourceMap map[string]*models.Resource, runningTaskMap map[string]*models.Task) {

	for taskId := range runningTaskMap {
		if _, ok := resourceMap[taskId]; ok {
			continue
		}
		task, err := s.repo.FindByID(ctx, taskId)
		if err != nil {
			log.Errorf("SyncResource fail in FindByID, err:%v", err)
			continue
		}
		if task == nil {
			log.Errorf("SyncResource tasker not found, taskId:%v", taskId)
			continue
		}

		if !task.IsInternalRunning() {
			log.Warnf("SyncResource tasker is not running, taskId:%v, lastUpdateTime:%v",
				task.TaskId, task.LastModified)
			continue
		}
		if task.IsRecentlyUpdated() {
			log.Warnf("SyncResource tasker is recently updated, taskId:%v, now:%v, lastModified:%v",
				task.TaskId, time.Now(), task.LastModified)
			continue
		}
		//新增case，由于任务回调时先进行资源归还再更新biz_status=stopped，internal_status状态=Stopped
		//所以可能出现biz_status=1,internal_status=1,但资源已归还的中间状态
		//TODO 根据taskId查询scheduler的资源信息，判断该taskId的资源是否最近更新过，如果没有，则更新internal_status
		//该日志用于sls中融合调度任务大盘统计，如日志内容变动需要同步更新sls大盘配置
		ctx = utils.SetTaskId(ctx, task.TaskId)
		ctx = utils.SetTaskProduct(ctx, task.Product)
		s.log.WithContext(ctx).Log(log.LevelWarn,
			"SyncResource found resource not in scheduler, taskId:%v, lastUpdateTime:%v",
			task.TaskId, task.LastModified)
		task.InternalStatus = models.TaskStateStopped
		err = s.repo.UpdateInternalStatus(ctx, task)
		if err != nil {
			log.Errorf("SyncResource fail in UpdateInternalStatus, taskId:%v, err:%v",
				task.TaskId, err)
		}
	}
}

func (s *SchedulerResourceSync) processLostResources(ctx context.Context, lostResourceMap map[string]*models.Resource, runningTaskMap map[string]*models.Task) {

	for _, runningResource := range lostResourceMap {
		// resource有， running 没有的情况已经在上个方法中处理，这里仅处理两边都有在线，但resource是lost的情况
		if task, ok := runningTaskMap[runningResource.TaskId]; ok {
			if task.EngineModel != models.LiveTranscode {
				// 先限制 只处理直播转码场景
				continue
			}

			if task.IsRecentlyUpdated() {
				log.Infof("SyncResource tasker is recently updated, taskId:%v, now:%v, lastModified:%v",
					task.TaskId, time.Now(), task.LastModified)
				continue
			}

			// 归还资源，更新internal_status
			//该日志用于sls中融合调度任务大盘统计，如日志内容变动需要同步更新sls大盘配置
			ctx = utils.SetTaskId(ctx, task.TaskId)
			ctx = utils.SetTaskProduct(ctx, task.Product)
			s.log.WithContext(ctx).Log(log.LevelWarn,
				"SyncResource free lost resource, taskId:%v, lastUpdateTime:%v",
				runningResource.TaskId, task.LastModified)
			err := s.scheduler.FreeResource(ctx, task.TaskId, task.Metadata)
			if err != nil {
				log.Errorf("SyncResource fail in FreeLostResource, taskId:%v, err:%v",
					runningResource.TaskId, err)
			}
			task.InternalStatus = models.TaskStateStopped
			err = s.repo.UpdateInternalStatus(ctx, task)
			if err != nil {
				log.Errorf("SyncResource fail in process lostResource UpdateInternalStatus, taskId:%v, err:%v",
					task.TaskId, err)
			}
		}
	}
}

func convertToResourceMap(resources []*models.Resource) map[string]*models.Resource {
	resourceMap := make(map[string]*models.Resource)
	for i := range resources {
		if resources[i].TaskType == models.StreamTaskType || resources[i].TaskType == models.UnknownTaskType {
			resourceMap[resources[i].TaskId] = resources[i]

			//测试用log日志
			//if resources[i].TaskType == models.StreamTaskType {
			//	log.Infof("SyncResource found stream task, taskId:%v,taskType:%v", resources[i].TaskId, resources[i].TaskType)
			//}
		}
	}
	return resourceMap
}

func ConvertToResourceMapAndFilterByTypes(resources []*models.Resource, taskTypes ...models.TaskType) map[string]*models.Resource {
	resourceMap := make(map[string]*models.Resource)

	// 创建一个哈希集，用于存储所有需要过滤的任务类型
	typeSet := make(map[models.TaskType]bool)
	for _, taskType := range taskTypes {
		typeSet[taskType] = true
	}

	// 遍历资源列表，根据任务类型进行过滤
	for _, resource := range resources {
		if _, exists := typeSet[resource.TaskType]; exists {
			resourceMap[resource.TaskId] = resource
			log.Infof("SyncResource and convertToResourceMapAndFilterByTypes, taskId:%v, taskType:%v", resource.TaskId, resource.TaskType)
		}
	}

	return resourceMap
}

func FilterTasksByType(tasks []*models.Task, taskType models.TaskType) []*models.Task {
	var filteredTasks []*models.Task
	for i := range tasks {
		task := tasks[i]
		if task.TaskType == taskType {
			filteredTasks = append(filteredTasks, task)
		}
	}
	return filteredTasks
}

func FilterTasksByTypes(tasks []*models.Task, taskTypes ...models.TaskType) []*models.Task {
	var filteredTasks []*models.Task

	// 创建一个哈希集，用于存储所有需要过滤的任务类型
	typeSet := make(map[models.TaskType]bool)
	for _, taskType := range taskTypes {
		typeSet[taskType] = true
	}

	// 遍历任务列表，根据任务类型进行过滤
	for _, task := range tasks {
		if _, exists := typeSet[task.TaskType]; exists {
			filteredTasks = append(filteredTasks, task)
		}
	}

	return filteredTasks
}

func (s *SchedulerResourceSync) LogResourceSyncInfo(ctx context.Context, event string, taskNum int, msg string) {
	s.log.WithContext(ctx).Log(log.LevelInfo,
		"taskNum", taskNum,
		"msg", msg,
		"event", event)
}
