package tasker

import (
	"context"
	"testing"
	"time"

	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/grayscale"
	"mpp/internal/taskmanager/infra/engine"
	"mpp/internal/taskmanager/models"

	"github.com/bytedance/mockey"
	"github.com/cinience/animus/registry"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
)

/**
 * @Author: qinxin
 * @Date: 2024/7/18 15:51
 * @Desc:
 * @Version 1.0
 */

var (
	simpleTaskBiz *SimpleTaskBiz
	service       *registry.Service

	DefaultQuotaSetTest = map[string]int64{
		"cpu": 1,
	}
	defaultModelTask = &models.Task{
		TaskId:         "123test",
		JobId:          "123test",
		UserId:         "mpp",
		Product:        "mpp-integration-test",
		BizStatus:      2,
		InternalStatus: 2,
		UserData:       "userData",
		RequestResource: &models.RequestResource{
			Quota: DefaultQuotaSetTest,
		},
		Param: &models.TaskEngineParam{
			Param: `{"sleepTime":20000}`,
		},
		Result: &models.TaskResult{
			Message: "success",
			Code:    200,
		},
		GmtCreate:    time.Now(),
		LastModified: time.Now(),
		Notify:       "mns://1426105496228119.mns.cn-shanghai.aliyuncs.com/queues/mpp-notify-test,mns://1426105496228119.mns.cn-shanghai.aliyuncs.com/queues/mpp-notify-test?dequeued=inner",
	}

	defaultWorker = &models.Worker{
		Id:          "eci-uf6ev5komggfroeezj9z",
		Port:        10882,
		Ip:          "*********",
		EngineModel: "",
		Tag:         "",
		Product:     "",
		ConnectMode: nil,
	}

	workerBytes = `{"Id":"eci-uf6ev5komggfroeezj9z","Port":10882,"Ip":"*********","EngineModel":"","Tag":"","Product":"","ConnectMode":null}`
)

func init() {
	service = &registry.Service{
		ID:        "123test",
		Name:      "test",
		Version:   "1.0.0",
		Endpoints: []string{"127.0.0.1:8080"},
		Nodes:     make([]*registry.Node, 0),
	}
	service.Nodes = append(service.Nodes, &registry.Node{
		Id:       "123test",
		Address:  "127.0.0.1:8080",
		Metadata: make(map[string]string),
	})
	service.Nodes[0].Metadata["worker"] = workerBytes
	simpleTaskBiz, _ = NewSimpleTaskBiz(log.DefaultLogger, &engine.CommonEngine{}, nil, nil)
}

func TestSimple_invokeTask(t *testing.T) {
	//返回成功
	mockey.PatchConvey("InvokeTask and invokeTask return success", t, func() {

		//mockey.Mock((*TaskService).StartTask).Return(defaultSuccessResult, nil).Build()
		defer mockey.Mock((*registry.FailOverDiscovery).PickService).Return(service, nil).Build().UnPatch()
		defer mockey.Mock((*grayscale.MixRuleConfigBiz).CheckHitMixRule).Return(true, true, &models.MixConfig{
			Product:     "mpp-integration-test",
			EngineModel: "mpp-integration-test",
		}, nil).Build().UnPatch()
		//mockey.Mock((*engine.CommonEngine).Invoke).Return(nil, nil).Build()
		_, err := simpleTaskBiz.Invoke(context.Background(), defaultModelTask)
		assert.Equal(t, common.ErrWorkerNotFound, err)
	})

	mockey.PatchConvey("InvokeTask and invokeTask return error", t, func() {
		//mockey.Mock((*TaskService).StartTask).Return(defaultSuccessResult, nil).Build()
		defer mockey.Mock((*registry.FailOverDiscovery).PickService).Return(service, common.ErrWorkerNotFound).Build().UnPatch()
		defer mockey.Mock((*grayscale.MixRuleConfigBiz).CheckHitMixRule).Return(true, true, &models.MixConfig{
			Product:     "mpp-integration-test",
			EngineModel: "mpp-integration-test",
		}, nil).Build().UnPatch()
		//mockey.Mock((*engine.CommonEngine).Invoke).Return(nil, nil).Build()
		_, err := simpleTaskBiz.Invoke(context.Background(), defaultModelTask)
		assert.Equal(t, common.ErrWorkerNotFound, err)
	})
}
