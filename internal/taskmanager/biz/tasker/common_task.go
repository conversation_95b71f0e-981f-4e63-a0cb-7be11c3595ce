package tasker

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"os"
	"strings"
	"sync"
	"time"

	jsoniter "github.com/json-iterator/go"

	v1 "proto.mpp/api/common/v1"
	pb "proto.mpp/api/taskmanager/v1"

	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/livetranscode"
	"mpp/internal/taskmanager/biz/sentinel"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/infra/client"
	"mpp/internal/taskmanager/mesh"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/models/custom_types"
	"mpp/internal/taskmanager/models/domain"
	lc "mpp/internal/taskmanager/models/livetranscode"
	"mpp/internal/taskmanager/repository"
	"mpp/internal/taskmanager/tracing/tasker_span"
	"mpp/internal/taskmanager/util"
	"mpp/internal/taskscheduler/biz"
	"mpp/pkg/notify"
	_ "mpp/pkg/notify/mns"
	plugin "mpp/pkg/plugins"
	"mpp/pkg/plugins/matcher"
	"mpp/pkg/redis"
	"mpp/pkg/tracer/tasker_tracing"
	"mpp/pkg/utils"

	"github.com/avast/retry-go"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	kHttp "github.com/go-kratos/kratos/v2/transport/http"
	goredis "github.com/redis/go-redis/v9"
)

var js = jsoniter.ConfigCompatibleWithStandardLibrary

func NewEvictedResult() *models.TaskResult {
	return &models.TaskResult{
		Code:    http.StatusInternalServerError,
		Reason:  common.ErrorTaskEvicted,
		Message: common.ErrorTaskEvicted,
	}
}
func NewMigrateResult() *models.TaskResult {
	return &models.TaskResult{
		Code:    models.MigratedCode,
		Reason:  models.MigratedReason,
		Message: "task migrated",
	}
}

func NewEngineLostResult(worker *models.Worker) *models.TaskResult {
	return &models.TaskResult{
		Code:    models.EngineLostCode,
		Reason:  models.EngineLostReason,
		Message: fmt.Sprintf("engine lost in worker:%v", worker),
	}
}

func NewEngineParamErrorResult(worker *models.Worker) *models.TaskResult {
	return &models.TaskResult{
		Code:    models.EngineParamErrorCode,
		Reason:  models.EngineParamError,
		Message: fmt.Sprintf("engine parram error in worker:%v", worker),
	}
}

type CommonTaskBiz struct {
	repo          repository.TaskRepo
	log           *log.Helper
	scheduler     common.ScheduleAdapter
	engine        common.EngineAdapter
	workflow      common.WorkflowAdapter
	mesh          *mesh.MeshService
	taskDbChecker *TaskDBChecker
	taskTracer    *tasker_tracing.Tracer
	conf          *conf.Bootstrap

	cacheDb     goredis.UniversalClient
	sentinelBiz *sentinel.SentinelBiz

	plugin matcher.Matcher

	//live transcode
	sourceStreamTaskRepo       repository.SourceStreamTaskRepo
	transcodeAttachmentRepo    repository.TranscodeAttachmentRepo
	transcodeStatInfoRepo      repository.TranscodeStatInfoRepo
	liveTransCodeParamsAdapter *livetranscode.LiveTransCodeParamsAdapter

	//http客户端池
	httpClientPool *util.HTTPClientPool
}

func NewTaskBiz(conf *conf.Bootstrap, repo repository.TaskRepo, sourceStreamTaskRepo repository.SourceStreamTaskRepo, scheduler common.ScheduleAdapter, engine common.EngineAdapter,
	workflow common.WorkflowAdapter, mesh *mesh.MeshService, sentinelBiz *sentinel.SentinelBiz, liveTransCodeParamsAdapter *livetranscode.LiveTransCodeParamsAdapter, transcodeAttachmentRepo repository.TranscodeAttachmentRepo, transcodeStatInfoRepo repository.TranscodeStatInfoRepo, logger log.Logger) (*CommonTaskBiz, error) {
	t := &CommonTaskBiz{conf: conf, repo: repo, sourceStreamTaskRepo: sourceStreamTaskRepo, scheduler: scheduler, engine: engine, workflow: workflow, mesh: mesh, sentinelBiz: sentinelBiz, liveTransCodeParamsAdapter: liveTransCodeParamsAdapter, transcodeAttachmentRepo: transcodeAttachmentRepo, transcodeStatInfoRepo: transcodeStatInfoRepo, log: log.NewHelper(logger),
		httpClientPool: util.NewHTTPClientPool()}
	var err error

	if conf.GetRedisUri() != "" {
		t.cacheDb, err = redis.GetRedisUniversalClientWithURL(conf.Data.Redis.GetEndpoint())
		if err != nil {
			return nil, err
		}
	}

	t.plugin = t.initPlugins()
	t.repo = repo.CloneWithSpecifiedTable(models.StreamTaskTableName)
	t.SetTracer(tasker_tracing.NewTracer(tasker_tracing.WithConsumerSpanKind()))
	return t, nil
}

func (t *CommonTaskBiz) Close() {
	if t.httpClientPool != nil {
		t.httpClientPool.Close()
	}
}

func NewTaskBizWithSpecifiedRepo(conf *conf.Bootstrap, repo repository.TaskRepo, sourceStreamTaskRepo repository.SourceStreamTaskRepo, scheduler common.ScheduleAdapter, engine common.EngineAdapter,
	workflow common.WorkflowAdapter, mesh *mesh.MeshService, sentinel *sentinel.SentinelBiz, liveTransCodeParamsAdapter *livetranscode.LiveTransCodeParamsAdapter, transcodeAttachmentRepo repository.TranscodeAttachmentRepo, transcodeStatInfoRepo repository.TranscodeStatInfoRepo, logger log.Logger) (*CommonTaskBiz, error) {
	t, err := NewTaskBiz(conf, repo, sourceStreamTaskRepo, scheduler, engine, workflow, mesh, sentinel, liveTransCodeParamsAdapter, transcodeAttachmentRepo, transcodeStatInfoRepo, logger)
	if err != nil {
		return nil, err
	}
	// 恢复指定的repo
	t.repo = repo

	return t, err
}

func (t *CommonTaskBiz) SetTracer(taskTracer *tasker_tracing.Tracer) {
	t.taskTracer = taskTracer
}

func (t *CommonTaskBiz) initPlugins() matcher.Matcher {
	m := matcher.NewMatch()

	// 直播并行转码插件
	engineParams := make(map[string]interface{})
	engineParams["streams"] = []map[string]interface{}{{"parallel": ""}}
	m.Add(matcher.Selector{Product: models.ProductLive, EngineModel: models.LiveTranscode, EngineParams: engineParams}, plugin.LiveParallelTranscode)
	// 测试引擎
	m.Add(matcher.Selector{Product: "mpp-integration-test", EngineModel: "mpp-integration-test-engine", EngineParams: engineParams}, plugin.LiveParallelTranscode)

	return m
}

func (t *CommonTaskBiz) buildReq(task *models.Task, param map[string]interface{}) map[string]interface{} {
	req := map[string]interface{}{
		"jobId":        task.JobId,
		"product":      task.Product,
		"engineModel":  task.EngineModel,
		"tag":          task.Tag,
		"engineParams": param,
	}

	return req
}

// todo 调用mpp-plugins获取DAG图
func (t *CommonTaskBiz) getDagGraph(ctx context.Context, task *models.Task) (map[string]interface{}, error) {
	var err error
	ctx, span := t.taskTracer.Start(ctx, "/CommonTask/getDagGraph", task.Metadata)
	defer func() {
		t.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTaskAndExtendInformation(ctx, "CommonTask.getDagGraph", task, nil)
		}, err)
	}()

	param := biz.StringToMap(task.Param.Param)
	param["taskId"] = task.TaskId
	s := matcher.Selector{
		Product:      task.Product,
		EngineModel:  task.EngineModel,
		Tag:          task.Tag,
		EngineParams: param,
	}
	plugins := t.plugin.Match(s)

	req := t.buildReq(task, param)

	next := func(ctx context.Context, req interface{}) (interface{}, error) {
		return req, nil
	}

	_, err = plugin.Chain(plugins...)(next)(context.Background(), req)
	if err != nil {
		log.Warn("use plugin chain failed:", err)
	} else {
		log.Infof("use plugin chain success, req:%+v", req)
	}

	if _, exist := req["graph"]; exist {
		graph := req["graph"].(map[string]interface{})
		log.Infof("graph:%+v", graph)
		return graph, nil
	} else {
		log.Errorf("graph not found,taskId:%s", task.TaskId)
		return nil, models.ErrDagGraphNotFound
	}

	return nil, nil
}

// TODO mesh配置下发 可先采用redis模拟
func (t *CommonTaskBiz) MeshConfigDistribution(ctx context.Context, task *models.Task) error {
	var err error
	ctx, span := t.taskTracer.Start(ctx, "/CommonTask/MeshConfigDistribution", task.Metadata)
	defer func() {
		t.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTaskAndExtendInformation(ctx, "CommonTask.MeshConfigDistribution", task, nil)
		}, err)
	}()
	req := &pb.MeshSetConfigRequest{
		Key:       task.TaskId,
		RequestId: utils.GetRequestIdFromContext(ctx),
	}
	if task.Worker != nil {
		req.Value = &pb.MeshConfigValue{
			WorkerId: task.Worker.Id,
			WorkerIp: task.Worker.Ip,
			Port:     uint32(task.Worker.Port),
		}
	}
	setRes, err := t.mesh.SetConfig(ctx, req)
	if err != nil || !models.IsSuccess(*setRes.Result) {
		log.Errorf("MeshConfigDistribution failed, taskId:%s, err:%v, setRes:%v", task.TaskId, err, setRes)
		return errors.New(500, "MeshConfigDistribution failed", "MeshConfigDistribution failed")
	}
	log.Infof("MeshConfigDistribution success, taskId:%s, setRes:%v", task.TaskId, setRes)
	return nil
}

// TODO mesh配置清理 可先采用redis模拟
func (t *CommonTaskBiz) MeshConfigCleanup(ctx context.Context, task *models.Task) error {
	var err error
	ctx, span := t.taskTracer.Start(ctx, "/CommonTask/MeshConfigCleanup", task.Metadata)
	defer func() {
		t.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTaskAndExtendInformation(ctx, "CommonTask.MeshConfigCleanup", task, nil)
		}, err)
	}()
	req := &pb.MeshDeleteConfigRequest{
		Key:       task.TaskId,
		RequestId: utils.GetRequestIdFromContext(ctx),
	}
	delRes, err := t.mesh.DeleteConfig(ctx, req)
	if err != nil || !models.IsSuccess(*delRes.Result) {
		log.Errorf("meshConfigCleanup failed, taskId:%s, delRes:%v, err:%v", task.TaskId, delRes, err)
		return errors.New(500, "MeshConfigCleanup failed", "MeshConfigCleanup failed")
	}

	log.Infof("meshConfigCleanup success, taskId:%s, delRes:%v", task.TaskId, delRes)
	return nil
}

// TODO mesh配置获取
func (t *CommonTaskBiz) MeshConfigGet(ctx context.Context, task *models.Task) (error, *pb.MeshConfigValue) {
	var err error
	ctx, span := t.taskTracer.Start(ctx, "/CommonTask/MeshConfigGet", task.Metadata)
	defer func() {
		t.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTaskAndExtendInformation(ctx, "CommonTask.MeshConfigGet", task, nil)
		}, err)
	}()

	req := &pb.MeshGetConfigRequest{
		Key:       task.TaskId,
		RequestId: utils.GetRequestIdFromContext(ctx),
	}
	getRes, err := t.mesh.GetConfig(ctx, req)
	if err != nil || !models.IsSuccess(*getRes.Result) {
		log.Errorf("meshConfigGet failed, taskId:%s, getRes:%v, err:%v", task.TaskId, getRes, err)
		return models.ErrMeshConfigNotFound, nil
	}
	log.Infof("meshConfigGet success, taskId:%s, getRes:%v, value:%+v", task.TaskId, getRes, *getRes.Value)
	return nil, getRes.Value
}

func (t *CommonTaskBiz) startDagJob(ctx context.Context, task *models.Task) error {
	var err error

	//失败错误处理
	defer func() {
		if err != nil {
			//获取dag图失败 or 调用工作流失败，更新任务状态为2/2
			task.SetBizStatus(models.TaskStateStopped)
			updateErr := t.repo.UpdateBizStatus(ctx, task)
			if updateErr != nil {
				t.log.WithContext(ctx).Errorf(" StartDagJob fail and startWorkfolw fail in update bizStatus, taskId:%s, err:%v", task.TaskId, updateErr)
			}
		}
	}()
	//todo 调用mpp-plugins获取DAG图
	dagGraph, err := t.getDagGraph(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Errorf("StartDagJob fail in getDagGroup, taskId:%s, err:%v", task.TaskId, err)
		return err
	}
	//todo DAG参数更新 暂不实现
	//err = t.repo.UpdateDagGroup(ctx, task)
	//if err != nil {
	//	t.log.WithContext(ctx).Errorf("StartTask fail in repo update dag group, taskId:%s, err:%v", task.TaskId, err)
	//	return err
	//}
	// todo 调用WF启动工作流
	err = t.workflow.StartDagJob(ctx, task, dagGraph)
	if err != nil {
		t.log.WithContext(ctx).Errorf("StartDagJob fail in StartDagJob, taskId:%s, err:%v", task.TaskId, err)

		return err
	}
	//更新任务内部状态
	//更新失败也返回成功，后续依赖同步逻辑进行重试
	task.InternalStatus = models.TaskStateActive
	updateErr := t.repo.UpdateResultAndAllStatus(ctx, task)
	//updateErr := t.repo.UpdateInternalStatus(ctx, task)
	if updateErr != nil {
		t.log.WithContext(ctx).Errorf("StartDagJob fail in UpdateInternalStatus, taskId:%s, err:%v", task.TaskId, err)
	}
	return nil
}

func (t *CommonTaskBiz) startTask(ctx context.Context, task *models.Task) error {
	var err error
	//1.资源申请 2.任务下发
	//if资源申请失败 or 任务下发失败 ，重试一次，重试失败，更新任务状态为2/2
	//if资源申请成功&任务下发成功 继续执行后续逻辑
	err = t.AllocAndDispatchTaskRetry(ctx, task, 1)
	if err != nil {
		t.log.WithContext(ctx).Errorf("StartTask fail in AllocAndDispatchTaskRetry, taskId:%s, err:%v", task.TaskId, err)
		//更新task状态
		//t.log.WithContext(ctx).Warnf("AllocAndDispatchTaskRetry startTask failed and update tasker biz_status, taskId:%s, err:%v", task.TaskId, err)
		task.SetBizStatus(models.TaskStateStopped)
		//TODO 这里如果err是上下文超时context deadline exceeded，会导致update操作直接返回失败，task状态不更新
		//目前RMS业务导致超时，业务层会进行start重试
		updateErr := t.repo.UpdateBizStatus(ctx, task)
		if updateErr != nil {
			t.log.WithContext(ctx).Errorf("AllocAndDispatchTaskRetry StopTask fail in repo update biz stopped, taskId:%s, error:%v", task.TaskId, updateErr)
		}
		return err
	}
	//todo 如果任务是dag_sub 配置下发mesh
	if task.IsDagSubTask() {
		err = t.MeshConfigDistribution(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Errorf("StartTask fail in MeshConfigDistribution, taskId:%s, err:%v", task.TaskId, err)
			return err
		}
	}
	//更新任务执行状态和work信息
	//更新失败也返回成功，后续依赖同步逻辑进行重试
	task.InternalStatus = models.TaskStateActive
	err = t.repo.UpdateScheduleWorkerAndInternalStatus(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Errorf("StartTask fail in UpdateScheduleWorkerAndInternalStatus, taskId:%s, err:%v", task.TaskId, err)
		return nil
	}
	return nil
}

/*
功能：
1.资源申请 2.任务下发引擎
*/
func (t *CommonTaskBiz) AllocAndDispatchTaskRetry(ctx context.Context, task *models.Task, maxReTryCount int) error {
	// 使用retry-go库重构重试逻辑
	retryOpts := []retry.Option{
		retry.Attempts(uint(maxReTryCount + 1)), // 包含首次尝试
		retry.OnRetry(func(n uint, err error) {
			t.log.WithContext(ctx).Warnf(
				"Retrying task allocation and dispatch (attempt %d/%d), taskId:%s, err:%v",
				n+1, maxReTryCount+1, task.TaskId, err,
			)
		}),
		retry.DelayType(retry.FixedDelay),
		retry.Delay(time.Millisecond),
		retry.Context(ctx),
	}

	var retryWorkerList []*models.Worker
	return retry.Do(
		func() error {
			//如果是重试任务，添加反亲和性，尽量不再申请同一台worker
			if retryWorkerList != nil && len(retryWorkerList) > 0 {
				task.CreateAffinity()
				for _, worker := range retryWorkerList {
					if task.RequestResource != nil && task.RequestResource.Affinity != nil {
						task.RequestResource.Affinity.AddMigrateAffinity(worker.Id, false)
						t.log.WithContext(ctx).Infof("ReStartTask task set affinity, taskId:%s, oldWork:%+v, Affinity:%v", worker, task.TaskId, task.RequestResource.Affinity)
					}
				}
			}
			worker, err := t.tryAllocAndDispatchTask(ctx, task)
			if err != nil {
				if worker != nil {
					retryWorkerList = append(retryWorkerList, worker)
					if freeErr := t.freeTaskResource(ctx, task); freeErr != nil {
						t.log.WithContext(ctx).Errorf("Failed to free resources, taskId:%s, err:%v",
							task.TaskId, freeErr)
					}
				}
				return err
			}
			return nil
		},
		retryOpts...,
	)
}

func (t *CommonTaskBiz) tryAllocAndDispatchTask(ctx context.Context, task *models.Task) (*models.Worker, error) {
	var err error
	err = t.allocResourceNotUpdateTask(ctx, task)
	if err != nil {
		return nil, err
	}

	err = t.dispatchTask(ctx, task)
	if err != nil {
		return task.Worker, err
	}
	return nil, nil
}

// 1.任务调度策略为restartAndUpdateParams:
//
//	（1）停止任务 （2）更新任务参数  (任务启动流程会重新拉起新任务)
//
// 2.其他：返回任务启动成功并更新任务状态
func (t *CommonTaskBiz) taskExistedPolicy(ctx context.Context, task *models.Task, existedTask *models.Task) error {
	//直播转码任务，添加forceRestart和schedulerForceRestart检测
	if existedTask.IsTranscodeJob() {
		extend := biz.StringToMap(existedTask.Metadata["extend"])
		forceRestart, schedulerForceRestart := livetranscode.ExtractTranscodeStartConfig(extend)
		if existedTask.BizStatus != models.TaskStateActive {
			if existedTask.InternalStatus == models.TaskStateActive {
				if !forceRestart && !schedulerForceRestart {
					t.log.WithContext(ctx).Errorf("task %s is still running, please wait...", existedTask.TaskId)
					return common.ErrTaskStillRunning
				}
				t.log.WithContext(ctx).Infof("task is already started, force restart, taskId:%s", existedTask.TaskId)
				//TODO check这里是否需要soft模式停止
				existedTask.InternalStatus = models.TaskStateStopped
				err := t.StopTask(ctx, existedTask.TaskId, "")
				if err != nil {
					t.log.WithContext(ctx).Errorf("task %s stop failed, err:%v", existedTask.TaskId, err)
				}
			}
		}
		return nil
	}
	var err error
	//当existedTask不存在时，直接返回
	if task == nil || existedTask == nil {
		t.log.WithContext(ctx).Infof("taskExistedPolicy task not exist, taskId:%s", task.TaskId)
		return nil
	}
	//当且任务参数未发生变化,直接返回
	if existedTask.Param != nil && task.Param != nil && existedTask.Param.Param == task.Param.Param {
		return nil
	}

	//当任务引擎参数发生变化，检查任务调度策略
	taskPolicy := t.conf.MatchTaskManagerConfig(task.Product, task.EngineModel, task.Tag)
	t.log.WithContext(ctx).Infof("taskExistedPolicy taskPolicy:%v,taskId:%s", taskPolicy, task.TaskId)
	//任务调度策略为restartAndUpdateParams,停止任务，更新任务参数,继续调度任务
	if taskPolicy != nil && taskPolicy.TaskRestartExistAndEngineMismatchPolicy == string(models.RestartAndUpdateParams) {
		t.log.WithContext(ctx).Infof("taskExistedPolicy StartTask param changed task, taskId:%s", task.TaskId)
		if existedTask.IsInternalRunning() {
			//停止任务
			err = t.stopJob(ctx, existedTask)
			if err != nil {
				t.log.WithContext(ctx).WithContext(ctx).Errorf("taskExistedPolicy StopTask fail in StartTask, taskId:%s, err:%v", existedTask.TaskId, err)
				return err
			}
		}
		//更新任务参数
		err = t.repo.UpdateEngineParam(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).WithContext(ctx).Errorf("taskExistedPolicy StartTask fail in repo update engine param and biz status, taskId:%s, err:%v", task.TaskId, err)
			return err
		}
		return nil
	}

	//其他策略：
	return nil
}

// 任务启动主流程
// 1.插入任务数据,检查任务已存在
// 2.检查任务是否时dag类型
// 3.启动任务
// todo 如果任务是dag类型，1.调用mpp-plugins获取DAG图 2.DAG参数更新 3.调用WF启动工作流
// 非dag类型任务：1.资源申请 2.任务下发agent
func (t *CommonTaskBiz) StartTask(ctx context.Context, task *models.Task) error {
	//插入时将biz_status状态设置为1
	task.SetBizStatus(models.TaskStateActive)
	//获取任务迁移配置
	task.MigrateConfig = models.GetMigrateConfigByProduct(task.Product)
	//正常状态插入数据，正常完成调度逻辑
	//当existedTask存在且biz状态为stop时，更新任务biz状态为active和各类参数，正常完成调度逻辑
	//当existedTask的biz为active时，进入existedTask处理逻辑

	var (
		err         error
		existedTask *models.Task
	)

	ctx, span := t.taskTracer.Start(ctx, "/CommonTask/Start", task.Metadata)
	defer func() {
		t.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTaskAndExtendInformation(ctx, "CommonTask.Start", task, nil)
		}, err)
	}()

	existedTask, err = t.saveAndUpdateOnDuplicate(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Errorf("StartTask fail in saveAndUpdateOnDuplicate, taskId:%v, err:%v", task.TaskId, err)
		return err
	}
	//t.cacheDb.Do()
	//当existedTask的biz为1
	t.conf.GetRedisUri()

	//当existedTask的biz为1，且任务参数发生变化,检查任务调度策略
	//1.任务调度策略为restartAndUpdateParams:停止任务，更新任务参数，继续调度任务
	//2.其他：返回任务启动成功并退出
	if existedTask != nil {
		if existedTask.Param != nil && task.Param != nil && existedTask.Param.Param != task.Param.Param {
			err = t.taskExistedPolicy(ctx, task, existedTask)
			if err != nil {
				t.log.WithContext(ctx).WithContext(ctx).Errorf("StartTask fail in taskExistedPolicy, taskId:%s, err:%v", task.TaskId, err)
				return err
			}
		}
		//任务已存在 && 任务已经在运行：返回任务启动成功
		if existedTask.IsInternalRunning() {
			t.log.WithContext(ctx).Infof("StartTask task already biz running and param not changed, taskId:%s", task.TaskId)
			return nil
		}
	}

	if t.cacheDb != nil {
		data, encodeErr := task.EncodeLiteToBytes()
		if encodeErr == nil {
			rst := t.cacheDb.Set(ctx, task.EncodeKVKey(), data, time.Minute*15)
			if rst != nil && rst.Err() != nil {
				t.log.Errorf("StartTask fail in cacheDb, taskId:%s, err:%v", task.TaskId, rst.Err())
			}
		}
	}

	//如果任务是dag类型：1.调用mpp-plugins获取DAG图 2.DAG参数更新 3.调用WF启动工作流 4.更新任务状态
	//非dag类型任务：1.资源申请 2.任务下发agent 3:更新任务状态和work信息
	if models.DagAsyncTaskType == task.TaskType {
		err = t.startDagJob(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Errorf("StartTask fail in StartDagJob, taskId:%s, err:%v", task.TaskId, err)
			return err
		}
		return nil
	} else {
		err = t.startTask(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Errorf("StartTask fail in StartTask, taskId:%s, err:%v", task.TaskId, err)
			return err
		}

	}

	return nil
}

func noNeedRestarting(existedTask *models.Task) bool {
	for _, engineModel := range models.PackagerTypeModels {
		if existedTask.EngineModel == engineModel {
			return true
		}
	}
	return false
}

func (t *CommonTaskBiz) saveAndUpdateOnDuplicate(ctx context.Context, task *models.Task) (*models.Task, error) {
	existTask, err := t.repo.FindByID(ctx, task.TaskId)
	if err != nil && !errors.Is(err, common.ErrTaskNotFoundError) {
		t.log.WithContext(ctx).Errorf("StartTask fail in repo find, taskId:%s, err:%v", task.TaskId, err)
		return nil, err
	}
	//如果任务已存在并且状态为running
	if err == nil && existTask.IsBizStatusRunning() {
		return existTask, nil
	}
	//如果任务之前不存在，插入
	//如果任务之前存在且状态为stopped,更新任务biz状态为running、result字段置为空、更新引擎参数等信息（不更新三元组和internal状态）
	err = t.repo.InsertUpdateOnDuplicate(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Errorf("StartTask fail in repo save, taskId:%s, err:%v", task.TaskId, err)
		return nil, err
	}

	return nil, nil
}

func (t *CommonTaskBiz) saveAndIgnoreOnDuplicate(ctx context.Context, task *models.Task) (bool, error) {
	notExist, err := t.repo.InsertIgnoreDuplicate(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Error("StartTask fail in repo save", err)
		return false, err
	}
	return notExist, nil
}

// 任务停止主流程：
// 1、查询任务
// 2、如果任务状态全为stopped，直接返回成功；
// 3、如果任务biz状态为running，更新任务biz状态为stopped
// 4、如果任务internal状态为stopped，返回成功
// 5、开始停止任务
func (t *CommonTaskBiz) StopTask(ctx context.Context, taskId string, extend string) error {
	var err error
	t.log.WithContext(ctx).Infof("stop tasker, taskId:%s", taskId)

	task, err := t.repo.FindByID(ctx, taskId)
	if err != nil {
		t.log.WithContext(ctx).Errorf("StopTask fail in FindByID, taskId:%s", taskId)
		return err
	}
	ctx, span := t.taskTracer.Start(ctx, "stopTask", task.Metadata)
	defer func() {
		t.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTaskAndExtendInformation(ctx, "", task, nil)
		}, err)
	}()

	ctx = utils.SetTaskProduct(ctx, task.Product)
	if !task.IsBizStatusRunning() && !task.IsInternalRunning() {
		t.log.WithContext(ctx).Warnf("StopTask tasker already stopped, taskId:%s", taskId)
		return nil
	}
	//如果任务状态为running，更新任务biz状态为stopped
	if task.IsBizStatusRunning() {
		task.SetBizStatus(models.TaskStateStopped)

		err = t.repo.UpdateBizStatus(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Errorf("StopTask fail in repo update biz stopped, taskId:%s", taskId)
			return err
		}
	}

	if !task.IsInternalRunning() {
		t.log.WithContext(ctx).Warnf("StopTask tasker already stooped, taskId:%s", taskId)
		return nil
	}
	if extend != "" {
		if task.Metadata == nil {
			task.Metadata = custom_types.Metadata{}
		}
		task.Metadata["extend"] = extend
	}
	err = t.stopJob(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Errorf("stopTask failed, taskId:%s, err:%v", task.TaskId, err)
		return err
	}
	if task.EngineModel == models.LiveTranscode {
		t.CleanTranscodeAttachment(ctx, task, lc.AttachmentTypeTEXT)
		t.CleanTranscodeAttachment(ctx, task, lc.AttachmentTypePIC)
	}

	if t.cacheDb != nil {
		rst := t.cacheDb.Del(ctx, task.EncodeKVKey())
		if rst != nil && rst.Err() != nil {
			t.log.Errorf("StartTask fail in cacheDb, taskId:%s, err:%v", task.TaskId, rst.Err())
		}
	}

	return nil
}

func (t *CommonTaskBiz) SubmitTask(_ context.Context, task *models.Task) (*models.Task, error) {
	return task, fmt.Errorf("unsupported for SubmitTask")
}

// 任务更新接口主流程
// 1、查询任务
// 2、更新引擎参数
func (t *CommonTaskBiz) UpdateTask(ctx context.Context, taskId, engineParams, engineParamsUrl string, updateEngine bool) error {
	task, err := t.repo.FindByID(ctx, taskId)
	if err != nil {
		t.log.WithContext(ctx).Errorf("UpdateTask fail in FindByID, taskId:%s, err:%v", taskId, err)
		return err
	}
	ctx = utils.SetTaskProduct(ctx, task.Product)
	ctx, span := t.taskTracer.Start(ctx, "UpdateTask", task.Metadata)
	defer func() {
		t.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTask(ctx, "UpdateTask", task)
		}, err)
	}()

	if !task.IsInternalRunning() {
		return models.ErrTaskNotRunning
	}
	task.Param.Param = engineParams
	task.Param.ParamUrl = engineParamsUrl
	if updateEngine {
		if task.TaskType == models.DagAsyncTaskType {
			err = t.workflow.UpdateDagJob(ctx, task)
		} else if task.EngineModel == models.LiveTranscode {
			err = t.liveTransCodeParamsAdapter.CorrectLiveTransCodeUpdateParams(task, engineParams)
			if err != nil {
				t.log.WithContext(ctx).Errorf("UpdateTask fail in liveTransCodeParamsAdapter update, taskId:%s, err:%v",
					task.TaskId, err)
				return err
			}
			err = t.engine.Update(ctx, task, task.Worker)
		} else {
			err = t.engine.Update(ctx, task, task.Worker)
		}
		if err != nil {
			t.log.WithContext(ctx).Errorf("UpdateTask fail in engine update, taskId:%s, err:%v",
				task.TaskId, err)
			return err
		}
	}
	//由于引擎参数更新无法触发任务同步机制，在更新数据库失败时添加重试逻辑，返回消息失败信息
	//todo 数据库更新失败后使用redis做兜底 qinxin
	retryCount := 0
	maxRetries := 2
	for {
		if retryCount >= maxRetries {
			break
		}
		err = t.repo.UpdateEngineParam(ctx, task)
		if err == nil {
			// 更新成功，跳出循环
			t.log.WithContext(ctx).Infof("UpdateTask success, taskId:%s, Param:%s ,ParamUrl:%s", task.TaskId, task.Param.Param, task.Param.ParamUrl)
			break
		}
		t.log.WithContext(ctx).Warnf("UpdateTask failed in repo update engine param, retrying... "+
			"taskId:%s, attempt:%d, err:%v", task.TaskId, retryCount+1, err)

		// 增加重试计数
		retryCount++
	}

	if err != nil {
		t.log.WithContext(ctx).Errorf("UpdateTask fail in repo update engine param after retries, "+
			"taskId:%s, finalError:%v,Param:%s ,ParamUrl:%s", task.TaskId, err, task.Param.Param, task.Param.ParamUrl)
		return err
	}

	return err
}

// 任务命令接口
// 1、查询任务
// 2、 (a) 如果任务类型为Dag:调用workflow ；(b)如果任务类型为普通任务：调用engineCommand
func (t *CommonTaskBiz) CommandTask(ctx context.Context, taskId, engineParams, engineParamsUrl string, commandType int32) error {
	task, err := t.repo.FindByID(ctx, taskId)
	if err != nil {
		t.log.WithContext(ctx).Errorf("CommandTask fail in FindByID, taskId:%s, err:%v", taskId, err)
		return err
	}
	ctx = utils.SetTaskProduct(ctx, task.Product)
	ctx, span := t.taskTracer.Start(ctx, "CommandTask", task.Metadata)
	defer func() {
		t.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTask(ctx, "CommandTask", task)
		}, err)
	}()

	if !task.IsInternalRunning() {
		return models.ErrTaskNotRunning
	}
	task.Param.ParamUrl = engineParamsUrl
	if task.IsTranscodeJob() {
		switch commandType {
		case lc.COMMAN_COMMAND:
			err = t.liveTransCodeParamsAdapter.CorrectLiveTransCodeCommandParams(task, engineParams)
		case lc.PIC_ADD_COMMAND:
			t.liveTransCodeParamsAdapter.CorrectLiveTransCodeAttachParams(task, engineParams, lc.AttachmentTypePIC, lc.AttachmentAddType)
		case lc.PIC_EDIT_COMMAND:
			t.liveTransCodeParamsAdapter.CorrectLiveTransCodeAttachParams(task, engineParams, lc.AttachmentTypePIC, lc.AttachmentEditType)
		case lc.TEXT_ADD_COMMAND:
			t.liveTransCodeParamsAdapter.CorrectLiveTransCodeAttachParams(task, engineParams, lc.AttachmentTypeTEXT, lc.AttachmentAddType)
		case lc.TEXT_EDIT_COMMAND:
			t.liveTransCodeParamsAdapter.CorrectLiveTransCodeAttachParams(task, engineParams, lc.AttachmentTypeTEXT, lc.AttachmentEditType)
		}
		if err != nil {
			t.log.WithContext(ctx).Errorf("CommandTask fail in liveTransCodeParamsAdapter, taskId:%s, err:%v",
				task.TaskId, err)
			return err
		}
		err = t.engine.Command(ctx, task, task.Worker)
	} else {
		task.Param.Param = engineParams
		if task.TaskType == models.DagAsyncTaskType {
			err = t.workflow.CommandDagJob(ctx, task)
		} else {
			err = t.engine.Command(ctx, task, task.Worker)
		}
	}
	if err != nil {
		t.log.WithContext(ctx).Errorf("CommandTask fail in engine command, taskId:%s, err:%v",
			task.TaskId, err)
		return err
	}

	return err
}

func (t *CommonTaskBiz) UpdateTaskStatus(ctx context.Context, taskId string, bizStatus int, internalStatus int) error {

	task, err := t.repo.FindByID(ctx, taskId)
	if err != nil {
		t.log.WithContext(ctx).Errorf("UpdateTaskStatus fail in FindByID, taskId:%s, err:%v", taskId, err)
		return err
	}
	ctx, span := t.taskTracer.Start(ctx, "UpdateTaskStatus", task.Metadata)
	defer func() {
		t.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTask(ctx, "UpdateTaskStatus", task)
		}, err)
	}()

	ctx = utils.SetTaskProduct(ctx, task.Product)
	if bizStatus != 0 {
		task.BizStatus, err = models.TaskStateFromInt(bizStatus)
		if err != nil {
			t.log.WithContext(ctx).Errorf("UpdateTaskStatus fail in TaskStateFromInt, bizStatus:%d, taskId:%s, err:%v", bizStatus, taskId, err)
			return err
		}
	}
	if internalStatus != 0 {
		task.InternalStatus, err = models.TaskStateFromInt(internalStatus)
		if err != nil {
			t.log.WithContext(ctx).Errorf("UpdateTaskStatus fail in TaskStateFromInt, internalStatus:%d, taskId:%s, err:%v", internalStatus, taskId, err)
			return err
		}
	}
	err = t.repo.UpdateInternalAndBizStatus(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Errorf("UpdateTaskStatus fail in repo update internal and biz status, taskId:%s, err:%v",
			task.TaskId, err)
		return err
	}

	t.log.WithContext(ctx).Infof("UpdateTaskStatus success, taskId:%s, bizStatus:%d, internalStatus:%d", task.TaskId, task.BizStatus, task.InternalStatus)
	return nil
}

func (t *CommonTaskBiz) GetTask(ctx context.Context, taskId string) (*models.Task, error) {
	task, err := t.repo.FindByID(ctx, taskId)
	if err != nil {
		t.log.WithContext(ctx).Errorf("GetTask fail in FindByID, taskId:%s, err:%v", taskId, err)
		return nil, err
	}

	return task, nil
}

// GetRunningTaskIds 支持查询优化
func (t *CommonTaskBiz) GetRunningTaskIds(ctx context.Context, taskInfo *models.Task) ([]*models.Task, error) {
	//根据任务信息查询任务
	tasks, err := t.repo.ListTasks(ctx, taskInfo)
	if err != nil {
		t.log.WithContext(ctx).Errorf("GetRunningTaskIds fail in ListBizRunningTasks, taskInfo:%+v, err:%v", taskInfo, err)
		return nil, err
	}
	return tasks, nil
}

func (t *CommonTaskBiz) GetRunningSimpleTasks(ctx context.Context, taskInfo *models.Task) ([]*models.Task, error) {
	//根据任务信息查询任务
	tasks, err := t.repo.ListTasksOptimized(ctx, taskInfo)
	if err != nil {
		t.log.WithContext(ctx).Errorf("GetRunningTaskIds fail in ListBizRunningTasks, taskInfo:%+v, err:%v", taskInfo, err)
		return nil, err
	}
	return tasks, nil
}

func (t *CommonTaskBiz) GetRunningTasks(ctx context.Context, engineModels []string) ([]*models.Task, error) {
	tasks, err := t.repo.FindRunning(ctx, engineModels)
	if err != nil {
		t.log.WithContext(ctx).Errorf("GetRunningTasks fail in FindRunning, err:%v", err)
		return nil, err
	}
	return tasks, nil
}

type CompleteRequest struct {
	JobID    string                 `json:"jobId,omitempty"`
	TaskID   string                 `json:"taskId,omitempty"`
	DataUrl  string                 `json:"dataUrl,omitempty"`
	Extends  map[string]interface{} `json:"extend,omitempty"`
	Duration int64                  `json:"realCostTime,omitempty"`
	Code     uint32                 `json:"code,omitempty"`
	Reason   string                 `json:"reason,omitempty"`
	Message  string                 `json:"message,omitempty"`
	Data     interface{}            `json:"data,omitempty"`
}

// 出参： bool:任务回调是否完成 error:错误信息
// 业务逻辑：
// a.任务结果为异常，且任务可迁移调度：尝试异步迁移任务  返回参数：false,nil
// b.任务结果为异常，且任务不可迁移或达到迁移上限：发送任务驱逐回调消息（mns），更新任务状态为停止 返回参数：true,nil
// c.其他情况：发送回调消息（mns）,更新任务状态为停止 返回参数：true,nil
func (t *CommonTaskBiz) CompleteTask(ctx context.Context, request *CompleteRequest) (bool, error) {
	task, err := t.repo.FindByID(ctx, request.TaskID)
	if err != nil {
		t.log.WithContext(ctx).Error("CompleteTask fail in FindByID ", err)
		return false, err
	}
	ctx = utils.SetTaskProduct(ctx, task.Product)
	ctx, span := t.taskTracer.Start(ctx, "CompleteTask", task.Metadata)
	defer func() {
		t.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTask(ctx, "server.CompleteTask", task)
		}, err)
	}()

	task.Result = &models.TaskResult{
		DataUrl:  request.DataUrl,
		Data:     request.Data,
		Code:     request.Code,
		Reason:   request.Reason,
		Message:  request.Message,
		Duration: request.Duration,
		Extends:  request.Extends,
	}

	//defer func() {
	//	taskType := v1.TaskType(tasker.TaskType)
	//	if taskType == v1.TaskType_Async || taskType == v1.TaskType_DagAsync {
	//		// todo 给redis queue回调个消息
	//		t.log.WithContext(ctx).Infof("call redis publish, taskId:%v, result:%v", tasker.TaskId, tasker.Result)
	//	}
	//}()

	//todo 这里要做策略区分
	//异常情况
	//a 任务结果为异常，尝试异步迁移任务（达到迁移上限则返回任务驱逐结果）
	//b 其他情况：继续正常完成回调
	if !isNormalCompleteCallback(task.Result) {
		t.log.WithContext(ctx).Warnf("completeTask not normal callback, taskId:%v, result:%v, task:%+v",
			task.TaskId, task, task.Result, task)

		if task.ShouldReSchedule() {
			//根据迁移次数做阶梯式延时 200-1000ms，避免频繁迁移
			task.ApplyMigrationDelay()
			//迁移任务
			taskIds := []string{task.TaskId}
			_, err = t.MigrateTask(ctx, taskIds, false)
			if err != nil {
				t.log.WithContext(ctx).Errorf("CompleteTask fail in MigrateTask, taskIds:%v, err:%v", taskIds, err)
				return false, err
			}
			return false, nil
		} else {
			//更新结果为任务驱逐，继续回调任务
			task.Result = NewEvictedResult()
			log.Warnf("completeTask not normal callback, taskId:%v, result:%v, task will be evicted", task.TaskId, task.Result)
		}
	}

	//为提高qps，调整task表更新顺序,改为分两步处理回调：1 资源返回+回调消息处理（如果有） 2 任务表更新
	//正常情况下，biz_status、internal_tatus、result同步更新
	//异常状态下完成biz_stauts和result更新
	err = t.replyTaskNotUpdateTask(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Errorf("completeTask fail in replyTaskNotUpdateTask, taskId:%s, err:%v", task.TaskId, err)
		task.SetBizStatus(models.TaskStateStopped)
		updateErr := t.repo.UpdateResult(ctx, task)
		if updateErr != nil {
			t.log.WithContext(ctx).Errorf("completeTask fail in repo update result, taskId:%s, err:%v",
				task.TaskId, updateErr)
		}
		return false, err
	}

	task.SetBizStatus(models.TaskStateStopped)
	task.SetInternalStatus(models.TaskStateStopped)

	err = t.repo.UpdateResultAndAllStatus(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Errorf("completeTask fail in repo update result and all status, taskId:%s, err:%v",
			task.TaskId, err)
		return false, err
	}
	//异步任务处理
	if task.TaskType == models.AsyncTaskType {
		// 流控信息添加
		err = t.sentinelBiz.DecreaseTaskRunningNum(ctx, task)
		if err != nil {
			//打印错误日志，不返回错误
			log.Errorf("checkTask failed in DecreaseTaskRunningNum. taskId:%s ,task:%+v, err:%v", task.TaskId, task, err)
		}
	}

	if t.cacheDb != nil {
		rst := t.cacheDb.Del(ctx, task.EncodeKVKey())
		if rst != nil && rst.Err() != nil {
			t.log.Errorf("Complete fail in cacheDb, taskId:%s, err:%v", task.TaskId, rst.Err())
		}
	}

	return true, nil
}

func isNormalCompleteCallback(taskResult *models.TaskResult) bool {
	reason := strings.ToLower(taskResult.Reason)
	abnormalReasons := []string{
		models.QuotaLimit,
		models.EngineUnavailable,
		models.InternalException,
		models.MigratedReason,
		models.EngineLostReason,
		models.EngineParamError,
		models.EngineRunShellErrorCode,
	}

	for _, s := range abnormalReasons {
		if strings.ToLower(s) == reason {
			return false
		}
	}
	return true
}

// 更新任务迁移
// 1、查询任务信息
// 2、去除dag主任务（不支持迁移）
// 3、更新任务内部状态和迁移信息
// 4、异步任务处理
func (t *CommonTaskBiz) MigrateTask(ctx context.Context, taskIds []string, tryStop bool) ([]string, error) {
	var (
		migrateSuccessTaskIds = []string{}
	)
	maxTaskIdsLen := 1000
	if taskIds != nil && len(taskIds) > maxTaskIdsLen {
		t.log.WithContext(ctx).Errorf("MigrateTask fail in check params, taskIds:%v, error:%v", taskIds, common.ErrRequestInvalid)
		return nil, common.ErrRequestInvalid
	}
	tasks, err := t.repo.FindByIDList(ctx, taskIds)
	if err != nil {
		t.log.WithContext(ctx).Errorf("MigrateTask fail in FindByID, error:%v", err)
		return nil, err
	}
	//dag主任务不支持迁移
	tasks = models.ExceptDagJob(tasks)

	// todo 正式版本打开
	// 直播转码任务走其他迁移路径
	tasks, transcodeTasks := models.ExceptTranscodeJob(tasks)

	if transcodeTasks != nil && len(transcodeTasks) > 0 {
		t.ReplayTranscodeTask(ctx, transcodeTasks)
	}

	t.log.WithContext(ctx).Warnf("migrate tasker, len input num:%d, len query result num:%d,taskIds:%v", len(taskIds), len(tasks), taskIds)

	//如果已经没有待迁移任务，则返回
	if tasks == nil || len(tasks) == 0 {
		t.log.WithContext(ctx).Warnf("MigrateTask and no task to be migrated, taskIds:%v", taskIds)
		return nil, nil
	}
	//这里过多会导致数据库更新超时，由scheduler限制单次batch大小上限=20

	//批量更新任务迁移信息
	taskResult := NewMigrateResult()
	err = t.repo.BatchUpdateAbnormalStopped(ctx, taskIds, taskResult)
	if err != nil {
		t.log.WithContext(ctx).Errorf("MigrateTask fail in BatchUpdateAbnormalStopped failed, taskIds:%v, err:%v", taskIds, err)
		return nil, err
	}
	for i := range tasks {
		tasks[i].Result = NewMigrateResult()
		task := tasks[i]
		migrateSuccessTaskIds = append(migrateSuccessTaskIds, task.TaskId)
		// TODO 应该通过chan放到后台异步操作，会不会丢失导致一些预期外的异常？？
		go func() {
			t.processAbnormalResult(context.Background(), "MigrateTask", task, false, tryStop)
		}()
	}
	return migrateSuccessTaskIds, nil
}

func (t *CommonTaskBiz) CommonMigrateTask(ctx context.Context, taskIds []string, workIds []string, workIps []string, tryStop bool) ([]string, error) {
	// TODO 这里对taskIds 的数量限制下，然外面分批传入
	//todo 这里如果同时对stream任务和batch任务进行迁移，如果两表出现相同id会导致正常任务迁移
	//优先按照taskIds维度进行迁移
	ctx = utils.SetRequestId(ctx)
	var (
		migrateTaskMap        = make(map[string]*models.Task) //待迁移任务map
		migrateTaskIdList     = []string{}                    //待迁移任务list
		migrateSuccessTaskIds []string                        // 迁移成功的任务id
		err                   error
	)
	//添加进待迁移map中
	if taskIds != nil && len(taskIds) > 0 {
		for _, taskId := range taskIds {
			migrateTaskMap[taskId] = &models.Task{
				TaskId: taskId,
			}
		}
	}

	if (workIds != nil && len(workIds) > 0) || (workIds != nil && len(workIps) > 0) {
		//按worker维度进行任务查找
		listTasks, err := t.ListMigrateTaskByWorkers(ctx, workIds, workIps)
		if err != nil {
			t.log.WithContext(ctx).Errorf("CommonMigrateTask fail in MigrateTaskByWorker, error:%v", err)
			return nil, err
		}
		// 合并
		if listTasks != nil && len(listTasks) > 0 {
			for _, task := range listTasks {
				migrateTaskMap[task.TaskId] = task
			}
		}
	}
	// 将map中任务添加进待迁移list中
	for _, task := range migrateTaskMap {
		migrateTaskIdList = append(migrateTaskIdList, task.TaskId)
	}

	if migrateTaskIdList != nil && len(migrateTaskIdList) > 0 {

		const batchSize = 1000
		if len(taskIds) > batchSize {
			return nil, common.ErrRequestInvalid
		}
		migrateSuccessTaskIds, err = t.MigrateTask(ctx, migrateTaskIdList, tryStop)
		if err != nil {
			t.log.WithContext(ctx).Errorf("CommonMigrateTask failed ,taskIds:%v, error:%v", taskIds, err)
			return nil, err
		}
	}
	return migrateSuccessTaskIds, nil
}

// 筛选迁移任务添加任务迁移信息
// 1、查询任务信息
// 2、去除dag主任务（不支持迁移）
// 3、更新任务内部状态和迁移信息
//func (t *CommonTaskBiz) FilterAndAddMigrateInfo(ctx context.Context, taskIds []string, tryStop bool) ([]*models.Task, error) {
//
//	maxTaskIdsLen := 1000
//	if taskIds != nil && len(taskIds) > maxTaskIdsLen {
//		t.log.WithContext(ctx).Errorf("AddMigrateInfo fail in check params, taskIds:%v, error:%v", taskIds, common.ErrRequestInvalid)
//		return nil, common.ErrRequestInvalid
//	}
//	tasks, err := t.repo.FindByIDList(ctx, taskIds)
//	if err != nil {
//		t.log.WithContext(ctx).Errorf("AddMigrateInfo fail in FindByID, error:%v", err)
//		return nil, err
//	}
//	//dag主任务不支持迁移
//	tasks = models.ExceptDagJob(tasks)
//	t.log.WithContext(ctx).Warnf("FilterAndAddMigrateInfo tasker, len input num:%d, len query result num:%d,taskIds:%v", len(taskIds), len(tasks), taskIds)
//
//	//如果已经没有待迁移任务，则返回
//	if tasks == nil || len(tasks) == 0 {
//		t.log.WithContext(ctx).Warnf("AddMigrateInfo and no task to be migrated, taskIds:%v", taskIds)
//		return nil, nil
//	}
//	//这里过多会导致数据库更新超时，由scheduler限制单次batch大小上限=20
//	//先批量更新任务迁移信息
//	taskResult := NewMigrateResult()
//	err = t.repo.BatchUpdateAbnormalStopped(ctx, taskIds, taskResult)
//	if err != nil {
//		t.log.WithContext(ctx).Errorf("MigrateTask fail in BatchUpdateAbnormalStopped failed, taskIds:%v, err:%v", taskIds, err)
//		return nil, err
//	}
//	//更新返回的任务信息
//	for i := range tasks {
//		tasks[i].Result = NewMigrateResult()
//		tasks[i].InternalStatus = models.TaskStateStopped
//	}
//	return tasks, nil
//}

/*
worker维度任务迁移
按照workerId或workerIp查询所有biz_status=active的任务
如果workerId不为空，则优先按workerId查询
*/
func (t *CommonTaskBiz) MigrateTaskByWorker(ctx context.Context, workerIds []string, workIps []string, tryStop bool) ([]string, error) {
	var err error
	t.log.WithContext(ctx).Warnf("start MigrateTaskByWorkerIds, workerIds:%v", workerIds)
	var taskIds []string

	if workerIds != nil && len(workerIds) > 0 {
		for _, workerId := range workerIds {
			tasks, err := t.repo.ListRunningTaskByWorkerId(ctx, workerId)
			if err != nil {
				t.log.WithContext(ctx).Errorf("MigrateTaskByWorkerId fail in  ListRunningTaskByWorkerId, workerId:%s, err:%v",
					workerId, err)
				return nil, err
			}
			for i := range tasks {
				taskIds = append(taskIds, tasks[i].TaskId)
			}
		}
	} else if workIps != nil && len(workIps) > 0 {
		for _, workIp := range workIps {
			tasks, err := t.repo.ListRunningTaskByWorkerIp(ctx, workIp)
			if err != nil {
				t.log.WithContext(ctx).Errorf("MigrateTaskByWorkerIps fail in  ListRunningTaskByWorkerId, workIp:%s, err:%v",
					workIp, err)
				return nil, err
			}
			for i := range tasks {
				taskIds = append(taskIds, tasks[i].TaskId)
			}
		}
	} else {
		return nil, errors.New(404, "InvalidMigrateParameters", "can not find any task by workerIds")
	}

	if taskIds == nil || len(taskIds) == 0 {
		return nil, errors.New(404, "InvalidMigrateParameters", "can not find any task by workerIds")
	}
	successMigrateTaskIds, err := t.MigrateTask(ctx, taskIds, tryStop)
	if err != nil {
		t.log.WithContext(ctx).Errorf("MigrateTaskByWorkerId fail in MigrateTask, workerIds:%v, taskIds:%v, err:%v",
			workerIds, taskIds, err)
		return nil, err
	}
	t.log.WithContext(ctx).Warnf("end MigrateTaskByWorkerIds, workerIds:%v, workerIps:%v, workerTaskIds:%v, successMigrateTaskIds:%v",
		workerIds, workIps, taskIds, successMigrateTaskIds)
	return successMigrateTaskIds, nil
}

/*
worker维度任务查找任务
按照workerId或workerIp查询所有biz_status=active的任务
如果workerId不为空，则优先按workerId查询
*/
func (t *CommonTaskBiz) ListMigrateTaskByWorkers(ctx context.Context, workerIds []string, workIps []string) ([]*models.Task, error) {
	t.log.WithContext(ctx).Warnf("start MigrateTaskByWorkerIds, workerIds:%v", workerIds)
	var (
		taskIds    []string
		taskReuslt []*models.Task
		taskMap    = make(map[string]*models.Task)
	)

	if workerIds != nil && len(workerIds) > 0 {
		for _, workerId := range workerIds {
			tasks, err := t.repo.ListRunningTaskByWorkerId(ctx, workerId)
			if err != nil {
				t.log.WithContext(ctx).Errorf("MigrateTaskByWorkerId fail in  ListRunningTaskByWorkerId, workerId:%s, err:%v",
					workerId, err)
				return nil, err
			}
			for i := range tasks {
				taskIds = append(taskIds, tasks[i].TaskId)
				taskMap[tasks[i].TaskId] = tasks[i]
			}
		}
	}
	if workIps != nil && len(workIps) > 0 {
		for _, workIp := range workIps {
			tasks, err := t.repo.ListRunningTaskByWorkerIp(ctx, workIp)
			if err != nil {
				t.log.WithContext(ctx).Errorf("MigrateTaskByWorkerIps fail in  ListRunningTaskByWorkerId, workIp:%s, err:%v",
					workIp, err)
				return nil, err
			}
			for i := range tasks {
				taskIds = append(taskIds, tasks[i].TaskId)
				taskMap[tasks[i].TaskId] = tasks[i]
			}
		}
	}
	if taskMap != nil && len(taskMap) != 0 {
		for _, task := range taskMap {
			taskReuslt = append(taskReuslt, task)
		}
	}

	return taskReuslt, nil
}

/*
worker维度任务迁移
*/
func (t *CommonTaskBiz) MigrateTaskByWorkerIps(ctx context.Context, workerIps []string, tryStop bool) ([]string, error) {
	var err error
	t.log.WithContext(ctx).Warnf("start MigrateTaskByWorkerIps, workerIds:%v", workerIps)
	var taskIds []string
	for _, workerIp := range workerIps {
		tasks, err := t.repo.ListRunningTaskByWorkerIp(ctx, workerIp)
		if err != nil {
			t.log.WithContext(ctx).Errorf("MigrateTaskByWorkerIps fail in  ListRunningTaskByWorkerId, workerIp:%s, err:%v",
				workerIp, err)
			return nil, err
		}
		for i := range tasks {
			taskIds = append(taskIds, tasks[i].TaskId)
		}
	}
	if taskIds == nil || len(taskIds) == 0 {
		return nil, errors.New(404, "InvalidMigrateParameters", "can not find any task by workerIps")
	}
	_, err = t.MigrateTask(ctx, taskIds, tryStop)
	if err != nil {
		t.log.WithContext(ctx).Errorf("MigrateTaskByWorkerIps fail in MigrateTask, workerIps:%v, taskIds:%v, err:%v",
			workerIps, taskIds, err)
		return nil, err
	}
	return taskIds, nil
}

func convertToMap(tasks []*models.Task) map[string]*models.Task {
	taskMap := make(map[string]*models.Task, len(tasks))
	for i := range tasks {
		taskMap[tasks[i].TaskId] = tasks[i]
	}
	return taskMap
}

/*
异常任务处理，由任务迁移和任务同步触发
*/
func (t *CommonTaskBiz) processAbnormalResult(ctx context.Context, source string, task *models.Task, forceSchedule bool, tryStop bool) {
	ctx = utils.SetTaskId(ctx, task.TaskId)
	ctx = utils.SetTaskProduct(ctx, task.Product)
	var err error
	ctx, span := t.taskTracer.Start(ctx, source+"/processAbnormalResult", task.Metadata)
	defer func() {
		t.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTask(ctx, "processAbnormalResult", task)
		}, err)
	}()

	//migrate接口对外使用时先调用引擎进行一次任务停止 ,如果失败,则继续迁移。
	//scheduler调用migrate接口时，不会尝试停止任务
	if tryStop {
		t.log.WithContext(ctx).Infof("processAbnormalResult tasker, try stop task, taskId:%s, task:%+v", task.TaskId, task)

		if task.TaskType == models.StreamTaskType {
			err = t.engine.Stop(ctx, task, task.Worker)
		} else {
			err = t.engine.Cancel(ctx, task, task.Worker)
		}
		if err != nil && !common.IsEngineTaskNotExist(err) {
			var e *common.EngineError
			if errors.As(err, &e) {
				t.log.WithContext(ctx).Warnf("processAbnormalResult stopTask fail in stopEngineTask,taskId:%s, err.Reason:%v", task.TaskId, e.Reason)
			}
			t.log.WithContext(ctx).Warnf("processAbnormalResult stopTask fail in stopEngineTask, taskId:%s", task.TaskId)
			//TODO 停止失败也继续迁移，后续任务停止交给引擎任务同步逻辑处理（可能会导致任务同时在多worker偷跑）
		}
	}

	err = t.freeTaskResource(ctx, task)
	if err != nil && !errors.IsNotFound(err) {
		t.log.WithContext(ctx).Errorf("processAbnormalResult fail in freeTaskResource, taskId:%s, err:%v", task.TaskId, err)
		return
	}

	if !task.IsBizStatusRunning() {
		t.log.WithContext(ctx).Warnf("processAbnormalResult tasker already stopped, taskId:%s, tasker:%+v", task.TaskId, task)
		return
	}

	if task.ShouldReSchedule() {
		t.log.WithContext(ctx).Infof("processAbnormalResult tasker should reschedule, taskId:%s, tasker:%+v", task.TaskId, task)
		err := t.scheduleAndDispatchTask(ctx, source, task, forceSchedule)
		//todo 增加任务一次重试结果
		if err != nil {
			t.log.WithContext(ctx).Errorf("processAbnormalResult fail in scheduleAndDispatchTask, taskId:%s, err:%v",
				task.TaskId, err)
			return
		}
		//任务查询->尝试迁移->迁移次数更新 该流程查询到迁移次数更新过程中未加锁，存在并发问题，可能会超过迁移次数上限
		//采用数据库原子操作更新migrateTimes
		//task.Result = nil
		//task.MigrateTimes = task.MigrateTimes + 1
		err = t.repo.IncreaseMigrateTimesAndClearResult(ctx, task.TaskId)
		if err != nil {
			t.log.WithContext(ctx).Errorf("processAbnormalResult fail in repo update result, taskId:%s, err:%v",
				task.TaskId, err)
		}
		t.log.WithContext(ctx).Infof("processAbnormalResult success, taskId:%s ,MigrateTimes:%d", task.TaskId, task.MigrateTimes)
	} else {
		//如果已经达到了迁移上限，则告警，人工介入兜底
		if task.HasReachedMaxMigrations() {
			t.log.WithContext(ctx).Errorf("processAbnormalResult tasker already reached max migrations, taskId:%s, tasker:%+v", task.TaskId, task)
		}
		//更新任务运行reason：InvalidStatus.Evicted 任务驱逐
		task.Result = NewEvictedResult()
		err = t.replyTaskAndUpdateInternalStopped(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Errorf("processAbnormalResult fail in replyTaskAndUpdateInternalStopped, taskId:%s, err:%v",
				task.TaskId, err)
			return
		}
		task.SetBizStatus(models.TaskStateStopped)

		err = t.repo.UpdateResult(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Errorf("processAbnormalResult fail in repo update biz status, taskId:%s, err:%v",
				task.TaskId, err)
		}
	}
}

func (t *CommonTaskBiz) allocResource(ctx context.Context, task *models.Task) error {
	worker, err := t.scheduler.AllocResource(ctx, task)
	if err != nil {
		//TODO 如果reason = RESOURCE_LOST message = resource already lost.  错误处理中需进行资源归还
		//通过判断 *Error.Reason 和 *Error.Code
		e := errors.FromError(err)
		if e.Reason == v1.ErrorReason_RESOURCE_LOST.String() {
			// free资源
			t.log.WithContext(ctx).Warnf("allocResource fail in RESOURCE_LOST, try freeTaskResource, taskId:%s, err:%v", task.TaskId, err)
			freeErr := t.freeTaskResource(ctx, task)
			if freeErr != nil {
				t.log.WithContext(ctx).Errorf("allocResource fail in RESOURCE_LOST and freeTaskResource fail, taskId:%s, err:%v", task.TaskId, freeErr)
			}
		}
		t.log.WithContext(ctx).Errorf("startTask fail in allocResource, taskId:%s, err:%v", task.TaskId, err.Error())
		return &common.ScheduleError{Code: e.Code, Reason: common.ErrorScheduleAllocResourceFailed, Message: err.Error()}
	}

	task.Worker = worker

	err = t.repo.UpdateScheduleWorker(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Errorf("startTask fail in update schedule resource, taskId:%s, err:%v", task.TaskId, err)
		e := errors.FromError(err)
		return &common.DbError{Code: e.Code, Reason: common.ErrorDbUpdateScheduleWorkerFailed, Message: err.Error()}
	}

	t.log.WithContext(ctx).Infof("allocResource success, taskId:%s, worker:%+v, task:%+v", task.TaskId, worker, task)
	return nil
}

func (t *CommonTaskBiz) allocResourceNotUpdateTask(ctx context.Context, task *models.Task) error {
	worker, err := t.scheduler.AllocResource(ctx, task)
	if err != nil {
		//如果reason = RESOURCE_LOST message = resource already lost.  错误处理中需进行资源归还
		//通过判断 *Error.Reason 和 *Error.Code
		//todo qinxin alloc内部有错误结构的转换，这里需要进一步测试FromError后是否能够获取到正确的reason
		e := errors.FromError(err)
		if e.Reason == v1.ErrorReason_RESOURCE_LOST.String() {
			// free资源
			t.log.WithContext(ctx).Warnf("allocResourceNotUpdateTask fail in RESOURCE_LOST, try freeTaskResource, taskId:%s, err:%v", task.TaskId, err)
			freeErr := t.freeTaskResource(ctx, task)
			if freeErr != nil {
				t.log.WithContext(ctx).Errorf("allocResourceNotUpdateTask fail in RESOURCE_LOST and freeTaskResource fail, taskId:%s, err:%v", task.TaskId, freeErr)
			}
		}
		t.log.WithContext(ctx).Errorf("allocResourceNotUpdateTask startTask fail in allocResource, taskId:%s, err:%v", task.TaskId, err)
		return &common.ScheduleError{Code: e.Code, Reason: common.ErrorScheduleAllocResourceFailed, Message: err.Error()}
	}
	task.Worker = worker

	//err = t.repo.UpdateScheduleWorker(ctx, tasker)
	//if err != nil {
	//	t.log.WithContext(ctx).Errorf("startTask fail in update schedule resource, taskId:%v, err:%v", tasker.TaskId, err)
	//	return &DbError{Reason: "updateScheduleWorkerErr", Message: err.Error()}
	//}
	t.log.WithContext(ctx).Infof("allocResourceNotUpdateTask success, taskId:%s, worker:%+v, task:%+v", task.TaskId, worker, task)
	return nil
}

func (t *CommonTaskBiz) scheduleAndDispatchTask(ctx context.Context, source string, task *models.Task, forceScheduler bool) error {
	ctx = utils.SetTaskId(ctx, task.TaskId)
	ctx = utils.SetTaskProduct(ctx, task.Product)
	var err error
	ctx, span := t.taskTracer.Start(ctx, source+"/scheduleAndDispatchTask", task.Metadata)
	defer func() {
		t.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTask(ctx, "scheduleAndDispatchTask", task)
		}, err)
	}()
	taskId := task.TaskId
	task, err = t.repo.FindByID(ctx, task.TaskId)
	if err != nil {
		//避免task为nil，打印panic
		t.log.WithContext(ctx).Errorf("scheduleAndDispatchTask fail in FindByID, taskId:%s, err:%v", taskId, err)
		return err
	}

	if !forceScheduler && task.IsInternalRunning() && task.Worker != nil {
		t.log.WithContext(ctx).Infof("scheduleAndDispatchTask already running, taskId:%s", task.TaskId)
		return nil
	}

	//如果任务是dag主任务，进入dag任务调度流程
	if task.IsDagJob() {
		err = t.startDagJob(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Errorf("scheduleAndDispatchTask fail in startDagJob, taskId:%s, err:%v", task.TaskId, err)
			return err
		}
		return nil
	}

	//if task.IsDagDependencyTask() {
	//	log.Infof("scheduleAndDispatchTask tasker is dag dependency task, taskId:%s", task.TaskId)
	//	dependencyReady, err := t.checkDagDependencyReady(ctx, task)
	//	if err != nil {
	//		t.log.WithContext(ctx).Error("checkDagDependencyReady fail in checkDagDependencyReady", err)
	//		return err
	//	}
	//	if !dependencyReady {
	//		t.log.WithContext(ctx).Info("dependency not ready, not schedule")
	//	}
	//	return nil
	//}

	//如果包含依赖任务， 检查依赖任务是否完成，并将依赖任务ip地址add到请求参数中
	if len(task.Dependencies) != 0 {
		isComplete, err := t.CheckAndCompletedDependencyByDB(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Error("complementDependencyIp fail in complementDependencyIp", err)
			return err
		}
		if !isComplete {
			t.log.WithContext(ctx).Info("dependency complete failed, not schedule, taskId:%s", task.TaskId)
			return models.ErrDependencyNotReady
		}
	}

	//如果是迁移任务，添加反亲和性
	if task.Result != nil && task.Result.Reason == models.MigratedReason && task.Worker != nil {
		task.CreateAffinity()
		task.RequestResource.Affinity.AddMigrateAffinity(task.Worker.Id, false)
		t.log.WithContext(ctx).Infof("scheduleAndDispatchTask tasker AddMigrateAffinity success, taskId:%s, task.woker:%+v", task.TaskId, task.Worker)
	}

	startTime := time.Now()
	for tryIdx := 0; tryIdx < 2; tryIdx++ {
		//调用一次update，更新worker信息
		err = t.allocResource(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Warnf("scheduleAndDispatchTask fail in allocResource, taskId:%s, err:%v", task.TaskId, err)
			continue
		}

		err = t.dispatchTask(ctx, task)
		if err == nil {
			// 成功，则直接退出
			break
		}
		t.log.WithContext(ctx).Warnf("scheduleAndDispatchTask fail in dispatchTask, taskId:%s, err:%v", task.TaskId, err)
		if err != nil && time.Now().Sub(startTime).Milliseconds() >= 500 {
			// 时间超过500ms，则不再重试
			break
		} else if err != nil && (strings.Contains(err.Error(), "connection refused") || strings.Contains(err.Error(), "timeout")) {
			// agent连接失败，可能是不在线，或者网络波动，进入重试一次
			continue
		} else {
			// 其它什么莫名其妙的错误？？？？
			_ = t.freeTaskResource(ctx, task)
		}
	}

	if err != nil {
		t.log.WithContext(ctx).Errorf("startTask failed in alloc or dispatch. err:%v, task:%+v", err, task)
		return err
	}
	//todo 如果任务是dag子任务，下发配置到mesh qinxin
	if task.IsDagSubTask() {
		err = t.MeshConfigDistribution(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Errorf("scheduleAndDispatchTask fail in MeshConfigDistribution, taskId:%s, err:%v", task.TaskId, err)
			return err
		}
	}
	if !task.IsInternalRunning() {
		// 这里要进一步区分任务类型，同步实时任务直接就返回失败了，没有后续的机会了，不能都依赖上层重试
		task.SetInternalStatus(models.TaskStateActive)
		err = t.repo.UpdateInternalStatus(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Error("startTask fail in update db status-running.", err)
			e := errors.FromError(err)
			return &common.DbError{Code: e.Code, Reason: "updateInternalStatusErr", Message: err.Error()}
		}
	}

	t.log.WithContext(ctx).Infof("scheduleAndDispatchTask success, taskId:%s, task:%+v", task.TaskId, task)

	return err
}

// 任务下发到engine
func (t *CommonTaskBiz) dispatchTask(ctx context.Context, task *models.Task) error {
	var err error = nil

	//添加AK/SK到notify中,agent回调需要
	if task.Notify != "" {
		task.Notify = t.convertNotifyUrl(task.Notify)
	}

	if task.TaskType == models.StreamTaskType {
		err = t.engine.Start(ctx, task, task.Worker)
	} else {
		err = t.engine.Submit(ctx, task, task.Worker)
	}

	if err != nil && !common.IsEngineTaskAlreadyExist(err) {
		t.log.WithContext(ctx).Errorf("dispatchTask fail in engine start, taskId:%s, err:%v", task.TaskId, err)
		return err
	}

	//tasker.InternalStatus = TaskStateActive
	//err = t.repo.UpdateInternalStatus(ctx, tasker)
	//if err != nil {
	//	t.log.WithContext(ctx).Error("startTask fail in update running", err)
	//	return &DbError{Reason: "updateInternalStatusErr", Message: err.Error()}
	//}

	return nil
}

// todo 检查是否保留该方法 qinxin
func (t *CommonTaskBiz) checkDagDependencyReady(ctx context.Context, task *models.Task) (bool, error) {
	tasks, err := t.repo.FindByJobId(ctx, task.JobId)
	if err != nil {
		t.log.WithContext(ctx).Error("checkDagDependencyReady fail in FindByJobId", err)
		e := errors.FromError(err)
		return false, &common.DbError{Code: e.Code, Reason: "FindByJobIdErr", Message: err.Error()}
	}
	for i := range task.Dependencies {
		dependencyTaskId := task.Dependencies[i].TaskId
		for j := range tasks {
			if tasks[j].TaskId == dependencyTaskId {
				if !tasks[j].IsInternalRunning() {
					//依赖任务未运行
					t.log.WithContext(ctx).Info("checkDagDependencyReady failed and dependency not ready,taskId:%s,notReadyDependencyTask:%+v,Dependency:%+v", task.TaskId, task.Dependencies[i], task.Dependencies)
					return false, nil
				}
				break
			}
		}
	}
	return true, nil
}

func (t *CommonTaskBiz) stopJob(ctx context.Context, task *models.Task) error {
	var err error
	if task.TaskType == models.DagAsyncTaskType {
		err = t.stopDagJob(ctx, task)
	} else {
		err = t.stopTask(ctx, task)
	}
	return err
}

// 1.检查依赖任务是否ready-DB查询
// 2.添加依赖任务ip地址到请求参数中
func (t *CommonTaskBiz) CheckAndCompletedDependencyByDB(ctx context.Context, task *models.Task) (bool, error) {

	if task.Dependencies != nil && len(task.Dependencies) > 0 {
		log.Infof("CheckAndCompletedDependencyByDB start taskId:%s, jobId:%s, task.Dependencies:%+v", task.TaskId, task.JobId, task.Dependencies)

		var taskIds []string
		for _, dep := range task.Dependencies {
			taskIds = append(taskIds, dep.TaskId)
		}

		// 批量获取依赖任务
		dependenciesTasks, err := t.repo.FindByIDList(ctx, taskIds)
		if err != nil {
			t.log.WithContext(ctx).Error("CheckAndCompletedDependencyByDB fail in FindByIDList", err)
			return false, err
		}

		// 创建一个dependencyTask映射表，方便根据任务ID查找任务
		depMap := make(map[string]*models.Task)
		for _, depTask := range dependenciesTasks {
			depMap[depTask.TaskId] = depTask
		}

		for i, dep := range task.Dependencies {
			dependencyTask, ok := depMap[dep.TaskId]
			if !ok || dependencyTask == nil || !dependencyTask.IsBizStatusRunning() ||
				!dependencyTask.IsInternalRunning() || dependencyTask.Worker == nil {
				//依赖任务未运行或不存在于结果集中
				t.log.WithContext(ctx).Infof(
					"CheckAndCompletedDependencyByDB failed and dependency not ready, taskId:%s, notReadyDependencyTask:%+v",
					task.TaskId, dep,
				)
				if time.Now().Sub(task.GmtCreate) > time.Minute {
					// 依赖任务等待太久
					waitTime := time.Now().Sub(task.GmtCreate)
					t.log.WithContext(ctx).Warnf("checkDependency and task wait too long , taskId:%s，TaskDependency:%s, CreatedTime:%v, waitTime:%v", task.TaskId, task.Dependencies[i].TaskId, task.GmtCreate, waitTime)
				}
				return false, nil
			}
			task.Dependencies[i].Fields["ip"] = dependencyTask.Worker.Ip
		}

		t.log.WithContext(ctx).Infof(
			"CheckAndCompletedDependencyByDB success, taskId:%s, task:%+v, tdenpendencies:%+v",
			task.TaskId, task, task.Dependencies,
		)
	}
	return true, nil
}

func (t *CommonTaskBiz) stopTask(ctx context.Context, task *models.Task) error {
	err := t.engine.Stop(ctx, task, task.Worker)
	if err != nil && !common.IsEngineTaskNotExist(err) {
		var e *common.EngineError
		if errors.As(err, &e) {
			t.log.WithContext(ctx).Warnf("stopTask fail in stopEngineTask, err.Reason:%v,err.message:%v", e.Reason, e.Message)
		}
		t.log.WithContext(ctx).Errorf("stopTask fail in stopEngineTask, err:%v", err)
		return err
	}

	err = t.freeTaskResource(ctx, task)
	if err != nil && !common.IsScheduleReourceNotExist(err) {
		t.log.WithContext(ctx).Errorf("stopTask fail in freeTaskResource, taskId:%s, err:%v", task.TaskId, err)
		return err
	}

	//TODO if 任务类型==dag_sub mesh配置清理
	if task.IsDagSubTask() {
		err = t.MeshConfigCleanup(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Errorf("stopTask fail in meshConfigCleanup, taskId:%s, err:%v", task.TaskId, err)
			return err
		}
	}

	//更新任务internal状态为stop
	task.InternalStatus = models.TaskStateStopped
	task, err = t.updateInternalStopped(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Warn("stopTask fail in updateInternalStopped")
		e := errors.FromError(err)
		return &common.DbError{
			Code:    e.Code,
			Reason:  "UpdateInternalStoppedFail",
			Message: err.Error(),
		}
	}
	return nil
}

// todo dag主任务停止
// 1、调用workflow下发停止请求
// 2、更新任务internal状态为stop
func (t *CommonTaskBiz) stopDagJob(ctx context.Context, task *models.Task) error {
	var err error
	//todo 调用workflow下发停止请求
	err = t.workflow.StopDagJob(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Errorf("stopDagJob fail in stopDagJob, taskId:%s, err:%v", task.TaskId, err)
		return err
	}
	//更新任务internal状态为stop
	task.InternalStatus = models.TaskStateStopped
	task, err = t.updateInternalStopped(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Warn("stopDagJob fail in updateInternalStopped")
		e := errors.FromError(err)
		return &common.DbError{
			Code:    e.Code,
			Reason:  "UpdateInternalStoppedFail",
			Message: err.Error(),
		}
	}
	return nil
}

func (t *CommonTaskBiz) cancelTask(ctx context.Context, task *models.Task) error {
	err := t.engine.Cancel(ctx, task, task.Worker)
	if err != nil && !common.IsEngineTaskNotExist(err) {
		var e *common.EngineError
		if errors.As(err, &e) {
			t.log.WithContext(ctx).Warnf("cancelTask fail in stopEngineTask, err.Reason:%v,err.message:%v", e.Reason, e.Message)
		}
		t.log.WithContext(ctx).Errorf("cancelTask fail in stopEngineTask, err:%v", err)
		return err
	}

	err = t.freeTaskResource(ctx, task)
	if err != nil && !common.IsScheduleReourceNotExist(err) {
		t.log.WithContext(ctx).Errorf("cancelTask fail in freeTaskResource, taskId:%s, err:%v", task.TaskId, err)
		return err
	}

	task.InternalStatus = models.TaskStateStopped
	task, err = t.updateInternalStopped(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Warn("cancelTask fail in updateInternalStopped")
		e := errors.FromError(err)
		return &common.DbError{
			Code:    e.Code,
			Reason:  "UpdateInternalStoppedFail",
			Message: err.Error(),
		}
	}
	return nil
}

func (t *CommonTaskBiz) updateInternalStopped(ctx context.Context, task *models.Task) (*models.Task, error) {
	err := t.repo.UpdateInternalStatus(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Error("updateInternalStoppedErr", err)
		e := errors.FromError(err)
		return nil, &common.DbError{Code: e.Code, Reason: "updateInternalStoppedErr", Message: err.Error()}
	}
	return task, nil
}

func (t *CommonTaskBiz) freeTaskResource(ctx context.Context, task *models.Task) error {
	err := t.scheduler.FreeResource(ctx, task.TaskId, task.Metadata)
	if err != nil {
		t.log.WithContext(ctx).Warn("freeTaskResource fail in free resource")
		e := errors.FromError(err)
		return &common.ScheduleError{Code: e.Code, Reason: e.Reason, Message: err.Error()}
	}
	return nil
}

// 返还资源
// 通知上游任务完成：根据任务的notify参数，通知上游作业管理任务完成
// 更新任务internal状态
func (t *CommonTaskBiz) replyTaskAndUpdateInternalStopped(ctx context.Context, task *models.Task) error {
	err := t.scheduler.FreeResource(ctx, task.TaskId, task.Metadata)
	if err != nil {
		t.log.WithContext(ctx).Errorf("replyTaskAndUpdateInternalStopped fail in free resource, taskId:%s, err:%v",
			task.TaskId, err)
		return err
	}

	if task.Notify != "" {
		err := t.sendCompleteNotify(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Errorf("replyTaskAndUpdateInternalStopped fail in sendCompleteNotify,task:%v, error:%v", task, err)
			return err
		}
	}

	task.InternalStatus = models.TaskStateStopped
	err = t.repo.UpdateInternalStatus(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Errorf("replyTaskAndUpdateInternalStopped fail in repo update internal status, taskId:%s, err:%v",
			task.TaskId, err)
		e := errors.FromError(err)
		return &common.DbError{Code: e.Code, Reason: "UpdateInternalStatusFail", Message: err.Error()}
	}

	return nil
}

// 返还资源
// 通知上游任务完成：根据任务的notify参数，通知上游作业管理任务完成
// 但是不更新任务状态
func (t *CommonTaskBiz) replyTaskNotUpdateTask(ctx context.Context, task *models.Task) error {
	var err error
	ctx, span := t.taskTracer.Start(ctx, "CommonTaskBiz/replyTaskNotUpdateTask", task.Metadata)
	defer func() {
		t.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTask(ctx, "replyTaskNotUpdateTask", task)
		}, err)
	}()

	err = t.scheduler.FreeResource(ctx, task.TaskId, task.Metadata)
	if err != nil {
		t.log.WithContext(ctx).Errorf("replyTaskAndUpdateInternalStopped fail in free resource, taskId:%s, err:%v",
			task.TaskId, err)
		return err
	}

	if task.Notify != "" {
		err := t.sendCompleteNotify(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Error("replyTaskAndUpdateInternalStopped fail in sendNotify, err:%v", err)
			return err
		}
	}
	return nil
}

func (t *CommonTaskBiz) convertNotifyUrl(uri string) string {
	// notify格式参考： mns://1426105496228119.mns.cn-shanghai-internal.aliyuncs.com/queues/mpp-qinxin-test
	var (
		AccessKeyId     string
		AccessKeySecret string
	)

	if t.conf != nil && t.conf.GetMns() != nil {
		AccessKeyId = t.conf.GetMns().GetAccessKey()
		AccessKeySecret = t.conf.GetMns().GetSecretKey()
	}

	var urls []string
	for _, item := range strings.Split(uri, ",") {
		newUrl := item
		if AccessKeyId != "" && AccessKeySecret != "" && !strings.Contains(item, "AccessKeySecret") {
			if !strings.Contains(item, "?") {
				newUrl = newUrl + "?"
			} else {
				newUrl = newUrl + "&"
			}
			newUrl = newUrl + fmt.Sprintf("accessKey=%s&accessSecret=%s", AccessKeyId, AccessKeySecret)
		}
		urls = append(urls, newUrl)
	}

	return strings.Join(urls, ",")
}

// 任务完成回调消息
func (t *CommonTaskBiz) sendCompleteNotify(ctx context.Context, task *models.Task) error {
	var err error
	// tc回调
	if task != nil && task.Metadata != nil {
		if _, exists := task.Metadata[string(custom_types.MetadataKeyRequestSource)]; exists {
			if string(custom_types.RequestSourceTc) == task.Metadata[string(custom_types.MetadataKeyRequestSource)] {
				err = t.SendTCCompleteNotify(ctx, task)
				return err
			}
		}
	}
	//通用mns回调
	err = t.SendCommonCompleteNotify(ctx, task)
	return err
}

// 通用返回 通过url向mns发送回调消息
func (t *CommonTaskBiz) SendCommonCompleteNotify(ctx context.Context, task *models.Task) error {
	// Set notify request.
	notifyRequest := models.AsyncJobNotifyRequest{
		JobId:      task.JobId,
		UserId:     task.UserId,
		UserData:   task.UserData,
		Code:       models.NOTIFY_MESSAGE_SUCCESS,
		Data:       task.Result.Data,
		DataUrl:    task.Result.DataUrl,
		Message:    task.Result.Message,
		CreateTime: task.GmtCreate.Format(time.DateTime),
		FinishTime: task.LastModified.Format(time.DateTime),
		Event:      string(models.JOB_COMPLETED_NOTIFY_EVENT),
	}

	// 如果任务返回了reason
	if task.Result != nil && task.Result.Reason != "" {
		//如果任务失败
		if task.Result.Reason != models.NOTIFY_MESSAGE_SUCCESS {
			// TODO 这里可能不对，因为业务层接受的 code 可能不只有 Success 和 Failed；比如云剪辑有 oom 之类 errorCode 需要判断
			notifyRequest.Code = models.NOTIFY_MESSAGE_FAILED
		}
	}

	dataString, err := task.Result.ConvertResultDataToString()
	if err != nil {
		t.log.WithContext(ctx).Warnf("sendCompleteNotify fail in convert data to string,taskId:%s", task.TaskId)
	}
	// 如果返回的data没有超长,直接返回data数据
	// if dataString != "" && len(dataString) < 30*1024
	if dataString != "" {
		notifyRequest.Data = task.Result.Data
	}
	if task.Result.DataUrl != "" {
		notifyRequest.DataUrl = task.Result.DataUrl
	}
	data, err := json.Marshal(notifyRequest)
	if err != nil {
		t.log.WithContext(ctx).Errorf("sendCompleteNotify fail in json.Marshal notifyRequest,taskId:%s, err:%v", task.TaskId, err)
		return err
	}
	completeNotifyUrl := getCompletedNotify(task.Notify)
	if completeNotifyUrl == "" {
		t.log.WithContext(ctx).Info("sendCompleteNotify and no CompletedNotify url ,taskId:%s, notify:%s", task.TaskId, task.Notify)
		return nil
	}
	notifyUrl := t.convertNotifyUrl(completeNotifyUrl)
	notifier, err := notify.GetNotifyProvider(notifyUrl)
	if err != nil {
		t.log.WithContext(ctx).Errorf("sendCompleteNotify fail in notify.GetNotifyProvider,taskId:%s, notify:%s, err:%v", task.TaskId, task.Notify, err)
		return err
	}
	defer notifier.Close()

	var msgId string
	msgId, err = notifier.Send(ctx, "", data)
	if err != nil {
		t.log.WithContext(ctx).Errorf("sendCompleteNotify fail in notifier. Send,taskId:%s, notify:%v, err:%v", task.TaskId, task.Notify, err)
		return err
	}

	t.log.WithContext(ctx).Infof("sendCompleteNotify success, taskId:%s, notify:%s, msgId:%s, notifyRequest:%+v ", task.TaskId, task.Notify, msgId, notifyRequest)

	return nil
}

// 输入以,分隔的多个notifyUrl字符串，输出任务完成通知的notifyUrl
func getCompletedNotify(notifyUrlStrings string) string {
	completeNotifyUrl := ""
	urls := strings.Split(notifyUrlStrings, ",")
	for _, str := range urls {
		u, err := url.Parse(str)
		if err != nil {
			log.Errorf("getCompletedNotify fail in url.Parse,str:%s, err:%v", str, err)
			continue // 如果无法解析为URL，跳过此元素，如果最终没有找到符合条件的URL，返回空字符串
		}
		// 如果Url的Host不为空且RawQuery为空,则返回
		if u.Host != "" && u.RawQuery == "" {
			completeNotifyUrl = u.String()
			break
		}
	}
	return completeNotifyUrl // 没有找到符合条件的URL，返回空字符串
}

// 任务出队回调消息,notify url中带有dequeued=xxx
func (t *CommonTaskBiz) sendDequeueNotify(ctx context.Context, task *models.Task) error {
	// Set notify request.
	notifyRequest := models.AsyncJobNotifyRequest{
		JobId:      task.JobId,
		UserId:     task.UserId,
		UserData:   task.UserData,
		Code:       models.NOTIFY_MESSAGE_SUCCESS,
		CreateTime: task.GmtCreate.Format(time.DateTime),
		FinishTime: task.LastModified.Format(time.DateTime),
		Event:      string(models.JOB_DEQUEUED_NOTIFY_EVENT),
	}
	if task.RequestResource != nil && task.RequestResource.Quota != nil {
		notifyRequest.QuotaSet = task.RequestResource.Quota

	}
	data, err := json.Marshal(notifyRequest)
	if err != nil {
		t.log.WithContext(ctx).Errorf("sendDequeueNotify fail in json.Marshal notifyRequest,taskId:%s, err:%v", task.TaskId, err)
		return err
	}
	dequeuedNotifyUrl := getDequeuedNotify(task.Notify)
	if dequeuedNotifyUrl == "" {
		t.log.WithContext(ctx).Info("sendDequeueNotify and no sendDequeueNotify url ,taskId:%s, notify:%s", task.TaskId, task.Notify)
		return nil
	}
	notifyUrl := t.convertNotifyUrl(dequeuedNotifyUrl)

	notifier, err := notify.GetNotifyProvider(notifyUrl)
	if err != nil {
		t.log.WithContext(ctx).Errorf("sendCompleteNotify fail in notify.GetNotifyProvider,taskId:%s, notify:%s, err:%v", task.TaskId, task.Notify, err)
		return err
	}
	defer notifier.Close()

	var msgId string
	msgId, err = notifier.Send(ctx, "", data)
	if err != nil {
		t.log.WithContext(ctx).Errorf("sendDequeueNotify fail in notifier. Send,taskId:%s, notify:%v, err:%v", task.TaskId, task.Notify, err)
		return err
	}

	t.log.WithContext(ctx).Infof("sendDequeueNotify success, taskId:%s, notify:%s, msgId:%s, notifyRequest:%+v ", task.TaskId, task.Notify, msgId, notifyRequest)

	return nil
}

func getDequeuedNotify(notifyUrlStrings string) string {
	dequeuedNotifyUrl := ""
	urls := strings.Split(notifyUrlStrings, ",")
	for _, str := range urls {
		u, err := url.Parse(str)
		if err != nil {
			log.Errorf("getDequeuedNotify fail in url.Parse,str:%s, err:%v", str, err)
			continue // 如果无法解析为URL，跳过此元素，如果最终没有找到符合条件的URL，返回空字符串
		}
		// 如果Url的Host不为空且RawQuery不为空且RawQuery中包含dequeued=xxx,则返回

		if u.Host != "" && u.RawQuery != "" && u.Query().Get(models.JOB_DEQUEUE_NOTIFY_URL_QUERY) != "" {
			dequeuedNotifyUrl = u.String()
			break
		}
	}
	return dequeuedNotifyUrl // 没有找到符合条件的URL，返回空字符串
}

const (
	//预发测试SK先用固定值
	TrafficAuthenticationSecret = "b9nty6FbDNujWIxxJyieLEF84iZBdBS0RWWn"
	maxNotifierParallel         = 100 // Up to 100 concurrent callbacks for coroutines
)

// 适配tc层的消息回调
// sendNotify send the callback request, if it succeeds, return true, otherwise return
func (t *CommonTaskBiz) SendTCCompleteNotify(ctx context.Context, task *models.Task) error {
	// Set notify request.
	request := models.TCNotifyRequest{
		ID:        task.TaskId,                   // 任务ID
		Code:      models.NOTIFY_MESSAGE_SUCCESS, // 任务状态
		UserData:  task.UserData,                 // 用户数据
		Result:    task.Result.Data,
		ResultURL: task.Result.DataUrl,
		Message:   task.Result.Message,
		Duration:  task.Result.Duration, // 任务耗时
		Extends:   task.Result.Extends,
	}

	// 如果任务返回了reason
	if task.Result != nil && task.Result.Reason != "" {
		//如果任务失败
		if task.Result.Reason != models.NOTIFY_MESSAGE_SUCCESS {
			request.Code = models.NOTIFY_MESSAGE_FAILED
		}
	}
	httpClient := http.Client{
		Transport: &http.Transport{
			Proxy: http.ProxyFromEnvironment,
			DialContext: (&net.Dialer{
				Timeout:   30 * time.Second,
				KeepAlive: 30 * time.Second,
				DualStack: true,
			}).DialContext,
			ForceAttemptHTTP2:     true,
			MaxIdleConns:          maxNotifierParallel,
			MaxIdleConnsPerHost:   maxNotifierParallel,
			IdleConnTimeout:       90 * time.Second,
			TLSHandshakeTimeout:   10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
		},
		Timeout: 10 * time.Second,
	}

	//todo 三次重试 qinxin
	err := util.HTTPPostErrorOnly(&httpClient, task.Notify, TrafficAuthenticationSecret, &request, nil)
	if err != nil {
		t.log.WithContext(ctx).Errorf("TCSendNotify fail in util.HTTPPostErrorOnly,taskId:%s, notify:%s, err:%v", task.TaskId, task.Notify, err)
		return err
	}
	return err

}

func (t *CommonTaskBiz) GetAllSchedulerTags() []string {
	return t.scheduler.GetAllSchedulerTags()
}

// AddSourceStreamTask 直播转码源流转码任务映射记录,启动转码任务时调用
func (t *CommonTaskBiz) AddSourceStreamTask(ctx context.Context, streamParams lc.StreamParams, taskId, streamId string) error {
	sourceStreamTask := domain.SourceStreamTask{
		App:            streamParams.AppName,
		TaskId:         taskId,
		Domain:         streamParams.Domain,
		Stream:         streamParams.StreamName + "_" + streamParams.Token,
		SourceStreamId: streamId,
	}
	err := t.sourceStreamTaskRepo.Insert(ctx, &sourceStreamTask)
	if err != nil {
		t.log.Errorf("add source stream task failed, err:%v", err)
		return err
	}
	return nil
}

func (t *CommonTaskBiz) GetTransCodeAttachments(ctx context.Context, taskId string, attachType string) []*domain.TranscodeAttachment {
	t.log.Infof("get transcode attachments, taskId:%s, attachType:%s", taskId, attachType)
	attachments, err := t.transcodeAttachmentRepo.SelectByTaskType(ctx, taskId, attachType)
	if err != nil {
		t.log.Errorf("get transcode attachments failed, err:%v", err)
		return nil
	}
	return attachments
}

func (t *CommonTaskBiz) UpdateTaskAttachments(ctx context.Context, attachType, taskId string, attachments []map[string]interface{}, names []string) error {
	atts := make([]*domain.TranscodeAttachment, 0)
	for idx, attachment := range attachments {
		atts = append(atts, &domain.TranscodeAttachment{
			TaskID:     taskId,
			AttachType: attachType,
			Name:       names[idx],
			Params:     biz.JsonToString(attachment),
		})
	}
	err := t.transcodeAttachmentRepo.Create(ctx, atts)
	if err != nil {
		t.log.Errorf("create or update task attachments failed, err:%v", err)
	}
	return err
}

func (t *CommonTaskBiz) QueryStat(ctx context.Context, tag string, transcodeDomain string) ([]*domain.TranscodeStatInfo, error) {
	statInfo, err := t.transcodeStatInfoRepo.GetLatest(ctx, tag)
	if err != nil {
		t.log.Errorf("get latest stat failed, err:%v", err)
		return nil, err
	}
	if statInfo != nil && !statInfo.StatTime.IsZero() {
		stats, err := t.transcodeStatInfoRepo.Query(ctx, tag, transcodeDomain, statInfo.StatTime)
		if err != nil {
			t.log.Errorf("query stat failed, err:%v", err)
			return nil, err
		}
		return stats, nil
	}
	return []*domain.TranscodeStatInfo{}, nil
}

func (t *CommonTaskBiz) CallBizHost(ctx context.Context, bizHost string, domain, app string) (any, error) {
	connHTTP, err := client.InitHttpConnection(ctx, bizHost)
	if err != nil {
		t.log.WithContext(ctx).Errorf("CallBizHost init connection to %s failed: %v", bizHost, err)
		return nil, err
	}
	defer func(connHTTP *kHttp.Client) {
		err := connHTTP.Close()
		if err != nil {
		}
	}(connHTTP)
	liveBizClient := pb.NewLiveBizHTTPClient(connHTTP)
	resp, err := liveBizClient.GetStatInfo(ctx, &pb.GetStatInfoRequest{
		Domain: domain,
		App:    app,
	})
	if err != nil {
		t.log.Errorf("CallBizHost GetStatInfo failed, err:%v", err)
		return nil, err
	}
	statisticsResult := make([]lc.LiveStatisticsTaskStat, 0)
	for _, stat := range resp.Data.Tasks {
		statisticsResult = append(statisticsResult, lc.LiveStatisticsTaskStat{
			Domain:     stat.Domain,
			App:        stat.App,
			Token:      stat.Token,
			Num:        stat.CurStreams,
			Percentage: stat.Percentage,
			Quota:      stat.Quota,
			Template:   stat.Template,
		})
	}
	return statisticsResult, nil
}

func (t *CommonTaskBiz) CleanTranscodeAttachment(ctx context.Context, task *models.Task, attachType string) {
	attaches, err := t.transcodeAttachmentRepo.SelectByTaskType(ctx, task.TaskId, attachType)
	if err != nil {
		t.log.Errorf("Clean task [%v] type [%v] TranscodeAttachment failed, err:%v", task.TaskId, attachType, err)
	}
	if attaches != nil && len(attaches) > 0 {
		err = t.transcodeAttachmentRepo.Delete(ctx, attaches)
		if err != nil {
			t.log.Errorf("Clean task [%v] type [%v] TranscodeAttachment failed, err:%v", task.TaskId, attachType, err)
		}
	}
}

func (t *CommonTaskBiz) ReplayTranscodeTask(ctx context.Context, tasks []*models.Task) {
	if tasks == nil || len(tasks) == 0 {
		t.log.Infof("ReplayTranscodeTask task is nil")
		return
	}
	t.log.WithContext(ctx).Infof("ReplayTranscodeTask start, task size %d", len(tasks))
	for _, task := range tasks {
		// 检查任务扩展信息
		extendInfo := make(map[string]interface{})
		var lostRetryCount int
		if task.Metadata != nil && task.Metadata["extend"] != "" {
			err := json.Unmarshal([]byte(task.Metadata["extend"]), &extendInfo)
			if err != nil {
				t.log.WithContext(ctx).Errorf("ReplayTranscodeTask failed to parse extend info, taskId:%s, err:%v", task.TaskId, err)
				continue
			}
			if extendInfo["lostRetryCount"] != nil && extendInfo["lostResouce"] != nil {
				count, _ := extendInfo["lostRetryCount"].(float64)
				lostRetryCount = int(count)
				lostResource, _ := extendInfo["lostResouce"].(bool)
				if lostResource && lostRetryCount > 3 {
					t.log.WithContext(ctx).Infof("checkResources task [%s] retry count:%d has been notified, ignore. metadata:%+v",
						task.TaskId, lostRetryCount, task.Metadata)
					// 释放资源
					task.SetBizStatus(models.TaskStateStopped)
					task.SetInternalStatus(models.TaskStateStopped)
					err = t.repo.UpdateInternalAndBizStatus(ctx, task)
					if err != nil {
						t.log.Errorf("ReplayTranscodeTask failed to update task status, taskId:%s, err:%v", task.TaskId, err)
					}
					err = t.freeTaskResource(ctx, task)
					if err != nil {
						t.log.Errorf("ReplayTranscodeTask failed to free task resource, taskId:%s, err:%v", task.TaskId, err)
					}
					continue
				}
			}
		}

		extendInfo["lostRetryCount"] = lostRetryCount + 1
		extendInfo["lostResouce"] = true
		extendStr, _ := json.Marshal(extendInfo)
		if task.Metadata == nil {
			task.Metadata = custom_types.Metadata{}
		}
		task.Metadata["extend"] = string(extendStr)
		err := t.repo.UpdateMetadata(ctx, task)
		if err != nil {
			t.log.WithContext(ctx).Errorf("ReplayTranscodeTask failed to update task metadata, taskId:%s, err:%v", task.TaskId, err)
		}

		// 请求 biz-transcode 的 play 接口，重新启动强制转码
		err = t.NotifyBizPlay(task, "CallBizForceTrans", "restart_force")
		if err != nil {
			t.log.WithContext(ctx).Errorf("ReplayTranscodeTask failed to notifyBizPlay, taskId:%s, err:%v", task.TaskId, err)
		}
		t.CallGslbForceTrans(task)
	}
}

// NotifyBizPlay 调用biz-transcode的play接口，重新强制启动/停止转码
func (t *CommonTaskBiz) NotifyBizPlay(task *models.Task, logFuncName string, action string) error {

	//获取四元组
	streamMetas, err := livetranscode.ExtractStreamStrMeta(task.TaskId, task.Param.Param)
	if err != nil {
		t.log.Errorf("%s failed to extract stream meta due to err %s, taskId:%s", logFuncName, err, task.TaskId)
		return err
	}

	bizUrl := t.liveTransCodeParamsAdapter.GetCallBizUrl("/play", "")
	body := map[string]interface{}{
		"Domain":    streamMetas[0],
		"App":       streamMetas[1],
		"Stream":    streamMetas[2],
		"Token":     streamMetas[3],
		"Action":    action,
		"Timestamp": time.Now().UnixMilli(),
	}
	if action == "restart_force" {
		body["SwitchWorker"] = "true"
	}

	bodyBytes, err := json.Marshal(body)
	if err != nil {
		t.log.Errorf("%s failed to marshal request body, taskId:%s, err:%v", logFuncName, task.TaskId, err)
		return err
	}

	bizClient := t.httpClientPool.GetClient(true)

	err = t.callBizClient(bizClient, bizUrl, bodyBytes, logFuncName, task, "POST")
	return err
}

// CallGslbForceTrans 调用GSLB强制转码接口
func (t *CommonTaskBiz) CallGslbForceTrans(task *models.Task) {
	const logFuncName = "CallGslbForceTrans"
	const uri = "/gslb/dispatch/play"
	//获取四元组
	streamMetas, err := livetranscode.ExtractStreamStrMeta(task.TaskId, task.Param.Param)
	if err != nil {
		t.log.Errorf("%s failed to extract stream meta due to err %s, taskId:%s", logFuncName, err, task.TaskId)
		return
	}

	requestParams := map[string]string{
		"app":       streamMetas[1],
		"call":      "play",
		"name":      fmt.Sprintf("%s_%s_%s", task.Domain, streamMetas[2], streamMetas[3]),
		"host":      task.Domain,
		"node_name": t.getLocalHostName(),
		"reason":    "transcode",
	}

	var queryParams []string
	for key, value := range requestParams {
		queryParams = append(queryParams, fmt.Sprintf("%s=%s", key, value))
	}
	paramString := strings.Join(queryParams, "&")

	bizUrl := t.liveTransCodeParamsAdapter.GetCallBizUrl(uri, paramString)

	bizClient := t.httpClientPool.GetClient(false)
	_ = t.callBizClient(bizClient, bizUrl, nil, logFuncName, task, "GET")
}

// getLocalHostName 获取本地主机名
func (t *CommonTaskBiz) getLocalHostName() string {
	hostname, err := os.Hostname()
	if err != nil {
		t.log.Errorf("getLocalHostName failed: %v", err)
		return "livetranscode.unknown"
	}
	// gslb要求名称带.xxx 的机房格式，对云上hostname进行修改
	// 原格式：livetranscode2ee61dc2d8828831a76de47ecc5b4695-92ngb
	if hostname != "" && strings.Contains(hostname, "-") {
		hostname = strings.ReplaceAll(hostname, "-", ".")
	}

	return hostname
}

func (t *CommonTaskBiz) TranscodeTaskBackupToSourceStream(ctx context.Context, taskId string) error {
	task, err := t.repo.FindByID(ctx, taskId)
	if err != nil {
		t.log.Errorf("TranscodeTaskBackupToSourceStream failed to find task by id, taskId:%s, err:%v", taskId, err)
		return err
	}
	// 调用直接中心源流兜底
	err = t.NotifyLiveTocBackup(task)
	if err != nil {
		t.log.Errorf("TranscodeTaskBackupToSourceStream failed to notifyLiveTocBackup, taskId:%s, err:%v", taskId, err)
	}
	//调用业务层停止任务
	err = t.NotifyBizPlay(task, "notifyLiveBizTranscode", "play_done")
	if err != nil {
		t.log.Errorf("TranscodeTaskBackupToSourceStream failed to notifyBizStopTask, taskId:%s, err:%v", taskId, err)
	}
	return nil
}

func (t *CommonTaskBiz) NotifyLiveTocBackup(task *models.Task) error {
	const logFuncName = "TranscodeSourceBackup"

	//获取四元组
	streamMetas, err := livetranscode.ExtractStreamStrMeta(task.TaskId, task.Param.Param)
	if err != nil {
		t.log.Errorf("%s failed to extract stream meta due to err %s, taskId:%s", logFuncName, err, task.TaskId)
		return err
	}
	path := fmt.Sprintf("/livetoc/internal/overloadtranscode?domain=%s&app=%s&stream=%s_%s", url.PathEscape(streamMetas[0]), url.PathEscape(streamMetas[1]), url.PathEscape(streamMetas[2]), url.PathEscape(streamMetas[3]))
	bizUrl := t.liveTransCodeParamsAdapter.GetCallLiveTocUrl(path)
	bizClient := t.httpClientPool.GetClient(false)
	return t.callBizClient(bizClient, bizUrl, nil, logFuncName, task, "GET")
}

func (t *CommonTaskBiz) callBizClient(bizClient *http.Client, bizUrl string, body []byte, logFuncName string, task *models.Task, method string) error {
	begin := time.Now()
	var req *http.Request
	var err error
	if method == "POST" {
		req, err = http.NewRequest(method, bizUrl, bytes.NewBuffer(body))
		if err != nil {
			t.log.Errorf("%s failed to create request, taskId:%s, err:%v", logFuncName, task.TaskId, err)
			return err
		}
		req.Header.Set("Content-Type", "application/json")
	} else if method == "GET" {
		req, err = http.NewRequest(method, bizUrl, nil)
		if err != nil {
			t.log.Errorf("%s failed to create request, taskId:%s, err:%v", logFuncName, task.TaskId, err)
			return err
		}
	}
	resp, err := bizClient.Do(req)
	if err != nil {
		t.log.Errorf("%s HTTP request failed, taskId:%s, url:%s, err:%v", logFuncName, task.TaskId, bizUrl, err)
		return err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			t.log.Errorf("%s failed to close response body, taskId:%s, err:%v", logFuncName, task.TaskId, err)
		}
	}(resp.Body)

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		t.log.Errorf("%s failed to read response body, taskId:%s, err:%v", logFuncName, task.TaskId, err)
		return err
	}

	// 检查HTTP状态码
	if resp.StatusCode > 499 || resp.StatusCode <= 0 {
		t.log.Errorf("%s HTTP request failed, taskId:%s, url:%s, status:%d, response:%s",
			logFuncName, task.TaskId, bizUrl, resp.StatusCode, string(respBody))
		return err
	}
	t.log.Infof("%s success, taskId:%s, url:%s, status:%d, response:%s, latency:%dms",
		logFuncName, task.TaskId, bizUrl, resp.StatusCode, string(respBody), time.Since(begin).Milliseconds())
	return nil
}

func (t *CommonTaskBiz) SendConfigToWorker(ctx context.Context, configData interface{}) error {
	// 获取所有在线worker
	allOnlineWorkers, err := t.scheduler.ListAllWorker(ctx, t.scheduler.GetAllSchedulerTags())
	if err != nil {
		t.log.Errorf("SendConfigToWorker, getAllOnlineWorkers failed, err:%v", err)
		return err
	}

	if len(allOnlineWorkers) == 0 {
		configJSON, _ := json.Marshal(configData)
		t.log.Warnf("SendConfigToWorker, no online worker, globalConfig:%s", string(configJSON))
		return nil
	}

	configJSON, _ := json.Marshal(configData)
	t.log.Infof("SendConfigToWorker, globalConfig:%s, workerSize:%d", string(configJSON), len(allOnlineWorkers))

	// 使用goroutine并发发送配置到所有worker
	var wg sync.WaitGroup
	for _, worker := range allOnlineWorkers {
		wg.Add(1)
		go func(w *models.Worker) {
			defer wg.Done()
			err := t.sendConfigToWorker(configJSON, w)
			if err != nil {
				t.log.Errorf("Error sending configuration to worker:%s, tag:%s config:%s, err:%v",
					w.Ip, w.Tag, string(configJSON), err)
			}
		}(worker)
	}

	wg.Wait()
	return nil
}

// sendConfigToSingleWorker 向单个worker发送配置
func (t *CommonTaskBiz) sendConfigToWorker(configJSON []byte, worker *models.Worker) error {
	// 构建worker的配置接口URL
	workerURL := fmt.Sprintf("http://%s:%d/updateConfig", worker.Ip, worker.Port)

	// 创建HTTP客户端
	connClient := t.httpClientPool.GetClient(true)

	// 创建POST请求
	req, err := http.NewRequest("POST", workerURL, bytes.NewBuffer(configJSON))
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := connClient.Do(req)
	if err != nil {
		return fmt.Errorf("HTTP request failed: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %v", err)
	}
	respJSON := js.Get(respBody)
	code := respJSON.Get("result").Get("code").ToString()
	if !strings.EqualFold(code, "Success") && code != "200" {
		return fmt.Errorf("failed to send config to worker %s:%d, response: %s", worker.Ip, worker.Port, string(respBody))
	}
	t.log.Infof("Successfully sent config to worker %s:%d, response: %s", worker.Ip, worker.Port, string(respBody))
	return nil
}

func (t *CommonTaskBiz) ListBySourceStreamId(ctx context.Context, streamId string) ([]*domain.SourceStreamTask, error) {
	if streamId == "" {
		return nil, fmt.Errorf("input streamId is nil")
	}
	return t.sourceStreamTaskRepo.ListBySourceStreamId(ctx, streamId)
}
