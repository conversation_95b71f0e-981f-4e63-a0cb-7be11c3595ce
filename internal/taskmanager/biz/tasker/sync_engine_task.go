package tasker

import (
	"context"
	v1 "proto.mpp/api/common/v1"
	"strings"
	"sync"
	"time"

	"github.com/cinience/animus/safe"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/leader"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"
	"mpp/internal/taskmanager/tracing/tasker_span"
	"mpp/pkg/tracer/tasker_tracing"
	"mpp/pkg/utils"

	"github.com/go-kratos/kratos/v2/log"
)

type EngineTaskSync struct {
	repo      repository.TaskRepo
	log       *log.Helper
	taskBiz   *CommonTaskBiz
	scheduler common.ScheduleAdapter
	engine    common.EngineAdapter
	leaderBiz *leader.LeaderBiz

	taskTracer *tasker_tracing.Tracer
}

const (
	//任务日志事件 用于监控大盘
	//报告running worker
	sync_event_worker = "sync.engine.worker"
	//报告running task
	sync_event_worker_longExcution = "sync.engine.longExcution"
)

func NewEngineTaskSync(ctx context.Context, repo repository.TaskRepo, taskBiz *CommonTaskBiz, leaderBiz *leader.LeaderBiz,
	scheduler common.ScheduleAdapter, engine common.EngineAdapter,
	logger log.Logger) *EngineTaskSync {
	ts := &EngineTaskSync{repo: repo, taskBiz: taskBiz, leaderBiz: leaderBiz,
		scheduler: scheduler, engine: engine, log: log.NewHelper(logger)}
	ts.repo = repo.CloneWithSpecifiedTable(models.StreamTaskTableName)
	ts.taskTracer = tasker_tracing.NewTracer(tasker_tracing.WithConsumerSpanKind())
	safe.Go(func() {
		ts.BackgroundMethod(ctx, v1.TaskType_Stream, time.Second*60)
	})

	return ts
}

func NewEngineTaskSyncWithSpecifiedRepo(ctx context.Context, repo repository.TaskRepo, taskBiz *CommonTaskBiz, leaderBiz *leader.LeaderBiz,
	scheduler common.ScheduleAdapter, engine common.EngineAdapter, logger log.Logger) *EngineTaskSync {
	ts := &EngineTaskSync{repo: repo, taskBiz: taskBiz, leaderBiz: leaderBiz,
		scheduler: scheduler, engine: engine, log: log.NewHelper(logger)}
	ts.taskTracer = tasker_tracing.NewTracer(tasker_tracing.WithConsumerSpanKind())

	return ts
}

func (et *EngineTaskSync) BackgroundMethod(ctx context.Context, taskType v1.TaskType, sleepTime time.Duration) {
	count := 0
	for {
		count++
		if count%10 == 0 {
			log.Infof("start SyncEngineTaskStatus, count:%d, isLeader:%v (if not leader, will do nothing)", count, et.leaderBiz.IsLeader())
		}

		if !et.leaderBiz.IsLeader() {
			time.Sleep(time.Second * 10)
			continue
		}
		//TODO 目前全查，后续修改为选择Tag?
		tags := et.scheduler.GetAllSchedulerTags()
		ctx := utils.ResetRequestId(ctx)
		err := et.SyncEngineTaskStatus(ctx, taskType, tags)
		if err != nil {
			log.Errorf("SyncEngineTaskStatus fail, err:%v", err)
		}

		if ctx.Err() != nil {
			log.Errorf("BackgroundMethod fail,ctx.err:%v", ctx.Err())
			return
		}

		// 休眠一段时间，以避免过于频繁的执行
		time.Sleep(sleepTime)
	}
}

// SyncEngineTaskStatus sync worker tasker and local tasker status
// (1) call scheduler list worker to get online workers
// (2) for each worker, call list tasks to get running tasks;
// (3) call repo query tasker by workerId
// (4) compare status with local status
// for missing tasks, update to internal stopped; for stolen tasks, try to stop it
func (et *EngineTaskSync) SyncEngineTaskStatus(ctx context.Context, taskType v1.TaskType, tags []string) error {

	// todo 这里worker太多咋弄？？？？得改为next token遍历吧
	//目前的设计中 worker总量不会太多，采用循环处理
	workers, err := et.scheduler.ListAllWorker(ctx, tags)
	if err != nil {
		log.Errorf("SyncEngineTaskStatus fail in ListWorker, err:%v", err)
		return err
	}
	//et.LogSyncInfo(ctx, sync_event_worker, &models.Worker{}, len(workers),0,"")
	et.log.WithContext(ctx).Infof("SyncEngineTaskStatus list worker, len:%v", len(workers))
	if len(workers) == 0 {
		return nil
	}

	// 并发处理每个worker
	var wg sync.WaitGroup
	size := len(workers)
	if size > 50 {
		size = 50
	}
	sem := make(chan struct{}, size)

	// 这里改成routing池来解决。。。。。。
	for _, worker := range workers {
		wg.Add(1)
		sem <- struct{}{} // take one semaphore

		go func(worker *models.Worker) {
			defer func() {
				wg.Done()
				<-sem
			}()

			et.log.WithContext(ctx).Debugf("SyncEngineTaskStatus start, worker:%v", worker)

			// 获取worker实际运行的任务- by tasktype
			task := &models.Task{
				TaskType: models.TaskType(taskType),
			}
			actualRunningTasks, err := et.getWorkerActualRunningTasksByTask(ctx, worker, task)
			if err != nil {
				et.log.WithContext(ctx).Errorf("SyncEngineTaskStatus fail in getWorkerActualRunningTasksByTask, worker:%v, err:%v",
					worker, err)
				return
			}
			//todo 测试用log, 后续删除
			et.log.WithContext(ctx).Debugf("SyncEngineTaskStatus getWorkerActualRunningTasksByTask success, worker:%v, num:%v,actualRunningTasks:%+v",
				worker, len(actualRunningTasks), actualRunningTasks)

			workerRunningTasks, err := et.repo.ListRunningTaskByWorkerId(ctx, worker.Id)
			if err != nil {
				et.log.WithContext(ctx).Errorf("SyncEngineTaskStatus ListRunningTaskByWorkerId fail, worker:%v, err:%v",
					worker, err)
				return
			}

			workerRunningTasks = FilterTasksByTypes(workerRunningTasks, models.TaskType(taskType), models.UnknownTaskType)

			// worker不存在了，需要迁移任务？ 现在直接返回失败了，没有进行后续处理了
			stolen, missing, err := et.compareTasksWithWorker(ctx, worker, actualRunningTasks, workerRunningTasks)
			if err != nil {
				et.log.WithContext(ctx).Error("SyncEngineTaskStatus fail in compareTasksWithWorker, worker:%v, err:%v",
					worker, err)
				return
			}
			if len(stolen) > 0 || len(missing) > 0 {
				et.log.WithContext(ctx).Infof("SyncEngineTaskStatus len worker:%v, stolenTasks:%v, stolenTasks:%v, len missedTasks:%v, missedTasks:%v",
					worker, len(stolen), stolen, len(missing), missing)
			}
			//worker任务信息log，用于监控大盘
			et.LogEngineSyncInfo(ctx, sync_event_worker, worker, len(workerRunningTasks), len(actualRunningTasks), len(stolen), len(missing), "")
			et.processMissingTasks(ctx, worker, missing)
			et.processingStolenTasks(ctx, worker, stolen)

		}(worker)
	}
	et.log.WithContext(ctx).Debugf("SyncEngineTaskStatus start, worker count：%d", len(workers))
	wg.Wait()
	close(sem)
	et.log.WithContext(ctx).Debugf("SyncEngineTaskStatus end, worker count：%d", len(workers))

	return nil
}

func (et *EngineTaskSync) getWorkerActualRunningTasks(ctx context.Context, worker *models.Worker) ([]models.SimpleTask, error) {
	tasks, err := et.engine.List(ctx, worker)
	if err != nil {
		log.Errorf("getWorkerActualRunningTasks fail, worker:%v, err:%v", worker, err)
		return nil, err
	}
	log.Debugf("getWorkerActualRunningTasks success, worker:%v, num:%v", worker, len(tasks))
	return tasks, nil
}

// 按任务维度获取worker实际运行的任务，目前仅支持按tasktype查询
func (et *EngineTaskSync) getWorkerActualRunningTasksByTask(ctx context.Context, worker *models.Worker, task *models.Task) ([]models.SimpleTask, error) {
	tasks, err := et.engine.ListByTask(ctx, worker, task)
	if err != nil {
		log.Errorf("getWorkerActualRunningTasks fail, worker:%v, taskType:%+v, err:%v", worker, task.TaskType, err)
		return nil, err
	}
	log.Debugf("getWorkerActualRunningTasks success, worker:%v, num:%v", worker, len(tasks))
	return tasks, nil
}

func (et *EngineTaskSync) compareTasksWithWorker(ctx context.Context, worker *models.Worker, workerActualRunningTasks []models.SimpleTask,
	workerRunningTasks []*models.Task) (stolenTasks, missedTasks []string, err error) {
	workerRunningTaskMap := convertToMap(workerRunningTasks)

	excutionTooLongTasks := make([]string, 0)

	for _, task := range workerActualRunningTasks {
		if workerRunningTaskMap[task.TaskId] == nil {
			stolenTasks = append(stolenTasks, task.TaskId)
		}
	}

	for _, task := range workerRunningTaskMap {
		//检查任务执行时间是否超长
		if task.IsExecutionTooLong() {
			ctx = utils.SetTaskId(ctx, task.TaskId)
			ctx = utils.SetTaskProduct(ctx, task.Product)
			et.log.WithContext(ctx).Warnf("SyncEngineTaskStatus task execution too long, worker:%v, taskId:%v", worker, task.TaskId)
			excutionTooLongTasks = append(excutionTooLongTasks, task.TaskId)
		}
		found := false
		for i := range workerActualRunningTasks {
			if workerActualRunningTasks[i].TaskId == task.TaskId {
				found = true
				break
			}
		}
		if !found && !task.IsRecentlyUpdated() {
			missedTasks = append(missedTasks, task.TaskId)
		} else if !found {
			log.Debugf("compareTasksWithWorker found, worker:%v, taskId:%v, task last modified:%v but tasker is update recently",
				worker, task.TaskId, task.LastModified)
		}
	}
	log.Infof("compareTasksWithWorker success, worker:%v, len stolenTasks:%v,len missedTasks:%v", worker, len(stolenTasks), len(missedTasks))
	//worker任务信息log，输出执行时间超长任务,用于监控大盘
	excutionTooLongTasksString := strings.Join(excutionTooLongTasks, ",")
	et.LogEngineSyncInfo(ctx, sync_event_worker_longExcution, worker, len(excutionTooLongTasks), 0, 0, 0, excutionTooLongTasksString)
	return stolenTasks, missedTasks, nil
}

// 处理未正确启动的任务
func (et *EngineTaskSync) processMissingTasks(ctx context.Context, worker *models.Worker, missedTasks []string) {
	if len(missedTasks) == 0 {
		return
	}

	log.Infof("processMissingTasks start, worker:%v, len missedTasks:%v", worker, len(missedTasks))
	tasks, err := et.repo.FindByIDList(ctx, missedTasks)
	if err != nil {
		log.Errorf("processMissingTasks FindByIDList fail, err:%v", err)
		return
	}
	if len(tasks) == 0 {
		log.Warnf("processMissingTasks unexpected result, tasker not found num:%v", len(tasks))
		return
	}

	for _, task := range tasks {
		// 任务不在正确的worker上运行
		if !task.IsTaskOnTargetWorker(worker) {
			log.Warnf("tasker is not on target worker, taskId:%v, tasker worker:%v, targetWorker:%v",
				task.TaskId, task.Worker, worker)
		} else if task.IsRecentlyUpdated() {
			// 任务近期更新过则忽略
			log.Infof("tasker is update recently, taskId:%v, lastModified:%v, cur:%v", task.TaskId, task.LastModified, time.Now())
			continue
		} else if task.IsDagJob() {
			// 任务为dag主任务则忽略
			log.Infof("compareTasksWithWorker tasker is dag job, taskId:%v", task.TaskId)
		} else if task.IsInternalRunning() {
			// 如果任务内部状态为running，则更新任务内部状态和result，等待任务状态同步sync_task_status进行处理
			log.Warnf("tasker is internal running and not running on engine, update stopped and migrate "+
				"taskId:%v, lastModified:%v, cur:%v", task.TaskId, task.LastModified, time.Now())
			task.InternalStatus = models.TaskStateStopped
			task.Result = NewEngineLostResult(worker)
			err := et.repo.UpdateAbnormalStopped(ctx, task)
			if err != nil {
				log.Errorf("updateAbnormalStopped fail, taskId:%v, err:%v", task.TaskId, err)
			}
		}
	}

}

// 处理偷跑的任务
func (et *EngineTaskSync) processingStolenTasks(ctx context.Context, worker *models.Worker, stolenTasks []string) {
	if len(stolenTasks) == 0 {
		return
	}

	tasks, err := et.repo.FindByIDList(ctx, stolenTasks)
	if err != nil {
		log.Errorf("processingStolenTasks FindByIDList fail, err:%v", err)
		return
	}
	if len(tasks) == 0 {
		log.Warnf("processingStolenTasks unexpected result, tasker not found num:%v, stolenTasks:%v", len(tasks), stolenTasks)
		return
	}

	for _, task := range tasks {
		if task.IsRecentlyUpdated() {
			// 任务近期更新过则不停止该任务
			log.Infof("processingStolenTasks tasker is update recently, taskId:%v, lastModified:%v, cur:%v", task.TaskId, task.LastModified, time.Now())
			continue
		} else if task.IsDagJob() {
			// 任务为dag主任务则忽略，不停止该任务
			log.Infof("processingStolenTasks tasker is dag job, taskId:%v", task.TaskId)
			continue
		} else if task.IsBizStatusRunning() && task.IsTaskOnTargetWorker(worker) {
			// 任务在当前worker上运行则不停止该任务
			log.Debugf("processingStolenTasks tasker is biz status running, taskId:%v", task.TaskId)
			continue
		}
		//调用engine停止该任务
		//1.任务存在偷跑
		//2.任务不在正确的worker上运行
		log.Warnf("processingStolenTasks tasker is stolen, try stop engine tasker, taskId:%v, targetWorker:%v", task.TaskId, worker)
		err = et.processingStolenTask(ctx, task, worker)
		if err != nil {
			log.Errorf("processingStolenTasks stop engine tasker fail, taskId:%v, err:%v", task.TaskId, err)
		}
	}

}

func (et *EngineTaskSync) processingStolenTask(ctx context.Context, task *models.Task, worker *models.Worker) error {
	var err error
	ctx, span := et.taskTracer.Start(ctx, "/EngineTaskSync/processingStolenTask", task.Metadata)
	defer func() {
		tasker_span.SpanRecordWithTask(ctx, "", task)
		et.taskTracer.End(ctx, span, nil, err)
	}()

	err = et.engine.Stop(ctx, task, worker)

	return err
}

func (et *EngineTaskSync) LogEngineSyncInfo(ctx context.Context, event string, worker *models.Worker, taskNum int, actualTaskNum int, stolenNum int, missingNum int, msg string) {
	et.log.WithContext(ctx).Log(log.LevelInfo,
		"workerId", worker.Id,
		"workerIp", worker.Ip,
		"engineModel", worker.EngineModel,
		"tag", worker.Tag,
		"product", worker.Product,
		"taskNum", taskNum,
		"actualTaskNum", actualTaskNum,
		"stolenNum", stolenNum,
		"missingNum", missingNum,
		"msg", msg,
		"event", event)
}
