package tasker

import (
	"context"
	"mpp/internal/taskmanager/mesh"
	"mpp/pkg/utils"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/sentinel"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"
)

type RecordHlsTaskBiz struct {
	*CommonTaskBiz
	log *log.Helper
}

func NewRecordHlsTaskBiz(conf *conf.Bootstrap, repo repository.TaskRepo, logger log.Logger, engine common.EngineAdapter, scheduler common.ScheduleAdapter, workflow common.WorkflowAdapter, mesh *mesh.MeshService, sentinelBiz *sentinel.SentinelBiz) (*RecordHlsTaskBiz, error) {
	var err error
	t := &RecordHlsTaskBiz{
		log: log.NewHelper(logger),
	}
	t.CommonTaskBiz, err = NewTaskBiz(conf, repo, nil, scheduler, engine, workflow, mesh, sentinelBiz, nil, nil, nil, logger)
	if err != nil {
		return nil, err
	}
	return t, nil
}

func (t *RecordHlsTaskBiz) StartTask(ctx context.Context, task *models.Task) error {
	//插入时将biz_status状态设置为1
	task.SetBizStatus(models.TaskStateActive)
	//获取任务迁移配置
	task.MigrateConfig = models.GetMigrateConfigByProduct(task.Product)
	//正常状态插入数据，正常完成调度逻辑
	//当existedTask存在且biz状态为0时，更新任务biz状态为1和各类参数，正常完成调度逻辑
	//当existedTask的biz为1时，进入existedTask处理逻辑
	existedTask, err := t.saveAndUpdateOnDuplicate(ctx, task)

	if err != nil {
		t.log.WithContext(ctx).Errorf("StartTask fail in saveAndUpdateOnDuplicate, taskId:%v, err:%v", task.TaskId, err)
		return err
	}

	//当existedTask的biz为1
	if existedTask != nil && existedTask.IsInternalRunning() {
		t.log.WithContext(ctx).Warnf("StartTask task already biz running, taskId:%s", task.TaskId)
		return nil
	}

	// 如果任务是bizRunning，但非internalRunning，还重新start的话，申请资源时增加亲和性限制，要求更换worker
	if existedTask != nil && existedTask.Worker != nil {
		task.CreateAffinity()
		task.RequestResource.Affinity.AddMigrateAffinity(existedTask.Worker.Id, false)
		t.log.WithContext(ctx).Warnf("ReStartTask task set affinity, taskId:%s, Affinity:%v", task.TaskId, task.RequestResource.Affinity)
	}

	if t.cacheDb != nil {
		data, encodeErr := task.EncodeLiteToBytes()
		if encodeErr == nil {
			rst := t.cacheDb.Set(ctx, task.EncodeKVKey(), data, time.Minute*15)
			if rst != nil && rst.Err() != nil {
				t.log.Errorf("StartTask fail in cacheDb, taskId:%s, err:%v", task.TaskId, rst.Err())
			}
		}
	}

	//为提高qps，调整task表更新顺序
	//资源申请
	//if资源申请失败，defer操作中更新任务biz状态为stopped，返回失败
	//if资源申请成功，继续任务下发
	err = t.allocResourceNotUpdateTask(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Errorf("StartTask fail in allocResource, taskId:%s, err:%v", task.TaskId, err)
		//更新task状态
		t.log.WithContext(ctx).Warnf("allocResourceNotUpdateTask startTask fail in and update tasker biz_status, taskId:%s, err:%v", task.TaskId, err)
		task.SetBizStatus(models.TaskStateStopped)
		//TODO 这里如果err是上下文超时context deadline exceeded，会导致update操作直接返回失败，task状态不更新
		//目前RMS业务导致超时，业务层会进行start重试
		updateErr := t.repo.UpdateBizStatus(ctx, task)
		if updateErr != nil {
			t.log.WithContext(ctx).Errorf("allocResourceNotUpdateTask StopTask fail in repo update biz stopped, taskId:%s, error:%v", task.TaskId, updateErr)
		}
		return err
	}
	//任务下发
	//if任务下发失败，错误处理中更新任务状态为0/0，进行资源返还
	//if任务下发成功，更新任务worker信息，更新interna_status,此时任务状态为 1/1，返回成功
	err = t.dispatchTask(ctx, task)
	if err != nil {
		// 0415测试 这里调用引擎如果超时，很容易导致ctx到时间，容易引发后续步骤操作不了的情况
		newCtx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		newCtx = utils.SetRequestId(newCtx)
		defer cancel()

		t.log.WithContext(newCtx).Errorf("RecordHls StartTask fail in dispatchTask, taskId:%s, err:%v", task.TaskId, err)
		//if任务下发失败，更新任务状态为1/0
		task.SetBizStatus(models.TaskStateStopped)
		updateErr := t.repo.UpdateBizStatus(newCtx, task)
		if updateErr != nil {
			t.log.WithContext(newCtx).Errorf("dispatchTaskNotUpdateTask startTask fail in update schedule resource, taskId:%s, err:%v", task.TaskId, updateErr)
		}
		// 测试中出现这里归还资源超时的情况，需要更新ctx时间
		freeErr := t.freeTaskResource(newCtx, task)
		if freeErr != nil {
			t.log.WithContext(newCtx).Errorf("dispatchTaskNotUpdateTask startTask fail in free tasker resource, taskId:%s, err:%v", task.TaskId, freeErr)
		}
		return err
	}
	//更新任务执行状态和work信息
	//更新失败也返回成功，后续依赖同步逻辑进行重试
	task.InternalStatus = models.TaskStateActive
	err = t.repo.UpdateScheduleWorkerAndInternalStatus(ctx, task)
	if err != nil {
		t.log.WithContext(ctx).Errorf("StartTask fail in UpdateScheduleWorkerAndInternalStatus, taskId:%s, err:%v", task.TaskId, err)
		return nil
	}
	return nil
}
