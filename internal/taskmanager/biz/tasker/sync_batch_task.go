package tasker

import (
	"context"
	"github.com/cinience/animus/safe"
	"github.com/go-kratos/kratos/v2/log"
	goredis "github.com/redis/go-redis/v9"
	gocron "github.com/robfig/cron/v3"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/leader"
	"mpp/internal/taskmanager/biz/sentinel"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/mesh"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"
	"mpp/pkg/redis"
	"mpp/pkg/tracer/tasker_tracing"
	"mpp/pkg/utils"
	v1 "proto.mpp/api/common/v1"
	"runtime/debug"
	"sync"
	"time"
)

/**
 * @Author: qinxin
 * @Date: 2024/8/30 09:38
 * @Desc: 异步任务同步链路
 * @Version 1.0
 */

type BatchTaskSync struct {
	repo          repository.TaskRepo
	log           *log.Helper
	taskBiz       *CommonTaskBiz
	leaderBiz     *leader.LeaderBiz
	taskDbChecker *TaskDBChecker
	engine        common.EngineAdapter
	cron          *gocron.Cron
	cacheDb       goredis.UniversalClient
	taskTracer    *tasker_tracing.Tracer
	scheduler     common.ScheduleAdapter
	sentinelBiz   *sentinel.SentinelBiz

	batchBiz              *BatchTaskBiz
	engineTaskSync        *EngineTaskSync
	schedulerResourceSync *SchedulerResourceSync
}

func NewBatchTaskSync(ctx context.Context, conf *conf.Bootstrap, repo repository.TaskRepo, engine common.EngineAdapter, taskBiz *CommonTaskBiz, batchBiz *BatchTaskBiz,
	leaderBiz *leader.LeaderBiz, scheduler common.ScheduleAdapter, sentinelBiz *sentinel.SentinelBiz, workflow common.WorkflowAdapter, mesh *mesh.MeshService, logger log.Logger) (*BatchTaskSync, error) {
	var err error
	t := &BatchTaskSync{repo: repo, taskBiz: taskBiz, leaderBiz: leaderBiz, engine: engine, batchBiz: batchBiz,
		log: log.NewHelper(logger)}
	t.repo = repo.CloneWithSpecifiedTable(models.BatchTaskTableName)
	t.taskBiz, err = NewTaskBizWithSpecifiedRepo(conf, t.repo, nil, scheduler, engine, workflow, mesh, sentinelBiz, nil, nil, nil, logger)
	t.engineTaskSync = NewEngineTaskSyncWithSpecifiedRepo(ctx, t.repo, t.taskBiz, leaderBiz, scheduler, engine, logger)
	t.schedulerResourceSync = NewSchedulerResourceSyncWithSpecifiedRepo(ctx, t.repo, t.taskBiz, leaderBiz, scheduler, logger)

	//taskBiz.SetTracer(tasker_tracing.NewTracer(tasker_tracing.WithConsumerSpanKind()))
	t.taskTracer = tasker_tracing.NewTracer(tasker_tracing.WithTracerName("mppBatchTaskChecker"))
	if conf.GetRedisUri() != "" {
		t.cacheDb, err = redis.GetRedisUniversalClientWithURL(conf.Data.Redis.GetEndpoint())
		if err != nil {
			return nil, err
		}
	}
	t.cron = gocron.New()

	t.taskDbChecker, err = NewTaskDBChecker(t.repo, leaderBiz, false)
	if err != nil {
		return nil, err
	}

	//处理丢失的任务
	safe.Go(func() {
		// todo 外部状态仍在运行中，但内部发现其已经停止
		t.startSyncBatchMissingRunningTasks(ctx, v1.TaskType_NonQueueAsync)
	})

	//处理偷跑任务
	safe.Go(func() {
		// 处理偷跑的任务，即外部已经停止，但内部发现任务仍在运行中
		t.startSyncBatchUnexpectedRunningTasks(ctx, v1.TaskType_NonQueueAsync)
	})

	//处理等待依赖的dag任务
	safe.Go(func() {
		t.startSyncBatchWaitDagDependencyTasks(ctx, v1.TaskType_NonQueueAsync)
	})

	//处理资源同步
	//safe.Go(func() {
	//	t.startSyncBatchResource(ctx, v1.TaskType_NonQueueAsync)
	//})
	safe.Go(func() {
		t.schedulerResourceSync.BackgroundMethod(ctx, v1.TaskType_NonQueueAsync, 2*time.Minute)
	})

	//处理引擎同步
	safe.Go(func() {
		t.engineTaskSync.BackgroundMethod(ctx, v1.TaskType_NonQueueAsync, time.Second*60)
	})
	return t, nil
}

func (ts *BatchTaskSync) startSyncBatchMissingRunningTasks(ctx context.Context, taskType v1.TaskType) {
	count := 0
	for {
		count++
		if count%10 == 0 {
			log.Infof("enter startSyncBatchMissingRunningTasks, count:%d, isLeader:%v (if not leader, will do nothing)", count, ts.leaderBiz.IsLeader())
		}

		if !ts.leaderBiz.IsLeader() {
			time.Sleep(time.Second * 1)
			continue
		}
		ctx := utils.ResetRequestId(ctx)

		var wg sync.WaitGroup
		wg.Add(1)

		// 使用 Goroutine 执行 SyncBatchMissingRunningTasks
		go func() {
			defer func() {
				if r := recover(); r != nil {
					log.Errorf("SyncBatchMissingRunningTasks panicked: %v", r)
				}
				wg.Done()
			}()
			//执行同步操作 捕获 panic
			ts.SyncBatchMissingRunningTasks(ctx, count, taskType)
		}()

		// 等待 Goroutine 执行完毕
		wg.Wait()

		if ctx.Err() != nil {
			log.Error("startSyncBatchMissingRunningTasks fail", ctx.Err())
			return
		}

		// 休眠一段时间，以避免过于频繁的执行
		time.Sleep(time.Second * 10)
	}
}

func (ts *BatchTaskSync) startSyncBatchUnexpectedRunningTasks(ctx context.Context, taskType v1.TaskType) {
	count := 0
	for {
		count++
		if count%10 == 0 {
			log.Infof("enter startSyncBatchUnexpectedRunningTasks, count:%d, isLeader:%v (if not leader, will do nothing)", count, ts.leaderBiz.IsLeader())
		}

		if !ts.leaderBiz.IsLeader() {
			time.Sleep(time.Second * 10)
			continue
		}

		ctx := utils.ResetRequestId(ctx)

		var wg sync.WaitGroup
		wg.Add(1)

		// 使用 Goroutine 执行 SyncBatchMissingRunningTasks
		go func() {
			defer func() {
				if r := recover(); r != nil {
					log.Errorf("SyncBatchUnexpectedRunningTasks panicked: %v", r)
				}
				wg.Done()
			}()
			//执行同步操作 捕获 panic
			ts.SyncBatchUnexpectedRunningTasks(ctx, count, taskType)
		}()

		// 等待 Goroutine 执行完毕
		wg.Wait()

		if ctx.Err() != nil {
			log.Error("startSyncBatchUnexpectedRunningTasks fail", ctx.Err())
			return
		}

		// 休眠一段时间，以避免过于频繁的执行
		time.Sleep(time.Second * 30)
	}
}

func (ts *BatchTaskSync) startSyncBatchWaitDagDependencyTasks(ctx context.Context, taskType v1.TaskType) {
	count := 0
	for {
		count++
		if count%10 == 0 {
			log.Infof("enter startSyncBatchWaitDagDependencyTasks, count:%d, isLeader:%v (if not leader, will do nothing)", count, ts.leaderBiz.IsLeader())
		}
		if !ts.leaderBiz.IsLeader() {
			time.Sleep(time.Second * 10)
			continue
		}
		ctx := utils.ResetRequestId(ctx)
		ts.SyncBatchWaitDagDependencyTasks(ctx, count, taskType)
		if ctx.Err() != nil {
			log.Error("startSyncBatchWaitDagDependencyTasks fail", ctx.Err())
			return
		}
		// 休眠一段时间，以避免过于频繁的执行
		time.Sleep(time.Second * 30)
	}
}

// 同步等待依赖的dag任务
// todo 是否需要??
func (ts *BatchTaskSync) SyncBatchWaitDagDependencyTasks(ctx context.Context, count int, taskType v1.TaskType) {
	log.Infof("enter SyncBatchWaitDagDependencyTasks, count:%d", count)

	tasks, err := ts.repo.ListTasks(ctx, &models.Task{
		BizStatus:      models.TaskStateActive,
		InternalStatus: models.TaskStatePending,
		//InternalStatus: models.TaskStateWaitDependency,
	})
	if err != nil {
		log.Errorf("SyncBatchWaitDagDependencyTasks fail in ListTasks, err:%+v", err)
		return
	}
	//过滤任务
	tasks = FilterTasksByType(tasks, models.TaskType(taskType))
	if len(tasks) == 0 {
		return
	}

	if count%10 == 0 {
		log.Infof("SyncBatchWaitDagDependencyTasks tasks num %v", len(tasks))
	}

	for i := range tasks {
		task := tasks[i]
		log.Infof("start SyncBatchWaitDagDependencyTasks tasker %v", task.TaskId)
		if task.IsBatchTaskRecentlyUpdated() {
			log.Debugf("SyncBatchWaitDagDependencyTasks tasker %v is recently updated", task.TaskId)
		} else if task.IsAbnormalStopped() {
			log.Warnf("SyncBatchWaitDagDependencyTasks tasker %v is abnormal stopped", task.TaskId)
		} else {
			log.Infof("SyncBatchWaitDagDependencyTasks try ScheduleNonQueueTask, tasker %v ", task.TaskId)
			err = ts.batchBiz.ScheduleNonQueueTask(ctx, task, "BatchTaskSync/SyncBatchWaitDagDependencyTasks")
			if err != nil {
				log.Errorf("SyncBatchWaitDagDependencyTasks tasker %v fail in scheduleNonQueueTask %v", task.TaskId, err)
			}
		}
	}
	log.Infof("SyncBatchWaitDagDependencyTasks end, count:%d", count)
}

// SyncBatchMissingRunningTasks sync internal not running tasks
// (1) call repo to get all biz running but internal not running tasks
// (2) for each tasker try restart it if not update recently
func (ts *BatchTaskSync) SyncBatchMissingRunningTasks(ctx context.Context, count int, taskType v1.TaskType) {
	log.Infof("enter SyncBatchMissingRunningTasks, count:%d", count)

	taskInfo := &models.Task{
		BizStatus:      models.TaskStateActive,
		InternalStatus: models.TaskStateStopped,
		TaskType:       models.TaskType(taskType),
	}
	tasks, err := ts.repo.ListTasks(ctx, taskInfo)
	if err != nil {
		log.Error("SyncBatchMissingRunningTasks fail in ListTasks", err)
		return
	}

	if len(tasks) > 0 {
		log.Infof("SyncBatchMissingRunningTasks tasks num %d", len(tasks))
	} else {
		log.Infof("SyncBatchMissingRunningTasks tasks num not > 0 and return, taskType: %s", taskType)
		return
	}

	// 限制并行任务的数量
	maxGoroutines := 30
	if len(tasks) <= 30 {
		maxGoroutines = len(tasks)
	}
	sem := make(chan struct{}, maxGoroutines)
	var wg sync.WaitGroup

	for i := range tasks {
		task := tasks[i]
		wg.Add(1)
		sem <- struct{}{} // 获取一个许可
		go func(task *models.Task) {
			defer func() {
				if r := recover(); r != nil {
					log.Errorf("SyncBatchMissingRunningTasks task %v panic: %v", task.TaskId, r)
				}
				<-sem // 释放一个许可
				wg.Done()
			}()

			log.Infof("start SyncBatchMissingRunningTasks task %v", task.TaskId)
			if task.IsBatchTaskRecentlyUpdated() {
				log.Debugf("SyncBatchMissingRunningTasks task %v is recently updated", task.TaskId)
			} else if task.IsAbnormalStopped() {
				log.Warnf("SyncBatchMissingRunningTasks task %v is abnormal stopped", task.TaskId)
				ts.taskBiz.processAbnormalResult(ctx, "BatchTaskSync/SyncBatchMissingRunningTasks", task, false, false)
				// ts.taskBiz.cancelTask(ctx, task)
				// todo 处理异常 batch任务直接停止 不再尝试迁移
				// safe.Go(func() {
				// 	err = ts.batchBiz.CancelTask(ctx, task.TaskId)
				// 	if err != nil {
				// 		log.Errorf("SyncBatchMissingRunningTasks task %v is IsAbnormalStopped and fail in CancelTask, err: %v", task.TaskId, err)
				// 	}
				// })
				// ts.taskBiz.processAbnormalResult(ctx, "TaskStatusSync/SyncBatchMissingRunningTasks", task, false, false)
			} else {
				// 该日志用于sls中融合调度任务大盘统计，如日志内容变动需要同步更新sls大盘配置
				ctx = utils.SetTaskId(ctx, task.TaskId)
				ctx = utils.SetTaskProduct(ctx, task.Product)
				ts.log.WithContext(ctx).Infof("SyncBatchMissingRunningTasks task %v, try scheduleAndDispatchTask", task.TaskId)
				err = ts.batchBiz.ScheduleNonQueueTask(ctx, task, "BatchTaskSync/SyncBatchMissingRunningTasks")
				if err != nil {
					log.Errorf("SyncBatchMissingRunningTasks task %v fail in scheduleNonQueueTask %v", task.TaskId, err)
				}
			}
		}(task)
	}

	wg.Wait()
	log.Infof("SyncBatchMissingRunningTasks end, count: %d", count)
}

// SyncUnexpectedRunningTasks sync internal unexpected running tasks
func (ts *BatchTaskSync) SyncBatchUnexpectedRunningTasks(ctx context.Context, count int, taskType v1.TaskType) {
	log.Infof("enter SyncBatchUnexpectedRunningTasks, count:%d, taskType: %s", count, taskType)
	taskInfo := &models.Task{
		BizStatus:      models.TaskStateStopped,
		InternalStatus: models.TaskStateActive,
		TaskType:       models.TaskType(taskType),
	}
	tasks, err := ts.repo.ListTasks(ctx, taskInfo)
	if err != nil {
		log.Error("SyncBatchUnexpectedRunningTasks fail in ListTasks", err)
		return
	}
	if len(tasks) > 0 {
		log.Infof("SyncBatchUnexpectedRunningTasks tasks num %d", len(tasks))
	} else {
		log.Infof("SyncBatchUnexpectedRunningTasks tasks num not > 0 and return, taskType: %s", taskType)
		return
	}

	// 限制并行任务的数量
	maxGoroutines := 30
	if len(tasks) <= 30 {
		maxGoroutines = len(tasks)
	}
	sem := make(chan struct{}, maxGoroutines)
	var wg sync.WaitGroup

	for i := range tasks {
		task := tasks[i]
		wg.Add(1)
		sem <- struct{}{} // 获取协程的许可
		go func(task *models.Task) {
			defer func() {
				if r := recover(); r != nil {
					log.Errorf("SyncBatchUnexpectedRunningTasks task %v panic: %v\nStack:\n%s",
						task.TaskId, r, debug.Stack())
				}
				<-sem // 释放一个许可
				wg.Done()
			}()

			log.Infof("SyncBatchUnexpectedRunningTasks task %v", task.TaskId)
			if task.IsBatchTaskRecentlyUpdated() {
				log.Debugf("SyncBatchUnexpectedRunningTasks task %v not recently updated", task.TaskId)
			} else if task.IsEngineAlreadyStopped() {
				log.Infof("SyncBatchUnexpectedRunningTasks task %v already stopped", task.TaskId)
				err = ts.taskBiz.replyTaskAndUpdateInternalStopped(ctx, task)
				if err != nil {
					log.Errorf("SyncBatchUnexpectedRunningTasks task %v fail in replyTaskAndUpdateInternalStopped, err: %v",
						task.TaskId, err)
				}
			} else if !task.IsBatchTaskRecentlyUpdated() {
				// 该日志用于sls中融合调度任务大盘统计，如日志内容变动需要同步更新sls大盘配置
				ctx = utils.SetTaskId(ctx, task.TaskId)
				ctx = utils.SetTaskProduct(ctx, task.Product)
				ts.log.WithContext(ctx).Infof("SyncBatchUnexpectedRunningTasks task %v, task still running", task.TaskId)

				err = ts.taskBiz.stopTask(ctx, task)
				// stop任务失败处理
				if err != nil {
					log.Errorf("SyncBatchUnexpectedRunningTasks task %v fail in stopTask, err: %v", task.TaskId, err)
					// 如果停止失败，则不再重试，直接将任务状态置为已停止
					task.InternalStatus = models.TaskStateStopped
					err = ts.repo.UpdateInternalStatus(ctx, task)
					if err != nil {
						log.Errorf("SyncBatchUnexpectedRunningTasks fail in UpdateInternalStatus, taskId: %v, err: %v",
							task.TaskId, err)
					}
				}
			}
		}(task)
	}
	wg.Wait()
	log.Infof("SyncBatchUnexpectedRunningTasks end, count: %d", count)
}
