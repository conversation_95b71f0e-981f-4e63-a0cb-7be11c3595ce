package quota

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/avast/retry-go"
	"github.com/cinience/animus/config"
	"github.com/go-kratos/kratos/v2/log"
	"io/ioutil"
	"mpp/internal/agent/logs"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	"net/http"
	"time"
)

/**
 * @Description: get quota from tc-job-analysis
 * @Doc: https://aliyuque.antfin.com/mymedia/eizqgf/gzmhv1z6mhzmp1pf?singleDoc# 《job-analysis接口文档》
 */

type QuotaService struct {
	DefaultQuota *DefaultEngineQuotaSet `json:"defaultQuota"`
	Host         string                 `json:"host"` // tc-job-analysis addr
}

// AnalysisQuotaRequest request, EngineParams(jsonType) can be {}
// EngineParams see https://aliyuque.antfin.com/uvpu6k/lxdngx/hgisyq#dM1K8
type AnalysisQuotaRequest struct {
	JobID                string      `json:"jobId,omitempty"`
	ScheduleParams       interface{} `json:"scheduleParams,omitempty"`
	EngineModel          string      `json:"engineModel,omitempty"`
	EngineParams         string      `json:"engineParams,omitempty"`
	UserId               string      `json:"userId,omitempty"`
	AnalysisMode         string      `json:"analysisMode,omitempty"`
	useWorkerBrainResult bool        `json:"useWorkerBrainResult,omitempty"`
	invokeWorkerBrain    bool        `json:"invokeWorkerBrain,omitempty"`
	//Trace          interface{} `json:"trace,omitempty"`      // optional
	//Tag            string      `json:"tag,omitempty"`        // optional
	//CreateTime     string      `json:"createTime,omitempty"` // optional
}

type AnalysisQuotaResponse struct {
	Code           string           `json:"code"`
	Message        string           `json:"message"`
	ExecuteMode    string           `json:"executeMode"`
	QuotaSet       map[string]int64 `json:"quotaSet"`
	ExpectCostTime int64            `json:"expectCostTime"`
	OriginQuotaSet map[string]int64 `json:"originQuotaSet"`
}

func GetTCAnalysisHost(conf *conf.Bootstrap) string {
	if conf.Quota == nil {
		logs.Infof("QuotaService Host config is nil")
		return ""
	}
	logs.Infof("QuotaService Host config: %s", conf.Quota.Host)
	return conf.Quota.Host
}

// reloadConfig reloads Host from Bootstrap configuration when it changes.
func (qs *QuotaService) reloadConfig() {
	var h conf.Quota
	err := config.WatchConfig("quota", &h, func() {
		log.Infof("QuotaService Host config changed, old host:%s, new host: %s", qs.Host, h.Host)
		qs.Host = h.Host
	})
	if err != nil {
		logs.Errorf("QuotaService Host config reloadConfig error: %v", err)
		return
	}
}

func NewQuotaService(conf *conf.Bootstrap) *QuotaService {
	qs := &QuotaService{
		DefaultQuota: NewDefaultEngineQuotaSet(conf),
		Host:         GetTCAnalysisHost(conf),
	}

	go qs.reloadConfig()
	return qs
}

// GetAnalysisQuota get quota from
// 1. DefaultQuota (if default quota set)
// 2. tc-job-analysis (if default quota not set)
func (qs *QuotaService) GetAnalysisQuota(jobId string, product string, engineModel string, tag string, engineParams string, userId string, scheduleParamsStr string) (map[string]int64, error) {
	qt := qs.DefaultQuota.GetDefaultQuota(product, engineModel, tag)
	if qt != nil {
		return qt, nil
	}

	req := AnalysisQuotaRequest{
		JobID:                jobId,
		ScheduleParams:       "{}",
		EngineModel:          engineModel,
		EngineParams:         engineParams,
		UserId:               userId,
		AnalysisMode:         "byScheduleParams",
		useWorkerBrainResult: false,
		invokeWorkerBrain:    false,
	}
	if scheduleParamsStr != "" {
		scheduleParams, err := models.ParseScheduleParams(scheduleParamsStr)
		if err != nil {
			log.Errorf("GetAnalysisQuota failed in parse scheduleParams failed:jobId:%s,scheduleParamsStr:%s, error:%v", jobId, scheduleParamsStr, err)
			return nil, err
		}
		req.ScheduleParams = scheduleParams
	}

	rst, err := qs.GetAnalysisResponse(req)
	if err != nil {
		return nil, err
	}
	return rst.QuotaSet, nil
}

// GetAnalysisResponse
// @UTTestGetAnalysisQuota
func (qs *QuotaService) GetAnalysisResponse(req AnalysisQuotaRequest) (AnalysisQuotaResponse, error) {
	url := fmt.Sprintf("%s/job/analysis", qs.Host)
	jsonBytes, err := json.Marshal(req)
	if err != nil {
		return AnalysisQuotaResponse{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	var result AnalysisQuotaResponse
	err = retry.Do(
		func() error {
			// Use http.Client and retry on conflict
			client := &http.Client{}
			req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBytes))
			if err != nil {
				return err
			}
			req.Header.Set("Content-Type", "application/json")
			resp, err := client.Do(req)
			if err != nil {
				return err
			}
			defer resp.Body.Close()

			body, err := ioutil.ReadAll(resp.Body)
			if err != nil {
				return err
			}

			err = json.Unmarshal(body, &result)
			if err != nil {
				return err
			}
			return nil
		},
		retry.Attempts(3),
		retry.Delay(200*time.Millisecond),
		retry.LastErrorOnly(true),
	)

	if err != nil {
		return AnalysisQuotaResponse{}, fmt.Errorf("request failed after retry: %w", err)
	}
	return result, nil
}
