package quota

import (
	"mpp/internal/agent/logs"
	"testing"

	"github.com/stretchr/testify/assert"
	"mpp/internal/taskmanager/conf"
)

// TestGetDefaultQuota tests & logs GetDefaultQuota
func TestGetDefaultQuota(t *testing.T) {
	// 1. init
	d := &DefaultEngineQuotaSet{
		EngineQuotaSetMap: &conf.EngineQuotaSetMap{
			EngineQuota: map[string]*conf.QuotaSet{
				"testProduct/testEngineModel/testTag": {
					Quota: map[string]int64{
						"cpu":    100,
						"memory": 1000,
					},
				},
			},
		},
	}

	// 2. get existed quotaSet
	qs1 := d.GetDefaultQuota("testProduct", "testEngineModel", "testTag")
	logs.Infof("qs1: %+v", qs1)
	assert.NotNil(t, qs1)

	// 3. get non-existed quotaSet
	qs2 := d.GetDefaultQuota("foo", "foo", "foo")
	logs.Infof("qs2: %+v", qs2)
	assert.Nil(t, qs2)
}
