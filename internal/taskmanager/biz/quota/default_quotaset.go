package quota

/**
 * get default engine quota from the given Bootstrap configuration. (Diamond/Nacos etc.)
 */

import (
	"fmt"
	"github.com/cinience/animus/config"
	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/agent/logs"
	"mpp/internal/taskmanager/conf"
)

type DefaultEngineQuotaSet struct {
	EngineQuotaSetMap *conf.EngineQuotaSetMap
}

// NewDefaultEngineQuotaSet loads the default engine quota set from the given Bootstrap configuration. (Diamond/Nacos etc.)
func NewDefaultEngineQuotaSet(conf *conf.Bootstrap) *DefaultEngineQuotaSet {
	q := &DefaultEngineQuotaSet{}
	if conf.Quota != nil {
		q.EngineQuotaSetMap = conf.Quota.EngineQuotaSetMap
	} else {
		q.EngineQuotaSetMap = nil
	}

	logs.Infof("DefaultEngineQuotaSet config: %+v", q.EngineQuotaSetMap)

	go q.reloadConfig()
	return q
}

// reloadConfig reloads EngineQuotaSetMap from Bootstrap configuration when it changes.
func (d *DefaultEngineQuotaSet) reloadConfig() {
	var eqm conf.EngineQuotaSetMap
	err := config.WatchConfig("engineQuotaSetMap", &eqm, func() {
		log.Infof("DefaultEngineQuotaSet config changed, new engine quota: %+v", eqm)
		d.EngineQuotaSetMap = &eqm
	})
	if err != nil {
		return
	}
}

//// GetEngineTable retrieves all engineQuotaSet configurations.
//func (d *DefaultEngineQuotaSet) GetEngineTable() *conf.EngineQuotaSetMap {
//	return d.EngineQuotaSetMap
//}

// GetDefaultQuota finds and returns the default quotaSet by {product, engineModel, tag}.
// Returns nil if
//  1. EngineQuotaSetMap is nil (not set in diamond/nacos)
//  2. no matching configuration is found.
func (d *DefaultEngineQuotaSet) GetDefaultQuota(product string, engineModel string, tag string) map[string]int64 {
	if d.EngineQuotaSetMap == nil {
		return nil
	}

	engine := fmt.Sprintf("%s/%s/%s", product, engineModel, tag)
	if q, ok := d.EngineQuotaSetMap.EngineQuota[engine]; ok {
		return q.GetQuota()
	}
	return nil
}
