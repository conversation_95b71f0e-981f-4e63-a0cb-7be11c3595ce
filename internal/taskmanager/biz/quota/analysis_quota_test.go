package quota

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestGetAnalysisQuota tests the GetAnalysisQuota function with various scenarios.
func TestGetAnalysisQuota(t *testing.T) {
	// Test case 1: Successful request with all fields provided
	req1 := AnalysisQuotaRequest{
		JobID:          "test_job_id",
		ScheduleParams: "{}",
		EngineModel:    "media-transfer",
		EngineParams:   "{}",
		UserId:         "test_user_id_mpp_24",
	}
	//expectedResp1 := AnalysisQuotaResponse{Code: "Success"}
	qs := &QuotaService{
		Host: "http://**************:8080",
	}

	//todo 会报错？？
	_, err1 := qs.GetAnalysisResponse(req1)
	//resp1, err1 := qs.GetAnalysisResponse(req1)
	//require.NoError(t, err1, "Case 1: Unexpected error")
	//logs.Infof("Case 1: Response: %+v", resp1)
	//assert.Equal(t, expectedResp1.Code, resp1.Code, "Case 1: Response doesn't match")
	assert.NotNil(t, err1)
}
