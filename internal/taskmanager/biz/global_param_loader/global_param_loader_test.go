package global_param_loader

import (
	"testing"

	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/mocks"
	"mpp/internal/taskmanager/models/domain"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"golang.org/x/time/rate"
)

func TestGlobalParamLoader(t *testing.T) {
	repo := mocks.NewMockKVStoreRepo(gomock.NewController(t))
	repo.EXPECT().ListLike(gomock.Any(), KEY_REPO_FLOWCONTROL, "").Return([]*domain.KVStore{
		{
			Key:   "apiName",
			Value: "100",
		},
	}, nil).AnyTimes()
	repo.EXPECT().List(gomock.Any(), KEY_REPO_PARAMS, KeyNameStillRunningForceRestart).Return([]*domain.KVStore{
		{
			Value: "true",
		},
	}, nil).AnyTimes()
	repo.EXPECT().List(gomock.Any(), KEY_REPO_PARAMS, KeyNameIgnoreReleaseFail).Return([]*domain.KVStore{
		{
			Value: "true",
		},
	}, nil).AnyTimes()
	repo.EXPECT().List(gomock.Any(), KEY_REPO_PARAMS, KeyNameParseTagForSyncData).Return([]*domain.KVStore{
		{
			Value: "true",
		},
	}, nil).AnyTimes()
	repo.EXPECT().List(gomock.Any(), KEY_REPO_QUOTA, KEY_REPO_QUOTA).Return([]*domain.KVStore{
		{
			Key:   "cpu",
			Value: "100",
		},
	}, nil).AnyTimes()
	repo.EXPECT().List(gomock.Any(), KEY_REPO_PARAMS, KeyNameWorkerDomain).Return([]*domain.KVStore{
		{
			Value: `{"domain":"pull-test.cn-shanghai.aliyuncs.com"}`,
		},
	}, nil).AnyTimes()
	repo.EXPECT().ListLike(gomock.Any(), KEY_REPO_QUOTA, gomock.Any()).Return([]*domain.KVStore{
		{
			Key:   "cpu",
			Value: "100",
		},
	}, nil).AnyTimes()
	repo.EXPECT().List(gomock.Any(), KEY_REPO_PARAMS, KeyNameInUseSubnetConfig).Return([]*domain.KVStore{
		{
			Value: "subnet-cn-shanghai-xxxxxx",
		},
	}, nil).AnyTimes()
	repo.EXPECT().List(gomock.Any(), KEY_REPO_PARAMS, KeyNameBackupDomains).Return([]*domain.KVStore{
		{
			Value: `["pull-l3-source"]`,
		},
	}, nil).AnyTimes()
	g := NewGlobalParamLoader(log.GetLogger(), repo, &conf.Bootstrap{
		Cluster: &conf.Cluster{
			Region: "cn-shanghai",
		},
		LiveTransCodeConfig: &conf.LiveTransCodeConfig{
			Unit:    "pre",
			Env:     "pub",
			BizHost: "http://livetranscode.cn-shanghai.aliyuncs.com",
			UnitDatabase: []*conf.LiveTransCodeConfig_UnitDatabase{
				{
					Unit:   "pre",
					Driver: "mysql",
					Source: "1",
				},
				{
					Unit:   "pub",
					Driver: "mysql",
					Source: "2",
				},
			},
		},
	})
	g.loadParams()
	assert.Equal(t, g.GetEnv(), "pub")
	assert.Equal(t, g.GetDefaultUnit(), "pre")
	assert.Equal(t, g.GetDefaultTag(), "livetranscode_pub")
	assert.Equal(t, g.GetRegion(), "cn-shanghai")
	assert.Equal(t, g.GetRateLimiter("apiName").Burst(), 100)
	assert.Equal(t, g.GetBizHost(), "http://livetranscode.cn-shanghai.aliyuncs.com")
	assert.Equal(t, g.GetDatasource("pre"), "1")
	assert.Equal(t, g.IgnoreReleaseFail, true)
	assert.Equal(t, g.GetWorkerDomain(), "pull-test.cn-shanghai.aliyuncs.com")
	m, err := g.Get("cpu")
	assert.Nil(t, err)
	assert.Equal(t, m["cpu"], 100)
	assert.Equal(t, g.ApiQpsLimits["apiName"].Limit(), rate.Limit(100))
	assert.Equal(t, g.GetInUseSubnetConfig(), "subnet-cn-shanghai-xxxxxx")
	assert.Equal(t, g.BackupDomainExist("pull-l3-source"), true)
}
