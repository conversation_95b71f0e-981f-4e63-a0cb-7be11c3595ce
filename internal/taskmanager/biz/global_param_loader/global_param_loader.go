// Package global_param_loader 提供全局参数加载和管理功能
// 负责从KV存储中动态加载和缓存系统配置参数，包括限流配置、业务参数等
package global_param_loader

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models/domain"
	"mpp/internal/taskmanager/repository"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/patrickmn/go-cache"
	"golang.org/x/time/rate"
)

// KV存储中的主要分类键名常量
const (
	KEY_REPO_FLOWCONTROL = "FlowControl" // 流量控制配置键
	KEY_REPO_QUOTA       = "qref"        // 配额引用配置键
	KEY_REPO_PARAMS      = "Params"      // 通用参数配置键
)

// 具体参数键名常量
const (
	KeyNameIgnoreReleaseFail        = "IgnoreReleaseFail"        // 是否忽略释放失败的参数键
	KeyNameStillRunningForceRestart = "StillRunningForceRestart" // 仍在运行时是否强制重启的参数键
	KeyNameWorkerDomain             = "workerDomain"             // Worker域名配置参数键
	KeyNameInUseSubnetConfig        = "InUseSubnetConfig"        // 使用中的子网配置参数键
	KeyNameBackupDomains            = "BackupDomains"            // 备用域名配置参数键
	KeyNameParseTagForSyncData      = "ParseTagForSyncData"      // 同步数据解析标签参数键
)

// GlobalParamLoader 全局参数加载器
// 负责从KV存储中加载和管理系统全局配置参数，包括限流配置、业务参数等
type GlobalParamLoader struct {
	// 限流相关配置
	ApiQpsLimits    map[string]*rate.Limiter // API QPS限流器映射表，key为API标识，value为对应的限流器
	ApiConfigLimits map[string]int           // API配置限制映射表，key为API标识，value为限制值

	// 核心依赖
	repo                repository.KVStoreRepo    // KV存储仓库接口，用于从数据库加载参数
	log                 *log.Helper               // 日志记录器
	liveTransCodeConfig *conf.LiveTransCodeConfig // 直播转码配置
	QuotaCache          *cache.Cache              // 配额缓存，用于缓存配额相关信息
	ctx                 context.Context           // 上下文对象

	// 业务配置参数
	IgnoreReleaseFail        bool     // 是否忽略释放失败
	StillRunningForceRestart bool     // 仍在运行时是否强制重启
	Region                   string   // 区域信息
	workerDomain             string   // Worker域名
	inUseSubnetConfig        string   // 使用中的子网配置
	backupDomains            []string // 备用域名列表
	parseTagForSyncData      bool     // 是否解析标签用于同步数据
}

func NewGlobalParamLoader(logger log.Logger, repo repository.KVStoreRepo, conf *conf.Bootstrap) *GlobalParamLoader {
	g := &GlobalParamLoader{
		repo:                     repo,
		log:                      log.NewHelper(logger),
		ApiQpsLimits:             make(map[string]*rate.Limiter),
		ApiConfigLimits:          make(map[string]int),
		liveTransCodeConfig:      conf.LiveTransCodeConfig,
		QuotaCache:               cache.New(1*time.Minute, 5*time.Minute),
		ctx:                      context.Background(),
		IgnoreReleaseFail:        false,
		StillRunningForceRestart: false,
		Region:                   conf.Cluster.Region,
		workerDomain:             "livetransworker.alivecdn.com",
		inUseSubnetConfig:        "",
		backupDomains:            make([]string, 0),
	}
	if g.liveTransCodeConfig != nil {
		g.log.Infof("global param loader started with config %v", g.liveTransCodeConfig)
		go g.load()
	}
	return g
}

func (g *GlobalParamLoader) load() {
	g.loadParams()
	t := time.NewTicker(time.Second * 60)
	defer t.Stop()
	for {
		select {
		case <-t.C:
			g.loadParams()
		}
	}
}

func (g *GlobalParamLoader) loadParams() {
	g.loadRateLimiters()
	//other load
	g.loadIgnoreReleaseFail()
	g.loadSchedulerForceRestart()
	g.loadWorkerDomain()
	g.loadInUseSubnetConfig()
	g.loadBackupDomains()
	g.loadParseTagForSyncData()
}

func (g *GlobalParamLoader) Get(key string) (map[string]int, error) {
	if data, found := g.QuotaCache.Get(key); found {
		typedData, ok := data.(map[string]int)
		if !ok {
			return nil, fmt.Errorf("unexpected type for cached data")
		}
		return typedData, nil
	}

	result, err := g.loadQuotaCache(key)
	if err != nil {
		return nil, err
	}

	g.QuotaCache.Set(key, result, cache.NoExpiration)
	return result, nil
}

func (g *GlobalParamLoader) loadInUseSubnetConfig() {
	kv, err := g.repo.List(g.ctx, KEY_REPO_PARAMS, KeyNameInUseSubnetConfig)
	if err != nil {
		g.log.Errorf("reload global param inUse subnet failed, err:%v", err)
		return
	}
	if kv == nil || len(kv) == 0 {
		return
	}
	if g.inUseSubnetConfig == kv[0].Value {
		g.log.Infof("reload global param inUse subnet is invalid. old:%s new:%s", g.inUseSubnetConfig, kv[0].Value)
		return
	}
	g.inUseSubnetConfig = kv[0].Value
	g.log.Infof("reload global param inUse subnet success. old:%s new:%s", g.inUseSubnetConfig, kv[0].Value)
}

func (g *GlobalParamLoader) loadWorkerDomain() {
	kv, err := g.repo.List(g.ctx, KEY_REPO_PARAMS, KeyNameWorkerDomain)
	if err != nil {
		g.log.Errorf("load worker domain failed, err:%v", err)
		return
	}
	if kv == nil || len(kv) == 0 {
		return
	}
	config := make(map[string]interface{})
	err = json.Unmarshal([]byte(kv[0].Value), &config)
	if err != nil {
		g.log.Errorf("load worker domain failed, err:%v", err)
		return
	}
	if v, ok := config["domain"]; ok {
		if g.workerDomain == v.(string) {
			return
		}
		if strings.TrimSpace(v.(string)) == "" {
			g.log.Infof("reload global param workerDomain is invalid. old:%s new:%s", g.workerDomain, v.(string))
			return
		}
		g.log.Infof("reload global param workerDomain success. old:%s new:%s", g.workerDomain, v.(string))
		g.workerDomain = v.(string)
	}
}

func (g *GlobalParamLoader) loadQuotaCache(key string) (map[string]int, error) {
	result := make(map[string]int)

	kvPairs, err := g.repo.ListLike(g.ctx, KEY_REPO_QUOTA, key)
	if err != nil {
		return nil, err
	}

	for _, kvPair := range kvPairs {
		value, err := strconv.Atoi(kvPair.Value)
		if err != nil {
			g.log.Errorf("invalid key[%s] = %s", kvPair.Key, kvPair.Value)
			continue
		}
		result[kvPair.Key] = value
	}

	return result, nil
}

func (g *GlobalParamLoader) loadBackupDomains() {
	kvPairs, err := g.repo.List(g.ctx, KEY_REPO_PARAMS, KeyNameBackupDomains)
	if err != nil {
		g.log.Errorf("load backup domains failed, err:%v", err)
		return
	}
	if kvPairs == nil || len(kvPairs) == 0 || len(kvPairs[0].Value) == 0 {
		g.backupDomains = make([]string, 0)
		return
	}

	domains := make([]string, 0)
	err = json.Unmarshal([]byte(kvPairs[0].Value), &domains)
	if err != nil {
		g.log.Errorf("load backup domains failed, err:%v", err)
		return
	}
	for _, d := range domains {
		if strings.TrimSpace(d) == "" {
			g.log.Errorf("load backup domains failed, err:%v", err)
			return
		}
	}
	g.log.Infof("reload global param backupDomains success. old:%v new:%v", g.backupDomains, domains)
	g.backupDomains = domains
}

func (g *GlobalParamLoader) loadSchedulerForceRestart() {
	kvPirs, err := g.repo.List(g.ctx, KEY_REPO_PARAMS, KeyNameStillRunningForceRestart)
	if err != nil {
		g.log.Errorf("load still running force restart failed, err:%v", err)
		return
	}
	if kvPirs == nil || len(kvPirs) == 0 {
		return
	}
	forceRestart, err := strconv.ParseBool(kvPirs[0].Value)
	if err != nil {
		g.log.Errorf("load still running force restart failed, err:%v", err)
		return
	}
	if forceRestart == g.StillRunningForceRestart {
		g.log.Infof("reload global param forceRestart is invalid. old:%v new:%v", forceRestart, g.StillRunningForceRestart)
		return
	}
	g.log.Infof("reload global param forceRestart success. old:%v new:%v", forceRestart, g.StillRunningForceRestart)
	g.StillRunningForceRestart = forceRestart
}

func (g *GlobalParamLoader) loadIgnoreReleaseFail() {
	kvPirs, err := g.repo.List(g.ctx, KEY_REPO_PARAMS, KeyNameIgnoreReleaseFail)
	if err != nil {
		g.log.Errorf("load ignore release fail failed, err:%v", err)
		return
	}
	if kvPirs == nil || len(kvPirs) == 0 {
		return
	}
	ignore, err := strconv.ParseBool(kvPirs[0].Value)
	if err != nil {
		g.log.Errorf("load ignore release fail failed, err:%v", err)
		return
	}
	if ignore == g.IgnoreReleaseFail {
		g.log.Infof("reload global param ignoreReleaseFail is invalid. old:%v new:%v", ignore, g.IgnoreReleaseFail)
		return
	}
	g.log.Infof("reload global param ignoreReleaseFail success. old:%v new:%v", ignore, g.IgnoreReleaseFail)
	g.IgnoreReleaseFail = ignore
}

func (g *GlobalParamLoader) loadParseTagForSyncData() {
	kvPirs, err := g.repo.List(g.ctx, KEY_REPO_PARAMS, KeyNameParseTagForSyncData)
	if err != nil {
		g.log.Errorf("load parse tag for sync data failed, err:%v", err)
		return
	}
	if kvPirs == nil || len(kvPirs) == 0 {
		return
	}
	parseTag, err := strconv.ParseBool(kvPirs[0].Value)
	if err != nil {
		g.log.Errorf("load parse tag for sync data failed, err:%v", err)
		return
	}
	if parseTag == g.parseTagForSyncData {
		g.log.Infof("reload global param parseTagForSyncData is invalid. old:%v new:%v", parseTag, g.parseTagForSyncData)
		return
	}
	g.log.Infof("reload global param parseTagForSyncData success. old:%v new:%v", parseTag, g.parseTagForSyncData)
	g.parseTagForSyncData = parseTag
}

func (g *GlobalParamLoader) loadRateLimiters() {
	kvPairs, err := g.repo.ListLike(g.ctx, KEY_REPO_FLOWCONTROL, "")
	if err != nil {
		g.log.Errorf("load rate limiters failed, err:%v", err)
		return
	}
	for _, kvPair := range kvPairs {
		apiName := kvPair.Key
		limit, err := strconv.Atoi(kvPair.Value)
		if err != nil {
			g.log.Errorf("load rate limiters failed, key:%s, value:%s, err:%v", kvPair.Key, kvPair.Value, err)
			continue
		}
		if limit < 0 || limit > 99999 {
			g.log.Errorf("load rate limiters failed, key:%s, value:%s, out of range", kvPair.Key, kvPair.Value)
			continue
		}
		if v, ok := g.ApiConfigLimits[apiName]; ok && v == limit {
			continue
		}
		g.ApiConfigLimits[apiName] = limit
		if limit < 1 {
			g.log.Infof("load rate limiters success, apiName:%s, limit:%d", apiName, limit)
			limit = 1
		}
		g.ApiQpsLimits[apiName] = rate.NewLimiter(rate.Limit(limit), limit)
		g.log.Infof("load rate limiters success, apiName:%s, limit:%d", apiName, limit)
	}
}

func (g *GlobalParamLoader) GetDefaultUnit() string {
	return g.liveTransCodeConfig.Unit
}

func (g *GlobalParamLoader) GetEnv() string {
	return g.liveTransCodeConfig.Env
}

func (g *GlobalParamLoader) GetDefaultTag() string {
	return fmt.Sprintf("livetranscode_%s", g.liveTransCodeConfig.Env)
}

func (g *GlobalParamLoader) GetRegion() string {
	return g.Region
}

func (g *GlobalParamLoader) GetRateLimiter(apiName string) *rate.Limiter {
	if limiter, ok := g.ApiQpsLimits[apiName]; ok {
		return limiter
	}
	return nil
}

func (g *GlobalParamLoader) GetBizHost() string {
	return g.liveTransCodeConfig.GetBizHost()
}

func (g *GlobalParamLoader) GetGslbHost() string {
	return g.liveTransCodeConfig.GetGslbHost()
}

func (g *GlobalParamLoader) GetLiveTocHost() string {
	return g.liveTransCodeConfig.GetLiveTocHost()
}

func (g *GlobalParamLoader) GetDatasource(unit string) string {
	for _, database := range g.liveTransCodeConfig.UnitDatabase {
		if database.Unit == unit {
			return database.Source
		}
	}
	return ""
}

func (g *GlobalParamLoader) GetWorkerDomain() string {
	return g.workerDomain
}

func (g *GlobalParamLoader) GetInUseSubnetConfig() string {
	return g.inUseSubnetConfig
}

func (g *GlobalParamLoader) BackupDomainExist(backupDomain string) bool {
	for _, d := range g.backupDomains {
		if d == backupDomain {
			return true
		}
	}
	return false
}

func (g *GlobalParamLoader) GetParseTagForSyncData() bool {
	return g.parseTagForSyncData
}

func (g *GlobalParamLoader) SetParams(ctx context.Context, kv *domain.KVStore) error {
	if kv == nil || kv.Repo == "" || kv.Key == "" {
		return nil
	}
	err := g.repo.Set(ctx, kv)
	if err != nil {
		g.log.Errorf("set params failed, err:%v", err)
		return err
	}
	return nil
}
