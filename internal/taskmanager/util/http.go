package util

import (
	"bytes"
	"crypto/md5"
	"crypto/rand"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math/big"
	"net/http"
	"net/http/httptrace"
	"net/url"
	"strings"
	"syscall"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"go.uber.org/zap"
)

// CheckHTTPResponse checking whether the HTTP return value is correct
func CheckHTTPResponse(resp *http.Response, err error) error {
	var response RestResponse
	return CheckRestResponse(resp, err, &response)
}

// CheckRestResponse checking whether the REST response value is correct
func CheckRestResponse(resp *http.Response, err error, responser RestResponser) error {
	// If there is an error, I will return the error directly.
	if nil != err {
		return err
	} else if nil == resp {
		return errors.New("http response is nil")
	}

	if nil != resp.Body {
		defer resp.Body.Close()
	}

	// Read HTTP response body.
	body, err := ioutil.ReadAll(resp.Body)
	if nil != err {
		log.Error("read body failed", zap.Error(err))
		return err
	} else if len(body) == 0 {
		// If there is no data in the body, determine the error based on the HTTP response status code only
		if http.StatusOK == resp.StatusCode {
			return nil
		}

		return errors.New(resp.Status)
	}

	// Parse rest response.
	if err = json.Unmarshal(body, responser); nil != err {
		log.Error("ummarshal response failed", zap.Error(err), zap.String("body", string(body)))
		return err
	}

	// Check code.
	if http.StatusOK/100 == resp.StatusCode/100 && "success" == strings.ToLower(responser.GetCode()) {
		return nil
	}

	return errors.New(responser.GetCode())
}

// HTTPPostErrorOnly post http request, return errors only.
func HTTPPostErrorOnly(client *http.Client, url, secret string, request interface{}, responser RestResponser) error {
	_, _, err := HTTPPost(client, url, secret, request, responser)
	return err
}

// HTTPPostError post http request, return errors, whether network error.
func HTTPPostError(client *http.Client, url, secret string, request interface{}, responser RestResponser) (bool, error) {
	net, _, err := HTTPPost(client, url, secret, request, responser)
	return net, err
}

// HTTPPost post http request, return errors, whether network error and status code.
func HTTPPost(client *http.Client, url, secret string, request interface{}, responser RestResponser) (bool, int, error) {
	// Marshal request.
	var err error
	var body []byte
	if nil != request {
		if body, err = json.Marshal(request); nil != err {
			log.Error("json marshal failed", zap.Error(err))
			return false, 0, err
		}
	}

	var auth string
	if secret != "" {
		uri := url[len("http://")+strings.Index(url[len("http://"):], "/"):]
		expired := time.Now().Add(time.Minute).Unix()
		rand, err := rand.Int(rand.Reader, big.NewInt(10000))
		if err != nil {
			log.Error("HTTPPost get rand failed", zap.Error(err))
			return false, 0, err
		}
		auth = fmt.Sprintf("%d-%d-%x", expired, rand, md5.Sum([]byte(fmt.Sprintf("%s-%d-%d-%s-%s", uri, expired, rand, string(body), secret))))
	}

	return HTTPPostWithReader(client, url, auth, bytes.NewReader(body), responser)
}

// HTTPPostWithReader post http request, return errors and whether network error.
func HTTPPostWithReader(client *http.Client, url, auth string, body io.Reader, responser RestResponser) (bool, int, error) {
	req, err := http.NewRequest("POST", url, body)
	if err != nil {
		return false, 0, err
	}

	req.Header.Set("Content-Type", "application/json")
	if auth != "" {
		req.Header.Set("Authorization", auth)
	}

	// Create http request with trace.
	//var trace httpRequestTrace
	//enableTrace := configure.EnableHTTPTrace
	//if enableTrace {
	//	req = createRequestTrace(req, &trace)
	//}

	// Send HTTP request.
	resp, err := client.Do(req)
	if nil != err {
		return true, 0, err
	}

	var response RestResponse
	if nil == responser {
		responser = &response
	}

	if err := CheckRestResponse(resp, err, responser); nil != err {
		log.Error("post request failed", zap.Error(err), zap.String("url", url))
		return false, resp.StatusCode, err
	}

	return false, resp.StatusCode, nil
}

// httpRequestTrace define http request trace.
type httpRequestTrace struct {
	GetConn              *httpRequestTraceGetConn              `json:"GetConn,omitempty"`
	GotConn              *httpRequestTraceGotConn              `json:"GotConn,omitempty"`
	GotFirstResponseByte *httpRequestTraceGotFirstResponseByte `json:"GotFirstResponseByte,omitempty"`
	DNSStart             *httpRequestTraceDNSStart             `json:"DNSStart,omitempty"`
	DNSDone              *httpRequestTraceDNSDone              `json:"DNSDone,omitempty"`
	ConnectStart         *httpRequestTraceConnectStart         `json:"ConnectStart,omitempty"`
	ConnectDone          *httpRequestTraceConnectDone          `json:"ConnectDone,omitempty"`
	TLSHandshakeStart    *httpRequestTraceTLSHandshakeStart    `json:"TLSHandshakeStart,omitempty"`
	TLSHandshakeDone     *httpRequestTraceTLSHandshakeDone     `json:"TLSHandshakeDone,omitempty"`
	WroteHeaders         *httpRequestTraceWroteHeaders         `json:"WroteHeaders,omitempty"`
	WroteRequest         *httpRequestTraceWroteRequest         `json:"WroteRequest,omitempty"`
}

func (t *httpRequestTrace) OnGetConn(hostPort string) {
	t.GetConn = &httpRequestTraceGetConn{Time: time.Now(), HostPort: hostPort}
}

func (t *httpRequestTrace) OnGotConn(connInfo httptrace.GotConnInfo) {
	t.GotConn = &httpRequestTraceGotConn{Time: time.Now(), ConnInfo: connInfo}
}

func (t *httpRequestTrace) OnGotFirstResponseByte() {
	t.GotFirstResponseByte = &httpRequestTraceGotFirstResponseByte{Time: time.Now()}
}

func (t *httpRequestTrace) OnDNSStart(dnsStartInfo httptrace.DNSStartInfo) {
	t.DNSStart = &httpRequestTraceDNSStart{Time: time.Now(), DNSStartInfo: dnsStartInfo}
}

func (t *httpRequestTrace) OnDNSDone(dnsDoneInfo httptrace.DNSDoneInfo) {
	t.DNSDone = &httpRequestTraceDNSDone{Time: time.Now(), DNSDoneInfo: dnsDoneInfo}
}

func (t *httpRequestTrace) OnConnectStart(network, addr string) {
	t.ConnectStart = &httpRequestTraceConnectStart{Time: time.Now(), Network: network, Addr: addr}
}
func (t *httpRequestTrace) OnConnectDone(network, addr string, err error) {
	t.ConnectDone = &httpRequestTraceConnectDone{Time: time.Now(), Network: network, Addr: addr, Error: err}
}

func (t *httpRequestTrace) OnTLSHandshakeStart() {
	t.TLSHandshakeStart = &httpRequestTraceTLSHandshakeStart{Time: time.Now()}
}

func (t *httpRequestTrace) OnTLSHandshakeDone(connectionState tls.ConnectionState, err error) {
	t.TLSHandshakeDone = &httpRequestTraceTLSHandshakeDone{Time: time.Now(), ConnectionState: connectionState, Error: err}
}

func (t *httpRequestTrace) OnWroteHeaders() {
	t.WroteHeaders = &httpRequestTraceWroteHeaders{Time: time.Now()}
}
func (t *httpRequestTrace) OnWroteRequest(wroteRequestInfo httptrace.WroteRequestInfo) {
	t.WroteRequest = &httpRequestTraceWroteRequest{Time: time.Now(), WroteRequestInfo: wroteRequestInfo}
}

type httpRequestTraceGetConn struct {
	Time     time.Time
	HostPort string
}
type httpRequestTraceGotConn struct {
	Time     time.Time
	ConnInfo httptrace.GotConnInfo
}
type httpRequestTraceGotFirstResponseByte struct {
	Time time.Time
}
type httpRequestTraceDNSStart struct {
	Time         time.Time
	DNSStartInfo httptrace.DNSStartInfo
}
type httpRequestTraceDNSDone struct {
	Time        time.Time
	DNSDoneInfo httptrace.DNSDoneInfo
}
type httpRequestTraceConnectStart struct {
	Time    time.Time
	Network string
	Addr    string
}
type httpRequestTraceConnectDone struct {
	Time    time.Time
	Network string
	Addr    string
	Error   error
}
type httpRequestTraceTLSHandshakeStart struct {
	Time time.Time
}
type httpRequestTraceTLSHandshakeDone struct {
	Time            time.Time
	ConnectionState tls.ConnectionState
	Error           error
}
type httpRequestTraceWroteHeaders struct {
	Time time.Time
}
type httpRequestTraceWroteRequest struct {
	Time             time.Time
	WroteRequestInfo httptrace.WroteRequestInfo
}

// createRequestTrace create http request trace.
func createRequestTrace(req *http.Request, trace *httpRequestTrace) *http.Request {
	return req.WithContext(httptrace.WithClientTrace(req.Context(), &httptrace.ClientTrace{
		GetConn:              trace.OnGetConn,
		GotConn:              trace.OnGotConn,
		GotFirstResponseByte: trace.OnGotFirstResponseByte,
		DNSStart:             trace.OnDNSStart,
		DNSDone:              trace.OnDNSDone,
		ConnectStart:         trace.OnConnectStart,
		ConnectDone:          trace.OnConnectDone,
		TLSHandshakeStart:    trace.OnTLSHandshakeStart,
		TLSHandshakeDone:     trace.OnTLSHandshakeDone,
		WroteHeaders:         trace.OnWroteHeaders,
		WroteRequest:         trace.OnWroteRequest,
	}))
}

// IsProbableEOF returns true if the given error resembles a connection termination
// scenario that would justify assuming that the watch is empty.
// These errors are what the Go http stack returns back to us which are general
// connection closure errors (strongly correlated) and callers that need to
// differentiate probable errors in connection behavior between normal "this is
// disconnected" should use the method.
// UT@TestIsHTTPProbableEOF
func IsHTTPProbableEOF(err error) bool {
	if err == nil {
		return false
	}
	var uerr *url.Error
	if errors.As(err, &uerr) {
		err = uerr.Err
	}
	msg := err.Error()
	switch {
	case err == io.EOF:
		return true
	case err == io.ErrUnexpectedEOF:
		return true
	case msg == "http: can't write HTTP request on broken connection":
		return true
	case strings.Contains(msg, "http2: server sent GOAWAY and closed the connection"):
		return true
	case strings.Contains(msg, "connection reset by peer"):
		return true
	case strings.Contains(strings.ToLower(msg), "use of closed network connection"):
		return true
	}
	return false
}

// Returns if the given err is "connection reset by peer" error.
func IsHTTPConnectionReset(err error) bool {
	var errno syscall.Errno
	if errors.As(err, &errno) {
		return errno == syscall.ECONNRESET
	}
	return false
}
