package util

import (
	"bytes"
	"encoding/json"
	"errors"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"testing"

	v1 "proto.mpp/api/common/v1"

	kratosErr "github.com/go-kratos/kratos/v2/errors"
)

// MockRestResponser mocks the RestResponser interface for testing
type MockRestResponser struct {
	code string
}

func (m *MockRestResponser) GetCode() string {
	return m.code
}

// TestCheckHTTPResponse tests the CheckHTTPResponse function
func TestCheckHTTPResponse(t *testing.T) {
	tests := []struct {
		name        string
		resp        *http.Response
		err         error
		expectedErr error
	}{
		{
			name: "nil error and non-nil response",
			resp: &http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewBufferString(`{"code": "success"}`)),
			},
			err:         nil,
			expectedErr: kratosErr.New(http.StatusInternalServerError, v1.ErrorReason_INTERNAL_ERROR.String(), "internal error."),
		},
		{
			name: "nil error and empty body",
			resp: &http.Response{
				StatusCode: http.StatusInternalServerError,
				Body:       ioutil.NopCloser(bytes.NewBufferString(``)),
			},
			err:         nil,
			expectedErr: errors.New("Internal Server Error"),
		},
		{
			name: "json unmarshal error",
			resp: &http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewBufferString(`not a json`)),
			},
			err:         nil,
			expectedErr: errors.New("invalid character 'n' looking for beginning of value"),
		},
		{
			name:        "non-nil error",
			resp:        nil,
			err:         errors.New("some error"),
			expectedErr: errors.New("some error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := CheckRestResponse(tt.resp, tt.err, nil)

			if err != nil && tt.expectedErr == nil {
				t.Errorf("expected error: %v, got: %v", tt.expectedErr, err)
			}
		})
	}
}

// TestHTTPPostError tests the HTTPPostError function
func TestHTTPPostError(t *testing.T) {
	tests := []struct {
		name       string
		mockServer *httptest.Server
	}{
		{
			name: "successful post",
			mockServer: httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusOK)
				json.NewEncoder(w).Encode(map[string]string{"code": "success"})
			})),
		},
		{
			name: "failed post with internal error",
			mockServer: httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusInternalServerError)
			})),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer tt.mockServer.Close()

			client := &http.Client{}

			_, err := HTTPPostError(client, tt.mockServer.URL, "", struct{}{}, nil)

			if tt.name == "successful post" && err != nil {
				t.Errorf("expected no error, got: %v", err)
			} else if tt.name == "failed post with internal error" && err == nil {
				t.Error("expected an error but got none")
			}
		})
	}
}

// Additional tests for other functions would follow a similar pattern...
