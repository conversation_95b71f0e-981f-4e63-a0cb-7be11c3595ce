package util

import (
	"net/http"
	"sync"
	"time"
)

// HTTPClientPool HTTP客户端池
type HTTPClientPool struct {
	clients map[string]*http.Client
	mu      sync.RWMutex
}

// NewHTTPClientPool 创建HTTP客户端池
func NewHTTPClientPool() *HTTPClientPool {
	return &HTTPClientPool{
		clients: make(map[string]*http.Client),
	}
}

// GetClient 获取或创建HTTP客户端，超时时间统一为5s
func (p *HTTPClientPool) GetClient(redirectAllow bool) *http.Client {
	p.mu.RLock()
	clientType := "direct"
	if !redirectAllow {
		clientType = "no_direct"
	}
	if client, exists := p.clients[clientType]; exists {
		p.mu.RUnlock()
		return client
	}
	p.mu.RUnlock()

	p.mu.Lock()
	defer p.mu.Unlock()

	// 双重检查
	if client, exists := p.clients[clientType]; exists {
		return client
	}

	// 根据类型创建不同的客户端
	var client *http.Client
	if redirectAllow {
		client = &http.Client{
			Timeout: 5 * time.Second,
			Transport: &http.Transport{
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: 10,
				IdleConnTimeout:     90 * time.Second,
				DisableCompression:  true,
			},
		}
	} else {
		client = &http.Client{
			Timeout: 5 * time.Second,
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				return http.ErrUseLastResponse
			},
			Transport: &http.Transport{
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: 10,
				IdleConnTimeout:     90 * time.Second,
				DisableCompression:  true,
			},
		}
	}

	p.clients[clientType] = client
	return client
}

// Close 关闭所有客户端
func (p *HTTPClientPool) Close() {
	p.mu.Lock()
	defer p.mu.Unlock()

	for _, client := range p.clients {
		if transport, ok := client.Transport.(*http.Transport); ok {
			transport.CloseIdleConnections()
		}
	}
}
