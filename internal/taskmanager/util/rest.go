package util

// RestResponser defines the response of the REST request
type RestResponser interface {
	GetCode() string
	SetCode(string)
	GetMessage() string
	SetMessage(string)
	GetResult() interface{}
	SetResult(interface{})
}

// RestResponse implement RestResponser
type RestResponse struct {
	Code    string      `json:"code,omitempty"`
	Message string      `json:"message,omitempty"`
	Result  interface{} `json:"data,omitempty"`
}

var _ RestResponser = &RestResponse{}

// GetCode implement RestResponser.GetCode()
func (rr *RestResponse) GetCode() string { return rr.Code }

// SetCode implement RestResponser.SetCode()
func (rr *RestResponse) SetCode(code string) { rr.Code = code }

// GetMessage implement RestResponser.GetMessage()
func (rr *RestResponse) GetMessage() string { return rr.Message }

// SetMessage implement RestResponser.SetMessage()
func (rr *RestResponse) SetMessage(message string) { rr.Message = message }

// GetResult implement RestResponser.GetResult()
func (rr *RestResponse) GetResult() interface{} { return rr.Result }

// SetResult implement RestResponser.SetResult()
func (rr *RestResponse) SetResult(result interface{}) { rr.Result = result }

// Error set error code.
func (rr *RestResponse) Error(err error) { rr.Code = err.Error() }

// ErrorWithMessage set error and message.
func (rr *RestResponse) ErrorWithMessage(err error, message string) {
	rr.Code, rr.Message = err.Error(), message
}
