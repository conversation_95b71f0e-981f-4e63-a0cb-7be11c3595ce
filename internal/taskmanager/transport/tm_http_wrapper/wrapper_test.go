package tm_http_wrapper

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/stretchr/testify/assert"
)

/**
 * @Author: qinxin
 * @Date: 2024/9/26 11:42
 * @Desc:
 * @Version 1.0
 */
//
//func TestWithCommonHandlerAndError(t *testing.T) {
//	ctrl := gomock.NewController(t)
//	defer ctrl.Finish()
//
//	// Initialize the mocks and test variables
//	mockHandler := func(w http.ResponseWriter, r *http.Request) (any, error) {
//		w.WriteHeader(http.StatusOK)
//		return "Success", nil
//	}
//
//	// Create a new HTTP context
//	c, _ := gin.CreateTestContext(httptest.NewRecorder())
//	c.Request, _ = http.NewRequest(http.MethodGet, "/test", nil)
//
//	// Call the function with the mocked handler
//	WithCommonHandlerAndError(mockHandler)
//
//	assert.Equal(t, http.StatusOK, c.Writer.Status())
//}
//
//func TestWithCommonHandlerAndErrorReturnsError(t *testing.T) {
//	ctrl := gomock.NewController(t)
//	defer ctrl.Finish()
//
//	mockHandler := func(w http.ResponseWriter, r *http.Request) (any, error) {
//		return nil, common.ErrInternalError
//	}
//
//	c, _ := gin.CreateTestContext(httptest.NewRecorder())
//	c.Request, _ = http.NewRequest(http.MethodGet, "/test", nil)
//
//	WithCommonHandlerAndError(mockHandler)
//
//	assert.Equal(t, http.StatusOK, c.Writer.Status()) // Ensure that it returns a 200 HTTP status
//}
//
//func TestWithTCHandlerAndError(t *testing.T) {
//	ctrl := gomock.NewController(t)
//	defer ctrl.Finish()
//
//	mockHandler := func(w http.ResponseWriter, r *http.Request) (any, error) {
//		return "TC Success", nil
//	}
//
//	c, _ := gin.CreateTestContext(httptest.NewRecorder())
//	c.Request, _ = http.NewRequest(http.MethodGet, "/test", nil)
//
//	WithTCHandlerAndError(mockHandler)
//	assert.Equal(t, http.StatusOK, c.Writer.Status())
//}
//
//func TestWithTCHandlerAndErrorReturnsTCError(t *testing.T) {
//	ctrl := gomock.NewController(t)
//	defer ctrl.Finish()
//
//	mockHandler := func(w http.ResponseWriter, r *http.Request) (any, error) {
//		return nil, common.ErrInternalError
//	}
//
//	c, _ := gin.CreateTestContext(httptest.NewRecorder())
//	c.Request, _ = http.NewRequest(http.MethodGet, "/test", nil)
//
//	WithTCHandlerAndError(mockHandler)
//
//	assert.Equal(t, http.StatusInternalServerError, c.Writer.Status()) // Assuming it would return 500
//}

// Additional tests for WithPOPHandlerAndError, WithTMHandlerAndError, and WithVODHandlerAndError can be structured similarly

// In the same way, implement tests for WithPOPHandlerAndError, WithTMHandlerAndError, and WithVODHandlerAndError

func TestMakeLiveResultFromRst(t *testing.T) {
	rst := map[string]interface{}{
		"statusCode": 200,
		"result":     "some data",
		"code":       "Success",
		"message":    "Operation completed successfully",
	}
	code, result := MakeLiveResultFromRst(rst)
	assert.Equal(t, code, http.StatusOK)
	assert.Equal(t, result.(map[string]interface{})["result"].(string), "some data")
}

func TestMakeLiveResult(t *testing.T) {
	code := "Success"
	message := "Operation completed successfully"
	result := MakeLiveTranscodeResult(code, message)

	assert.Equal(t, result["retCode"].(int), -1)
	assert.Equal(t, result["description"].(string), "Operation completed successfully")
}

func TestWithLiveHandlerAndError(t *testing.T) {
	WithLiveHandlerAndError(MockLiveHandler, MakeTMResult)
	result, err := MockLiveHandler(nil, nil)
	assert.NotNil(t, err)
	assert.Equal(t, result.(map[string]interface{})["code"].(int), -1)
}

func TestMakeTMResult(t *testing.T) {
	code := "Success"
	message := "Operation completed successfully"
	result := MakeTMResult(code, message)

	assert.Equal(t, result["result"].(map[string]string)["code"], code)
	assert.Equal(t, result["result"].(map[string]string)["message"], message)
}

// TestMakeTMResultFromRst 测试案例：
// 案例1: 输入一个标准的map类型数据，包含"statusCode"键值对
// 案例2: 输入非标准的数据类型
// 案例3: 输入不包含"statusCode"键的map类型数据
func TestMakeTMResultFromRst(t *testing.T) {
	cases := []struct {
		input   any
		expCode int
		expRes  any
	}{
		{map[string]interface{}{"statusCode": 200, "result": "some data"}, 200, map[string]interface{}{"statusCode": 200, "result": "some data"}},
		{"non-map type", 200, "non-map type"},
		//todo 如果map中不包含statusCode，强转int会panic
		//{map[string]interface{}{"otherKey": "value"}, 200, map[string]interface{}{"otherKey": "value"}},
	}

	for _, c := range cases {
		code, res := MakeTMResultFromRst(c.input)
		if code != c.expCode || fmt.Sprintf("%v", res) != fmt.Sprintf("%v", c.expRes) {
			t.Errorf("For input %v; expected (%d, %v), got (%d, %v)", c.input, c.expCode, c.expRes, code, res)
		}
	}
}

// TestMakeVODResult 测试案例：
// 案例1: 提供所有参数的情况
// 案例2: 不提供code参数的情况（应默认为“Failed”）
func TestMakeVODResult(t *testing.T) {
	testCases := []struct {
		code     string
		message  string
		expected map[string]interface{}
	}{
		{"SUCCESS", "操作成功", map[string]interface{}{"code": "SUCCESS", "message": "操作成功"}},
		{"", "操作失败", map[string]interface{}{"code": "Failed", "message": "操作失败"}},
	}

	for _, tc := range testCases {
		result := MakeVODResult(tc.code, tc.message)
		if fmt.Sprintf("%v", result) != fmt.Sprintf("%v", tc.expected) {
			t.Errorf("Test failed with input '%s', expected %v, got %v", tc.code, tc.expected, result)
		}
	}
}

// TestMakePOPResult 测试案例：
// 案例1: 输入正常状态码和其他参数
// 案例2: 输入异常状态码和其他参数
// 案例3: 输入空的rst参数
func TestMakePOPResult(t *testing.T) {
	cases := []struct {
		httpStatus int32
		code       string
		message    string
		rst        any
		expCode    int
		expResp    map[string]interface{}
	}{
		{http.StatusOK, "SUCCESS", "操作成功", map[string]interface{}{"requestId": "req_123"}, http.StatusOK, map[string]interface{}{"requestId": "req_123", "success": true, "httpStatusCode": 200, "code": "SUCCESS", "message": "操作成功"}},
		//{http.StatusInternalServerError, "", "", nil, 500, map[string]interface{}{"success": false, "httpStatusCode": 500, "code": "INTERNAL_ERROR", "message": "internal error"}},
		{http.StatusOK, "", "", nil, 200, map[string]interface{}{"success": true, "httpStatusCode": 200, "code": "", "message": ""}},
	}

	for _, c := range cases {
		code, resp := MakePOPResult(c.httpStatus, c.code, c.message, c.rst)
		if code != c.expCode || fmt.Sprintf("%v", resp) != fmt.Sprintf("%v", c.expResp) {
			t.Errorf("For input (%d, %s, %s, %v); expected (%d, %v), got (%d, %v)", c.httpStatus, c.code, c.message, c.rst, c.expCode, c.expResp, code, resp)
		}
	}
}

func MockLiveHandler(w http.ResponseWriter, r *http.Request) (any, error) {
	return map[string]interface{}{
		"code":    -1,
		"data":    "some data",
		"message": "Operation completed successfully",
	}, errors.New(500, "reason", "message")
}
