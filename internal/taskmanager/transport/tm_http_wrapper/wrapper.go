package tm_http_wrapper

import (
	"context"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/service"
	stdhttp "net/http"

	"github.com/cinience/animus/contribs/transport/wrapper"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/transport/http"
)

// 通用错误处理
func WithCommonHandlerAndError(handler func(stdhttp.ResponseWriter, *stdhttp.Request) (any, error)) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		br := wrapper.NewBodyRecorder(ctx.Response(), 0)
		r := ctx.Request()
		http.SetOperation(ctx, r.URL.Path)

		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			rst, err := handler(br, r.WithContext(ctx))
			return rst, err
		})

		rst, err := h(ctx, r)
		if err != nil {
			kratosErr := common.ToKratosError(err)
			e2 := errors.FromError(kratosErr)
			code, result := MakePOPResult(e2.Code, e2.Reason, e2.Message, rst)
			//failed: pop rpc风格接口 httpCode需要是200，具体错误信息由result返回
			if common.IsPopReq(r) {
				code = stdhttp.StatusOK
			}
			err = ctx.JSON(code, result)
		} else {
			err = ctx.JSON(stdhttp.StatusOK, rst)
		}

		return err
	}
}

// 通用错误处理
func WithInvokeHandlerAndError(handler func(stdhttp.ResponseWriter, *stdhttp.Request) (any, error)) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		br := wrapper.NewBodyRecorder(ctx.Response(), 0)
		r := ctx.Request()
		http.SetOperation(ctx, r.URL.Path)

		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			rst, err := handler(br, r.WithContext(ctx))
			return rst, err
		})

		rst, err := h(ctx, r)
		if err != nil {
			kratosErr := common.ToKratosError(err)
			e2 := errors.FromError(kratosErr)
			code, result := MakeInvokeResult(e2.Code, e2.Reason, e2.Message, rst)
			//failed: pop rpc风格接口 httpCode需要是200，具体错误信息由result返回
			if common.IsPopReq(r) {
				code = stdhttp.StatusOK
			}
			err = ctx.JSON(code, result)
		} else {
			err = ctx.JSON(stdhttp.StatusOK, rst)
		}

		return err
	}
}

func WithTCHandlerAndError(handler func(stdhttp.ResponseWriter, *stdhttp.Request) (any, error)) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		br := wrapper.NewBodyRecorder(ctx.Response(), 0)
		r := ctx.Request()
		http.SetOperation(ctx, r.URL.Path)

		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			rst, err := handler(br, r.WithContext(ctx))
			return rst, err
		})

		rst, err := h(ctx, r)
		if err != nil {
			//构造适配tc的错误码
			httpCode, result := service.MakeTCResultError(err)
			err = ctx.JSON(httpCode, result)
		} else {
			err = ctx.JSON(stdhttp.StatusOK, rst)
		}

		return err
	}
}

func WithPOPHandlerAndError(handler func(stdhttp.ResponseWriter, *stdhttp.Request) (any, error)) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		br := wrapper.NewBodyRecorder(ctx.Response(), 0)
		r := ctx.Request()
		http.SetOperation(ctx, r.URL.Path)

		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			rst, err := handler(br, r.WithContext(ctx))
			return rst, err
		})

		rst, err := h(ctx, r)
		if err != nil {
			kratosErr := common.ToKratosError(err)
			e2 := errors.FromError(kratosErr)
			_, result := MakePOPResult(e2.Code, e2.Reason, e2.Message, rst)
			//failed: pop rpc风格接口 httpCode需要是200，具体错误信息由result返回
			err = ctx.JSON(stdhttp.StatusOK, result)
		} else {
			//success:
			err = ctx.JSON(stdhttp.StatusOK, rst)
		}

		return err
	}
}

func WithTMHandlerAndError(handler func(stdhttp.ResponseWriter, *stdhttp.Request) (any, error)) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		br := wrapper.NewBodyRecorder(ctx.Response(), 0)
		r := ctx.Request()
		http.SetOperation(ctx, r.URL.Path)

		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			rst, err := handler(br, r.WithContext(ctx))
			return rst, err
		})

		rst, err := h(ctx, r)
		if err != nil {
			e2 := errors.FromError(err)
			result := MakeTMResult(e2.Reason, e2.Message)
			err = ctx.JSON(int(e2.Code), result)
		} else {
			code, responseData := MakeTMResultFromRst(rst)
			err = ctx.JSON(code, responseData)
		}
		return err
	}
}

func WithLiveHandlerAndError(handler func(stdhttp.ResponseWriter, *stdhttp.Request) (any, error), makeErrorResultHandler func(string, string) map[string]interface{}) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		br := wrapper.NewBodyRecorder(ctx.Response(), 0)
		r := ctx.Request()
		http.SetOperation(ctx, r.URL.Path)

		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			rst, err := handler(br, r.WithContext(ctx))
			return rst, err
		})
		rst, err := h(ctx, r)
		if err != nil {
			e2 := errors.FromError(err)
			result := makeErrorResultHandler(e2.Reason, e2.Message)
			err = ctx.JSON(int(e2.Code), result)
		} else {
			code, responseData := MakeLiveResultFromRst(rst)
			err = ctx.JSON(code, responseData)
		}
		return err
	}
}

func WithVODHandlerAndError(handler func(stdhttp.ResponseWriter, *stdhttp.Request) (any, error)) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		br := wrapper.NewBodyRecorder(ctx.Response(), 0)
		r := ctx.Request()
		http.SetOperation(ctx, r.URL.Path)

		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			rst, err := handler(br, r.WithContext(ctx))
			return rst, err
		})

		rst, err := h(ctx, r)
		if err != nil {
			e2 := errors.FromError(err)
			result := MakeVODResult(e2.Reason, e2.Message)
			err = ctx.JSON(int(e2.Code), result)
		} else {
			err = ctx.JSON(stdhttp.StatusOK, rst)
		}
		return err
	}
}

func MakeTMResultFromRst(rst any) (int, any) {
	var httpCode int
	// 这样的判断太死了，返回不是此类的结果时就不通了  可以改为只取statusCode，剩下直接放到result里
	if m, ok := rst.(map[string]interface{}); ok {
		httpCode = m["statusCode"].(int)
		// resp := make(map[string]interface{})
		// resp["result"] = m["result"]
		return httpCode, rst
	} else {
		return stdhttp.StatusOK, rst
	}
}

func MakeLiveResultFromRst(rst any) (int, any) {
	var httpCode int
	if m, ok := rst.(map[string]interface{}); ok {
		if m["statusCode"] != nil {
			httpCode = m["statusCode"].(int)
			// resp := make(map[string]interface{})
			// resp["result"] = m["result"]
			return httpCode, rst
		} else {
			return stdhttp.StatusOK, rst
		}
	} else {
		return stdhttp.StatusOK, rst
	}
}

func MakeTMResult(code string, message string) map[string]interface{} {
	result := make(map[string]string)
	result["code"] = code
	result["message"] = message

	resp := make(map[string]interface{})
	resp["result"] = result

	return resp
}

func MakeLiveTranscodeResult(code string, message string) map[string]interface{} {
	result := make(map[string]interface{})
	if message != "" {
		result["description"] = message
	}
	result["retCode"] = -1

	return result
}

func MakeVODResult(code string, message string) map[string]interface{} {
	result := make(map[string]interface{})
	if code == "" {
		code = "Failed"
	}
	result["code"] = code
	result["message"] = message
	return result
}

// 通用错误处理，包含错误码透出检查
func MakePOPResult(httpStatusCode int32, code string, message string, rst any) (int, map[string]interface{}) {
	resp := make(map[string]interface{})
	if m, ok := rst.(map[string]interface{}); ok {
		resp["requestId"] = m["requestId"]
	}

	resp["success"] = true
	if httpStatusCode != stdhttp.StatusOK {
		resp["success"] = false
		// 对错误码做透出处理
		httpStatusCode, code, message = common.MakeExposeError(httpStatusCode, code, message)
	}
	resp["httpStatusCode"] = httpStatusCode
	resp["code"] = code
	resp["message"] = message

	return int(httpStatusCode), resp
}

func MakeInvokeResult(httpStatusCode int32, code string, message string, rst any) (int, map[string]interface{}) {
	resp := make(map[string]interface{})
	if m, ok := rst.(map[string]interface{}); ok {
		resp["requestId"] = m["requestId"]
	}
	if m, ok := rst.(map[string]interface{}); ok {
		resp["job"] = m["job"]
	}

	resp["success"] = true
	if httpStatusCode != stdhttp.StatusOK {
		resp["success"] = false
	}
	resp["httpStatusCode"] = httpStatusCode
	resp["code"] = code
	resp["message"] = message

	return int(httpStatusCode), resp
}
