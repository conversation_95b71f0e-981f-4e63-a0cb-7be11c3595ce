package scheduler

import (
	"context"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/jinzhu/copier"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/wrapperspb"
	"mpp/internal/taskmanager/infra/client"
	"mpp/internal/taskmanager/mocks"
	"mpp/internal/taskmanager/models"
	v1 "proto.mpp/api/common/v1"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	schedulerV1 "proto.mpp/api/taskscheduler/v1"

	. "github.com/bytedance/mockey"
)

func TestNewScheduleAdapter(t *testing.T) {
	ctrl := gomock.NewController(t)
	rd := mocks.NewMockDiscovery(ctrl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"l2"}, nil).AnyTimes()
	defer ctrl.Finish()
	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	defer Mock(client.InitHttpClient).Return(conn, nil).Build().UnPatch()
	adapter, cleanup, err := NewScheduleAdapter(context.Background(), rd)
	assert.NotNil(t, adapter)
	assert.NotNil(t, cleanup)
	assert.Nil(t, err)
}

func TestAllocResource_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockScheduleClient := mocks.NewMockScheduleHTTPClient(ctrl)

	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	rd := mocks.NewMockDiscovery(ctrl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"mpp-taskscheduler-l2.http", "mpp-taskscheduler.http"}, nil).AnyTimes()
	defer Mock(client.InitHttpClient).Return(conn, nil).Build().UnPatch()
	defer Mock(schedulerV1.NewScheduleHTTPClient).Return(mockScheduleClient).Build().UnPatch()

	expectedReply := &schedulerV1.AllocResourceReply{
		Result: &v1.CommonReplyResult{
			Code: 200,
		},
		Data: &schedulerV1.AllocResourceReply_Data{
			Resource: &schedulerV1.AllocResourceReply_Resource{
				WorkerIp:          "127.0.0.1",
				WorkerPort:        8080,
				WorkerId:          "worker1",
				WorkerConnectMode: []string{"mode1"},
			},
		},
	}

	mockScheduleClient.EXPECT().AllocResource(gomock.Any(), gomock.Any()).Return(expectedReply, nil).AnyTimes()

	adapter, _, _ := NewScheduleAdapter(context.Background(), rd)
	task := &models.Task{
		JobId:       "job1",
		TaskId:      "task1",
		Product:     "product1",
		EngineModel: "model1",
		Tag:         "tag1",
		RequestResource: &models.RequestResource{
			Quota: map[string]int64{"cpu": 10},
		},
		TaskType: models.TaskType(v1.TaskType_Simple),
		Metadata: map[string]string{"taskSchedulerTag": "l2"},
	}

	worker, err := adapter.AllocResource(context.Background(), task)
	assert.Nil(t, err)
	assert.Equal(t, "127.0.0.1", worker.Ip)
	assert.Equal(t, int32(8080), worker.Port)
	assert.Equal(t, "worker1", worker.Id)

	task = &models.Task{
		JobId:       "job1",
		TaskId:      "task1",
		Product:     "product1",
		EngineModel: "model1",
		Tag:         "tag1",
		RequestResource: &models.RequestResource{
			Quota: map[string]int64{"cpu": 10},
		},
		TaskType: models.TaskType(v1.TaskType_Simple),
	}
	worker, err = adapter.AllocResource(context.Background(), task)
	assert.Nil(t, err)
	assert.Equal(t, "127.0.0.1", worker.Ip)
	assert.Equal(t, int32(8080), worker.Port)
	assert.Equal(t, "worker1", worker.Id)
}

func TestAllocResource_Failure(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockScheduleClient := mocks.NewMockScheduleHTTPClient(ctrl)
	rd := mocks.NewMockDiscovery(ctrl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"mpp-taskscheduler-l2.http"}, nil).Times(1)
	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	defer Mock(client.InitHttpClient).Return(conn, nil).Build().UnPatch()
	defer Mock(schedulerV1.NewScheduleHTTPClient).Return(mockScheduleClient).Build().UnPatch()

	task := &models.Task{
		JobId:       "job1",
		TaskId:      "task1",
		Product:     "product1",
		EngineModel: "model1",
		Tag:         "tag1",
		RequestResource: &models.RequestResource{
			Quota: map[string]int64{"cpu": 10},
			Affinity: &models.TaskAffinity{
				Preferred: []*models.Affinity{
					{
						Key:      "topology1",
						Operator: "In",
						Values:   []string{"value1"},
					},
				},
			},
		},
		TaskType: models.TaskType(v1.TaskType_Simple),
		Metadata: map[string]string{"taskSchedulerTag": "l2"},
	}

	expectedReply := &schedulerV1.AllocResourceReply{
		Result: &v1.CommonReplyResult{
			Code:    500,
			Reason:  "Internal Server Error",
			Message: "Failed to allocate resource",
		},
	}

	mockScheduleClient.EXPECT().AllocResource(gomock.Any(), gomock.Any()).Return(expectedReply, nil).Times(1)

	adapter, _, _ := NewScheduleAdapter(context.Background(), rd)

	worker, err := adapter.AllocResource(context.Background(), task)
	assert.NotNil(t, err)
	assert.Nil(t, worker)
}

func TestFreeResource_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockScheduleClient := mocks.NewMockScheduleHTTPClient(ctrl)

	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	rd := mocks.NewMockDiscovery(ctrl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"mpp-taskscheduler-l2.http"}, nil).Times(1)
	defer Mock(client.InitHttpClient).Return(conn, nil).Build().UnPatch()
	defer Mock(schedulerV1.NewScheduleHTTPClient).Return(mockScheduleClient).Build().UnPatch()

	expectedReply := &schedulerV1.FreeResourceReply{
		Result: &v1.CommonReplyResult{
			Code: 200,
		},
	}

	mockScheduleClient.EXPECT().FreeResource(gomock.Any(), gomock.Any()).Return(expectedReply, nil).Times(1)
	adapter, _, _ := NewScheduleAdapter(context.Background(), rd)
	err := adapter.FreeResource(context.Background(), "taskId", map[string]string{"taskSchedulerTag": "l2"})
	assert.Nil(t, err)
}

func TestFreeResource_Failure(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockScheduleClient := mocks.NewMockScheduleHTTPClient(ctrl)

	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	rd := mocks.NewMockDiscovery(ctrl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"mpp-taskscheduler-l2.http"}, nil).Times(1)
	defer Mock(client.InitHttpClient).Return(conn, nil).Build().UnPatch()
	defer Mock(schedulerV1.NewScheduleHTTPClient).Return(mockScheduleClient).Build().UnPatch()

	expectedReply := &schedulerV1.FreeResourceReply{
		Result: &v1.CommonReplyResult{
			Code:    500,
			Reason:  "Internal Server Error",
			Message: "Failed to free resource",
		},
	}

	mockScheduleClient.EXPECT().FreeResource(gomock.Any(), gomock.Any()).Return(expectedReply, nil).Times(1)
	adapter, _, _ := NewScheduleAdapter(context.Background(), rd)
	err := adapter.FreeResource(context.Background(), "taskId", map[string]string{"taskSchedulerTag": "l2"})
	assert.NotNil(t, err)
}

func TestListAllWorker_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockWorkerClient := mocks.NewMockWorkerHTTPClient(ctrl)

	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	rd := mocks.NewMockDiscovery(ctrl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"mpp-taskscheduler-l2.http"}, nil).Times(1)
	defer Mock(client.InitHttpClient).Return(conn, nil).Build().UnPatch()
	defer Mock(schedulerV1.NewWorkerHTTPClient).Return(mockWorkerClient).Build().UnPatch()

	expectedReply := &schedulerV1.ListWorkerReply{
		Result: &v1.CommonReplyResult{
			Code: 200,
		},
		Data: &schedulerV1.ListWorkerReply_Data{
			Workers: []*schedulerV1.WorkerInfo{
				{
					Ip:          "127.0.0.1",
					Port:        8080,
					WorkerId:    "worker1",
					EngineModel: "model1",
					Tag:         "tag1",
					Product:     "product1",
				},
			},
		},
	}

	mockWorkerClient.EXPECT().ListWorker(gomock.Any(), gomock.Any()).Return(expectedReply, nil).Times(1)
	adapter, _, _ := NewScheduleAdapter(context.Background(), rd)
	workers, err := adapter.ListAllWorker(context.Background(), []string{"l2"})
	assert.Nil(t, err)
	assert.Len(t, workers, 1)
	assert.Equal(t, "127.0.0.1", workers[0].Ip)
	assert.Equal(t, int32(8080), workers[0].Port)
	assert.Equal(t, "worker1", workers[0].Id)
	assert.Equal(t, "model1", workers[0].EngineModel)
	assert.Equal(t, "tag1", workers[0].Tag)
	assert.Equal(t, "product1", workers[0].Product)
}

func TestListAllWorker_Failure(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockWorkerClient := mocks.NewMockWorkerHTTPClient(ctrl)

	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	rd := mocks.NewMockDiscovery(ctrl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"mpp-taskscheduler-l2.http"}, nil).Times(1)
	defer Mock(client.InitHttpClient).Return(conn, nil).Build().UnPatch()
	defer Mock(schedulerV1.NewWorkerHTTPClient).Return(mockWorkerClient).Build().UnPatch()

	expectedReply := &schedulerV1.ListWorkerReply{
		Result: &v1.CommonReplyResult{
			Code:    500,
			Reason:  "Internal Server Error",
			Message: "Failed to list workers",
		},
	}

	mockWorkerClient.EXPECT().ListWorker(gomock.Any(), gomock.Any()).Return(expectedReply, nil).Times(1)
	adapter, _, _ := NewScheduleAdapter(context.Background(), rd)
	workers, err := adapter.ListAllWorker(context.Background(), []string{"l2"})
	assert.NotNil(t, err)
	assert.Len(t, workers, 0)
}

func TestGetWorker_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockWorkerClient := mocks.NewMockWorkerHTTPClient(ctrl)

	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	rd := mocks.NewMockDiscovery(ctrl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"mpp-taskscheduler-l2.http"}, nil).Times(1)
	defer Mock(client.InitHttpClient).Return(conn, nil).Build().UnPatch()
	defer Mock(schedulerV1.NewWorkerHTTPClient).Return(mockWorkerClient).Build().UnPatch()

	expectedReply := &schedulerV1.GetWorkerReply{
		Result: &v1.CommonReplyResult{
			Code: 200,
		},
		Data: &schedulerV1.GetWorkerReply_Data{
			Worker: &schedulerV1.WorkerInfo{
				Ip:       "127.0.0.1",
				Port:     8080,
				WorkerId: "worker1",
			},
		},
	}

	mockWorkerClient.EXPECT().GetWorker(gomock.Any(), gomock.Any()).Return(expectedReply, nil).Times(1)
	adapter, _, _ := NewScheduleAdapter(context.Background(), rd)
	worker, err := adapter.GetWorker(context.Background(), "workerId", map[string]string{"taskSchedulerTag": "l2"})
	assert.Nil(t, err)
	assert.Equal(t, "127.0.0.1", worker.Ip)
	assert.Equal(t, int32(8080), worker.Port)
	assert.Equal(t, "worker1", worker.Id)
}

func TestGetWorker_Failure(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockWorkerClient := mocks.NewMockWorkerHTTPClient(ctrl)

	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	rd := mocks.NewMockDiscovery(ctrl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"mpp-taskscheduler-l2.http"}, nil).Times(1)
	defer Mock(client.InitHttpClient).Return(conn, nil).Build().UnPatch()
	defer Mock(schedulerV1.NewWorkerHTTPClient).Return(mockWorkerClient).Build().UnPatch()

	expectedReply := &schedulerV1.GetWorkerReply{
		Result: &v1.CommonReplyResult{
			Code:    500,
			Reason:  "Internal Server Error",
			Message: "Failed to get worker",
		},
	}

	mockWorkerClient.EXPECT().GetWorker(gomock.Any(), gomock.Any()).Return(expectedReply, nil).Times(1)
	adapter, _, _ := NewScheduleAdapter(context.Background(), rd)
	worker, err := adapter.GetWorker(context.Background(), "workerId", map[string]string{"taskSchedulerTag": "l2"})
	assert.NotNil(t, err)
	assert.Nil(t, worker)
}

func TestListWorker_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockWorkerClient := mocks.NewMockWorkerHTTPClient(ctrl)

	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	rd := mocks.NewMockDiscovery(ctrl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"mpp-taskscheduler-l2.http"}, nil).Times(1)
	defer Mock(client.InitHttpClient).Return(conn, nil).Build().UnPatch()
	defer Mock(schedulerV1.NewWorkerHTTPClient).Return(mockWorkerClient).Build().UnPatch()

	product := "product1"
	modelId := "model1"
	tag := "tag1"

	expectedReply := &schedulerV1.ListWorkerReply{
		Result: &v1.CommonReplyResult{
			Code: 200,
		},
		Data: &schedulerV1.ListWorkerReply_Data{
			Workers: []*schedulerV1.WorkerInfo{
				{
					Ip:       "127.0.0.1",
					Port:     8080,
					WorkerId: "worker1",
				},
			},
		},
	}

	mockWorkerClient.EXPECT().ListWorker(gomock.Any(), gomock.Any()).Return(expectedReply, nil).Times(1)
	adapter, _, _ := NewScheduleAdapter(context.Background(), rd)
	workers, err := adapter.ListWorker(context.Background(), product, modelId, tag, []string{"l2"})
	assert.Nil(t, err)
	assert.Len(t, workers, 1)
	assert.Equal(t, "127.0.0.1", workers[0].Ip)
	assert.Equal(t, int32(8080), workers[0].Port)
	assert.Equal(t, "worker1", workers[0].Id)
}

func TestListWorker_Failure(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockWorkerClient := mocks.NewMockWorkerHTTPClient(ctrl)

	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	rd := mocks.NewMockDiscovery(ctrl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"mpp-taskscheduler-l2.http"}, nil).Times(1)
	defer Mock(client.InitHttpClient).Return(conn, nil).Build().UnPatch()
	defer Mock(schedulerV1.NewWorkerHTTPClient).Return(mockWorkerClient).Build().UnPatch()

	product := "product1"
	modelId := "model1"
	tag := "tag1"

	expectedReply := &schedulerV1.ListWorkerReply{
		Result: &v1.CommonReplyResult{
			Code:    500,
			Reason:  "Internal Server Error",
			Message: "Failed to list workers",
		},
	}

	mockWorkerClient.EXPECT().ListWorker(gomock.Any(), gomock.Any()).Return(expectedReply, nil).Times(1)
	adapter, _, _ := NewScheduleAdapter(context.Background(), rd)
	workers, err := adapter.ListWorker(context.Background(), product, modelId, tag, []string{"l2"})
	assert.NotNil(t, err)
	assert.Len(t, workers, 0)
}

func TestListResource_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockResourceClient := mocks.NewMockResourceHTTPClient(ctrl)
	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	rd := mocks.NewMockDiscovery(ctrl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"mpp-taskscheduler-l2.http"}, nil).Times(1)
	defer Mock(client.InitHttpClient).Return(conn, nil).Build().UnPatch()
	defer Mock(schedulerV1.NewResourceHTTPClient).Return(mockResourceClient).Build().UnPatch()

	expectedReply := &schedulerV1.ListResourceReply{
		Result: &v1.CommonReplyResult{
			Code: 200,
		},
		Data: &schedulerV1.ListResourceReply_Data{
			Resources: []*schedulerV1.ResourceDetail{
				{
					TaskId: "task1",
				},
			},
		},
	}

	mockResourceClient.EXPECT().ListResource(gomock.Any(), gomock.Any()).Return(expectedReply, nil).Times(1)
	adapter, _, _ := NewScheduleAdapter(context.Background(), rd)
	resources, err := adapter.ListResource(context.Background(), []string{"l2"}, false)
	assert.Nil(t, err)
	assert.Len(t, resources, 1)
	assert.Equal(t, "task1", resources[0].TaskId)
}

func TestListResource_Failure(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockResourceClient := mocks.NewMockResourceHTTPClient(ctrl)
	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	rd := mocks.NewMockDiscovery(ctrl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"mpp-taskscheduler-l2.http"}, nil).Times(1)
	defer Mock(client.InitHttpClient).Return(conn, nil).Build().UnPatch()
	defer Mock(schedulerV1.NewResourceHTTPClient).Return(mockResourceClient).Build().UnPatch()
	expectedReply := &schedulerV1.ListResourceReply{
		Result: &v1.CommonReplyResult{
			Code:    500,
			Reason:  "Internal Server Error",
			Message: "Failed to list resources",
		},
	}
	mockResourceClient.EXPECT().ListResource(gomock.Any(), gomock.Any()).Return(expectedReply, nil).Times(1)
	adapter, _, _ := NewScheduleAdapter(context.Background(), rd)
	resources, err := adapter.ListResource(context.Background(), []string{"l2"}, false)
	assert.NotNil(t, err)
	assert.Len(t, resources, 0)
}

func TestGetResource_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockResourceClient := mocks.NewMockResourceHTTPClient(ctrl)
	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	rd := mocks.NewMockDiscovery(ctrl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"mpp-taskscheduler-l2.http"}, nil).Times(1)
	defer Mock(client.InitHttpClient).Return(conn, nil).Build().UnPatch()
	defer Mock(schedulerV1.NewResourceHTTPClient).Return(mockResourceClient).Build().UnPatch()
	expectedReply := &schedulerV1.GetResourceReply{
		Result: &v1.CommonReplyResult{
			Code: 200,
		},
		Data: &schedulerV1.GetResourceReply_Data{
			Resource: &schedulerV1.ResourceDetail{
				TaskId: "task1",
			},
		},
	}
	mockResourceClient.EXPECT().GetResource(gomock.Any(), gomock.Any()).Return(expectedReply, nil).Times(1)
	adapter, _, _ := NewScheduleAdapter(context.Background(), rd)
	resource, err := adapter.GetResource(context.Background(), "taskId", map[string]string{"taskSchedulerTag": "l2"})
	assert.Nil(t, err)
	assert.Equal(t, "task1", resource.TaskId)
}

func TestGetResource_Failure(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockResourceClient := mocks.NewMockResourceHTTPClient(ctrl)
	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	rd := mocks.NewMockDiscovery(ctrl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"mpp-taskscheduler-l2.http"}, nil).Times(1)
	defer Mock(client.InitHttpClient).Return(conn, nil).Build().UnPatch()
	defer Mock(schedulerV1.NewResourceHTTPClient).Return(mockResourceClient).Build().UnPatch()
	expectedReply := &schedulerV1.GetResourceReply{
		Result: &v1.CommonReplyResult{
			Code:    500,
			Reason:  "Internal Server Error",
			Message: "Failed to get resource",
		},
	}
	mockResourceClient.EXPECT().GetResource(gomock.Any(), gomock.Any()).Return(expectedReply, nil).Times(1)
	adapter, _, _ := NewScheduleAdapter(context.Background(), rd)
	resource, err := adapter.GetResource(context.Background(), "taskId", map[string]string{"taskSchedulerTag": "l2"})
	assert.NotNil(t, err)
	assert.Nil(t, resource)
}

func TestGetAllSchedulerTags(t *testing.T) {
	ctrl := gomock.NewController(t)
	rd := mocks.NewMockDiscovery(ctrl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"mpp-taskscheduler.http", "mpp-taskscheduler-l2.http"}, nil).Times(1)
	defer ctrl.Finish()
	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	defer Mock(client.InitHttpClient).Return(conn, nil).Build().UnPatch()
	adapter, _, _ := NewScheduleAdapter(context.Background(), rd)
	tags := adapter.GetAllSchedulerTags()
	assert.Equal(t, 2, len(tags))
	exist := false
	for _, tag := range tags {
		if tag == "l2" {
			exist = true
		}
	}
	assert.True(t, exist)
}

func TestGetAllSchedulerTagsWithError(t *testing.T) {
	ctrl := gomock.NewController(t)
	rd := mocks.NewMockDiscovery(ctrl)
	rd.EXPECT().GetServiceList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"mpp-taskscheduler.http"}, nil).Times(1)
	defer ctrl.Finish()
	conn, _ := http.NewClient(context.Background(), http.WithTimeout(time.Second*3))
	defer Mock(client.InitHttpClient).Return(conn, nil).Build().UnPatch()
	adapter, _, _ := NewScheduleAdapter(context.Background(), rd)
	tags := adapter.GetAllSchedulerTags()
	assert.Equal(t, tags[0], "default")
}

func TestPbAnyConvert(t *testing.T) {
	extend := make(map[string]*anypb.Any)
	stringVal := wrapperspb.String("pull-l3.test.com")
	packedAny, err := anypb.New(stringVal)
	boolAny, err := anypb.New(wrapperspb.Bool(true))
	assert.Nil(t, err)
	extend["domain"] = packedAny
	extend["check"] = boolAny
	var str wrapperspb.StringValue
	err = extend["domain"].UnmarshalTo(&str)
	assert.Nil(t, err)
	assert.Equal(t, "pull-l3.test.com", str.GetValue())
	var resource struct {
		Extend map[string]*anypb.Any `protobuf:"bytes,10,rep,name=extend,proto3" json:"extend,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	}

	resource.Extend = extend
	var resourceExtend struct {
		Extend map[string]interface{} `json:"Extend,omitempty"`
	}
	err = copier.Copy(&resourceExtend, &resource)
	assert.Nil(t, err)
	//t.Logf("%v", resourceExtend.Extend["domain"])
	for k, v := range resourceExtend.Extend {
		anyMsg, ok := v.(*anypb.Any)
		assert.True(t, ok)
		msg, err := anyMsg.UnmarshalNew()
		assert.Nil(t, err)
		t.Logf("%v", msg.ProtoReflect().Descriptor().Name())
		t.Logf("%v", msg)
		switch msg.ProtoReflect().Descriptor().Name() {
		case "StringValue":
			t.Logf("%v", msg.(*wrapperspb.StringValue).GetValue())
		case "BoolValue":
			t.Logf("%v", msg.(*wrapperspb.BoolValue).GetValue())
		}
		resourceExtend.Extend[k] = anyMsg
	}
}
