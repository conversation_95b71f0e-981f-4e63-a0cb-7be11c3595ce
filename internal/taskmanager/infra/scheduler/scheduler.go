package scheduler

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/wrapperspb"

	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/infra/client"
	"mpp/internal/taskmanager/models"
	"mpp/pkg/utils"

	"github.com/cinience/animus/safe"

	"github.com/cinience/animus/registry"
	"github.com/go-kratos/kratos/v2/log"
	v1 "proto.mpp/api/common/v1"
	schedulerV1 "proto.mpp/api/taskscheduler/v1"
)

const (
	BaseServiceName = "mpp-taskscheduler"
	BaseUScheme     = "http"
	DefaultTag      = "default"
)

type Adapter struct {
	taskSchedulerClientMap map[string]*TaskSchedulerClient
	discovery              registry.Discovery
	stopCh                 chan struct{}
}

type TaskSchedulerClient struct {
	workerClient   schedulerV1.WorkerHTTPClient
	resourceClient schedulerV1.ResourceHTTPClient
	scheduleClient schedulerV1.ScheduleHTTPClient
}

func NewScheduleAdapter(ctx context.Context, dis registry.Discovery) (common.ScheduleAdapter, func(), error) {
	adapter := &Adapter{
		taskSchedulerClientMap: make(map[string]*TaskSchedulerClient),
		stopCh:                 make(chan struct{}),
	}
	adapter.RecycleGetSchedulerServices(ctx, dis)
	safe.Go(func() {
		t := time.Tick(5 * time.Second)
		for {
			select {
			case <-t:
				adapter.RecycleGetSchedulerServices(ctx, dis)
			case <-adapter.stopCh:
				return
			}
		}
	})
	return adapter, func() {}, nil
}

func (s *Adapter) RecycleGetSchedulerServices(ctx context.Context, dis registry.Discovery) {
	schedulerServices, err := dis.GetServiceList(ctx, BaseUScheme, BaseServiceName)
	if err != nil {
		log.Errorf("get scheduler services failed, err:%v", err)
		return
	}
	taskSchedulerClientMap := make(map[string]bool)
	for _, serviceName := range schedulerServices {
		taskSchedulerClientMap[serviceName] = true
		if _, ok := s.taskSchedulerClientMap[serviceName]; !ok {
			conn, err := client.InitHttpClient(ctx, dis, serviceName)
			if err != nil {
				log.Errorf("new http conn failed, err:%v", err)
				return
			}
			s.taskSchedulerClientMap[serviceName] = &TaskSchedulerClient{
				workerClient:   schedulerV1.NewWorkerHTTPClient(conn),
				resourceClient: schedulerV1.NewResourceHTTPClient(conn),
				scheduleClient: schedulerV1.NewScheduleHTTPClient(conn),
			}
		}
	}
	for serviceName := range s.taskSchedulerClientMap {
		if _, ok := taskSchedulerClientMap[serviceName]; !ok {
			delete(s.taskSchedulerClientMap, serviceName)
		}
	}
}
func (s *Adapter) AllocResource(ctx context.Context, task *models.Task) (*models.Worker, error) {
	taskSchedulerClient, schedulerTag := s.GetTaskSchedulerClientOnTaskMeta(task.Metadata)
	if taskSchedulerClient == nil {
		log.Errorf("get taskSchedulerClient failed, taskSchedulerTag:%v", schedulerTag)
		return nil, fmt.Errorf("get taskSchedulerClient failed, taskSchedulerTag:%v", schedulerTag)
	}
	request := &schedulerV1.AllocResourceRequest{
		JobId:       task.JobId,
		TaskId:      task.TaskId,
		Product:     task.Product,
		EngineModel: task.EngineModel,
		Tag:         task.Tag,
		Quota:       task.RequestResource.Quota,
		Affinity:    nil,
		RequestId:   utils.GetRequestIdFromContext(ctx),
		TaskType:    v1.TaskType(task.TaskType),
	}
	// 直播转码带入domain  todo 验证是否影响其他业务链路
	extend := make(map[string]*anypb.Any)
	stringVal := wrapperspb.String(task.Domain)
	packedAny, err := anypb.New(stringVal)
	extend["domain"] = packedAny
	request.Extend = extend

	//混跑引擎测试，修改tag
	//if strings.Contains(task.Tag, common.Mixed_Tag_Prefix) {
	//	request.Product = common.Mixed_Product
	//	request.EngineModel = common.Mixed_Engine
	//	request.Tag = common.Mixed_Agent_Tag
	//	log.Infof("alloc resource mix test, update product:%s, engineModel:%s, tag:%s, taskId:%s", request.Product, request.EngineModel, request.Tag, task.TaskId)
	//}
	//todo 混跑引擎资源申请
	mixconfigMap := make(map[string]string)
	if task.Metadata[string(models.MetaDataFieldMixConfig)] != "" {
		//jsonString 转 map
		err := json.Unmarshal([]byte(task.Metadata[string(models.MetaDataFieldMixConfig)]), &mixconfigMap)
		if err != nil {
			log.Errorf("alloc resource mix test, jsonString to map failed, err:%v", err)
		}
		request.IsMixed = true
		request.MixConfig = mixconfigMap
		log.Infof("alloc mixed resource , update product:%s, engineModel:%s, tag:%s, taskId:%s, mixConfig:%+v",
			request.Product, request.EngineModel, request.Tag, task.TaskId, request.MixConfig)
	}

	log.Infof("start alloc resource, taskId:%v, jobId:%v, task:%+v, req:%+v", request.TaskId, request.JobId, task, request)
	//测试用log 添加亲和性
	if task.RequestResource != nil && task.RequestResource.Affinity != nil {
		request.Affinity = models.ToV1TaskAffinity(task.RequestResource.Affinity)
		if request.Affinity.Preferred != nil {
			for i := range request.Affinity.Preferred {
				log.Infof("alloc resource and add request.Affinity.Preferred , taskId:%v, jobId:%v, quota:%+v,affinity.Preferred:%+v",
					request.TaskId, request.JobId, request.Quota, request.Affinity.Preferred[i])
			}
		}
	}
	reply, err := taskSchedulerClient.scheduleClient.AllocResource(ctx, request)
	if err != nil {
		//todo 如果申请混跑引擎失败 && 混跑资源策略为 TryAbandonMixed，则再次尝试申请原有资源
		mixAllocStrategy := mixconfigMap[string(models.MixConfigFiledMixAllocStrategy)]
		if task.IsMixedTask() && mixconfigMap != nil && string(models.TryAbandonMixed) == mixAllocStrategy {
			log.Warnf("alloc mixed resource failed and retry alloc normal resource, taskId:%v, jobId:%v, mixAllocStrategy:%v, task:%+v, req:%+v, err:%v",
				request.TaskId, request.JobId, mixAllocStrategy, task, request, err)
			request.IsMixed = false
			request.MixConfig = nil
			reply, err = taskSchedulerClient.scheduleClient.AllocResource(ctx, request)
			if err != nil {
				log.Errorf("alloc mixed resource failed and retry alloc normal resource failed, taskId:%v, jobId:%v, mixAllocStrategy:%v, task:%+v, req:%+v, err:%v",
					request.TaskId, request.JobId, mixAllocStrategy, task, request, err)
				return nil, err
			}
		} else {
			//普通资源申请失败，直接返回错误结果
			log.Errorf("alloc resource failed, taskId:%v, jobId:%v, quota:%v+,err:%v",
				request.TaskId, request.JobId, request.Quota, err)
			return nil, err
		}

	}
	if reply.Result.Code != 200 {
		log.Errorf("alloc resource failed, taskId:%v, jobId:%v, quota:%v+,reply:%v+",
			request.TaskId, request.JobId, request.Quota, reply)
		return nil, toSchedulerErr(reply.Result)
	}

	log.Infof("alloc resource success, taskId:%v, jobId:%v, quota:%v+,reply:%v+,err:%v",
		request.TaskId, request.JobId, request.Quota, reply, err)

	return &models.Worker{
		Ip:          reply.Data.Resource.WorkerIp,
		Port:        reply.Data.Resource.WorkerPort,
		Id:          reply.Data.Resource.WorkerId,
		ConnectMode: reply.Data.Resource.WorkerConnectMode,
	}, nil
}

func (s *Adapter) FreeResource(ctx context.Context, taskId string, metaData map[string]string) error {
	taskSchedulerClient, schedulerTag := s.GetTaskSchedulerClientOnTaskMeta(metaData)
	if taskSchedulerClient == nil {
		log.Errorf("get taskSchedulerClient failed, taskSchedulerTag:%v", schedulerTag)
		return fmt.Errorf("get taskSchedulerClient failed, taskSchedulerTag:%v", schedulerTag)
	}
	reply, err := taskSchedulerClient.scheduleClient.FreeResource(ctx, &schedulerV1.FreeResourceRequest{
		TaskId:    taskId,
		RequestId: utils.GetRequestIdFromContext(ctx),
	})

	if err != nil {
		log.Infof("free tasker failed, taskId:%v, err:%v", taskId, err)
		return err
	}
	log.Infof("free tasker, taskId:%v, reply:%v+", taskId, reply)

	if reply.Result.Code != 200 {
		return toSchedulerErr(reply.Result)
	}
	return nil
}

func (s *Adapter) ListAllWorker(ctx context.Context, schedulerTags []string) ([]*models.Worker, error) {
	var workers []*models.Worker
	for _, schedulerTag := range schedulerTags {
		taskSchedulerClient, schedulerTagName := s.GetTaskSchedulerClientOnTag(schedulerTag)
		if taskSchedulerClient == nil {
			log.Errorf("get taskSchedulerClient failed, taskSchedulerTag:%v", schedulerTagName)
			return nil, fmt.Errorf("get taskSchedulerClient failed, taskSchedulerTag:%v", schedulerTagName)
		}
		reply, err := taskSchedulerClient.workerClient.ListWorker(ctx, &schedulerV1.ListWorkerRequest{
			RequestId: utils.GetRequestIdFromContext(ctx),
		})
		if err != nil {
			log.Errorf("list worker failed, err:%v", err)
			return nil, err
		}
		if reply.Result.Code != 200 {
			log.Errorf("list worker failed, reply:%v", reply)
			return nil, toSchedulerErr(reply.Result)
		}
		ws := make([]*models.Worker, len(reply.Data.Workers))
		for i, worker := range reply.Data.Workers {
			ws[i] = &models.Worker{
				Ip:          worker.Ip,
				Port:        worker.Port,
				Id:          worker.WorkerId,
				EngineModel: worker.EngineModel,
				Tag:         worker.Tag,
				Product:     worker.Product,
			}
		}
		workers = append(workers, ws...)
	}
	return workers, nil
}

func (s *Adapter) GetWorker(ctx context.Context, workerId string, metaData map[string]string) (*models.Worker, error) {
	taskSchedulerClient, schedulerTag := s.GetTaskSchedulerClientOnTaskMeta(metaData)
	if taskSchedulerClient == nil {
		log.Errorf("get taskSchedulerClient failed, taskSchedulerTag:%v", schedulerTag)
		return nil, fmt.Errorf("get taskSchedulerClient failed, taskSchedulerTag:%v", schedulerTag)
	}
	in := &schedulerV1.GetWorkerRequest{
		RequestId: utils.GetRequestIdFromContext(ctx),
		WorkerId:  workerId,
	}
	reply, err := taskSchedulerClient.workerClient.GetWorker(ctx, in)
	if err != nil {
		log.Errorf("list worker failed, err:%v", err)
		return nil, err
	}

	if reply.Result.Code != 200 {
		log.Errorf("list worker failed, reply:%v", reply)
		return nil, toSchedulerErr(reply.Result)
	}

	if reply.Data.GetWorker() == nil {
		return nil, fmt.Errorf("reply data is nil")
	}

	return &models.Worker{
		Ip:   reply.Data.GetWorker().GetIp(),
		Port: reply.Data.GetWorker().GetPort(),
		Id:   reply.Data.GetWorker().GetWorkerId(),
	}, nil
}

func (s *Adapter) ListWorker(ctx context.Context, product string, modelId string, tag string, schedulerTags []string) ([]*models.Worker, error) {
	var workers []*models.Worker
	for _, schedulerTag := range schedulerTags {
		taskSchedulerClient, schedulerTagName := s.GetTaskSchedulerClientOnTag(schedulerTag)
		if taskSchedulerClient == nil {
			log.Errorf("get taskSchedulerClient failed, taskSchedulerTag:%v", schedulerTagName)
			return nil, fmt.Errorf("get taskSchedulerClient failed, taskSchedulerTag:%v", schedulerTagName)
		}
		in := &schedulerV1.ListWorkerRequest{
			RequestId:   utils.GetRequestIdFromContext(ctx),
			Product:     product,
			EngineModel: modelId,
			Tag:         tag,
			Ips:         nil,
			WorkerIds:   nil,
			Status:      "online",
		}

		reply, err := taskSchedulerClient.workerClient.ListWorker(ctx, in)
		if err != nil {
			log.Errorf("list worker failed, err:%v", err)
			return nil, err
		}

		if reply.Result.Code != 200 {
			log.Errorf("list worker failed, reply:%v", reply)
			return nil, toSchedulerErr(reply.Result)
		}

		ws := make([]*models.Worker, len(reply.Data.Workers))
		for i, worker := range reply.Data.Workers {
			ws[i] = &models.Worker{
				Ip:   worker.Ip,
				Port: worker.Port,
				Id:   worker.WorkerId,
			}
		}
		workers = append(workers, ws...)
	}
	return workers, nil
}

func (s *Adapter) ListResource(ctx context.Context, schedulerTags []string, isLost bool) ([]*models.Resource, error) {
	var resources []*models.Resource
	for _, schedulerTag := range schedulerTags {
		taskSchedulerClient, schedulerTagName := s.GetTaskSchedulerClientOnTag(schedulerTag)
		if taskSchedulerClient == nil {
			log.Errorf("get taskSchedulerClient failed, taskSchedulerTag:%v", schedulerTagName)
			return nil, fmt.Errorf("get taskSchedulerClient failed, taskSchedulerTag:%v", schedulerTagName)
		}
		request := &schedulerV1.ListResourceRequest{
			RequestId: utils.GetRequestIdFromContext(ctx),
		}

		var reply *schedulerV1.ListResourceReply
		var err error
		if !isLost {
			reply, err = taskSchedulerClient.resourceClient.ListResource(ctx, request)
		} else {
			reply, err = taskSchedulerClient.resourceClient.ListLostResource(ctx, request)
		}

		if err != nil {
			log.Errorf("list resource failed, err:%v", err)
			return nil, err
		}
		if reply.Result.Code != 200 {
			return nil, toSchedulerErr(reply.Result)
		}
		var rs []*models.Resource
		for i := range reply.Data.Resources {
			rs = append(rs, &models.Resource{
				JobId:        reply.Data.Resources[i].JobId,
				TaskId:       reply.Data.Resources[i].TaskId,
				WorkerId:     reply.Data.Resources[i].WorkerId,
				TaskType:     models.TaskType(reply.Data.Resources[i].TaskType),
				SchedulerTag: schedulerTag,
			})
		}
		resources = append(resources, rs...)
	}
	return resources, nil
}

func (s *Adapter) GetResource(ctx context.Context, taskId string, metaData map[string]string) (*models.Resource, error) {
	taskSchedulerClient, schedulerTag := s.GetTaskSchedulerClientOnTaskMeta(metaData)
	if taskSchedulerClient == nil {
		log.Errorf("get taskSchedulerClient failed, taskSchedulerTag:%v", schedulerTag)
		return nil, fmt.Errorf("get taskSchedulerClient failed, taskSchedulerTag:%v", schedulerTag)
	}
	request := &schedulerV1.GetResourceRequest{
		RequestId: utils.GetRequestIdFromContext(ctx),
		TaskId:    taskId,
	}
	reply, err := taskSchedulerClient.resourceClient.GetResource(ctx, request)

	if err != nil {
		log.Errorf("get resource failed, taskId:%v, err:%v", taskId, err)
		return nil, err
	}
	if reply.Result.Code != 200 {
		return nil, toSchedulerErr(reply.Result)
	}
	re := regexp.MustCompile("^mpp-taskscheduler-(.*).http")
	var tag string
	if re.MatchString(schedulerTag) {
		ks := strings.Split(schedulerTag, "-")
		tag = strings.Split(ks[len(ks)-1], ".")[0]
	} else {
		tag = DefaultTag
	}
	resource := &models.Resource{
		JobId:        reply.Data.Resource.JobId,
		TaskId:       reply.Data.Resource.TaskId,
		WorkerId:     reply.Data.Resource.WorkerId,
		SchedulerTag: tag,
	}

	return resource, nil
}

func toSchedulerErr(result *v1.CommonReplyResult) *common.ScheduleError {
	if result == nil {
		return nil
	}
	return &common.ScheduleError{
		Code:    int32(result.Code),
		Reason:  result.Reason,
		Message: result.Message,
	}
}

func (s *Adapter) GetTaskSchedulerClientOnTag(tag string) (*TaskSchedulerClient, string) {
	var router string
	if len(tag) == 0 || tag == DefaultTag || tag == "center" {
		router = BaseServiceName + "." + BaseUScheme
	} else {
		router = BaseServiceName + "-" + tag + "." + BaseUScheme
	}
	if taskSchedulerClient, ok := s.taskSchedulerClientMap[router]; ok {
		return taskSchedulerClient, router
	}
	return nil, router
}

func (s *Adapter) GetTaskSchedulerClientOnTaskMeta(metaData map[string]string) (*TaskSchedulerClient, string) {
	var router, tag string
	if metaData == nil {
		if taskSchedulerClient, ok := s.taskSchedulerClientMap[BaseServiceName+"."+BaseUScheme]; ok {
			return taskSchedulerClient, router
		}
		return nil, router
	}
	//if exist use schedulerTag
	if schedulerTag, ok := metaData["taskSchedulerTag"]; ok {
		tag = schedulerTag
	}
	return s.GetTaskSchedulerClientOnTag(tag)
}

func (s *Adapter) GetAllSchedulerTags() []string {
	re := regexp.MustCompile("^mpp-taskscheduler-(.*).http")
	var schedulerTags []string
	for k := range s.taskSchedulerClientMap {
		//check if exist tag
		if re.MatchString(k) {
			ks := strings.Split(k, "-")
			schedulerTags = append(schedulerTags, strings.Split(ks[len(ks)-1], ".")[0])
		} else if k == BaseServiceName+"."+BaseUScheme {
			schedulerTags = append(schedulerTags, DefaultTag)
		}
	}
	return schedulerTags
}

//// parseExtend 解析extend中的domain字段
//func parseExtend(extend map[string]*anypb.Any) (string, error) {
//	if extend == nil {
//		return "", fmt.Errorf("extend is nil")
//	}
//
//	domainAny, ok := extend["domain"]
//	if !ok {
//		return "", fmt.Errorf("domain field not found in extend")
//	}
//
//	var domainValue wrapperspb.StringValue
//	err := domainAny.UnmarshalTo(&domainValue)
//	if err != nil {
//		return "", fmt.Errorf("failed to unmarshal domain value: %v", err)
//	}
//
//	return domainValue.GetValue(), nil
//}
