package workflow

import (
	"context"
	"mpp/pkg/httputils"
	"mpp/pkg/tracer/middleware/tracer"
	"mpp/pkg/utils"
	"time"

	"github.com/cinience/animus/registry"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/http"
	pb "proto.mpp/api/taskmanager/v1"
)

type MppTaskApi interface {
	StartTask(ctx context.Context, request *pb.StartTaskRequest) (*pb.StartTaskReply, error)
	StopTask(ctx context.Context, request *pb.StopTaskRequest) (*pb.StopTaskReply, error)
	CommandTask(ctx context.Context, request *pb.CommandTaskRequest) (*pb.CommandTaskReply, error)
	UpdateTask(ctx context.Context, request *pb.UpdateTaskRequest) (*pb.UpdateTaskReply, error)
}

type MppTaskClient struct {
	client pb.TaskHTTPClient
	log    *log.Helper
}

func NewMppClient(discovery registry.Discovery, logger log.Logger) MppTaskApi {
	taskManagerClient := initTaskManagerClient(discovery)
	cli := &MppTaskClient{
		log:    log.NewHelper(logger),
		client: taskManagerClient,
	}
	return cli
}

func initTaskManagerClient(discovery registry.Discovery) pb.TaskHTTPClient {
	connHTTP, err := InitHttpClientWithDiscovery(context.Background(), discovery)
	if err != nil {
		log.Fatalf("init tasker manager connection failed:%v ", err)
		return nil
	}

	c := pb.NewTaskHTTPClient(connHTTP)
	if err != nil {
		log.Fatalf("init tasker manager client failed:%v ", err)
		return nil
	}

	log.Infof("init tasker manager client success")

	return c
}

func InitHttpClientWithDiscovery(ctx context.Context, dis registry.Discovery) (*http.Client, error) {
	// todo 后续要整合到一起，否则nacos sdk写文件是否有问题
	// TODO 这个名字后续要改成自动的吧
	serviceName := "mpp-taskmanager.http"
	timeout := utils.GetTimeoutFromContext(ctx, time.Second*5)
	return http.NewClient(ctx,
		http.WithTransport(httputils.NewRetryTransportWithDiscovery(dis, serviceName)),
		http.WithTimeout(timeout),
		http.WithMiddleware(
			tracing.Client(),
			tracer.MPPClientTracer(),
			metadata.Client(),
		))
}

func (m MppTaskClient) StartTask(ctx context.Context, request *pb.StartTaskRequest) (*pb.StartTaskReply, error) {
	log.Infof("mpp start success, req:%+v", request)

	request.RequestId = utils.GetRequestIdFromContext(ctx)

	reply, err := m.client.StartTask(ctx, request)
	if err != nil {
		log.Warn("start task error. ", err)
		return nil, err
	}

	return reply, nil
}

func (m MppTaskClient) StopTask(ctx context.Context, request *pb.StopTaskRequest) (*pb.StopTaskReply, error) {
	log.Infof("mpp stop task, req:%+v", request)

	request.RequestId = utils.GetRequestIdFromContext(ctx)

	reply, err := m.client.StopTask(ctx, request)
	if err != nil {
		log.Warn("start task error. ", err)
		return nil, err
	}

	return reply, nil
}

func (m MppTaskClient) CommandTask(ctx context.Context, request *pb.CommandTaskRequest) (*pb.CommandTaskReply, error) {
	log.Infof("mpp command task, req:%+v", request)

	request.RequestId = utils.GetRequestIdFromContext(ctx)

	reply, err := m.client.CommandTask(ctx, request)
	if err != nil {
		log.Warn("start task error. ", err)
		return nil, err
	}

	return reply, nil
}

func (m MppTaskClient) UpdateTask(ctx context.Context, request *pb.UpdateTaskRequest) (*pb.UpdateTaskReply, error) {
	log.Infof("mpp stop task, req:%+v", request)

	request.RequestId = utils.GetRequestIdFromContext(ctx)

	reply, err := m.client.UpdateTask(ctx, request)
	if err != nil {
		log.Warn("start task error. ", err)
		return nil, err
	}

	return reply, nil
}
