package workflow

/**
 * @Author: qinxin
 * @Date: 2025/2/7 17:15
 * @Desc:
 * @Version 1.0
 */

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	pb "proto.mpp/api/taskmanager/v1"
	"reflect"
)

// MockMppTaskApi is a mock of MppTaskApi interface.
type MockMppTaskApi struct {
	ctrl     *gomock.Controller
	recorder *MockMppTaskApiMockRecorder
}

// MockMppTaskApiMockRecorder is the mock recorder for MockMppTaskApi.
type MockMppTaskApiMockRecorder struct {
	mock *MockMppTaskApi
}

// NewMockMppTaskApi creates a new mock instance.
func NewMockMppTaskApi(ctrl *gomock.Controller) *MockMppTaskApi {
	mock := &MockMppTaskApi{ctrl: ctrl}
	mock.recorder = &MockMppTaskApiMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMppTaskApi) EXPECT() *MockMppTaskApiMockRecorder {
	return m.recorder
}

// CommandTask mocks base method.
func (m *MockMppTaskApi) CommandTask(ctx context.Context, request *pb.CommandTaskRequest) (*pb.CommandTaskReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CommandTask", ctx, request)
	ret0, _ := ret[0].(*pb.CommandTaskReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CommandTask indicates an expected call of CommandTask.
func (mr *MockMppTaskApiMockRecorder) CommandTask(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommandTask", reflect.TypeOf((*MockMppTaskApi)(nil).CommandTask), ctx, request)
}

// StartTask mocks base method.
func (m *MockMppTaskApi) StartTask(ctx context.Context, request *pb.StartTaskRequest) (*pb.StartTaskReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartTask", ctx, request)
	ret0, _ := ret[0].(*pb.StartTaskReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartTask indicates an expected call of StartTask.
func (mr *MockMppTaskApiMockRecorder) StartTask(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartTask", reflect.TypeOf((*MockMppTaskApi)(nil).StartTask), ctx, request)
}

// StopTask mocks base method.
func (m *MockMppTaskApi) StopTask(ctx context.Context, request *pb.StopTaskRequest) (*pb.StopTaskReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StopTask", ctx, request)
	ret0, _ := ret[0].(*pb.StopTaskReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StopTask indicates an expected call of StopTask.
func (mr *MockMppTaskApiMockRecorder) StopTask(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopTask", reflect.TypeOf((*MockMppTaskApi)(nil).StopTask), ctx, request)
}

// UpdateTask mocks base method.
func (m *MockMppTaskApi) UpdateTask(ctx context.Context, request *pb.UpdateTaskRequest) (*pb.UpdateTaskReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTask", ctx, request)
	ret0, _ := ret[0].(*pb.UpdateTaskReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTask indicates an expected call of UpdateTask.
func (mr *MockMppTaskApiMockRecorder) UpdateTask(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTask", reflect.TypeOf((*MockMppTaskApi)(nil).UpdateTask), ctx, request)
}
