package workflow

import (
	"context"
	"github.com/cinience/animus/registry"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"

	"github.com/go-kratos/kratos/v2/log"
)

// TODO 工作流接口
type Adapter struct {
	discovery registry.Discovery

	liveTranscode common.WorkflowAdapter
	mpsTranscode  common.WorkflowAdapter
}

func NewWorkflowAdapter(conf *conf.Bootstrap, dis registry.Discovery, client MppTaskApi, logger log.Logger) (common.WorkflowAdapter, func(), error) {
	return &Adapter{
		discovery:     dis,
		liveTranscode: NewLiveTranscodeWorkflow(logger, client, conf.Workflow),
	}, func() {}, nil
}

func (s *Adapter) StartDagJob(ctx context.Context, task *models.Task, dagGraph map[string]interface{}) error {
	return s.GetWorkflow(task).StartDagJob(ctx, task, dagGraph)
}

func (s *Adapter) StopDagJob(ctx context.Context, task *models.Task) error {
	return s.GetWorkflow(task).StopDagJob(ctx, task)
}

func (s *Adapter) CommandDagJob(ctx context.Context, task *models.Task) error {
	return s.GetWorkflow(task).CommandDagJob(ctx, task)
}

func (s *Adapter) UpdateDagJob(ctx context.Context, task *models.Task) error {
	return s.GetWorkflow(task).UpdateDagJob(ctx, task)
}

func (a *Adapter) GetWorkflow(task *models.Task) common.WorkflowAdapter {
	switch task.EngineModel {
	case models.LiveTranscode:
		return a.liveTranscode
	case "mpp-integration-test-engine":
		return a.liveTranscode
	default:
		return a.mpsTranscode
	}
}
