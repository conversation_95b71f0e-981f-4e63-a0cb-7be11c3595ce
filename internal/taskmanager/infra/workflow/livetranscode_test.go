package workflow

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"go.temporal.io/sdk/testsuite"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	pb "proto.mpp/api/taskmanager/v1"
	"testing"
)
import "github.com/golang/mock/gomock"

/**
 * @Author: qinxin
 * @Date: 2025/2/7 16:30
 * @Desc:
 * @Version 1.0
 */

func TestNewLiveTranscodeWorkflow(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockConf := &conf.Workflow{
		Address: "temporal:7233",
	}

	wf := NewLiveTranscodeWorkflow(log.DefaultLogger, nil, mockConf)
	assert.NotNil(t, wf)
	assert.NotNil(t, wf.CommonWorkflow)
	assert.Equal(t, mockConf, wf.conf)
}

// 无法连接到workerflow服务
func TestStartDagJob(t *testing.T) {

	config := &conf.Bootstrap{
		Workflow: &conf.Workflow{
			Address: "127.0.0.1:8080",
		},
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	//mockClient := mock_livetranscode.NewMockMppTaskApi(ctrl)
	//mockClient.EXPECT().StartTask(gomock.Any(), gomock.Any()).Return(&pb.StartTaskResponse{}, nil).AnyTimes()

	//redis := repository.NewRedisClient(config)
	dagWorkflow := NewLiveTranscodeWorkflow(log.DefaultLogger, nil, config.Workflow)

	task := &models.Task{
		UserId:      "user1",
		JobId:       "job1",
		Product:     "product1",
		EngineModel: "model1",
		Tag:         "tag1",
		Metadata:    map[string]string{},
		Param: &models.TaskEngineParam{
			Param: "testParams",
		},
		BizStatus:      1,
		InternalStatus: 1,
	}

	graph := map[string]interface{}{
		"merge": map[string]interface{}{
			"taskId": "mergeTask1",
			"quota":  1.0,
		},
		"transcode": []map[string]interface{}{
			{
				"taskId": "transcodeTask1",
				"quota":  1.0,
			},
		},
	}

	err := dagWorkflow.StartDagJob(context.Background(), task, graph)
	assert.NotNil(t, err)
}

// TestStopDagJob 测试 StopDagJob 方法
func TestStopDagJob(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := NewMockMppTaskApi(ctrl)
	mockClient.EXPECT().StopTask(gomock.Any(), gomock.Any()).Return(&pb.StopTaskReply{}, nil).AnyTimes()

	config := &conf.Workflow{
		Address: "temporal:7233",
	}

	dagWorkflow := NewLiveTranscodeWorkflow(log.DefaultLogger, mockClient, config)
	//result是一个map[string]interface{}
	reust := map[string]interface{}{
		"mergeResult": map[string]interface{}{
			"jobId":  "job1",
			"taskId": "mergeTask1",
		},
		"transcodeResult": []map[string]interface{}{
			{
				"jobId":  "job1",
				"taskId": "transcodeTask1",
			},
		},
	}
	task := &models.Task{
		UserId:         "user1",
		JobId:          "job1",
		Product:        "product1",
		EngineModel:    "model1",
		Tag:            "tag1",
		BizStatus:      1,
		InternalStatus: 1,
		Metadata:       map[string]string{},
		Result: &models.TaskResult{
			Data: reust,
		},
	}

	err := dagWorkflow.StopDagJob(context.Background(), task)
	assert.Nil(t, err)
}

// TestCommandDagJob 测试 CommandDagJob 方法
func TestCommandDagJob(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := NewMockMppTaskApi(ctrl)
	mockClient.EXPECT().CommandTask(gomock.Any(), gomock.Any()).Return(&pb.CommandTaskReply{}, nil).AnyTimes()

	config := &conf.Workflow{
		Address: "temporal:7233",
	}

	dagWorkflow := NewLiveTranscodeWorkflow(log.DefaultLogger, mockClient, config)

	reust := map[string]interface{}{
		"jobId":  "job1",
		"taskId": "task1",
		"mergeResult": map[string]interface{}{
			"jobId":  "job1",
			"taskId": "mergeTask1",
		},
		"transcodeResult": []map[string]interface{}{
			{
				"jobId":  "job1",
				"taskId": "transcodeTask1",
			},
		},
	}
	task := &models.Task{
		UserId:         "user1",
		JobId:          "job1",
		Product:        "product1",
		EngineModel:    "model1",
		Tag:            "tag1",
		BizStatus:      1,
		InternalStatus: 1,
		Metadata:       map[string]string{},
		Result: &models.TaskResult{
			Data: reust,
		},
		Param: &models.TaskEngineParam{
			Param: "testParams",
		},
	}

	err := dagWorkflow.CommandDagJob(context.Background(), task)
	assert.Nil(t, err)
}

// TestUpdateDagJob 测试 UpdateDagJob 方法
func TestUpdateDagJob(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := NewMockMppTaskApi(ctrl)
	mockClient.EXPECT().UpdateTask(gomock.Any(), gomock.Any()).Return(&pb.UpdateTaskReply{}, nil).AnyTimes()

	config := &conf.Workflow{
		Address: "temporal:7233",
	}

	dagWorkflow := NewLiveTranscodeWorkflow(log.DefaultLogger, mockClient, config)

	reust := map[string]interface{}{
		"mergeResult": map[string]interface{}{
			"jobId":  "job1",
			"taskId": "mergeTask1",
		},
		"transcodeResult": []map[string]interface{}{
			{
				"jobId":  "job1",
				"taskId": "transcodeTask1",
			},
		},
	}
	task := &models.Task{
		UserId:         "user1",
		JobId:          "job1",
		Product:        "product1",
		EngineModel:    "model1",
		Tag:            "tag1",
		BizStatus:      1,
		InternalStatus: 1,
		Metadata:       map[string]string{},
		Result: &models.TaskResult{
			Data: reust,
		},
		Param: &models.TaskEngineParam{
			Param: "testParams",
		},
	}

	err := dagWorkflow.UpdateDagJob(context.Background(), task)
	assert.Nil(t, err)
}

func TestLiveMerge(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := NewMockMppTaskApi(ctrl)
	mockClient.EXPECT().StartTask(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	config := &conf.Workflow{
		Address: "temporal:7233",
	}

	dagWorkflow := NewLiveTranscodeWorkflow(log.DefaultLogger, mockClient, config)

	input := LiveParallelTranscodeParam{
		UserId:      "user1",
		JobId:       "job1",
		Product:     "product1",
		EngineModel: "model1",
		Tag:         "tag1",
		Merge: map[string]interface{}{
			"taskId": "mergeTask1",
			"quota":  1.0,
		},
		Transcode: []map[string]interface{}{
			{
				"taskId": "transcodeTask1",
				"quota":  1.0,
			},
		},
	}

	result, err := dagWorkflow.LiveMerge(context.Background(), input)
	assert.Nil(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "job1", result.JobId)
	assert.Equal(t, "mergeTask1", result.TaskId)
	assert.Equal(t, "Success", result.Result.Code)
	assert.Equal(t, "success", result.Result.Message)
}

func TestLiveTranscode(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := NewMockMppTaskApi(ctrl)
	mockClient.EXPECT().StartTask(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	config := &conf.Workflow{
		Address: "temporal:7233",
	}

	dagWorkflow := NewLiveTranscodeWorkflow(log.DefaultLogger, mockClient, config)

	input := LiveParallelTranscodeParam{
		UserId:      "user1",
		JobId:       "job1",
		Product:     "product1",
		EngineModel: "model1",
		Tag:         "tag1",
		Merge: map[string]interface{}{
			"taskId": "mergeTask1",
			"quota":  1.0,
		},
		Transcode: []map[string]interface{}{
			{
				"taskId": "transcodeTask1",
				"quota":  1.0,
			},
		},
	}

	result, err := dagWorkflow.LiveTranscode(context.Background(), input, 0)
	assert.Nil(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "job1", result.JobId)
	assert.Equal(t, "transcodeTask1", result.TaskId)
	assert.Equal(t, "Success", result.Result.Code)
	//assert.Equal(t, nil, result.Result.Message) // Assuming the result is an empty JSON object
}

func TestLiveParallelTranscodeWorkflow(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := NewMockMppTaskApi(ctrl)
	mockClient.EXPECT().StartTask(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	config := &conf.Workflow{
		Address: "temporal:7233",
	}

	dagWorkflow := NewLiveTranscodeWorkflow(log.DefaultLogger, mockClient, config)

	input := LiveParallelTranscodeParam{
		UserId:      "user1",
		JobId:       "job1",
		Product:     "product1",
		EngineModel: "model1",
		Tag:         "tag1",
		Merge: map[string]interface{}{
			"taskId": "mergeTask1",
			"quota":  1.0,
		},
		Transcode: []map[string]interface{}{
			{
				"taskId": "transcodeTask1",
				"quota":  1.0,
			},
		},
	}

	testSuite := &testsuite.WorkflowTestSuite{}
	env := testSuite.NewTestWorkflowEnvironment()
	env.RegisterWorkflow(dagWorkflow.LiveParallelTranscodeWorkflow)
	env.RegisterActivity(dagWorkflow.LiveMerge)
	env.RegisterActivity(dagWorkflow.LiveTranscode)

	env.ExecuteWorkflow(dagWorkflow.LiveParallelTranscodeWorkflow, input)

	assert.True(t, env.IsWorkflowCompleted())
	assert.NoError(t, env.GetWorkflowError())

	var result LiveParallelTranscodeWorkflowResult
	env.GetWorkflowResult(&result)
	assert.NotNil(t, result)
	//assert.Equal(t, "Success", result.Result.Code)
	//assert.Equal(t, "success", result.Result.Message)
	//assert.Equal(t, "job1", result.JobId)
	//assert.Equal(t, "mergeTask1", result.MergeResult.TaskId)
	//assert.Equal(t, "Success", result.MergeResult.Result.Code)
	//assert.Equal(t, "success", result.MergeResult.Result.Message)
	//assert.Len(t, result.TranscodeResult, 1)
	//assert.Equal(t, "job1", result.TranscodeResult[0].JobId)
	//assert.Equal(t, "transcodeTask1", result.TranscodeResult[0].TaskId)
	//assert.Equal(t, "Success", result.TranscodeResult[0].Result.Code)
	//assert.Equal(t, "{}", result.TranscodeResult[0].Result.Message) // Assuming the result is an empty JSON object
}
