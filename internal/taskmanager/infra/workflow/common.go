package workflow

import (
	"context"
	"mpp/internal/taskmanager/models"

	"github.com/go-kratos/kratos/v2/log"
)

// TODO 工作流接口

type CommonWorkflow struct {
	log *log.Helper
}

func (w *CommonWorkflow) StartDagJob(ctx context.Context, task *models.Task, dagGraph map[string]interface{}) error {
	return nil
}

func (w *CommonWorkflow) StopDagJob(ctx context.Context, task *models.Task) error {

	return nil
}

func (w *CommonWorkflow) CommandDagJob(ctx context.Context, task *models.Task) error {

	return nil
}

func (w *CommonWorkflow) UpdateDagJob(ctx context.Context, task *models.Task) error {

	return nil
}
