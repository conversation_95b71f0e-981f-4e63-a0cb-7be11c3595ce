package workflow

import (
	"context"
	"fmt"
	"mpp/internal/agent/pkg/utils"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/tracing/tasker_span"
	"mpp/internal/taskscheduler/biz"
	"mpp/pkg/tracer/tasker_tracing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"go.temporal.io/api/workflowservice/v1"
	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/worker"
	"go.temporal.io/sdk/workflow"
	"google.golang.org/protobuf/types/known/durationpb"
	pb "proto.mpp/api/taskmanager/v1"
)

const LiveParallelTranscodeTaskQueueName = "LIVE-PARALLEL-TRANSCODE"

type LiveParallelTranscodeParam struct {
	UserId      string                   `json:"userId"`
	JobId       string                   `json:"jobId"`
	Product     string                   `json:"product"`
	EngineModel string                   `json:"engineModel"`
	Tag         string                   `json:"tag"`
	Merge       map[string]interface{}   `json:"merge"`
	Transcode   []map[string]interface{} `json:"transcode"`
}

type Result struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

type ActivityResult struct {
	Result Result `json:"result"`
	JobId  string `json:"jobId"`
	TaskId string `json:"taskId"`
}

type LiveParallelTranscodeWorkflowResult struct {
	Result          Result           `json:"result"`
	JobId           string           `json:"jobId"`
	WorkflowId      string           `json:"workflowId"`
	RunId           string           `json:"runId"`
	MergeResult     ActivityResult   `json:"mergeResult"`
	TranscodeResult []ActivityResult `json:"transcodeResult"`
}

type LiveTranscodeWorkflow struct {
	*CommonWorkflow
	client     MppTaskApi
	taskTracer *tasker_tracing.Tracer
	conf       *conf.Workflow
}

func NewLiveTranscodeWorkflow(logger log.Logger, client MppTaskApi, conf *conf.Workflow) *LiveTranscodeWorkflow {
	wf := &LiveTranscodeWorkflow{
		CommonWorkflow: &CommonWorkflow{log: log.NewHelper(logger)},
		client:         client,
		conf:           conf,
	}

	//wf.createNamespace()

	wf.register()

	// 初始化trace
	wf.taskTracer = tasker_tracing.NewTracer(tasker_tracing.WithConsumerSpanKind())

	return wf
}

func (wf *LiveTranscodeWorkflow) StartDagJob(ctx context.Context, task *models.Task, graph map[string]interface{}) error {
	var err error
	ctx, span := wf.taskTracer.Start(ctx, "/LiveTranscodeWorkflow/StartDagJob", task.Metadata)
	defer func() {
		wf.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTaskAndExtendInformation(ctx, "LiveTranscodeWorkflow.StartDagJob", task, nil)
		}, err)
	}()

	//构建输入参数
	input := LiveParallelTranscodeParam{
		UserId:      task.UserId,
		JobId:       task.JobId,
		Product:     task.Product,
		EngineModel: task.EngineModel,
		Tag:         task.Tag,
		Merge:       graph["merge"].(map[string]interface{}),
		Transcode:   graph["transcode"].([]map[string]interface{}),
	}

	//连接Temporal客户端
	c, err := client.Dial(client.Options{HostPort: wf.conf.GetAddress()})
	if err != nil {
		log.Context(ctx).Warnf("Unable to create Temporal client:%v", err)
		return err
	}
	defer c.Close()

	//设置工作流选项
	options := client.StartWorkflowOptions{
		ID:        "live-parallel-transcode",
		TaskQueue: LiveParallelTranscodeTaskQueueName,
	}

	log.Context(ctx).Infof("Starting live parallel transcode. detail:%s", biz.JsonToString(input))
	//启动工作流
	we, err := c.ExecuteWorkflow(ctx, options, wf.LiveParallelTranscodeWorkflow, input)
	if err != nil {
		log.Context(ctx).Warnf("Unable to start the Workflow:%v", err)
		return err
	}

	log.Context(ctx).Infof("jobId:%s WorkflowID: %s RunID: %s", task.JobId, we.GetID(), we.GetRunID())

	//获取工作流结果
	var result LiveParallelTranscodeWorkflowResult
	err = we.Get(context.Background(), &result)
	if err != nil {
		return err
	}
	result.WorkflowId = we.GetID()
	result.RunId = we.GetRunID()
	log.Infof("start dag job[%s] success, result:%v", task.JobId, result)
	task.Result = &models.TaskResult{
		Data: result,
	}

	return nil
}

func (wf *LiveTranscodeWorkflow) StopDagJob(ctx context.Context, task *models.Task) error {
	var err error
	ctx, span := wf.taskTracer.Start(ctx, "/LiveTranscodeWorkflow/StopDagJob", task.Metadata)
	defer func() {
		wf.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTaskAndExtendInformation(ctx, "LiveTranscodeWorkflow.StopDagJob", task, nil)
		}, err)
	}()
	log.Context(ctx).Infof("Stop live parallel transcode. jobId:%s", task.JobId)
	if task.Result == nil || task.Result.Data == nil {
		log.Warn("result is nil. jobId:", task.JobId)
		return fmt.Errorf("result is nil")
	}

	result := LiveParallelTranscodeWorkflowResult{}
	err = utils.JsonMapToStruct(task.Result.Data.(map[string]interface{}), &result)
	if err != nil || result.TranscodeResult == nil {
		log.Warnf("workflow result[%+v] is invalid . jobId:%s", task.Result, task.JobId)
		return fmt.Errorf("workflow result is nil")
	}

	log.Infof("stop dag job[%s] workflow:%+v", task.JobId, result)
	// 先停merge任务，再停转码任务
	// 停止任务
	req := &pb.StopTaskRequest{
		JobId:  task.JobId,
		TaskId: result.MergeResult.TaskId,
	}
	stopResult, err := wf.client.StopTask(ctx, req)
	if err != nil {
		return err
	}
	log.Infof("jobId[%s] merge[%s] stop success, result[%s]...", result.JobId, result.MergeResult.TaskId, stopResult)

	for _, tr := range result.TranscodeResult {
		// 停止任务
		req = &pb.StopTaskRequest{
			JobId:  tr.JobId,
			TaskId: tr.TaskId,
		}
		stopResult, err = wf.client.StopTask(ctx, req)
		if err != nil {
			return err
		}
		log.Infof("jobId[%s] transcode[%s] stop success, result[%s]...", tr.JobId, tr.TaskId, stopResult)
	}

	return nil
}

func (wf *LiveTranscodeWorkflow) CommandDagJob(ctx context.Context, task *models.Task) error {
	var err error
	ctx, span := wf.taskTracer.Start(ctx, "/LiveTranscodeWorkflow/CommandDagJob", task.Metadata)
	defer func() {
		wf.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTaskAndExtendInformation(ctx, "LiveTranscodeWorkflow.CommandDagJob", task, nil)
		}, err)
	}()
	log.Context(ctx).Infof("Command live parallel transcode. jobId:%s", task.JobId)

	if task.Result == nil || task.Result.Data == nil {
		log.Warn("workflow result is nil. jobId:", task.JobId)
		return fmt.Errorf("workflow result is nil")
	}

	result := LiveParallelTranscodeWorkflowResult{}
	err = utils.JsonMapToStruct(task.Result.Data.(map[string]interface{}), &result)
	if err != nil || result.TranscodeResult == nil {
		log.Warnf("workflow result[%+v] is invalid . jobId:%s", task.Result, task.JobId)
		return fmt.Errorf("workflow result is nil")
	}

	// command工作流的任务
	log.Infof("command dag job[%s] tasks:%+v", task.JobId, result)

	// 先command merge任务，再command转码任务
	req := &pb.CommandTaskRequest{
		JobId:        task.JobId,
		TaskId:       result.MergeResult.TaskId,
		EngineParams: task.Param.Param,
	}
	commandResult, err := wf.client.CommandTask(ctx, req)
	if err != nil {
		return err
	}
	log.Infof("jobId[%s] merge[%s] command success, result[%s]...", result.JobId, result.MergeResult.TaskId, commandResult)

	for _, tr := range result.TranscodeResult {
		// command任务
		req = &pb.CommandTaskRequest{
			JobId:        tr.JobId,
			TaskId:       tr.TaskId,
			EngineParams: task.Param.Param,
		}
		commandResult, err = wf.client.CommandTask(ctx, req)
		if err != nil {
			return err
		}
		log.Infof("jobId[%s] transcode[%s] command success, result[%s]...", tr.JobId, tr.TaskId, commandResult)
	}

	return nil
}

func (wf *LiveTranscodeWorkflow) UpdateDagJob(ctx context.Context, task *models.Task) error {

	var err error
	ctx, span := wf.taskTracer.Start(ctx, "/LiveTranscodeWorkflow/UpdateDagJob", task.Metadata)
	defer func() {
		wf.taskTracer.EndWithLogEvent(ctx, span, func() {
			tasker_span.SpanRecordWithTaskAndExtendInformation(ctx, "LiveTranscodeWorkflow.UpdateDagJob", task, nil)
		}, err)
	}()
	log.Context(ctx).Infof("Update live parallel transcode. jobId:%s", task.JobId)

	if task.Result == nil || task.Result.Data == nil {
		log.Warn("result is nil. jobId:", task.JobId)
		return fmt.Errorf("result is nil")
	}

	result := LiveParallelTranscodeWorkflowResult{}
	err = utils.JsonMapToStruct(task.Result.Data.(map[string]interface{}), &result)
	if err != nil || result.TranscodeResult == nil {
		log.Warnf("workflow result[%+v] is invalid . jobId:%s", task.Result, task.JobId)
		return fmt.Errorf("workflow result is nil")
	}

	// 先更新工作流的任务
	log.Infof("update tasks:%+v", result)

	// 先更新merge任务，再更新转码任务
	// 更新任务
	req := &pb.UpdateTaskRequest{
		JobId:        task.JobId,
		TaskId:       result.MergeResult.TaskId,
		EngineParams: task.Param.Param,
	}
	commandResult, err := wf.client.UpdateTask(ctx, req)
	if err != nil {
		return err
	}
	log.Infof("jobId[%s] merge[%s] command success, result[%s]...", result.JobId, result.MergeResult.TaskId, commandResult)

	for _, tr := range result.TranscodeResult {
		// 停止任务
		req = &pb.UpdateTaskRequest{
			JobId:        tr.JobId,
			TaskId:       tr.TaskId,
			EngineParams: task.Param.Param,
		}
		commandResult, err = wf.client.UpdateTask(ctx, req)
		if err != nil {
			return err
		}
		log.Infof("jobId[%s] transcode[%s] command success, result[%s]...", tr.JobId, tr.TaskId, commandResult)
	}

	return nil
}

func (wf *LiveTranscodeWorkflow) createNamespace() {
	c, err := client.Dial(client.Options{HostPort: wf.conf.GetAddress()})
	if err != nil {
		log.Warn("Unable to create Temporal client.", err)
		return
	}
	defer c.Close()

	// 创建一个命名空间请求
	duration := durationpb.Duration{
		Seconds: 86400 * 30 * 365, // 一年
	}
	registerNamespaceRequest := &workflowservice.RegisterNamespaceRequest{
		Namespace:                        "default",
		WorkflowExecutionRetentionPeriod: &duration,
	}

	resp, err := c.WorkflowService().RegisterNamespace(context.Background(), registerNamespaceRequest)
	if err != nil {
		log.Warnf("failed to register namespace: %v", err)
	} else {
		log.Infof("namespace created successfully. resp:%+v", resp)
	}
}

func (wf *LiveTranscodeWorkflow) register() {
	c, err := client.Dial(client.Options{HostPort: wf.conf.GetAddress()})
	if err != nil {
		log.Warn("Unable to create Temporal client	.", err)
		return
	}

	// 并行转码
	w := worker.New(c, LiveParallelTranscodeTaskQueueName, worker.Options{})
	w.RegisterWorkflow(wf.LiveParallelTranscodeWorkflow)
	w.RegisterActivity(wf.LiveMerge)
	w.RegisterActivity(wf.LiveTranscode)

	// Start listening to the Task Queue.
	//err = w.Run(worker.InterruptCh())
	err = w.Start()
	if err != nil {
		log.Error("unable to start worker", err)
	} else {
		wf.log.Infof("workflow start success. ")
	}
}

// LiveMerge 合并节点
func (wf *LiveTranscodeWorkflow) LiveMerge(ctx context.Context, input LiveParallelTranscodeParam) (*ActivityResult, error) {
	if input.Merge == nil {
		log.Warn("merge is nil. jobId:", input.JobId)
		return nil, fmt.Errorf("merge is nil")
	}

	log.Debugf("merge start. jobId:%s", input.JobId)
	if input.Merge["taskId"] == nil {
		log.Warn("taskId is nil. jobId:", input.JobId)
		return nil, fmt.Errorf("taskId is nil")
	}
	taskId := input.Merge["taskId"].(string)

	// 提交转码任务
	if input.Merge["quota"] == nil {
		log.Warnf("quota is nil. jobId:%s, param:%s", input.JobId, input.Merge)
		return nil, fmt.Errorf("quota is nil")
	}
	quota := input.Merge["quota"].(float64)

	// 提交合并
	req := &pb.StartTaskRequest{
		UserId:       input.UserId,
		JobId:        input.JobId,
		TaskId:       taskId,
		Quota:        map[string]int64{"cpu": int64(quota)},
		Product:      input.Product,
		EngineModel:  input.EngineModel,
		Tag:          input.Tag,
		EngineParams: biz.JsonToString(input.Merge),
	}
	result, err := wf.client.StartTask(ctx, req)
	if err != nil {
		return nil, err
	}

	log.Infof("jobId[%s] merge[%s] start success, result[%s]...", input.JobId, taskId, result)

	return &ActivityResult{
		JobId:  input.JobId,
		TaskId: taskId,
		Result: Result{
			Code:    "Success",
			Message: "success",
		},
	}, err
}

// LiveTranscode 转码节点
func (wf *LiveTranscodeWorkflow) LiveTranscode(ctx context.Context, input LiveParallelTranscodeParam, index int) (*ActivityResult, error) {
	if input.Transcode == nil {
		log.Warn("transcode is nil. jobId:", input.JobId)
		return nil, fmt.Errorf("transcode is nil")
	}

	if len(input.Transcode) <= index {
		return nil, fmt.Errorf("transcode index out of range")
	}

	taskId := input.Transcode[index]["taskId"].(string)
	log.Infof("job:%s transcode[%d] startting, taskId:%s...", input.JobId, index, taskId)

	if input.Transcode[index]["quota"] == nil {
		log.Warnf("quota is nil. jobId:%s taskId:%s, param:%s", input.JobId, taskId, input.Transcode[index])
		return nil, fmt.Errorf("quota is nil")
	}
	quota := input.Transcode[index]["quota"].(float64)
	req := &pb.StartTaskRequest{
		UserId:       input.UserId,
		JobId:        input.JobId,
		TaskId:       taskId,
		Quota:        map[string]int64{"cpu": int64(quota)},
		Product:      input.Product,
		EngineModel:  input.EngineModel,
		Tag:          input.Tag,
		EngineParams: biz.JsonToString(input.Transcode[index]),
	}
	result, err := wf.client.StartTask(ctx, req)
	if err != nil {
		return nil, err
	}

	return &ActivityResult{
		JobId:  input.JobId,
		TaskId: taskId,
		Result: Result{
			Code:    "Success",
			Message: biz.JsonToString(result),
		},
	}, nil
}

func (wf *LiveTranscodeWorkflow) LiveParallelTranscodeWorkflow(ctx workflow.Context, input LiveParallelTranscodeParam) (*LiveParallelTranscodeWorkflowResult, error) {
	// RetryPolicy specifies how to automatically handle retries if an Activity fails.
	retryPolicy := &temporal.RetryPolicy{
		InitialInterval:        time.Second,
		BackoffCoefficient:     2.0,
		MaximumInterval:        100 * time.Second,
		MaximumAttempts:        3, // 0 is unlimited retries
		NonRetryableErrorTypes: []string{"InvalidAccountError", "InsufficientFundsError"},
	}

	options := workflow.ActivityOptions{
		// Timeout options specify when to automatically timeout Activity functions.
		StartToCloseTimeout: time.Minute,
		// Optionally provide a customized RetryPolicy.
		// Temporal retries failed Activities by default.
		RetryPolicy: retryPolicy,
		// Optionally provide a custom IDGenerator.
	}

	// Apply the options.
	ctx = workflow.WithActivityOptions(ctx, options)

	workflowResult := LiveParallelTranscodeWorkflowResult{}
	err := workflow.ExecuteActivity(ctx, wf.LiveMerge, input).Get(ctx, &workflowResult.MergeResult)
	if err != nil {
		log.Infof("merge failed. result:%s, err:%+v", workflowResult.MergeResult, err)
		return nil, err
	}

	var ts []workflow.Future
	for i := 0; i < len(input.Transcode); i++ {
		t := workflow.ExecuteActivity(ctx, wf.LiveTranscode, input, i)
		ts = append(ts, t)
		//if !workflow.ExecuteActivity(ctx, LiveTranscode, input, i).IsReady() {
		//	return "", fmt.Errorf("transcode %d failed", i)
		//}
	}

	for _, t := range ts {
		var transcodeResult ActivityResult
		err = t.Get(ctx, &transcodeResult)
		if err != nil {
			log.Warnf("transcode failed. result:%s, err:%+v", transcodeResult, err)
			return nil, err
		}
		workflowResult.TranscodeResult = append(workflowResult.TranscodeResult, transcodeResult)
	}

	return &workflowResult, nil
}
