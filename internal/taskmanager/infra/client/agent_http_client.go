package client

import (
	"context"
	"mpp/pkg/stream/transport"
	"mpp/pkg/tracer/middleware/tracer"
	"mpp/pkg/utils"
	stdhttp "net/http"

	"github.com/cinience/animus/middlewares/timeout"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/http"
)

func InitAgentHttpClient(ctx context.Context, uri string, header stdhttp.Header) (*http.Client, error) {
	// 动态获取 ctx 的 deadline，并计算剩余超时时间
	timeoutDuration := utils.GetTimeoutFromContext(ctx, utils.DefaultDiscoveryTimeout)
	return http.NewClient(
		ctx,
		http.WithEndpoint(uri),
		http.WithBlock(),
		http.WithMiddleware(
			tracing.Client(),
			tracer.MPPClientTracer(),
			metadata.Client(),
			timeout.Client(timeout.WithTimeoutMs(timeoutDuration.Milliseconds())),
		),
		http.WithTimeout(timeoutDuration),
		http.WithTransport(transport.NewHttpExchangeTransport(header)),
	)
}
