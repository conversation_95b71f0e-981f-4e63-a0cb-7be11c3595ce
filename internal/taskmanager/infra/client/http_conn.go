package client

import (
	"context"
	"fmt"
	"mpp/pkg/httputils"
	"mpp/pkg/tracer/middleware/tracer"
	"mpp/pkg/utils"

	"github.com/cinience/animus/registry"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/http"
)

func InitHttpClient(ctx context.Context, dis registry.Discovery, serviceName string) (*http.Client, error) {
	// todo 后续要整合到一起，否则nacos sdk写文件是否有问题
	// TODO 这个名字后续要改成自动的吧
	// 动态获取 ctx 的 deadline，并计算剩余超时时间
	timeout := utils.GetTimeoutFromContext(ctx, utils.DefaultDiscoveryTimeout)
	client, err := http.NewClient(ctx,
		http.WithTransport(httputils.NewRetryTransportWithDiscovery(dis, serviceName)),
		http.WithTimeout(timeout),
		http.WithMiddleware(
			tracing.Client(),
			tracer.MPPClientTracer(),
			metadata.Client(),
		))
	return client, err
}

func InitHttpConnection(ctx context.Context, addr string) (*http.Client, error) {
	connHTTP, err := http.NewClient(
		ctx,
		http.WithEndpoint(fmt.Sprintf("http://%v", addr)),
		http.WithBlock(),
		//arms链路追踪埋点
		http.WithMiddleware(
			tracing.Client(),
			tracer.MPPServerTracer(),
			metadata.Client(),
		),
	)
	if err != nil {
		log.Errorf("init http:%v connection failed:%v ", addr, err)
		return nil, err
	}

	return connHTTP, nil
}
