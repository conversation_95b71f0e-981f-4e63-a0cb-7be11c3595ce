package client

//
//import (
//	"context"
//	"fmt"
//	"time"
//
//	"github.com/go-kratos/kratos/v2/middleware/metadata"
//	"github.com/go-kratos/kratos/v2/middleware/tracing"
//	kgrpc "github.com/go-kratos/kratos/v2/transport/grpc"
//	"google.golang.org/grpc"
//
//	"mpp/internal/taskmanager/conf"
//	"github.com/cinience/animus/contribs/registry/nacos"
//	"github.com/cinience/animus/middlewares/tracer"
//)
//
//func NewGrpcConn(ctx context.Context, conf *conf.Registry) (grpc.ClientConnInterface, func(), error) {
//	url := conf.Endpoint
//	dis := nacos.NewWithURL(conf.Endpoint)
//	if dis == nil {
//		return nil, func() {}, fmt.Errorf("nacos.NewWithURL failed. url:%s", url)
//	}
//
//	endpoint := "discovery:///mpp-taskscheduler.grpc"
//	clientConn, err := kgrpc.DialInsecure(
//		ctx,
//		kgrpc.WithEndpoint(endpoint),
//		//kgrpc.WithDiscovery(dis),
//		kgrpc.WithTimeout(time.Second*5),
//		kgrpc.WithPrintDiscoveryDebugLog(true),
//		//arms链路追踪埋点
//		kgrpc.WithMiddleware(
//			tracing.Client(),
//			tracer.MPPClientTracer(),
//			metadata.Client(),
//		),
//	)
//	if err != nil {
//		return nil, func() {
//		}, err
//	}
//	return clientConn, func() {
//	}, nil
//}
//package client
//
//import (
//	"context"
//	"fmt"
//	"time"
//
//	"github.com/go-kratos/kratos/v2/middleware/metadata"
//	"github.com/go-kratos/kratos/v2/middleware/tracing"
//	kgrpc "github.com/go-kratos/kratos/v2/transport/grpc"
//	"google.golang.org/grpc"
//
//	"mpp/internal/taskmanager/conf"
//	"github.com/cinience/animus/contribs/registry/nacos"
//	"github.com/cinience/animus/middlewares/tracer"
//)
//
//func NewGrpcConn(ctx context.Context, conf *conf.Registry) (grpc.ClientConnInterface, func(), error) {
//	url := conf.Endpoint
//	dis := nacos.NewWithURL(conf.Endpoint)
//	if dis == nil {
//		return nil, func() {}, fmt.Errorf("nacos.NewWithURL failed. url:%s", url)
//	}
//
//	endpoint := "discovery:///mpp-taskscheduler.grpc"
//	clientConn, err := kgrpc.DialInsecure(
//		ctx,
//		kgrpc.WithEndpoint(endpoint),
//		//kgrpc.WithDiscovery(dis),
//		kgrpc.WithTimeout(time.Second*5),
//		kgrpc.WithPrintDiscoveryDebugLog(true),
//		//arms链路追踪埋点
//		kgrpc.WithMiddleware(
//			tracing.Client(),
//			tracer.MPPClientTracer(),
//			metadata.Client(),
//		),
//	)
//	if err != nil {
//		return nil, func() {
//		}, err
//	}
//	return clientConn, func() {
//	}, nil
//}
