package engine

import (
	"context"
	"fmt"
	"mpp/internal/taskmanager/util"
	"mpp/pkg/header"
	"runtime"
	"strings"
	"time"

	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/infra/client"
	"mpp/internal/taskmanager/models"
	"mpp/pkg/tracer/tasker_tracing"
	"mpp/pkg/utils"

	stdhttp "net/http"

	v1 "proto.mpp/api/common/v1"
	workerAgentV1 "proto.mpp/api/workeragent/v1"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
)

type CommonEngine struct {
	notifyAddr string
}

func NewCommonEngine(notifier *conf.Notifier) *CommonEngine {
	return &CommonEngine{
		notifyAddr: notifier.Address,
	}
}

func (c *CommonEngine) Submit(ctx context.Context, task *models.Task, worker *models.Worker) error {
	client, err := c.getClient(ctx, worker)

	if err != nil {
		return err
	}

	var dependencies []*workerAgentV1.Dependency
	if task.Dependencies != nil {
		for i := range task.Dependencies {
			dependencies = append(dependencies, &workerAgentV1.Dependency{
				TaskId: task.Dependencies[i].TaskId,
				Fields: task.Dependencies[i].Fields,
			})
		}
	}

	ext, _ := tasker_tracing.MetadataFromContext(ctx)
	if ext == nil {
		ext = task.Metadata
	}
	request := &workerAgentV1.SubmitTaskRequest{
		Metadata: &workerAgentV1.Metadata{
			WorkerId: worker.Id,
			Ext:      ext,
		},
		RequestId:       utils.GetRequestIdFromContext(ctx),
		TaskId:          task.TaskId,
		JobId:           task.JobId,
		Trace:           task.Trace,
		Quota:           task.RequestResource.Quota,
		EngineParams:    task.Param.Param,
		EngineParamsUrl: task.Param.ParamUrl,
		// Notify:          c.Notify,
		Notify:      task.Notify,
		TaskType:    v1.TaskType(task.TaskType),
		Dependency:  dependencies,
		Product:     task.Product,
		EngineModel: task.EngineModel,
		Tag:         task.Tag,
		UserData:    task.UserData,
	}
	startTime := time.Now()
	response, err := client.SubmitTask(ctx, request)
	c.logCommonEngine(ctx, c.getFunctionName(), util.EventAgentTaskSubmit, startTime, request, response, err)
	if err != nil {
		return err
	}

	if response.Result.Code != 200 {
		return c.convertToErr(int32(response.Result.Code), response.Result.Reason, response.Result.Message)
	}

	return nil
}

func (c *CommonEngine) Cancel(ctx context.Context, task *models.Task, worker *models.Worker) error {
	client, err := c.getClient(ctx, worker)

	if err != nil {
		return err
	}

	ext, _ := tasker_tracing.MetadataFromContext(ctx)
	if ext == nil {
		ext = task.Metadata
	}
	request := &workerAgentV1.CancelTaskRequest{
		Metadata: &workerAgentV1.Metadata{
			WorkerId: worker.Id,
			Ext:      ext,
		},
		RequestId: utils.GetRequestIdFromContext(ctx),
		TaskId:    task.TaskId,
		JobId:     task.JobId,
	}

	startTime := time.Now()
	response, err := client.CancelTask(ctx, request)
	c.logCommonEngine(ctx, c.getFunctionName(), util.EventAgentTaskCancel, startTime, request, response, err)
	if err != nil {
		return err
	}

	if response.Result.Code != 200 {
		return c.convertToErr(int32(response.Result.Code), response.Result.Reason, response.Result.Message)
	}

	return nil
}

func (c *CommonEngine) Start(ctx context.Context, task *models.Task, worker *models.Worker) error {

	client, err := c.getClient(ctx, worker)

	if err != nil {
		return err
	}
	ext := task.Metadata
	if ext == nil {
		ext, _ = tasker_tracing.MetadataFromContext(ctx)
	}
	request := &workerAgentV1.StartTaskRequest{
		Metadata: &workerAgentV1.Metadata{
			WorkerId: worker.Id,
			Ext:      ext,
		},
		RequestId:       utils.GetRequestIdFromContext(ctx),
		TaskId:          task.TaskId,
		JobId:           task.JobId,
		Trace:           task.Trace,
		Quota:           task.RequestResource.Quota,
		EngineParamsUrl: task.Param.ParamUrl,
		EngineParams:    task.Param.Param,
		Notify:          task.Notify,
		TaskType:        v1.TaskType(task.TaskType),
		Product:         task.Product,
		EngineModel:     task.EngineModel,
		Tag:             task.Tag,
		UserData:        task.UserData,
	}

	response, err := client.StartTask(ctx, request)
	if err != nil {
		log.Infof("start tasker failed, taskId:%v, worker:%v, err:%v",
			task.TaskId, worker, err)
		return err
	}
	if response.Result.Code != 200 {
		log.Infof("start tasker failed, taskId:%v, worker:%v, resCode:%v, resMessage:%v",
			task.TaskId, worker, response.Result.Code, response.Result.Message)
		return c.convertToErr(int32(response.Result.Code), response.Result.Reason, response.Result.Message)
	}

	if response != nil && response.Result != nil {
		task.Result = &models.TaskResult{
			Code:    response.Result.Code,
			Message: response.Result.Message,
			Reason:  response.Result.Reason,
			Data:    response.Result.Metadata,
		}
	}
	log.Infof("start tasker success, taskId:%v, worker:%v , request:%+v, response:%+v", task.TaskId, task.Worker, request, response)

	return nil
}

func (c *CommonEngine) Invoke(ctx context.Context, task *models.Task, worker *models.Worker) (*models.SimpleTaskResult, error) {
	client, err := c.getClient(ctx, worker)
	if err != nil {
		return nil, err
	}

	ext, _ := tasker_tracing.MetadataFromContext(ctx)
	if ext == nil {
		ext = task.Metadata
	}
	request := &workerAgentV1.InvokeTaskRequest{
		Metadata: &workerAgentV1.Metadata{
			WorkerId: worker.Id,
			Ext:      ext,
		},
		RequestId:       utils.GetRequestIdFromContext(ctx),
		TaskId:          task.TaskId,
		JobId:           task.JobId,
		Trace:           task.Trace,
		Quota:           task.RequestResource.Quota,
		EngineParams:    task.Param.Param,
		EngineParamsUrl: task.Param.ParamUrl,
		ModelId:         task.EngineModel,
		TaskType:        v1.TaskType(task.TaskType),
		Product:         task.Product,
		EngineModel:     task.EngineModel,
		Tag:             task.Tag,
		EngineEntrance:  task.EngineEntrance,
	}

	startTIme := time.Now()
	response, err := client.InvokeTask(ctx, request)
	c.logCommonEngine(ctx, c.getFunctionName(), util.EventAgentTaskInvoke, startTIme, request, response, err)
	if err != nil {
		return nil, err
	}

	if response.Result.Code != stdhttp.StatusOK {
		log.Errorf("start tasker failed, taskId:%v, worker:%v, resCode:%v, resMessage:%v",
			task.TaskId, worker, response.Result.Code, response.Result.Message)
		return nil, c.convertToErr(int32(response.Result.Code), response.Result.Reason, response.Result.Message)
	}
	log.Infof("invoke tasker success, taskId:%v, worker:%v", task.TaskId, task.Worker)

	return &models.SimpleTaskResult{
		TaskId:  request.TaskId,
		DataUrl: response.Data.DataUrl,
		Data:    []byte(response.Data.Data),
	}, nil
}

func (c *CommonEngine) Stop(ctx context.Context, task *models.Task, worker *models.Worker) error {
	client, err := c.getClient(ctx, worker)

	if err != nil {
		return err
	}

	ext, _ := tasker_tracing.MetadataFromContext(ctx)
	if ext == nil {
		ext = task.Metadata
	}
	request := &workerAgentV1.StopTaskRequest{
		Metadata: &workerAgentV1.Metadata{
			WorkerId: worker.Id,
			Ext:      ext,
		},
		RequestId: utils.GetRequestIdFromContext(ctx),
		TaskId:    task.TaskId,
		JobId:     task.JobId,
	}

	response, err := client.StopTask(ctx, request)

	if err != nil {
		log.Errorf("fail to stop tasker, taskId:%v, worker:%v, err:%v", task.TaskId, worker, err)
		e := errors.FromError(err)
		return &common.EngineError{Code: e.Code, Reason: common.ErrorEngineHttpFailed, Message: err.Error()}
	}

	if response.Result.Code != 200 {
		log.Errorf("fail to stop tasker, taskId:%v, worker:%v, response.Reason:%v,response.Message:%v",
			task.TaskId, worker, response.Result.Reason, response.Result.Message)
		return &common.EngineError{Code: int32(response.Result.Code), Reason: response.Result.Reason, Message: response.Result.Message}
	}
	log.Infof("stop engine tasker success, taskId:%v, worker:%v", task.TaskId, worker)

	return nil
}

func (c *CommonEngine) List(ctx context.Context, worker *models.Worker) ([]models.SimpleTask, error) {
	client, err := c.getClient(ctx, worker)

	if err != nil {
		return nil, err
	}

	ext, _ := tasker_tracing.MetadataFromContext(ctx)
	request := &workerAgentV1.ListTaskRequest{
		Metadata: &workerAgentV1.Metadata{
			WorkerId: worker.Id,
			Ext:      ext,
		},
		RequestId: utils.GetRequestIdFromContext(ctx),
	}
	reply, err := client.ListTask(ctx, request)

	if err != nil {
		log.Errorf("list tasker failed, worker:%+v, err:%v", worker, err)
		return nil, err
	}

	if reply.Result.Code != 200 {
		log.Errorf("list tasker failed, worker:%v, response.Reason:%v,response.Message:%v",
			worker, reply.Result.Reason, reply.Result.Message)
		return nil, c.convertToErr(int32(reply.Result.Code), reply.Result.Reason, reply.Result.Message)
	}

	log.Debugf("list tasker success, worker:%v, tasker len:%v", worker, len(reply.Data.Tasks))

	var tasks []models.SimpleTask
	for _, task := range reply.Data.Tasks {
		tasks = append(tasks, models.SimpleTask{
			TaskId:   task.TaskId,
			ParamMd5: task.ParamMd5,
		})
	}
	return tasks, err
}

// ListByTask 查询时带task信息，目前agent支持按tasktype分类查询
func (c *CommonEngine) ListByTask(ctx context.Context, worker *models.Worker, task *models.Task) ([]models.SimpleTask, error) {
	client, err := c.getClient(ctx, worker)

	if err != nil {
		return nil, err
	}

	ext, _ := tasker_tracing.MetadataFromContext(ctx)
	request := &workerAgentV1.ListTaskRequest{
		Metadata: &workerAgentV1.Metadata{
			WorkerId: worker.Id,
			Ext:      ext,
		},
		RequestId: utils.GetRequestIdFromContext(ctx),
		TaskType:  v1.TaskType(task.TaskType),
	}
	reply, err := client.ListTask(ctx, request)

	if err != nil {
		log.Errorf("ListByTask failed, worker:%+v,taskType:%v, err:%v", worker, task.TaskType, err)
		return nil, err
	}

	if reply.Result.Code != 200 {
		log.Errorf("ListByTask failed, worker:%v, response.Reason:%v,response.Message:%v",
			worker, reply.Result.Reason, reply.Result.Message)
		return nil, c.convertToErr(int32(reply.Result.Code), reply.Result.Reason, reply.Result.Message)
	}

	log.Debugf("ListByTask success, worker:%v, tasker len:%v", worker, len(reply.Data.Tasks))

	var tasks []models.SimpleTask
	for _, task := range reply.Data.Tasks {
		tasks = append(tasks, models.SimpleTask{
			TaskId:   task.TaskId,
			ParamMd5: task.ParamMd5,
		})
	}
	return tasks, err
}

func (c *CommonEngine) Update(ctx context.Context, task *models.Task, worker *models.Worker) error {
	client, err := c.getClient(ctx, worker)

	if err != nil {
		return err
	}

	ext, _ := tasker_tracing.MetadataFromContext(ctx)
	if ext == nil {
		ext = task.Metadata
	}
	request := &workerAgentV1.UpdateTaskRequest{
		Metadata: &workerAgentV1.Metadata{
			WorkerId: worker.Id,
			Ext:      ext,
		},
		JobId:           task.JobId,
		TaskId:          task.TaskId,
		EngineParams:    task.Param.Param,
		EngineParamsUrl: task.Param.ParamUrl,
		RequestId:       utils.GetRequestIdFromContext(ctx),
	}

	reply, err := client.UpdateTask(ctx, request)

	if err != nil {
		log.Errorf("update tasker failed, taskId:%v, worker:%+v, err:%v", task.TaskId,
			worker, err)
		return err
	}
	if reply.Result.Code != 200 {
		log.Errorf("update tasker failed, taskId:%v, worker:%v, response.Reason:%v,response.Message:%v",
			task.TaskId, worker, reply.Result.Reason, reply.Result.Message)
		return c.convertToErr(int32(reply.Result.Code), reply.Result.Reason, reply.Result.Message)
	}
	log.Infof("update tasker success, taskId:%v, param:%v", task.TaskId, task.Param)
	return nil
}

func (c *CommonEngine) Check(ctx context.Context, task *models.Task, worker *models.Worker) error {
	client, err := c.getClient(ctx, worker)
	if err != nil {
		return err
	}
	taskId := task.TaskId
	ext, _ := tasker_tracing.MetadataFromContext(ctx)
	if ext == nil {
		ext = task.Metadata
	}

	requestId := utils.GetRequestIdFromContext(ctx)
	request := &workerAgentV1.CheckTaskRequest{
		RequestId: requestId,
		Metadata: &workerAgentV1.Metadata{
			WorkerId: worker.Id,
			Ext:      ext,
		},
		TaskId: taskId,
	}

	reply, err := client.CheckTask(ctx, request)
	if err != nil {
		log.Errorf("check tasker failed, requestId:%s, taskId:%v, worker:%+v, err:%v", requestId, taskId,
			worker, err)
		return err
	}

	if reply.Result.Code != 200 {
		log.Errorf("check tasker failed, requestId:%s, taskId:%v, worker:%v, response.Reason:%v, response.Message:%v",
			requestId,
			taskId, worker, reply.Result.Reason, reply.Result.Message)
		return c.convertToErr(int32(reply.Result.Code), reply.Result.Reason, reply.Result.Message)
	}

	return nil
}

// Command command接口，不更新引擎参数
func (c *CommonEngine) Command(ctx context.Context, task *models.Task, worker *models.Worker) error {
	client, err := c.getClient(ctx, worker)

	if err != nil {
		return err
	}

	ext, _ := tasker_tracing.MetadataFromContext(ctx)
	if ext == nil {
		ext = task.Metadata
	}
	request := &workerAgentV1.CommandTaskRequest{
		Metadata: &workerAgentV1.Metadata{
			WorkerId: worker.Id,
			Ext:      ext,
		},
		JobId:         task.JobId,
		TaskId:        task.TaskId,
		CommandParams: task.Param.Param,
		RequestId:     utils.GetRequestIdFromContext(ctx),
	}

	reply, err := client.CommandTask(ctx, request)

	if err != nil {
		log.Errorf("command tasker failed, taskId:%v, worker:%+v, err:%v", task.TaskId,
			worker, err)
		return err
	}
	if reply.Result.Code != 200 {
		log.Errorf("command tasker failed, taskId:%v, worker:%v, response.Reason:%v,response.Message:%v",
			task.TaskId, worker, reply.Result.Reason, reply.Result.Message)
		return c.convertToErr(int32(reply.Result.Code), reply.Result.Reason, reply.Result.Message)
	}
	log.Infof("command tasker success, taskId:%v, param:%v", task.TaskId, task.Param)
	return nil
}

func (c *CommonEngine) convertToErr(code int32, reason, message string) error {
	return &common.EngineError{Code: code, Reason: reason, Message: message}
}

func (c *CommonEngine) getClient(ctx context.Context, worker *models.Worker) (workerAgentV1.AgentHTTPClient, error) {
	if worker == nil {
		return nil, fmt.Errorf("engine.getClient param worker is nil")
	}

	const defaultSchema = "http"
	uri := fmt.Sprintf("%s://%s:%d", defaultSchema, worker.Ip, worker.Port)

	customHeader := make(stdhttp.Header)
	customHeader.Set(header.HeaderConnMode, strings.Join(worker.ConnectMode, ","))
	customHeader.Set(header.HeaderSN, worker.Id)

	connHTTP, err := client.InitAgentHttpClient(ctx, uri, customHeader)

	if err != nil {
		log.Warnf("checkOnline worker:%v init connection failed:%v ", worker.Ip, err)
		return nil, err
	}

	client := workerAgentV1.NewAgentHTTPClient(connHTTP)
	return client, nil
}

func (c *CommonEngine) getFunctionName() string {
	pc := make([]uintptr, 1)
	runtime.Callers(2, pc)
	f := runtime.FuncForPC(pc[0])
	return "engine" + f.Name()[strings.LastIndex(f.Name(), "."):]
}

func (c *CommonEngine) logCommonEngine(ctx context.Context, f string, event string, start time.Time, req interface{}, resp interface{}, err error) {
	logLevel := log.LevelInfo
	if err != nil {
		logLevel = log.LevelError
	}
	log.Log(logLevel,
		//"task.id", taskId,
		"event", event,
		"latency", time.Since(start).Milliseconds(),
		"request", req,
		"response", resp,
		"func", f,
		"error", err)
}
