package engine

import (
	"context"
	"fmt"
	stdlog "log"
	"net"
	stdhttp "net/http"
	"strconv"
	"testing"
	"time"

	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	"mpp/pkg/utils"

	"github.com/stretchr/testify/assert"
	v1 "proto.mpp/api/common/v1"
	workerAgentV1 "proto.mpp/api/workeragent/v1"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/transport/http"
)

type TestAgentServer struct {
	workerAgentV1.UnimplementedAgentServer
	ch        chan string
	RequestId string
}

func NewTestAgentServer(ch chan string) *TestAgentServer {
	return &TestAgentServer{ch: ch}
}

var (
	commonEngine = NewCommonEngine(notifier)
)

func (t TestAgentServer) StopTask(ctx context.Context, req *workerAgentV1.StopTaskRequest) (*workerAgentV1.StopTaskReply, error) {
	reply := &workerAgentV1.StopTaskReply{
		Result: &v1.CommonReplyResult{
			Code:     stdhttp.StatusOK,
			Metadata: nil,
			Reason:   stdhttp.StatusText(stdhttp.StatusOK),
			Message:  "foo",
		},
	}
	t.ch <- req.RequestId
	stdlog.Println("StopTask received. requestId:", req.RequestId)
	return reply, nil
}

func TestClient(t *testing.T) {
	httpServer := http.NewServer(
		http.Address(":12001"),
	)

	ch := make(chan string, 1)

	testAgentServer := NewTestAgentServer(ch)

	workerAgentV1.RegisterAgentHTTPServer(httpServer, testAgentServer)

	app := kratos.New(
		kratos.Name("test-agent"),
		kratos.Server(
			httpServer,
		),
	)

	go func() {
		err := app.Run()
		if err != nil {
			t.Errorf("start agent server err:%v", err)
		}
	}()

	time.Sleep(time.Second)
	u, err := httpServer.Endpoint()
	if err != nil {
		t.Errorf("parse Endpoint err:%v", err)
	}

	stdlog.Println("address:", u.String())

	ip, _, err := net.SplitHostPort(u.Host)
	if err != nil {
		t.Errorf("parse port err:%v", err)
	}

	port, err := strconv.ParseInt(u.Port(), 10, 64)
	a := NewAdapter(&conf.Notifier{
		Address: "",
	}, nil, nil)

	if err != nil {
		t.Errorf("parse port err:%v", err)
	}

	worker := &models.Worker{Id: "test-uuid", Port: int32(port), Ip: ip, ConnectMode: nil}

	{
		worker.ConnectMode = []string{"direct", "gw:127.0.0.1:" + u.Port()}
		ctx := utils.ResetRequestId(context.Background())
		err = a.Stop(ctx,
			&models.Task{},
			worker,
		)
		if err != nil {
			t.Errorf("start err:%v", err)
		}

		start := time.Now()
		select {
		case id := <-ch:
			assert.Equal(t, utils.GetRequestIdFromContext(ctx), id)
		case <-time.After(time.Second * 3):
			assert.NoError(t, fmt.Errorf("timeout "))
		}
		fmt.Println("cost :", time.Now().Sub(start))
	}

	{
		worker.ConnectMode = []string{"gw:127.0.0.1:" + u.Port()}
		ctx := utils.ResetRequestId(context.Background())
		err = a.Stop(ctx,
			&models.Task{},
			worker,
		)
		if err != nil {
			t.Errorf("start err:%v", err)
		}
		start := time.Now()
		select {
		case id := <-ch:
			assert.Equal(t, utils.GetRequestIdFromContext(ctx), id)
		case <-time.After(time.Second * 3):
			assert.NoError(t, fmt.Errorf("timeout "))
		}
		fmt.Println("cost :", time.Now().Sub(start))
	}

	{
		worker.ConnectMode = []string{"gw:"}
		ctx := utils.ResetRequestId(context.Background())
		err = a.Stop(ctx,
			&models.Task{},
			worker,
		)
		if err != nil {
			t.Errorf("start err:%v", err)
		}
		start := time.Now()
		select {
		case id := <-ch:
			assert.Equal(t, utils.GetRequestIdFromContext(ctx), id)
		case <-time.After(time.Second * 3):
			assert.NoError(t, fmt.Errorf("timeout "))
		}
		fmt.Println("cost :", time.Now().Sub(start))
	}

	{
		worker.ConnectMode = []string{"gw:127.0.0.1"}
		ctx := utils.ResetRequestId(context.Background())
		err = a.Stop(ctx,
			&models.Task{},
			worker,
		)
		if err != nil {
			t.Errorf("start err:%v", err)
		}

		worker.ConnectMode = []string{"gw:127.0.0.1:3333"}
		newCtx := utils.ResetRequestId(context.Background())
		err = a.Stop(newCtx,
			&models.Task{},
			worker,
		)
		//if err != nil {
		//	t.Errorf("start err:%v", err)
		//}
		start := time.Now()
		select {
		case id := <-ch:
			assert.Equal(t, utils.GetRequestIdFromContext(ctx), id)
		case <-time.After(time.Second * 3):
			assert.NoError(t, fmt.Errorf("timeout "))
		}
		fmt.Println("cost :", time.Now().Sub(start))
	}
}

func TestCommonEngine_Submit(t *testing.T) {

	err := commonEngine.Submit(context.Background(), task, worker)
	assert.NotNil(t, err)
}

func TestCommonEngine_Cancel(t *testing.T) {

	err := commonEngine.Cancel(context.Background(), task, worker)
	assert.NotNil(t, err)
}

func TestCommonEngine_Invoke(t *testing.T) {
	ctx := context.Background()
	var cancel context.CancelFunc
	ctx, cancel = context.WithTimeout(ctx, 5000*time.Millisecond)
	defer cancel()
	dealine, ok := ctx.Deadline()
	if ok {
		fmt.Println("deadline:", dealine)
	} else {
		fmt.Println("no deadline")
	}
	_, err := commonEngine.Invoke(ctx, task, worker)
	assert.NotNil(t, err)
}

func TestCommonEngine_Start(t *testing.T) {

	err := commonEngine.Start(context.Background(), task, worker)
	assert.NotNil(t, err)
}

func TestCommonEngine_Stop(t *testing.T) {

	err := commonEngine.Stop(context.Background(), task, worker)
	assert.NotNil(t, err)
}

func TestCommonEngine_List(t *testing.T) {

	_, err := commonEngine.List(context.Background(), worker)
	assert.NotNil(t, err)
}

func TestCommonEngine_ListByTask(t *testing.T) {

	_, err := commonEngine.ListByTask(context.Background(), worker, task)
	assert.NotNil(t, err)
}

func TestCommonEngine_Update(t *testing.T) {

	err := commonEngine.Update(context.Background(), task, worker)
	assert.NotNil(t, err)
}

func TestCommonEngine_Check(t *testing.T) {

	err := commonEngine.Check(context.Background(), task, worker)
	assert.NotNil(t, err)
}

func TestCommonEngine_Command(t *testing.T) {

	err := commonEngine.Command(context.Background(), task, worker)
	assert.NotNil(t, err)
}
