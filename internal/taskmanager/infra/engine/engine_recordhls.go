package engine

import (
	"bytes"
	"context"
	"crypto/md5"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	"mpp/pkg/utils"

	"github.com/go-kratos/kratos/v2/log"
	jsoniter "github.com/json-iterator/go"
	v1 "proto.mpp/api/common/v1"
)

type RecordHlsEngine struct {
	*CommonEngine
}

func NewRecordHlsEngine(notifier *conf.Notifier) *RecordHlsEngine {
	return &RecordHlsEngine{
		CommonEngine: &CommonEngine{
			notifyAddr: notifier.Address,
		},
	}
}

var json = jsoniter.ConfigCompatibleWithStandardLibrary

const (
	authKey      = "Authorization"
	workerSecret = "liveworker1qaz"
	startPath    = "/task/start"
	stopPath     = "/task/stop"
	commandPath  = "/task/command"
	listPath     = "/job/list"
)

func (r *RecordHlsEngine) Start(ctx context.Context, task *models.Task, worker *models.Worker) error {
	return r.recordHlsSendToWorker(ctx, task, worker, startPath)
}

func (r *RecordHlsEngine) Stop(ctx context.Context, task *models.Task, worker *models.Worker) error {
	return r.recordHlsSendToWorker(ctx, task, worker, stopPath)
}

func (r *RecordHlsEngine) recordHlsSendToWorker(ctx context.Context, task *models.Task, worker *models.Worker, path string) error {
	if worker == nil {
		return fmt.Errorf("RecordHlsSendToWorker failed while worker is nil, engineModel:%s, taskId:%s", task.EngineModel, task.TaskId)
	}
	log.Infof("RecordHlsSendToWorker start, engineModel:%s, path:%s, taskId:%s, worker:%s", task.EngineModel, path, task.TaskId, worker.Ip)
	newCtx := utils.SetRequestId(ctx)

	const defaultSchema = "http"
	uri := fmt.Sprintf("%s://%s", defaultSchema, worker.Ip+path)

	reqURL, err := url.Parse(uri)
	queryParams := url.Values{}
	queryParams.Add("taskId", task.TaskId)
	queryParams.Add("appName", task.EngineModel)
	queryParams.Add("weight", strconv.FormatInt(task.RequestResource.Quota["cpu"]/10, 10))
	queryParams.Add("RequestId", newCtx.Value("requestId").(string))
	reqURL.RawQuery = queryParams.Encode()

	var taskParam = make(map[string]interface{})
	taskParam["params"] = task.Param.Param
	if task.Result != nil && task.Result.Reason == models.MigratedReason {
		taskParam["exceptionRestart"] = true
	}
	taskParamBytes, err := json.Marshal(taskParam)
	if err != nil {
		log.Errorf("construct task param fail, taskParam:%s, err:%v ", task.Param.Param, err)
		return err
	}

	req, err := http.NewRequest("POST", reqURL.String(), bytes.NewReader(taskParamBytes))
	if err != nil {
		log.Errorf("construct http req fail, err:%v", err)
		return err
	}
	req.Header.Set("Content-Type", "application/json; charset=UTF-8")
	req.Header.Set(authKey, generateAuthValue(path))
	req.Close = true

	client := &http.Client{Timeout: time.Second * 5} // 不设定超时时间，则默认30s
	response, err := client.Do(req)
	if err != nil {
		log.Infof("RecordHlsSendToWorker request worker failed,engineModel:%s, path:%s, taskId:%v, worker:%v, err:%v",
			task.EngineModel, path, task.TaskId, worker, err)
		return err
	}

	var bodyByte []byte
	if response.Body != nil {
		defer response.Body.Close()
		bodyByte, err = io.ReadAll(response.Body)
		if err != nil {
			log.Errorf("read body fail, err:%v", err)
			return err
		}
	}
	log.Infof("RecordHlsSendToWorker end, engineModel:%s, taskId:%v, worker:%v, responseBody:%v", task.EngineModel, task.TaskId, worker, string(bodyByte))
	if response.StatusCode != 200 {
		log.Errorf("RecordHlsSendToWorker failed with bad status code, engineModel:%s, path:%s, taskId:%v, worker:%v, statusCode:%v, body:%v",
			task.EngineModel, path, task.TaskId, worker, response.StatusCode, string(bodyByte))
		return fmt.Errorf("RecordHlsSendToWorker failed with status code:%v", response.StatusCode)
	}

	// 返回体的result中有code，值为Success、Rejected、TaskIsStopped等
	js := json.Get(bodyByte)
	code := js.Get("result").Get("code").ToString()
	if code != "Success" {
		if code == "Rejected" {
			return fmt.Errorf("start task failed with worker Rejected, taskId:%v, worker:%v", task.TaskId, worker.Ip)
		} else if code == "TaskIsStopped" {
			log.Warnf("RecordHlsSendToWorker end with code TaskIsStopped, uri:%s, workerIp:%v", path, worker.Ip)
			return nil
		}
		log.Errorf("RecordHlsSendToWorker failed with code not success, engineModel:%s, path:%s, taskId:%v, worker:%v, statusCode:%v, body:%v",
			task.EngineModel, path, task.TaskId, worker.Ip, response.StatusCode, string(bodyByte))
		return fmt.Errorf("start task failed with code:%v", code)
	}

	log.Infof("RecordHlsSendToWorker success, engineModel:%s, path:%s, taskId:%v, worker:%v", task.EngineModel, path, task.TaskId, worker.Ip)
	return nil
}

func (r *RecordHlsEngine) List(ctx context.Context, worker *models.Worker) ([]models.SimpleTask, error) {
	if worker == nil {
		return nil, fmt.Errorf("RecordHlsList failed while worker is nil")
	}
	log.Infof("RecordHlsList start, worker:%v", worker.Ip)
	newCtx := utils.SetRequestId(ctx)
	const defaultSchema = "http"
	uri := fmt.Sprintf("%s://%s/job/list", defaultSchema, worker.Ip)

	reqURL, err := url.Parse(uri)
	queryParams := url.Values{}
	queryParams.Add("RequestId", newCtx.Value("requestId").(string))
	reqURL.RawQuery = queryParams.Encode()

	req, err := http.NewRequest("POST", reqURL.String(), nil)
	if err != nil {
		log.Errorf("construct http req fail, err:%v", err)
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set(authKey, generateAuthValue(listPath))
	req.Close = true

	client := &http.Client{Timeout: time.Second * 5}
	response, err := client.Do(req)
	if err != nil {
		log.Infof("RecordHlsList request worker failed, worker:%v, err:%v", worker, err)
		return nil, err
	}
	defer response.Body.Close()
	var bodyByte []byte
	if response.Body != nil {
		bodyByte, err = io.ReadAll(response.Body)
		if err != nil {
			log.Errorf("read body fail, err:%v", err)
			return nil, err
		}
	}
	log.Infof("RecordHlsList end, worker:%v, responseBody:%v", worker, string(bodyByte))
	if response.StatusCode != 200 {
		log.Errorf("RecordHlsList failed with bad status code, worker:%v, statusCode:%v, body:%v",
			worker, response.StatusCode, string(bodyByte))
		return nil, fmt.Errorf("RecordHlsSendToWorker failed with status code:%v, workerIp:%s", response.StatusCode, worker.Ip)
	}

	js := json.Get(bodyByte)
	code := js.Get("result").Get("code").ToString()
	if code != "Success" {
		log.Errorf("RecordHlsList failed with code not Success, worker:%v, statusCode:%v, body:%v",
			worker.Ip, response.StatusCode, string(bodyByte))
		return nil, fmt.Errorf("list task failed with code:%v", code)
	}
	tasks := js.Get("data").Get("tasks").ToString()
	var taskData []map[string]string
	if err := json.Unmarshal([]byte(tasks), &taskData); err != nil {
		log.Errorf("JSON unmarshalling failed:%v", err)
	}
	var simpleTasks []models.SimpleTask
	for _, task := range taskData {
		simpleTask := models.SimpleTask{
			TaskId:   task["taskId"],
			ParamMd5: task["paramMd5"],
		}
		simpleTasks = append(simpleTasks, simpleTask)
	}
	log.Infof("RecordHlsList success, worker:%v", worker.Ip)
	return simpleTasks, nil
}

// 录制时移不需要按任务类型查询
func (r *RecordHlsEngine) ListByTask(ctx context.Context, worker *models.Worker, task *models.Task) ([]models.SimpleTask, error) {
	if task != nil && task.TaskType != models.TaskType(v1.TaskType_Stream) {
		return nil, nil
	}
	return r.List(ctx, worker)
}

func (r *RecordHlsEngine) Check(ctx context.Context, task *models.Task, worker *models.Worker) error {
	return nil
}

func generateAuthValue(path string) string {
	authTimestamp := time.Now().Add(time.Minute).Unix()
	authRand := int(rand.Float64() * 100)

	rawStr := fmt.Sprintf("%s-%d-%d-%s", path, authTimestamp, authRand, workerSecret)

	key := fmt.Sprintf("%x", md5.Sum([]byte(rawStr)))

	result := fmt.Sprintf("%d-%d-%s", authTimestamp, authRand, key)

	return result
}

func (r *RecordHlsEngine) Command(ctx context.Context, task *models.Task, worker *models.Worker) error {
	// VOD packager
	return r.recordHlsSendToWorker(ctx, task, worker, commandPath)
}
