package engine

import (
	"context"
	"io"
	"net/http"
	"strings"
	"testing"

	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"

	. "github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

var (
	notifier = &conf.Notifier{
		Address: "127.0.0.1:9093",
	}
	engine = NewRecordHlsEngine(notifier)
	task   = &models.Task{
		TaskId: "123",
		RequestResource: &models.RequestResource{
			Quota: map[string]int64{
				"cpu": 10,
			},
		},
		EngineModel: models.LiveRecord,
		Param: &models.TaskEngineParam{
			Param: "{\"url\":\"rtmp://127.0.0.1:1935/live/test\",\"path\":\"/tmp/test.flv\"}",
		},
		TaskType: models.StreamTaskType,
	}
	worker = &models.Worker{
		Id: "123",
		Ip: "127.0.0.1",
	}
)

func TestRecordHlsEngine_Start_NIlWorker(t *testing.T) {
	assert.Equal(t, "RecordHlsSendToWorker failed while worker is nil, engineModel:liverecord, taskId:123", engine.Start(context.Background(), task, nil).Error())
}

func TestRecordHlsEngine_Start_Connection_Error(t *testing.T) {
	err := engine.Start(context.Background(), task, worker)
	assert.Contains(t, err.Error(), "connection refused")
}

func TestRecordHlsEngine_Start_NotStatusOk(t *testing.T) {
	// 返回码非200
	response := &http.Response{
		StatusCode: http.StatusBadRequest,
		Body:       nil,
	}
	mock1 := Mock((*http.Client).Do).Return(response, nil).Build()
	defer mock1.UnPatch()
	err := engine.Start(context.Background(), task, worker)
	assert.Equal(t, "RecordHlsSendToWorker failed with status code:400", err.Error())
}

func TestRecordHlsEngine_Start_Success(t *testing.T) {
	response := &http.Response{
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(strings.NewReader(`{"result":{"code":"Success"}}`)),
	}
	mock2 := Mock((*http.Client).Do).Return(response, nil).Build()
	defer mock2.UnPatch()
	err := engine.Start(context.Background(), task, worker)
	assert.Nil(t, err)
}

func TestRecordHlsEngine_Start_Failed(t *testing.T) {
	{
		// code rejected
		response := &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(`{"result":{"code":"Rejected"}}`)),
		}
		mock1 := Mock((*http.Client).Do).Return(response, nil).Build()
		err := engine.Start(context.Background(), task, worker)
		assert.Equal(t, "start task failed with worker Rejected, taskId:123, worker:127.0.0.1", err.Error())
		mock1.UnPatch()
	}
	{
		// code TaskIsStopped
		response := &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(`{"result":{"code":"TaskIsStopped"}}`)),
		}
		mock2 := Mock((*http.Client).Do).Return(response, nil).Build()
		err := engine.Start(context.Background(), task, worker)
		assert.Nil(t, err)
		mock2.UnPatch()
	}
	{
		// code TaskIsStopped
		response := &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(`{"result":{"code":"OtherCode"}}`)),
		}
		mock3 := Mock((*http.Client).Do).Return(response, nil).Build()

		err := engine.Start(context.Background(), task, worker)
		assert.Equal(t, "start task failed with code:OtherCode", err.Error())
		mock3.UnPatch()
	}
}

func TestRecordHlsEngine_Stop_Failed(t *testing.T) {
	response := &http.Response{
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(strings.NewReader(`{"result":{"code":"Success"}}`)),
	}
	mock2 := Mock((*http.Client).Do).Return(response, nil).Build()
	defer mock2.UnPatch()
	err := engine.Stop(context.Background(), task, worker)
	assert.Nil(t, err)
}

func TestRecordHlsEngine_List_Failed(t *testing.T) {
	{
		_, err := engine.List(context.Background(), nil)
		assert.Equal(t, "RecordHlsList failed while worker is nil", err.Error())
	}
	{
		_, err := engine.List(context.Background(), worker)
		assert.Contains(t, err.Error(), "connection refused")
	}
	{
		response := &http.Response{
			StatusCode: http.StatusBadRequest,
			Body:       io.NopCloser(strings.NewReader(`{"result":{"code":"Success"}}`)),
		}
		mock := Mock((*http.Client).Do).Return(response, nil).Build()
		_, err := engine.List(context.Background(), worker)
		assert.Equal(t, "RecordHlsSendToWorker failed with status code:400, workerIp:127.0.0.1", err.Error())
		mock.UnPatch()
	}
	{
		response := &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(`{"result":{"code":"Failed"}}`)),
		}
		mock := Mock((*http.Client).Do).Return(response, nil).Build()
		_, err := engine.List(context.Background(), worker)
		assert.Equal(t, "list task failed with code:Failed", err.Error())
		mock.UnPatch()
	}
}

func TestRecordHlsEngine_List_Success(t *testing.T) {
	response := &http.Response{
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(strings.NewReader(`{"result":{"code":"Success"},"data":{"tasks":[{"taskId":"id1"},{"taskId":"id2"}]}`)),
	}
	mock := Mock((*http.Client).Do).Return(response, nil).Build()
	defer mock.UnPatch()
	tasks, _ := engine.List(context.Background(), worker)
	assert.Equal(t, 2, len(tasks))
}

func TestRecordHlsEngine_ListByTask_Success(t *testing.T) {
	response := &http.Response{
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(strings.NewReader(`{"result":{"code":"Success"},"data":{"tasks":[{"taskId":"id1"},{"taskId":"id2"}]}`)),
	}
	mock := Mock((*http.Client).Do).Return(response, nil).Build()
	defer mock.UnPatch()
	tasks, _ := engine.ListByTask(context.Background(), worker, task)
	assert.Equal(t, 2, len(tasks))
}

func TestRecordHlsEngine_command_Failed(t *testing.T) {
	{
		// code rejected
		response := &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(`{"result":{"code":"Rejected"}}`)),
		}
		mock1 := Mock((*http.Client).Do).Return(response, nil).Build()
		err := engine.Command(context.Background(), task, worker)
		assert.Equal(t, "start task failed with worker Rejected, taskId:123, worker:127.0.0.1", err.Error())
		mock1.UnPatch()
	}
	{
		// code TaskIsStopped
		response := &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(`{"result":{"code":"TaskIsStopped"}}`)),
		}
		mock2 := Mock((*http.Client).Do).Return(response, nil).Build()
		err := engine.Command(context.Background(), task, worker)
		assert.Nil(t, err)
		mock2.UnPatch()
	}
	{
		// code TaskIsStopped
		response := &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(`{"result":{"code":"OtherCode"}}`)),
		}
		mock3 := Mock((*http.Client).Do).Return(response, nil).Build()

		err := engine.Command(context.Background(), task, worker)
		assert.Equal(t, "start task failed with code:OtherCode", err.Error())
		mock3.UnPatch()
	}
}
