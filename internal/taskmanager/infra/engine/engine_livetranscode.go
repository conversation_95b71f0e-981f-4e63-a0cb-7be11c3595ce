package engine

import (
	"context"
	"crypto/md5"
	"fmt"
	"math/rand"
	"net"
	"strconv"
	"strings"
	"time"

	"mpp/internal/agent/pkg/utils"
	"mpp/internal/taskmanager/biz/global_param_loader"
	"mpp/internal/taskmanager/biz/livetranscode"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	lc "mpp/internal/taskmanager/models/livetranscode"
	"mpp/internal/taskscheduler/biz"
	"mpp/pkg/tracer/middleware/tracer"

	v1 "proto.mpp/api/common/v1"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/http"
)

type LiveTranscodeEngine struct {
	*CommonEngine
	log               *log.Helper
	globalParamLoader *global_param_loader.GlobalParamLoader
}

func NewLiveTranscodeEngine(notifier *conf.Notifier, logger log.Logger, globalParamLoader *global_param_loader.GlobalParamLoader) *LiveTranscodeEngine {
	return &LiveTranscodeEngine{
		CommonEngine: &CommonEngine{
			notifyAddr: notifier.Address,
		},
		log:               log.NewHelper(logger),
		globalParamLoader: globalParamLoader,
	}
}

const (
	TranscodeAuthKey      = "Authorization"
	TranscodeWorkerSecret = "test"

	TranscodeStartPath   = "/startTask"
	TranscodeStopPath    = "/stopTask"
	TranscodeListPath    = "/queryTasks"
	TranscodeUpdatePath  = "/updateTask"
	TranscodeCommandPath = "/sendAgentCommand"

	TranscodeAddPicture     = "/addPicture"
	TranscodeEditPicture    = "/editPicture"
	TranscodeAddText        = "/addText"
	TranscodeEditText       = "/editText"
	TranscodeAddObject      = "/addObject"
	TranscodeEditObject     = "/editObject"
	TranscoderRPCPath       = "/lvfmRPC"
	TranscodeEditTaskSource = "/editTaskSource"
)

func InitHttpConnection(ctx context.Context, addr string) (*http.Client, error) {
	connHTTP, err := http.NewClient(
		ctx,
		http.WithEndpoint(fmt.Sprintf("http://%v", addr)),
		http.WithBlock(),
		//arms链路追踪埋点
		http.WithMiddleware(
			tracing.Client(),
			tracer.MPPClientTracer(),
			metadata.Client(),
		),
	)
	if err != nil {
		log.Errorf("init http:%v connection failed:%v ", addr, err)
		return nil, err
	}

	return connHTTP, nil
}

func (t *LiveTranscodeEngine) Start(ctx context.Context, task *models.Task, worker *models.Worker) error {
	connHTTP, err := InitHttpConnection(ctx, fmt.Sprintf("%v:%v", worker.Ip, worker.Port))
	if err != nil {
		t.log.WithContext(ctx).Errorf("start task[%s] worker[%s] init connection failed:%v ", task.TaskId, worker.Ip, err)
		return err
	}
	defer connHTTP.Close()

	reply := make(map[string]interface{})
	path := fmt.Sprintf("%s?workerUUID=%s&requestId=%s", TranscodeStartPath, worker.Id, utils.GetRequestId(ctx))
	param := biz.StringToMap(task.Param.Param)
	if nil == param {
		log.Warnf("start task[%s] worker[%s] task param is nil, [%s]", task.TaskId, worker.Id, task.Param.Param)
		return fmt.Errorf("param is nil")
	}

	param["taskId"] = getWorkerTaskId(param, task.TaskId, task.Domain)

	err = t.addAliLiveClientNode(task, param, worker.Ip)
	if err != nil {
		t.log.WithContext(ctx).Errorf("start task[%s] worker[%s] addAliLiveClientNode failed:%v ", task.TaskId, worker.Ip, err)
		return err
	}
	//soft模式参数填充
	if extend, ok := task.Metadata["extend"]; ok {
		taskInfo, parseError := biz.ParseToMap(extend)
		if parseError != nil {
			t.log.WithContext(ctx).Errorf("parse extend data failed while start task:%s, error:%v", task.TaskId, parseError)
		}
		if soft, ok := taskInfo["soft"]; ok {
			softTask := soft.(bool)
			if lw, ok := taskInfo["lastWorkerIp"]; ok && softTask {
				param["masterHost"] = lw
				t.log.WithContext(ctx).Infof("start task[%s] worker[%s] task in soft mode with lastWorkerIp is [%s]", task.TaskId, worker.Id, lw)
			}
		}
	}
	err = connHTTP.Invoke(ctx, "POST", path, param, &reply)
	if err != nil {
		t.log.WithContext(ctx).Errorf("worker:%v start task[%s] failed. resp:%v err:%v", worker.Ip, task.TaskId, reply, err)
		return err
	}
	t.log.WithContext(ctx).Infof("livetransworker send to engine success:%v start task[%s],engine reply %v", worker.Ip, task.TaskId, reply)
	return nil
}

func (t *LiveTranscodeEngine) Stop(ctx context.Context, task *models.Task, worker *models.Worker) error {
	connHTTP, err := InitHttpConnection(ctx, fmt.Sprintf("%v:%v", worker.Ip, worker.Port))
	if err != nil {
		t.log.WithContext(ctx).Errorf("stop task[%s] worker[%s] init connection failed:%v ", task.TaskId, worker.Ip, err)
		return err
	}
	defer connHTTP.Close()

	param := biz.StringToMap(task.Param.Param)
	if nil == param {
		log.Warnf("start task[%s] worker[%s] task param is nil, [%s]", task.TaskId, worker.Id, task.Param.Param)
		return fmt.Errorf("param is nil")
	}

	param["taskId"] = getWorkerTaskId(param, task.TaskId, task.Domain)

	request := map[string]interface{}{
		"taskId":     param["taskId"],
		"workerUUID": worker.Id,
	}

	//soft模式参数填充
	if extend, ok := task.Metadata["extend"]; ok {
		taskInfo, parseError := biz.ParseToMap(extend)
		if parseError != nil {
			t.log.WithContext(ctx).Errorf("parse extend data failed while start task:%s, error:%v", task.TaskId, parseError)
		}
		if soft, ok := taskInfo["soft"]; ok {
			softTask := soft.(bool)
			if softTask {
				request["delayDuration"] = 60
				t.log.WithContext(ctx).Infof("stop task[%s] worker[%s] task in soft mode", task.TaskId, worker.Id)
			}
		}
	}

	reply := make(map[string]interface{})
	path := fmt.Sprintf("%s?workerUUID=%s&requestId=%s", TranscodeStopPath, worker.Id, utils.GetRequestId(ctx))
	err = connHTTP.Invoke(ctx, "POST", path, request, &reply)
	if err != nil {
		t.log.WithContext(ctx).Errorf("worker:%v stop task[%s] failed. resp:%v err:%v", worker.Ip, task.TaskId, reply, err)
		return err
	}
	t.log.WithContext(ctx).Infof("livetransworker send to engine success:%v stop task[%s] success ,engine reply [%v]", worker.Ip, task.TaskId, reply)
	return nil
}

func (t *LiveTranscodeEngine) Update(ctx context.Context, task *models.Task, worker *models.Worker) error {
	connHTTP, err := InitHttpConnection(ctx, fmt.Sprintf("%v:%v", worker.Ip, worker.Port))
	if err != nil {
		t.log.WithContext(ctx).Errorf("stop task[%s] worker[%s] init connection failed:%v ", task.TaskId, worker.Ip, err)
		return err
	}
	defer connHTTP.Close()

	param := biz.StringToMap(task.Param.Param)
	if nil == param {
		log.Warnf("start task[%s] worker[%s] task param is nil, [%s]", task.TaskId, worker.Id, task.Param.Param)
		return fmt.Errorf("param is nil")
	}

	param["taskId"] = getWorkerTaskId(param, task.TaskId, task.Domain)

	reply := make(map[string]interface{})
	path := fmt.Sprintf("%s?workerUUID=%s&requestId=%s", TranscodeUpdatePath, worker.Id, utils.GetRequestId(ctx))
	err = connHTTP.Invoke(ctx, "POST", path, param, &reply)
	if err != nil {
		t.log.WithContext(ctx).Errorf("worker:%v start task[%s] failed. resp:%v err:%v", worker.Ip, task.TaskId, reply, err)
		return err
	}
	return nil
}

func (t *LiveTranscodeEngine) Command(ctx context.Context, task *models.Task, worker *models.Worker) error {
	// 水印命令+SendCommand命令
	connHTTP, err := InitHttpConnection(ctx, fmt.Sprintf("%v:%v", worker.Ip, worker.Port))
	if err != nil {
		t.log.WithContext(ctx).Errorf("start task[%s] worker[%s] init connection failed:%v ", task.TaskId, worker.Ip, err)
		return err
	}
	defer connHTTP.Close()

	param := biz.StringToMap(task.Param.Param)
	path := fmt.Sprintf("%s?workerUUID=%s&requestId=%s", param["uri"].(string), worker.Id, utils.GetRequestId(ctx))
	reply := make(map[string]interface{})
	editParamExtras := []string{"pictures", "texts"}
	if _, ok := param["domain"]; ok {
		param["taskId"] = getWorkerTaskId(param, task.TaskId, task.Domain)
		if cmdUri, ok := param["cmdUri"]; ok {
			param["uri"] = cmdUri.(string)
		}
		for _, extra := range editParamExtras {
			if extraStr, ok := param[extra].(string); ok {
				var pictures []map[string]interface{}
				err = json.Unmarshal([]byte(extraStr), &pictures)
				if err != nil {
					t.log.WithContext(ctx).Errorf("start task[%s] worker[%s] %s parse failed:%v ", task.TaskId, worker.Ip, extra, err)
					return err
				}
				param[extra] = pictures
			}
		}
		err = connHTTP.Invoke(ctx, "POST", path, param, &reply)
		//t.log.WithContext(ctx).Infof("livetranscode worker:%v command[%s] task[%s] command param[%v] reply [%v]", worker.Ip, path, task.TaskId, param, reply)
	} else if cmdParam, ok := param["command"]; ok {
		cmd := biz.StringToMap(cmdParam.(string))
		err = connHTTP.Invoke(ctx, "POST", path, cmd, &reply)
		//t.log.WithContext(ctx).Infof("livetranscode worker:%v command[%s] task[%s] command param[%v] reply [%v]", worker.Ip, path, task.TaskId, param, reply)
	} else {
		t.log.WithContext(ctx).Errorf("start task[%s] worker[%s] task param is nil, [%s]", task.TaskId, worker.Id, task.Param.Param)
		return err
	}

	if err != nil {
		t.log.WithContext(ctx).Errorf("worker:%v command[%s] task[%s] failed. resp:%v err:%v", worker.Ip, path, task.TaskId, reply, err)
		return err
	}
	t.log.WithContext(ctx).Infof("livetransworker send to engine success:%v command uri[%s] params[%v] task[%s] success. reply [%v]", worker.Ip, path, param, task.TaskId, reply)
	return nil
}

func (t *LiveTranscodeEngine) List(ctx context.Context, worker *models.Worker) ([]models.SimpleTask, error) {
	connHTTP, err := InitHttpConnection(ctx, fmt.Sprintf("%v:%v", worker.Ip, worker.Port))
	if err != nil {
		t.log.WithContext(ctx).Errorf("list worker[%s] init connection failed:%v ", worker.Ip, err)
		return nil, err
	}
	defer connHTTP.Close()

	reply := make(map[string]interface{})
	err = connHTTP.Invoke(ctx, "GET", TranscodeListPath, nil, &reply)
	if err != nil {
		t.log.WithContext(ctx).Errorf("worker:%v list task failed. resp:%v err:%v", worker.Ip, nil, err)
		return nil, err
	}

	var simpleTasks []models.SimpleTask
	if reply["tasks"] == nil {
		return simpleTasks, nil
	}
	tasks := reply["tasks"].([]interface{})
	for _, t := range tasks {
		task := t.(map[string]interface{})
		if task == nil {
			log.Warnf("list task from worker:%v task nil:%v", worker.Ip, biz.JsonToString(t))
			continue
		}

		if task["taskId"] == nil {
			log.Warnf("list task from worker:%v taskId nil:%v", worker.Ip, biz.JsonToString(t))
			continue
		}

		taskId := task["taskId"].(string)
		if strings.Contains(taskId, "/") {
			taskId = strings.Split(taskId, "/")[1]
		}

		simpleTask := models.SimpleTask{
			TaskId: taskId,
		}
		simpleTasks = append(simpleTasks, simpleTask)
	}
	return simpleTasks, nil
}

func (t *LiveTranscodeEngine) ListByTask(ctx context.Context, worker *models.Worker, task *models.Task) ([]models.SimpleTask, error) {
	if task != nil && task.TaskType != models.TaskType(v1.TaskType_Stream) {
		return nil, nil
	}
	return t.List(ctx, worker)
}

func (t *LiveTranscodeEngine) Check(ctx context.Context, task *models.Task, worker *models.Worker) error {
	return nil
}

func (t *LiveTranscodeEngine) generateAuthValue(path string) string {
	authTimestamp := time.Now().Add(time.Minute).Unix()
	authRand := int(rand.Float64() * 100)

	rawStr := fmt.Sprintf("%s-%d-%d-%s", path, authTimestamp, authRand, workerSecret)

	key := fmt.Sprintf("%x", md5.Sum([]byte(rawStr)))

	result := fmt.Sprintf("%d-%d-%s", authTimestamp, authRand, key)

	return result
}

func (t *LiveTranscodeEngine) addAliLiveClientNode(task *models.Task, taskParams map[string]interface{}, workerIp string) error {
	clientNode := "alilive_clientnode="
	streamsRaw, ok := taskParams["streams"]
	if !ok {
		return fmt.Errorf("streams not found in params")
	}
	streams, ok := streamsRaw.([]interface{})
	if !ok {
		log.Errorf("task params streams %v can not convert to array", streamsRaw)
		return fmt.Errorf("task params streams %v can not convert to array", streamsRaw)
	}
	streamMetas, err := livetranscode.ExtractStreamMeta(task.TaskId, taskParams)
	if err == nil && len(streamMetas) >= 4 {
		for _ = range streams {
			clientNode += fmt.Sprintf("%s_", streamMetas[3])
		}
	}
	//extend, ok := taskParams["extend"].(string)
	//var streamInfoExtend lc.StreamInfoExtend
	//if !ok {
	//	return fmt.Errorf("extend not found in params")
	//}
	//err := json.Unmarshal([]byte(extend), &streamInfoExtend)
	//if err != nil {
	//	return err
	//}
	//TODO 任务参数添加unit字段
	//if len(streamInfoExtend.Unit) != 0 {
	//	taskParams["unit"] = streamInfoExtend.Unit
	//}
	resourcePoolType, ok := task.Metadata["taskSchedulerTag"]
	if !ok {
		//center资源池拼接
		clientNode += fmt.Sprintf("center_%s", workerIp)
	} else if resourcePoolType == lc.PUBLIC || resourcePoolType == lc.L2_CPU_2 || resourcePoolType == lc.L2_CPU || resourcePoolType == lc.CENTER || resourcePoolType == lc.DEFAULT {
		if resourcePoolType == lc.DEFAULT {
			clientNode += fmt.Sprintf("center_%s", workerIp)
		} else {
			clientNode += fmt.Sprintf("%s_%s", resourcePoolType, workerIp)
		}
	} else {
		clientNode += "unknown_" + workerIp
	}
	if !ok {
		clientNode += t.GetNodeName(workerIp, "center") + "_transcode"
	} else {
		if resourcePoolType == lc.DEFAULT {
			clientNode += t.GetNodeName(workerIp, "center") + "_transcode"
		} else {
			clientNode += t.GetNodeName(workerIp, resourcePoolType) + "_transcode"
		}
	}
	streamStr, ok := taskParams["streamSource"]
	if !ok {
		return fmt.Errorf("streamSource not found in params")
	}
	streamSource, ok := streamStr.(string)
	if !ok {
		return fmt.Errorf("streamSource %v can not convert to string", streamStr)
	}
	if !strings.Contains(streamSource, "?") {
		streamSource += "?" + clientNode
	} else {
		streamSource += "&" + clientNode
	}
	taskParams["streamSource"] = streamSource
	return nil
}

func (t *LiveTranscodeEngine) GetNodeName(ip string, resourcePoolType string) string {
	v := t.globalParamLoader.GetInUseSubnetConfig()
	var inUseSubnetConfig lc.InUseSubnetConfig
	err := json.Unmarshal([]byte(v), &inUseSubnetConfig)
	if err != nil {
		return ""
	}
	if len(resourcePoolType) != 0 && (lc.L2_CPU == resourcePoolType || lc.L2_CPU_2 == resourcePoolType) {
		return matchNodeNameByIp(ip, inUseSubnetConfig.L2Subnet)
	}
	return matchNodeNameByIp(ip, inUseSubnetConfig.PublicSubnet)
}

func matchNodeNameByIp(ip string, subnet map[string]string) string {
	if len(subnet) == 0 {
		log.Warnf("InUseSubnetConfig is empty,need config")
		return ""
	}
	inetAddress := net.ParseIP(ip)
	if inetAddress == nil {
		log.Error("Invalid IP address")
		return ""
	}
	for key, ipMask := range subnet {
		parts := strings.Split(ipMask, "/")
		if len(parts) != 2 {
			log.Errorf("Invalid IP mask: %s", ipMask)
			continue
		}
		subnetIp := parts[0]
		prefixLength, err := strconv.Atoi(parts[1])
		if err != nil {
			log.Errorf("Invalid IP mask: %s", ipMask)
			continue
		}
		if isIpInRange(inetAddress, net.ParseIP(subnetIp), prefixLength) {
			return "_" + key
		}
	}
	return ""
}

func isIpInRange(ip net.IP, subnet net.IP, prefixLength int) bool {
	ipBytes := ip.To4()
	subnetBytes := subnet.To4()
	if ipBytes == nil || subnetBytes == nil {
		return false
	}

	if len(ipBytes) != len(subnetBytes) {
		// IPv4 and IPv6 lengths mismatch
		return false
	}
	byteCount := prefixLength / 8
	bitCount := prefixLength % 8
	for i := 0; i < byteCount; i++ {
		if ipBytes[i] != subnetBytes[i] {
			return false
		}
	}
	if bitCount > 0 {
		mask := byte(0xFF << (8 - bitCount))
		return (ipBytes[byteCount] & mask) == (subnetBytes[byteCount] & mask)
	}
	return true
}

// getWorkerTaskId 生成 worker 任务ID
// 将 domain、taskId 和 imsExtra 用 "/" 连接，跳过空值
func getWorkerTaskId(param map[string]interface{}, taskId, taskDomain string) string {
	var parts = []string{
		taskDomain,
		taskId,
	}

	// 安全地获取 imsExtra 并添加到 parts
	if imsExtra, ok := param["imsExtra"].(string); ok && imsExtra != "" {
		parts = append(parts, imsExtra)
	}

	return strings.Join(parts, "/")
}
