package engine

import (
	"context"
	"net"
	"testing"

	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskscheduler/biz"

	"github.com/bytedance/mockey"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/stretchr/testify/assert"
)

var transcodeWorker = models.Worker{Ip: "************", Port: 80, Id: "1234567890"}

var param = map[string]interface{}{
	"http_header_host": "livetransworker.alivecdn.com",
	"parallelSource": []string{
		"rtmp://127.0.0.1/test/stream-111_sd5-0?key=value",
		"rtmp://127.0.0.1/test/stream-111_sd5-1?key=value",
		"rtmp://127.0.0.1/test/stream-111_sd5-2?key=value",
	},
	"quota":                 100,
	"region":                "cn-beijing",
	"streamNum":             1,
	"streamSource":          "rtmp://*******/test/mpp.aliyun.com_stream-111?key=value",
	"streamSourceBitrate":   2441,
	"streamSourceCodec":     "h265",
	"streamSourceFps":       22,
	"streamSourceHeight":    1920,
	"streamSourceType":      "stream",
	"streamSourceWidth":     1088,
	"streamTimestampOffset": 0,
	"imsExtra":              "test",
	"streams": []map[string]interface{}{{
		"aCodec":        "copy",
		"afilter":       "",
		"env":           "pub",
		"ffver":         "ffv5",
		"fps":           "1:30",
		"height":        540,
		"input_bypass":  "-threads 2",
		"liveDictator":  "livedictator-l2-cn-beijing-inner.alivecdn.com",
		"output_bypass": "-cqfactor 25 -annexb 0 -rule adapt_revertv2_540",
		"parallel":      "3-3",
		"rtmpDest":      "rtmp://3.3.3.3/test/stream-111_sd5?key=value",
		"sw2main":       3,
		"type":          "transcode",
		"vCodec":        "librs265",
		"vfilter":       "no_ph_vf[0:v]blendimages=json={}[vfo]",
		"width":         -2,
		"x265_params":   "qpmax=45:all-p-refresh=1:max-merge=5:ref=2:refreshframe-rc=1:cutree-motion-beta=1:rc-lookahead=5:frame-threads=2:numtl=1:info=0:bframes=3:ctb3-fix=1:scenecut=0:open-gop=0:aq-mode=4:aq-strength=1.2:keyint=-1:bitrate=1000:vbv-maxrate=1200:vbv-bufsize=1500 -preset faster -tune ssim -vcopybr 200:1:1600 -copyts -optimize_cts -vadjust_ts '(0.134,0.68):(0.27,1.2):(1,1)' -force_key_frames source -enable_hevcdec_opt 1 -enable_avx2_opt 1 -ignore_subsei_ex 1 -qsd_flush -sei_hold_all -force_avsync -sei_order_by_dts -colorinfo_bypass all -global_setopt scalemaxthreads=8",
	}},
	"taskId":   "mpp.aliyun.com/1234567890",
	"domain":   "mpp.aliyun.com",
	"userData": "{\"lastWorkerIp\":\"************\",\"soft\":true,\"taskId\":\"7ff9595d-8adb-3ac7-af74-8f809754025d\"}",
}

// todo 修正
func TestListTask(t *testing.T) {
	notifier := conf.Notifier{}
	engine := NewLiveTranscodeEngine(&notifier, nil, nil)
	defer mockey.Mock((*http.Client).Invoke).Return(nil).Build().UnPatch()

	tasks, err := engine.List(context.Background(), &transcodeWorker)
	assert.Equal(t, err, nil)
	assert.GreaterOrEqual(t, len(tasks), 0)
}

func TestStartTask(t *testing.T) {
	notifier := conf.Notifier{}
	engine := NewLiveTranscodeEngine(&notifier, log.DefaultLogger, nil)

	task := models.Task{
		TaskId: "1234567890",
		Param: &models.TaskEngineParam{
			Param: biz.JsonToString(param),
		},
		Metadata: map[string]string{"extend": biz.JsonToString(param)},
	}
	defer mockey.Mock((*LiveTranscodeEngine).GetNodeName).Return("fakenode").Build().UnPatch()
	defer mockey.Mock((*http.Client).Invoke).Return(nil).Build().UnPatch()
	err := engine.Start(context.Background(), &task, &transcodeWorker)
	assert.Equal(t, err, nil)
}

func TestStopTask(t *testing.T) {
	notifier := conf.Notifier{}
	engine := NewLiveTranscodeEngine(&notifier, log.DefaultLogger, nil)

	task := models.Task{
		TaskId: "1234567890",
		Param: &models.TaskEngineParam{
			Param: biz.JsonToString(param),
		},
	}
	defer mockey.Mock((*http.Client).Invoke).Return(nil).Build().UnPatch()
	err := engine.Stop(context.Background(), &task, &transcodeWorker)
	assert.Equal(t, err, nil)
}

func TestUpdateTask(t *testing.T) {
	notifier := conf.Notifier{}
	engine := NewLiveTranscodeEngine(&notifier, log.DefaultLogger, nil)
	task := models.Task{
		TaskId: "1234567890",
		Param: &models.TaskEngineParam{
			Param: biz.JsonToString(param),
		},
		Metadata: map[string]string{"extend": biz.JsonToString(param)},
	}
	defer mockey.Mock((*LiveTranscodeEngine).GetNodeName).Return("fakenode").Build().UnPatch()
	defer mockey.Mock((*http.Client).Invoke).Return(nil).Build().UnPatch()
	err := engine.Start(context.Background(), &task, &transcodeWorker)
	assert.Nil(t, err)
}

func TestUpdate(t *testing.T) {
	notifier := conf.Notifier{}
	engine := NewLiveTranscodeEngine(&notifier, log.DefaultLogger, nil)
	task := models.Task{
		TaskId: "1234567890",
		Param: &models.TaskEngineParam{
			Param: biz.JsonToString(param),
		},
		Metadata: map[string]string{"extend": biz.JsonToString(param)},
	}
	defer mockey.Mock((*http.Client).Invoke).Return(nil).Build().UnPatch()
	err := engine.Update(context.Background(), &task, &transcodeWorker)
	assert.Nil(t, err)
}

func TestCommandTask(t *testing.T) {
	notifier := conf.Notifier{}
	engine := NewLiveTranscodeEngine(&notifier, log.DefaultLogger, nil)

	param := map[string]interface{}{
		"workerUUID": "7ff9595d-8adb-3ac7-af74-8f809754025d",
		"taskId":     "mpp.aliyun.com/1234567890",
		"pictures": biz.JsonToString([]map[string]interface{}{
			{
				"animate":           0,
				"name":              "WATERMARK_V_1.0.0.2",
				"normalizedRefMode": 0,
				"normalizedh":       0.06666666666666667,
				"offsetcorner":      3,
				"pictureurl":        "https://dfl-watermark.oss-cn-shanghai.aliyuncs.com/logo/20230223livelogo.png",
				"transparency":      255,
				"visible":           1,
				"xoffset":           0.044444444444444446,
				"yoffset":           0.06666666666666667,
			}}),
		"http_header_host": "livetransworker.alivecdn.com",
		"uri":              "addPicture",
		"domain":           "mpp.aliyun.com",
	}

	//command := map[string]interface{}{
	//	"uri":    "/addPicture",
	//	"domain": "mpp.aliyun.com",
	//	"pictures":    param,
	//}

	task := models.Task{
		TaskId: "1234567890",
		Param: &models.TaskEngineParam{
			Param: biz.JsonToString(param),
		},
	}
	defer mockey.Mock((*http.Client).Invoke).Return(nil).Build().UnPatch()
	err := engine.Command(context.Background(), &task, &transcodeWorker)
	assert.Nil(t, err)
}

func TestListByTask(t *testing.T) {
	notifier := conf.Notifier{}
	engine := NewLiveTranscodeEngine(&notifier, nil, nil)
	defer mockey.Mock((*LiveTranscodeEngine).List).Return(nil, nil).Build().UnPatch()
	workers, err := engine.ListByTask(context.Background(), &transcodeWorker, &models.Task{TaskId: "1234567890"})
	assert.Nil(t, err)
	assert.Nil(t, workers)
}

func TestCheck(t *testing.T) {
	notifier := conf.Notifier{}
	engine := NewLiveTranscodeEngine(&notifier, log.DefaultLogger, nil)
	err := engine.Check(context.Background(), &models.Task{TaskId: "1234567890"}, &transcodeWorker)
	assert.Nil(t, err)
}

func TestGenerateAuthValue(t *testing.T) {
	notifier := conf.Notifier{}
	engine := NewLiveTranscodeEngine(&notifier, log.DefaultLogger, nil)
	authValue := engine.generateAuthValue("1234567890")
	assert.NotEmpty(t, authValue)
}

//func TestGetNodeName(t *testing.T) {
//	notifier := conf.Notifier{}
//	defer mockey.Mock((*global_param_loader.GlobalParamLoader).GetInUseSubnetConfig).
//		Return("{\"l2Subnet\":{\"subnet1\":\"************/24\",\"subnet2\":\"***********/24\"},\"publicSubnet\":{\"subnet3\":\"***********/24\"}}").
//		Build().UnPatch()
//	engine := NewLiveTranscodeEngine(&notifier, nil, &global_param_loader.GlobalParamLoader{})
//	nodeName := engine.GetNodeName("************", "l2")
//	assert.Equal(t, nodeName, "_subnet1")
//}

func TestIsIprange(t *testing.T) {
	assert.Equal(t, isIpInRange(net.ParseIP("************"), net.ParseIP("************"), 24), true)
}

func TestGetWorkerTaskId(t *testing.T) {
	assert.Equal(t, getWorkerTaskId(param, "1234567890", "mpp.aliyun.com"), "mpp.aliyun.com/1234567890/test")
}
