package engine

import (
	"context"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/biz/global_param_loader"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"

	"github.com/go-kratos/kratos/v2/log"
)

type Adapter struct {
	commonEngine        common.EngineAdapter
	recordHlsEngine     common.EngineAdapter
	liveTranscodeEngine common.EngineAdapter
}

func NewAdapter(notifier *conf.Notifier, logger log.Logger, globalParamLoader *global_param_loader.GlobalParamLoader) common.EngineAdapter {
	return &Adapter{
		commonEngine:        NewCommonEngine(notifier),
		recordHlsEngine:     NewRecordHlsEngine(notifier),
		liveTranscodeEngine: NewLiveTranscodeEngine(notifier, logger, globalParamLoader),
	}
}

func (a *Adapter) Submit(ctx context.Context, task *models.Task, worker *models.Worker) error {
	return a.GetEngine(task).Submit(ctx, task, worker)
}

func (a *Adapter) Cancel(ctx context.Context, task *models.Task, worker *models.Worker) error {
	return a.GetEngine(task).Cancel(ctx, task, worker)
}

func (a *Adapter) Start(ctx context.Context, task *models.Task, worker *models.Worker) error {
	return a.GetEngine(task).Start(ctx, task, worker)
}

func (a *Adapter) Invoke(ctx context.Context, task *models.Task, worker *models.Worker) (*models.SimpleTaskResult, error) {
	return a.GetEngine(task).Invoke(ctx, task, worker)
}

func (a *Adapter) Stop(ctx context.Context, task *models.Task, worker *models.Worker) error {
	return a.GetEngine(task).Stop(ctx, task, worker)
}

func (a *Adapter) Command(ctx context.Context, task *models.Task, worker *models.Worker) error {
	return a.GetEngine(task).Command(ctx, task, worker)
}

func (a *Adapter) List(ctx context.Context, worker *models.Worker) ([]models.SimpleTask, error) {
	return a.GetEngineByWorker(worker).List(ctx, worker)
}

func (a *Adapter) ListByTask(ctx context.Context, worker *models.Worker, task *models.Task) ([]models.SimpleTask, error) {
	return a.GetEngineByWorker(worker).ListByTask(ctx, worker, task)
}

func (a *Adapter) Update(ctx context.Context, task *models.Task, worker *models.Worker) error {
	return a.GetEngine(task).Update(ctx, task, worker)
}

func (a *Adapter) Check(ctx context.Context, task *models.Task, worker *models.Worker) error {
	return a.GetEngine(task).Check(ctx, task, worker)
}

func (a *Adapter) GetEngine(task *models.Task) common.EngineAdapter {
	switch task.EngineModel {
	case models.LiveRecord, models.LiveHLS, models.TransPackager, models.IMSCaPackager, models.MediaPackager, models.LiveSnapshot:
		return a.recordHlsEngine
	case models.LiveTranscode:
		return a.liveTranscodeEngine
	default:
		return a.commonEngine
	}
}

func (a *Adapter) GetEngineByWorker(worker *models.Worker) common.EngineAdapter {
	switch worker.EngineModel {
	case models.LiveRecord, models.LiveHLS, models.TransPackager, models.IMSCaPackager, models.MediaPackager, models.LiveSnapshot:
		return a.recordHlsEngine
	case models.LiveTranscode:
		return a.liveTranscodeEngine
	default:
		return a.commonEngine
	}
}
