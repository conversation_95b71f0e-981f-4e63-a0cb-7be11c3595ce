package conf

func (x *Bootstrap) GetRedisUri() string {
	if x != nil && x.GetData() != nil && x.GetData().GetRedis() != nil {
		return x.GetData().GetRedis().GetEndpoint()
	}

	return ""
}

// 精确匹配 获取任务管理配置
func (x *Bootstrap) getTaskManagerConfig(product, engineModel, tag string) *TaskManagerConfig {
	key := product + "_" + engineModel + "_" + tag
	if x.ManagerConfig != nil && x.ManagerConfig.TaskManagerConfigMap != nil {
		config := x.ManagerConfig.TaskManagerConfigMap[key]
		return config
	}
	return nil
}

// 模糊匹配 获取任务管理配置
func (x *Bootstrap) MatchTaskManagerConfig(product, engineModel, tag string) *TaskManagerConfig {
	wildcard := "*"
	key := product + "_" + engineModel + "_" + tag
	if x.ManagerConfig == nil || x.ManagerConfig.TaskManagerConfigMap == nil {
		return nil
	}

	if config, ok := x.ManagerConfig.TaskManagerConfigMap[key]; ok {
		return config
	}

	key = product + "_" + engineModel + "_" + wildcard
	if config, ok := x.ManagerConfig.TaskManagerConfigMap[key]; ok {
		return config
	}
	key = product + "_" + wildcard + "_" + wildcard
	if config, ok := x.ManagerConfig.TaskManagerConfigMap[key]; ok {
		return config
	}
	key = wildcard + "_" + wildcard + "_" + wildcard
	if config, ok := x.ManagerConfig.TaskManagerConfigMap[key]; ok {
		return config
	}
	return nil
}
