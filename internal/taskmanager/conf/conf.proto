syntax = "proto3";
package kratos.api;

option go_package = "mpp/internal/taskmanager/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Registry registry = 2;
  Data data = 3;
  Swagger swagger = 4;
  Notifier notifier = 5;
  Cluster cluster = 6;
  Mns mns = 7;
  Router router = 8;
  Quota quota = 9;
  DefaultSentinelConfig defaultSentinelConfig = 10;
  //三元组调度配置
  ManagerConfig managerConfig = 11;
  Workflow workflow = 12;
  LiveTransCodeConfig liveTransCodeConfig = 13;
}

message LiveTransCodeConfig{
  string unit = 1;
  string env = 2;
  string bizHost = 3;
  string gslbHost = 4;
  string liveTocHost = 5;
  repeated UnitDatabase unitDatabase = 6;

  message UnitDatabase {
    string unit = 1;
    string driver = 2;
    string source = 3;
  }
}

message Quota {
  string host = 1;
  EngineQuotaSetMap engineQuotaSetMap = 2;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;

  string tracingUrl = 3;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
    bool autoMigrate = 3;
  }

  message Redis {
    string endpoint = 1;
  }

  Database database = 1;

  Redis redis = 2;

  Database databaseBackup = 3;

  int32 threshold = 4;
}

message Registry {
  string endpoint = 1;
}

message Notifier {
  string address = 1;
}

message Workflow {
  string address = 1;
}

message Cluster {
  string id = 1;
  string region = 2;
}

message Swagger {
  string enabled = 1;
}

message Mns {
  string accessKey = 1;
  string secretKey = 2;
}

message Router {
  int32 percent = 1;
  string oldSystemHost = 2;
  // 此字段废弃
  bool holdFailed = 3;
  //ai灰度配置
  string oldAiSystemHost = 4;
  repeated EngineTable engineTable = 5;

  bool oldSystemExist = 6;
  bool stopBoth = 7;
  repeated DomainTable domainTable = 8;
}



// Map: engine -> quota("resourceName" -> "value")
// engine = "$product/$engineModel/$tag"
message EngineQuotaSetMap{
  map<string, QuotaSet> engineQuota = 1;
}
message QuotaSet {
  map<string, int64> quota = 1;
}

//AI根据三元组生成灰度表
message EngineTable {
  string product = 1;
  string engineModel = 2;
  string tag = 3;
  int32 percent = 4;
}

// 根据域名灰度能力
message DomainTable {
  string domain = 1;
  int32 percent = 2;
}

//默认限流配置
message DefaultSentinelConfig {
  int32 defaultMaxParallelNum = 1;
  bool enable = 2;
}

//三元组调度配置
//key = $product_$engineModel_$tag 例：rms_rms_default
message ManagerConfig{
  map<string, taskManagerConfig> taskManagerConfigMap = 1;
}

//任务调度配置
message taskManagerConfig{
  //任务重复启动且任务正在运行中时，重入引擎参数不匹配的调度重启策略
  //1.ignoreAndReturn: 忽略参数不匹配，返回任务启动成功(默认策略)
  //2.restartAndUpdateParams: 重启任务并更新引擎参数
  string TaskRestartExistAndEngineMismatchPolicy = 1;
}
