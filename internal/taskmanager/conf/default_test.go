package conf

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

type TestBootstrap struct {
	*Bootstrap
	managerConfig MockManagerConfig // 使用mock模拟ManagerConfig对象
}

var (
	test_policy  = "ignoreAndReturn"
	test_policy2 = "restartAndUpdateParams"
)

// 初始化测试数据
func setupTest() (*TestBootstrap, func()) {
	tb := &TestBootstrap{
		Bootstrap: new(Bootstrap),
	}
	tb.Bootstrap.ManagerConfig = &ManagerConfig{
		TaskManagerConfigMap: NewMockManagerConfig().TaskManagerConfigMap,
	}
	cleanup := func() {
		tb = nil // 释放资源
	}

	return tb, cleanup
}

// 创建MockManagerConfig用于测试
type MockManagerConfig struct {
	TaskManagerConfigMap map[string]*TaskManagerConfig // 假设TaskManagerConfigMap是map类型
}

// 返回一个新的MockManagerConfig实例
func NewMockManagerConfig() MockManagerConfig {
	res := MockManagerConfig{
		TaskManagerConfigMap: make(map[string]*TaskManagerConfig),
	}
	//res.TaskManagerConfigMap["product1_engine1_tag1"] = &TaskManagerConfig{
	//	TaskRestartExistAndEngineMismatchPolicy: test_policy,
	//}
	//res.TaskManagerConfigMap["product1_engine1_*"] = &TaskManagerConfig{
	//	TaskRestartExistAndEngineMismatchPolicy: test_policy2,
	//}
	return res
}

// 测试精确匹配获取任务管理配置
func TestGetTaskManagerConfig(t *testing.T) {
	tb, cleanup := setupTest()
	defer cleanup()

	// 测试用例1: 存在对应配置
	taskManagerConfig1 := &TaskManagerConfig{
		TaskRestartExistAndEngineMismatchPolicy: test_policy,
	}
	tb.Bootstrap.ManagerConfig.TaskManagerConfigMap["product1_engine1_tag1"] = taskManagerConfig1
	config := tb.getTaskManagerConfig("product1", "engine1", "tag1")
	assert.Equal(t, taskManagerConfig1, config)

	// 测试用例2: 不存在对应配置
	actual := tb.getTaskManagerConfig("product2", "engine2", "tag2")
	assert.Equal(t, (*TaskManagerConfig)(nil), actual)
}

// 测试模糊匹配获取任务管理配置
func TestMatchTaskManagerConfig(t *testing.T) {
	tb, cleanup := setupTest()
	defer cleanup()

	taskManagerConfig1 := &TaskManagerConfig{
		TaskRestartExistAndEngineMismatchPolicy: test_policy,
	}

	// 测试用例1: 精确匹配存在
	tb.Bootstrap.ManagerConfig.TaskManagerConfigMap["product1_engine1_tag1"] = taskManagerConfig1
	assert.Equal(t, taskManagerConfig1, tb.MatchTaskManagerConfig("product1", "engine1", "tag1"))

	// 测试用例2: 第一层次模糊匹配存在
	tb.Bootstrap.ManagerConfig.TaskManagerConfigMap["product1_engine1_*"] = taskManagerConfig1
	assert.Equal(t, taskManagerConfig1, tb.MatchTaskManagerConfig("product1", "engine1", "tag2"))

	// 测试用例3: 第二层次模糊匹配存在
	tb.Bootstrap.ManagerConfig.TaskManagerConfigMap["product1_*_*"] = taskManagerConfig1
	assert.Equal(t, taskManagerConfig1, tb.MatchTaskManagerConfig("product1", "engine2", "tag2"))

	// 测试用例4: 所有匹配都不存在
	actual := tb.MatchTaskManagerConfig("product2", "engine2", "tag2")
	assert.Equal(t, (*TaskManagerConfig)(nil), actual)

	// 测试用例5: 全局模糊匹配存在
	tb.Bootstrap.ManagerConfig.TaskManagerConfigMap["*_*_*"] = taskManagerConfig1
	assert.Equal(t, taskManagerConfig1, tb.MatchTaskManagerConfig("product2", "engine2", "tag2"))

}
