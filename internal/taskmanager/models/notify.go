package models

// AsyncJobNotifyRequest 适配tc的notify结构
type AsyncJobNotifyRequest struct {
	UserId            string           `json:"userId"`
	JobId             string           `json:"jobId"`
	UserData          string           `json:"userData"`
	Code              string           `json:"code"`
	Message           string           `json:"message"`
	Data              interface{}      `json:"data"`
	DataUrl           string           `json:"dataUrl"`
	ExecuteMode       string           `json:"executeMode"`
	Event             string           `json:"event"`
	CreateTime        string           `json:"createTime"`
	FinishTime        string           `json:"finishTime"`
	DequeueTime       string           `json:"dequeueTime"`
	QuotaSet          map[string]int64 `json:"quotaSet"`
	IsExecuteBySpeedX bool             `json:"isExecuteBySpeedX"`
	Speedx            string           `json:"speedx"`
}

type NotifyEvent string

const (
	JOB_COMPLETED_NOTIFY_EVENT NotifyEvent = "JobCompleted"
	JOB_DEQUEUED_NOTIFY_EVENT  NotifyEvent = "JobDequeued"
)

const (
	JOB_DEQUEUE_NOTIFY_URL_QUERY = "dequeued"
)

const (
	NOTIFY_MESSAGE_SUCCESS = "Success"
	NOTIFY_MESSAGE_FAILED  = "Failed"
)

// 适配tc层的notify结构
type TCNotifyRequest struct {
	ID        string                 `json:"taskId"`
	Code      string                 `json:"code"`
	UserData  string                 `json:"userData,omitempty"`
	Message   string                 `json:"message,omitempty"`
	Result    interface{}            `json:"data,omitempty"`
	ResultURL string                 `json:"dataUrl,omitempty"`
	Duration  int64                  `json:"realCostTime,omitempty"`
	Extends   map[string]interface{} `json:"extend,omitempty"`
}

const (
	//tc 层的notify事件
	EventTaskNotify = "tc.jobmanager.notify"
)
