package models

import (
	"github.com/go-kratos/kratos/v2/log"
	pb "proto.mpp/api/taskmanager/v1"
)

func init() {
	// 示例产品列表及其配置
	products := []struct {
		product    string
		maxRuntime int32
		unMigrate  bool
	}{
		{"rms", 50, false},
		{"mediaai", 8, false},
		{"mps", 3, false},
	}

	for _, p := range products {
		config := &pb.MigrateConfig{
			MaxRuntime: p.maxRuntime,
			UnMigrate:  p.unMigrate,
		}
		migrateConfigsByProductMap[p.product] = config
		log.Infof("Initialized migration config for product=%s :MaxRuntime=%d, UnMigrate=%t\n", p.product, p.maxRuntime, p.unMigrate)
	}
}

// 默认最大迁移次数
const (
	DefaultMaxRuntime = 10
)

var (
	// 默认迁移配置
	defaultMigrateConfig = &pb.MigrateConfig{
		MaxRuntime: DefaultMaxRuntime,
		UnMigrate:  false,
	}
	migrateConfigsByProductMap = map[string]*pb.MigrateConfig{}
)

func GetMigrateConfigByProduct(product string) *pb.MigrateConfig {
	if config, ok := migrateConfigsByProductMap[product]; ok {
		return config
	}
	return defaultMigrateConfig
}

func GetMigrateConfigs(unMigrate bool, maxRuntime int32) *pb.MigrateConfig {
	reuslt := &pb.MigrateConfig{
		UnMigrate:  false,
		MaxRuntime: DefaultMaxRuntime,
	}
	if unMigrate {
		reuslt.UnMigrate = unMigrate
	}
	if maxRuntime > 0 {
		reuslt.MaxRuntime = maxRuntime
	}
	return reuslt
}

func SetMigrateConfigByProduct(product string, config *pb.MigrateConfig) {
	migrateConfigsByProductMap[product] = config
}
