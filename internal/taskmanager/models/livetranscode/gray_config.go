package livetranscode

type GrayConfigRequest struct {
	Configs []GrayConfigRequestConfig `json:"configs"`
}

type GrayConfigRequestConfig struct {
	Product         string `json:"product"`
	EngineModel     string `json:"engineModel"`
	Domain          string `json:"domain"`
	App             string `json:"app"`
	Token           string `json:"token"`
	EdgeCpu         int    `json:"edgeCpu"`
	CenterCpu       int    `json:"centerCpu"`
	CenterGpu       int    `json:"centerGpu"`
	PublicCpu       int    `json:"publicCpu"`
	PublicGpu       int    `json:"publicGpu"`
	L2Cpu           int    `json:"l2Cpu"`
	CenterCpu2      int    `json:"centerCpu2"`
	L2Cpu2          int    `json:"l2Cpu2"`
	Desc            string `json:"desc"`
	CanHoldByPubTag bool   `json:"canHoldByPubTag"`
	Order           string `json:"order"`
	Tag             string `json:"tag"`
}

type GrayConfigQueryRequest struct {
	Product     string `json:"product"`
	EngineModel string `json:"engineModel"`
	Domain      string `json:"domain"`
	App         string `json:"app"`
	Token       string `json:"token"`
	Tag         string `json:"tag"`
}

type SwitchConfig struct {
	EdgeCpu    int `json:"edgeCpu"`
	CenterCpu  int `json:"centerCpu"`
	CenterGpu  int `json:"centerGpu"`
	PublicCpu  int `json:"publicCpu"`
	PublicGpu  int `json:"publicGpu"`
	L2Cpu      int `json:"l2Cpu"`
	CenterCpu2 int `json:"centerCpu2"`
	L2Cpu2     int `json:"l2Cpu2"`
}

type BackupConfig struct {
	CanHoldByPubTag bool   `json:"canHoldByPubTag"`
	Order           string `json:"order"`
}
