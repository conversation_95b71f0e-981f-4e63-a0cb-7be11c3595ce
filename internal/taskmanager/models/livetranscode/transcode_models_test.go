package livetranscode

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestParamsSwitch(t *testing.T) {
	jsonStr := `{
  "appName": "stage",
  "centerPublicAddr": "121.89.97.xxx",
  "domain": "pull-l3.xxxx.com",
  "gslbAuthTime": "1234xxx",
  "isCdnEdge": "false",
  "isEdge": "false",
  "params": {
    "streamNum": 1,
    "streamSource": "rtmp://121.89.97.xxx:yyy/stage/pull-l3.douyincdn.com_stream-xxxxxxxx?ali_mapstream_ignore=on&ali_ols_play=on&ali_ols_play_type=transcoding&ali_rtmp_quick_start=off&ali_rtmp_retain=0&vhost=pull-l3.douyincdn.com",
    "streamSourceBakVips": [
      "121.89.97.xxx:1936",
      "*************:xxx"
    ],
    "streamSourceBitrate": 2441,
    "streamSourceCodec": "h265",
    "streamSourceFps": 22,
    "streamSourceHeight": 1920,
    "streamSourceType": "stream",
    "streamSourceWidth": 1088,
    "streamTimestampOffset": 0.0,
    "streams": [
      {
        "destBakVips": [
          "121.89.97.xxx:1935"
        ],
        "rtmpDest": "rtmp://121.89.xxx.yyy/stage/stream-xxxxx?vhost=pull-l3.douyincdn.com&live_rtmp_test=on&ali_inter_auth_time=682d3a64&transcode=no&transcoded=true&ali_center_tran_pub=on",
        "vfilter": "no_ph_vf[0:v]blendimages=json={}[vfo]",
        "input_bypass": "-threads 3",
        "fps": "1:30",
        "vprofile": "high",
        "env": "pub",
        "type": "transcode",
        "ffver": "ffv5",
        "sw2main": 3,
        "width": -2,
        "output_bypass": "-cqfactor 25 -rule adapt_res_480*852#4d",
        "x264_params": "hierarchical-b=1:aq-strength=0.8:sync-lookahead=2:force-cfr=1:lookahead-threads=1:rc-lookahead=5:bframes=3:threads=3:keyint=infinite:scenecut=0:psy=0:trellis=0:me=dia:merange=16:subme=4:vbv-maxrate=1000:vbv-bufsize=1500:bitrate=1000:ref=2:partitions=i8x8,i4x4:mb-lowres=2:aq-mode=2:opt-lowres-intra=1:bi-skip=20:mbtree-motion-lag=1:bskip-ref=1:b-adapt=0 -vcopybr 200:1:1000 -copyts -force_key_frames source -ignore_subsei_ex 1 -qsd_flush -sei_hold_all -force_avsync -sei_order_by_dts -dec_force_header_on_key -colorinfo_bypass all",
        "aCodec": "copy",
        "height": 852,
        "vCodec": "h264"
      }
    ]
  },
  "publishNode": "cn8413",
  "pushDomain": "push-rtmp-l3.xxxxxx.com",
  "quota": 300,
  "streamName": "stream-xxxxx",
  "switchWorker": true,
  "templateName": "xxxx-ld-1000-ffv5",
  "token": "ld",
  "forceRestart": "true"
}`
	var streamParams StreamParams
	err := json.Unmarshal([]byte(jsonStr), &streamParams)
	assert.Nil(t, err)
	//t.Logf("%#v", streamParams)
	//t.Logf("%+v", streamParams.ForceRestart)
	//t.Logf("%+v", streamParams.SwitchWorker)
}
