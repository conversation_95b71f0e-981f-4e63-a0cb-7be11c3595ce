package livetranscode

import "time"

//type StreamInfoExtend struct {
//	V2               string `json:"v2"`
//	WorkerIp         string `json:"workerIp"`
//	ApiVersion       string `json:"apiVersion"`
//	ResourcePoolType string `json:"resourcePoolType"`
//	RouteMode        string `json:"routeMode"`
//	LostResouce      bool   `json:"lostResouce"`
//	Token            string `json:"token"`
//	Unit             string `json:"unit"`
//}

const (
	V2          string = "V2"
	DEFAULT     string = "default"
	EDGE_CPU    string = "L1"
	INNER_CPU   string = "Inner"
	INNER_GPU   string = "Inner"
	PUBLIC_CPU  string = "Public"
	PUBLIC_GPU  string = "Public"
	PUBLIC      string = "public"
	L2_CPU      string = "l2"
	INNER_CPU_2 string = "Inner_2"
	L2_CPU_2    string = "l22"
	AUTO        string = "Auto"
	CENTER      string = "center"

	COMMAN_COMMAND    int32 = 1
	UPDATE_COMMAND    int32 = 2
	PIC_ADD_COMMAND   int32 = 3
	PIC_EDIT_COMMAND  int32 = 4
	TEXT_ADD_COMMAND  int32 = 5
	TEXT_EDIT_COMMAND int32 = 6

	AttachmentAddType  string = "add"
	AttachmentEditType string = "edit"

	AttachmentTypePIC  string = "pic"
	AttachmentTypeTEXT string = "text"
)

// request

type LiveTransCodePicEditTaskRequest struct {
	TaskInfo
	Pictures []map[string]interface{} `json:"pictures"`
}

type LiveTransCodeTextEditTaskRequest struct {
	TaskInfo
	Texts []map[string]interface{} `json:"texts"`
}

type LiveTransCodeStopRequest struct {
	TaskInfo
	Unit string `json:"unit"`
}

type LiveTransCodeMigrateRequest struct {
	Tag          string `json:"tag"`
	Domain       string `json:"domain"`
	App          string `json:"app"`
	Token        string `json:"token"`
	Stream       string `json:"stream"`
	TaskId       string `json:"taskId"`
	SameDictator bool   `json:"sameDictator"`
}

type LiveTransCodeSendCommandRequest struct {
	TaskId string      `json:"taskId"`
	Uri    string      `json:"uri"`
	Body   interface{} `json:"body"`
}

type LiveTransCodeUpdateRequest struct {
	TaskId  string        `json:"taskId"`
	Streams []interface{} `json:"streams"`
}

type LiveTransCodeQueryStatRequest struct {
	Tag    string `json:"tag"`
	Domain string `json:"domain"`
}

type LiveTransCodeTaskStatisticsRequest struct {
	DingUser string `json:"ding_user"`
	Tag      string `json:"tag"`
	Domain   string `json:"domain"`
	App      string `json:"app"`
}

type LiveTransCodeListTagRequest struct {
	DingUser string `json:"ding_user"`
}

//model

type TaskInfo struct {
	TaskId                string `json:"taskId"`
	Soft                  bool   `json:"soft"`
	LastWorkerIp          string `json:"lastWorkerIp"`
	Token                 string `json:"token"`
	IsEdge                string `json:"isEdge"`
	FailOnError           bool   `json:"failOnError"`
	ForceRestart          bool   `json:"forceRestart"`
	SchedulerForceRestart bool   `json:"schedulerForceRestart"`
	IgnoreReleaseFail     bool   `json:"ignoreReleaseFail"`
}

//type SurfaceObject struct {
//	Name         string  `json:"name"`
//	Biztag       string  `json:"biztag"`
//	Animate      int     `json:"animate"`
//	Visible      int     `json:"visible"`
//	Transparency int     `json:"transparency"`
//	Xoffset      float64 `json:"xoffset"`
//	Yoffset      float64 `json:"yoffset"`
//	Offsetcorner int     `json:"offsetcorner"`
//
//	Text               string  `json:"text"`
//	NormalizedFontSIze float64 `json:"normalizedfontsize"`
//	Color              string  `json:"color"`
//	Borderwidth        int     `json:"borderwidth"`
//	BorderColor        string  `json:"bordercolor"`
//
//	PictureUrl        string  `json:"pictureurl"`
//	NormalizedH       float64 `json:"normalizedh"`
//	NormalizedRefMode int     `json:"normalizedRefMode"`
//}

type StreamParams struct {
	StreamInfo
	Quota            int             `json:"quota"`
	Params           TranscodeParams `json:"params"`
	CenterPublicAddr string          `json:"centerPublicAddr"`
	Unit             string          `json:"unit"`
	TraceId          string          `json:"traceId"`
}

type TranscodeParams struct {
	StreamSource              string                 `json:"streamSource"`              // 流源地址
	StreamSourceType          string                 `json:"streamSourceType"`          // 流源类型
	StreamSourceCodec         string                 `json:"streamSourceCodec"`         // 流源编码格式
	StreamSourceWidth         interface{}            `json:"streamSourceWidth"`         // 流源宽度
	StreamSourceHeight        interface{}            `json:"streamSourceHeight"`        // 流源高度
	StreamSourceFps           interface{}            `json:"streamSourceFps"`           // 流源帧率
	StreamSourceBitrate       interface{}            `json:"streamSourceBitrate"`       // 流源比特率
	StreamSourceVolatile      bool                   `json:"streamSourceVolatile"`      // 流源是否易变
	StreamNum                 int                    `json:"streamNum"`                 // 流数量
	StreamTimestampOffset     float64                `json:"streamTimestampOffset"`     // 流时间戳偏移
	Streams                   []interface{}          `json:"streams"`                   // 流信息列表
	SwitchStreams             []interface{}          `json:"switchStreams"`             // 切换流信息列表
	BackupParams              map[string]interface{} `json:"backupParams"`              // 备份参数
	StreamSourceBakVips       []string               `json:"streamSourceBakVips"`       // 流源备用 VIP 地址列表
	StreamInterSourceBakAddrs []string               `json:"streamInterSourceBakAddrs"` // 内部流源备用地址列表
	StreamL2SourceBakAddrs    []string               `json:"streamL2SourceBakAddrs"`    // L2 流源备用地址列表
	ImsExtra                  string                 `json:"imsExtra"`                  // 实时媒体转码透传参数
	AdaptiveHDROutput         string                 `json:"adaptiveHDROutput"`         // 自适应 HDR 输出
	Quota                     int                    `json:"quota"`                     // 预设配额
	AllAudioCopy              int                    `json:"allAudioCopy"`              // 全音频复制标志
	BizNotifyAddr             string                 `json:"bizNotifyAddr"`             // 业务通知地址
	BizReportMsgAddr          string                 `json:"bizReportMsgAddr"`          // 业务报告消息地址
	RateCallbackParams        interface{}            `json:"rateCallbackParams"`        // 实时码率回调参数
	PushDomain                string                 `json:"pushDomain"`                // 推流域名
	Domain                    string                 `json:"domain"`                    // 域名
	AppName                   string                 `json:"appName"`                   // 应用名称
	StreamName                string                 `json:"streamName"`                // 流名称
	Token                     string                 `json:"token"`                     // Token
	CenterPublicAddr          string                 `json:"centerPublicAddr"`          // 直播中心公网 IP
}

type StreamInfo struct {
	Domain                   string           `json:"domain"`       // 域名
	AppName                  string           `json:"appName"`      // 应用名称
	StreamName               string           `json:"streamName"`   // 流名称
	Token                    string           `json:"token"`        // Token
	IsEdge                   string           `json:"isEdge"`       // ENS 边缘转码
	IsCdnEdge                string           `json:"isCdnEdge"`    // CDN 边缘转码
	Test                     bool             `json:"test"`         // 测试标志
	SwitchWorker             bool             `json:"switchWorker"` // 启动转码并替换机器
	PublishNode              string           `json:"publishNode"`  // 原始流所在节点名
	PushDomain               string           `json:"pushDomain"`   // 推流域名
	GslbAuthTime             string           `json:"gslbAuthTime"` // GSLB 认证时间
	TemplateName             string           `json:"templateName"` // 模板名称
	Extend                   StreamInfoExtend `json:"extend"`       // 扩展字段
	ForceRestart             string           `json:"forceRestart"` // 强制重启
	NoFailHoldAndReturnError string           `json:"noFailHoldAndReturnError"`
}

// StreamInfoExtend 对应 Java 类 StreamInfoExtend 的结构体
type StreamInfoExtend struct {
	WorkerIp         string `json:"workerIp"`         // 工作节点 IP
	ApiVersion       string `json:"apiVersion"`       // API 版本
	ResourcePoolType string `json:"resourcePoolType"` // 资源池类型
	RouteMode        string `json:"routeMode"`        // 路由模式
	LostResource     bool   `json:"lostResource"`     // 是否丢失资源
	Token            string `json:"token"`            // Token
	Unit             string `json:"unit"`             // 单位
}

type LiveStatisticsTaskStat struct {
	Domain     string  `json:"domain"`
	App        string  `json:"app"`
	Token      string  `json:"token"`
	Num        int32   `json:"num"`
	Percentage float64 `json:"percentage"`
	Quota      int32   `json:"quota"`
	Template   string  `json:"template"`
}

// RateCallbackConfig 速率回调配置
type RateCallbackConfig struct {
	Contents []RateCallbackContent `json:"contents"`
	Version  int64                 `json:"version"`
}

// RateCallbackContent 速率回调内容
type RateCallbackContent struct {
	Domain   string `json:"domain"`
	App      string `json:"app"`
	Token    string `json:"token"`
	Interval int    `json:"interval"`
	Duration int    `json:"duration"`
}

// TranscodeTask 转码任务信息
type TranscodeTask struct {
	TaskId     string `json:"taskId"`
	Env        string `json:"env"`
	DestStream string `json:"destStream"`
	WorkerIp   string `json:"workerIp"`
}

// QueryTasksWithSourceStreamParam 查询带源流任务的参数
type QueryTasksWithSourceStreamParam struct {
	Domain string `json:"domain"`
	App    string `json:"app"`
	Stream string `json:"stream"`
}

type LiveTransCodeTask struct {
	ID                  int64       `json:"id,omitempty"`
	Domain              string      `json:"domain,omitempty"`
	TaskID              string      `json:"taskId,omitempty"`
	Quota               int         `json:"quota,omitempty"`
	Params              string      `json:"params,omitempty"`
	Status              int         `json:"status,omitempty"`
	WorkerUUID          string      `json:"workerUuid,omitempty"`
	WorkerIP            string      `json:"workerIp,omitempty"`
	WorkerOnlineVersion int         `json:"workerOnlineVersion,omitempty"`
	Running             int         `json:"running,omitempty"`
	Env                 string      `json:"env,omitempty"`
	TemplateName        string      `json:"templateName,omitempty"`
	Extend              string      `json:"extend,omitempty"`
	GmtCreate           time.Time   `json:"gmtCreate,omitempty"`
	GmtModified         time.Time   `json:"gmtModified,omitempty"`
	UserData            interface{} `json:"userData,omitempty"`
}
