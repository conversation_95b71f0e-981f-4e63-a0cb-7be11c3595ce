package custom_types

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Metadata 参数
type Metadata map[string]string

type MetadataKey string

const (
	MetadataKeyRequestSource MetadataKey = "request_source"
)

type RequestSource string

const (
	RequestSourceStd = RequestSource("std_adapter")
	RequestSourceTc  = RequestSource("tc_adapter")
)

// Scan Scanner
func (args Metadata) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	b, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("value is not []byte, value: %v", value)
	}

	err := json.Unmarshal(b, &args)
	if err != nil {
		// todo 先忽略错误
	}

	return nil
}

// Value Valuer
func (args Metadata) Value() (driver.Value, error) {
	if args == nil {
		return nil, nil
	}

	return json.Marshal(args)
}
