package models

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"mpp/internal/taskmanager/models/domain"
	"mpp/internal/taskmanager/util"

	v1 "proto.mpp/api/common/v1"
	pb "proto.mpp/api/taskmanager/v1"

	"github.com/go-kratos/kratos/v2/errors"
)

type TaskType v1.TaskType

var (
	ErrInternalError      = errors.NotFound(v1.ErrorReason_INTERNAL_ERROR.String(), "internal error.")
	ErrTaskNotFoundError  = errors.NotFound(v1.ErrorReason_TASK_NOT_FOUND.String(), "tasker not found.")
	ErrTaskNotRunning     = errors.NotFound(v1.ErrorReason_TASK_NOT_RUNNING.String(), "tasker not running.")
	ErrTaskAlreadyExist   = errors.NotFound("TaskAlreadyExist", "tasker is already exist.")
	ErrTaskStillRunning   = errors.NotFound("TaskStillRunning", "tasker is still running.")
	ErrRequestInvalid     = errors.NotFound("InvalidParameters", "Invalid parameters.")
	ErrMigrateParams      = errors.NotFound("InvalidMigrateParameters", "taskIds length surpasses the batchSize limit.")
	ErrDagGraphNotFound   = errors.NotFound("dag graph not found", "dag graph not found")
	ErrMeshConfigNotFound = errors.NotFound("mesh config not found", "mesh config not found")

	ErrWorkerNotFound     = errors.NotFound(v1.ErrorReason_WORKER_NOT_FOUND.String(), "worker not found")
	ErrDependencyNotReady = errors.NotFound("DEPENDENCY_NOT_READY", "dependency not ready")
)

type MetaDataField string

const (
	//混跑配置
	MetaDataFieldMixConfig MetaDataField = "mixConfig"
	//trace
	MetaDataFieldTraceparent MetaDataField = "traceparent"
)
const (
	DefaultTaskTag         = "default"
	DefaultTaskEngineModel = "default"
)

const (
	StreamTaskTableName = "stream_tasks"
	BatchTaskTableName  = "batch_tasks"
)

const (
	LiveHLS       = "livehls"
	LiveRecord    = "liverecord"
	LiveTranscode = "livetranscode"
	TransPackager = "transpackager"
	MediaPackager = "mediapackager"
	IMSCaPackager = "imsca"
	LiveSnapshot  = "livesnapshot"
	UnknownType   = "UnknownType"

	ProductLive = "ols"
)

var PackagerTypeModels = []string{LiveRecord, LiveHLS, TransPackager, IMSCaPackager, MediaPackager, LiveSnapshot}

const (
	UnknownTaskType       = TaskType(v1.TaskType_Unknown)
	SimpleTaskType        = TaskType(v1.TaskType_Simple)
	SyncTaskType          = TaskType(v1.TaskType_Simple)
	StreamTaskType        = TaskType(v1.TaskType_Stream)
	AsyncTaskType         = TaskType(v1.TaskType_Async)
	DagAsyncTaskType      = TaskType(v1.TaskType_DagAsync)
	DagSubTaskType        = TaskType(v1.TaskType_DagSub)
	NonQueueAsyncTaskType = TaskType(v1.TaskType_NonQueueAsync)
)

func (x TaskType) String() string {
	return v1.TaskType(x).String()
}

const (
	EngineParamErrorCode uint32 = 400
	MigratedCode         uint32 = 503
	EngineLostCode       uint32 = 500
	//调度任务迁移 由TM迁移任务时更新该错误码
	MigratedReason string = "migrated"
	//引擎丢失
	EngineLostReason string = "engineLost"
	////引擎不可用 继承chaos错误码
	EngineUnavailable string = "engineUnavailable"
	//引擎内部错误 继承chaos错误码
	InternalException string = "internalException"
	///引擎资源限制 继承chaos错误码
	QuotaLimit string = "quotaLimit"
	//agent执行shell命令失败
	EngineRunShellErrorCode string = "EngineError.ShellError.CmdExitError"
	//任务无法执行，参数错误
	EngineParamError string = "engineParamError"
)

type TaskStatus int8

const (
	// TaskStateNone 默认状态，无状态
	TaskStateNone TaskStatus = iota
	TaskStateActive
	TaskStateStopped
	TaskStatePending
	TaskStateScheduled
	TaskStateRetry
	TaskStateArchived
	TaskStateCompleted
	TaskStateAggregating
	//dag 新增状态 等待依赖任务
	TaskStateWaitDependency
)

//任务调度策略

// 任务重复启动且任务正在运行中时，重入引擎参数不匹配的调度重启策略
type TaskRestartExistAndEngineMismatchPolicy string

const (
	//1.ignoreAndReturn: 忽略参数不匹配，返回任务启动成功(默认策略)
	IgnoreAndReturn TaskRestartExistAndEngineMismatchPolicy = "ignoreAndReturn"
	//2.restartAndUpdateParams: 重启任务并更新引擎参数
	RestartAndUpdateParams TaskRestartExistAndEngineMismatchPolicy = "restartAndUpdateParams"
)

func (s TaskStatus) Int() int {
	return int(s)
}

func (s TaskStatus) String() string {
	switch s {
	case TaskStateActive:
		return "active"
	case TaskStateStopped:
		return "stopped"
	case TaskStatePending:
		return "pending"
	case TaskStateScheduled:
		return "scheduled"
	case TaskStateRetry:
		return "retry"
	case TaskStateArchived:
		return "archived"
	case TaskStateCompleted:
		return "completed"
	case TaskStateAggregating:
		return "aggregating"
	}
	panic(fmt.Sprintf("internal error: unknown tasker state %d", s))
}

func TaskStateFromString(s string) (TaskStatus, error) {
	switch s {
	case "active":
		return TaskStateActive, nil
	case "stopped":
		return TaskStateStopped, nil
	case "pending":
		return TaskStatePending, nil
	case "scheduled":
		return TaskStateScheduled, nil
	case "retry":
		return TaskStateRetry, nil
	case "archived":
		return TaskStateArchived, nil
	case "completed":
		return TaskStateCompleted, nil
	case "aggregating":
		return TaskStateAggregating, nil
	}

	return 0, fmt.Errorf("unknown tasker state. %q is not supported tasker state", s)
}

func TaskStateFromInt(c int) (TaskStatus, error) {
	switch c {
	case 1:
		return TaskStateActive, nil
	case 2:
		return TaskStateStopped, nil
	case 3:
		return TaskStatePending, nil
	case 4:
		return TaskStateScheduled, nil
	case 5:
		return TaskStateRetry, nil
	case 6:
		return TaskStateArchived, nil
	case 7:
		return TaskStateCompleted, nil
	case 8:
		return TaskStateAggregating, nil
	}

	return 0, fmt.Errorf("TaskStateFromInt fail unknown tasker state. %v is not supported tasker state", c)
}

type RequestResource struct {
	Quota    map[string]int64
	Affinity *TaskAffinity `json:"Affinity,omitempty"`
}

type Dependency struct {
	TaskId string            `json:"taskId,omitempty"`
	Fields map[string]string `json:"fields,omitempty"`
}

type TaskEngineParam struct {
	Param    string `json:"Param,omitempty"`
	ParamUrl string `json:"ParamUrl,omitempty"`
}

type TaskResult struct {
	Data     interface{}            `json:"Data,omitempty"`
	DataUrl  string                 `json:"DataUrl,omitempty"`
	Code     uint32                 `json:"Code,omitempty"`
	Reason   string                 `json:"Reason,omitempty"`
	Message  string                 `json:"Message,omitempty"`
	Extends  map[string]interface{} `json:"Extend,omitempty"`
	Duration int64                  `json:"Duration,omitempty"`
}

type Worker struct {
	Id          string
	Port        int32
	Ip          string
	EngineModel string
	Tag         string
	Product     string
	ConnectMode []string
}

func (w *Worker) String() string {
	return fmt.Sprintf("Worker{Id:%s,Port:%d,Ip:%s,EngineModel:%s,ConnectMode:%v}", w.Id, w.Port, w.Ip, w.EngineModel, w.ConnectMode)
}

type Resource struct {
	JobId        string
	TaskId       string
	WorkerId     string
	SchedulerTag string
	TaskType     TaskType
}

type MigrateConfig struct {
	MaxRuntime int32
	UnMigrate  bool
}

// 点播dag图结构
type DagGraph struct {
	Nodes         []string
	Edges         []string
	ScheduleMode  string
	migratePolicy string
}

type Task struct {
	Product         string            `json:"product,omitempty"`
	TaskId          string            `json:"taskId,omitempty"`
	JobId           string            `json:"jobId,omitempty"`
	UserId          string            `json:"userId,omitempty"`
	PipelineId      string            `json:"pipelineId,omitempty"`
	BizStatus       TaskStatus        `json:"bizStatus,omitempty"`
	InternalStatus  TaskStatus        `json:"internalStatus,omitempty"`
	Worker          *Worker           `json:"worker,omitempty"`
	UserData        string            `json:"userData,omitempty"`
	Param           *TaskEngineParam  `json:"param,omitempty"`
	EngineModel     string            `json:"engineModel,omitempty"`
	Tag             string            `json:"tag,omitempty"`
	Trace           string            `json:"trace,omitempty"`
	RequestResource *RequestResource  `json:"requestResource,omitempty"`
	Result          *TaskResult       `json:"result,omitempty"`
	TaskType        TaskType          `json:"taskType,omitempty"`
	MigrateConfig   *pb.MigrateConfig `json:"migrateConfig,omitempty"`
	MigrateTimes    int32             `json:"migrateTimes,omitempty"`
	Notify          string            `json:"notify,omitempty"`
	Dependencies    []Dependency      `json:"dependencies,omitempty"`
	LastModified    time.Time         `json:"lastModified,omitempty"`
	GmtCreate       time.Time         `json:"gmtCreate,omitempty"`
	Domain          string            `json:"domain,omitempty"`
	//新增存储直播转码资源池tag，exp:"taskSchedulerTag":"l2"
	Metadata map[string]string `json:"metadata,omitempty"`

	//invoke参数 内容保护
	EngineEntrance map[string]string
	// 任务调度参数 jsonString
	ScheduleParams string
	// 任务dag图 jsonString 暂未持久化
	DagGraph string
}

type TaskQueryOptions struct {
	BatchSize              int
	WorkerCount            int
	EnableCursorPagination bool
	QueryType              string
}

type SimpleTask struct {
	TaskId   string
	ParamMd5 string
}

type SimpleTaskResult struct {
	TaskId  string
	DataUrl string
	Data    []byte
}

type EngineResult struct {
	Data    interface{}
	DataUrl string
	Code    uint32
	Reason  string
	Message string
}

func (t *Task) IsMixedTask() bool {
	if t.Metadata[string(MetaDataFieldMixConfig)] != "" {
		return true
	} else {
		return false
	}

}

func (t *Task) GetTaskMixedConfig() string {
	if t.IsMixedTask() {
		return t.Metadata[string(MetaDataFieldMixConfig)]
	}
	return ""
}

func (t *Task) IsTaskEnqueue() bool {
	return t.InternalStatus == TaskStatePending
}

func (t *Task) IsInternalRunning() bool {
	return t.InternalStatus == TaskStateActive
}

func (t *Task) IsInternalStopped() bool {
	return t.InternalStatus == TaskStateStopped
}

func (t *Task) IsBizStatusRunning() bool {
	return t.BizStatus == TaskStateActive
}

func (t *Task) GetBizStatus() TaskStatus {
	return t.BizStatus
}

func (t *Task) GetInternalStatus() TaskStatus {
	return t.InternalStatus
}

func (t *Task) SetBizStatus(state TaskStatus) {
	t.BizStatus = state
}

func (t *Task) SetInternalStatus(state TaskStatus) {
	t.InternalStatus = state
}

func (t *Task) IsInternalPending() bool {
	return t.InternalStatus == TaskStatePending
}

func (t *Task) IsDagDependencyTask() bool {
	return t.TaskType == DagAsyncTaskType && len(t.Dependencies) > 0
}

func (t *Task) IsDagJob() bool {
	return t.TaskType == DagAsyncTaskType
}

func (t *Task) IsTranscodeJob() bool {
	return t.Product == "ols" && t.EngineModel == LiveTranscode
}

func (t *Task) IsDagSubTask() bool {
	return t.TaskId != t.JobId || len(t.Dependencies) > 0
}

func ExceptDagJob(tasks []*Task) []*Task {
	var result []*Task
	for _, task := range tasks {
		if !task.IsDagJob() {
			result = append(result, task)
		}
	}
	return result
}

func ExceptTranscodeJob(tasks []*Task) ([]*Task, []*Task) {
	var result []*Task
	var transcodeTasks []*Task
	for _, task := range tasks {
		if task.IsTranscodeJob() {
			transcodeTasks = append(transcodeTasks, task)
		} else {
			result = append(result, task)
		}
	}
	return result, transcodeTasks
}

func (t *Task) IsAbnormalStopped() bool {
	if t.Result == nil {
		return false
	}

	return !isNormalCompleteCallback(t.Result)
}

func (t *Task) IsBizStatusStopped() bool {
	return t.BizStatus == TaskStateStopped
}

func (t *Task) ShouldReSchedule() bool {
	if t.MigrateConfig == nil {
		return true
	}

	return !t.MigrateConfig.UnMigrate && t.MigrateTimes+1 <= t.MigrateConfig.MaxRuntime
}

// 根据迁移次数，添加阶梯式延时0-1s
func (t *Task) ApplyMigrationDelay() {
	migratesTimes := t.MigrateTimes

	var baseDelay time.Duration
	switch {
	case migratesTimes <= 1:
		baseDelay = 200 * time.Millisecond
	case migratesTimes == 2:
		baseDelay = 400 * time.Millisecond
	default:
		baseDelay = 800 * time.Millisecond
	}

	// 使用随机数生成 0~maxDelay 的延迟时间
	maxRandDelay := 200 * time.Millisecond
	randomDelay := time.Duration(util.RandInt64n(int64(maxRandDelay)))
	delay := baseDelay + randomDelay
	time.Sleep(delay)
}

// 是否达到迁移次数上线
//
//	true：达到上限
//	false：未达到上限
func (t *Task) HasReachedMaxMigrations() bool {
	//迁移配置为空时，默认迁移次数无上限
	if t.MigrateConfig == nil {
		return false
	}
	if t.MigrateConfig.MaxRuntime == 0 {
		return true
	}
	return t.MigrateTimes >= t.MigrateConfig.MaxRuntime

}

func (t *Task) IsTaskOnTargetWorker(worker *Worker) bool {
	return t.Worker != nil && t.Worker.Id == worker.Id
}

func (t *Task) IsRecentlyUpdated() bool {
	currentTime := time.Now()

	timeDiff := currentTime.Sub(t.LastModified)

	return timeDiff < time.Second*3
}

func (t *Task) IsBatchTaskRecentlyUpdated() bool {
	currentTime := time.Now()

	timeDiff := currentTime.Sub(t.LastModified)

	return timeDiff < time.Second*5
}

// 检查任务是否执行时间过长
// 判断标准：创建时间大于2天 或 更新时间大于1天
func (t *Task) IsExecutionTooLong() bool {
	currentTime := time.Now()

	modifiedTimeDiff := currentTime.Sub(t.LastModified)

	gmtCreateTimeDiff := currentTime.Sub(t.GmtCreate)

	return gmtCreateTimeDiff > time.Hour*48 || modifiedTimeDiff > time.Hour*24
}

func (t *Task) EncodeToBytes() ([]byte, error) {
	return json.Marshal(t)
}

func (t *Task) EncodeLiteToBytes() ([]byte, error) {
	// 只序列化必要信息
	return json.Marshal(struct {
		Metadata   map[string]string
		GmtCreate  time.Time
		TaskId     string
		JobId      string
		UserId     string
		PipelineId string
		TaskType   TaskType
	}{
		Metadata:   t.Metadata,
		GmtCreate:  t.GmtCreate,
		TaskId:     t.TaskId,
		JobId:      t.JobId,
		UserId:     t.UserId,
		PipelineId: t.PipelineId,
		TaskType:   t.TaskType,
	})
	//return json.Marshal(t)
}

func (t *Task) EncodeKVKey() string {
	return t.TaskType.String() + ":" + t.Product + ":" + t.EngineModel + ":" + t.Tag + ":" + t.UserId + ":" + t.TaskId
}

func (t *Task) EncodeBaseElem() string {
	return t.TaskType.String() + ":" + t.Product + ":" + t.EngineModel + ":" + t.Tag
}

func (t *Task) DecodeFromBytes(data []byte) error {
	return json.Unmarshal(data, t)
}

func (t *Task) IsEngineAlreadyStopped() bool {
	return t.Result != nil && isNormalCompleteCallback(t.Result)
}

func isNormalCompleteCallback(taskResult *TaskResult) bool {
	reason := strings.ToLower(taskResult.Reason)
	abnormalReasons := []string{
		QuotaLimit,
		EngineUnavailable,
		InternalException,
		MigratedReason,
		EngineLostReason,
	}

	for _, s := range abnormalReasons {
		if strings.ToLower(s) == reason {
			return false
		}
	}
	return true
}

func (t *TaskResult) ConvertResultDataToString() (string, error) {
	if t.Data == nil {
		return "", nil
	}
	switch value := t.Data.(type) {
	case string:
		return value, nil
	case *string:
		if value == nil {
			return "", nil
		}
		return *value, nil
	default:
		return "", fmt.Errorf("unexpected type for data, expected string or *string, got %T", value)
	}
}

func (t *Task) CreateAffinity() {
	if t.RequestResource == nil {
		t.RequestResource = &RequestResource{}
	}
	if t.RequestResource.Affinity == nil {
		t.RequestResource.Affinity = &TaskAffinity{}
	}
}

func ToTaskModel(task *Task) (*domain.Task, error) {
	if task.EngineModel == "" {
		task.EngineModel = "default"
	}

	if task.Tag == "" {
		task.Tag = "default"
	}

	dbTask := &domain.Task{
		TaskId:         task.TaskId,
		JobId:          task.JobId,
		PipelineId:     task.PipelineId,
		Product:        task.Product,
		EngineModel:    task.EngineModel,
		Tag:            task.Tag,
		Notify:         task.Notify,
		UserData:       task.UserData,
		Trace:          task.Trace,
		InternalStatus: int32(task.GetInternalStatus()),
		BizStatus:      int32(task.GetBizStatus()),
		TaskType:       int32(task.TaskType),
		MigrateTimes:   task.MigrateTimes,
		GmtModified:    task.LastModified,
		GmtCreate:      task.GmtCreate,
		UserId:         task.UserId,
	}

	//resultData, err := json.Marshal(task.Result)
	//if err != nil {
	//	return nil, err
	//}
	//dbTask.Result = resultData

	requestResourceData, err := json.Marshal(task.RequestResource)
	if err != nil {
		return nil, err
	}
	dbTask.RequestResource = requestResourceData

	engineParamData, err := json.Marshal(task.Param)
	if err != nil {
		return nil, err
	}
	dbTask.EngineParam = engineParamData

	if len(task.Dependencies) > 0 {
		data, err := json.Marshal(task.Dependencies)
		if err != nil {
			return nil, err
		}
		dbTask.Dependency = data
	}

	if task.Worker != nil {
		dbTask.WorkerIp = task.Worker.Ip
		dbTask.WorkerId = task.Worker.Id
		dbTask.WorkerPort = task.Worker.Port
	}

	if task.Result != nil {
		data, err := json.Marshal(task.Result)
		if err != nil {
			return nil, err
		}
		dbTask.Result = data
	}

	if task.MigrateConfig != nil {
		data, err := json.Marshal(task.MigrateConfig)
		if err != nil {
			return nil, err
		}
		dbTask.MigrateConfig = data
	}

	if len(task.Metadata) > 0 {
		data, err := json.Marshal(task.Metadata)
		if err != nil {
			return nil, err
		}
		dbTask.Metadata = data
	}

	if task.ScheduleParams != "" {
		date, err := json.Marshal(task.ScheduleParams)
		if err != nil {
			return nil, err
		}
		dbTask.ScheduleParam = date
	}

	return dbTask, nil
}

// 用于count查询的结构
type TempQueryTask struct {
	UserId      string `gorm:"column:user_id"`
	PipelineId  string `gorm:"column:pipeline_id"`
	TaskType    int32  `gorm:"column:task_type"`
	Product     string `gorm:"column:product"`
	EngineModel string `gorm:"column:engine_model"`
	Tag         string `gorm:"column:tag"`

	// 用于流控的参数
	TaskNum          int32  `gorm:"column:task_num"`
	SentinelConfigId string `gorm:"column:sentinel_config_id"`
}

type ScheduleParams struct {
	PipelineID string           `json:"pipelineId,omitempty"`
	QuotaSet   map[string]int64 `json:"quotaSet,omitempty"`
	Priority   int              `json:"priority,omitempty"`
	//ExpectCostTime int
	ScheduleLevel string `json:"scheduleLevel,omitempty"`
	//ScheduleLevel    EnumScheduleLevel
	//ScheduleMode     EnumScheduleMode
	Inputs      []map[string]interface{} `json:"inputs,omitempty"`
	Configs     []map[string]interface{} `json:"configs,omitempty"`
	ParallelNum int
	//SlaFinishDelay  int64 // 假设以毫秒为单位，可以转换为所需的时间单位
	//SlaQueuingDelay int64 // 假设以毫秒为单位，可以转换为所需的时间单位
}

func ParseScheduleParams(scheduleParamsJson string) (*ScheduleParams, error) {
	var scheduleParams *ScheduleParams
	if scheduleParamsJson != "" {
		if err := json.Unmarshal([]byte(scheduleParamsJson), &scheduleParams); err != nil {
			return nil, err
		}
	}
	return scheduleParams, nil
}

// 是否是并行任务
// scheduleParams中参数：ParallelNum>1
func TaskTypeByScheduleParams(jobId, taskId, scheduleParamsString string) (v1.TaskType, error) {
	//默认是流式任务
	taskType := v1.TaskType_Stream
	scheduleParams, err := ParseScheduleParams(scheduleParamsString)
	if err != nil {
		return v1.TaskType_Unknown, err
	}

	//判断是否是DAG并行任务
	if scheduleParams != nil && scheduleParams.ParallelNum > 1 {
		taskType = v1.TaskType_DagAsync
	}
	return taskType, nil
}

type JobMeshData struct {
	JobId            string
	TaskMeshDataList []TaskMeshData
}

type TaskMeshData struct {
	TaskId     string
	WorkerId   string
	WorkerIp   string
	WorkerPort int
}
