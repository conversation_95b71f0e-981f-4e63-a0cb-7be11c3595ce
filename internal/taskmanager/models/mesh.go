package models

import (
	"fmt"
	v1 "proto.mpp/api/common/v1"
	"time"
)

type SetConfigRequest struct {
	// group identity
	Product     string `json:"product,omitempty"`
	EngineModel string `json:"engineModel,omitempty"`
	Tag         string `json:"tag,omitempty"`

	// task identity
	JobId  string `json:"jobId,omitempty"`
	TaskId string `json:"taskId,omitempty"`

	// user pipeline identity
	PipelineId string `json:"pipelineId,omitempty"`
	UserId     string `json:"userId,omitempty"`

	// request identity
	RequestId string    `json:"requestId,omitempty"`
	CreateAt  time.Time `json:"createAt,omitempty"`

	// TODO: change traceInfo type
	TraceInfo string `json:"trace,omitempty"`

	// key ->  value
	// usually key is task_id
	Key   string          `json:"key,omitempty"`
	Value MeshConfigValue `json:"value,omitempty"`
	//ValueUrl string      `json:"valueUrl,omitempty"`
}

type GetConfigRequest struct {
	// key ->  value
	// usually key is task_id
	Key string `json:"key,omitempty"`

	// request identity
	RequestId string    `json:"requestId,omitempty"`
	CreateAt  time.Time `json:"createAt,omitempty"`
	// TODO: change traceInfo type
	TraceInfo string `json:"trace,omitempty"`
}

type DeleteConfigRequest struct {
	// key ->  value
	// usually key is task_id
	Key string `json:"key,omitempty"`

	// request identity
	RequestId string    `json:"requestId,omitempty"`
	CreateAt  time.Time `json:"createAt,omitempty"`
	// TODO: change traceInfo type
	TraceInfo string `json:"trace,omitempty"`
}

// Config
// ConfigGroup: product/engineModel/tag
type Config struct {
	CreateAt       time.Time `json:"createAt,omitempty"`
	LastModifiedAt time.Time `json:"updateAt,omitempty"`

	// group identity
	Group       string `json:"group,omitempty"`
	Product     string `json:"product,omitempty"`
	EngineModel string `json:"engineModel,omitempty"`
	Tag         string `json:"tag,omitempty"`

	// key -> value
	Key      string          `json:"key,omitempty"`
	Value    MeshConfigValue `json:"value,omitempty"`
	ValueUrl string          `json:"valueUrl,omitempty"`

	// Task / user / pipeline identity
	JobId      string `json:"jobId,omitempty"`
	TaskId     string `json:"taskId,omitempty"`
	PipelineId string `json:"pipelineId,omitempty"`
	UserId     string `json:"userId,omitempty"`
}

// MeshConfigValue for parallel worker.
type MeshConfigValue struct {
	WorkerId string `json:"workerId,omitempty"`
	WorkerIp string `json:"workerIp,omitempty"`
	Port     int    `json:"port,omitempty"`
}

var (
	ERROR_SUCCESS               = v1.CommonReplyResult{Code: 200, Reason: "Success", Message: "Success"}
	ERROR_VALIDATE_REQ_FIALED   = v1.CommonReplyResult{Code: 400, Reason: "ValidateRequestError", Message: "validate request failed."}
	ERRPR_MESH_CONFIG_NOT_FOUND = v1.CommonReplyResult{Code: 404, Reason: "MeshConfig.NotFoundError", Message: "not found."}
	ERRPR_MESH_INTERNAL_ERROR   = v1.CommonReplyResult{Code: 500, Reason: "Mesh.InternalError", Message: "internal error."}
)

func IsSuccess(result v1.CommonReplyResult) bool {
	return result.Code == ERROR_SUCCESS.Code
}

func IsNotFound(result v1.CommonReplyResult) bool {
	return result.Code == ERRPR_MESH_CONFIG_NOT_FOUND.Code
}

func WithError(r v1.CommonReplyResult, err error) *v1.CommonReplyResult {
	r.Message += " err:" + err.Error()
	return &r
}

func WithTraceInfo(r v1.CommonReplyResult, trInfo interface{}) *v1.CommonReplyResult {
	r.Message += " traceInfo:" + fmt.Sprintf("%v", trInfo)
	return &r
}

func WithErrAndTraceInfo(r v1.CommonReplyResult, err error, trInfo interface{}) *v1.CommonReplyResult {
	r.Message += " err:" + err.Error() + " traceInfo:" + fmt.Sprintf("%v", trInfo)
	return &r
}
