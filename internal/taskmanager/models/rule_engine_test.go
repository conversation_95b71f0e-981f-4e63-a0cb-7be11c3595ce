package models

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"mpp/internal/taskmanager/models/domain"
	pb "proto.mpp/api/taskmanager/v1"
	"testing"
)

/**
 * @Author: qinxin
 * @Date: 2025/3/31 16:06
 * @Desc:
 * @Version 1.0
 */

func TestProtoToMixGrayConfig(t *testing.T) {
	// 创建一个测试的 proto 消息
	protoConfig := &pb.MixGrayConfig{
		Id:            "test-uid",
		RuleName:      "test-rule",
		Priority:      1,
		ConfigStatus:  1,
		RuleConfigs:   `[{"paramName":"test-param","operate":"==","value":"test-value","valueType":"string"}]`,
		MixRatio:      0.5,
		MixRuleConfig: `[{"product":"test-product","engineModel":"test-model","tag":"test-tag","version":"test-version","ratio":0.5,"mixAllocStrategy":"NormalMixed"}]`,
	}

	// 调用被测试的函数
	goConfig, err := ProtoToMixGrayConfig(protoConfig)
	assert.NoError(t, err)

	// 验证结果
	assert.Equal(t, "test-uid", goConfig.Uid)
	assert.Equal(t, "test-rule", goConfig.RuleName)
	assert.Equal(t, 1, goConfig.Priority)
	assert.Equal(t, MixGrayConfigStatusActive, goConfig.ConfigStatus)
	assert.Equal(t, float32(0.5), goConfig.MixRatio)
	assert.Len(t, goConfig.RuleConfigs, 1)
	assert.Len(t, goConfig.MixConfigs, 1)

}

func TestMixGrayConfigToProto(t *testing.T) {
	// 创建一个测试的 Go 结构体
	goConfig := &MixGrayConfig{
		Uid:          "test-uid",
		RuleName:     "test-rule",
		Priority:     1,
		ConfigStatus: MixGrayConfigStatusActive,
		RuleConfigs: []*MixRuleConfig{
			{
				ParamName: "test-param",
				Operate:   "==",
				Value:     "test-value",
				ValueType: "string",
			},
		},
		MixRatio: 0.5,
		MixConfigs: []*MixConfig{
			{
				Product:          "test-product",
				EngineModel:      "test-model",
				Tag:              "test-tag",
				Version:          "test-version",
				Ratio:            0.5,
				MixAllocStrategy: NormalMixed,
			},
		},
	}

	// 调用被测试的函数
	protoConfig, err := MixGrayConfigToProto(goConfig)
	assert.NoError(t, err)

	// 验证结果
	assert.Equal(t, "test-uid", protoConfig.Id)
	assert.Equal(t, "test-rule", protoConfig.RuleName)
	assert.Equal(t, int32(1), protoConfig.Priority)
	assert.Equal(t, int32(1), protoConfig.ConfigStatus)
	assert.Equal(t, float32(0.5), protoConfig.MixRatio)

	// 解析 JSON 字符串以验证 RuleConfigs 和 MixRuleConfig
	var ruleConfigs []*MixRuleConfig
	err = json.Unmarshal([]byte(protoConfig.RuleConfigs), &ruleConfigs)
	assert.NoError(t, err)
	assert.Len(t, ruleConfigs, 1)
	assert.Equal(t, "test-param", ruleConfigs[0].ParamName)

	var mixConfigs []*MixConfig
	err = json.Unmarshal([]byte(protoConfig.MixRuleConfig), &mixConfigs)
	assert.NoError(t, err)
	assert.Len(t, mixConfigs, 1)
	assert.Equal(t, "test-product", mixConfigs[0].Product)
}

func TestMixGrayConfigBizToDomain(t *testing.T) {
	// 创建一个测试的 Go 结构体
	goConfig := &MixGrayConfig{
		Uid:          "test-uid",
		RuleName:     "test-rule",
		Priority:     1,
		ConfigStatus: MixGrayConfigStatusActive,
		RuleConfigs: []*MixRuleConfig{
			{
				ParamName: "test-param",
				Operate:   "==",
				Value:     "test-value",
				ValueType: "string",
			},
		},
		MixRatio: 0.5,
		MixConfigs: []*MixConfig{
			{
				Product:          "test-product",
				EngineModel:      "test-model",
				Tag:              "test-tag",
				Version:          "test-version",
				Ratio:            0.5,
				MixAllocStrategy: NormalMixed,
			},
		},
		Extends: map[string]interface{}{
			"key": "value",
		},
	}

	// 调用被测试的函数
	domainConfig, err := MixGrayConfigBizToDomain(goConfig)
	assert.NoError(t, err)

	// 验证结果
	assert.Equal(t, "test-uid", domainConfig.Uid)
	assert.Equal(t, "test-rule", domainConfig.RuleName)
	assert.Equal(t, int32(1), domainConfig.Priority)
	assert.Equal(t, int32(1), domainConfig.ConfigStatus)
	assert.Equal(t, float32(0.5), domainConfig.MixRatio)

	// 解析 JSON 字符串以验证 RuleConfigs 和 MixConfigs
	var ruleConfigs []*MixRuleConfig
	err = json.Unmarshal(domainConfig.RuleConfigs, &ruleConfigs)
	assert.NoError(t, err)
	assert.Len(t, ruleConfigs, 1)
	assert.Equal(t, "test-param", ruleConfigs[0].ParamName)

	var mixConfigs []*MixConfig
	err = json.Unmarshal(domainConfig.MixConfigs, &mixConfigs)
	assert.NoError(t, err)
	assert.Len(t, mixConfigs, 1)
	assert.Equal(t, "test-product", mixConfigs[0].Product)

	var extends map[string]interface{}
	err = json.Unmarshal(domainConfig.Extends, &extends)
	assert.NoError(t, err)
	assert.Equal(t, map[string]interface{}{"key": "value"}, extends)
}

func TestDomainToMixGrayConfigBiz(t *testing.T) {
	// 创建一个测试的 domain 结构体
	domainConfig := &domain.MixGrayConfig{
		Uid:          "test-uid",
		RuleName:     "test-rule",
		Priority:     1,
		ConfigStatus: 1,
		RuleConfigs:  []byte(`[{"paramName":"test-param","operate":"==","value":"test-value","valueType":"string"}]`),
		MixConfigs:   []byte(`[{"product":"test-product","engineModel":"test-model","tag":"test-tag","version":"test-version","ratio":0.5,"mixAllocStrategy":"NormalMixed"}]`),
		Extends:      []byte(`{"key":"value"}`),
		MixRatio:     0.5,
	}

	// 调用被测试的函数
	goConfig, err := DomainToMixGrayConfigBiz(domainConfig)
	assert.NoError(t, err)

	// 验证结果
	assert.Equal(t, "test-uid", goConfig.Uid)
	assert.Equal(t, "test-rule", goConfig.RuleName)
	assert.Equal(t, 1, goConfig.Priority)
	assert.Equal(t, MixGrayConfigStatusActive, goConfig.ConfigStatus)
	assert.Equal(t, float32(0.5), goConfig.MixRatio)
	assert.Len(t, goConfig.RuleConfigs, 1)
	assert.Len(t, goConfig.MixConfigs, 1)
	assert.Equal(t, map[string]interface{}{"key": "value"}, goConfig.Extends)
}

func TestMixGrayConfigBizListToDomainList(t *testing.T) {
	// 创建一个测试的 Go 结构体列表
	goConfigs := []*MixGrayConfig{
		{
			Uid:          "test-uid1",
			RuleName:     "test-rule1",
			Priority:     1,
			ConfigStatus: MixGrayConfigStatusActive,
			RuleConfigs: []*MixRuleConfig{
				{
					ParamName: "test-param1",
					Operate:   "==",
					Value:     "test-value1",
					ValueType: "string",
				},
			},
			MixRatio: 0.5,
			MixConfigs: []*MixConfig{
				{
					Product:          "test-product1",
					EngineModel:      "test-model1",
					Tag:              "test-tag1",
					Version:          "test-version1",
					Ratio:            0.5,
					MixAllocStrategy: NormalMixed,
				},
			},
			Extends: map[string]interface{}{
				"key1": "value1",
			},
		},
		{
			Uid:          "test-uid2",
			RuleName:     "test-rule2",
			Priority:     2,
			ConfigStatus: MixGrayConfigStatusPause,
			RuleConfigs: []*MixRuleConfig{
				{
					ParamName: "test-param2",
					Operate:   "!=",
					Value:     "test-value2",
					ValueType: "integer",
				},
			},
			MixRatio: 0.3,
			MixConfigs: []*MixConfig{
				{
					Product:          "test-product2",
					EngineModel:      "test-model2",
					Tag:              "test-tag2",
					Version:          "test-version2",
					Ratio:            0.3,
					MixAllocStrategy: TryAbandonMixed,
				},
			},
			Extends: map[string]interface{}{
				"key2": "value2",
			},
		},
	}

	// 调用被测试的函数
	domainConfigs, err := MixGrayConfigBizListToDomainList(goConfigs)
	assert.NoError(t, err)

	// 验证结果
	assert.Len(t, domainConfigs, 2)

	// 验证第一个配置
	assert.Equal(t, "test-uid1", domainConfigs[0].Uid)
	assert.Equal(t, "test-rule1", domainConfigs[0].RuleName)
	assert.Equal(t, int32(1), domainConfigs[0].Priority)
	assert.Equal(t, int32(1), domainConfigs[0].ConfigStatus)
	assert.Equal(t, float32(0.5), domainConfigs[0].MixRatio)

	// 解析 JSON 字符串以验证 RuleConfigs 和 MixConfigs
	var ruleConfigs1 []*MixRuleConfig
	err = json.Unmarshal(domainConfigs[0].RuleConfigs, &ruleConfigs1)
	assert.NoError(t, err)
	assert.Len(t, ruleConfigs1, 1)
	assert.Equal(t, "test-param1", ruleConfigs1[0].ParamName)

	var mixConfigs1 []*MixConfig
	err = json.Unmarshal(domainConfigs[0].MixConfigs, &mixConfigs1)
	assert.NoError(t, err)
	assert.Len(t, mixConfigs1, 1)
	assert.Equal(t, "test-product1", mixConfigs1[0].Product)

	var extends1 map[string]interface{}
	err = json.Unmarshal(domainConfigs[0].Extends, &extends1)
	assert.NoError(t, err)
	assert.Equal(t, map[string]interface{}{"key1": "value1"}, extends1)

	// 验证第二个配置
	assert.Equal(t, "test-uid2", domainConfigs[1].Uid)
	assert.Equal(t, "test-rule2", domainConfigs[1].RuleName)
	assert.Equal(t, int32(2), domainConfigs[1].Priority)
	assert.Equal(t, int32(2), domainConfigs[1].ConfigStatus)
	assert.Equal(t, float32(0.3), domainConfigs[1].MixRatio)

	// 解析 JSON 字符串以验证 RuleConfigs 和 MixConfigs
	var ruleConfigs2 []*MixRuleConfig
	err = json.Unmarshal(domainConfigs[1].RuleConfigs, &ruleConfigs2)
	assert.NoError(t, err)
	assert.Len(t, ruleConfigs2, 1)
	assert.Equal(t, "test-param2", ruleConfigs2[0].ParamName)

	var mixConfigs2 []*MixConfig
	err = json.Unmarshal(domainConfigs[1].MixConfigs, &mixConfigs2)
	assert.NoError(t, err)
	assert.Len(t, mixConfigs2, 1)
	assert.Equal(t, "test-product2", mixConfigs2[0].Product)

	var extends2 map[string]interface{}
	err = json.Unmarshal(domainConfigs[1].Extends, &extends2)
	assert.NoError(t, err)
	assert.Equal(t, map[string]interface{}{"key2": "value2"}, extends2)
}

func TestDomainListToMixGrayConfigBizList(t *testing.T) {
	// 创建一个测试的 domain 结构体列表
	domainConfigs := []*domain.MixGrayConfig{
		{
			Uid:          "test-uid1",
			RuleName:     "test-rule1",
			Priority:     1,
			ConfigStatus: 1,
			RuleConfigs:  []byte(`[{"paramName":"test-param1","operate":"==","value":"test-value1","valueType":"string"}]`),
			MixConfigs:   []byte(`[{"product":"test-product1","engineModel":"test-model1","tag":"test-tag1","version":"test-version1","ratio":0.5,"mixAllocStrategy":"NormalMixed"}]`),
			Extends:      []byte(`{"key1":"value1"}`),
			MixRatio:     0.5,
		},
		{
			Uid:          "test-uid2",
			RuleName:     "test-rule2",
			Priority:     2,
			ConfigStatus: 2,
			RuleConfigs:  []byte(`[{"paramName":"test-param2","operate":"!=","value":"test-value2","valueType":"integer"}]`),
			MixConfigs:   []byte(`[{"product":"test-product2","engineModel":"test-model2","tag":"test-tag2","version":"test-version2","ratio":0.3,"mixAllocStrategy":"TryAbandonMixed"}]`),
			Extends:      []byte(`{"key2":"value2"}`),
			MixRatio:     0.3,
		},
	}

	// 调用被测试的函数
	goConfigs, err := DomainListToMixGrayConfigBizList(domainConfigs)
	assert.NoError(t, err)

	// 验证结果
	assert.Len(t, goConfigs, 2)

	// 验证第一个配置
	assert.Equal(t, "test-uid1", goConfigs[0].Uid)
	assert.Equal(t, "test-rule1", goConfigs[0].RuleName)
	assert.Equal(t, 1, goConfigs[0].Priority)
	assert.Equal(t, MixGrayConfigStatusActive, goConfigs[0].ConfigStatus)
	assert.Equal(t, float32(0.5), goConfigs[0].MixRatio)
	assert.Len(t, goConfigs[0].RuleConfigs, 1)
	assert.Len(t, goConfigs[0].MixConfigs, 1)
	assert.Equal(t, map[string]interface{}{"key1": "value1"}, goConfigs[0].Extends)

	// 验证第二个配置
	assert.Equal(t, "test-uid2", goConfigs[1].Uid)
	assert.Equal(t, "test-rule2", goConfigs[1].RuleName)
	assert.Equal(t, 2, goConfigs[1].Priority)
	assert.Equal(t, MixGrayConfigStatusPause, goConfigs[1].ConfigStatus)
	assert.Equal(t, float32(0.3), goConfigs[1].MixRatio)
	assert.Len(t, goConfigs[1].RuleConfigs, 1)
	assert.Len(t, goConfigs[1].MixConfigs, 1)
	assert.Equal(t, map[string]interface{}{"key2": "value2"}, goConfigs[1].Extends)
}
