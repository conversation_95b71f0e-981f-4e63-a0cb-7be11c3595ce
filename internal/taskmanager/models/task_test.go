package models

import (
	"encoding/json"
	"fmt"
	"github.com/stretchr/testify/assert"
	pb "proto.mpp/api/taskmanager/v1"
	"reflect"
	"strings"
	"testing"
	"time"
)

/**
 * @Author: qinxin
 * @Date: 2024/9/26 12:09
 * @Desc:
 * @Version 1.0
 */

// TestIsTaskEnqueue 测试案例：
// TC1 - 正常情况：内部状态为待处理（TaskStatePending），期望返回true。
// TC2 - 异常情况：内部状态不为待处理，期望返回false。
func TestIsTaskEnqueue(t *testing.T) {
	task := &Task{InternalStatus: TaskStatePending}
	testCases := []struct {
		name     string
		task     *Task
		expected bool
	}{
		{"正常情况", task, true},
		{"异常情况", &Task{InternalStatus: TaskStateActive}, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			var result = tc.task.IsTaskEnqueue()
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestIsInternalRunning 测试案例：
// TC1 - 正常情况：内部状态为活动（TaskStateActive），期望返回true。
// TC2 - 异常情况：内部状态不是活动，期望返回false。
func TestIsInternalRunning(t *testing.T) {
	task := &Task{InternalStatus: TaskStateActive}
	testCases := []struct {
		name     string
		task     *Task
		expected bool
	}{
		{"正常情况", task, true},
		{"异常情况", &Task{InternalStatus: TaskStatePending}, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.task.IsInternalRunning()
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestIsInternalStopped 测试案例：
// TC1 - 正常情况：内部状态为停止（TaskStateStopped），期望返回true。
// TC2 - 异常情况：内部状态不是停止，期望返回false。
func TestIsInternalStopped(t *testing.T) {
	task := &Task{InternalStatus: TaskStateStopped}
	testCases := []struct {
		name     string
		task     *Task
		expected bool
	}{
		{"正常情况", task, true},
		{"异常情况", &Task{InternalStatus: TaskStateActive}, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.task.IsInternalStopped()
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestIsBizStatusRunning 测试案例：
// TC1 - 正常情况：业务状态为活动（TaskStateActive），期望返回true。
// TC2 - 异常情况：业务状态不是活动，期望返回false。
func TestIsBizStatusRunning(t *testing.T) {
	task := &Task{BizStatus: TaskStateActive}
	testCases := []struct {
		name     string
		task     *Task
		expected bool
	}{
		{"正常情况", task, true},
		{"异常情况", &Task{BizStatus: TaskStatePending}, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.task.IsBizStatusRunning()
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestGetBizStatus 测试案例：
// TC1 - 获取业务状态，验证返回值正确性。
func TestGetBizStatus(t *testing.T) {
	task := &Task{BizStatus: TaskStateActive}
	expected := TaskStateActive
	result := task.GetBizStatus()
	assert.Equal(t, expected, result)
}

// TestGetInternalStatus 测试案例：
// TC1 - 获取内部状态，验证返回值正确性。
func TestGetInternalStatus(t *testing.T) {
	task := &Task{InternalStatus: TaskStateActive}
	expected := TaskStateActive
	result := task.GetInternalStatus()
	assert.Equal(t, expected, result)
}

// TestSetBizStatus 测试案例：
// TC1 - 设置并获取业务状态，验证设置后返回值正确性。
func TestSetBizStatus(t *testing.T) {
	task := &Task{}
	newStatus := TaskStateActive
	task.SetBizStatus(newStatus)
	result := task.GetBizStatus()
	assert.Equal(t, newStatus, result)
}

// TestSetInternalStatus 测试案例：
// TC1 - 设置并获取内部状态，验证设置后返回值正确性。
func TestSetInternalStatus(t *testing.T) {
	task := &Task{}
	newStatus := TaskStateActive
	task.SetInternalStatus(newStatus)
	result := task.GetInternalStatus()
	assert.Equal(t, newStatus, result)
}

// TestIsInternalPending 测试案例：
// TC1 - 正常情况：内部状态为待处理（TaskStatePending），期望返回true。
// TC2 - 异常情况：内部状态不是待处理，期望返回false。
func TestIsInternalPending(t *testing.T) {
	task := &Task{InternalStatus: TaskStatePending}
	testCases := []struct {
		name     string
		task     *Task
		expected bool
	}{
		{"正常情况", task, true},
		{"异常情况", &Task{InternalStatus: TaskStateActive}, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.task.IsInternalPending()
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestIsDagDependencyTask 测试案例：
// TC1 - 正常情况：类型为DagAsync且存在依赖项，期望返回true。
// TC2 - 异常情况：类型非DagAsync或不存在依赖项，期望返回false。
func TestIsDagDependencyTask(t *testing.T) {
	dagTask := &Task{TaskType: DagAsyncTaskType, Dependencies: []Dependency{{}}}
	nonDagTask := &Task{TaskType: SimpleTaskType}
	noDependenceTask := &Task{TaskType: DagAsyncTaskType}
	testCases := []struct {
		name     string
		task     *Task
		expected bool
	}{
		{"正常情况", dagTask, true},
		{"异常情况类型非DagAsync", nonDagTask, false},
		{"异常情况无依赖项", noDependenceTask, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.task.IsDagDependencyTask()
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestIsAbnormalStopped 测试案例：
// TC1 - 正常情况：回调结果包含异常原因，期望返回true。
// TC2 - 异常情况：回调结果正常，期望返回false。
func TestIsAbnormalStopped(t *testing.T) {
	normalResult := &TaskResult{Reason: ""}
	abnormalResult := &TaskResult{Reason: QuotaLimit}
	taskWithNormalResult := &Task{Result: normalResult}
	taskWithAbnormalResult := &Task{Result: abnormalResult}
	testCases := []struct {
		name     string
		task     *Task
		expected bool
	}{
		{"正常情况", taskWithAbnormalResult, true},
		{"异常情况", taskWithNormalResult, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.task.IsAbnormalStopped()
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestIsBizStatusStopped 测试案例：
// TC1 - 正常情况：业务状态为停止（TaskStateStopped），期望返回true。
// TC2 - 异常情况：业务状态不是停止，期望返回false。
func TestIsBizStatusStopped(t *testing.T) {
	task := &Task{BizStatus: TaskStateStopped}
	testCases := []struct {
		name     string
		task     *Task
		expected bool
	}{
		{"正常情况", task, true},
		{"异常情况", &Task{BizStatus: TaskStateActive}, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.task.IsBizStatusStopped()
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestShouldReSchedule 测试案例：
// TC1 - 正常情况：迁移次数小于最大运行次数，期望返回true。
// TC2 - 异常情况：迁移次数等于或超过最大运行次数，期望返回false。
func TestShouldReSchedule(t *testing.T) {
	migrateConfig := &pb.MigrateConfig{MaxRuntime: 5}
	taskUnderLimit := &Task{MigrateConfig: migrateConfig, MigrateTimes: 3}
	taskAtLimit := &Task{MigrateConfig: migrateConfig, MigrateTimes: 5}
	testCases := []struct {
		name     string
		task     *Task
		expected bool
	}{
		{"正常情况", taskUnderLimit, true},
		{"异常情况", taskAtLimit, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.task.ShouldReSchedule()
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestHasReachedMaxMigrations 测试案例：
// TC1 - 达到迁移次数上限，期望返回true。
// TC2 - 尚未达到迁移次数上限，期望返回false。
func TestHasReachedMaxMigrations(t *testing.T) {
	migrateConfig := &pb.MigrateConfig{MaxRuntime: 5}
	taskAtLimit := &Task{MigrateConfig: migrateConfig, MigrateTimes: 5}
	taskBelowLimit := &Task{MigrateConfig: migrateConfig, MigrateTimes: 3}
	testCases := []struct {
		name     string
		task     *Task
		expected bool
	}{
		{"达到迁移次数上限", taskAtLimit, true},
		{"尚未达到迁移次数上限", taskBelowLimit, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.task.HasReachedMaxMigrations()
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestIsTaskOnTargetWorker 测试案例：
// TC1 - 工作者ID匹配，期望返回true。
// TC2 - 工作者ID不匹配，期望返回false。
func TestIsTaskOnTargetWorker(t *testing.T) {
	worker := &Worker{Id: "test"}
	taskOnTarget := &Task{Worker: worker}
	taskOffTarget := &Task{Worker: &Worker{Id: "other"}}
	testCases := []struct {
		name     string
		task     *Task
		worker   *Worker
		expected bool
	}{
		{"工作者ID匹配", taskOnTarget, worker, true},
		{"工作者ID不匹配", taskOffTarget, worker, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.task.IsTaskOnTargetWorker(tc.worker)
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestIsRecentlyUpdated 测试案例：
// TC1 - 最近更新时间距离当前时间不足两秒，期望返回true。
// TC2 - 最近更新时间距离当前时间超过两秒，期望返回false。
func TestIsRecentlyUpdated(t *testing.T) {
	now := time.Now()
	taskRecentUpdate := &Task{LastModified: now.Add(-time.Second)}
	taskOldUpdate := &Task{LastModified: now.Add(-time.Second * 3)}
	testCases := []struct {
		name     string
		task     *Task
		expected bool
	}{
		{"最近更新时间不足两秒", taskRecentUpdate, true},
		{"最近更新时间超过两秒", taskOldUpdate, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.task.IsRecentlyUpdated()
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestIsExecutionTooLong 测试案例：
// TC1 - 创建时间超过两天，期望返回true。
// TC2 - 创建时间不超过两天，期望返回false。
// TC3 - 更新时间超过一天，期望返回true。
// TC4 - 更新时间不超过一天，期望返回false。
func TestIsExecutionTooLong(t *testing.T) {
	now := time.Now()
	taskCreatedTooLongAgo := &Task{GmtCreate: now.Add(-time.Hour * 49), LastModified: time.Now()}
	taskNotCreatedTooLongAgo := &Task{GmtCreate: now.Add(-time.Hour * 47), LastModified: time.Now()}
	taskModifiedTooLongAgo := &Task{LastModified: now.Add(-time.Hour * 25), GmtCreate: now.Add(-time.Hour * 25)}
	taskNotModifiedTooLongAgo := &Task{LastModified: now.Add(-time.Hour * 23), GmtCreate: now.Add(-time.Hour * 23)}
	testCases := []struct {
		name     string
		task     *Task
		expected bool
	}{
		{"创建时间超过两天", taskCreatedTooLongAgo, true},
		{"创建时间不超过两天", taskNotCreatedTooLongAgo, false},
		{"更新时间超过一天", taskModifiedTooLongAgo, true},
		{"更新时间不超过一天", taskNotModifiedTooLongAgo, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.task.IsExecutionTooLong()
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestEncodeToBytes 测试案例：
// TC1 - 成功编码为字节切片，期望没有错误发生。
func TestEncodeToBytes(t *testing.T) {
	task := &Task{}
	data, err := task.EncodeToBytes()
	assert.Nil(t, err)
	fmt.Println(string(data)) // 打印数据以便检查
}

// TestInt 方法用于测试 TaskStatus 的 Int 方法是否正确返回对应的整数值。
// 测试用例：
// TC1 - 测试 active 状态转换成整数
// TC2 - 测试 stopped 状态转换成整数
// TC3 - 测试 pending 状态转换成整数
// TC4 - 测试 scheduled 状态转换成整数
// TC5 - 测试 retry 状态转换成整数
// TC6 - 测试 archived 状态转换成整数
// TC7 - 测试 completed 状态转换成整数
// TC8 - 测试 aggregating 状态转换成整数
func TestInt(t *testing.T) {
	testCases := []struct {
		name     string
		status   TaskStatus
		expected int
	}{
		{"TC1", TaskStateActive, 1},
		{"TC2", TaskStateStopped, 2},
		{"TC3", TaskStatePending, 3},
		{"TC4", TaskStateScheduled, 4},
		{"TC5", TaskStateRetry, 5},
		{"TC6", TaskStateArchived, 6},
		{"TC7", TaskStateCompleted, 7},
		{"TC8", TaskStateAggregating, 8},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.status.Int()
			if result != tc.expected {
				t.Errorf("Expected %d but got %d", tc.expected, result)
			}
		})
	}
}

// TestString 方法用于测试 TaskStatus 的 String 方法是否正确返回对应的状态字符串。
// 测试用例：
// TC1 - 测试整数 1 转换成 active 字符串
// TC2 - 测试整数 2 转换成 stopped 字符串
// TC3 - 测试整数 3 转换成 pending 字符串
// TC4 - 测试整数 4 转换成 scheduled 字符串
// TC5 - 测试整数 5 转换成 retry 字符串
// TC6 - 测试整数 6 转换成 archived 字符串
// TC7 - 测试整数 7 转换成 completed 字符串
// TC8 - 测试整数 8 转换成 aggregating 字符串
func TestString(t *testing.T) {
	testCases := []struct {
		name     string
		status   TaskStatus
		expected string
	}{
		{"TC1", TaskStateActive, "active"},
		{"TC2", TaskStateStopped, "stopped"},
		{"TC3", TaskStatePending, "pending"},
		{"TC4", TaskStateScheduled, "scheduled"},
		{"TC5", TaskStateRetry, "retry"},
		{"TC6", TaskStateArchived, "archived"},
		{"TC7", TaskStateCompleted, "completed"},
		{"TC8", TaskStateAggregating, "aggregating"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.status.String()
			if result != tc.expected {
				t.Errorf("Expected '%s' but got '%s'", tc.expected, result)
			}
		})
	}
}

// TestTaskStateFromString 方法用于测试 TaskStateFromString 函数是否能正确地从字符串转换到 TaskStatus 枚举值。
// 测试用例：
// TC1 - 测试字符串 "active" 转换成功
// TC2 - 测试字符串 "stopped" 转换成功
// TC3 - 测试字符串 "pending" 转换成功
// TC4 - 测试字符串 "scheduled" 转换成功
// TC5 - 测试字符串 "retry" 转换成功
// TC6 - 测试字符串 "archived" 转换成功
// TC7 - 测试字符串 "completed" 转换成功
// TC8 - 测试字符串 "aggregating" 转换成功
// TC9 - 测试未知状态字符串应返回错误
func TestTaskStateFromString(t *testing.T) {
	testCases := []struct {
		name        string
		input       string
		expected    TaskStatus
		expectError bool
	}{
		{"TC1", "active", TaskStateActive, false},
		{"TC2", "stopped", TaskStateStopped, false},
		{"TC3", "pending", TaskStatePending, false},
		{"TC4", "scheduled", TaskStateScheduled, false},
		{"TC5", "retry", TaskStateRetry, false},
		{"TC6", "archived", TaskStateArchived, false},
		{"TC7", "completed", TaskStateCompleted, false},
		{"TC8", "aggregating", TaskStateAggregating, false},
		{"TC9", "invalid", 0, true},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := TaskStateFromString(tc.input)
			if tc.expectError {
				if err == nil {
					t.Errorf("Expected an error for invalid input")
				}
			} else {
				if err != nil || result != tc.expected {
					t.Errorf("Unexpected error or wrong TaskStatus returned")
				}
			}
		})
	}
}

// TestTaskStateFromInt 方法用于测试 TaskStateFromInt 函数是否能正确地从整数转换到 TaskStatus 枚举值。
// 测试用例：
// TC1 - 测试整数 1 转换成功
// TC2 - 测试整数 2 转换成功
// TC3 - 测试整数 3 转换成功
// TC4 - 测试整数 4 转换成功
// TC5 - 测试整数 5 转换成功
// TC6 - 测试整数 6 转换成功
// TC7 - 测试整数 7 转换成功
// TC8 - 测试整数 8 转换成功
// TC9 - 测试无效整数应返回错误
func TestTaskStateFromInt(t *testing.T) {
	testCases := []struct {
		name        string
		input       int
		expected    TaskStatus
		expectError bool
	}{
		{"TC1", 1, TaskStateActive, false},
		{"TC2", 2, TaskStateStopped, false},
		{"TC3", 3, TaskStatePending, false},
		{"TC4", 4, TaskStateScheduled, false},
		{"TC5", 5, TaskStateRetry, false},
		{"TC6", 6, TaskStateArchived, false},
		{"TC7", 7, TaskStateCompleted, false},
		{"TC8", 8, TaskStateAggregating, false},
		{"TC9", 0, 0, true},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := TaskStateFromInt(tc.input)
			if tc.expectError {
				if err == nil {
					t.Errorf("Expected an error for invalid input")
				}
			} else {
				if err != nil || result != tc.expected {
					t.Errorf("Unexpected error or wrong TaskStatus returned")
				}
			}
		})
	}
}

// 测试EncodeLiteToBytes方法
// 测试案例1 - 正常情况下的序列化
// 测试案例2 - 包含空值的情况
func TestTask_EncodeLiteToBytes(t *testing.T) {
	task := &Task{
		Metadata:   map[string]string{"key": "value"},
		GmtCreate:  time.Now(),
		TaskId:     "taskId",
		JobId:      "jobId",
		UserId:     "userId",
		PipelineId: "pipelineId",
		TaskType:   TaskType(0),
	}

	cases := []struct {
		name string
		task *Task
		want []byte
		err  error
	}{
		{"正常情况", task, nil, nil},
		{"包含空值", &Task{}, nil, nil},
	}

	for _, c := range cases {
		got, err := c.task.EncodeLiteToBytes()
		//if !reflect.DeepEqual(err, c.err) || !reflect.DeepEqual(got, c.want) {
		//	t.Errorf("case %s failed: expected (%v, %v), got (%v, %v)", c.name, c.want, c.err, got, err)
		//}
		if err != nil {
			t.Errorf("case %s success: got %v", c.name, got)
		}
	}
}

// 测试EncodeKVKey方法
// 测试案例1 - 正常情况下的键编码
// 测试案例2 - 字段为空的情况
func TestTask_EncodeKVKey(t *testing.T) {
	task := &Task{
		TaskType:    TaskType(0),
		Product:     "product",
		EngineModel: "model",
		Tag:         "tag",
		UserId:      "userId",
		TaskId:      "taskId",
	}

	cases := []struct {
		name string
		task *Task
		want string
	}{
		{"正常情况", task, "Unknown:product:model:tag:userId:taskId"},
		{"字段为空", &Task{}, "Unknown:::::"},
	}

	for _, c := range cases {
		got := c.task.EncodeKVKey()
		if got != c.want {
			t.Errorf("case %s failed: expected %v, got %v", c.name, c.want, got)
		}
	}
}

// 测试EncodeBaseElem方法
// 测试案例1 - 正常情况下的基础元素编码
// 测试案例2 - 字段为空的情况
func TestTask_EncodeBaseElem(t *testing.T) {
	task := &Task{
		TaskType:    TaskType(0),
		Product:     "product",
		EngineModel: "model",
		Tag:         "tag",
	}

	cases := []struct {
		name string
		task *Task
		want string
	}{
		{"正常情况", task, "Unknown:product:model:tag"},
		{"字段为空", &Task{}, "Unknown:::"},
	}

	for _, c := range cases {
		got := c.task.EncodeBaseElem()
		if got != c.want {
			t.Errorf("case %s failed: expected %v, got %v", c.name, c.want, got)
		}
	}
}

// 测试DecodeFromBytes方法
// 测试案例1 - 正常情况下的反序列化
// 测试案例2 - 数据不完整或错误的情况
func TestTask_DecodeFromBytes(t *testing.T) {
	task := &Task{}
	data, _ := json.Marshal(task)

	cases := []struct {
		name        string
		data        []byte
		want        error
		isExpectErr bool
	}{
		{"正常情况", data, nil, false},
		{"数据不完整或错误", []byte(`invalid`), nil, true},
	}

	for _, c := range cases {
		err := task.DecodeFromBytes(c.data)
		if err != nil && !c.isExpectErr {
			t.Errorf("case %s failed: expected %v, got %v", c.name, c.want, err)
		}
	}
}

// 测试IsEngineAlreadyStopped方法
// 测试案例1 - 引擎已经停止的情况
// 测试案例2 - 引擎未停止的情况
func TestTask_IsEngineAlreadyStopped(t *testing.T) {
	task := &Task{Result: &TaskResult{}}

	cases := []struct {
		name string
		task *Task
		want bool
	}{
		{"引擎已经停止", task, true},
		{"引擎未停止", &Task{}, false},
	}

	for _, c := range cases {
		got := c.task.IsEngineAlreadyStopped()
		if got != c.want {
			t.Errorf("case %s failed: expected %v, got %v", c.name, c.want, got)
		}
	}
}

// TestConvertResultDataToString 测试用例：
// 案例1: 数据为空
// 案例2: 数据为字符串
// 案例3: 数据为指向字符串指针
// 案例4: 数据类型不符合预期
func TestConvertResultDataToString(t *testing.T) {
	taskResult := TaskResult{}

	// 案例1: 数据为空
	_, err := taskResult.ConvertResultDataToString()
	if err != nil {
		t.Errorf("当 Data 为空时应返回无错误")
	}

	// 准备数据
	validString := "valid string"
	validPtrString := &validString

	// 案例2: 数据为字符串
	taskResult.Data = validString
	result, _ := taskResult.ConvertResultDataToString()
	if result != validString {
		t.Errorf("转换失败，期望得到 '%s', 实际得到 '%s'", validString, result)
	}

	// 案例3: 数据为指向字符串指针
	taskResult.Data = validPtrString
	result, _ = taskResult.ConvertResultDataToString()
	if result != validString {
		t.Errorf("转换失败，期望得到 '%s', 实际得到 '%s'", validString, result)
	}

	// 案例4: 数据类型不符合预期
	invalidData := 123
	taskResult.Data = invalidData
	_, err = taskResult.ConvertResultDataToString()
	if err == nil || !strings.Contains(err.Error(), "unexpected type") {
		t.Errorf("对于非字符串或非字符串指针的数据应该报错 'unexpected type'")
	}
}

// TestCreateAffinity 测试用例：
// 案例1: RequestResource 和 Affinity 均为空
// 案例2: RequestResource 不为空但 Affinity 为空
// 案例3: RequestResource 和 Affinity 均不为空
func TestCreateAffinity(t *testing.T) {
	task := Task{}

	// 案例1: RequestResource 和 Affinity 均为空
	task.CreateAffinity()
	if task.RequestResource == nil || task.RequestResource.Affinity == nil {
		t.Errorf("创建关联后 RequestResource 或其下的 Affinity 应该被初始化")
	}

	// 初始化 RequestResource
	task.RequestResource = &RequestResource{}

	// 案例2: RequestResource 不为空但 Affinity 为空
	task.CreateAffinity()
	if task.RequestResource.Affinity == nil {
		t.Errorf("创建关联后 Affinity 应该被初始化")
	}

	// 初始化 Affinity
	task.RequestResource.Affinity = &TaskAffinity{}

	// 案例3: RequestResource 和 Affinity 均不为空
	task.CreateAffinity()
	if task.RequestResource.Affinity == nil {
		t.Errorf("已有 Affinity 的情况下不应改变")
	}
}

// TestParseScheduleParams 测试 ParseScheduleParams 方法
// 案例1: 正常解析JSON字符串 (TC_001)
// 案例2: 解析空字符串 (TC_002)
// 案例3: JSON字符串格式错误 (TC_003)
func TestParseScheduleParams(t *testing.T) {
	scheduleParams := &ScheduleParams{PipelineID: "test"}
	tests := []struct {
		name           string
		input          string
		expectedOutput *ScheduleParams
		expectError    bool
	}{
		{"正常解析JSON字符串", `{"pipelineId":"test"}`, scheduleParams, false}, // TC_001
		{"解析空字符串", "", nil, false},                                      // TC_002
		{"JSON字符串格式错误", `{invalid`, nil, true},                          // TC_003
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			output, err := ParseScheduleParams(tt.input)
			if tt.expectError {
				if err == nil {
					t.Errorf("对于输入 %s, 期望出现错误但没有发生", tt.input)
				}
			} else {
				if err != nil {
					t.Errorf("对于输入 %s, 不期望出现错误但是发生了: %v", tt.input, err)
				}
				//if output != tt.expectedOutput {
				//	t.Errorf("对于输入 %s, 期望输出 %v, 实际得到 %v", tt.input, tt.expectedOutput, output)
				//}
				if !reflect.DeepEqual(output, tt.expectedOutput) {
					t.Errorf("对于输入 %s, 期望输出 %v, 实际得到 %v", tt.input, tt.expectedOutput, output)
				}
			}
		})
	}
}

func TestExceptTranscodeJob(t *testing.T) {
	tasks := []*Task{
		{
			TaskType:    TaskType(0),
			Product:     "ols",
			EngineModel: "livetranscode",
			Tag:         "tag",
			UserId:      "userId",
			TaskId:      "taskId",
		},
		{
			TaskType:    TaskType(1),
			Product:     "product",
			EngineModel: "model",
			Tag:         "tag",
			UserId:      "userId",
		},
	}
	transcodeTasks, normalTasks := ExceptTranscodeJob(tasks)
	assert.Equal(t, 1, len(transcodeTasks))
	assert.Equal(t, 1, len(normalTasks))
}

func TestToTaskModel(t *testing.T) {
	// 创建一个 Task 实例并填充字段
	task := &Task{
		Product:        "example_product",
		TaskId:         "task_12345",
		JobId:          "job_67890",
		UserId:         "user_abcde",
		PipelineId:     "pipeline_xyz",
		BizStatus:      1,
		InternalStatus: 1,
		Worker: &Worker{
			Id:          "worker_001",
			Port:        8080,
			Ip:          "***********",
			EngineModel: "example_engine",
			Tag:         "example_tag",
			Product:     "example_product",
			ConnectMode: []string{"mode1", "mode2"},
		},
		UserData: "example_user_data",
		Param: &TaskEngineParam{
			Param:    "example_param",
			ParamUrl: "http://example.com/param",
		},
		EngineModel:    "example_engine_model",
		Tag:            "example_tag",
		Trace:          "trace_12345",
		ScheduleParams: "{\"pipelineId\":\"test\"}",
		RequestResource: &RequestResource{
			Quota: map[string]int64{
				"quota1": 100,
				"quota2": 200,
			},
			Affinity: nil,
		},
		Result: &TaskResult{
			Data: "example_result",
		},
		MigrateConfig: defaultMigrateConfig,
		Metadata: map[string]string{"key1": "value1",
			"key2": "value2",
		},
	}
	ta, err := ToTaskModel(task)
	assert.Nil(t, err)
	assert.Equal(t, ta.Product, task.Product)
}
