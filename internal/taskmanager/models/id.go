package models

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/google/uuid"
	"github.com/lithammer/shortuuid/v4"
	gonanoid "github.com/matoous/go-nanoid/v2"
	"mpp/internal/taskmanager/conf"
	v1 "proto.mpp/api/common/v1"
)

var (
	clusterInfo *conf.Cluster
)

func SetClusterId(c *conf.Cluster) {
	clusterInfo = c
}

func NewShortID() string {
	id, err := gonanoid.New()
	if err != nil {
		return shortuuid.New()
	}
	return id
}

func NewUUIDString() string {
	return strings.Replace(uuid.NewString(), "-", "", -1)
}

func NewTaskID(taskType v1.TaskType) string {
	return NewTaskIDWithTraceID(taskType, "")
}

func NewTaskIDWithTraceID(taskType v1.TaskType, traceId string) string {
	if traceId == "" || strings.HasPrefix(traceId, "00") || len(traceId) > 40 {
		traceId = NewShortID()
	}

	if clusterInfo == nil {
		return fmt.Sprintf("%s%d-%s", "00", taskType, traceId)
	}

	return fmt.Sprintf("%s%d-%s", clusterInfo.Id, taskType, traceId)
}

func GetTaskTypeFromID(taskId string) v1.TaskType {
	ss := strings.Split(taskId, "-")
	if len(ss) > 1 && len(ss[0]) == 3 {
		num, err := strconv.Atoi(ss[0][2:3])
		if err != nil {
			return v1.TaskType_Unknown
		}
		_, ok := v1.TaskType_name[int32(num)]
		if ok {
			return v1.TaskType(num)
		}
	}

	return v1.TaskType_Unknown
}

func GetTaskTypeFromProduct(product string) v1.TaskType {
	return v1.TaskType_Unknown
}

func GetTaskTypeFromProductIDOrTaskID(productId string, taskId string) v1.TaskType {
	if GetTaskTypeFromProduct(productId) == v1.TaskType_Unknown {
		return GetTaskTypeFromID(taskId)
	}

	return GetTaskTypeFromProduct(productId)
}
