package models

import (
	"encoding/json"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	v1 "proto.mpp/api/common/v1"
)

type TaskAffinity struct {
	Required  []*Affinity `json:"Required,omitempty"`
	Preferred []*Affinity `json:"Preferred,omitempty"`
}

type Affinity struct {
	Key      string
	Operator string
	Values   []string
}
type AffinityKey string

type K8sAffinity struct {
	Required  []*metav1.LabelSelector
	Preferred []*metav1.LabelSelector
}

const (
	AffinityKey_KEY_WORKER_ID = AffinityKey("workerId")
	AffinityKey_KEY_WORKER_IP = AffinityKey("workerIp")
)

func NewAffinity(key string, operator string, values []string) *Affinity {
	if key == "" || operator == "" {
		return nil
	}
	return &Affinity{
		Key:      key,
		Operator: operator,
		Values:   values,
	}
}
func NewTaskAffinity(required, preferred []*Affinity) *TaskAffinity {

	return &TaskAffinity{
		Required:  required,
		Preferred: preferred,
	}
}

func StringToV1TaskAffinity(s string) *v1.TaskAffinity {
	var v1TaskAffinity v1.TaskAffinity
	err := json.Unmarshal([]byte(s), &v1TaskAffinity)
	if err != nil {
		return nil
	}

	return &v1TaskAffinity
}

func (a *TaskAffinity) AddAffinityInTaskAffinity(affinity *Affinity, isRequired bool) *TaskAffinity {
	if a == nil {
		a = &TaskAffinity{
			Required:  make([]*Affinity, 0),
			Preferred: make([]*Affinity, 0),
		}
	}

	var list []*Affinity
	if isRequired {
		list = a.Required
	} else {
		list = a.Preferred
	}

	list = append(list, affinity)
	if isRequired {
		a.Required = list
	} else {
		a.Preferred = list
	}
	return a
}
func (a *TaskAffinity) DeleteAffinityInTaskAffinity(key string, operator string, value string, isRequired bool) *TaskAffinity {
	if a == nil {
		return nil
	}
	var list []*Affinity
	if isRequired {
		list = a.Required
	} else {
		list = a.Preferred
	}
	for i, req := range list {
		if req.Key == key && req.Operator == operator {
			if value == "" {
				list = append(list[:i], list[i+1:]...)
			} else {
				valueIdx := -1
				for j, v := range req.Values {
					if v == value {
						valueIdx = j
						break
					}
				}
				if valueIdx != -1 {
					req.Values = append(req.Values[:valueIdx], req.Values[valueIdx+1:]...)
					if len(req.Values) == 0 {
						list = append(list[:i], list[i+1:]...)
					}
				}
			}
			break
		}
	}
	if isRequired {
		a.Required = list
	} else {
		a.Preferred = list
	}
	return a
}

func (a *TaskAffinity) GetAffinityInTaskAffinity(key string, operator string, isRequired bool) *Affinity {

	if a == nil {
		return nil
	}
	var list []*Affinity
	if isRequired {
		list = a.Required
	} else {
		list = a.Preferred
	}
	for _, req := range list {
		if req.Key == key && req.Operator == operator {
			return req
		}
	}
	return nil
}

func ToV1TaskAffinity(affinity *TaskAffinity) *v1.TaskAffinity {
	if affinity == nil {
		return nil
	}

	var v1Required = make([]*v1.Affinity, 0)
	for _, req := range affinity.Required {
		v1Required = append(v1Required, &v1.Affinity{
			Key:      req.Key,
			Operator: req.Operator,
			Values:   req.Values,
		})
	}

	var v1Preferred = make([]*v1.Affinity, 0)
	for _, pref := range affinity.Preferred {
		v1Preferred = append(v1Preferred, &v1.Affinity{
			Key:      pref.Key,
			Operator: pref.Operator,
			Values:   pref.Values,
		})
	}

	return &v1.TaskAffinity{
		Required:  v1Required,
		Preferred: v1Preferred,
	}
}

func ToBizTaskAffinity(req *v1.TaskAffinity) *TaskAffinity {
	var affinity *TaskAffinity
	if req != nil {
		var required = make([]*Affinity, 0)
		if len(req.Required) > 0 {
			for _, v := range req.Required {
				required = append(required, &Affinity{
					Key:      v.Key,
					Operator: v.Operator,
					Values:   v.Values,
				})
			}
		}

		var preferred = make([]*Affinity, 0)
		if len(req.Preferred) > 0 {
			for _, v := range req.Preferred {
				preferred = append(preferred, &Affinity{
					Key:      v.Key,
					Operator: v.Operator,
					Values:   v.Values,
				})
			}
		}

		affinity = &TaskAffinity{
			Required:  required,
			Preferred: preferred,
		}
	}

	return affinity
}

// ConvertK8sToV1TaskAffinity k8s亲和性结构转affinity
func ConvertK8sToV1TaskAffinity(k8sAffinity *K8sAffinity) *v1.TaskAffinity {
	taskAffinity := &v1.TaskAffinity{}

	for _, requiredGroup := range k8sAffinity.Required {
		for _, matchExpr := range requiredGroup.MatchExpressions {
			affinity := &v1.Affinity{
				Key:      matchExpr.Key,
				Operator: string(matchExpr.Operator),
				Values:   matchExpr.Values,
			}
			taskAffinity.Required = append(taskAffinity.Required, affinity)
		}
	}

	for _, preferredGroup := range k8sAffinity.Preferred {
		for _, matchExpr := range preferredGroup.MatchExpressions {
			affinity := &v1.Affinity{
				Key:      matchExpr.Key,
				Operator: string(matchExpr.Operator),
				Values:   matchExpr.Values,
			}
			taskAffinity.Preferred = append(taskAffinity.Preferred, affinity)
		}
	}

	return taskAffinity
}

func (a *TaskAffinity) HasAffinityInTaskAffinity(key string, operator string, isRequired bool) bool {

	if a == nil {
		return false
	}
	var list []*Affinity
	if isRequired {
		list = a.Required
	} else {
		list = a.Preferred
	}
	for _, req := range list {
		if req.Key == key && req.Operator == operator {
			return true
		}
	}
	return false
}

func (a *TaskAffinity) AddMigrateAffinity(workerId string, isRequired bool) {
	//有则append
	if a.HasAffinityInTaskAffinity(string(AffinityKey_KEY_WORKER_ID), string(metav1.LabelSelectorOpNotIn), isRequired) {
		migrateAffinity := a.GetAffinityInTaskAffinity(string(AffinityKey_KEY_WORKER_ID), string(metav1.LabelSelectorOpNotIn), isRequired)
		migrateAffinity.Values = append(migrateAffinity.Values, workerId)
	} else {
		//没有则直接添加
		workerIds := make([]string, 0)
		workerIds = append(workerIds, workerId)
		if isRequired {
			a.Required = append(a.Required, NewAffinity(string(AffinityKey_KEY_WORKER_ID), string(metav1.LabelSelectorOpNotIn), workerIds))
		} else {
			a.Preferred = append(a.Preferred, NewAffinity(string(AffinityKey_KEY_WORKER_ID), string(metav1.LabelSelectorOpNotIn), workerIds))
		}
	}
}
