package models

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	v1 "proto.mpp/api/common/v1"
	"strings"
	"time"
)

type TaskSentinelConfig struct {
	GmtCreate        time.Time
	GmtModified      time.Time
	SentinelConfigId string
	SentinelStatus   SentinelStatus
	UserId           string
	SentinelConfig   *SentinelConfig
	CurrentTaskNum   int32
	PipelineId       string
	TaskType         v1.TaskType
	Product          string
	EngineModel      string
	Tag              string
}
type SentinelConfig struct {
	//最大并发任务数
	MaxParallelNum int32
	//小并发任务数,预留值
	MinParallelNum int32
	//权重，预留
	Weight float32
}

type SentinelStatus int8

const (
	// SentinelStatusNone 默认状态，无状态
	SentinelStatusStateNone SentinelStatus = iota
	//  SentinelStatus 配置已启动
	SentinelStatusStateActive
	//  SentinelStatus 配置已暂停
	SentinelStatusStatePaused
)

const (
	TaskSentinelKeyPrex = "taskSentinel-"
	TaskSentinelNumPrex = "taskSentinelNum-"
)

// 通过userId+pipelineId+taskType+product+engineModel+tag生成唯一的哈希值
func (t *TaskSentinelConfig) GenerateSentinelConfigId() {
	hasher := sha256.New()
	// 将所有参数转换为字符串并拼接到一起
	inputStr := GetSentinelKey(t.UserId, t.PipelineId, int32(t.TaskType), t.Product, t.EngineModel, t.Tag)
	// 计算哈希值
	hasher.Write([]byte(inputStr))
	hashBytes := hasher.Sum(nil)
	// 将哈希值转换为十六进制字符串
	hashHex := hex.EncodeToString(hashBytes)
	t.SentinelConfigId = hashHex
}

func GetSentinelConfigId(key string) string {
	hasher := sha256.New()
	// 将所有参数转换为字符串并拼接到一起
	// 计算哈希值
	hasher.Write([]byte(key))
	hashBytes := hasher.Sum(nil)
	// 将哈希值转换为十六进制字符串
	hashHex := hex.EncodeToString(hashBytes)
	return hashHex
}

//func GetSentinelKey(userId string, pipeline string, taskType int32, product string, engineModel string, tag string) string {
//	return fmt.Sprintf("%s%s-%s-%d-%s-%s-%s", TaskSentinelKeyPrex, userId, pipeline, taskType, product, engineModel, tag)
//}

func GetSentinelKey(userId string, pipeline string, taskType int32, product string, engineModel string, tag string) string {
	parts := []string{fmt.Sprintf("%s%d-", TaskSentinelKeyPrex, taskType)}

	if userId != "" {
		parts = append(parts, userId)
	}

	if product != "" && engineModel != "" && tag != "" {
		parts = append(parts, "-", product, "-", engineModel, "-", tag)
	}

	if pipeline != "" {
		parts = append(parts, "-", pipeline)
	}

	key := strings.Join(parts, "")
	return key
}

// redis中存储的流控数据结构
type TaskSentinelData struct {
	Id               string `json:"id"`
	SentinelConfigId string `json:"sentinel_config_id"`
	// 任务运行数
	TaskRunningNum int32 `json:"task_Running_num"`
	// 任务并发限制数
	MaxParallelNum int32 `json:"max_parallel_num"`
	// 是否启用流控
	EnableSentinel bool   `json:"enable_sentinel"`
	CreateTime     string `json:"create_time"`
	UpdateTime     string `json:"update_time"`
}
