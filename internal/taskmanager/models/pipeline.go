package models

import (
	"fmt"
	"github.com/go-kratos/kratos/v2/errors"
	v1 "proto.mpp/api/common/v1"
	"time"
)

type Pipeline struct {
	Product            string                    `json:"product,omitempty"`
	UserId             string                    `json:"userId,omitempty"`
	PipelineId         string                    `json:"pipelineId,omitempty"`
	Status             v1.PipelineStatus         `json:"status,omitempty"`
	ScheduleLevel      v1.PipelineSchedulerLevel `json:"schedulerLevel,omitempty"`
	Type               v1.PipelineType           `json:"type,omitempty"`
	ReservedInstanceId string                    `json:"reservedInstanceId,omitempty"`
	RealUserId         string                    `json:"realUserId,omitempty"`
	EngineModel        string                    `json:"engineModel,omitempty"`
	Tag                string                    `json:"tag,omitempty"`
	QuotaConfig        string                    `json:"QuotaConfig,omitempty"`
	LastModified       time.Time                 `json:"lastModified,omitempty"`
	GmtCreate          time.Time                 `json:"gmtCreate,omitempty"`
}

var (
	ErrPipelineNotFoundError = errors.NotFound(v1.ErrorReason_PIPELINE_NOT_FOUND.String(), "pipeline not found.")
)

func GeneratePipelineId() string {
	pipelineId := NewUUIDString()
	return pipelineId
}

// StringToPipelineType 将字符串转换为PipelineType
func StringToPipelineType(s string) (v1.PipelineType, error) {
	switch s {
	case "SHARED":
		return v1.PipelineType_SHARED, nil
	case "RESERVED":
		return v1.PipelineType_RESERVED, nil
	default:
		return 0, fmt.Errorf("invalid PipelineType: %s", s)
	}
}

// PipelineTypeToString 将PipelineType转换为字符串
func PipelineTypeToString(pt v1.PipelineType) (string, error) {
	switch pt {
	case v1.PipelineType_SHARED:
		return "SHARED", nil
	case v1.PipelineType_RESERVED:
		return "RESERVED", nil
	default:
		return "", fmt.Errorf("invalid PipelineType: %d", pt)
	}
}

// StringToPipelineSchedulerLevel 将字符串转换为PipelineSchedulerLevel
func StringToPipelineSchedulerLevel(s string) (v1.PipelineSchedulerLevel, error) {
	switch s {
	case "LOW_PRIORITY":
		return v1.PipelineSchedulerLevel_LOW_PRIORITY, nil
	case "STANDARD":
		return v1.PipelineSchedulerLevel_STANDARD, nil
	case "URGENT":
		return v1.PipelineSchedulerLevel_URGENT, nil
	default:
		return 0, fmt.Errorf("invalid PipelineSchedulerLevel: %s", s)
	}
}

// PipelineSchedulerLevelToString 将PipelineSchedulerLevel转换为字符串
func PipelineSchedulerLevelToString(pl v1.PipelineSchedulerLevel) (string, error) {
	switch pl {
	case v1.PipelineSchedulerLevel_LOW_PRIORITY:
		return "LOW_PRIORITY", nil
	case v1.PipelineSchedulerLevel_STANDARD:
		return "STANDARD", nil
	case v1.PipelineSchedulerLevel_URGENT:
		return "URGENT", nil
	default:
		return "", fmt.Errorf("invalid PipelineSchedulerLevel: %d", pl)
	}
}

// StringToPipelineStatus 将字符串转换为PipelineStatus
func StringToPipelineStatus(s string) (v1.PipelineStatus, error) {
	switch s {
	case "ACTIVE":
		return v1.PipelineStatus_ACTIVE, nil
	case "PAUSED":
		return v1.PipelineStatus_PAUSED, nil
	case "DELETED":
		return v1.PipelineStatus_DELETED, nil
	default:
		return 0, fmt.Errorf("invalid PipelineStatus: %s", s)
	}
}

// PipelineStatusToString 将PipelineStatus转换为字符串
func PipelineStatusToString(ps v1.PipelineStatus) (string, error) {
	switch ps {
	case v1.PipelineStatus_ACTIVE:
		return "ACTIVE", nil
	case v1.PipelineStatus_PAUSED:
		return "PAUSED", nil
	case v1.PipelineStatus_DELETED:
		return "DELETED", nil
	default:
		return "", fmt.Errorf("invalid PipelineStatus: %d", ps)
	}
}
