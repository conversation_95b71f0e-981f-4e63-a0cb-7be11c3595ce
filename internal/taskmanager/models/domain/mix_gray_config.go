package domain

import (
	"gorm.io/datatypes"
	"time"
)

/**
 * @Author: qinxin
 * @Date: 2024/12/23 20:41
 * @Desc:
 * @Version 1.0
 */

// Task task任务主表,历史已完成任务不删除（biz_status=stopped internal_status=stopped），表查询必须带索引，通过biz_status=1或internal_status=1或task_id
type MixGrayConfig struct {
	ID          uint64    `gorm:"primarykey;COMMENT:主键"`
	GmtCreate   time.Time `gorm:"COMMENT:创建时间"`
	GmtModified time.Time `gorm:"COMMENT:修改时间"`

	Uid          string         `gorm:"type:varchar(64);index:idx_uid,unique;NOT NULL;COMMENT:混跑规则唯一id,后端生成"`
	RuleName     string         `gorm:"type:varchar(64);index:uk_rule_name,unique;NOT NULL;COMMENT:混跑规则名称"`
	Priority     int32          `gorm:"default:0;COMMENT:优先级 范围[0,100]"`                                //优先级，值越大越优先
	ConfigStatus int32          `gorm:"default:2;index:idx_config_status;COMMENT:配置状态"`                 //1: 启用 2: 禁用
	RuleConfigs  datatypes.JSON `gorm:"column:rule_configs;type:json;COMMENT:参数匹配规则:操作符 参数名称 参数类型 参数值"` //参数名称 参数类型 操作符 参数值
	MixRatio     float32        `gorm:"default:0;COMMENT:混跑比例 [0-1]"`
	MixConfigs   datatypes.JSON `gorm:"column:mix_configs;type:json;COMMENT:混跑配置,记录混跑资源的三元组等信息"` //三元组配置，版本
	Extends      datatypes.JSON `gorm:"column:extends;type:json;COMMENT:扩展字段"`
}
