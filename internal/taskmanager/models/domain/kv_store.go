package domain

import "time"

type KVStore struct {
	ID           uint64    `gorm:"primarykey;COMMENT:主键"`
	CreateTime   time.Time `gorm:"column:gmt_create;type:datetime;not null;COMMENT:创建时间"`
	ModifiedTime time.Time `gorm:"column:gmt_modified;type:datetime;not null;COMMENT:修改时间"`
	Repo         string    `gorm:"column:repo;uniqueIndex:idx_repo_key;type:varchar(64);NOT NULL;COMMENT:仓库"`
	Key          string    `gorm:"column:key;uniqueIndex:idx_repo_key;type:varchar(64);NOT NULL;COMMENT:键"`
	Value        string    `gorm:"column:value;type:text;NOT NULL;COMMENT:值"`
	ExpiredTime  time.Time `gorm:"column:gmt_expired;index:idx_expired;type:datetime;NOT NULL;COMMENT:过期时间"`
}
