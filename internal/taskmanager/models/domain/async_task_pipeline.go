package domain

import (
	"time"
)

type AsyncTaskPipeline struct {
	ID          uint64    `gorm:"primarykey;COMMENT:主键"`
	GmtCreate   time.Time `gorm:"COMMENT:创建时间"`
	GmtModified time.Time `gorm:"COMMENT:修改时间"`

	UserId             string `gorm:"type:varchar(64);NOT NULL;COMMENT:用户id"`
	PipelineId         string `gorm:"type:varchar(64);index:idx_pipeline_id;index:idx_type_pipeline_id,priority:9,unique;NOT NULL;COMMENT:管道id"`
	Status             int32  `gorm:"default:0;index:idx_status;NOT NULL;COMMENT:管道状态，0:初始态，1:ACTIVE，2:PAUSED，3:DELETED"`
	ScheduleLevel      int32  `gorm:"default:2;COMMENT:调度级别，0:初始态，1:低级别 LOW_PRIORITY，2:标准 STANDARD，3:紧急 URGENT"`
	Type               int32  `gorm:"default:1;index:idx_type;index:idx_type_pipeline_id,priority:10,unique;COMMENT:任务类型 1:共享实例管道Shared 2：保留实例管道Reserved"`
	ReservedInstanceId string `gorm:"type:varchar(64);COMMENT:保留实例id"`
	RealUserId         string `gorm:"type:varchar(64);COMMENT:真实用户id"`
}
