package domain

import (
	"gorm.io/datatypes"
	"time"
)

type UnitTask struct {
	ID          uint64         `gorm:"column:id;primarykey;COMMENT:主键"`
	GmtCreate   time.Time      `gorm:"column:gmt_create;not null;COMMENT:创建时间"`
	GmtModified time.Time      `gorm:"column:gmt_modified;not null;COMMENT:修改时间"`
	Extend      datatypes.JSON `gorm:"column:extend;type:json;COMMENT:扩展字段"`
	TaskId      string         `gorm:"column:task_id;type:varchar(64);not null;COMMENT:任务ID"`
	Unit        string         `gorm:"column:unit;type:varchar(64);not null;COMMENT:单元标识"`
	Status      int8           `gorm:"column:status;not null;COMMENT:状态"`
}
