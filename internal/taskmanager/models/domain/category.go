package domain

import (
	"database/sql"
	"time"

	"gorm.io/gorm"
)

// Category pipe tree
type Category struct {
	CreatedAt     time.Time
	UpdatedAt     time.Time
	DeletedAt     gorm.DeletedAt `gorm:"index"`
	ID            int64          `gorm:"PRIMARY_KEY;AUTO_INCREMENT" nestedset:"id"`
	ParentID      sql.NullInt64  `nestedset:"parent_id"`
	Type          string         `nestedset:"scope"`
	Rgt           int            `nestedset:"rgt"`
	Lft           int            `nestedset:"lft"`
	Depth         int            `nestedset:"depth"`
	ChildrenCount int            `nestedset:"children_count"`
	// Event
	Message string
	// 类型值
	Key string
	// 策略器
	OPA string
}
