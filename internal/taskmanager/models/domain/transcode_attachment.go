package domain

import "time"

type TranscodeAttachment struct {
	ID           uint64    `gorm:"column:id;primarykey;COMMENT:主键"`
	CreateTime   time.Time `gorm:"column:gmt_create;type:datetime;not null;COMMENT:创建时间"`
	ModifiedTime time.Time `gorm:"column:gmt_modified;type:datetime;not null;COMMENT:修改时间"`
	TaskID       string    `gorm:"column:task_id;uniqueIndex:uk_attachment;index:idx_task;type:varchar(64);not null;COMMENT:转码任务唯一ID"`
	AttachType   string    `gorm:"column:attach_type;uniqueIndex:uk_attachment;type:varchar(16);not null;COMMENT:附件类型"`
	Name         string    `gorm:"column:name;uniqueIndex:uk_attachment;type:varchar(64);not null;COMMENT:附件名"`
	Params       string    `gorm:"column:params;type:varchar(1024);not null;COMMENT:附件参数（json格式）"`
	Status       int8      `gorm:"column:status;index:idx_task;type:tinyint;not null;COMMENT:0表示未删除，1表示已删除"`
}
