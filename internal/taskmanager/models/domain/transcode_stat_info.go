package domain

import "time"

type TranscodeStatInfo struct {
	ID            uint64    `gorm:"column:id;primarykey;autoIncrement;COMMENT:主键"`
	CreateTime    time.Time `gorm:"column:gmt_create;type:datetime;not null;COMMENT:创建时间"`
	ModifiedTime  time.Time `gorm:"column:gmt_modified;type:datetime;not null;COMMENT:修改时间"`
	Domain        string    `gorm:"column:domain;type:varchar(128);not null;COMMENT:域名"`
	Tag           string    `gorm:"column:tag;type:varchar(64);not null;COMMENT:标签"`
	StatTime      time.Time `gorm:"column:stat_time;type:datetime;not null;uniqueIndex:idx_time_doamin;COMMENT:统计时间"`
	StatInfo      string    `gorm:"column:stat_info;type:varchar(2048);COMMENT:统计信息"`
	TotalQuota    int32     `gorm:"column:total_quota;COMMENT:总quota"`
	TotalTaskNum  uint32    `gorm:"column:total_task_num;not null;COMMENT:总任务数"`
	InnerQuota    uint32    `gorm:"column:inner_quota;not null;default:0;COMMENT:弹内quota"`
	InnerTaskNum  uint32    `gorm:"column:inner_task_num;COMMENT:弹内任务数"`
	CloudQuota    uint32    `gorm:"column:cloud_quota;not null;default:0;COMMENT:售卖区quota"`
	CloudTaskNum  uint32    `gorm:"column:cloud_task_num;COMMENT:售卖区任务数"`
	L2Quota       uint32    `gorm:"column:l2_quota;COMMENT:l2quota"`
	L2TaskNum     uint32    `gorm:"column:l2_task_num;COMMENT:l2任务数"`
	L1Quota       uint32    `gorm:"column:l1_quota;COMMENT:l1quota"`
	L1TaskNum     uint32    `gorm:"column:l1_task_num;COMMENT:l1任务数"`
	L22Quota      uint32    `gorm:"column:l2_2_quota;COMMENT:l2二套quota"`
	L22TaskNum    uint32    `gorm:"column:l2_2_task_num;COMMENT:l2二套任务数"`
	Inner2Quota   uint32    `gorm:"column:inner_2_quota;COMMENT:弹内二套quota"`
	Inner2TaskNum uint32    `gorm:"column:inner_2_task_num;COMMENT:弹内二套任务数"`
	Extend        string    `gorm:"column:extend;type:varchar(2048);COMMENT:扩展信息"`
}
