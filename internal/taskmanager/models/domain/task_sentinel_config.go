package domain

import (
	"gorm.io/datatypes"
	"time"
)

type TaskSentinelConfig struct {
	ID          uint64    `gorm:"primarykey;COMMENT:主键"`
	GmtCreate   time.Time `gorm:"COMMENT:创建时间"`
	GmtModified time.Time `gorm:"COMMENT:修改时间"`

	SentinelConfigId string         `gorm:"type:varchar(64);index:idx_sentinel_config_id,unique;NOT NULL;COMMENT:限流配置id,由配置项SHA-256哈希生成的唯一id" `
	SentinelConfig   datatypes.JSON `gorm:"column:sentinel_config;type:json;COMMENT:限流配置"`
	SentinelStatus   int32          `gorm:"default:0;index:idx_sentinel_status;COMMENT:限流状态 1:启用 2：禁用"`
	CurrentTaskNum   int32          `gorm:"default:0;COMMENT:当前任务数量"`
	UserId           string         `gorm:"type:varchar(64);NOT NULL;COMMENT:用户id"`
	PipelineId       string         `gorm:"type:varchar(64);index:idx_pipeline_id;NOT NULL;COMMENT:管道id"`
	TaskType         int32          `gorm:"default:0;COMMENT:任务类型"`
	Product          string         `gorm:"type:varchar(32);COMMENT:产品"`
	EngineModel      string         `gorm:"type:varchar(32);COMMENT:引擎模型"`
	Tag              string         `gorm:"type:varchar(32);COMMENT:标签"`
}
