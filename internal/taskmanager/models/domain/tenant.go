package domain

import (
	"time"

	"gorm.io/gorm"
)

type Tenant struct {
	CreatedAt   time.Time
	UpdatedAt   time.Time
	DeletedAt   gorm.DeletedAt `gorm:"index"`
	ID          int64          `gorm:"PRIMARY_KEY;AUTO_INCREMENT"`
	UserID      string         `gorm:"type:varchar(64);index:idx_user_id;COMMENT:用户id"`
	PipelineID  string         `gorm:"type:varchar(64);index:idx_pipeline_id;COMMENT:管道id"`
	Description string         `gorm:"type:varchar(1024);COMMENT:描述数据备用"`
}
