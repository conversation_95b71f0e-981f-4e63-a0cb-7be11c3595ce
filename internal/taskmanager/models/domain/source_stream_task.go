package domain

import "time"

// SourceStreamTask 直播源流任务
type SourceStreamTask struct {
	ID             uint64    `gorm:"primarykey;COMMENT:主键"`
	CreateTime     time.Time `gorm:"column:gmt_create;type:datetime;not null;COMMENT:创建时间"`
	ModifiedTime   time.Time `gorm:"column:gmt_modified;type:datetime;not null;COMMENT:修改时间"`
	SourceStreamId string    `gorm:"column:source_stream_id;uniqueIndex:uk_source_stream_task_id;type:char(36);not null;COMMENT:源流ID"`
	Domain         string    `gorm:"column:domain;type:varchar(256);not null;COMMENT:播放域名"`
	App            string    `gorm:"column:app;type:varchar(256);not null;COMMENT:app名"`
	Stream         string    `gorm:"column:stream;type:varchar(256);not null;COMMENT:流名"`
	TaskId         string    `gorm:"column:task_id;uniqueIndex:uk_source_stream_task_id;index:idx_task_id;type:varchar(64);not null;COMMENT:任务ID"`
}
