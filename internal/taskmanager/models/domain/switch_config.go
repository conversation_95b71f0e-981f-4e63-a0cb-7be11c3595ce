package domain

import "time"

type SwitchConfig struct {
	Id           int64     `gorm:"primarykey;COMMENT:主键"`
	GmtCreate    time.Time `gorm:"COMMENT:创建时间"`
	GmtModified  time.Time `gorm:"COMMENT:修改时间"`
	Product      string    `gorm:"type:varchar(32);NOT NULL;uniqueIndex:uk_tag_index;COMMENT:产品"`
	EngineModel  string    `gorm:"type:varchar(64);NOT NULL;uniqueIndex:uk_tag_index;COMMENT:引擎模型"`
	Tag          string    `gorm:"type:varchar(64);NOT NULL;uniqueIndex:uk_tag_index;COMMENT:资源组标签"`
	Domain       string    `gorm:"type:varchar(256);NOT NULL;uniqueIndex:uk_tag_index;COMMENT:转码域名"`
	App          string    `gorm:"type:varchar(64);NOT NULL;uniqueIndex:uk_tag_index;COMMENT:转码APP"`
	Token        string    `gorm:"type:varchar(64);NOT NULL;uniqueIndex:uk_tag_index;COMMENT:转码Token"`
	SwitchConfig string    `gorm:"type:varchar(1024);NOT NULL;COMMENT:切流配置"`
	BackupConfig string    `gorm:"type:varchar(1024);NOT NULL;COMMENT:兜底配置"`
	Extend       string    `gorm:"type:varchar(1024);COMMENT:扩展配置"`
	Status       int8      `gorm:"type:tinyint;NOT NULL;COMMENT:状态"`
}
