package domain

import (
	"time"
)

type Leader struct {
	ID           uint64    `gorm:"primarykey;COMMENT:主键"`
	GmtCreate    time.Time `gorm:"COMMENT:创建时间"`
	GmtModified  time.Time `gorm:"COMMENT:修改时间"`
	Key          string    `gorm:"uniqueIndex:idx_key;type:varchar(64);NOT NULL;COMMENT:选举的键"`
	Instance     string    `gorm:"Index:idx_instance;type:varchar(64);NOT NULL;COMMENT:实例信息"`
	LastTickTime time.Time `gorm:"column:last_tick_time;type:datetime;NOT NULL;COMMENT:心跳时间"`
}
