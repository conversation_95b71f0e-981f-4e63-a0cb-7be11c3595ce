package domain

import (
	"time"

	"gorm.io/datatypes"
)

// Task task任务主表,历史已完成任务不删除（biz_status=stopped internal_status=stopped），表查询必须带索引，通过biz_status=1或internal_status=1或task_id
type Task struct {
	ID             uint64    `gorm:"primarykey;COMMENT:主键"`
	GmtCreate      time.Time `gorm:"COMMENT:创建时间"`
	GmtModified    time.Time `gorm:"COMMENT:修改时间"`
	BizStatus      int32     `gorm:"default:0;index:idx_biz_status;index:idx_biz_status_worker_id,priority:9;COMMENT:业务状态，0表示未运行，1表示运行中"`
	InternalStatus int32     `gorm:"default:0;index:idx_internal_status;COMMENT:内部状态，0表示未运行，1表示运行中"`

	TaskId     string `gorm:"type:varchar(64);index:idx_task_id,unique;NOT NULL;COMMENT:任务id"`
	JobId      string `gorm:"type:varchar(64);index:idx_job_id;COMMENT:作业id"`
	UserId     string `gorm:"type:varchar(64);index:idx_user_id;COMMENT:用户id"`
	PipelineId string `gorm:"type:varchar(64);index:idx_pipeline_id;COMMENT:管道id"`
	WorkerId   string `gorm:"type:varchar(64);index:idx_biz_status_worker_id,priority:10;COMMENT:分配的workerId"`

	Product         string         `gorm:"type:varchar(32);NOT NULL;COMMENT:产品"`
	EngineModel     string         `gorm:"type:varchar(64);NOT NULL;COMMENT:引擎模型"`
	Tag             string         `gorm:"type:varchar(64);NOT NULL;COMMENT:资源组标签"`
	Notify          string         `gorm:"type:varchar(1024);NOT NULL;COMMENT:回调地址"`
	UserData        string         `gorm:"type:varchar(1024);COMMENT:用户数据"`
	Trace           string         `gorm:"type:varchar(1024);COMMENT:链路trace"`
	RequestResource datatypes.JSON `gorm:"column:request_resource;type:json;COMMENT:任务资源需求"`
	WorkerIp        string         `gorm:"type:varchar(64);COMMENT:分配的workerIp"`
	WorkerPort      int32          `gorm:"COMMENT:分配的worker访问端口"`
	EngineParam     datatypes.JSON `gorm:"column:engine_param;type:json;COMMENT:引擎任务参数"`
	Result          datatypes.JSON `gorm:"column:result;type:json;COMMENT:任务运行结果"`
	MigrateConfig   datatypes.JSON `gorm:"column:migrate_config;type:json;COMMENT:任务迁移配置"`
	TaskType        int32          `gorm:"default:0;COMMENT:任务类型,请参考pb的定义"`
	MigrateTimes    int32          `gorm:"column:migrate_times;default:0;COMMENT:任务迁移次数"`
	Dependency      datatypes.JSON `gorm:"column:dependency;type:json;COMMENT:任务依赖"`
	Metadata        datatypes.JSON `gorm:"column:metadata;type:json;COMMENT:任务元信息"`
	Retried         int32          `gorm:"COMMENT:重试次数"`

	//并行转码新增字段
	ScheduleParam datatypes.JSON `gorm:"column:schedule_param;type:json;COMMENT:任务调度参数"`
	//DagGraph      datatypes.JSON `gorm:"column:dag_graph;type:json;COMMENT:任务dag图"`

	// 直播转码新增字段
	Domain string `gorm:"column:domain;type:varchar(256);COMMENT:转码域名"`
	// todo 是否增加一个链路追踪的tracing id到数据库，构建完整的链路图
	// todo 后面任务表将增加明显，应该要有自动清理过期任务的机制，老的任务数据可以同步到odps存储

	// todo 查询都要限制，做好limit，最好用nexttoken的方式
}

// SimpleTask 同步任务
type SimpleTask struct {
	*Task
}

type SimpleTaskWithStatus struct {
	ID          uint64    `gorm:"primarykey;COMMENT:主键"`
	GmtCreate   time.Time `gorm:"COMMENT:创建时间"`
	GmtModified time.Time `gorm:"COMMENT:修改时间"`

	BizStatus      int32  `gorm:"default:0;index:idx_biz_status;index:idx_biz_status_worker_id,priority:9;COMMENT:业务状态，0表示未运行，1表示运行中"`
	InternalStatus int32  `gorm:"default:0;index:idx_internal_status;COMMENT:内部状态，0表示未运行，1表示运行中"`
	TaskId         string `gorm:"type:varchar(64);index:idx_task_id,unique;NOT NULL;COMMENT:任务id"`
	WorkerId       string `gorm:"type:varchar(64);index:idx_biz_status_worker_id,priority:10;COMMENT:分配的workerId"`

	Product     string `gorm:"type:varchar(32);NOT NULL;COMMENT:产品"`
	EngineModel string `gorm:"type:varchar(64);NOT NULL;COMMENT:引擎模型"`
	Tag         string `gorm:"type:varchar(64);NOT NULL;COMMENT:资源组标签"`

	WorkerIp string `gorm:"type:varchar(64);COMMENT:分配的workerIp"`
	Domain   string `gorm:"column:domain;type:varchar(256);COMMENT:转码域名"`
}
