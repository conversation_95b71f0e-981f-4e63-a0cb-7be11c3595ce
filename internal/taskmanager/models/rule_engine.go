package models

import (
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"mpp/internal/taskmanager/models/domain"
	pb "proto.mpp/api/taskmanager/v1"
	"time"
)

/**
 * @Author: qinxin
 * @Date: 2024/12/23 17:28
 * @Desc: 混跑灰度构造
 * @Version 1.0
 */

type MixGrayConfig struct {
	ID          uint64    `json:"id,omitempty"`
	GmtModified time.Time `json:"gmtModified,omitempty"`
	GmtCreate   time.Time `json:"gmtCreate,omitempty"`

	Uid          string                 `json:"uid,omitempty"` //唯一id
	RuleName     string                 `json:"ruleName,omitempty"`
	Priority     int                    `json:"priority,omitempty"`     //优先级，值越大越优先
	ConfigStatus MixGrayConfigStatus    `json:"configStatus,omitempty"` //1: 启用 2: 禁用
	RuleConfigs  []*MixRuleConfig       `json:"ruleConfigs,omitempty"`  //参数名称 参数类型 操作符 参数值
	MixRatio     float32                `json:"mixRatio,omitempty"`     //混跑比例
	MixConfigs   []*MixConfig           `json:"mixConfigs,omitempty"`   //混跑资源配置
	Extends      map[string]interface{} `json:"extends,omitempty"`      //扩展字段
}

// 任务状态类型
type MixGrayConfigStatus int

const (
	defaultMixGrayConfigStatus MixGrayConfigStatus = 0 //默认
	MixGrayConfigStatusActive  MixGrayConfigStatus = 1 //启用
	MixGrayConfigStatusPause   MixGrayConfigStatus = 2 //禁用
)

// 支持的操作符号
type MixOperateType string

const (
	MixOperateEqual            MixOperateType = "=="
	MixOperateNotEqual         MixOperateType = "!="
	MixOperateLessThan         MixOperateType = "<"
	MixOperateLessThanEqual    MixOperateType = "<="
	MixOperateGreaterThan      MixOperateType = ">"
	MixOperateGreaterThanEqual MixOperateType = ">="
	//OperateContains         MixOperateType = "contains"    暂不支持
	//OperateNotContains      MixOperateType = "not contains"	暂不支持
	MixOperateIn    MixOperateType = "in"
	MixOperateNotIn MixOperateType = "not in"
)

// 支持的任务类型
type MixValueType string

const (
	MixValueTypeString  MixValueType = "string"
	MixValueTypeInteger MixValueType = "integer"
	MixValueTypeLong    MixValueType = "long"
	MixValueTypeBool    MixValueType = "bool"
	MixValueTypeDouble  MixValueType = "double"
	MixValueTypeFloat   MixValueType = "float"
)

// 混跑规则配置
type MixRuleConfig struct {
	ParamName string      `json:"paramName,omitempty"` // 参数名
	Operate   string      `json:"operate,omitempty"`   // 规则 1.in  2.not in  3.=  4. !=  5.contains 6. not contains 7.大于 8.小于 9.大于等于 10.小于等于
	Value     interface{} `json:"value,omitempty"`     // 参数值
	ValueType string      `json:"valueType,omitempty"` // 参数类型 string integer long bool double float
}

// 混跑配置
type MixConfig struct {
	Product          string           `json:"product,omitempty"`     // 混跑产品 json
	EngineModel      string           `json:"engineModel,omitempty"` //
	Tag              string           `json:"tag,omitempty"`
	Version          string           `json:"version,omitempty"`
	Ratio            float32          `json:"ratio,omitempty"`
	MixAllocStrategy MixAllocStrategy `json:"mixAllocStrategy,omitempty"`
}

type MixConfigFiled string

const (
	MixConfigFiledProduct          MixConfigFiled = "product"
	MixConfigFiledEngine           MixConfigFiled = "engine"
	MixConfigFiledTag              MixConfigFiled = "tag"
	MixConfigFiledVersion          MixConfigFiled = "version"
	MixConfigFiledRatio            MixConfigFiled = "ratio"
	MixConfigFiledMixAllocStrategy MixConfigFiled = "mixAllocStrategy"
)

type MixAllocStrategy string

const (
	// NormalMixed: 正常混跑 资源申请失败后 不退避
	NormalMixed MixAllocStrategy = "NormalMixed"
	// TryAbandonMixed: 尝试混跑 资源申请失败后 退避非混跑执行
	TryAbandonMixed MixAllocStrategy = "TryAbandonMixed"
)

// ProtoToMixGrayConfig converts a proto MixGrayConfig to a Go MixGrayConfig.
func ProtoToMixGrayConfig(protoConfig *pb.MixGrayConfig) (*MixGrayConfig, error) {
	goConfig := &MixGrayConfig{
		Uid:          protoConfig.Id,
		RuleName:     protoConfig.RuleName,
		Priority:     int(protoConfig.Priority),
		ConfigStatus: MixGrayConfigStatus(protoConfig.ConfigStatus),
		RuleConfigs:  nil,
		MixRatio:     float32(protoConfig.MixRatio),
		MixConfigs:   nil,
		Extends:      nil, // Assuming Extends is a JSON string, you need to unmarshal it.
	}

	//字符串转json ruleConfigs、mixConfigs、Extends
	var ruleConfigs []*MixRuleConfig
	var mixConfigs []*MixConfig
	if protoConfig.RuleConfigs != "" {
		ruleConfigsStr := protoConfig.RuleConfigs
		err := json.Unmarshal([]byte(ruleConfigsStr), &ruleConfigs)
		if err != nil {
			log.Errorf("Error unmarshaling RuleConfigs: %v, err: %v", protoConfig.RuleConfigs, err)
			return nil, err
		}
		goConfig.RuleConfigs = ruleConfigs
	}
	if protoConfig.MixRuleConfig != "" {
		mixConfigsStr := protoConfig.MixRuleConfig
		err := json.Unmarshal([]byte(mixConfigsStr), &mixConfigs)
		if err != nil {
			log.Errorf("Error unmarshaling MixConfigs: %v, err: %v", protoConfig.MixRuleConfig, err)
			return nil, err
		}
		goConfig.MixConfigs = mixConfigs
	}

	return goConfig, nil
}

// MixGrayConfigToProto converts a Go MixGrayConfig to a proto MixGrayConfig.
func MixGrayConfigToProto(goConfig *MixGrayConfig) (*pb.MixGrayConfig, error) {

	protoConfig := &pb.MixGrayConfig{
		Id:            goConfig.Uid,
		RuleName:      goConfig.RuleName,
		Priority:      int32(goConfig.Priority),
		ConfigStatus:  int32(goConfig.ConfigStatus),
		RuleConfigs:   "",
		MixRatio:      float32(goConfig.MixRatio),
		MixRuleConfig: "",
		Extends:       "", // Assuming Extends is a JSON string, you need to marshal it.
	}

	//json转字符串 RuleConfigs MixRuleConfig Extends
	if goConfig.RuleConfigs != nil {
		ruleConfigsBytes, err := json.Marshal(goConfig.RuleConfigs)
		if err != nil {
			log.Errorf("Error marshaling RuleConfigs: %v, err: %v", goConfig.RuleConfigs, err)
			return nil, err
		}
		protoConfig.RuleConfigs = string(ruleConfigsBytes)
	}
	if goConfig.MixConfigs != nil {
		mixConfigsBytes, err := json.Marshal(goConfig.MixConfigs)
		if err != nil {
			log.Errorf("Error marshaling MixConfigs: %v, err: %v", goConfig.MixConfigs, err)
			return nil, err
		}
		protoConfig.MixRuleConfig = string(mixConfigsBytes)
	}

	return protoConfig, nil
}

func MixGrayConfigBizToDomain(mixGrayConfigBiz *MixGrayConfig) (*domain.MixGrayConfig, error) {

	var err error
	mixGrayConfigDomain := &domain.MixGrayConfig{
		Uid:          mixGrayConfigBiz.Uid,
		RuleName:     mixGrayConfigBiz.RuleName,
		Priority:     int32(mixGrayConfigBiz.Priority),
		MixRatio:     float32(mixGrayConfigBiz.MixRatio),
		ConfigStatus: int32(mixGrayConfigBiz.ConfigStatus),

		RuleConfigs: nil,
		MixConfigs:  nil,
		Extends:     nil,
	}
	if mixGrayConfigBiz.RuleConfigs != nil {
		ruleConfigsBytes, err := json.Marshal(mixGrayConfigBiz.RuleConfigs)
		if err != nil {
			log.Errorf("Error marshaling RuleConfigs: %v, err: %v", mixGrayConfigBiz.RuleConfigs, err)
			return nil, err
		}
		mixGrayConfigDomain.RuleConfigs = ruleConfigsBytes
	}
	if mixGrayConfigBiz.MixConfigs != nil {
		mixConfigsBytes, err := json.Marshal(mixGrayConfigBiz.MixConfigs)
		if err != nil {
			log.Errorf("Error marshaling MixConfigs: %v, err: %v", mixGrayConfigBiz.MixConfigs, err)
			return nil, err
		}
		mixGrayConfigDomain.MixConfigs = mixConfigsBytes
	}
	if mixGrayConfigBiz.Extends != nil {
		extendsBytes, err := json.Marshal(mixGrayConfigBiz.Extends)
		if err != nil {
			log.Errorf("Error marshaling Extends: %v, err: %v", mixGrayConfigBiz.Extends, err)
			return nil, err
		}
		mixGrayConfigDomain.Extends = extendsBytes
	}

	return mixGrayConfigDomain, err

}

func DomainToMixGrayConfigBiz(mixGrayConfig *domain.MixGrayConfig) (*MixGrayConfig, error) {
	var err error
	mixGrayConfigBiz := &MixGrayConfig{
		Uid:          mixGrayConfig.Uid,
		RuleName:     mixGrayConfig.RuleName,
		Priority:     int(mixGrayConfig.Priority),
		MixRatio:     float32(mixGrayConfig.MixRatio),
		ConfigStatus: MixGrayConfigStatus(mixGrayConfig.ConfigStatus),
		RuleConfigs:  nil,
		MixConfigs:   nil,
		Extends:      nil,
	}
	if mixGrayConfig.RuleConfigs != nil {
		var ruleConfigs []*MixRuleConfig
		err = json.Unmarshal(mixGrayConfig.RuleConfigs, &ruleConfigs)
		if err != nil {
			log.Errorf("Error unmarshaling RuleConfigs: %v, err: %v", mixGrayConfig.RuleConfigs, err)
			return nil, err
		}
		mixGrayConfigBiz.RuleConfigs = ruleConfigs
	}
	if mixGrayConfig.MixConfigs != nil {
		var mixConfigs []*MixConfig
		err = json.Unmarshal(mixGrayConfig.MixConfigs, &mixConfigs)
		if err != nil {
			log.Errorf("Error unmarshaling MixConfigs: %v, err: %v", mixGrayConfig.MixConfigs, err)
			return nil, err
		}
		mixGrayConfigBiz.MixConfigs = mixConfigs
	}
	if mixGrayConfig.Extends != nil {
		var extends map[string]interface{}
		err = json.Unmarshal(mixGrayConfig.Extends, &extends)
		if err != nil {
			log.Errorf("Error unmarshaling Extends: %v, err: %v", mixGrayConfig.Extends, err)
			return nil, err
		}
		mixGrayConfigBiz.Extends = extends
	}

	return mixGrayConfigBiz, err
}

func MixGrayConfigBizListToDomainList(mixGrayConfigBizList []*MixGrayConfig) ([]*domain.MixGrayConfig, error) {
	var mixGrayConfigDomainList []*domain.MixGrayConfig
	for _, mixGrayConfigBiz := range mixGrayConfigBizList {
		mixGrayConfigDomain, err := MixGrayConfigBizToDomain(mixGrayConfigBiz)
		if err != nil {
			return nil, err
		}
		mixGrayConfigDomainList = append(mixGrayConfigDomainList, mixGrayConfigDomain)
	}
	return mixGrayConfigDomainList, nil
}

func DomainListToMixGrayConfigBizList(mixGrayConfigDomainList []*domain.MixGrayConfig) ([]*MixGrayConfig, error) {
	var mixGrayConfigBizList []*MixGrayConfig
	for _, mixGrayConfigDomain := range mixGrayConfigDomainList {
		mixGrayConfigBiz, err := DomainToMixGrayConfigBiz(mixGrayConfigDomain)
		if err != nil {
			return nil, err
		}
		mixGrayConfigBizList = append(mixGrayConfigBizList, mixGrayConfigBiz)
	}
	return mixGrayConfigBizList, nil
}
