package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"proto.mpp/api/common/v1"

	"fmt"
)

/**
 * @Author: qinxin
 * @Date: 2024/9/26 14:09
 * @Desc:
 * @Version 1.0
 */

// TestGenerateSentinelConfigId 测试用例：
// TC1: 检查正常情况下能否正确生成SentinelConfigId
// TC2: 检查当某些字段为空时是否仍能正确生成SentinelConfigId
func TestGenerateSentinelConfigId(t *testing.T) {
	tests := []struct {
		name     string
		taskConf *TaskSentinelConfig
		want     string
	}{
		{
			name: "TC1",
			taskConf: &TaskSentinelConfig{
				UserId:      "testUser",
				PipelineId:  "testPipeline",
				TaskType:    v1.TaskType_Stream,
				Product:     "testProduct",
				EngineModel: "testEngine",
				Tag:         "testTag",
			},
			want: "",
		},
		{
			name: "TC2",
			taskConf: &TaskSentinelConfig{
				UserId:      "",
				PipelineId:  "",
				TaskType:    v1.TaskType_Async,
				Product:     "",
				EngineModel: "",
				Tag:         "",
			},
			want: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.taskConf.GenerateSentinelConfigId()

			got := tt.taskConf.SentinelConfigId
			if got == "" {
				t.Errorf("GenerateSentinelConfigId() returned an empty string for %v", tt.name)
			} else if len(got) != 64 { // SHA256哈希长度应为64个字符
				t.Errorf("GenerateSentinelConfigId() returned a string of incorrect length; want 64, got %d", len(got))
			}
		})
	}
}

// TestGetSentinelKey 测试用例：
// TC3: 检查正常情况下能否正确生成SentinelKey
// TC4: 检查当部分字段为空时是否仍能正确生成SentinelKey
func TestGetSentinelKey(t *testing.T) {
	tests := []struct {
		name     string
		userID   string
		pipeline string
		taskType int32
		product  string
		engine   string
		tag      string
		want     string
	}{
		{
			name:     "TC3",
			userID:   "testUser",
			pipeline: "testPipeline",
			taskType: int32(v1.TaskType_Stream),
			product:  "testProduct",
			engine:   "testEngine",
			tag:      "testTag",
			want:     fmt.Sprintf("%s2-testUser-testProduct-testEngine-testTag-testPipeline", TaskSentinelKeyPrex),
		},
		{
			name:     "TC4",
			userID:   "",
			pipeline: "",
			taskType: int32(v1.TaskType_Async),
			product:  "",
			engine:   "",
			tag:      "",
			want:     fmt.Sprintf("%s3-", TaskSentinelKeyPrex),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetSentinelKey(tt.userID, tt.pipeline, tt.taskType, tt.product, tt.engine, tt.tag)
			if !assert.Equal(t, tt.want, got) {
				t.Errorf("GetSentinelKey() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestGetSentinelConfigId 测试用例：
// TC5: 检查正常情况下能否正确生成SentinelConfigId
func TestGetSentinelConfigId(t *testing.T) {
	testKey := "testKey"
	expectedHashLength := 64

	got := GetSentinelConfigId(testKey)
	if len(got) != expectedHashLength {
		t.Errorf("GetSentinelConfigId() returned a string of incorrect length; want %d, got %d", expectedHashLength, len(got))
	}
}
