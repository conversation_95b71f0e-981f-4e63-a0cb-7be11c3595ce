package models

import (
	"fmt"
	v1 "proto.mpp/api/common/v1"
	"testing"

	"github.com/stretchr/testify/assert"
)

/**
 * @Author: qinxin
 * @Date: 2024/9/26 12:09
 * @Desc:
 * @Version 1.0
 */

// 测试GeneratePipelineId方法
// 测试用例：
// TC1 - 检查是否返回非空字符串
func TestGeneratePipelineId(t *testing.T) {
	t.Run("TC1", func(t *testing.T) {
		id := GeneratePipelineId()
		assert.NotEmpty(t, id, "expected non-empty pipeline ID")
	})
}

// 测试StringToPipelineType方法
// 测试用例：
// TC1 - 正确转换SHARED类型
// TC2 - 正确转换RESERVED类型
// TC3 - 转换无效类型应返回错误
func TestStringToPipelineType(t *testing.T) {
	testCases := []struct {
		input    string
		expected v1.PipelineType
	}{
		{"SHARED", v1.PipelineType_SHARED},
		{"RESERVED", v1.PipelineType_RESERVED},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("TC%d", tc.expected), func(t *testing.T) {
			pt, err := StringToPipelineType(tc.input)
			if !assert.NoError(t, err) {
				return
			}
			assert.Equal(t, tc.expected, pt)
		})
	}

	t.Run("TC3", func(t *testing.T) {
		_, err := StringToPipelineType("INVALID")
		assert.Error(t, err)
	})
}

// 测试PipelineTypeToString方法
// 测试用例：
// TC1 - 正确转换SHARED类型
// TC2 - 正确转换RESERVED类型
// TC3 - 转换无效类型应返回错误
func TestPipelineTypeToString(t *testing.T) {
	testCases := []struct {
		input    v1.PipelineType
		expected string
	}{
		{v1.PipelineType_SHARED, "SHARED"},
		{v1.PipelineType_RESERVED, "RESERVED"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("TC%d", tc.input), func(t *testing.T) {
			s, err := PipelineTypeToString(tc.input)
			if !assert.NoError(t, err) {
				return
			}
			assert.Equal(t, tc.expected, s)
		})
	}

	t.Run("TC3", func(t *testing.T) {
		_, err := PipelineTypeToString(v1.PipelineType(99))
		assert.Error(t, err)
	})
}

// 测试StringToPipelineSchedulerLevel方法
// 测试用例：
// TC1 - 正确转换LOW_PRIORITY级别
// TC2 - 正确转换STANDARD级别
// TC3 - 正确转换URGENT级别
// TC4 - 转换无效级别应返回错误
func TestStringToPipelineSchedulerLevel(t *testing.T) {
	testCases := []struct {
		input    string
		expected v1.PipelineSchedulerLevel
	}{
		{"LOW_PRIORITY", v1.PipelineSchedulerLevel_LOW_PRIORITY},
		{"STANDARD", v1.PipelineSchedulerLevel_STANDARD},
		{"URGENT", v1.PipelineSchedulerLevel_URGENT},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("TC%d", tc.expected), func(t *testing.T) {
			pl, err := StringToPipelineSchedulerLevel(tc.input)
			if !assert.NoError(t, err) {
				return
			}
			assert.Equal(t, tc.expected, pl)
		})
	}

	t.Run("TC4", func(t *testing.T) {
		_, err := StringToPipelineSchedulerLevel("INVALID")
		assert.Error(t, err)
	})
}

// 测试PipelineSchedulerLevelToString方法
// 测试用例：
// TC1 - 正确转换LOW_PRIORITY级别
// TC2 - 正确转换STANDARD级别
// TC3 - 正确转换URGENT级别
// TC4 - 转换无效级别应返回错误
func TestPipelineSchedulerLevelToString(t *testing.T) {
	testCases := []struct {
		input    v1.PipelineSchedulerLevel
		expected string
	}{
		{v1.PipelineSchedulerLevel_LOW_PRIORITY, "LOW_PRIORITY"},
		{v1.PipelineSchedulerLevel_STANDARD, "STANDARD"},
		{v1.PipelineSchedulerLevel_URGENT, "URGENT"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("TC%d", tc.input), func(t *testing.T) {
			s, err := PipelineSchedulerLevelToString(tc.input)
			if !assert.NoError(t, err) {
				return
			}
			assert.Equal(t, tc.expected, s)
		})
	}

	t.Run("TC4", func(t *testing.T) {
		_, err := PipelineSchedulerLevelToString(v1.PipelineSchedulerLevel(99))
		assert.Error(t, err)
	})
}

// 测试StringToPipelineStatus方法
// 测试用例：
// TC1 - 正确转换ACTIVE状态
// TC2 - 正确转换PAUSED状态
// TC3 - 正确转换DELETED状态
// TC4 - 转换无效状态应返回错误
func TestStringToPipelineStatus(t *testing.T) {
	testCases := []struct {
		input    string
		expected v1.PipelineStatus
	}{
		{"ACTIVE", v1.PipelineStatus_ACTIVE},
		{"PAUSED", v1.PipelineStatus_PAUSED},
		{"DELETED", v1.PipelineStatus_DELETED},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("TC%d", tc.expected), func(t *testing.T) {
			ps, err := StringToPipelineStatus(tc.input)
			if !assert.NoError(t, err) {
				return
			}
			assert.Equal(t, tc.expected, ps)
		})
	}

	t.Run("TC4", func(t *testing.T) {
		_, err := StringToPipelineStatus("INVALID")
		assert.Error(t, err)
	})
}

// 测试PipelineStatusToString方法
// 测试用例：
// TC1 - 正确转换ACTIVE状态
// TC2 - 正确转换PAUSED状态
// TC3 - 正确转换DELETED状态
// TC4 - 转换无效状态应返回错误
func TestPipelineStatusToString(t *testing.T) {
	testCases := []struct {
		input    v1.PipelineStatus
		expected string
	}{
		{v1.PipelineStatus_ACTIVE, "ACTIVE"},
		{v1.PipelineStatus_PAUSED, "PAUSED"},
		{v1.PipelineStatus_DELETED, "DELETED"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("TC%d", tc.input), func(t *testing.T) {
			s, err := PipelineStatusToString(tc.input)
			if !assert.NoError(t, err) {
				return
			}
			assert.Equal(t, tc.expected, s)
		})
	}

	t.Run("TC4", func(t *testing.T) {
		_, err := PipelineStatusToString(v1.PipelineStatus(99))
		assert.Error(t, err)
	})
}
