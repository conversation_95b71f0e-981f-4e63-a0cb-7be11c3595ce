package models

import (
	"github.com/stretchr/testify/assert"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	mppv1 "proto.mpp/api/common/v1"
	"testing"
)

func TestNewAffinity(t *testing.T) {
	// Test case 1: Valid input
	affinity := NewAffinity("key1", "op1", []string{"val1"})
	assert.NotNil(t, affinity)
	assert.Equal(t, "key1", affinity.Key)
	assert.Equal(t, "op1", affinity.Operator)
	assert.Equal(t, []string{"val1"}, affinity.Values)

	// Test case 2: Empty key
	affinity = NewAffinity("", "op1", []string{"val1"})
	assert.Nil(t, affinity)

	// Test case 3: Empty operator
	affinity = NewAffinity("key1", "", []string{"val1"})
	assert.Nil(t, affinity)
}

func TestNewTaskAffinity(t *testing.T) {
	// Test case 1: Valid input
	required := []*Affinity{NewAffinity("key1", "op1", []string{"val1"})}
	preferred := []*Affinity{NewAffinity("key2", "op2", []string{"val2"})}
	taskAffinity := NewTaskAffinity(required, preferred)
	assert.NotNil(t, taskAffinity)
	assert.Equal(t, required, taskAffinity.Required)
	assert.Equal(t, preferred, taskAffinity.Preferred)
}

func TestTaskAffinity_AddAffinityInTaskAffinity(t *testing.T) {
	// Test case 1: Add to required list
	taskAffinity := &TaskAffinity{}
	affinity := NewAffinity("key1", "op1", []string{"val1"})
	taskAffinity = taskAffinity.AddAffinityInTaskAffinity(affinity, true)
	assert.NotNil(t, taskAffinity)
	assert.NotNil(t, taskAffinity.Required)
	assert.Equal(t, 1, len(taskAffinity.Required))

	// Test case 2: Add to preferred list
	taskAffinity = &TaskAffinity{}
	affinity = NewAffinity("key2", "op2", []string{"val2"})
	taskAffinity = taskAffinity.AddAffinityInTaskAffinity(affinity, false)
	assert.NotNil(t, taskAffinity)
	assert.NotNil(t, taskAffinity.Preferred)
	assert.Equal(t, 1, len(taskAffinity.Preferred))
}

func TestTaskAffinity_DeleteAffinityInTaskAffinity(t *testing.T) {
	// Test case 1: Delete from required list
	taskAffinity := &TaskAffinity{Required: []*Affinity{NewAffinity("key1", "op1", []string{"val1"})}}
	assert.NotNil(t, taskAffinity.Required)
	assert.Equal(t, true, taskAffinity.HasAffinityInTaskAffinity("key1", "op1", true))
	taskAffinity = taskAffinity.DeleteAffinityInTaskAffinity("key1", "op1", "", true)
	assert.Equal(t, false, taskAffinity.HasAffinityInTaskAffinity("key1", "op1", true))
	taskAffinity = &TaskAffinity{Required: []*Affinity{NewAffinity("key1", "op1", []string{"val1", "val2"})}}
	taskAffinity = taskAffinity.DeleteAffinityInTaskAffinity("key1", "op1", "val1", true)
	assert.Equal(t, true, taskAffinity.HasAffinityInTaskAffinity("key1", "op1", true))
	taskAffinity = taskAffinity.DeleteAffinityInTaskAffinity("key1", "op1", "val2", true)
	assert.Equal(t, false, taskAffinity.HasAffinityInTaskAffinity("key1", "op1", true))

	// Test case 2: Delete from preferred list
	taskAffinity = &TaskAffinity{Preferred: []*Affinity{NewAffinity("key2", "op2", []string{"val2"})}}
	assert.NotNil(t, taskAffinity)
	assert.Equal(t, true, taskAffinity.HasAffinityInTaskAffinity("key2", "op2", false))
	taskAffinity = taskAffinity.DeleteAffinityInTaskAffinity("key2", "op2", "", false)
	assert.Equal(t, false, taskAffinity.HasAffinityInTaskAffinity("key1", "op1", false))
}

func TestTaskAffinity_GetAffinityInTaskAffinity(t *testing.T) {
	// Test case 1: Get from required list
	taskAffinity := &TaskAffinity{Required: []*Affinity{NewAffinity("key1", "op1", []string{"val1"})}}
	affinity := taskAffinity.GetAffinityInTaskAffinity("key1", "op1", true)
	assert.NotNil(t, affinity)
	assert.Equal(t, "key1", affinity.Key)

	// Test case 2: Get from preferred list
	taskAffinity = &TaskAffinity{Preferred: []*Affinity{NewAffinity("key2", "op2", []string{"val2"})}}
	affinity = taskAffinity.GetAffinityInTaskAffinity("key2", "op2", false)
	assert.NotNil(t, affinity)
	assert.Equal(t, "key2", affinity.Key)

	// Test case 3: Not found
	affinity = taskAffinity.GetAffinityInTaskAffinity("key3", "op3", true)
	assert.Nil(t, affinity)
}

func TestToV1TaskAffinity(t *testing.T) {
	// Test case 1: Convert non-nil TaskAffinity
	taskAffinity := &TaskAffinity{
		Required: []*Affinity{
			NewAffinity("key1", "op1", []string{"val1"}),
		},
		Preferred: []*Affinity{
			NewAffinity("key2", "op2", []string{"val2"}),
		},
	}
	v1TaskAffinity := ToV1TaskAffinity(taskAffinity)
	assert.NotNil(t, v1TaskAffinity)
	assert.NotNil(t, v1TaskAffinity.Required)
	assert.NotNil(t, v1TaskAffinity.Preferred)
	assert.Equal(t, 1, len(v1TaskAffinity.Required))
	assert.Equal(t, 1, len(v1TaskAffinity.Preferred))

	// Test case 2: Convert nil TaskAffinity
	v1TaskAffinity = ToV1TaskAffinity(nil)
	assert.Nil(t, v1TaskAffinity)
}

func TestToBizTaskAffinity(t *testing.T) {
	// Test case 1: Convert non-nil v1.TaskAffinity
	v1TaskAffinity := &mppv1.TaskAffinity{
		Required: []*mppv1.Affinity{
			{
				Key:      "key1",
				Operator: "op1",
				Values:   []string{"val1"},
			},
		},
		Preferred: []*mppv1.Affinity{
			{
				Key:      "key2",
				Operator: "op2",
				Values:   []string{"val2"},
			},
		},
	}
	taskAffinity := ToBizTaskAffinity(v1TaskAffinity)
	assert.NotNil(t, taskAffinity)
	assert.NotNil(t, taskAffinity.Required)
	assert.NotNil(t, taskAffinity.Preferred)
	assert.Equal(t, 1, len(taskAffinity.Required))
	assert.Equal(t, 1, len(taskAffinity.Preferred))

	// Test case 2: Convert nil v1.TaskAffinity
	taskAffinity = ToBizTaskAffinity(nil)
	assert.Nil(t, taskAffinity)
}

func TestTaskAffinity_HasAffinityInTaskAffinity(t *testing.T) {
	// Test case 1: Present in required list
	taskAffinity := &TaskAffinity{Required: []*Affinity{NewAffinity("key1", "op1", []string{"val1"})}}
	assert.True(t, taskAffinity.HasAffinityInTaskAffinity("key1", "op1", true))

	// Test case 2: Present in preferred list
	taskAffinity = &TaskAffinity{Preferred: []*Affinity{NewAffinity("key2", "op2", []string{"val2"})}}
	assert.True(t, taskAffinity.HasAffinityInTaskAffinity("key2", "op2", false))

	// Test case 3: Not present
	taskAffinity = &TaskAffinity{}
	assert.False(t, taskAffinity.HasAffinityInTaskAffinity("key3", "op3", true))
}

func TestTaskAffinity_AddMigrateAffinity(t *testing.T) {
	// Test case 1: Add to existing NotIn affinity
	taskAffinity := &TaskAffinity{
		Preferred: []*Affinity{
			NewAffinity(string(AffinityKey_KEY_WORKER_ID), string(metav1.LabelSelectorOpNotIn), []string{"worker1"}),
		},
	}
	taskAffinity.AddMigrateAffinity("worker2", false)
	workerIds := taskAffinity.GetAffinityInTaskAffinity(string(AffinityKey_KEY_WORKER_ID), string(metav1.LabelSelectorOpNotIn), false).Values
	assert.NotNil(t, workerIds)
	assert.Contains(t, workerIds, "worker1")
	assert.Contains(t, workerIds, "worker2")

	// Test case 2: Add new NotIn affinity
	taskAffinity = &TaskAffinity{}
	taskAffinity.AddMigrateAffinity("worker1", false)
	workerIds = taskAffinity.GetAffinityInTaskAffinity(string(AffinityKey_KEY_WORKER_ID), string(metav1.LabelSelectorOpNotIn), false).Values
	assert.NotNil(t, workerIds)
	assert.Contains(t, workerIds, "worker1")
}
