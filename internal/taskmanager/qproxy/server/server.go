package server

import (
	"fmt"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/qproxy/worker"
	"mpp/pkg/redis"
	"net/http"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/hibiken/asynq"
	asynqMetrics "github.com/hibiken/asynq/x/metrics"
	"github.com/hibiken/asynqmon"
	"github.com/prometheus/client_golang/prometheus"
)

type AsynqServer struct {
	asynqServer  *asynq.Server
	redisConnOpt asynq.RedisConnOpt
	promReg      prometheus.Registerer
	wr           map[string]*worker.AsyncWorker
	httpHandler  *asynqmon.HTTPHandler
	inspector    *asynq.Inspector
}

func NewAsynqServer(conf *conf.Bootstrap, promReg prometheus.Registerer) (*AsynqServer, func(), error) {
	workers := map[string]*worker.AsyncWorker{}
	return newAsynqServer(conf, workers, promReg)
}

func newAsynqServer(conf *conf.Bootstrap, wr map[string]*worker.AsyncWorker, promReg prometheus.Registerer) (*AsynqServer, func(), error) {
	t := &AsynqServer{
		promReg: promReg,
	}

	if conf.GetRedisUri() == "" {
		panic(fmt.Errorf("unsupported redis endpoint"))
	}

	var err error
	uri := conf.GetData().Redis.Endpoint

	t.redisConnOpt, err = redis.ParseRedisURI(uri)
	if err != nil {
		return nil, func() {}, err
	}

	t.inspector = asynq.NewInspector(t.redisConnOpt)

	t.newMetricsCollector()

	t.wr = wr

	return t, t.Shutdown, nil
}

func (s *AsynqServer) Dashboard(rootPath string) http.Handler {
	h := asynqmon.New(asynqmon.Options{
		RootPath:     rootPath, // RootPath specifies the root for asynqmon app
		RedisConnOpt: s.redisConnOpt,
	})
	return h
}

func (s *AsynqServer) newMetricsCollector() {
	if s.promReg != nil {
		s.promReg.MustRegister(asynqMetrics.NewQueueMetricsCollector(s.inspector))
	}
}

func (s *AsynqServer) AddWorker(workerType string, w *worker.AsyncWorker) {
	s.wr[workerType] = w
}

func (s *AsynqServer) BatchWorker() {
	// 要抢个分布式锁
	queues, err := s.inspector.Queues()
	if err != nil {
		log.Error(err)
	}
	const pageSize = 500
	for _, queueName := range queues {
		for page := 1; s.handlePendingTasks(queueName, page, pageSize); page++ {
			if page > 1000 {
				log.Error("why so many tasks")
			}
		}
	}
}

// asynq优雅退出,程序退出时调用
func (s *AsynqServer) Shutdown() {
	log.Infof("shutdown asynq server...")
	s.asynqServer.Shutdown()
}

func (s *AsynqServer) handlePendingTasks(queueName string, page int, pageSize int) (isEnd bool) {
	tasks, err := s.inspector.ListPendingTasks(queueName, asynq.Page(page), asynq.PageSize(pageSize))
	if err != nil {
		log.Error(err)
		return true
	}
	if len(tasks) == 0 {
		return true
	}

	//s.inspector.ArchiveTask()
	return false
}

func (s *AsynqServer) handleFailedTasks(queueName string, page int, pageSize int) (isEnd bool) {
	tasks, err := s.inspector.ListRetryTasks(queueName, asynq.Page(page), asynq.PageSize(pageSize))
	if err != nil {
		log.Error(err)
		return true
	}
	if len(tasks) == 0 {
		return true
	}

	//s.inspector.ArchiveTask()
	return false
}

func (s *AsynqServer) Start() error {
	s.asynqServer = asynq.NewServer(
		s.redisConnOpt,
		asynq.Config{
			// Specify how many concurrent workers to use
			Concurrency: 10000,
			// Optionally specify multiple queues with different priority.
			Queues: map[string]int{
				"critical": 0,
				"default":  9,
				"low":      1,
			},
			// See the godoc for other configuration options
			//边转边播适配：队列轮询间隔改为100ms
			TaskCheckInterval: 100 * time.Millisecond,
		},
	)
	// mux maps a type to a handler
	mux := asynq.NewServeMux()
	//mux.HandleFunc(tasks.TypeEmailDelivery, tasks.HandleEmailDeliveryTask)
	for name, handler := range s.wr {
		mux.Handle(name, handler)
	}

	if err := s.asynqServer.Start(mux); err != nil {
		return err
	}
	return nil
}
