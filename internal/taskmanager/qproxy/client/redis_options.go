package client

import (
	"net/url"
	"time"

	"github.com/hibiken/asynq"
)

func ParseRedisUri(uri string) (*asynq.RedisClientOpt, error) {
	u, err := url.Parse(uri)
	if err != nil {
		return nil, err
	}
	o := &asynq.RedisClientOpt{
		Network:      "tcp",
		Addr:         u.Host,
		DB:           0,
		DialTimeout:  time.Millisecond * 50,
		ReadTimeout:  time.Second * 3,
		WriteTimeout: time.Second * 3,
		PoolSize:     100,
		TLSConfig:    nil,
	}
	if u.Scheme != "" && u.Scheme != "redis" {
		o.Network = u.Scheme
	}
	if u.User != nil {
		o.Username = u.User.Username()
		o.Password, _ = u.User.Password()
	}
	// todo parse other params
	return o, nil
}
