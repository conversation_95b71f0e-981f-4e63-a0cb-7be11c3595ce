package client

import (
	"github.com/bytedance/mockey"
	"github.com/hibiken/asynq"
	"github.com/stretchr/testify/assert"
	"mpp/internal/taskmanager/biz/common"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/qproxy/worker"
	"testing"
)

/**
 * @Author: qinxin
 * @Date: 2024/9/29 10:04
 * @Desc:
 * @Version 1.0
 */

var asyncQueueClientTest *AsyncQueueClient
var defalultworkerTask *worker.AsyncTask
var defalultAsyncTask *asynq.TaskInfo
var testTaskId = "test_task_id"

func init() {
	asyncQueueClientTest = NewAsyncQueueClient(&conf.Bootstrap{Data: &conf.Data{Redis: &conf.Data_Redis{Endpoint: "redis://127.0.0.1:6379"}}})
	asyncQueueClientTest.client = asynq.NewClient(asyncQueueClientTest.redisConnOpt)

	defalultworkerTask = worker.NewAsyncTask("3", testTaskId, nil)

	defalultAsyncTask = &asynq.TaskInfo{
		ID:   "test_task_id",
		Type: "3",
	}
}

// TestNewAsyncQueueClient 测试用例：

func TestNewAsync_Enqueue(t *testing.T) {

	defer mockey.Mock((*asynq.Client).Enqueue).Return(nil, common.ErrInternalError).Build().UnPatch()

	err := asyncQueueClientTest.Enqueue(defalultworkerTask)

	assert.NotNil(t, err)
}

func TestAsyncQueueClient_Cancel(t *testing.T) {
	mockey.PatchConvey("Cancel returns success", t, func() {
		defer mockey.Mock((*asynq.Inspector).CancelProcessing).Return(nil).Build().UnPatch()
		err := asyncQueueClientTest.CancelTask(testTaskId)
		assert.Nil(t, err)
	})
	mockey.PatchConvey("Cancel returns error", t, func() {
		defer mockey.Mock((*asynq.Inspector).CancelProcessing).Return(common.ErrInternalError).Build().UnPatch()
		err := asyncQueueClientTest.CancelTask(testTaskId)
		assert.NotNil(t, err)
	})
}
