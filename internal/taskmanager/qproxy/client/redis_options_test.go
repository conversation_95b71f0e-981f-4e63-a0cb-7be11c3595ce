package client

import (
	"github.com/hibiken/asynq"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

/**
 * @Author: qinxin
 * @Date: 2024/9/29 10:05
 * @Desc:
 * @Version 1.0
 */

// 测试案例1 - 正常情况下的解析
// 设计思路：提供一个标准的 Redis URI，验证是否能正确解析出各个字段。
// 编号：TC001
func TestParseRedisUriNormal(t *testing.T) {
	expected := &asynq.RedisClientOpt{
		Network:      "tcp",
		Addr:         "localhost:6379",
		DB:           0,
		DialTimeout:  50 * time.Millisecond,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolSize:     100,
		TLSConfig:    nil,
		Username:     "user",
		Password:     "pass",
	}
	inputURI := "redis://user:pass@localhost:6379"
	result, err := ParseRedisUri(inputURI)
	assert.NoError(t, err)
	assert.Equal(t, expected, result)
}

// 测试案例2 - 错误的 URI 格式
// 设计思路：提供一个错误格式的 URI，验证是否返回正确的错误信息。
// 编号：TC002
func TestParseRedisUriInvalidFormat(t *testing.T) {
	inputURI := "invalid://uri"
	_, err := ParseRedisUri(inputURI)
	assert.Nil(t, err)
}

// 测试案例3 - 不支持的 Scheme
// 设计思路：提供一个不支持的 scheme 的 URI，验证是否能正确设置 Network 字段。
// 编号：TC003
func TestParseRedisUriUnsupportedScheme(t *testing.T) {
	expected := &asynq.RedisClientOpt{
		Network:      "unsupported",
		Addr:         "localhost:6379",
		DB:           0,
		DialTimeout:  50 * time.Millisecond,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolSize:     100,
		TLSConfig:    nil,
	}
	inputURI := "unsupported://localhost:6379"
	result, err := ParseRedisUri(inputURI)
	assert.NoError(t, err)
	assert.Equal(t, expected, result)
}
