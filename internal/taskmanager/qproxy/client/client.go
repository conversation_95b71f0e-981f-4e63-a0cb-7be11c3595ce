package client

import (
	"fmt"
	"mpp/pkg/redis"
	"time"

	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/qproxy/worker"

	"github.com/avast/retry-go"
	"github.com/hibiken/asynq"
)

type AsyncQueueClient struct {
	client       *asynq.Client
	redisConnOpt asynq.RedisConnOpt
	inspector    *asynq.Inspector
}

func NewAsyncQueueClient(conf *conf.Bootstrap) *AsyncQueueClient {
	t := &AsyncQueueClient{}
	var err error

	uri := conf.GetRedisUri()
	if uri == "" {
		panic(fmt.Errorf("unsupported redis endpoint"))
	}

	t.redisConnOpt, err = redis.ParseRedisURI(uri)
	if err != nil {
		panic(err)
	}
	t.client = asynq.NewClient(t.redisConnOpt)
	t.inspector = asynq.NewInspector(t.redisConnOpt)
	return t
}

func (c *AsyncQueueClient) Enqueue(task *worker.AsyncTask) error {
	asynqTask := asynq.NewTask(task.GetType(), task.GetData(), asynq.TaskID(task.GetID()), asynq.Timeout(time.Hour*24*5))

	var err error
	err = retry.Do(
		func() error {
			_, err = c.client.Enqueue(asynqTask)
			return err
		},
		retry.Delay(time.Millisecond),
		retry.Attempts(3),
		retry.DelayType(retry.FixedDelay),
		retry.LastErrorOnly(true),
	)

	if err != nil {
		return err
	}

	return nil
}

func (c *AsyncQueueClient) CancelTask(taskID string) error {
	err := c.inspector.CancelProcessing(taskID)
	if err != nil {
		return err
	}

	return nil
}
