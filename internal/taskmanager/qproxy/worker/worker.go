package worker

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"runtime"

	"github.com/hibiken/asynq"
)

type TaskRunner interface {
	Execute(context.Context, *AsyncTask) error
}

type AsyncWorker struct {
	taskRunner TaskRunner
	//inspector  *asynq.Inspector
}

func NewAsyncWorker(taskRunner TaskRunner) *AsyncWorker {
	return &AsyncWorker{taskRunner: taskRunner}
}

func (w *AsyncWorker) ProcessTask(ctx context.Context, task *asynq.Task) error {
	// todo 获取任务 只负责获取 资源和
	defer func() {
		var err error
		if rerr := recover(); rerr != nil {
			buf := make([]byte, 64<<10) //nolint:gomnd
			n := runtime.Stack(buf, false)
			buf = buf[:n]
			// 防止写日志失败
			err = fmt.Errorf("%v: %s\n", rerr, buf)
		}
		if err != nil {
			log.Errorf("batchtask execution failed. taskId:%s, taskPayload:%s, err: %v", task.ID(), string(task.Payload()), err)
		}
	}()

	err := w.taskRunner.Execute(ctx, NewAsyncTask(task.Type(), task.ID(), task.Payload()))

	return err
}
