package worker

const (
	TaskCommon string = "common"
)

type AsyncTask struct {
	taskID   string
	queue    string
	taskType string
	data     []byte
	// 用来存储tracing的数据
	header map[string]string
}

func NewAsyncTask(taskType string, taskId string, bs []byte) *AsyncTask {
	return &AsyncTask{taskType: taskType, taskID: taskId, data: bs}
}

func (t *AsyncTask) GetType() string {
	return t.taskType
}

func (t *AsyncTask) GetID() string {
	return t.taskID
}

func (t *AsyncTask) GetData() []byte {
	return t.data
}

func (t *AsyncTask) SetData(bs []byte) {
	t.data = bs
}
