package databus

import (
	"context"
	"encoding/json"
	"fmt"

	"mpp/internal/taskmanager/repository"

	"github.com/redis/go-redis/v9"
	pb "proto.mpp/api/taskmanager/v1"
)

type ConfigService struct {
	redisClient *repository.RedisClient
}

func NewConfigService(redisClient *repository.RedisClient) *ConfigService {
	return &ConfigService{
		redisClient: redisClient,
	}
}

func (c *ConfigService) GetConfig(key string) (pb.MeshConfigValue, error) {
	return c.innerGetConfig(key)
}

func (c *ConfigService) SetConfig(key string, value pb.MeshConfigValue) error {
	return c.innerSetConfig(key, value)
}

func (c *ConfigService) DeleteConfig(key string) error {
	return c.innerDeleteConfig(key)
}

// innerGetConfig retrieves the configuration value for the given key from Redis.
func (c *ConfigService) innerGetConfig(key string) (pb.MeshConfigValue, error) {
	innerKey := "mesh_config_" + key
	keyInfo := fmt.Sprintf("key:%s", key)
	var val pb.MeshConfigValue

	// get from Redis
	res, err := c.redisClient.GetClient().Get(context.Background(), innerKey).Result()
	if err != nil && err != redis.Nil { // redis.Nil key does not exist
		return val, fmt.Errorf("failed to get key from Redis: %w, %s", err, keyInfo)
	} else if err == redis.Nil { // key does not exist
		return val, fmt.Errorf("key does not exist: %s", keyInfo)
	}

	// json Unmarshal
	err = json.Unmarshal([]byte(res), &val)
	if err != nil {
		return val, fmt.Errorf("failed to unmarshal JSON: %w, %s", err, keyInfo)
	}

	return val, nil
}

// SetConfig into Redis
func (c *ConfigService) innerSetConfig(key string, value pb.MeshConfigValue) error {
	innerKey := "mesh_config_" + key
	keyInfo := fmt.Sprintf("key:%s", key)

	// json marshal
	jsonValue, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("set config failed %s, failed to marshal JSON: %w", keyInfo, err)
	}

	// insert into Redis
	err = c.redisClient.GetClient().Set(context.Background(), innerKey, jsonValue, 0).Err()
	if err != nil {
		return fmt.Errorf("set config failed %s, failed to set key in Redis: %w", keyInfo, err)
	}

	return nil
}

// innerDeleteConfig removes a configuration entry with the specified key from Redis.
func (c *ConfigService) innerDeleteConfig(key string) error {
	innerKey := "mesh_config_" + key
	keyInfo := fmt.Sprintf("key:%s", key)

	// delete from Redis
	// if key does not exist, ignore(err = nil).
	err := c.redisClient.GetClient().Del(context.Background(), innerKey).Err()
	if err != nil {
		return fmt.Errorf("failed to delete key from Redis: %w, %s", err, keyInfo)
	}

	return nil
}
