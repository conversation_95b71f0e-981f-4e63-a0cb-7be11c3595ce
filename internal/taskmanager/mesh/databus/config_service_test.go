package databus

import (
	"context"
	"encoding/json"
	"github.com/alicebob/miniredis/v2"
	"github.com/stretchr/testify/assert"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/repository"
	"testing"

	"github.com/golang/mock/gomock"
	pb "proto.mpp/api/taskmanager/v1"
)

var configService *ConfigService
var redisCli *repository.RedisClient
var mini *miniredis.Miniredis

func initTest(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mini = miniredis.RunT(t)

	config := &conf.Bootstrap{
		Data: &conf.Data{
			Redis: &conf.Data_Redis{
				Endpoint: "redis://" + mini.Addr(),
			},
		},
	}

	redisCli = repository.NewRedisClient(config)

	configService = &ConfigService{
		redisClient: redisCli,
	}
}

func TestConfigService_GetConfig(t *testing.T) {
	initTest(t)
	defer mini.Close()

	ctx := context.Background()
	key := "test_key"
	innerKey := "mesh_config_" + key
	expectedValue := pb.MeshConfigValue{
		WorkerId: "test_value",
	}
	expectedJSON, _ := json.Marshal(expectedValue)

	redisCli.GetClient().Set(ctx, innerKey, expectedJSON, 0)

	value, err := configService.GetConfig(key)
	assert.Nil(t, err)
	assert.Equal(t, expectedValue, value)
}

func TestConfigService_GetConfig_NotFound(t *testing.T) {
	initTest(t)
	defer mini.Close()

	ctx := context.Background()
	key := "test_key"
	innerKey := "mesh_config_" + key
	redisCli.GetClient().Del(ctx, innerKey)
	_, err := configService.GetConfig(key)
	assert.Contains(t, err.Error(), "key does not exist")
}

func TestConfigService_SetConfig(t *testing.T) {
	initTest(t)
	defer mini.Close()

	ctx := context.Background()
	key := "test_key"
	innerKey := "mesh_config_" + key
	value := pb.MeshConfigValue{
		WorkerId: "test_value",
	}
	expectedJSON, _ := json.Marshal(value)
	err := configService.SetConfig(key, value)
	res, err := redisCli.GetClient().Get(ctx, innerKey).Result()
	assert.Nil(t, err)
	assert.Equal(t, string(expectedJSON), res)
}

func TestConfigService_SetConfig_MarshalError(t *testing.T) {
	initTest(t)
	defer mini.Close()

	ctx := context.Background()
	key := "test_key"
	value := pb.MeshConfigValue{
		WorkerId: "test_value",
	}

	err := configService.SetConfig(key, value)
	assert.Nil(t, err)
	_, err = redisCli.GetClient().Get(ctx, key).Result()
	assert.NotNil(t, err)
}

func TestConfigService_DeleteConfig(t *testing.T) {
	initTest(t)
	defer mini.Close()

	ctx := context.Background()
	key := "test_key"
	innerKey := "mesh_config_" + key
	redisCli.GetClient().Set(ctx, innerKey, "test_value", 0)
	err := configService.DeleteConfig(key)
	assert.Nil(t, err)
}
