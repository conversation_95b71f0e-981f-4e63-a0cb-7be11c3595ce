package mesh

import (
	"context"
	"github.com/alicebob/miniredis/v2"
	"mpp/internal/taskmanager/conf"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"
	pb "proto.mpp/api/taskmanager/v1"
	"testing"
)

func MockMeshService(t *testing.T) *MeshService {
	s := miniredis.RunT(t)
	// defer s.Close()
	// shutdown when test finished
	t.Cleanup(func() { s.Close() })

	config := &conf.Bootstrap{
		Data: &conf.Data{
			Redis: &conf.Data_Redis{
				Endpoint: "redis://" + s.Addr(),
			},
		},
	}

	redis := repository.NewRedisClient(config)
	return NewMeshService(redis)
}

// TestMeshConfigService tests:
// 1. SetConfig
// 2. GetConfig
// 3. DeleteConfig
func TestMeshConfigService(t *testing.T) {
	m := MockMeshService(t)
	// redis will be shutdown when test finished
	// defer func() {}()

	// 1. SetConfig
	setReq := &pb.MeshSetConfigRequest{
		Key: "test-key-001",
		Value: &pb.MeshConfigValue{
			WorkerId: "test-worker-id-001",
			WorkerIp: "test-worker-ip-001",
			Port:     8080,
		},
		RequestId: "test-request-id-001",
	}
	setRes, err := m.SetConfig(context.Background(), setReq)
	if err != nil || !models.IsSuccess(*setRes.Result) {
		t.Fatalf("Set config failed: %v", setRes)
	}

	// 2.1 GetConfig
	getReq := &pb.MeshGetConfigRequest{
		Key:       "test-key-001",
		RequestId: "test-request-id-001",
	}
	getRes, err := m.GetConfig(context.Background(), getReq)
	if err != nil || !models.IsSuccess(*getRes.Result) {
		t.Fatalf("get config failed: %v", getRes)
	}
	t.Logf("get config result: %v", *getRes.Result)

	// 2.2 GetConfig (not found)
	getReq1 := &pb.MeshGetConfigRequest{
		Key:       "test-key-001-1",
		RequestId: "test-request-id-001-1",
	}
	getRes1, err := m.GetConfig(context.Background(), getReq1)
	if err != nil || !models.IsNotFound(*getRes1.Result) {
		t.Fatalf("get config failed: %v", getRes)
	}
	t.Logf("get config result: %v", *getRes1.Result)

	// 3. DeleteConfig
	deleteReq := &pb.MeshDeleteConfigRequest{
		Key:       "test-key-001",
		RequestId: "test-request-id-001",
	}
	m.DeleteConfig(context.Background(), deleteReq)
	// Check if the config is deleted
	ResAfterDelete, err := m.GetConfig(context.Background(), getReq)
	if models.IsSuccess(*ResAfterDelete.Result) {
		t.Fatalf("Expected 'not found' error after deletion but got:%v", ResAfterDelete)
	}

}
