package mesh

import (
	"context"
	"fmt"
	"mpp/internal/taskmanager/mesh/databus"
	"mpp/internal/taskmanager/models"
	"mpp/internal/taskmanager/repository"
	"mpp/pkg/tracer/tasker_tracing"
	"mpp/pkg/utils"

	pb "proto.mpp/api/taskmanager/v1"
)

type MeshService struct {
	pb.UnimplementedMeshServer
	configService *databus.ConfigService
	meshTracer    *tasker_tracing.Tracer
}

func NewMeshService(redisClient *repository.RedisClient) *MeshService {
	return &MeshService{
		configService: databus.NewConfigService(redisClient),
		meshTracer:    tasker_tracing.NewTracer(tasker_tracing.WithTracerName("meshService")),
	}
}

func (m *MeshService) ValidateSetConfigReq(request *pb.MeshSetConfigRequest) error {
	var errs []error

	if request == nil {
		errs = append(errs, fmt.Erro<PERSON>("request is nil"))
	}
	if request.RequestId == "" {
		errs = append(errs, fmt.Errorf("requestId is empty"))
	}
	keyLog := fmt.Sprintf("ValidateSetConfigReq failed, requestId = %s", request.RequestId)

	if request.Key == "" {
		errs = append(errs, fmt.Errorf("%s, key is empty", keyLog))
	}
	//if request.Value ==  {
	//	errs = append(errs, fmt.Errorf("%s, value is empty", keyLog))
	//}

	if len(errs) == 0 {
		return nil
	}
	return utils.AggregateErrors(errs)
}

func (m *MeshService) ValidateGetConfigReq(request *pb.MeshGetConfigRequest) error {
	var errs []error

	if request == nil {
		errs = append(errs, fmt.Errorf("request is nil"))
	}
	if request.RequestId == "" {
		errs = append(errs, fmt.Errorf("requestId is empty"))
	}
	keyLog := fmt.Sprintf("ValidateGetConfigReq failed, requestId = %s", request.RequestId)

	if request.Key == "" {
		errs = append(errs, fmt.Errorf("%s, key is empty", keyLog))
	}

	if len(errs) == 0 {
		return nil
	}
	return utils.AggregateErrors(errs)
}

func (m *MeshService) ValidateDeleteConfigReq(request *pb.MeshDeleteConfigRequest) error {
	var errs []error

	if request == nil {
		errs = append(errs, fmt.Errorf("request is nil"))
	}
	if request.RequestId == "" {
		errs = append(errs, fmt.Errorf("requestId is empty"))
	}
	keyLog := fmt.Sprintf("ValidateDeleteConfigReq failed, requestId = %s", request.RequestId)

	if request.Key == "" {
		errs = append(errs, fmt.Errorf("%s, key is empty", keyLog))
	}

	if len(errs) == 0 {
		return nil
	}
	return utils.AggregateErrors(errs)
}

func (m *MeshService) SetConfig(ctx context.Context, request *pb.MeshSetConfigRequest) (*pb.MeshSetConfigResponse, error) {
	resp := &pb.MeshSetConfigResponse{
		Result: &models.ERROR_SUCCESS,
	}

	// TODO: Add tracer
	defer func() {
		//	 TODO: implement this
	}()

	if err := m.ValidateSetConfigReq(request); err != nil {
		resp.Result = models.WithError(models.ERROR_VALIDATE_REQ_FIALED, err)
		return resp, nil
	}

	if err := m.configService.SetConfig(request.Key, *request.Value); err != nil {
		resp.Result = models.WithError(models.ERRPR_MESH_INTERNAL_ERROR, err)
		return resp, nil
	}

	return resp, nil
}

func (m *MeshService) GetConfig(ctx context.Context, request *pb.MeshGetConfigRequest) (*pb.MeshGetConfigResponse, error) {
	resp := &pb.MeshGetConfigResponse{
		Result: &models.ERROR_SUCCESS,
		Value:  nil,
	}

	// TODO: Add tracer
	defer func() {
		//	 TODO: implement this
	}()

	if err := m.ValidateGetConfigReq(request); err != nil {
		resp.Result = models.WithError(models.ERROR_VALIDATE_REQ_FIALED, err)
		return resp, nil
	}

	v, err := m.configService.GetConfig(request.Key)
	if err != nil {
		resp.Result = models.WithError(models.ERRPR_MESH_CONFIG_NOT_FOUND, err)
		return resp, nil
	}
	resp.Value = &v

	return resp, nil
}

func (m *MeshService) DeleteConfig(ctx context.Context, request *pb.MeshDeleteConfigRequest) (*pb.MeshDeleteConfigResponse, error) {
	// default response: success
	resp := &pb.MeshDeleteConfigResponse{
		Result: &models.ERROR_SUCCESS,
	}
	//TODO: Add tracer
	defer func() {
		//	 TODO: implement this
	}()

	// validate fail
	if err := m.ValidateDeleteConfigReq(request); err != nil {
		resp.Result = models.WithError(models.ERROR_VALIDATE_REQ_FIALED, err)
		return resp, nil
	}

	// innerDelete will return success if key not found
	err := m.configService.DeleteConfig(request.Key)
	if err != nil {
		resp.Result = models.WithError(models.ERRPR_MESH_INTERNAL_ERROR, err)
		return resp, nil
	}

	return resp, nil
}
