// Code generated by MockGen. DO NOT EDIT.
// Source: internal/taskmanager/repository/source_stream_task.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	domain "mpp/internal/taskmanager/models/domain"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockSourceStreamTaskRepo is a mock of SourceStreamTaskRepo interface.
type MockSourceStreamTaskRepo struct {
	ctrl     *gomock.Controller
	recorder *MockSourceStreamTaskRepoMockRecorder
}

// MockSourceStreamTaskRepoMockRecorder is the mock recorder for MockSourceStreamTaskRepo.
type MockSourceStreamTaskRepoMockRecorder struct {
	mock *MockSourceStreamTaskRepo
}

// NewMockSourceStreamTaskRepo creates a new mock instance.
func NewMockSourceStreamTaskRepo(ctrl *gomock.Controller) *MockSourceStreamTaskRepo {
	mock := &MockSourceStreamTaskRepo{ctrl: ctrl}
	mock.recorder = &MockSourceStreamTaskRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSourceStreamTaskRepo) EXPECT() *MockSourceStreamTaskRepoMockRecorder {
	return m.recorder
}

// Insert mocks base method.
func (m *MockSourceStreamTaskRepo) Insert(arg0 context.Context, arg1 *domain.SourceStreamTask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Insert indicates an expected call of Insert.
func (mr *MockSourceStreamTaskRepoMockRecorder) Insert(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockSourceStreamTaskRepo)(nil).Insert), arg0, arg1)
}

// ListBySourceStreamId mocks base method.
func (m *MockSourceStreamTaskRepo) ListBySourceStreamId(arg0 context.Context, arg1 string) ([]*domain.SourceStreamTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBySourceStreamId", arg0, arg1)
	ret0, _ := ret[0].([]*domain.SourceStreamTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBySourceStreamId indicates an expected call of ListBySourceStreamId.
func (mr *MockSourceStreamTaskRepoMockRecorder) ListBySourceStreamId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBySourceStreamId", reflect.TypeOf((*MockSourceStreamTaskRepo)(nil).ListBySourceStreamId), arg0, arg1)
}
