// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/dev_manus/mpp/vendor/proto.mpp/api/taskscheduler/v1/resource_http.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	v1 "proto.mpp/api/taskscheduler/v1"
	reflect "reflect"

	http "github.com/go-kratos/kratos/v2/transport/http"
	gomock "github.com/golang/mock/gomock"
)

// MockResourceHTTPServer is a mock of ResourceHTTPServer interface.
type MockResourceHTTPServer struct {
	ctrl     *gomock.Controller
	recorder *MockResourceHTTPServerMockRecorder
}

// MockResourceHTTPServerMockRecorder is the mock recorder for MockResourceHTTPServer.
type MockResourceHTTPServerMockRecorder struct {
	mock *MockResourceHTTPServer
}

// NewMockResourceHTTPServer creates a new mock instance.
func NewMockResourceHTTPServer(ctrl *gomock.Controller) *MockResourceHTTPServer {
	mock := &MockResourceHTTPServer{ctrl: ctrl}
	mock.recorder = &MockResourceHTTPServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockResourceHTTPServer) EXPECT() *MockResourceHTTPServerMockRecorder {
	return m.recorder
}

// GetResource mocks base method.
func (m *MockResourceHTTPServer) GetResource(arg0 context.Context, arg1 *v1.GetResourceRequest) (*v1.GetResourceReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResource", arg0, arg1)
	ret0, _ := ret[0].(*v1.GetResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResource indicates an expected call of GetResource.
func (mr *MockResourceHTTPServerMockRecorder) GetResource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResource", reflect.TypeOf((*MockResourceHTTPServer)(nil).GetResource), arg0, arg1)
}

// ListLostResource mocks base method.
func (m *MockResourceHTTPServer) ListLostResource(arg0 context.Context, arg1 *v1.ListResourceRequest) (*v1.ListResourceReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListLostResource", arg0, arg1)
	ret0, _ := ret[0].(*v1.ListResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListLostResource indicates an expected call of ListLostResource.
func (mr *MockResourceHTTPServerMockRecorder) ListLostResource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListLostResource", reflect.TypeOf((*MockResourceHTTPServer)(nil).ListLostResource), arg0, arg1)
}

// ListResource mocks base method.
func (m *MockResourceHTTPServer) ListResource(arg0 context.Context, arg1 *v1.ListResourceRequest) (*v1.ListResourceReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListResource", arg0, arg1)
	ret0, _ := ret[0].(*v1.ListResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListResource indicates an expected call of ListResource.
func (mr *MockResourceHTTPServerMockRecorder) ListResource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListResource", reflect.TypeOf((*MockResourceHTTPServer)(nil).ListResource), arg0, arg1)
}

// MockResourceHTTPClient is a mock of ResourceHTTPClient interface.
type MockResourceHTTPClient struct {
	ctrl     *gomock.Controller
	recorder *MockResourceHTTPClientMockRecorder
}

// MockResourceHTTPClientMockRecorder is the mock recorder for MockResourceHTTPClient.
type MockResourceHTTPClientMockRecorder struct {
	mock *MockResourceHTTPClient
}

// NewMockResourceHTTPClient creates a new mock instance.
func NewMockResourceHTTPClient(ctrl *gomock.Controller) *MockResourceHTTPClient {
	mock := &MockResourceHTTPClient{ctrl: ctrl}
	mock.recorder = &MockResourceHTTPClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockResourceHTTPClient) EXPECT() *MockResourceHTTPClientMockRecorder {
	return m.recorder
}

// GetResource mocks base method.
func (m *MockResourceHTTPClient) GetResource(ctx context.Context, req *v1.GetResourceRequest, opts ...http.CallOption) (*v1.GetResourceReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetResource", varargs...)
	ret0, _ := ret[0].(*v1.GetResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResource indicates an expected call of GetResource.
func (mr *MockResourceHTTPClientMockRecorder) GetResource(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResource", reflect.TypeOf((*MockResourceHTTPClient)(nil).GetResource), varargs...)
}

// ListLostResource mocks base method.
func (m *MockResourceHTTPClient) ListLostResource(ctx context.Context, req *v1.ListResourceRequest, opts ...http.CallOption) (*v1.ListResourceReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListLostResource", varargs...)
	ret0, _ := ret[0].(*v1.ListResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListLostResource indicates an expected call of ListLostResource.
func (mr *MockResourceHTTPClientMockRecorder) ListLostResource(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListLostResource", reflect.TypeOf((*MockResourceHTTPClient)(nil).ListLostResource), varargs...)
}

// ListResource mocks base method.
func (m *MockResourceHTTPClient) ListResource(ctx context.Context, req *v1.ListResourceRequest, opts ...http.CallOption) (*v1.ListResourceReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListResource", varargs...)
	ret0, _ := ret[0].(*v1.ListResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListResource indicates an expected call of ListResource.
func (mr *MockResourceHTTPClientMockRecorder) ListResource(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListResource", reflect.TypeOf((*MockResourceHTTPClient)(nil).ListResource), varargs...)
}
