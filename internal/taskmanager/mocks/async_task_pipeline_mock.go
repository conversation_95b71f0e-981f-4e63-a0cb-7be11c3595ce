// Code generated by MockGen. DO NOT EDIT.
// Source: internal/taskmanager/repository/async_task_pipeline.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	models "mpp/internal/taskmanager/models"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	v1 "proto.mpp/api/common/v1"
)

// MockAsyncTaskPipelineRepo is a mock of AsyncTaskPipelineRepo interface.
type MockAsyncTaskPipelineRepo struct {
	ctrl     *gomock.Controller
	recorder *MockAsyncTaskPipelineRepoMockRecorder
}

// MockAsyncTaskPipelineRepoMockRecorder is the mock recorder for MockAsyncTaskPipelineRepo.
type MockAsyncTaskPipelineRepoMockRecorder struct {
	mock *MockAsyncTaskPipelineRepo
}

// NewMockAsyncTaskPipelineRepo creates a new mock instance.
func NewMockAsyncTaskPipelineRepo(ctrl *gomock.Controller) *MockAsyncTaskPipelineRepo {
	mock := &MockAsyncTaskPipelineRepo{ctrl: ctrl}
	mock.recorder = &MockAsyncTaskPipelineRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAsyncTaskPipelineRepo) EXPECT() *MockAsyncTaskPipelineRepoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockAsyncTaskPipelineRepo) Create(ctx context.Context, pipeline *models.Pipeline) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, pipeline)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockAsyncTaskPipelineRepoMockRecorder) Create(ctx, pipeline interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockAsyncTaskPipelineRepo)(nil).Create), ctx, pipeline)
}

// Delete mocks base method.
func (m *MockAsyncTaskPipelineRepo) Delete(ctx context.Context, pipelineId string, pipelineType v1.PipelineType) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, pipelineId, pipelineType)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Delete indicates an expected call of Delete.
func (mr *MockAsyncTaskPipelineRepoMockRecorder) Delete(ctx, pipelineId, pipelineType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockAsyncTaskPipelineRepo)(nil).Delete), ctx, pipelineId, pipelineType)
}

// FindByPipelineAndType mocks base method.
func (m *MockAsyncTaskPipelineRepo) FindByPipelineAndType(ctx context.Context, pipelineId string, pipelineType v1.PipelineType) (*models.Pipeline, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByPipelineAndType", ctx, pipelineId, pipelineType)
	ret0, _ := ret[0].(*models.Pipeline)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByPipelineAndType indicates an expected call of FindByPipelineAndType.
func (mr *MockAsyncTaskPipelineRepoMockRecorder) FindByPipelineAndType(ctx, pipelineId, pipelineType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByPipelineAndType", reflect.TypeOf((*MockAsyncTaskPipelineRepo)(nil).FindByPipelineAndType), ctx, pipelineId, pipelineType)
}

// QueryByUserId mocks base method.
func (m *MockAsyncTaskPipelineRepo) QueryByUserId(ctx context.Context, userId string) ([]*models.Pipeline, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryByUserId", ctx, userId)
	ret0, _ := ret[0].([]*models.Pipeline)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryByUserId indicates an expected call of QueryByUserId.
func (mr *MockAsyncTaskPipelineRepoMockRecorder) QueryByUserId(ctx, userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryByUserId", reflect.TypeOf((*MockAsyncTaskPipelineRepo)(nil).QueryByUserId), ctx, userId)
}

// Update mocks base method.
func (m *MockAsyncTaskPipelineRepo) Update(ctx context.Context, pipeline *models.Pipeline) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, pipeline)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockAsyncTaskPipelineRepoMockRecorder) Update(ctx, pipeline interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockAsyncTaskPipelineRepo)(nil).Update), ctx, pipeline)
}

// UpdatePipelineScheduleLevel mocks base method.
func (m *MockAsyncTaskPipelineRepo) UpdatePipelineScheduleLevel(ctx context.Context, pipeline *models.Pipeline) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePipelineScheduleLevel", ctx, pipeline)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePipelineScheduleLevel indicates an expected call of UpdatePipelineScheduleLevel.
func (mr *MockAsyncTaskPipelineRepoMockRecorder) UpdatePipelineScheduleLevel(ctx, pipeline interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePipelineScheduleLevel", reflect.TypeOf((*MockAsyncTaskPipelineRepo)(nil).UpdatePipelineScheduleLevel), ctx, pipeline)
}

// UpdatePipelineScheduleLevelByUserId mocks base method.
func (m *MockAsyncTaskPipelineRepo) UpdatePipelineScheduleLevelByUserId(ctx context.Context, pipeline *models.Pipeline) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePipelineScheduleLevelByUserId", ctx, pipeline)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePipelineScheduleLevelByUserId indicates an expected call of UpdatePipelineScheduleLevelByUserId.
func (mr *MockAsyncTaskPipelineRepoMockRecorder) UpdatePipelineScheduleLevelByUserId(ctx, pipeline interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePipelineScheduleLevelByUserId", reflect.TypeOf((*MockAsyncTaskPipelineRepo)(nil).UpdatePipelineScheduleLevelByUserId), ctx, pipeline)
}

// UpdatePipelineStatus mocks base method.
func (m *MockAsyncTaskPipelineRepo) UpdatePipelineStatus(ctx context.Context, pipeline *models.Pipeline) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePipelineStatus", ctx, pipeline)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePipelineStatus indicates an expected call of UpdatePipelineStatus.
func (mr *MockAsyncTaskPipelineRepoMockRecorder) UpdatePipelineStatus(ctx, pipeline interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePipelineStatus", reflect.TypeOf((*MockAsyncTaskPipelineRepo)(nil).UpdatePipelineStatus), ctx, pipeline)
}
