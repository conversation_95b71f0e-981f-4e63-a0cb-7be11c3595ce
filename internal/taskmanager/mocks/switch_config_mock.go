// Code generated by MockGen. DO NOT EDIT.
// Source: internal/taskmanager/repository/switch_config.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	repository "mpp/internal/taskmanager/repository"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockSwitchConfigRepo is a mock of SwitchConfigRepo interface.
type MockSwitchConfigRepo struct {
	ctrl     *gomock.Controller
	recorder *MockSwitchConfigRepoMockRecorder
}

// MockSwitchConfigRepoMockRecorder is the mock recorder for MockSwitchConfigRepo.
type MockSwitchConfigRepoMockRecorder struct {
	mock *MockSwitchConfigRepo
}

// NewMockSwitchConfigRepo creates a new mock instance.
func NewMockSwitchConfigRepo(ctrl *gomock.Controller) *MockSwitchConfigRepo {
	mock := &MockSwitchConfigRepo{ctrl: ctrl}
	mock.recorder = &MockSwitchConfigRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSwitchConfigRepo) EXPECT() *MockSwitchConfigRepoMockRecorder {
	return m.recorder
}

// Delete mocks base method.
func (m *MockSwitchConfigRepo) Delete(arg0 context.Context, arg1 *repository.SwitchConfig) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Delete", arg0, arg1)
}

// Delete indicates an expected call of Delete.
func (mr *MockSwitchConfigRepoMockRecorder) Delete(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockSwitchConfigRepo)(nil).Delete), arg0, arg1)
}

// Insert mocks base method.
func (m *MockSwitchConfigRepo) Insert(arg0 context.Context, arg1 *repository.SwitchConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Insert indicates an expected call of Insert.
func (mr *MockSwitchConfigRepoMockRecorder) Insert(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockSwitchConfigRepo)(nil).Insert), arg0, arg1)
}

// ListAll mocks base method.
func (m *MockSwitchConfigRepo) ListAll(arg0 context.Context) ([]*repository.SwitchConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAll", arg0)
	ret0, _ := ret[0].([]*repository.SwitchConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAll indicates an expected call of ListAll.
func (mr *MockSwitchConfigRepoMockRecorder) ListAll(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAll", reflect.TypeOf((*MockSwitchConfigRepo)(nil).ListAll), arg0)
}

// Query mocks base method.
func (m *MockSwitchConfigRepo) Query(arg0 context.Context, arg1 *repository.SwitchConfig) ([]*repository.SwitchConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Query", arg0, arg1)
	ret0, _ := ret[0].([]*repository.SwitchConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Query indicates an expected call of Query.
func (mr *MockSwitchConfigRepoMockRecorder) Query(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Query", reflect.TypeOf((*MockSwitchConfigRepo)(nil).Query), arg0, arg1)
}
