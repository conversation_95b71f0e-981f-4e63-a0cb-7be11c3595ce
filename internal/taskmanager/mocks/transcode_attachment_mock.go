// Code generated by MockGen. DO NOT EDIT.
// Source: internal/taskmanager/repository/transcode_attachment.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	domain "mpp/internal/taskmanager/models/domain"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockTranscodeAttachmentRepo is a mock of TranscodeAttachmentRepo interface.
type MockTranscodeAttachmentRepo struct {
	ctrl     *gomock.Controller
	recorder *MockTranscodeAttachmentRepoMockRecorder
}

// MockTranscodeAttachmentRepoMockRecorder is the mock recorder for MockTranscodeAttachmentRepo.
type MockTranscodeAttachmentRepoMockRecorder struct {
	mock *MockTranscodeAttachmentRepo
}

// NewMockTranscodeAttachmentRepo creates a new mock instance.
func NewMockTranscodeAttachmentRepo(ctrl *gomock.Controller) *MockTranscodeAttachmentRepo {
	mock := &MockTranscodeAttachmentRepo{ctrl: ctrl}
	mock.recorder = &MockTranscodeAttachmentRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTranscodeAttachmentRepo) EXPECT() *MockTranscodeAttachmentRepoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockTranscodeAttachmentRepo) Create(ctx context.Context, transcodeAttachments []*domain.TranscodeAttachment) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, transcodeAttachments)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockTranscodeAttachmentRepoMockRecorder) Create(ctx, transcodeAttachments interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockTranscodeAttachmentRepo)(nil).Create), ctx, transcodeAttachments)
}

// Delete mocks base method.
func (m *MockTranscodeAttachmentRepo) Delete(ctx context.Context, transcodeAttachments []*domain.TranscodeAttachment) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, transcodeAttachments)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockTranscodeAttachmentRepoMockRecorder) Delete(ctx, transcodeAttachments interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockTranscodeAttachmentRepo)(nil).Delete), ctx, transcodeAttachments)
}

// SelectByTaskType mocks base method.
func (m *MockTranscodeAttachmentRepo) SelectByTaskType(ctx context.Context, taskId, attachType string) ([]*domain.TranscodeAttachment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SelectByTaskType", ctx, taskId, attachType)
	ret0, _ := ret[0].([]*domain.TranscodeAttachment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SelectByTaskType indicates an expected call of SelectByTaskType.
func (mr *MockTranscodeAttachmentRepoMockRecorder) SelectByTaskType(ctx, taskId, attachType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SelectByTaskType", reflect.TypeOf((*MockTranscodeAttachmentRepo)(nil).SelectByTaskType), ctx, taskId, attachType)
}
