// Code generated by MockGen. DO NOT EDIT.
// Source: internal/taskmanager/repository/transcode_stat_info.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	domain "mpp/internal/taskmanager/models/domain"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
)

// MockTranscodeStatInfoRepo is a mock of TranscodeStatInfoRepo interface.
type MockTranscodeStatInfoRepo struct {
	ctrl     *gomock.Controller
	recorder *MockTranscodeStatInfoRepoMockRecorder
}

// MockTranscodeStatInfoRepoMockRecorder is the mock recorder for MockTranscodeStatInfoRepo.
type MockTranscodeStatInfoRepoMockRecorder struct {
	mock *MockTranscodeStatInfoRepo
}

// NewMockTranscodeStatInfoRepo creates a new mock instance.
func NewMockTranscodeStatInfoRepo(ctrl *gomock.Controller) *MockTranscodeStatInfoRepo {
	mock := &MockTranscodeStatInfoRepo{ctrl: ctrl}
	mock.recorder = &MockTranscodeStatInfoRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTranscodeStatInfoRepo) EXPECT() *MockTranscodeStatInfoRepoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockTranscodeStatInfoRepo) Create(ctx context.Context, transcodeStatInfo *domain.TranscodeStatInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, transcodeStatInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockTranscodeStatInfoRepoMockRecorder) Create(ctx, transcodeStatInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockTranscodeStatInfoRepo)(nil).Create), ctx, transcodeStatInfo)
}

// GetLatest mocks base method.
func (m *MockTranscodeStatInfoRepo) GetLatest(ctx context.Context, tag string) (*domain.TranscodeStatInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatest", ctx, tag)
	ret0, _ := ret[0].(*domain.TranscodeStatInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatest indicates an expected call of GetLatest.
func (mr *MockTranscodeStatInfoRepoMockRecorder) GetLatest(ctx, tag interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatest", reflect.TypeOf((*MockTranscodeStatInfoRepo)(nil).GetLatest), ctx, tag)
}

// Query mocks base method.
func (m *MockTranscodeStatInfoRepo) Query(ctx context.Context, tag, transcodeDomain string, statTime time.Time) ([]*domain.TranscodeStatInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Query", ctx, tag, transcodeDomain, statTime)
	ret0, _ := ret[0].([]*domain.TranscodeStatInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Query indicates an expected call of Query.
func (mr *MockTranscodeStatInfoRepoMockRecorder) Query(ctx, tag, transcodeDomain, statTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Query", reflect.TypeOf((*MockTranscodeStatInfoRepo)(nil).Query), ctx, tag, transcodeDomain, statTime)
}
