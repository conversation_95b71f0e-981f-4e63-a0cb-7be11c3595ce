// Code generated by MockGen. DO NOT EDIT.
// Source: internal/taskmanager/repository/task_repo.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	models "mpp/internal/taskmanager/models"
	repository "mpp/internal/taskmanager/repository"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockTaskRepo is a mock of TaskRepo interface.
type MockTaskRepo struct {
	ctrl     *gomock.Controller
	recorder *MockTaskRepoMockRecorder
}

// MockTaskRepoMockRecorder is the mock recorder for MockTaskRepo.
type MockTaskRepoMockRecorder struct {
	mock *MockTaskRepo
}

// NewMockTaskRepo creates a new mock instance.
func NewMockTaskRepo(ctrl *gomock.Controller) *MockTaskRepo {
	mock := &MockTaskRepo{ctrl: ctrl}
	mock.recorder = &MockTaskRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTaskRepo) EXPECT() *MockTaskRepoMockRecorder {
	return m.recorder
}

// BatchUpdateAbnormalStopped mocks base method.
func (m *MockTaskRepo) BatchUpdateAbnormalStopped(ctx context.Context, taskIds []string, taskResult *models.TaskResult) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateAbnormalStopped", ctx, taskIds, taskResult)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateAbnormalStopped indicates an expected call of BatchUpdateAbnormalStopped.
func (mr *MockTaskRepoMockRecorder) BatchUpdateAbnormalStopped(ctx, taskIds, taskResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateAbnormalStopped", reflect.TypeOf((*MockTaskRepo)(nil).BatchUpdateAbnormalStopped), ctx, taskIds, taskResult)
}

// ClearResultAndAddMigrateTimes mocks base method.
func (m *MockTaskRepo) ClearResultAndAddMigrateTimes(arg0 context.Context, arg1 *models.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearResultAndAddMigrateTimes", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearResultAndAddMigrateTimes indicates an expected call of ClearResultAndAddMigrateTimes.
func (mr *MockTaskRepoMockRecorder) ClearResultAndAddMigrateTimes(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearResultAndAddMigrateTimes", reflect.TypeOf((*MockTaskRepo)(nil).ClearResultAndAddMigrateTimes), arg0, arg1)
}

// CloneWithSpecifiedTable mocks base method.
func (m *MockTaskRepo) CloneWithSpecifiedTable(arg0 string) repository.TaskRepo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloneWithSpecifiedTable", arg0)
	ret0, _ := ret[0].(repository.TaskRepo)
	return ret0
}

// CloneWithSpecifiedTable indicates an expected call of CloneWithSpecifiedTable.
func (mr *MockTaskRepoMockRecorder) CloneWithSpecifiedTable(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloneWithSpecifiedTable", reflect.TypeOf((*MockTaskRepo)(nil).CloneWithSpecifiedTable), arg0)
}

// CountAggregatedTasks mocks base method.
func (m *MockTaskRepo) CountAggregatedTasks(ctx context.Context) ([]*models.TempQueryTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountAggregatedTasks", ctx)
	ret0, _ := ret[0].([]*models.TempQueryTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountAggregatedTasks indicates an expected call of CountAggregatedTasks.
func (mr *MockTaskRepoMockRecorder) CountAggregatedTasks(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountAggregatedTasks", reflect.TypeOf((*MockTaskRepo)(nil).CountAggregatedTasks), ctx)
}

// FindByID mocks base method.
func (m *MockTaskRepo) FindByID(arg0 context.Context, arg1 string) (*models.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByID", arg0, arg1)
	ret0, _ := ret[0].(*models.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByID indicates an expected call of FindByID.
func (mr *MockTaskRepoMockRecorder) FindByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByID", reflect.TypeOf((*MockTaskRepo)(nil).FindByID), arg0, arg1)
}

// FindByIDList mocks base method.
func (m *MockTaskRepo) FindByIDList(ctx context.Context, taskIds []string) ([]*models.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByIDList", ctx, taskIds)
	ret0, _ := ret[0].([]*models.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByIDList indicates an expected call of FindByIDList.
func (mr *MockTaskRepoMockRecorder) FindByIDList(ctx, taskIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByIDList", reflect.TypeOf((*MockTaskRepo)(nil).FindByIDList), ctx, taskIds)
}

// FindByJobId mocks base method.
func (m *MockTaskRepo) FindByJobId(arg0 context.Context, arg1 string) ([]*models.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByJobId", arg0, arg1)
	ret0, _ := ret[0].([]*models.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByJobId indicates an expected call of FindByJobId.
func (mr *MockTaskRepoMockRecorder) FindByJobId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByJobId", reflect.TypeOf((*MockTaskRepo)(nil).FindByJobId), arg0, arg1)
}

// FindFirstID mocks base method.
func (m *MockTaskRepo) FindFirstID(arg0 context.Context) (*models.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindFirstID", arg0)
	ret0, _ := ret[0].(*models.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindFirstID indicates an expected call of FindFirstID.
func (mr *MockTaskRepoMockRecorder) FindFirstID(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindFirstID", reflect.TypeOf((*MockTaskRepo)(nil).FindFirstID), arg0)
}

// FindRunning mocks base method.
func (m *MockTaskRepo) FindRunning(arg0 context.Context, arg1 []string) ([]*models.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindRunning", arg0, arg1)
	ret0, _ := ret[0].([]*models.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindRunning indicates an expected call of FindRunning.
func (mr *MockTaskRepoMockRecorder) FindRunning(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindRunning", reflect.TypeOf((*MockTaskRepo)(nil).FindRunning), arg0, arg1)
}

// IncreaseMigrateTimesAndClearResult mocks base method.
func (m *MockTaskRepo) IncreaseMigrateTimesAndClearResult(ctx context.Context, taskId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncreaseMigrateTimesAndClearResult", ctx, taskId)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncreaseMigrateTimesAndClearResult indicates an expected call of IncreaseMigrateTimesAndClearResult.
func (mr *MockTaskRepoMockRecorder) IncreaseMigrateTimesAndClearResult(ctx, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncreaseMigrateTimesAndClearResult", reflect.TypeOf((*MockTaskRepo)(nil).IncreaseMigrateTimesAndClearResult), ctx, taskId)
}

// InsertIgnoreDuplicate mocks base method.
func (m *MockTaskRepo) InsertIgnoreDuplicate(ctx context.Context, task *models.Task) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertIgnoreDuplicate", ctx, task)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertIgnoreDuplicate indicates an expected call of InsertIgnoreDuplicate.
func (mr *MockTaskRepoMockRecorder) InsertIgnoreDuplicate(ctx, task interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertIgnoreDuplicate", reflect.TypeOf((*MockTaskRepo)(nil).InsertIgnoreDuplicate), ctx, task)
}

// InsertUpdateOnDuplicate mocks base method.
func (m *MockTaskRepo) InsertUpdateOnDuplicate(arg0 context.Context, arg1 *models.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertUpdateOnDuplicate", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertUpdateOnDuplicate indicates an expected call of InsertUpdateOnDuplicate.
func (mr *MockTaskRepoMockRecorder) InsertUpdateOnDuplicate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertUpdateOnDuplicate", reflect.TypeOf((*MockTaskRepo)(nil).InsertUpdateOnDuplicate), arg0, arg1)
}

// ListAllBizRunningTasks mocks base method.
func (m *MockTaskRepo) ListAllBizRunningTasks(ctx context.Context) ([]*models.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAllBizRunningTasks", ctx)
	ret0, _ := ret[0].([]*models.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAllBizRunningTasks indicates an expected call of ListAllBizRunningTasks.
func (mr *MockTaskRepoMockRecorder) ListAllBizRunningTasks(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAllBizRunningTasks", reflect.TypeOf((*MockTaskRepo)(nil).ListAllBizRunningTasks), ctx)
}

// ListBizRunningTasks mocks base method.
func (m *MockTaskRepo) ListBizRunningTasks(ctx context.Context, taskInfo *models.Task) ([]*models.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBizRunningTasks", ctx, taskInfo)
	ret0, _ := ret[0].([]*models.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBizRunningTasks indicates an expected call of ListBizRunningTasks.
func (mr *MockTaskRepoMockRecorder) ListBizRunningTasks(ctx, taskInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBizRunningTasks", reflect.TypeOf((*MockTaskRepo)(nil).ListBizRunningTasks), ctx, taskInfo)
}

// ListBizRunningTasksExceptDagJob mocks base method.
func (m *MockTaskRepo) ListBizRunningTasksExceptDagJob(ctx context.Context) ([]*models.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBizRunningTasksExceptDagJob", ctx)
	ret0, _ := ret[0].([]*models.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBizRunningTasksExceptDagJob indicates an expected call of ListBizRunningTasksExceptDagJob.
func (mr *MockTaskRepoMockRecorder) ListBizRunningTasksExceptDagJob(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBizRunningTasksExceptDagJob", reflect.TypeOf((*MockTaskRepo)(nil).ListBizRunningTasksExceptDagJob), ctx)
}

// ListBizTasksWithOptionalFilters mocks base method.
func (m *MockTaskRepo) ListBizTasksWithOptionalFilters(ctx context.Context, taskInfo *models.Task) ([]*models.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBizTasksWithOptionalFilters", ctx, taskInfo)
	ret0, _ := ret[0].([]*models.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBizTasksWithOptionalFilters indicates an expected call of ListBizTasksWithOptionalFilters.
func (mr *MockTaskRepoMockRecorder) ListBizTasksWithOptionalFilters(ctx, taskInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBizTasksWithOptionalFilters", reflect.TypeOf((*MockTaskRepo)(nil).ListBizTasksWithOptionalFilters), ctx, taskInfo)
}

// ListMissingRunningTasks mocks base method.
func (m *MockTaskRepo) ListMissingRunningTasks(ctx context.Context) ([]*models.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMissingRunningTasks", ctx)
	ret0, _ := ret[0].([]*models.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMissingRunningTasks indicates an expected call of ListMissingRunningTasks.
func (mr *MockTaskRepoMockRecorder) ListMissingRunningTasks(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMissingRunningTasks", reflect.TypeOf((*MockTaskRepo)(nil).ListMissingRunningTasks), ctx)
}

// ListRunningTaskByWorkerId mocks base method.
func (m *MockTaskRepo) ListRunningTaskByWorkerId(ctx context.Context, workerId string) ([]*models.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRunningTaskByWorkerId", ctx, workerId)
	ret0, _ := ret[0].([]*models.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRunningTaskByWorkerId indicates an expected call of ListRunningTaskByWorkerId.
func (mr *MockTaskRepoMockRecorder) ListRunningTaskByWorkerId(ctx, workerId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRunningTaskByWorkerId", reflect.TypeOf((*MockTaskRepo)(nil).ListRunningTaskByWorkerId), ctx, workerId)
}

// ListRunningTaskByWorkerIp mocks base method.
func (m *MockTaskRepo) ListRunningTaskByWorkerIp(ctx context.Context, workerIp string) ([]*models.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRunningTaskByWorkerIp", ctx, workerIp)
	ret0, _ := ret[0].([]*models.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRunningTaskByWorkerIp indicates an expected call of ListRunningTaskByWorkerIp.
func (mr *MockTaskRepoMockRecorder) ListRunningTaskByWorkerIp(ctx, workerIp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRunningTaskByWorkerIp", reflect.TypeOf((*MockTaskRepo)(nil).ListRunningTaskByWorkerIp), ctx, workerIp)
}

// ListTasks mocks base method.
func (m *MockTaskRepo) ListTasks(ctx context.Context, taskInfo *models.Task) ([]*models.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTasks", ctx, taskInfo)
	ret0, _ := ret[0].([]*models.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTasks indicates an expected call of ListTasks.
func (mr *MockTaskRepoMockRecorder) ListTasks(ctx, taskInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTasks", reflect.TypeOf((*MockTaskRepo)(nil).ListTasks), ctx, taskInfo)
}

// ListTasksOptimized mocks base method.
func (m *MockTaskRepo) ListTasksOptimized(ctx context.Context, taskInfo *models.Task) ([]*models.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTasksOptimized", ctx, taskInfo)
	ret0, _ := ret[0].([]*models.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTasksOptimized indicates an expected call of ListTasksOptimized.
func (mr *MockTaskRepoMockRecorder) ListTasksOptimized(ctx, taskInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTasksOptimized", reflect.TypeOf((*MockTaskRepo)(nil).ListTasksOptimized), ctx, taskInfo)
}

// ListUnexpectedRunningTasks mocks base method.
func (m *MockTaskRepo) ListUnexpectedRunningTasks(ctx context.Context) ([]*models.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListUnexpectedRunningTasks", ctx)
	ret0, _ := ret[0].([]*models.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListUnexpectedRunningTasks indicates an expected call of ListUnexpectedRunningTasks.
func (mr *MockTaskRepoMockRecorder) ListUnexpectedRunningTasks(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListUnexpectedRunningTasks", reflect.TypeOf((*MockTaskRepo)(nil).ListUnexpectedRunningTasks), ctx)
}

// UpdateAbnormalStopped mocks base method.
func (m *MockTaskRepo) UpdateAbnormalStopped(arg0 context.Context, arg1 *models.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAbnormalStopped", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAbnormalStopped indicates an expected call of UpdateAbnormalStopped.
func (mr *MockTaskRepoMockRecorder) UpdateAbnormalStopped(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAbnormalStopped", reflect.TypeOf((*MockTaskRepo)(nil).UpdateAbnormalStopped), arg0, arg1)
}

// UpdateBizStatus mocks base method.
func (m *MockTaskRepo) UpdateBizStatus(arg0 context.Context, arg1 *models.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBizStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateBizStatus indicates an expected call of UpdateBizStatus.
func (mr *MockTaskRepoMockRecorder) UpdateBizStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBizStatus", reflect.TypeOf((*MockTaskRepo)(nil).UpdateBizStatus), arg0, arg1)
}

// UpdateDagGroup mocks base method.
func (m *MockTaskRepo) UpdateDagGroup(ctx context.Context, task *models.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDagGroup", ctx, task)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDagGroup indicates an expected call of UpdateDagGroup.
func (mr *MockTaskRepoMockRecorder) UpdateDagGroup(ctx, task interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDagGroup", reflect.TypeOf((*MockTaskRepo)(nil).UpdateDagGroup), ctx, task)
}

// UpdateEngineParam mocks base method.
func (m *MockTaskRepo) UpdateEngineParam(arg0 context.Context, arg1 *models.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEngineParam", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateEngineParam indicates an expected call of UpdateEngineParam.
func (mr *MockTaskRepoMockRecorder) UpdateEngineParam(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEngineParam", reflect.TypeOf((*MockTaskRepo)(nil).UpdateEngineParam), arg0, arg1)
}

// UpdateEngineParamAndBizStatus mocks base method.
func (m *MockTaskRepo) UpdateEngineParamAndBizStatus(arg0 context.Context, arg1 *models.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEngineParamAndBizStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateEngineParamAndBizStatus indicates an expected call of UpdateEngineParamAndBizStatus.
func (mr *MockTaskRepoMockRecorder) UpdateEngineParamAndBizStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEngineParamAndBizStatus", reflect.TypeOf((*MockTaskRepo)(nil).UpdateEngineParamAndBizStatus), arg0, arg1)
}

// UpdateInternalAndBizStatus mocks base method.
func (m *MockTaskRepo) UpdateInternalAndBizStatus(arg0 context.Context, arg1 *models.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInternalAndBizStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInternalAndBizStatus indicates an expected call of UpdateInternalAndBizStatus.
func (mr *MockTaskRepoMockRecorder) UpdateInternalAndBizStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInternalAndBizStatus", reflect.TypeOf((*MockTaskRepo)(nil).UpdateInternalAndBizStatus), arg0, arg1)
}

// UpdateInternalStatus mocks base method.
func (m *MockTaskRepo) UpdateInternalStatus(arg0 context.Context, arg1 *models.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInternalStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInternalStatus indicates an expected call of UpdateInternalStatus.
func (mr *MockTaskRepoMockRecorder) UpdateInternalStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInternalStatus", reflect.TypeOf((*MockTaskRepo)(nil).UpdateInternalStatus), arg0, arg1)
}

// UpdateMetadata mocks base method.
func (m *MockTaskRepo) UpdateMetadata(arg0 context.Context, arg1 *models.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMetadata", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateMetadata indicates an expected call of UpdateMetadata.
func (mr *MockTaskRepoMockRecorder) UpdateMetadata(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMetadata", reflect.TypeOf((*MockTaskRepo)(nil).UpdateMetadata), arg0, arg1)
}

// UpdateResult mocks base method.
func (m *MockTaskRepo) UpdateResult(arg0 context.Context, arg1 *models.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateResult", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateResult indicates an expected call of UpdateResult.
func (mr *MockTaskRepoMockRecorder) UpdateResult(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateResult", reflect.TypeOf((*MockTaskRepo)(nil).UpdateResult), arg0, arg1)
}

// UpdateResultAndAllStatus mocks base method.
func (m *MockTaskRepo) UpdateResultAndAllStatus(arg0 context.Context, arg1 *models.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateResultAndAllStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateResultAndAllStatus indicates an expected call of UpdateResultAndAllStatus.
func (mr *MockTaskRepoMockRecorder) UpdateResultAndAllStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateResultAndAllStatus", reflect.TypeOf((*MockTaskRepo)(nil).UpdateResultAndAllStatus), arg0, arg1)
}

// UpdateScheduleWorker mocks base method.
func (m *MockTaskRepo) UpdateScheduleWorker(arg0 context.Context, arg1 *models.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateScheduleWorker", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateScheduleWorker indicates an expected call of UpdateScheduleWorker.
func (mr *MockTaskRepoMockRecorder) UpdateScheduleWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateScheduleWorker", reflect.TypeOf((*MockTaskRepo)(nil).UpdateScheduleWorker), arg0, arg1)
}

// UpdateScheduleWorkerAndInternalStatus mocks base method.
func (m *MockTaskRepo) UpdateScheduleWorkerAndInternalStatus(arg0 context.Context, arg1 *models.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateScheduleWorkerAndInternalStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateScheduleWorkerAndInternalStatus indicates an expected call of UpdateScheduleWorkerAndInternalStatus.
func (mr *MockTaskRepoMockRecorder) UpdateScheduleWorkerAndInternalStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateScheduleWorkerAndInternalStatus", reflect.TypeOf((*MockTaskRepo)(nil).UpdateScheduleWorkerAndInternalStatus), arg0, arg1)
}
