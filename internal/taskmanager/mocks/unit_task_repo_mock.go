// Code generated by MockGen. DO NOT EDIT.
// Source: internal/taskmanager/repository/unit_task_repo.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	domain "mpp/internal/taskmanager/models/domain"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockUnitTaskRepo is a mock of UnitTaskRepo interface.
type MockUnitTaskRepo struct {
	ctrl     *gomock.Controller
	recorder *MockUnitTaskRepoMockRecorder
}

// MockUnitTaskRepoMockRecorder is the mock recorder for MockUnitTaskRepo.
type MockUnitTaskRepoMockRecorder struct {
	mock *MockUnitTaskRepo
}

// NewMockUnitTaskRepo creates a new mock instance.
func NewMockUnitTaskRepo(ctrl *gomock.Controller) *MockUnitTaskRepo {
	mock := &MockUnitTaskRepo{ctrl: ctrl}
	mock.recorder = &MockUnitTaskRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnitTaskRepo) EXPECT() *MockUnitTaskRepoMockRecorder {
	return m.recorder
}

// DeleteTask mocks base method.
func (m *MockUnitTaskRepo) DeleteTask(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTask", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTask indicates an expected call of DeleteTask.
func (mr *MockUnitTaskRepoMockRecorder) DeleteTask(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTask", reflect.TypeOf((*MockUnitTaskRepo)(nil).DeleteTask), arg0, arg1)
}

// GetUnitTask mocks base method.
func (m *MockUnitTaskRepo) GetUnitTask(arg0 context.Context, arg1 string) (*domain.UnitTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUnitTask", arg0, arg1)
	ret0, _ := ret[0].(*domain.UnitTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUnitTask indicates an expected call of GetUnitTask.
func (mr *MockUnitTaskRepoMockRecorder) GetUnitTask(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUnitTask", reflect.TypeOf((*MockUnitTaskRepo)(nil).GetUnitTask), arg0, arg1)
}

// GetUnitTaskAll mocks base method.
func (m *MockUnitTaskRepo) GetUnitTaskAll(arg0 context.Context, arg1 string) ([]*domain.UnitTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUnitTaskAll", arg0, arg1)
	ret0, _ := ret[0].([]*domain.UnitTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUnitTaskAll indicates an expected call of GetUnitTaskAll.
func (mr *MockUnitTaskRepoMockRecorder) GetUnitTaskAll(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUnitTaskAll", reflect.TypeOf((*MockUnitTaskRepo)(nil).GetUnitTaskAll), arg0, arg1)
}

// InsertUnitTask mocks base method.
func (m *MockUnitTaskRepo) InsertUnitTask(arg0 context.Context, arg1 *domain.UnitTask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertUnitTask", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertUnitTask indicates an expected call of InsertUnitTask.
func (mr *MockUnitTaskRepoMockRecorder) InsertUnitTask(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertUnitTask", reflect.TypeOf((*MockUnitTaskRepo)(nil).InsertUnitTask), arg0, arg1)
}

// SyncTask mocks base method.
func (m *MockUnitTaskRepo) SyncTask(arg0 context.Context, arg1 *domain.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncTask", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SyncTask indicates an expected call of SyncTask.
func (mr *MockUnitTaskRepoMockRecorder) SyncTask(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncTask", reflect.TypeOf((*MockUnitTaskRepo)(nil).SyncTask), arg0, arg1)
}

// UpdateTaskStatus mocks base method.
func (m *MockUnitTaskRepo) UpdateTaskStatus(arg0 context.Context, arg1 string, arg2, arg3 int8) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTaskStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTaskStatus indicates an expected call of UpdateTaskStatus.
func (mr *MockUnitTaskRepoMockRecorder) UpdateTaskStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaskStatus", reflect.TypeOf((*MockUnitTaskRepo)(nil).UpdateTaskStatus), arg0, arg1, arg2, arg3)
}

// UpdateUnitTaskStatus mocks base method.
func (m *MockUnitTaskRepo) UpdateUnitTaskStatus(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUnitTaskStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUnitTaskStatus indicates an expected call of UpdateUnitTaskStatus.
func (mr *MockUnitTaskRepoMockRecorder) UpdateUnitTaskStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUnitTaskStatus", reflect.TypeOf((*MockUnitTaskRepo)(nil).UpdateUnitTaskStatus), arg0, arg1)
}
