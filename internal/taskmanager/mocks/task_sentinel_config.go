// Code generated by MockGen. DO NOT EDIT.
// Source: internal/taskmanager/repository/task_sentinel_config.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	models "mpp/internal/taskmanager/models"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockTaskSentinelConfigRepo is a mock of TaskSentinelConfigRepo interface.
type MockTaskSentinelConfigRepo struct {
	ctrl     *gomock.Controller
	recorder *MockTaskSentinelConfigRepoMockRecorder
}

// MockTaskSentinelConfigRepoMockRecorder is the mock recorder for MockTaskSentinelConfigRepo.
type MockTaskSentinelConfigRepoMockRecorder struct {
	mock *MockTaskSentinelConfigRepo
}

// NewMockTaskSentinelConfigRepo creates a new mock instance.
func NewMockTaskSentinelConfigRepo(ctrl *gomock.Controller) *MockTaskSentinelConfigRepo {
	mock := &MockTaskSentinelConfigRepo{ctrl: ctrl}
	mock.recorder = &MockTaskSentinelConfigRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTaskSentinelConfigRepo) EXPECT() *MockTaskSentinelConfigRepoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockTaskSentinelConfigRepo) Create(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, taskSentinelConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockTaskSentinelConfigRepoMockRecorder) Create(ctx, taskSentinelConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockTaskSentinelConfigRepo)(nil).Create), ctx, taskSentinelConfig)
}

// Delete mocks base method.
func (m *MockTaskSentinelConfigRepo) Delete(ctx context.Context, sentinelConfigId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, sentinelConfigId)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockTaskSentinelConfigRepoMockRecorder) Delete(ctx, sentinelConfigId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockTaskSentinelConfigRepo)(nil).Delete), ctx, sentinelConfigId)
}

// GetTaskSentinelConfig mocks base method.
func (m *MockTaskSentinelConfigRepo) GetTaskSentinelConfig(ctx context.Context, sentinelConfigId string) (*models.TaskSentinelConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskSentinelConfig", ctx, sentinelConfigId)
	ret0, _ := ret[0].(*models.TaskSentinelConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskSentinelConfig indicates an expected call of GetTaskSentinelConfig.
func (mr *MockTaskSentinelConfigRepoMockRecorder) GetTaskSentinelConfig(ctx, sentinelConfigId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskSentinelConfig", reflect.TypeOf((*MockTaskSentinelConfigRepo)(nil).GetTaskSentinelConfig), ctx, sentinelConfigId)
}

// InsertUpdateOnDuplicate mocks base method.
func (m *MockTaskSentinelConfigRepo) InsertUpdateOnDuplicate(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertUpdateOnDuplicate", ctx, taskSentinelConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertUpdateOnDuplicate indicates an expected call of InsertUpdateOnDuplicate.
func (mr *MockTaskSentinelConfigRepoMockRecorder) InsertUpdateOnDuplicate(ctx, taskSentinelConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertUpdateOnDuplicate", reflect.TypeOf((*MockTaskSentinelConfigRepo)(nil).InsertUpdateOnDuplicate), ctx, taskSentinelConfig)
}

// ListActiveTaskSentinelConfig mocks base method.
func (m *MockTaskSentinelConfigRepo) ListActiveTaskSentinelConfig(ctx context.Context) ([]*models.TaskSentinelConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListActiveTaskSentinelConfig", ctx)
	ret0, _ := ret[0].([]*models.TaskSentinelConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListActiveTaskSentinelConfig indicates an expected call of ListActiveTaskSentinelConfig.
func (mr *MockTaskSentinelConfigRepoMockRecorder) ListActiveTaskSentinelConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListActiveTaskSentinelConfig", reflect.TypeOf((*MockTaskSentinelConfigRepo)(nil).ListActiveTaskSentinelConfig), ctx)
}

// UpdateSentinelStatus mocks base method.
func (m *MockTaskSentinelConfigRepo) UpdateSentinelStatus(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSentinelStatus", ctx, taskSentinelConfig)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSentinelStatus indicates an expected call of UpdateSentinelStatus.
func (mr *MockTaskSentinelConfigRepoMockRecorder) UpdateSentinelStatus(ctx, taskSentinelConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSentinelStatus", reflect.TypeOf((*MockTaskSentinelConfigRepo)(nil).UpdateSentinelStatus), ctx, taskSentinelConfig)
}

// UpdateTaskSentinelConfig mocks base method.
func (m *MockTaskSentinelConfigRepo) UpdateTaskSentinelConfig(ctx context.Context, taskSentinelConfig *models.TaskSentinelConfig) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTaskSentinelConfig", ctx, taskSentinelConfig)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTaskSentinelConfig indicates an expected call of UpdateTaskSentinelConfig.
func (mr *MockTaskSentinelConfigRepoMockRecorder) UpdateTaskSentinelConfig(ctx, taskSentinelConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaskSentinelConfig", reflect.TypeOf((*MockTaskSentinelConfigRepo)(nil).UpdateTaskSentinelConfig), ctx, taskSentinelConfig)
}
