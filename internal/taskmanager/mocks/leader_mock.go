// Code generated by MockGen. DO NOT EDIT.
// Source: internal/taskmanager/biz/leader/leader.go

// Package mocks is a generated GoMock package.
package mocks

import (
	leader "mpp/internal/taskmanager/biz/leader"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockLeaderRepo is a mock of LeaderRepo interface.
type MockLeaderRepo struct {
	ctrl     *gomock.Controller
	recorder *MockLeaderRepoMockRecorder
}

// MockLeaderRepoMockRecorder is the mock recorder for MockLeaderRepo.
type MockLeaderRepoMockRecorder struct {
	mock *MockLeaderRepo
}

// NewMockLeaderRepo creates a new mock instance.
func NewMockLeaderRepo(ctrl *gomock.Controller) *MockLeaderRepo {
	mock := &MockLeaderRepo{ctrl: ctrl}
	mock.recorder = &MockLeaderRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLeaderRepo) EXPECT() *MockLeaderRepoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockLeaderRepo) Create(arg0 leader.Leader) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockLeaderRepoMockRecorder) Create(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockLeaderRepo)(nil).Create), arg0)
}

// KeepAlive mocks base method.
func (m *MockLeaderRepo) KeepAlive(arg0 leader.Leader) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KeepAlive", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// KeepAlive indicates an expected call of KeepAlive.
func (mr *MockLeaderRepoMockRecorder) KeepAlive(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KeepAlive", reflect.TypeOf((*MockLeaderRepo)(nil).KeepAlive), arg0)
}

// QueryLeader mocks base method.
func (m *MockLeaderRepo) QueryLeader(key string) (*leader.Leader, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryLeader", key)
	ret0, _ := ret[0].(*leader.Leader)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryLeader indicates an expected call of QueryLeader.
func (mr *MockLeaderRepoMockRecorder) QueryLeader(key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryLeader", reflect.TypeOf((*MockLeaderRepo)(nil).QueryLeader), key)
}

// UpdateLeader mocks base method.
func (m *MockLeaderRepo) UpdateLeader(arg0 leader.Leader, arg1 int) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLeader", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateLeader indicates an expected call of UpdateLeader.
func (mr *MockLeaderRepoMockRecorder) UpdateLeader(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLeader", reflect.TypeOf((*MockLeaderRepo)(nil).UpdateLeader), arg0, arg1)
}
