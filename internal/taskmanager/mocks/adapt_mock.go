// Code generated by MockGen. DO NOT EDIT.
// Source: internal/taskmanager/biz/common/adapter.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	models "mpp/internal/taskmanager/models"
	reflect "reflect"

	registry "github.com/cinience/animus/registry"
	gomock "github.com/golang/mock/gomock"
)

// MockEngineAdapter is a mock of EngineAdapter interface.
type MockEngineAdapter struct {
	ctrl     *gomock.Controller
	recorder *MockEngineAdapterMockRecorder
}

// MockEngineAdapterMockRecorder is the mock recorder for MockEngineAdapter.
type MockEngineAdapterMockRecorder struct {
	mock *MockEngineAdapter
}

// NewMockEngineAdapter creates a new mock instance.
func NewMockEngineAdapter(ctrl *gomock.Controller) *MockEngineAdapter {
	mock := &MockEngineAdapter{ctrl: ctrl}
	mock.recorder = &MockEngineAdapterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEngineAdapter) EXPECT() *MockEngineAdapterMockRecorder {
	return m.recorder
}

// Cancel mocks base method.
func (m *MockEngineAdapter) Cancel(ctx context.Context, task *models.Task, worker *models.Worker) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cancel", ctx, task, worker)
	ret0, _ := ret[0].(error)
	return ret0
}

// Cancel indicates an expected call of Cancel.
func (mr *MockEngineAdapterMockRecorder) Cancel(ctx, task, worker interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockEngineAdapter)(nil).Cancel), ctx, task, worker)
}

// Check mocks base method.
func (m *MockEngineAdapter) Check(ctx context.Context, task *models.Task, worker *models.Worker) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Check", ctx, task, worker)
	ret0, _ := ret[0].(error)
	return ret0
}

// Check indicates an expected call of Check.
func (mr *MockEngineAdapterMockRecorder) Check(ctx, task, worker interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Check", reflect.TypeOf((*MockEngineAdapter)(nil).Check), ctx, task, worker)
}

// Command mocks base method.
func (m *MockEngineAdapter) Command(ctx context.Context, task *models.Task, worker *models.Worker) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Command", ctx, task, worker)
	ret0, _ := ret[0].(error)
	return ret0
}

// Command indicates an expected call of Command.
func (mr *MockEngineAdapterMockRecorder) Command(ctx, task, worker interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Command", reflect.TypeOf((*MockEngineAdapter)(nil).Command), ctx, task, worker)
}

// Invoke mocks base method.
func (m *MockEngineAdapter) Invoke(ctx context.Context, task *models.Task, worker *models.Worker) (*models.SimpleTaskResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Invoke", ctx, task, worker)
	ret0, _ := ret[0].(*models.SimpleTaskResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Invoke indicates an expected call of Invoke.
func (mr *MockEngineAdapterMockRecorder) Invoke(ctx, task, worker interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Invoke", reflect.TypeOf((*MockEngineAdapter)(nil).Invoke), ctx, task, worker)
}

// List mocks base method.
func (m *MockEngineAdapter) List(ctx context.Context, worker *models.Worker) ([]models.SimpleTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, worker)
	ret0, _ := ret[0].([]models.SimpleTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockEngineAdapterMockRecorder) List(ctx, worker interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockEngineAdapter)(nil).List), ctx, worker)
}

// ListByTask mocks base method.
func (m *MockEngineAdapter) ListByTask(ctx context.Context, worker *models.Worker, task *models.Task) ([]models.SimpleTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByTask", ctx, worker, task)
	ret0, _ := ret[0].([]models.SimpleTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByTask indicates an expected call of ListByTask.
func (mr *MockEngineAdapterMockRecorder) ListByTask(ctx, worker, task interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByTask", reflect.TypeOf((*MockEngineAdapter)(nil).ListByTask), ctx, worker, task)
}

// Start mocks base method.
func (m *MockEngineAdapter) Start(ctx context.Context, task *models.Task, worker *models.Worker) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Start", ctx, task, worker)
	ret0, _ := ret[0].(error)
	return ret0
}

// Start indicates an expected call of Start.
func (mr *MockEngineAdapterMockRecorder) Start(ctx, task, worker interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockEngineAdapter)(nil).Start), ctx, task, worker)
}

// Stop mocks base method.
func (m *MockEngineAdapter) Stop(ctx context.Context, task *models.Task, worker *models.Worker) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stop", ctx, task, worker)
	ret0, _ := ret[0].(error)
	return ret0
}

// Stop indicates an expected call of Stop.
func (mr *MockEngineAdapterMockRecorder) Stop(ctx, task, worker interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockEngineAdapter)(nil).Stop), ctx, task, worker)
}

// Submit mocks base method.
func (m *MockEngineAdapter) Submit(ctx context.Context, task *models.Task, worker *models.Worker) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Submit", ctx, task, worker)
	ret0, _ := ret[0].(error)
	return ret0
}

// Submit indicates an expected call of Submit.
func (mr *MockEngineAdapterMockRecorder) Submit(ctx, task, worker interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Submit", reflect.TypeOf((*MockEngineAdapter)(nil).Submit), ctx, task, worker)
}

// Update mocks base method.
func (m *MockEngineAdapter) Update(ctx context.Context, task *models.Task, worker *models.Worker) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, task, worker)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockEngineAdapterMockRecorder) Update(ctx, task, worker interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockEngineAdapter)(nil).Update), ctx, task, worker)
}

// MockScheduleAdapter is a mock of ScheduleAdapter interface.
type MockScheduleAdapter struct {
	ctrl     *gomock.Controller
	recorder *MockScheduleAdapterMockRecorder
}

// MockScheduleAdapterMockRecorder is the mock recorder for MockScheduleAdapter.
type MockScheduleAdapterMockRecorder struct {
	mock *MockScheduleAdapter
}

// NewMockScheduleAdapter creates a new mock instance.
func NewMockScheduleAdapter(ctrl *gomock.Controller) *MockScheduleAdapter {
	mock := &MockScheduleAdapter{ctrl: ctrl}
	mock.recorder = &MockScheduleAdapterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockScheduleAdapter) EXPECT() *MockScheduleAdapterMockRecorder {
	return m.recorder
}

// AllocResource mocks base method.
func (m *MockScheduleAdapter) AllocResource(ctx context.Context, task *models.Task) (*models.Worker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AllocResource", ctx, task)
	ret0, _ := ret[0].(*models.Worker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AllocResource indicates an expected call of AllocResource.
func (mr *MockScheduleAdapterMockRecorder) AllocResource(ctx, task interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllocResource", reflect.TypeOf((*MockScheduleAdapter)(nil).AllocResource), ctx, task)
}

// FreeResource mocks base method.
func (m *MockScheduleAdapter) FreeResource(ctx context.Context, taskId string, metaData map[string]string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FreeResource", ctx, taskId, metaData)
	ret0, _ := ret[0].(error)
	return ret0
}

// FreeResource indicates an expected call of FreeResource.
func (mr *MockScheduleAdapterMockRecorder) FreeResource(ctx, taskId, metaData interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreeResource", reflect.TypeOf((*MockScheduleAdapter)(nil).FreeResource), ctx, taskId, metaData)
}

// GetAllSchedulerTags mocks base method.
func (m *MockScheduleAdapter) GetAllSchedulerTags() []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllSchedulerTags")
	ret0, _ := ret[0].([]string)
	return ret0
}

// GetAllSchedulerTags indicates an expected call of GetAllSchedulerTags.
func (mr *MockScheduleAdapterMockRecorder) GetAllSchedulerTags() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllSchedulerTags", reflect.TypeOf((*MockScheduleAdapter)(nil).GetAllSchedulerTags))
}

// GetResource mocks base method.
func (m *MockScheduleAdapter) GetResource(ctx context.Context, taskId string, metaData map[string]string) (*models.Resource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResource", ctx, taskId, metaData)
	ret0, _ := ret[0].(*models.Resource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResource indicates an expected call of GetResource.
func (mr *MockScheduleAdapterMockRecorder) GetResource(ctx, taskId, metaData interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResource", reflect.TypeOf((*MockScheduleAdapter)(nil).GetResource), ctx, taskId, metaData)
}

// GetWorker mocks base method.
func (m *MockScheduleAdapter) GetWorker(ctx context.Context, workerId string, metaData map[string]string) (*models.Worker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorker", ctx, workerId, metaData)
	ret0, _ := ret[0].(*models.Worker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorker indicates an expected call of GetWorker.
func (mr *MockScheduleAdapterMockRecorder) GetWorker(ctx, workerId, metaData interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorker", reflect.TypeOf((*MockScheduleAdapter)(nil).GetWorker), ctx, workerId, metaData)
}

// ListAllWorker mocks base method.
func (m *MockScheduleAdapter) ListAllWorker(ctx context.Context, schedulerTags []string) ([]*models.Worker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAllWorker", ctx, schedulerTags)
	ret0, _ := ret[0].([]*models.Worker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAllWorker indicates an expected call of ListAllWorker.
func (mr *MockScheduleAdapterMockRecorder) ListAllWorker(ctx, schedulerTags interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAllWorker", reflect.TypeOf((*MockScheduleAdapter)(nil).ListAllWorker), ctx, schedulerTags)
}

// ListResource mocks base method.
func (m *MockScheduleAdapter) ListResource(ctx context.Context, schedulerTags []string, isLost bool) ([]*models.Resource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListResource", ctx, schedulerTags, isLost)
	ret0, _ := ret[0].([]*models.Resource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListResource indicates an expected call of ListResource.
func (mr *MockScheduleAdapterMockRecorder) ListResource(ctx, schedulerTags, isLost interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListResource", reflect.TypeOf((*MockScheduleAdapter)(nil).ListResource), ctx, schedulerTags, isLost)
}

// ListWorker mocks base method.
func (m *MockScheduleAdapter) ListWorker(ctx context.Context, product, modelId, tag string, schedulerTags []string) ([]*models.Worker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListWorker", ctx, product, modelId, tag, schedulerTags)
	ret0, _ := ret[0].([]*models.Worker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListWorker indicates an expected call of ListWorker.
func (mr *MockScheduleAdapterMockRecorder) ListWorker(ctx, product, modelId, tag, schedulerTags interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListWorker", reflect.TypeOf((*MockScheduleAdapter)(nil).ListWorker), ctx, product, modelId, tag, schedulerTags)
}

// RecycleGetSchedulerServices mocks base method.
func (m *MockScheduleAdapter) RecycleGetSchedulerServices(ctx context.Context, dis registry.Discovery) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RecycleGetSchedulerServices", ctx, dis)
}

// RecycleGetSchedulerServices indicates an expected call of RecycleGetSchedulerServices.
func (mr *MockScheduleAdapterMockRecorder) RecycleGetSchedulerServices(ctx, dis interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecycleGetSchedulerServices", reflect.TypeOf((*MockScheduleAdapter)(nil).RecycleGetSchedulerServices), ctx, dis)
}

// MockWorkflowAdapter is a mock of WorkflowAdapter interface.
type MockWorkflowAdapter struct {
	ctrl     *gomock.Controller
	recorder *MockWorkflowAdapterMockRecorder
}

// MockWorkflowAdapterMockRecorder is the mock recorder for MockWorkflowAdapter.
type MockWorkflowAdapterMockRecorder struct {
	mock *MockWorkflowAdapter
}

// NewMockWorkflowAdapter creates a new mock instance.
func NewMockWorkflowAdapter(ctrl *gomock.Controller) *MockWorkflowAdapter {
	mock := &MockWorkflowAdapter{ctrl: ctrl}
	mock.recorder = &MockWorkflowAdapterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWorkflowAdapter) EXPECT() *MockWorkflowAdapterMockRecorder {
	return m.recorder
}

// CommandDagJob mocks base method.
func (m *MockWorkflowAdapter) CommandDagJob(ctx context.Context, task *models.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CommandDagJob", ctx, task)
	ret0, _ := ret[0].(error)
	return ret0
}

// CommandDagJob indicates an expected call of CommandDagJob.
func (mr *MockWorkflowAdapterMockRecorder) CommandDagJob(ctx, task interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommandDagJob", reflect.TypeOf((*MockWorkflowAdapter)(nil).CommandDagJob), ctx, task)
}

// StartDagJob mocks base method.
func (m *MockWorkflowAdapter) StartDagJob(ctx context.Context, task *models.Task, dagGraph map[string]interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartDagJob", ctx, task, dagGraph)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartDagJob indicates an expected call of StartDagJob.
func (mr *MockWorkflowAdapterMockRecorder) StartDagJob(ctx, task, dagGraph interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartDagJob", reflect.TypeOf((*MockWorkflowAdapter)(nil).StartDagJob), ctx, task, dagGraph)
}

// StopDagJob mocks base method.
func (m *MockWorkflowAdapter) StopDagJob(ctx context.Context, task *models.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StopDagJob", ctx, task)
	ret0, _ := ret[0].(error)
	return ret0
}

// StopDagJob indicates an expected call of StopDagJob.
func (mr *MockWorkflowAdapterMockRecorder) StopDagJob(ctx, task interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopDagJob", reflect.TypeOf((*MockWorkflowAdapter)(nil).StopDagJob), ctx, task)
}

// UpdateDagJob mocks base method.
func (m *MockWorkflowAdapter) UpdateDagJob(ctx context.Context, task *models.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDagJob", ctx, task)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDagJob indicates an expected call of UpdateDagJob.
func (mr *MockWorkflowAdapterMockRecorder) UpdateDagJob(ctx, task interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDagJob", reflect.TypeOf((*MockWorkflowAdapter)(nil).UpdateDagJob), ctx, task)
}
