package mocks

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	"mpp/internal/taskmanager/models/domain"
	reflect "reflect"
)

/**
 * @Author: qinxin
 * @Date: 2025/3/31 16:52
 * @Desc:
 * @Version 1.0
 */

// Code generated by MockGen. DO NOT EDIT.
// Source: internal/taskmanager/biz/gengine/mix_gray_rule_engine.go
/*待mock的方法
Create(ctx context.Context, mixGrayConfig *domain.MixGrayConfig) error
Delete(ctx context.Context, ruleName string) error
Update(ctx context.Context, mixGrayConfig *domain.MixGrayConfig) error
UpdateMixRatio(ctx context.Context, mixGrayConfig *domain.MixGrayConfig) error
InsertUpdateOnDuplicate(ctx context.Context, mixGrayConfig *domain.MixGrayConfig) error
GetMixGrayConfigByName(ctx context.Context, ruleName string) (*domain.MixGrayConfig, error)
ListActiveMixGrayConfig(ctx context.Context) ([]*domain.MixGrayConfig, error)
List(ctx context.Context) ([]*domain.MixGrayConfig, error)
*/

// MockMixGrayRuleEngineInterface is a mock of MixGrayRuleEngineInterface interface.
type MockMixGrayRuleEngineInterface struct {
	ctrl     *gomock.Controller
	recorder *MockMixGrayRuleEngineInterfaceMockRecorder
}

// MockMixGrayRuleEngineInterfaceMockRecorder is the mock recorder for MockMixGrayRuleEngineInterface.
type MockMixGrayRuleEngineInterfaceMockRecorder struct {
	mock *MockMixGrayRuleEngineInterface
}

// NewMockMixGrayRuleEngineInterface creates a new mock instance.
func NewMockMixGrayRuleEngineInterface(ctrl *gomock.Controller) *MockMixGrayRuleEngineInterface {
	mock := &MockMixGrayRuleEngineInterface{ctrl: ctrl}
	mock.recorder = &MockMixGrayRuleEngineInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMixGrayRuleEngineInterface) EXPECT() *MockMixGrayRuleEngineInterfaceMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockMixGrayRuleEngineInterface) Create(ctx context.Context, mixGrayConfig *domain.MixGrayConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, mixGrayConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockMixGrayRuleEngineInterfaceMockRecorder) Create(ctx, mixGrayConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockMixGrayRuleEngineInterface)(nil).Create), ctx, mixGrayConfig)
}

// Delete mocks base method.

func (m *MockMixGrayRuleEngineInterface) Delete(ctx context.Context, ruleName string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, ruleName)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockMixGrayRuleEngineInterfaceMockRecorder) Delete(ctx, ruleName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockMixGrayRuleEngineInterface)(nil).Delete), ctx, ruleName)
}

// Update mocks base method.

func (m *MockMixGrayRuleEngineInterface) Update(ctx context.Context, mixGrayConfig *domain.MixGrayConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, mixGrayConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.

func (mr *MockMixGrayRuleEngineInterfaceMockRecorder) Update(ctx, mixGrayConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockMixGrayRuleEngineInterface)(nil).Update), ctx, mixGrayConfig)
}

func (m *MockMixGrayRuleEngineInterface) UpdateMixRatio(ctx context.Context, mixGrayConfig *domain.MixGrayConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMixRatio", ctx, mixGrayConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

func (mr *MockMixGrayRuleEngineInterfaceMockRecorder) UpdateMixRatio(ctx, mixGrayConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMixRatio", reflect.TypeOf((*MockMixGrayRuleEngineInterface)(nil).UpdateMixRatio), ctx, mixGrayConfig)
}

func (m *MockMixGrayRuleEngineInterface) InsertUpdateOnDuplicate(ctx context.Context, mixGrayConfig *domain.MixGrayConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertUpdateOnDuplicate", ctx, mixGrayConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

func (mr *MockMixGrayRuleEngineInterfaceMockRecorder) InsertUpdateOnDuplicate(ctx, mixGrayConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertUpdateOnDuplicate", reflect.TypeOf((*MockMixGrayRuleEngineInterface)(nil).InsertUpdateOnDuplicate), ctx, mixGrayConfig)
}

func (m *MockMixGrayRuleEngineInterface) GetMixGrayConfigByName(ctx context.Context, ruleName string) (*domain.MixGrayConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMixGrayConfigByName", ctx, ruleName)
	ret0, _ := ret[0].(*domain.MixGrayConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockMixGrayRuleEngineInterfaceMockRecorder) GetMixGrayConfigByName(ctx, ruleName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMixGrayConfigByName", reflect.TypeOf((*MockMixGrayRuleEngineInterface)(nil).GetMixGrayConfigByName), ctx, ruleName)
}

func (m *MockMixGrayRuleEngineInterface) ListActiveMixGrayConfig(ctx context.Context) ([]*domain.MixGrayConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListActiveMixGrayConfig", ctx)
	ret0, _ := ret[0].([]*domain.MixGrayConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}
func (mr *MockMixGrayRuleEngineInterfaceMockRecorder) ListActiveMixGrayConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListActiveMixGrayConfig", reflect.TypeOf((*MockMixGrayRuleEngineInterface)(nil).ListActiveMixGrayConfig), ctx)
}
func (m *MockMixGrayRuleEngineInterface) List(ctx context.Context) ([]*domain.MixGrayConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx)
	ret0, _ := ret[0].([]*domain.MixGrayConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}
func (mr *MockMixGrayRuleEngineInterfaceMockRecorder) List(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockMixGrayRuleEngineInterface)(nil).List), ctx)
}
