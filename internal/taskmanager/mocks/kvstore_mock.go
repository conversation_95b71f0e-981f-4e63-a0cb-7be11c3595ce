// Code generated by MockGen. DO NOT EDIT.
// Source: internal/taskmanager/repository/kvstore.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	domain "mpp/internal/taskmanager/models/domain"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockKVStoreRepo is a mock of KVStoreRepo interface.
type MockKVStoreRepo struct {
	ctrl     *gomock.Controller
	recorder *MockKVStoreRepoMockRecorder
}

// MockKVStoreRepoMockRecorder is the mock recorder for MockKVStoreRepo.
type MockKVStoreRepoMockRecorder struct {
	mock *MockKVStoreRepo
}

// NewMockKVStoreRepo creates a new mock instance.
func NewMockKVStoreRepo(ctrl *gomock.Controller) *MockKVStoreRepo {
	mock := &MockKVStoreRepo{ctrl: ctrl}
	mock.recorder = &MockKVStoreRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKVStoreRepo) EXPECT() *MockKVStoreRepoMockRecorder {
	return m.recorder
}

// Cas mocks base method.
func (m *MockKVStoreRepo) Cas(arg0 context.Context, arg1, arg2 *domain.KVStore) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cas", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Cas indicates an expected call of Cas.
func (mr *MockKVStoreRepoMockRecorder) Cas(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cas", reflect.TypeOf((*MockKVStoreRepo)(nil).Cas), arg0, arg1, arg2)
}

// Delete mocks base method.
func (m *MockKVStoreRepo) Delete(arg0 context.Context, arg1 *domain.KVStore) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Delete", arg0, arg1)
}

// Delete indicates an expected call of Delete.
func (mr *MockKVStoreRepoMockRecorder) Delete(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockKVStoreRepo)(nil).Delete), arg0, arg1)
}

// Get mocks base method.
func (m *MockKVStoreRepo) Get(arg0 context.Context, arg1 *domain.KVStore) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockKVStoreRepoMockRecorder) Get(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockKVStoreRepo)(nil).Get), arg0, arg1)
}

// List mocks base method.
func (m *MockKVStoreRepo) List(ctx context.Context, repo, key string) ([]*domain.KVStore, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, repo, key)
	ret0, _ := ret[0].([]*domain.KVStore)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockKVStoreRepoMockRecorder) List(ctx, repo, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockKVStoreRepo)(nil).List), ctx, repo, key)
}

// ListAll mocks base method.
func (m *MockKVStoreRepo) ListAll(arg0 context.Context) ([]*domain.KVStore, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAll", arg0)
	ret0, _ := ret[0].([]*domain.KVStore)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAll indicates an expected call of ListAll.
func (mr *MockKVStoreRepoMockRecorder) ListAll(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAll", reflect.TypeOf((*MockKVStoreRepo)(nil).ListAll), arg0)
}

// ListLike mocks base method.
func (m *MockKVStoreRepo) ListLike(ctx context.Context, repo, key string) ([]*domain.KVStore, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListLike", ctx, repo, key)
	ret0, _ := ret[0].([]*domain.KVStore)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListLike indicates an expected call of ListLike.
func (mr *MockKVStoreRepoMockRecorder) ListLike(ctx, repo, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListLike", reflect.TypeOf((*MockKVStoreRepo)(nil).ListLike), ctx, repo, key)
}

// Set mocks base method.
func (m *MockKVStoreRepo) Set(arg0 context.Context, arg1 *domain.KVStore) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Set", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Set indicates an expected call of Set.
func (mr *MockKVStoreRepoMockRecorder) Set(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Set", reflect.TypeOf((*MockKVStoreRepo)(nil).Set), arg0, arg1)
}
