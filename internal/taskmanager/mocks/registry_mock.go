// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/animus/registry/registry.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	http "net/http"
	reflect "reflect"

	registry "github.com/cinience/animus/registry"
	gomock "github.com/golang/mock/gomock"
)

// MockRegistrar is a mock of Registrar interface.
type MockRegistrar struct {
	ctrl     *gomock.Controller
	recorder *MockRegistrarMockRecorder
}

// MockRegistrarMockRecorder is the mock recorder for MockRegistrar.
type MockRegistrarMockRecorder struct {
	mock *MockRegistrar
}

// NewMockRegistrar creates a new mock instance.
func NewMockRegistrar(ctrl *gomock.Controller) *MockRegistrar {
	mock := &MockRegistrar{ctrl: ctrl}
	mock.recorder = &MockRegistrarMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRegistrar) EXPECT() *MockRegistrarMockRecorder {
	return m.recorder
}

// Deregister mocks base method.
func (m *MockRegistrar) Deregister(ctx context.Context, service *registry.Service) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Deregister", ctx, service)
	ret0, _ := ret[0].(error)
	return ret0
}

// Deregister indicates an expected call of Deregister.
func (mr *MockRegistrarMockRecorder) Deregister(ctx, service interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Deregister", reflect.TypeOf((*MockRegistrar)(nil).Deregister), ctx, service)
}

// Register mocks base method.
func (m *MockRegistrar) Register(ctx context.Context, service *registry.Service) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Register", ctx, service)
	ret0, _ := ret[0].(error)
	return ret0
}

// Register indicates an expected call of Register.
func (mr *MockRegistrarMockRecorder) Register(ctx, service interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Register", reflect.TypeOf((*MockRegistrar)(nil).Register), ctx, service)
}

// MockDiscovery is a mock of Discovery interface.
type MockDiscovery struct {
	ctrl     *gomock.Controller
	recorder *MockDiscoveryMockRecorder
}

// MockDiscoveryMockRecorder is the mock recorder for MockDiscovery.
type MockDiscoveryMockRecorder struct {
	mock *MockDiscovery
}

// NewMockDiscovery creates a new mock instance.
func NewMockDiscovery(ctrl *gomock.Controller) *MockDiscovery {
	mock := &MockDiscovery{ctrl: ctrl}
	mock.recorder = &MockDiscoveryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDiscovery) EXPECT() *MockDiscoveryMockRecorder {
	return m.recorder
}

// GetService mocks base method.
func (m *MockDiscovery) GetService(ctx context.Context, serviceName string) ([]*registry.Service, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetService", ctx, serviceName)
	ret0, _ := ret[0].([]*registry.Service)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetService indicates an expected call of GetService.
func (mr *MockDiscoveryMockRecorder) GetService(ctx, serviceName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetService", reflect.TypeOf((*MockDiscovery)(nil).GetService), ctx, serviceName)
}

// GetServiceList mocks base method.
func (m *MockDiscovery) GetServiceList(ctx context.Context, uScheme, serviceNamePrefix string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceList", ctx, uScheme, serviceNamePrefix)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceList indicates an expected call of GetServiceList.
func (mr *MockDiscoveryMockRecorder) GetServiceList(ctx, uScheme, serviceNamePrefix interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceList", reflect.TypeOf((*MockDiscovery)(nil).GetServiceList), ctx, uScheme, serviceNamePrefix)
}

// Overhaul mocks base method.
func (m *MockDiscovery) Overhaul(w http.ResponseWriter, r *http.Request) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Overhaul", w, r)
}

// Overhaul indicates an expected call of Overhaul.
func (mr *MockDiscoveryMockRecorder) Overhaul(w, r interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Overhaul", reflect.TypeOf((*MockDiscovery)(nil).Overhaul), w, r)
}

// PickService mocks base method.
func (m *MockDiscovery) PickService(ctx context.Context, serviceName string) (*registry.Service, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PickService", ctx, serviceName)
	ret0, _ := ret[0].(*registry.Service)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PickService indicates an expected call of PickService.
func (mr *MockDiscoveryMockRecorder) PickService(ctx, serviceName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PickService", reflect.TypeOf((*MockDiscovery)(nil).PickService), ctx, serviceName)
}

// Watch mocks base method.
func (m *MockDiscovery) Watch(ctx context.Context, serviceName string) (registry.Watcher, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Watch", ctx, serviceName)
	ret0, _ := ret[0].(registry.Watcher)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Watch indicates an expected call of Watch.
func (mr *MockDiscoveryMockRecorder) Watch(ctx, serviceName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Watch", reflect.TypeOf((*MockDiscovery)(nil).Watch), ctx, serviceName)
}
