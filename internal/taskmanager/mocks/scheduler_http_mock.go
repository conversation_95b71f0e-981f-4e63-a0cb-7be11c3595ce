// Code generated by MockGen. DO NOT EDIT.
// Source: vendor/proto.mpp/api/taskscheduler/v1/schedule_http.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	v1 "proto.mpp/api/taskscheduler/v1"
	reflect "reflect"

	http "github.com/go-kratos/kratos/v2/transport/http"
	gomock "github.com/golang/mock/gomock"
)

// MockScheduleHTTPServer is a mock of ScheduleHTTPServer interface.
type MockScheduleHTTPServer struct {
	ctrl     *gomock.Controller
	recorder *MockScheduleHTTPServerMockRecorder
}

// MockScheduleHTTPServerMockRecorder is the mock recorder for MockScheduleHTTPServer.
type MockScheduleHTTPServerMockRecorder struct {
	mock *MockScheduleHTTPServer
}

// NewMockScheduleHTTPServer creates a new mock instance.
func NewMockScheduleHTTPServer(ctrl *gomock.Controller) *MockScheduleHTTPServer {
	mock := &MockScheduleHTTPServer{ctrl: ctrl}
	mock.recorder = &MockScheduleHTTPServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockScheduleHTTPServer) EXPECT() *MockScheduleHTTPServerMockRecorder {
	return m.recorder
}

// AllocMasterResource mocks base method.
func (m *MockScheduleHTTPServer) AllocMasterResource(arg0 context.Context, arg1 *v1.AllocMasterResourceRequest) (*v1.AllocMasterResourceReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AllocMasterResource", arg0, arg1)
	ret0, _ := ret[0].(*v1.AllocMasterResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AllocMasterResource indicates an expected call of AllocMasterResource.
func (mr *MockScheduleHTTPServerMockRecorder) AllocMasterResource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllocMasterResource", reflect.TypeOf((*MockScheduleHTTPServer)(nil).AllocMasterResource), arg0, arg1)
}

// AllocResource mocks base method.
func (m *MockScheduleHTTPServer) AllocResource(arg0 context.Context, arg1 *v1.AllocResourceRequest) (*v1.AllocResourceReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AllocResource", arg0, arg1)
	ret0, _ := ret[0].(*v1.AllocResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AllocResource indicates an expected call of AllocResource.
func (mr *MockScheduleHTTPServerMockRecorder) AllocResource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllocResource", reflect.TypeOf((*MockScheduleHTTPServer)(nil).AllocResource), arg0, arg1)
}

// FreeMasterResource mocks base method.
func (m *MockScheduleHTTPServer) FreeMasterResource(arg0 context.Context, arg1 *v1.FreeMasterResourceRequest) (*v1.FreeMasterResourceReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FreeMasterResource", arg0, arg1)
	ret0, _ := ret[0].(*v1.FreeMasterResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreeMasterResource indicates an expected call of FreeMasterResource.
func (mr *MockScheduleHTTPServerMockRecorder) FreeMasterResource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreeMasterResource", reflect.TypeOf((*MockScheduleHTTPServer)(nil).FreeMasterResource), arg0, arg1)
}

// FreeResource mocks base method.
func (m *MockScheduleHTTPServer) FreeResource(arg0 context.Context, arg1 *v1.FreeResourceRequest) (*v1.FreeResourceReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FreeResource", arg0, arg1)
	ret0, _ := ret[0].(*v1.FreeResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreeResource indicates an expected call of FreeResource.
func (mr *MockScheduleHTTPServerMockRecorder) FreeResource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreeResource", reflect.TypeOf((*MockScheduleHTTPServer)(nil).FreeResource), arg0, arg1)
}

// GetMasterWorker mocks base method.
func (m *MockScheduleHTTPServer) GetMasterWorker(arg0 context.Context, arg1 *v1.GetMasterWorkerRequest) (*v1.GetMasterWorkerReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMasterWorker", arg0, arg1)
	ret0, _ := ret[0].(*v1.GetMasterWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMasterWorker indicates an expected call of GetMasterWorker.
func (mr *MockScheduleHTTPServerMockRecorder) GetMasterWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMasterWorker", reflect.TypeOf((*MockScheduleHTTPServer)(nil).GetMasterWorker), arg0, arg1)
}

// UpdateMasterResource mocks base method.
func (m *MockScheduleHTTPServer) UpdateMasterResource(arg0 context.Context, arg1 *v1.UpdateMasterResourceRequest) (*v1.UpdateMasterResourceReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMasterResource", arg0, arg1)
	ret0, _ := ret[0].(*v1.UpdateMasterResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateMasterResource indicates an expected call of UpdateMasterResource.
func (mr *MockScheduleHTTPServerMockRecorder) UpdateMasterResource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMasterResource", reflect.TypeOf((*MockScheduleHTTPServer)(nil).UpdateMasterResource), arg0, arg1)
}

// UpdateResource mocks base method.
func (m *MockScheduleHTTPServer) UpdateResource(arg0 context.Context, arg1 *v1.UpdateResourceRequest) (*v1.UpdateResourceReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateResource", arg0, arg1)
	ret0, _ := ret[0].(*v1.UpdateResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateResource indicates an expected call of UpdateResource.
func (mr *MockScheduleHTTPServerMockRecorder) UpdateResource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateResource", reflect.TypeOf((*MockScheduleHTTPServer)(nil).UpdateResource), arg0, arg1)
}

// MockScheduleHTTPClient is a mock of ScheduleHTTPClient interface.
type MockScheduleHTTPClient struct {
	ctrl     *gomock.Controller
	recorder *MockScheduleHTTPClientMockRecorder
}

// MockScheduleHTTPClientMockRecorder is the mock recorder for MockScheduleHTTPClient.
type MockScheduleHTTPClientMockRecorder struct {
	mock *MockScheduleHTTPClient
}

// NewMockScheduleHTTPClient creates a new mock instance.
func NewMockScheduleHTTPClient(ctrl *gomock.Controller) *MockScheduleHTTPClient {
	mock := &MockScheduleHTTPClient{ctrl: ctrl}
	mock.recorder = &MockScheduleHTTPClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockScheduleHTTPClient) EXPECT() *MockScheduleHTTPClientMockRecorder {
	return m.recorder
}

// AllocMasterResource mocks base method.
func (m *MockScheduleHTTPClient) AllocMasterResource(ctx context.Context, req *v1.AllocMasterResourceRequest, opts ...http.CallOption) (*v1.AllocMasterResourceReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AllocMasterResource", varargs...)
	ret0, _ := ret[0].(*v1.AllocMasterResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AllocMasterResource indicates an expected call of AllocMasterResource.
func (mr *MockScheduleHTTPClientMockRecorder) AllocMasterResource(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllocMasterResource", reflect.TypeOf((*MockScheduleHTTPClient)(nil).AllocMasterResource), varargs...)
}

// AllocResource mocks base method.
func (m *MockScheduleHTTPClient) AllocResource(ctx context.Context, req *v1.AllocResourceRequest, opts ...http.CallOption) (*v1.AllocResourceReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AllocResource", varargs...)
	ret0, _ := ret[0].(*v1.AllocResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AllocResource indicates an expected call of AllocResource.
func (mr *MockScheduleHTTPClientMockRecorder) AllocResource(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllocResource", reflect.TypeOf((*MockScheduleHTTPClient)(nil).AllocResource), varargs...)
}

// FreeMasterResource mocks base method.
func (m *MockScheduleHTTPClient) FreeMasterResource(ctx context.Context, req *v1.FreeMasterResourceRequest, opts ...http.CallOption) (*v1.FreeMasterResourceReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FreeMasterResource", varargs...)
	ret0, _ := ret[0].(*v1.FreeMasterResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreeMasterResource indicates an expected call of FreeMasterResource.
func (mr *MockScheduleHTTPClientMockRecorder) FreeMasterResource(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreeMasterResource", reflect.TypeOf((*MockScheduleHTTPClient)(nil).FreeMasterResource), varargs...)
}

// FreeResource mocks base method.
func (m *MockScheduleHTTPClient) FreeResource(ctx context.Context, req *v1.FreeResourceRequest, opts ...http.CallOption) (*v1.FreeResourceReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FreeResource", varargs...)
	ret0, _ := ret[0].(*v1.FreeResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreeResource indicates an expected call of FreeResource.
func (mr *MockScheduleHTTPClientMockRecorder) FreeResource(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreeResource", reflect.TypeOf((*MockScheduleHTTPClient)(nil).FreeResource), varargs...)
}

// GetMasterWorker mocks base method.
func (m *MockScheduleHTTPClient) GetMasterWorker(ctx context.Context, req *v1.GetMasterWorkerRequest, opts ...http.CallOption) (*v1.GetMasterWorkerReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMasterWorker", varargs...)
	ret0, _ := ret[0].(*v1.GetMasterWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMasterWorker indicates an expected call of GetMasterWorker.
func (mr *MockScheduleHTTPClientMockRecorder) GetMasterWorker(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMasterWorker", reflect.TypeOf((*MockScheduleHTTPClient)(nil).GetMasterWorker), varargs...)
}

// UpdateMasterResource mocks base method.
func (m *MockScheduleHTTPClient) UpdateMasterResource(ctx context.Context, req *v1.UpdateMasterResourceRequest, opts ...http.CallOption) (*v1.UpdateMasterResourceReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateMasterResource", varargs...)
	ret0, _ := ret[0].(*v1.UpdateMasterResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateMasterResource indicates an expected call of UpdateMasterResource.
func (mr *MockScheduleHTTPClientMockRecorder) UpdateMasterResource(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMasterResource", reflect.TypeOf((*MockScheduleHTTPClient)(nil).UpdateMasterResource), varargs...)
}

// UpdateResource mocks base method.
func (m *MockScheduleHTTPClient) UpdateResource(ctx context.Context, req *v1.UpdateResourceRequest, opts ...http.CallOption) (*v1.UpdateResourceReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateResource", varargs...)
	ret0, _ := ret[0].(*v1.UpdateResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateResource indicates an expected call of UpdateResource.
func (mr *MockScheduleHTTPClientMockRecorder) UpdateResource(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateResource", reflect.TypeOf((*MockScheduleHTTPClient)(nil).UpdateResource), varargs...)
}
