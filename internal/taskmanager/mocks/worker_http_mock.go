// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/dev_manus/mpp/vendor/proto.mpp/api/taskscheduler/v1/worker_http.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	v1 "proto.mpp/api/taskscheduler/v1"
	reflect "reflect"

	http "github.com/go-kratos/kratos/v2/transport/http"
	gomock "github.com/golang/mock/gomock"
)

// MockWorkerHTTPServer is a mock of WorkerHTTPServer interface.
type MockWorkerHTTPServer struct {
	ctrl     *gomock.Controller
	recorder *MockWorkerHTTPServerMockRecorder
}

// MockWorkerHTTPServerMockRecorder is the mock recorder for MockWorkerHTTPServer.
type MockWorkerHTTPServerMockRecorder struct {
	mock *MockWorkerHTTPServer
}

// NewMockWorkerHTTPServer creates a new mock instance.
func NewMockWorkerHTTPServer(ctrl *gomock.Controller) *MockWorkerHTTPServer {
	mock := &MockWorkerHTTPServer{ctrl: ctrl}
	mock.recorder = &MockWorkerHTTPServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWorkerHTTPServer) EXPECT() *MockWorkerHTTPServerMockRecorder {
	return m.recorder
}

// GetWorker mocks base method.
func (m *MockWorkerHTTPServer) GetWorker(arg0 context.Context, arg1 *v1.GetWorkerRequest) (*v1.GetWorkerReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorker", arg0, arg1)
	ret0, _ := ret[0].(*v1.GetWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorker indicates an expected call of GetWorker.
func (mr *MockWorkerHTTPServerMockRecorder) GetWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorker", reflect.TypeOf((*MockWorkerHTTPServer)(nil).GetWorker), arg0, arg1)
}

// Heartbeat mocks base method.
func (m *MockWorkerHTTPServer) Heartbeat(arg0 context.Context, arg1 *v1.HeartbeatWorkerRequest) (*v1.HeartbeatWorkerReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Heartbeat", arg0, arg1)
	ret0, _ := ret[0].(*v1.HeartbeatWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Heartbeat indicates an expected call of Heartbeat.
func (mr *MockWorkerHTTPServerMockRecorder) Heartbeat(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Heartbeat", reflect.TypeOf((*MockWorkerHTTPServer)(nil).Heartbeat), arg0, arg1)
}

// LabelWorker mocks base method.
func (m *MockWorkerHTTPServer) LabelWorker(arg0 context.Context, arg1 *v1.LabelWorkerRequest) (*v1.LabelWorkerReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LabelWorker", arg0, arg1)
	ret0, _ := ret[0].(*v1.LabelWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LabelWorker indicates an expected call of LabelWorker.
func (mr *MockWorkerHTTPServerMockRecorder) LabelWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LabelWorker", reflect.TypeOf((*MockWorkerHTTPServer)(nil).LabelWorker), arg0, arg1)
}

// ListWorker mocks base method.
func (m *MockWorkerHTTPServer) ListWorker(arg0 context.Context, arg1 *v1.ListWorkerRequest) (*v1.ListWorkerReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListWorker", arg0, arg1)
	ret0, _ := ret[0].(*v1.ListWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListWorker indicates an expected call of ListWorker.
func (mr *MockWorkerHTTPServerMockRecorder) ListWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListWorker", reflect.TypeOf((*MockWorkerHTTPServer)(nil).ListWorker), arg0, arg1)
}

// Register mocks base method.
func (m *MockWorkerHTTPServer) Register(arg0 context.Context, arg1 *v1.RegisterWorkerRequest) (*v1.RegisterWorkerReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Register", arg0, arg1)
	ret0, _ := ret[0].(*v1.RegisterWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Register indicates an expected call of Register.
func (mr *MockWorkerHTTPServerMockRecorder) Register(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Register", reflect.TypeOf((*MockWorkerHTTPServer)(nil).Register), arg0, arg1)
}

// RegisterChildWorker mocks base method.
func (m *MockWorkerHTTPServer) RegisterChildWorker(arg0 context.Context, arg1 *v1.RegisterChildWorkerRequest) (*v1.RegisterChildWorkerReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RegisterChildWorker", arg0, arg1)
	ret0, _ := ret[0].(*v1.RegisterChildWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RegisterChildWorker indicates an expected call of RegisterChildWorker.
func (mr *MockWorkerHTTPServerMockRecorder) RegisterChildWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterChildWorker", reflect.TypeOf((*MockWorkerHTTPServer)(nil).RegisterChildWorker), arg0, arg1)
}

// TagWorker mocks base method.
func (m *MockWorkerHTTPServer) TagWorker(arg0 context.Context, arg1 *v1.TagWorkerRequest) (*v1.TagWorkerReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TagWorker", arg0, arg1)
	ret0, _ := ret[0].(*v1.TagWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TagWorker indicates an expected call of TagWorker.
func (mr *MockWorkerHTTPServerMockRecorder) TagWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TagWorker", reflect.TypeOf((*MockWorkerHTTPServer)(nil).TagWorker), arg0, arg1)
}

// TaintWorker mocks base method.
func (m *MockWorkerHTTPServer) TaintWorker(arg0 context.Context, arg1 *v1.TaintWorkerRequest) (*v1.TaintWorkerReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TaintWorker", arg0, arg1)
	ret0, _ := ret[0].(*v1.TaintWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TaintWorker indicates an expected call of TaintWorker.
func (mr *MockWorkerHTTPServerMockRecorder) TaintWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TaintWorker", reflect.TypeOf((*MockWorkerHTTPServer)(nil).TaintWorker), arg0, arg1)
}

// UnregisterChildWorker mocks base method.
func (m *MockWorkerHTTPServer) UnregisterChildWorker(arg0 context.Context, arg1 *v1.UnregisterChildWorkerRequest) (*v1.UnregisterChildWorkerReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnregisterChildWorker", arg0, arg1)
	ret0, _ := ret[0].(*v1.UnregisterChildWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnregisterChildWorker indicates an expected call of UnregisterChildWorker.
func (mr *MockWorkerHTTPServerMockRecorder) UnregisterChildWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnregisterChildWorker", reflect.TypeOf((*MockWorkerHTTPServer)(nil).UnregisterChildWorker), arg0, arg1)
}

// UnregisterWorker mocks base method.
func (m *MockWorkerHTTPServer) UnregisterWorker(arg0 context.Context, arg1 *v1.UnregisterWorkerRequest) (*v1.UnregisterWorkerReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnregisterWorker", arg0, arg1)
	ret0, _ := ret[0].(*v1.UnregisterWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnregisterWorker indicates an expected call of UnregisterWorker.
func (mr *MockWorkerHTTPServerMockRecorder) UnregisterWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnregisterWorker", reflect.TypeOf((*MockWorkerHTTPServer)(nil).UnregisterWorker), arg0, arg1)
}

// UntaintWorker mocks base method.
func (m *MockWorkerHTTPServer) UntaintWorker(arg0 context.Context, arg1 *v1.UntaintWorkerRequest) (*v1.UntaintWorkerReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UntaintWorker", arg0, arg1)
	ret0, _ := ret[0].(*v1.UntaintWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UntaintWorker indicates an expected call of UntaintWorker.
func (mr *MockWorkerHTTPServerMockRecorder) UntaintWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UntaintWorker", reflect.TypeOf((*MockWorkerHTTPServer)(nil).UntaintWorker), arg0, arg1)
}

// MockWorkerHTTPClient is a mock of WorkerHTTPClient interface.
type MockWorkerHTTPClient struct {
	ctrl     *gomock.Controller
	recorder *MockWorkerHTTPClientMockRecorder
}

// MockWorkerHTTPClientMockRecorder is the mock recorder for MockWorkerHTTPClient.
type MockWorkerHTTPClientMockRecorder struct {
	mock *MockWorkerHTTPClient
}

// NewMockWorkerHTTPClient creates a new mock instance.
func NewMockWorkerHTTPClient(ctrl *gomock.Controller) *MockWorkerHTTPClient {
	mock := &MockWorkerHTTPClient{ctrl: ctrl}
	mock.recorder = &MockWorkerHTTPClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWorkerHTTPClient) EXPECT() *MockWorkerHTTPClientMockRecorder {
	return m.recorder
}

// GetWorker mocks base method.
func (m *MockWorkerHTTPClient) GetWorker(ctx context.Context, req *v1.GetWorkerRequest, opts ...http.CallOption) (*v1.GetWorkerReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWorker", varargs...)
	ret0, _ := ret[0].(*v1.GetWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorker indicates an expected call of GetWorker.
func (mr *MockWorkerHTTPClientMockRecorder) GetWorker(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorker", reflect.TypeOf((*MockWorkerHTTPClient)(nil).GetWorker), varargs...)
}

// Heartbeat mocks base method.
func (m *MockWorkerHTTPClient) Heartbeat(ctx context.Context, req *v1.HeartbeatWorkerRequest, opts ...http.CallOption) (*v1.HeartbeatWorkerReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Heartbeat", varargs...)
	ret0, _ := ret[0].(*v1.HeartbeatWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Heartbeat indicates an expected call of Heartbeat.
func (mr *MockWorkerHTTPClientMockRecorder) Heartbeat(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Heartbeat", reflect.TypeOf((*MockWorkerHTTPClient)(nil).Heartbeat), varargs...)
}

// LabelWorker mocks base method.
func (m *MockWorkerHTTPClient) LabelWorker(ctx context.Context, req *v1.LabelWorkerRequest, opts ...http.CallOption) (*v1.LabelWorkerReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LabelWorker", varargs...)
	ret0, _ := ret[0].(*v1.LabelWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LabelWorker indicates an expected call of LabelWorker.
func (mr *MockWorkerHTTPClientMockRecorder) LabelWorker(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LabelWorker", reflect.TypeOf((*MockWorkerHTTPClient)(nil).LabelWorker), varargs...)
}

// ListWorker mocks base method.
func (m *MockWorkerHTTPClient) ListWorker(ctx context.Context, req *v1.ListWorkerRequest, opts ...http.CallOption) (*v1.ListWorkerReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListWorker", varargs...)
	ret0, _ := ret[0].(*v1.ListWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListWorker indicates an expected call of ListWorker.
func (mr *MockWorkerHTTPClientMockRecorder) ListWorker(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListWorker", reflect.TypeOf((*MockWorkerHTTPClient)(nil).ListWorker), varargs...)
}

// Register mocks base method.
func (m *MockWorkerHTTPClient) Register(ctx context.Context, req *v1.RegisterWorkerRequest, opts ...http.CallOption) (*v1.RegisterWorkerReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Register", varargs...)
	ret0, _ := ret[0].(*v1.RegisterWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Register indicates an expected call of Register.
func (mr *MockWorkerHTTPClientMockRecorder) Register(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Register", reflect.TypeOf((*MockWorkerHTTPClient)(nil).Register), varargs...)
}

// RegisterChildWorker mocks base method.
func (m *MockWorkerHTTPClient) RegisterChildWorker(ctx context.Context, req *v1.RegisterChildWorkerRequest, opts ...http.CallOption) (*v1.RegisterChildWorkerReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RegisterChildWorker", varargs...)
	ret0, _ := ret[0].(*v1.RegisterChildWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RegisterChildWorker indicates an expected call of RegisterChildWorker.
func (mr *MockWorkerHTTPClientMockRecorder) RegisterChildWorker(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterChildWorker", reflect.TypeOf((*MockWorkerHTTPClient)(nil).RegisterChildWorker), varargs...)
}

// TagWorker mocks base method.
func (m *MockWorkerHTTPClient) TagWorker(ctx context.Context, req *v1.TagWorkerRequest, opts ...http.CallOption) (*v1.TagWorkerReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TagWorker", varargs...)
	ret0, _ := ret[0].(*v1.TagWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TagWorker indicates an expected call of TagWorker.
func (mr *MockWorkerHTTPClientMockRecorder) TagWorker(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TagWorker", reflect.TypeOf((*MockWorkerHTTPClient)(nil).TagWorker), varargs...)
}

// TaintWorker mocks base method.
func (m *MockWorkerHTTPClient) TaintWorker(ctx context.Context, req *v1.TaintWorkerRequest, opts ...http.CallOption) (*v1.TaintWorkerReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TaintWorker", varargs...)
	ret0, _ := ret[0].(*v1.TaintWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TaintWorker indicates an expected call of TaintWorker.
func (mr *MockWorkerHTTPClientMockRecorder) TaintWorker(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TaintWorker", reflect.TypeOf((*MockWorkerHTTPClient)(nil).TaintWorker), varargs...)
}

// UnregisterChildWorker mocks base method.
func (m *MockWorkerHTTPClient) UnregisterChildWorker(ctx context.Context, req *v1.UnregisterChildWorkerRequest, opts ...http.CallOption) (*v1.UnregisterChildWorkerReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnregisterChildWorker", varargs...)
	ret0, _ := ret[0].(*v1.UnregisterChildWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnregisterChildWorker indicates an expected call of UnregisterChildWorker.
func (mr *MockWorkerHTTPClientMockRecorder) UnregisterChildWorker(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnregisterChildWorker", reflect.TypeOf((*MockWorkerHTTPClient)(nil).UnregisterChildWorker), varargs...)
}

// UnregisterWorker mocks base method.
func (m *MockWorkerHTTPClient) UnregisterWorker(ctx context.Context, req *v1.UnregisterWorkerRequest, opts ...http.CallOption) (*v1.UnregisterWorkerReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnregisterWorker", varargs...)
	ret0, _ := ret[0].(*v1.UnregisterWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnregisterWorker indicates an expected call of UnregisterWorker.
func (mr *MockWorkerHTTPClientMockRecorder) UnregisterWorker(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnregisterWorker", reflect.TypeOf((*MockWorkerHTTPClient)(nil).UnregisterWorker), varargs...)
}

// UntaintWorker mocks base method.
func (m *MockWorkerHTTPClient) UntaintWorker(ctx context.Context, req *v1.UntaintWorkerRequest, opts ...http.CallOption) (*v1.UntaintWorkerReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UntaintWorker", varargs...)
	ret0, _ := ret[0].(*v1.UntaintWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UntaintWorker indicates an expected call of UntaintWorker.
func (mr *MockWorkerHTTPClientMockRecorder) UntaintWorker(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UntaintWorker", reflect.TypeOf((*MockWorkerHTTPClient)(nil).UntaintWorker), varargs...)
}
