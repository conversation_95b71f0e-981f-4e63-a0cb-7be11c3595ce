package data

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"

	"mpp/internal/taskscheduler/biz"
)

const (
	DefaultRepo        = "default"
	DefaultExpiredTime = 86400 * 365 * 100
)

type kvStore struct {
	DBBaseModel
	Repo        string    `gorm:"column:repo;uniqueIndex:idx_repo_key;type:varchar(64);NOT NULL;COMMENT:仓库"`
	Key         string    `gorm:"column:key;uniqueIndex:idx_repo_key;type:varchar(64);NOT NULL;COMMENT:键"`
	Value       string    `gorm:"column:value;type:varchar(256);NOT NULL;COMMENT:值"`
	ExpiredTime time.Time `gorm:"column:gmt_expired;index:idx_expired;type:datetime;NOT NULL;COMMENT:过期时间"`
}

type dbKVStore struct {
	data *Data
	log  *log.Helper
}

func NewDBKVStore(data *Data, logger log.Logger) biz.KVStoreRepo {
	return &dbKVStore{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (k *dbKVStore) Set(ctx context.Context, kv *biz.KVStore) error {
	result := k.data.db.WithContext(ctx).Exec(
		"INSERT INTO kv_stores(gmt_create, gmt_modified, repo, `key`, value, gmt_expired) "+
			"VALUES(now(), now(), ?, ?, ?, ?) "+
			"ON DUPLICATE KEY UPDATE value = ?, gmt_expired = ?, gmt_modified = now()",
		kv.Repo, kv.Key, kv.Value, kv.ExpiredTime, kv.Value, kv.ExpiredTime,
	)
	if result.Error != nil {
		k.log.WithContext(ctx).Warnf("set kvstore failed, err:%v", result)
		return result.Error
	}

	return nil
}

func (k *dbKVStore) ListAll(ctx context.Context) ([]*biz.KVStore, error) {
	var kvs []kvStore
	result := k.data.db.WithContext(ctx).Where("CURRENT_TIMESTAMP <= gmt_expired").Find(&kvs)
	if result.Error != nil {
		return nil, result.Error
	}

	var kvStores []*biz.KVStore

	for _, kv := range kvs {
		kvStore := biz.KVStore{}
		err := copier.Copy(&kvStore, &kv)
		if nil != err {
			k.log.WithContext(ctx).Warnf("copy kvstore failed, err:%v", err)
			return nil, err
		}
		kvStores = append(kvStores, &kvStore)
	}

	return kvStores, nil
}

func (k *dbKVStore) Get(ctx context.Context, kv *biz.KVStore) (string, error) {
	result := k.data.db.WithContext(ctx).Where("repo = ? AND `key` = ? AND CURRENT_TIMESTAMP < gmt_expired", kv.Repo, kv.Key).Take(&kv)
	if result.Error != nil {
		k.log.WithContext(ctx).Warnf("get kvstore failed, err:%v", result)
		return kv.Value, result.Error
	}

	return kv.Value, nil
}

func (k *dbKVStore) Delete(ctx context.Context, kv *biz.KVStore) {
	k.data.db.WithContext(ctx).Where("repo =? and `key` =?", kv.Repo, kv.Key).Delete(&kvStore{})
}

func (k *dbKVStore) Cas(ctx context.Context, newKV *biz.KVStore, oldKV *biz.KVStore) (bool, error) {
	result := k.data.db.WithContext(ctx).Exec(
		"UPDATE kv_stores SET value = ?, gmt_expired = ?, gmt_modified = CURRENT_TIMESTAMP WHERE repo = ? AND `key` = ? AND value = ?",
		newKV.Value, newKV.ExpiredTime, oldKV.Repo, oldKV.Key, oldKV.Value,
	)
	if result.Error != nil {
		k.log.WithContext(ctx).Warnf("cas kvstore failed, err:%v", result)
		return false, result.Error
	}

	if result.RowsAffected == 0 {
		k.log.WithContext(ctx).Warnf("compare and set kvstore new:%+v old:%+v failed, affected rows is 0", newKV, oldKV)
		return false, nil
	}

	return true, nil
}
