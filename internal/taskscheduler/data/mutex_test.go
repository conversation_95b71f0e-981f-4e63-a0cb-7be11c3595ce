package data_test

import (
	"time"

	"github.com/go-kratos/kratos/v2/log"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/data"
	"mpp/internal/taskscheduler/testdata"
)

var _ = Describe("Mutex", func() {
	var ro biz.MutexRepo
	var uD *biz.Mutex
	var uDList []*biz.Mutex
	BeforeEach(func() {
		// 这里的 Db 是 data_suite_test.go 文件里面定义的
		ro = data.NewMutexRepo(Db, log.GetLogger())
		// 这里你可以引入外部组装好的数据
		uD = testdata.Lock()

		uDList = testdata.LockList(10)
	})

	// 悲观锁：普通加解锁
	It("Lock", func() {
		ok := ro.Lock(ctx, uD)
		Ω(ok).Should(Equal(true))
		ok = ro.Unlock(ctx, uD)
		Ω(ok).Should(Equal(true))
	})

	// 悲观锁：超时抢锁
	It("LockExpired", func() {
		ok := ro.Lock(ctx, uD)
		Ω(ok).Should(Equal(true))

		// 抢锁失败
		newLock := &biz.Mutex{
			Name:   uD.Name,
			Owner:  "newOwner",
			Second: 1,
		}

		ok = ro.Lock(ctx, newLock)
		Ω(ok).Should(Equal(false))

		// 等待两秒
		time.Sleep(2 * time.Second)

		// 抢锁成功
		ok = ro.Lock(ctx, uD)
		Ω(ok).Should(Equal(true))

		// 解锁
		ok = ro.Unlock(ctx, uD)
		Ω(ok).Should(Equal(true))
	})

	// 锁的批量操作
	It("ListLock", func() {
		for _, mutex := range uDList {
			ok := ro.Lock(ctx, mutex)
			Ω(ok).Should(Equal(true))
		}
		mutexes, err := ro.ListLock(ctx)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(mutexes)).Should(Equal(len(uDList)))
		for i, mutex := range mutexes {
			LockEqual(mutex, uDList[i])
		}

		time.Sleep(2 * time.Second)

		mutexes, err = ro.ListLock(ctx)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(mutexes)).Should(Equal(len(uDList) / 2))
		for i, mutex := range mutexes {
			LockEqual(mutex, uDList[5+i])
		}

		mutexes, err = ro.ListExpired(ctx)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(mutexes)).Should(Equal(len(uDList) / 2))
		for i, mutex := range mutexes {
			LockEqual(mutex, uDList[i])
			ok := ro.Unlock(ctx, mutex)
			Ω(ok).Should(Equal(true))
		}
	})
})

func LockEqual(a *biz.Mutex, b *biz.Mutex) {
	Ω(a.Name).Should(Equal(b.Name))
	Ω(a.Owner).Should(Equal(b.Owner))
}
