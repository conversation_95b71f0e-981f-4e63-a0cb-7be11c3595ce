package data_test

import (
	"sort"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/data"
	"mpp/internal/taskscheduler/testdata"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Worker", func() {
	var ro biz.WorkerRepo
	var uD *biz.Worker
	var uDList []*biz.Worker
	BeforeEach(func() {
		// 这里的 Db 是 data_suite_test.go 文件里面定义的
		ro = data.NewWorkerRepo(Db, log.GetLogger())
		// 这里你可以引入外部组装好的数据
		uD = testdata.Worker()

		uDList = testdata.WorkerList(10)
	})

	// ListWorker
	It("ListWorker", func() {
		for _, level := range uDList {
			_, err := ro.Save(ctx, level)
			Ω(err).ShouldNot(HaveOccurred())
		}

		workers, err := ro.ListOnlineWorker(ctx)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(workers)).Should(Equal(len(uDList)))
		for i, worker := range workers {
			WorkerBaseEqual(worker, uDList[i])
		}

		_, err = ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		workers, err = ro.ListByConditions(ctx, uDList[0].Product, uDList[0].EngineModel, uDList[0].Tag, nil)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(workers)).Should(Equal(len(uDList)))
		for i, worker := range workers {
			WorkerBaseEqual(worker, uDList[i])
		}

		workers, err = ro.ListByWorkerIds(ctx, []string{uDList[2].WorkerId, uDList[3].WorkerId})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(workers)).Should(Equal(2))
		for i, worker := range workers {
			WorkerBaseEqual(worker, uDList[2+i])
		}

		workers, err = ro.ListByIps(ctx, []string{uDList[4].Ip, uDList[5].Ip})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(workers)).Should(Equal(2))
		for i, worker := range workers {
			WorkerBaseEqual(worker, uDList[4+i])
		}
	})

	// 创建和查询worker
	It("CreateWorker", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		worker, err := ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		WorkerBaseEqual(worker, uD)
	})

	// 查询worker
	It("QueryWorker", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		worker, err := ro.FindByIp(ctx, uD.Ip)
		Ω(err).ShouldNot(HaveOccurred())
		WorkerBaseEqual(worker, uD)
	})

	// 更新心跳
	It("UpdateWorkerHeartbeat", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())
		worker, _ := ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		log.Infof("worker:%+v", biz.JsonToString(worker))

		uD.Load = 1.1
		uD.RealQuota = map[string]int64{"cpu": 55}
		uD.AvgQuota = map[string]int64{"cpu": 44}

		time.Sleep(1 * time.Second)
		err = ro.UpdateHeartbeat(ctx, uD, false)
		Ω(err).ShouldNot(HaveOccurred())
		err = ro.UpdateHeartbeat(ctx, uD, false)
		worker, _ = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		log.Infof("worker:%+v", biz.JsonToString(worker))
		Ω(err).ShouldNot(HaveOccurred())

		worker, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		WorkerHeartbeatCompare(worker, uD)

		// 更新心跳
		uD.HeartbeatTime = worker.HeartbeatTime
	})

	// 更新心跳
	It("UpdateWorkerHeartbeatWithScheduler", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		uD.Load = 1.1
		uD.RealQuota = map[string]int64{"cpu": 55}
		uD.AvgQuota = map[string]int64{"cpu": 44}
		uD.ScheduleQuota = map[string]int64{"cpu": 33}
		uD.AllocQuota = map[string]int64{"cpu": 44}
		uD.TaskNum = 5

		time.Sleep(1 * time.Second)
		err = ro.UpdateHeartbeat(ctx, uD, true)
		Ω(err).ShouldNot(HaveOccurred())

		time.Sleep(10 * time.Millisecond)
		err = ro.UpdateHeartbeat(ctx, uD, true)
		worker, _ := ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		log.Infof("worker:%+v", worker)
		Ω(err).ShouldNot(HaveOccurred())

		worker, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		WorkerHeartbeatCompare(worker, uD)

		// 更新心跳
		uD.HeartbeatTime = worker.HeartbeatTime
	})

	// 更新Quota
	It("UpdateWorkerQuota", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		uD.AllocQuota = map[string]int64{"cpu": 111}
		uD.ScheduleQuota = map[string]int64{"cpu": 222}

		time.Sleep(1 * time.Second)
		// 申请Quota，会更新最后申请资源时间
		err = ro.UpdateQuota(ctx, uD, biz.QuotaOptAdd)
		Ω(err).ShouldNot(HaveOccurred())

		worker, err := ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		WorkerAllocCompare(worker, uD)

		// 更新最后申请时间
		uD.LatestAllocTime = worker.LatestAllocTime
		uD.UpdateQuotaVersion += 1

		time.Sleep(1 * time.Second)
		uD.AllocQuota = map[string]int64{"cpu": 55}
		uD.ScheduleQuota = map[string]int64{"cpu": 66}
		// 释放Quota
		err = ro.UpdateQuota(ctx, uD, biz.QuotaOptSub)
		Ω(err).ShouldNot(HaveOccurred())

		worker, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		WorkerEqual(worker, uD)
	})

	// 更新变化的Quota
	It("UpdateWorkerChangedQuota", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		quota := biz.QuotaSet{"cpu": 35}
		ok, err := ro.UpdateChangeQuota(ctx, uD.WorkerId, quota, biz.DefaultMasterStrategy.Config, biz.QuotaOptAdd)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(ok).Should(BeTrue())

		worker, err := ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())

		Ω(worker.Quota).Should(Equal(uD.Quota))
		Ω(*(worker.AllocQuota.Sub(quota))).Should(Equal(uD.AllocQuota))
		Ω(worker.RealQuota).Should(Equal(uD.RealQuota))
		Ω(worker.AvgQuota).Should(Equal(uD.AvgQuota))
		Ω(*(worker.ScheduleQuota.Add(quota))).Should(Equal(uD.ScheduleQuota))

		ok, err = ro.UpdateChangeQuota(ctx, uD.WorkerId, quota, biz.DefaultMasterStrategy.Config, biz.QuotaOptSub)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(ok).Should(BeTrue())

		worker, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(worker.Quota).Should(Equal(uD.Quota))
		Ω(worker.AllocQuota).Should(Equal(uD.AllocQuota))
		Ω(worker.RealQuota).Should(Equal(uD.RealQuota))
		Ω(worker.AvgQuota).Should(Equal(uD.AvgQuota))
		Ω(worker.ScheduleQuota).Should(Equal(uD.ScheduleQuota))

		config := &biz.StrategyConfig{
			ScheduleResourceType: biz.ScheduleResourceTypeReal,
			LimitRatio:           map[string]int64{"cpu": 100},
			MaxTaskNum:           1000,
			LoadRatio:            100,
			Dominants:            []string{"cpu"},
		}
		ok, err = ro.UpdateChangeQuota(ctx, uD.WorkerId, quota, config, biz.QuotaOptAdd)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(ok).Should(BeTrue())

		worker, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())

		Ω(worker.Quota).Should(Equal(uD.Quota))
		Ω(*(worker.AllocQuota.Sub(quota))).Should(Equal(uD.AllocQuota))
		Ω(*(worker.RealQuota.Sub(quota))).Should(Equal(uD.RealQuota))
		Ω(worker.AvgQuota).Should(Equal(uD.AvgQuota))
		Ω(*(worker.ScheduleQuota.Add(quota))).Should(Equal(uD.ScheduleQuota))

		ok, err = ro.UpdateChangeQuota(ctx, uD.WorkerId, quota, config, biz.QuotaOptSub)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(ok).Should(BeTrue())

		worker, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())

		Ω(worker.Quota).Should(Equal(uD.Quota))
		Ω(worker.AllocQuota).Should(Equal(uD.AllocQuota))
		Ω(*(worker.RealQuota.Sub(quota))).Should(Equal(uD.RealQuota))
		Ω(worker.AvgQuota).Should(Equal(uD.AvgQuota))
		Ω(*(worker.ScheduleQuota.Add(quota))).Should(Equal(uD.ScheduleQuota))

		//worker, err := ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		//Ω(err).ShouldNot(HaveOccurred())
		//WorkerAllocCompare(worker, uD)
		//
		//// 更新最后申请时间
		//uD.LatestAllocTime = worker.LatestAllocTime
		//uD.UpdateQuotaVersion += 1
		//
		//time.Sleep(1 * time.Second)
		//uD.AllocQuota = map[string]int64{"cpu": 55}
		//uD.ScheduleQuota = map[string]int64{"cpu": 66}
		//// 释放Quota
		//err = ro.UpdateQuota(ctx, uD, biz.QuotaOptSub)
		//Ω(err).ShouldNot(HaveOccurred())
		//
		//worker, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		//Ω(err).ShouldNot(HaveOccurred())
		//WorkerEqual(worker, uD)
	})

	// 更新状态
	It("UpdateWorkerStatus", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		err = ro.UpdateStatus(ctx, uD.WorkerId, biz.WorkerStatusOffline)
		Ω(err).ShouldNot(HaveOccurred())

		_, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).Should(HaveOccurred())

		err = ro.UpdateStatus(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		worker, err := ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		WorkerBaseEqual(worker, uD)
	})

	// 更新标签和污点
	It("UpdateWorkerLabel", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		uD.Label = map[string]string{"labelUpdated": "valueUpdated"}
		err = ro.UpdateLabel(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		worker, err := ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		WorkerBaseEqual(worker, uD)
	})

	// 更新标签和污点
	It("UpdateWorkerTaint", func() {
		worker, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())
		worker, _ = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		log.Infof("111=====worker:%+v", biz.JsonToString(worker))

		uD.Taint = []*biz.Taint{{Key: "key_taint_2", Value: "", Effect: "Delete"}}
		err = ro.UpdateTaint(ctx, []string{uD.WorkerId}, nil, uD.Taint)
		Ω(err).ShouldNot(HaveOccurred())
		worker, _ = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		log.Infof("222====worker:%+v", biz.JsonToString(worker))

		uD.Taint = []*biz.Taint{{Key: "taintUpdated2", Value: "valueUpdated2", Effect: "NoSchedule"}}
		err = ro.UpdateTaint(ctx, []string{uD.WorkerId}, nil, uD.Taint)
		Ω(err).ShouldNot(HaveOccurred())

		worker, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		log.Infof("333=====worker:%+v", biz.JsonToString(worker))

		uD.Taint = []*biz.Taint{{Key: "key_taint_1", Value: "value_taint_1", Effect: "NoSchedule"}, {Key: "taintUpdated2", Value: "valueUpdated2", Effect: "NoSchedule"}}
		Ω(err).ShouldNot(HaveOccurred())
		WorkerBaseEqual(worker, uD)
	})

	// 更新tag
	It("UpdateWorkerTag", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		tag := "tagUpdated"
		err = ro.UpdateTagByWorkerIds(ctx, []string{uDList[0].WorkerId, uDList[1].WorkerId}, tag)
		Ω(err).ShouldNot(HaveOccurred())
		uDList[0].Tag = tag
		uDList[1].Tag = tag

		workers, err := ro.ListByConditions(ctx, uDList[0].Product, uDList[0].EngineModel, tag, nil)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(workers)).Should(Equal(2))
		for i, worker := range workers {
			WorkerBaseEqual(worker, uDList[i])
		}

		tag = "tagUpdated-2"
		err = ro.UpdateTagByWorkerIds(ctx, []string{uDList[4].WorkerId, uDList[5].WorkerId}, tag)
		Ω(err).ShouldNot(HaveOccurred())
		uDList[4].Tag = tag
		uDList[5].Tag = tag

		workers, err = ro.ListByConditions(ctx, uDList[4].Product, uDList[4].EngineModel, tag, nil)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(workers)).Should(Equal(2))
		for i, worker := range workers {
			WorkerBaseEqual(worker, uDList[4+i])
		}
	})
})

func WorkerEqual(a *biz.Worker, b *biz.Worker) {
	WorkerBaseEqual(a, b)
	Ω(a.LatestAllocTime.Unix()).Should(Equal(b.LatestAllocTime.Unix()))
	Ω(a.HeartbeatTime.Unix()).Should(Equal(b.HeartbeatTime.Unix()))
}

func WorkerHeartbeatCompare(a *biz.Worker, b *biz.Worker) {
	WorkerBaseEqual(a, b)
	Ω(a.HeartbeatTime.Unix()).Should(BeNumerically(">", b.HeartbeatTime.Unix()))
}

func WorkerAllocCompare(a *biz.Worker, b *biz.Worker) {
	WorkerBaseEqual(a, b)
	Ω(a.LatestAllocTime.Unix()).Should(BeNumerically(">", b.LatestAllocTime.Unix()))
}

type TestTaint []*biz.Taint

func (a TestTaint) Len() int           { return len(a) }
func (a TestTaint) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a TestTaint) Less(i, j int) bool { return strings.Compare(a[i].Key, a[j].Key) >= 0 }

func WorkerBaseEqual(a *biz.Worker, b *biz.Worker) {
	Ω(a.WorkerId).Should(Equal(b.WorkerId))
	Ω(a.Ip).Should(Equal(b.Ip))
	Ω(a.PodName).Should(Equal(b.PodName))
	Ω(a.NodeName).Should(Equal(b.NodeName))
	Ω(a.Port).Should(Equal(b.Port))
	Ω(a.Product).Should(Equal(b.Product))
	Ω(a.EngineModel).Should(Equal(b.EngineModel))
	Ω(a.Tag).Should(Equal(b.Tag))
	Ω(a.Load).Should(Equal(b.Load))
	Ω(a.OnlineVersion).Should(BeNumerically(">=", b.OnlineVersion))
	Ω(a.Quota).Should(Equal(b.Quota))
	Ω(a.AllocQuota).Should(Equal(b.AllocQuota))
	Ω(a.RealQuota).Should(Equal(b.RealQuota))
	Ω(a.AvgQuota).Should(Equal(b.AvgQuota))
	Ω(a.ScheduleQuota).Should(Equal(b.ScheduleQuota))
	Ω(a.Label).Should(Equal(b.Label))
	sort.Sort(TestTaint(a.Taint))
	sort.Sort(TestTaint(b.Taint))
	Ω(a.Taint).Should(Equal(b.Taint))
	Ω(a.Extend).Should(Equal(b.Extend))
}
