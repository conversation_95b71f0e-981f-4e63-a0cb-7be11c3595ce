package data_test

import (
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"github.com/go-kratos/kratos/v2/log"

	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/data"
)

var _ = Describe("LeaderElect", func() {
	var leaders []biz.LeaderRepo
	//var uD *biz.Leader
	//var uDList []*biz.LeaderRepo
	BeforeEach(func() {
		// 这里的 Db 是 data_suite_test.go 文件里面定义的
		leaders = []biz.LeaderRepo{
			data.NewDBLeaderRepo2(Db, instance1, log.GetLogger()),
			data.NewDBLeaderRepo2(Db, instance2, log.GetLogger()),
			data.NewDBLeaderRepo2(Db, instance3, log.GetLogger()),
		}

		// 这里你可以引入外部组装好的数据
		//uD = testdata.Leader()

	})

	// 测试3个worker，1个leader，2个slave
	It("LeaderElect", func() {
		leaders[0].Watch(key, tesLeader1.OnEvent)
		time.Sleep(1 * time.Second)
		Ω(tesLeader1.Leader).Should(Equal(instance1))
		leaders[1].Watch(key, tesLeader2.OnEvent)
		time.Sleep(1 * time.Second)
		Ω(tesLeader2.Leader).Should(Equal(instance1))
		leaders[2].Watch(key, tesLeader3.OnEvent)
		time.Sleep(1 * time.Second)
		Ω(tesLeader2.Leader).Should(Equal(instance1))
	})

	// Leader超时，一个slave抢leader成功，另外一个slave抢leader失败
	It("LeaderLost", func() {

	})

	// Leader超时恢复，发现自己已经不是leader,重新抢成功，不通知leader变化。
	It("LeaderLost", func() {

	})

	// Leader超时恢复，发现自己已经不是leader,重新抢失败，通知leader变化。
	It("LeaderLost", func() {

	})
})

type TestLeader struct {
	Instance string
	Leader   string
}

var instance1 = "127.0.0.1"
var instance2 = "127.0.0.2"
var instance3 = "127.0.0.3"

var key = "TaskSchedule"

var tesLeader1 = TestLeader{Instance: instance1}
var tesLeader2 = TestLeader{Instance: instance2}
var tesLeader3 = TestLeader{Instance: instance3}

func (t *TestLeader) OnEvent(event int8, instance string) {
	t.Leader = instance
	log.Infof("t:%v event:%v, leader:%v.", t.Instance, event, instance)
	switch event {
	case biz.EventLeaderSuccess:
		// leader成功
		log.Infof("leader success.")
	case biz.EventLeaderLost:
		// leader 丢失
		log.Infof("leader lost.")
	case biz.EventLeaderChanged:
		// leader变化
		log.Infof("leader changed.")
	default:
		// 其他
		log.Warnf("unknow event:%v.", event)
	}
}
