package data

import (
	"context"
	"mpp/internal/taskscheduler/biz"
	"mpp/pkg/utils"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"google.golang.org/grpc"
	pb "proto.mpp/api/taskscheduler/v1"
)

type taskScheduleMasterClient struct {
	endpoint   string
	client     pb.ScheduleClient
	connection *grpc.ClientConn

	data *Data
	log  *log.Helper
}

func NewTaskScheduleMasterClient(data *Data, logger log.Logger) biz.TaskScheduleMasterApi {
	client := &taskScheduleMasterClient{
		data: data,
		log:  log.NewHelper(logger),
	}
	return client
}

func (c *taskScheduleMasterClient) InitClient(ctx context.Context, endpoint string) error {
	c.endpoint = endpoint
	// 设置连接选项
	opts := []grpc.DialOption{
		grpc.WithInsecure(),
		grpc.WithTimeout(1 * time.Second),
		//grpc.WithBlock(),
	}

	conn, err := grpc.DialContext(context.Background(), c.endpoint, opts...)
	if err != nil {
		log.Warnf("connect to master failed. endpoint:%v err:%v", c.endpoint, err)
		return err
	}
	//defer conn.Close()
	c.connection = conn
	// 创建 gRPC 客户端
	c.client = pb.NewScheduleClient(conn)

	log.Infof("connect to master success. endpoint:%v ", c.endpoint)
	return nil
}

func (c *taskScheduleMasterClient) ResetClient(ctx context.Context, endpoint string) error {
	log.Infof("OnLeaderLost called. old:%v new:%v", c.endpoint, endpoint)

	if c.connection != nil {
		err := c.connection.Close()
		c.connection = nil
		if err != nil {
			log.Warnf("close leader connection failed. err:%v", err)
		} else {
			log.Infof("close leader connection success. endpoint:%v ", c.endpoint)
		}
	}

	err := c.InitClient(ctx, endpoint)
	if err != nil {
		log.Warnf("reset leader connection failed. endpoint:%v err:%v", c.endpoint, err)
		return err
	}

	return nil
}

func (c *taskScheduleMasterClient) FreeMasterResource(ctx context.Context, taskId string) (*biz.Worker, error) {
	c.log.WithContext(ctx).Infof("send free request to master taskId:%v", taskId)

	if c.connection == nil {
		return nil, biz.ErrMasterError
	}

	// 发送请求并接收响应
	req := pb.FreeMasterResourceRequest{
		TaskId:    taskId,
		RequestId: utils.GetRequestIdFromContext(ctx),
	}

	res, err := c.client.FreeMasterResource(ctx, &req)
	if err != nil {
		c.log.WithContext(ctx).Warnf("free from master failed. task:%v reply:%v err:%v", biz.JsonToString(res), taskId, err)
		return nil, err
	} else {
		c.log.WithContext(ctx).Infof("free from master success. reply:%v", biz.JsonToString(res))

		var worker biz.Worker
		err := copier.Copy(&worker, res.Data.Worker)
		if err != nil {
			c.log.WithContext(ctx).Warnf("copy alloc response failed. err:%v", err)
			return nil, err
		} else {
			return &worker, nil
		}
	}
}

func (c *taskScheduleMasterClient) AllocMasterResource(ctx context.Context, req *pb.AllocResourceRequest) (*biz.Worker, error) {
	c.log.WithContext(ctx).Infof("alloc request from master req:%+v", req)

	if c.connection == nil {
		return nil, biz.ErrMasterError
	}

	// 发送请求并接收响应
	var masterReq pb.AllocMasterResourceRequest
	err := copier.Copy(&masterReq, req)
	if err != nil {
		c.log.WithContext(ctx).Warnf("copy alloc request failed. err:%v", err)
		return nil, err
	}

	masterReq.RequestId = utils.GetRequestIdFromContext(ctx)

	res, err := c.client.AllocMasterResource(ctx, &masterReq)
	if err != nil {
		c.log.WithContext(ctx).Warnf("alloc from master failed. reply:%v err:%v", biz.JsonToString(res), err)
		return nil, err
	} else {
		c.log.WithContext(ctx).Infof("alloc from master success. reply:%v", biz.JsonToString(res))

		var worker biz.Worker
		err := copier.Copy(&worker, res.Data.Worker)
		if err != nil {
			c.log.WithContext(ctx).Warnf("copy alloc response failed. err:%v", err)
			return nil, err
		} else {
			return &worker, nil
		}
	}
}

func (c *taskScheduleMasterClient) GetMasterWorker(ctx context.Context, workerId string) (*biz.Worker, error) {
	c.log.WithContext(ctx).Infof("get master worker request workerId:%v", workerId)

	if c.connection == nil {
		return nil, biz.ErrMasterError
	}

	// 发送请求并接收响应
	req := pb.GetMasterWorkerRequest{
		WorkerId:  workerId,
		RequestId: utils.GetRequestIdFromContext(ctx),
	}

	res, err := c.client.GetMasterWorker(ctx, &req)
	if err != nil {
		c.log.WithContext(ctx).Warnf("get worker from master failed. reply:%v err:%v", biz.JsonToString(res), err)
		return nil, err
	} else {
		c.log.WithContext(ctx).Infof("get worker from master success. reply:%v", biz.JsonToString(res))
		if nil == res || res.Data == nil || res.Data.Worker == nil {
			return nil, nil
		}

		var worker biz.Worker
		err := copier.Copy(&worker, res.Data.Worker)
		if err != nil {
			c.log.WithContext(ctx).Warnf("copy get master worker response failed. err:%v", err)
			return nil, err
		} else {
			return &worker, nil
		}
	}
}

func (c *taskScheduleMasterClient) GetEndpoint() string {
	return c.endpoint
}
