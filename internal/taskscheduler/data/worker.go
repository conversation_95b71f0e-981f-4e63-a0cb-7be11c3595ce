package data

import (
	"context"
	"encoding/json"
	"fmt"
	"gorm.io/hints"
	"mpp/internal/taskscheduler/pkg/http"
	"time"

	"mpp/internal/taskscheduler/biz"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"

	v1 "proto.mpp/api/workeragent/v1"
)

type WorkerTaint map[string]*biz.Taint

func toWorkerTaint(taints []*biz.Taint) string {
	workerTaint := make(WorkerTaint)
	for _, taint := range taints {
		workerTaint[taint.Key] = taint
	}

	str := biz.JsonToString(workerTaint)
	return str
}

func toBizTaint(wt string) []*biz.Taint {
	var taintMap WorkerTaint
	err := json.Unmarshal([]byte(wt), &taintMap)
	if err != nil {
		return nil
	}

	var taints []*biz.Taint

	for _, taint := range taintMap {
		taints = append(taints, taint)
	}

	return taints
}

type Worker struct {
	DBBaseModel
	WorkerId           string       `gorm:"column:worker_id;uniqueIndex;type:varchar(64);NOT NULL;COMMENT:唯一ID"`
	PodName            string       `gorm:"column:pod_name;type:varchar(128);COMMENT:worker pod 名称"`
	NodeName           string       `gorm:"column:node_name;type:varchar(128);COMMENT:worker node 名称"`
	Ip                 string       `gorm:"type:varchar(64);NOT NULL;index:idx_ip;COMMENT:IP地址"`
	Port               int32        `gorm:"DEFAULT:80;COMMENT:worker监听端口"`
	Product            string       `gorm:"type:varchar(32);NOT NULL;COMMENT:产品"`
	EngineModel        string       `gorm:"column:engine_model;type:varchar(32);NOT NULL;COMMENT:引擎模型"`
	Tag                string       `gorm:"type:varchar(32);NOT NULL;COMMENT:资源组标签"`
	HeartBeatTime      time.Time    `gorm:"column:gmt_heartbeat;type:datetime;NOT NULL;COMMENT:最近心跳时间"`
	Status             int8         `gorm:"default:1;index:idx_status;COMMENT:状态：0-不在线；1-在线"`
	OnlineVersion      int32        `gorm:"column:online_version;DEFAULT:0;COMMENT:每次上线加1"`
	UpdateQuotaVersion uint64       `gorm:"column:update_quota_version;DEFAULT:0;COMMENT:每次更新加1"`
	Quota              biz.QuotaSet `gorm:"column:quota;type:json;NOT NULL;COMMENT:多维度quota上限值"`
	RealQuota          biz.QuotaSet `gorm:"column:real_quota;type:json;COMMENT:多维度quota实际值"`
	AllocQuota         biz.QuotaSet `gorm:"column:alloc_quota;type:json;COMMENT:多维度quot申请值"`
	AvgQuota           biz.QuotaSet `gorm:"column:avg_quota;type:json;COMMENT:多维度quot平均值"`
	ScheduleQuota      biz.QuotaSet `gorm:"column:schedule_quota;type:json;COMMENT:多维度quot调度值"`
	ScheduleConfig     string       `gorm:"column:schedule_config;type:json;COMMENT:调度配置"`
	Label              MAPSTRING    `gorm:"type:json;COMMENT:标签组"`
	WorkerTaint        string       `gorm:"column:taint;type:json;COMMENT:污点组"`
	Load               float32      `gorm:"type:decimal(10,2);column:load;COMMENT:worker负载"`
	LatestAllocTime    time.Time    `gorm:"column:gmt_latest_alloc;type:datetime;NOT NULL;DEFAULT:CURRENT_TIMESTAMP"`
	ConnectMode        LISTSTRING   `gorm:"column:connect_mode;type:json;COMMENT:访问worker模式连接模式，direct:直连，gateway:网关代理"`
	TaskNum            int32        `gorm:"column:task_num;DEFAULT:0;COMMENT:worker任务数"`
	Extend             MAPOBJECT    `gorm:"type:json;COMMENT:扩展字段"`
	MetaData           MAPSTRING    `gorm:"column:metadata;type:json;COMMENT:metadata"`
}

type workerRepo struct {
	data *Data
	log  *log.Helper
}

type workerApiHttp struct {
	data *Data
	log  *log.Helper
}

func (w *workerApiHttp) CheckOnline(ctx context.Context, worker *biz.Worker) (bool, interface{}) {
	if worker.Product == "ols" {
		if worker.EngineModel == "livehls" || worker.EngineModel == "liverecord" || worker.EngineModel == "transpackager" {
			return w.checkLiveTaskManagerWorker(ctx, worker)
		} else if worker.EngineModel == "livetranscode" {
			return w.checkLiveTranscodeWorker(ctx, worker)
		} else {
			return false, nil
		}
	} else {
		return w.checkMppWorker(ctx, worker)
	}
}

func (w *workerApiHttp) checkLiveTranscodeWorker(ctx context.Context, worker *biz.Worker) (bool, interface{}) {
	connHTTP, err := http.InitHttpConnection(ctx, fmt.Sprintf("%v:%v", worker.Ip, worker.Port))
	if err != nil {
		w.log.WithContext(ctx).Errorf("checkOnline worker:%v init connection failed:%v ", worker.Ip, err)
		return false, err
	}
	defer connHTTP.Close()

	reply := make(map[string]interface{})
	err = connHTTP.Invoke(ctx, "GET", "/", nil, &reply)
	if err != nil {
		e := errors.FromError(err)
		if e.Code < 500 {
			return true, nil
		}

		w.log.WithContext(ctx).Errorf("worker:%v check online failed. resp:%v err:%v", worker.Ip, nil, err)
		return false, err
	}
	return true, nil
}

func (w *workerApiHttp) checkLiveTaskManagerWorker(ctx context.Context, worker *biz.Worker) (bool, interface{}) {
	connHTTP, err := http.InitHttpConnection(ctx, fmt.Sprintf("%v:%v", worker.Ip, worker.Port))
	if err != nil {
		w.log.WithContext(ctx).Errorf("checkOnline worker:%v init connection failed:%v ", worker.Ip, err)
		return false, err
	}
	defer connHTTP.Close()

	path := fmt.Sprintf("/checkAlive?id=%v", worker.WorkerId)
	reply := make(map[string]interface{})
	err = connHTTP.Invoke(ctx, "GET", path, nil, &reply)
	if err != nil {
		e := errors.FromError(err)
		if e.Code < 500 {
			return true, nil
		}

		w.log.WithContext(ctx).Errorf("worker:%v check online failed. resp:%v err:%v", worker.Ip, nil, err)
		return false, err
	}
	return true, nil
}

func (w *workerApiHttp) checkMppWorker(ctx context.Context, worker *biz.Worker) (bool, interface{}) {
	connHTTP, err := http.InitHttpConnection(ctx, fmt.Sprintf("%v:%v", worker.Ip, worker.Port))
	if err != nil {
		w.log.WithContext(ctx).Errorf("checkOnline worker:%v init connection failed:%v ", worker.Ip, err)
		return false, err
	}
	defer connHTTP.Close()

	client := v1.NewAgentHTTPClient(connHTTP)
	if err != nil {
		w.log.WithContext(ctx).Errorf("checkOnline worker:%v new client failed:%v ", worker.Ip, err)
		return false, err
	}

	req := &v1.CheckOnlineRequest{
		Metadata: &v1.Metadata{
			WorkerId: worker.WorkerId,
			Ext:      nil,
		},
	}

	resp, err := client.CheckOnline(ctx, req)
	if err != nil {
		error := errors.FromError(err)
		if error.Code < 500 {
			return true, resp
		}

		w.log.WithContext(ctx).Errorf("worker:%v check online failed. resp:%v err:%v", worker.Ip, resp, err)
		return false, resp
	}
	w.log.WithContext(ctx).Debugf("worker:%v check online success. %v", worker.Ip, resp)

	return true, resp
}

func NewWorkerApi(data *Data, logger log.Logger) biz.WorkerApi {
	wa := &workerApiHttp{
		data: data,
		log:  log.NewHelper(logger),
	}
	return wa
}

func NewWorkerRepo(data *Data, logger log.Logger) biz.WorkerRepo {
	wr := &workerRepo{
		data: data,
		log:  log.NewHelper(logger),
	}

	return wr
}

func (r *workerRepo) Save(ctx context.Context, w *biz.Worker) (*biz.Worker, error) {
	result := r.data.db.WithContext(ctx).Exec(
		"INSERT INTO workers(gmt_create, gmt_modified, worker_id, pod_name, node_name, ip, port, product, engine_model, tag, gmt_heartbeat, status, online_version, update_quota_version, quota, alloc_quota, avg_quota, real_quota, schedule_quota, schedule_config, gmt_latest_alloc, label, taint, `load`, connect_mode, extend, metadata, task_num) "+
			"VALUES(now(), now(), ?, ?, ?, ?, ?, ?, ?, ?, now(), ?, 0, ?, ?, ?, ?, ?, ?, ?, now(), ?, ?, ?, ?, ?, ?, 0) "+
			"ON DUPLICATE KEY UPDATE pod_name = ?, node_name = ?, ip = ?, port = ?, product = ?, engine_model = ?, tag = ?, quota = ?, alloc_quota = ?, avg_quota = ?, real_quota = ?, schedule_quota = ?, schedule_config = ?, status = ?, update_quota_version = ?, label = ?, taint = ?, `load` = ?, connect_mode = ?, extend = ?, metadata = ?, online_version = online_version+1, gmt_heartbeat = now(), gmt_modified = now(), task_num = 0",
		w.WorkerId, w.PodName, w.NodeName, w.Ip, w.Port, w.Product, w.EngineModel, w.Tag, biz.WorkerStatusOnline, w.UpdateQuotaVersion, w.Quota, w.AllocQuota, w.AvgQuota, w.RealQuota, w.ScheduleQuota, biz.JsonToString(w.ScheduleConfig), MAPSTRING(w.Label), toWorkerTaint(w.Taint), w.Load, biz.JsonToString(w.ConnectMode), MAPOBJECT(w.Extend), MAPSTRING(w.Metadata),
		w.PodName, w.NodeName, w.Ip, w.Port, w.Product, w.EngineModel, w.Tag, w.Quota, w.AllocQuota, w.AvgQuota, w.RealQuota, w.ScheduleQuota, biz.JsonToString(w.ScheduleConfig), biz.WorkerStatusOnline, w.UpdateQuotaVersion, MAPSTRING(w.Label), toWorkerTaint(w.Taint), w.Load, biz.JsonToString(w.ConnectMode), MAPOBJECT(w.Extend), MAPSTRING(w.Metadata),
	)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("save worker:%v failed, err:%v", biz.JsonToString(w), result.Error)
		return nil, result.Error
	}

	return w, nil
}

func (r *workerRepo) UpdateHeartbeat(ctx context.Context, w *biz.Worker, isUpdateSchedule bool) error {
	data := map[string]interface{}{
		"Load":               w.Load,
		"RealQuota":          w.RealQuota,
		"AvgQuota":           w.AvgQuota,
		"ModifiedTime":       gorm.Expr("CURRENT_TIMESTAMP"),
		"HeartBeatTime":      gorm.Expr("CURRENT_TIMESTAMP"),
		"UpdateQuotaVersion": gorm.Expr("update_quota_version + 1"),
	}

	if isUpdateSchedule {
		data["ScheduleQuota"] = w.ScheduleQuota
		data["AllocQuota"] = w.AllocQuota
		data["TaskNum"] = w.TaskNum
		data["LatestAllocTime"] = gorm.Expr("CURRENT_TIMESTAMP")
	}
	if w.Metadata != nil && len(w.Metadata) > 0 {
		data["Metadata"] = MAPSTRING(w.Metadata)
	}

	// 增加、删除，嵌套
	r.appendTaint(w.Taint, data)

	result := r.data.db.WithContext(ctx).Model(&Worker{}).Clauses(hints.UseIndex("idx_workers_worker_id")).Where("worker_id = ? AND status = ?", w.WorkerId, biz.WorkerStatusOnline).Updates(
		data)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateHeartbeat worker:%v failed, result:%v", biz.JsonToString(w), result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("update heartbeat worker:%v failed, affected rows is 0", biz.JsonToString(w))
		return biz.ErrNoRegister
	}

	return nil
}

func (r *workerRepo) UpdateChangeQuota(ctx context.Context, workerId string, quota biz.QuotaSet, scheduleConfig *biz.StrategyConfig, opt string) (bool, error) {
	dominant := scheduleConfig.Dominants[0]
	var result *gorm.DB
	if scheduleConfig.ScheduleResourceType == biz.ScheduleResourceTypeReal {
		if opt == biz.QuotaOptAdd {
			sql := fmt.Sprintf("UPDATE workers SET task_num=task_num+1, alloc_quota = JSON_SET(alloc_quota, '$.%v', CAST(alloc_quota->>'$.%v' AS SIGNED)+?), real_quota = JSON_SET(real_quota, '$.%v', CAST(real_quota->>'$.%v' AS SIGNED)+?), schedule_quota = JSON_SET(schedule_quota, '$.%v', CAST(schedule_quota->>'$.%v' AS SIGNED)-?), update_quota_version = update_quota_version + 1, gmt_modified = now(), gmt_latest_alloc = now() WHERE worker_id = ? AND status = ? AND quota->>'$.%v'*?/100 - alloc_quota->>'$.%v' >= ?",
				dominant, dominant, dominant, dominant, dominant, dominant, dominant, dominant)
			result = r.data.db.Clauses(hints.UseIndex("idx_workers_worker_id")).Exec(
				sql, quota[dominant], quota[dominant], quota[dominant], workerId, biz.WorkerStatusOnline, scheduleConfig.LimitRatio[dominant], quota[dominant],
			)
		} else {
			sql := fmt.Sprintf("UPDATE workers SET task_num=task_num-1, alloc_quota = JSON_SET(alloc_quota, '$.%v', CAST(alloc_quota->>'$.%v' AS SIGNED)-?), update_quota_version = update_quota_version + 1, gmt_modified = now(), gmt_latest_alloc = now() WHERE worker_id = ? AND status = ?",
				dominant, dominant)
			result = r.data.db.Clauses(hints.UseIndex("idx_workers_worker_id")).Exec(
				sql, quota[dominant], workerId, biz.WorkerStatusOnline,
			)
		}

	} else {
		if opt == biz.QuotaOptAdd {
			sql := fmt.Sprintf("UPDATE workers SET task_num=task_num+1, alloc_quota = JSON_SET(alloc_quota, '$.%v', CAST(alloc_quota->>'$.%v' AS SIGNED)+?), schedule_quota = JSON_SET(schedule_quota, '$.%v', CAST(schedule_quota->>'$.%v' AS SIGNED)-?), update_quota_version = update_quota_version + 1, gmt_modified = now(), gmt_latest_alloc = now() WHERE worker_id = ? AND status = ? AND quota->>'$.%v'*?/100 - alloc_quota->>'$.%v' >= ?",
				dominant, dominant, dominant, dominant, dominant, dominant)
			result = r.data.db.Clauses(hints.UseIndex("idx_workers_worker_id")).Exec(
				sql, quota[dominant], quota[dominant], workerId, biz.WorkerStatusOnline, scheduleConfig.LimitRatio[dominant], quota[dominant],
			)
		} else {
			sql := fmt.Sprintf("UPDATE workers SET task_num=task_num-1, alloc_quota = JSON_SET(alloc_quota, '$.%v', CAST(alloc_quota->>'$.%v' AS SIGNED)-?), schedule_quota = JSON_SET(schedule_quota, '$.%v', CAST(schedule_quota->>'$.%v' AS SIGNED)+?), update_quota_version = update_quota_version + 1, gmt_modified = now(), gmt_latest_alloc = now() WHERE worker_id = ? AND status = ?",
				dominant, dominant, dominant, dominant)
			result = r.data.db.Clauses(hints.UseIndex("idx_workers_worker_id")).Exec(
				sql, quota[dominant], quota[dominant], workerId, biz.WorkerStatusOnline,
			)
		}

	}

	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateChangeQuota worker:%v failed, result:%v", workerId, result)
		return false, result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("UpdateChangeQuota worker:%v failed, affected rows is 0, result:%v", workerId, result)
		return false, nil
	}

	return true, nil
}

func (r *workerRepo) UpdateQuota(ctx context.Context, w *biz.Worker, opt string) error {
	data := map[string]interface{}{
		"AllocQuota":         w.AllocQuota,
		"ScheduleQuota":      w.ScheduleQuota,
		"ModifiedTime":       gorm.Expr("CURRENT_TIMESTAMP"),
		"UpdateQuotaVersion": gorm.Expr("update_quota_version + 1"),
		"TaskNum":            w.TaskNum,
	}

	// 申请资源，更新最后申请时间
	if opt == biz.QuotaOptAdd {
		data["LatestAllocTime"] = gorm.Expr("CURRENT_TIMESTAMP")
	}

	result := r.data.db.WithContext(ctx).Model(&Worker{}).Clauses(hints.UseIndex("idx_workers_worker_id")).Where("worker_id = ? AND status = ?", w.WorkerId, biz.WorkerStatusOnline).Updates(data)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateQuota worker:%v failed, result:%v", biz.JsonToString(w), result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("update quota worker:%v failed, affected rows is 0", biz.JsonToString(w))
		return nil
	}

	return nil
}

func (r *workerRepo) BatchUpdateStatus(ctx context.Context, workerIds []string, status int8) error {
	data := map[string]interface{}{
		"Status":       status,
		"ModifiedTime": gorm.Expr("CURRENT_TIMESTAMP"),
	}

	result := r.data.db.WithContext(ctx).Model(&Worker{}).Where("worker_id in ?", workerIds).Updates(data)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("BatchUpdateStatus worker:%v failed, result:%v", workerIds, result)
		return result.Error
	}

	if result.RowsAffected != int64(len(workerIds)) {
		r.log.WithContext(ctx).Warnf("batch update status no equal. workerIds:%v status:%v workerIds len:%v affected rows is %v", workerIds,
			status, len(workerIds), result.RowsAffected)
		return nil
	}

	return nil
}

func (r *workerRepo) UpdateStatus(ctx context.Context, workerId string, status int8) error {
	data := map[string]interface{}{
		"Status":             status,
		"UpdateQuotaVersion": gorm.Expr("update_quota_version + 1"),
		"ModifiedTime":       gorm.Expr("CURRENT_TIMESTAMP"),
	}

	result := r.data.db.WithContext(ctx).Model(&Worker{}).Clauses(hints.UseIndex("idx_workers_worker_id")).Where("worker_id = ?", workerId).Updates(data)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateStatus worker:%v failed, result:%v", workerId, result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("update status worker:%v status:%v failed, affected rows is 0", workerId, status)
		return biz.ErrWorkerNotFound
	}

	return nil
}

func (r *workerRepo) UpdateLabel(ctx context.Context, w *biz.Worker) error {
	data := map[string]interface{}{
		"Label":              MAPSTRING(w.Label),
		"UpdateQuotaVersion": gorm.Expr("update_quota_version + 1"),
		"ModifiedTime":       gorm.Expr("CURRENT_TIMESTAMP"),
	}

	result := r.data.db.WithContext(ctx).Model(&Worker{}).Clauses(hints.UseIndex("idx_workers_worker_id")).Where("worker_id = ? AND status = ?", w.WorkerId, biz.WorkerStatusOnline).Updates(data)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateLabel worker:%v failed, result:%v", biz.JsonToString(w), result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("update label worker:%v failed, affected rows is 0", biz.JsonToString(w))
		return nil
	}

	return nil
}

func (r *workerRepo) UpdateTaint(ctx context.Context, workerIds []string, workerIps []string, taint []*biz.Taint) error {
	query := map[string]interface{}{}
	if workerIds != nil {
		query["worker_id"] = workerIds
	} else {
		query["ip"] = workerIps
	}

	data := map[string]interface{}{
		"ModifiedTime":       gorm.Expr("CURRENT_TIMESTAMP"),
		"UpdateQuotaVersion": gorm.Expr("update_quota_version + 1"),
	}

	// 增加、删除，嵌套
	r.appendTaint(taint, data)

	result := r.data.db.WithContext(ctx).Model(&Worker{}).Where(query).Updates(data)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateTaint workerIds:%v workerIps:%v taint:%v failed, result:%v",
			workerIds, workerIps, taint, result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("UpdateTaint taint workerIds:%v workerIps:%v taint:%v failed, affected rows is 0",
			workerIds, workerIps, taint)
		return biz.ErrWorkerNotFound
	}

	return nil
}

func (r *workerRepo) appendTaint(taint []*biz.Taint, data map[string]interface{}) {
	if taint != nil && len(taint) > 0 {
		exp := "taint"
		for _, taint := range taint {
			if taint.Effect == "Delete" {
				exp = fmt.Sprintf("JSON_REMOVE(%s, '$.\"%s\"')", exp, taint.Key)
			} else {
				exp = fmt.Sprintf("JSON_SET(%s, '$.\"%s\"', CAST('%s' AS JSON))", exp, taint.Key, biz.JsonToString(taint))
			}
		}
		data["WorkerTaint"] = gorm.Expr(exp)
	}
}

func (r *workerRepo) UpdateTagByIps(ctx context.Context, ips []string, tag string) error {
	result := r.data.db.WithContext(ctx).Exec("UPDATE workers SET tag = ?, gmt_modified = now() where status = ? AND ip IN ?", tag,
		biz.WorkerStatusOnline, ips)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateTagByIps ips:%v tag:%v failed, result:%v", ips, tag, result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("update tag by ips:%v tag:%v failed, affected rows is 0", ips, tag)
		return nil
	}

	return nil
}

func (r *workerRepo) UpdateTagByWorkerIds(ctx context.Context, workerIds []string, tag string) error {
	result := r.data.db.WithContext(ctx).Exec("UPDATE workers SET tag = ?, gmt_modified = now() where status = ? AND worker_id IN ?", tag,
		biz.WorkerStatusOnline, workerIds)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateTagByWorkerIds workerIds:%v tag:%v failed, result:%v", workerIds, tag, result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("update tag by workerIds:%v tag:%v failed, affected rows is 0", workerIds, tag)
		return nil
	}

	return nil
}

func (r *workerRepo) FindByWorkerId(ctx context.Context, workerId string, status int8) (*biz.Worker, error) {
	var worker Worker
	result := r.data.db.WithContext(ctx).Clauses(hints.UseIndex("idx_workers_worker_id")).Where("worker_id = ? AND status = ?", workerId, status).Take(&worker)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("FindByWorkerId workerId:%v failed, result:%v", workerId, result)
		if result.Error == gorm.ErrRecordNotFound {
			return nil, biz.ErrWorkerNotFound
		} else {
			return nil, result.Error
		}
	}

	w, err := r.coverDbWorkerToBizWorker(ctx, &worker)
	if err != nil {
		return nil, err
	}

	return w, nil
}

func (r *workerRepo) coverDbWorkerToBizWorker(ctx context.Context, worker *Worker) (*biz.Worker, error) {
	w := biz.Worker{}
	err := copier.Copy(&w, worker)
	if nil != err {
		r.log.WithContext(ctx).Warnf("FindByWorkerId worker:%v failed, err:%v", biz.JsonToString(w), err)
		return nil, err
	}

	w.Taint = toBizTaint(worker.WorkerTaint)
	return &w, nil
}

func (r *workerRepo) FindByIp(ctx context.Context, ip string) (*biz.Worker, error) {
	var worker Worker
	result := r.data.db.WithContext(ctx).Where("status = ? AND ip = ?", biz.WorkerStatusOnline, ip).Take(&worker)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("FindByIp ip:%v failed, result:%v", ip, result)
		return nil, result.Error
	}

	w, err := r.coverDbWorkerToBizWorker(ctx, &worker)
	if err != nil {
		return nil, err
	}

	return w, nil
}

func (r *workerRepo) ListOnlineWorker(ctx context.Context) ([]*biz.Worker, error) {
	var ws []Worker
	result := r.data.db.Where("status = ?", biz.WorkerStatusOnline).Find(&ws)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("ListAll failed, result:%v", result)
		return nil, result.Error
	}

	var workers []*biz.Worker

	for _, w := range ws {
		worker, err := r.coverDbWorkerToBizWorker(ctx, &w)
		if err != nil {
			return nil, err
		}

		workers = append(workers, worker)
	}
	return workers, nil
}

func (r *workerRepo) ListByConditions(ctx context.Context, product string, engineModel string, tag string, ips []string) ([]*biz.Worker, error) {
	var ws []Worker

	filters := map[string]interface{}{}
	filters["status"] = biz.WorkerStatusOnline
	if product != "" {
		filters["product"] = product
	}

	if engineModel != "" {
		filters["engine_model"] = engineModel
	}

	if tag != "" {
		filters["tag"] = tag
	}

	result := r.data.db.WithContext(ctx).Where(filters)
	if ips != nil && len(ips) > 0 {
		result = result.Where("ip IN ?", ips)
	}

	result = result.Find(&ws)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("ListByConditions failed, product:%v engineModel:%v tag:%v ips:%v result:%v",
			product, engineModel, tag, ips, result)
		return nil, result.Error
	}

	var workers []*biz.Worker

	for _, w := range ws {
		worker, err := r.coverDbWorkerToBizWorker(ctx, &w)
		if err != nil {
			return nil, err
		}
		workers = append(workers, worker)
	}

	return workers, nil
}

func (r *workerRepo) ListByWorkerIds(ctx context.Context, workerIds []string) ([]*biz.Worker, error) {
	var ws []Worker
	result := r.data.db.WithContext(ctx).Where("status = ? and worker_id IN ?", biz.WorkerStatusOnline, workerIds).Find(&ws)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("ListByWorkerIds failed, workerIds:%v result:%v", workerIds, result)
		return nil, result.Error
	}

	var workers []*biz.Worker

	for _, w := range ws {
		worker, err := r.coverDbWorkerToBizWorker(ctx, &w)
		if err != nil {
			return nil, err
		}
		workers = append(workers, worker)
	}
	return workers, nil
}

func (r *workerRepo) ListByIps(ctx context.Context, ips []string) ([]*biz.Worker, error) {
	var ws []Worker
	result := r.data.db.WithContext(ctx).Where("status = ? and ip IN ?", biz.WorkerStatusOnline, ips).Find(&ws)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("ListByIps failed, ips:%v result:%v", ips, result)
		return nil, result.Error
	}

	var workers []*biz.Worker

	for _, w := range ws {
		worker, err := r.coverDbWorkerToBizWorker(ctx, &w)
		if err != nil {
			return nil, err
		}
		workers = append(workers, worker)
	}
	return workers, nil
}
