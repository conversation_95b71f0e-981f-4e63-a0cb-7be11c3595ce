package data_test

import (
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/data"
	"mpp/internal/taskscheduler/testdata"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("KvStore", func() {
	var ro biz.KVStoreRepo
	var uD *biz.KVStore
	var uDList []*biz.KVStore
	BeforeEach(func() {
		// 这里的 Db 是 data_suite_test.go 文件里面定义的
		ro = data.NewDBKVStore(Db, log.GetLogger())
		// 这里你可以引入外部组装好的数据
		uD = testdata.KVStore("key", "value")

		uDList = testdata.KVStoreList(10)
	})

	// 读写kv
	It("KVStoreSetGet", func() {
		err := ro.Set(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())
		value, err := ro.Get(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(value).Should(Equal(uD.Value))
	})

	// 写->超时->读失败
	It("KVStoreSetExpiredGet", func() {
		err := ro.Set(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		// 等待两秒超时
		time.Sleep(3 * time.Second)

		// 读失败
		_, err = ro.Get(ctx, uD)
		Ω(err).Should(HaveOccurred())

		kvs, err := ro.ListAll(ctx)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(kvs)).Should(Equal(0))
	})

	// 批量操作
	It("KVStoreBatchOp", func() {
		uDList = testdata.KVStoreList(10)
		for _, kv := range uDList {
			err := ro.Set(ctx, kv)
			Ω(err).ShouldNot(HaveOccurred())
		}
		kvs, err := ro.ListAll(ctx)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(kvs)).Should(Equal(len(uDList)))
		for i, kv := range kvs {
			KvStoreEqual(kv, uDList[i])
		}

		time.Sleep(3 * time.Second)

		kvs, err = ro.ListAll(ctx)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(kvs)).Should(Equal(len(uDList) / 2))
		for i, kv := range kvs {
			KvStoreEqual(kv, uDList[5+i])
		}
	})

	// 写/删除kv
	It("KVStoreSetDelete", func() {
		err := ro.Set(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())
		ro.Delete(ctx, uD)
	})

	// 写/CAP,kv
	It("KVStoreSetCAP", func() {
		uD2 := testdata.KVStore("key", "value2")
		err := ro.Set(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())
		ok, err := ro.Cas(ctx, uD2, uD)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(ok).Should(BeTrue())
	})
})

func KvStoreEqual(a *biz.KVStore, b *biz.KVStore) {
	Ω(a.Repo).Should(Equal(b.Repo))
	Ω(a.Key).Should(Equal(b.Key))
	Ω(a.Value).Should(Equal(b.Value))
}
