package data_test

import (
	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/data"
	"mpp/internal/taskscheduler/testdata"

	"github.com/go-kratos/kratos/v2/log"
	v1 "proto.mpp/api/common/v1"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Resource", func() {
	var ro biz.ResourceRepo
	var uD *biz.Resource
	var uDList []*biz.Resource
	BeforeEach(func() {
		// 这里的 Db 是 data_suite_test.go 文件里面定义的
		ro = data.NewResourceRepo(Db, log.GetLogger())
		// 这里你可以引入外部组装好的数据
		uD = testdata.Resource()

		uDList = testdata.ResourceList(10)
	})

	// ListResource
	It("ListResource", func() {
		for _, resource := range uDList {
			_, err := ro.Save(ctx, resource)
			Ω(err).ShouldNot(HaveOccurred())
		}

		resources, err := ro.ListAll(ctx, nil)
		log.Infof("ListResource resources: %+v", biz.JsonToString(resources))
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(resources)).Should(Equal(len(uDList)))
		for i, resource := range resources {
			ResourceEqual(resource, uDList[i])
		}

		t := v1.TaskType_Unknown
		resources, err = ro.ListAll(ctx, &t)
		log.Infof("2222==ListResource resources: %+v", biz.JsonToString(resources))
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(resources)).Should(Equal(len(uDList)))
		for i, resource := range resources {
			ResourceEqual(resource, uDList[i])
		}
	})

	// 创建资源
	It("CreateResource", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		resource, err := ro.FindByTaskId(ctx, uD.TaskId)
		Ω(err).ShouldNot(HaveOccurred())
		ResourceEqual(resource, uD)
	})

	// 更新状态
	It("UpdateResourceStatus", func() {
		err := ro.UpdateStatus(ctx, uD.TaskId, biz.ResourceStatusStopped)
		Ω(err).ShouldNot(HaveOccurred())

		_, err = ro.FindByTaskId(ctx, uD.TaskId)
		Ω(err).Should(HaveOccurred())

		err = ro.UpdateStatus(ctx, uD.TaskId, biz.ResourceStatusRunning)
		Ω(err).ShouldNot(HaveOccurred())
		resource, err := ro.FindByTaskId(ctx, uD.TaskId)
		Ω(err).ShouldNot(HaveOccurred())
		ResourceEqual(resource, uD)
	})

	// 任务重启
	It("ResourceRestart", func() {
		err := ro.UpdateStatus(ctx, uD.TaskId, biz.ResourceStatusStopped)
		Ω(err).ShouldNot(HaveOccurred())

		_, err = ro.FindByTaskId(ctx, uD.TaskId)
		Ω(err).Should(HaveOccurred())

		_, err = ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		resource, err := ro.FindByTaskId(ctx, uD.TaskId)
		Ω(err).ShouldNot(HaveOccurred())
		ResourceEqual(resource, uD)
	})

	// 更新Quota
	It("UpdateResourceQuota", func() {
		uD.RealQuota = map[string]int64{"cpu": 222}
		uD.AvgQuota = map[string]int64{"cpu": 233}
		err := ro.UpdateQuota(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		resource, err := ro.FindByTaskId(ctx, uD.TaskId)
		Ω(err).ShouldNot(HaveOccurred())
		ResourceEqual(resource, uD)
	})

	//查询lost资源
	It("ListLostResource", func() {
		_, err := ro.ListLost(ctx, nil)
		Ω(err).ShouldNot(HaveOccurred())
	})

	//// 性能测试
	//It("TestListAllResource", func() {
	//	//uDList := testdata.ResourceList(400000)
	//	//for _, resource := range uDList {
	//	//	_, err := ro.Save(ctx, resource)
	//	//	Ω(err).ShouldNot(HaveOccurred())
	//	//}
	//	startTime := time.Now()
	//	resources, err := ro.ListAll(ctx, nil)
	//	Ω(err).ShouldNot(HaveOccurred())
	//	log.Infof("ListAllResources resources: %v, cost:%v", len(resources), time.Since(startTime))
	//})
})

// 时间格式会变化，不能用整个equal比较
func ResourceEqual(a *biz.Resource, b *biz.Resource) {
	Ω(a.JobId).Should(Equal(b.JobId))
	Ω(a.TaskId).Should(Equal(b.TaskId))
	Ω(a.Quota).Should(Equal(b.Quota))
	Ω(a.WorkerId).Should(Equal(b.WorkerId))
	Ω(a.WorkerVersion).Should(Equal(b.WorkerVersion))
	Ω(a.IsMaster).Should(Equal(b.IsMaster))
	Ω(a.RealQuota).Should(Equal(b.RealQuota))
	Ω(a.AvgQuota).Should(Equal(b.AvgQuota))
	Ω(a.Label).Should(Equal(b.Label))
	Ω(a.Affinity).Should(Equal(b.Affinity))
	Ω(a.Extend).Should(Equal(b.Extend))
}
