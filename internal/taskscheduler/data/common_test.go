package data_test

import (
	"context"
	"mpp/internal/taskscheduler/data"
	"mpp/internal/taskscheduler/pkg/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAddTableDriven(t *testing.T) {
	uuid := data.NewUUID()
	assert.Equal(t, len(uuid), 36)
}

//
//func TestGetIp(t *testing.T) {
//	ip := data.GetIp()
//	assert.NotEmpty(t, ip)
//}

func TestGetHostName(t *testing.T) {
	client, err := http.InitHttpConnection(context.Background(), "www.taobao.com")
	assert.NoError(t, err)
	assert.NotNil(t, client)

	client, err = http.InitHttpConnection(context.Background(), "1.1.1")
}
