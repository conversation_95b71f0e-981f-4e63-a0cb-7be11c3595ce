package data

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"net"

	"mpp/pkg/tracer/middleware/tracer"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/google/uuid"
)

type MAPSTRING map[string]string
type MAPOBJECT map[string]interface{}
type LISTSTRING []string

func (j MAPSTRING) Value() (driver.Value, error) {
	return json.Marshal(j)
}

func (j *MAPSTRING) Scan(input interface{}) error {
	return json.Unmarshal(input.([]byte), j)
}

func (j MAPOBJECT) Value() (driver.Value, error) {
	return json.Marshal(j)
}

func (j *MAPOBJECT) Scan(input interface{}) error {
	return json.Unmarshal(input.([]byte), j)
}

func (j LISTSTRING) Value() (driver.Value, error) {
	bytes, err := json.Marshal(j)
	return bytes, err
}

func (j *LISTSTRING) Scan(input interface{}) error {
	return json.Unmarshal(input.([]byte), j)
}

func NewUUID() string {
	u := uuid.New()
	return u.String()
}

func GetIp() string {
	// 获取本机的所有网络接口
	interfaces, err := net.Interfaces()
	if err != nil {
		log.Fatal("get local ip failed. err:", err)
	}

	// 遍历所有网络接口，获取 IP 地址
	for _, i := range interfaces {
		addresses, err := i.Addrs()
		if err != nil {
			log.Fatal("get local ip failed. err:", err)
			continue
		}

		for _, addr := range addresses {
			// 检查 IP 地址是否为 IPv4 或 IPv6
			ipNet, ok := addr.(*net.IPNet)
			if !ok {
				continue
			}

			// 排除回环地址和非全球单播地址
			if !ipNet.IP.IsLoopback() && ipNet.IP.To4() != nil && ipNet.IP.IsGlobalUnicast() {
				return ipNet.IP.String()
			}
		}
	}

	log.Fatal("get local ip failed. not found.")
	return ""
}

func InitHttpConnection(ctx context.Context, addr string) (*http.Client, error) {
	connHTTP, err := http.NewClient(
		ctx,
		http.WithEndpoint(fmt.Sprintf("http://%v", addr)),
		http.WithBlock(),
		//arms链路追踪埋点
		http.WithMiddleware(
			tracing.Client(),
			tracer.MPPClientTracer(),
			metadata.Client(),
		),
	)
	if err != nil {
		log.Errorf("init http:%v connection failed:%v ", addr, err)
		return nil, err
	}

	return connHTTP, nil
}
