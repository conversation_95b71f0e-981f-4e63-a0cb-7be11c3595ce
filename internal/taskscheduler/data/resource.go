package data

import (
	"context"
	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/conf"
	"mpp/internal/taskscheduler/pkg/http"
	"runtime"
	"sync"
	"time"

	"github.com/cinience/animus/registry"

	"gorm.io/gorm"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	v1common "proto.mpp/api/common/v1"
	v1 "proto.mpp/api/taskmanager/v1"
)

type Resource struct {
	DBBaseModel
	JobId  string `gorm:"column:job_id;type:varchar(64);NOT NULL;COMMENT:作业ID"`
	TaskId string `gorm:"column:task_id;uniqueIndex;type:varchar(64);NOT NULL;COMMENT:作业ID"`
	Status int8   `gorm:"default:1;index:idx_status;index:idx_status_worker_id,priority:10;COMMENT:状态：0-不在线，1-在线"`
	//Status        int8         `gorm:"default:1;index:idx_status;index:idx_status_task_type,priority:1;index:idx_status_worker_id,priority:10;COMMENT:状态：0-不在线，1-在线"`
	WorkerId      string       `gorm:"column:worker_id;index:idx_status_worker_id,priority:11;COMMENT:worker唯一ID"`
	WorkerVersion int32        `gorm:"column:worker_version;COMMENT:worker版本号"`
	IsMaster      int8         `gorm:"column:is_master;COMMENT:是否master分配资源，0-不是，1-是"`
	Quota         biz.QuotaSet `gorm:"type:json;NOT NULL;COMMENT:多维度quota上限值"`
	RealQuota     biz.QuotaSet `gorm:"column:real_quota;type:json;COMMENT:多维度quota实际值"`
	AvgQuota      biz.QuotaSet `gorm:"column:avg_quota;type:json;COMMENT:多维度quot平均值"`
	TaskAffinity  string       `gorm:"column:affinity;type:json;COMMENT:亲和性"`
	Schedule      MAPOBJECT    `gorm:"column:schedule;type:json;COMMENT:调度参数，任务可改变单机的部分调度参数"`
	Label         MAPSTRING    `gorm:"type:json;COMMENT:任务标签，用于类似直播转码源流反亲和场景"`
	Extend        MAPOBJECT    `gorm:"type:json;COMMENT:扩展字段"`
	TaskType      int32        `gorm:"column:task_type;COMMENT:任务类型，0:未知类型 1:同步任务 2:流式任务 3:离线任务 4:dag任务，其它请参考pb的定义"`
	//TaskType int32 `gorm:"column:task_type;index:idx_status_task_type,priority:2;COMMENT:任务类型，0:未知类型 1:同步任务 2:流式任务 3:离线任务 4:dag任务，其它请参考pb的定义"`
}

type resourceRepo struct {
	data *Data
	log  *log.Helper
}

func NewResourceRepo(data *Data, logger log.Logger) biz.ResourceRepo {
	return &resourceRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (r *resourceRepo) Save(ctx context.Context, resource *biz.Resource) (*biz.Resource, error) {
	dbResource := Resource{}
	err := copier.Copy(&dbResource, resource)
	if nil != err {
		r.log.WithContext(ctx).Errorf("copy resource:%v failed, err:%v", biz.JsonToString(resource), err)
		return nil, err
	}

	dbResource.TaskAffinity = biz.JsonToString(resource.Affinity)

	result := r.data.db.WithContext(ctx).Exec(
		"INSERT INTO resources(gmt_create, gmt_modified, job_id, task_id, status, worker_id, worker_version, quota, real_quota, avg_quota, affinity, is_master, label, extend, task_type) "+
			"VALUES(now(), now(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) "+
			"ON DUPLICATE KEY UPDATE status = ?, worker_id = ?, worker_version = ?, quota = ?, real_quota = ?, avg_quota = ?, affinity = ?, is_master = ?, label = ?, extend = ?, gmt_modified = now(), task_type = ?",
		dbResource.JobId, dbResource.TaskId, biz.ResourceStatusRunning, dbResource.WorkerId, dbResource.WorkerVersion, dbResource.Quota, dbResource.RealQuota, dbResource.AvgQuota, dbResource.TaskAffinity, dbResource.IsMaster, dbResource.Label, dbResource.Extend, dbResource.TaskType,
		biz.ResourceStatusRunning, dbResource.WorkerId, dbResource.WorkerVersion, dbResource.Quota, dbResource.RealQuota, dbResource.AvgQuota, dbResource.TaskAffinity, dbResource.IsMaster, dbResource.Label, dbResource.Extend, dbResource.TaskType,
	)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("save resource:%v failed, err:%v", biz.JsonToString(resource), result)
		return nil, result.Error
	}

	return resource, nil
}

func (r *resourceRepo) UpdateQuota(ctx context.Context, resource *biz.Resource) error {
	data := map[string]interface{}{
		"Quota":        resource.Quota,
		"RealQuota":    resource.RealQuota,
		"AvgQuota":     resource.AvgQuota,
		"ModifiedTime": gorm.Expr("CURRENT_TIMESTAMP"),
	}

	result := r.data.db.WithContext(ctx).Model(&Resource{}).Where("task_id = ? AND status = ?", resource.TaskId, biz.ResourceStatusRunning).Updates(data)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateQuota resource:%v failed, err:%v", biz.JsonToString(resource), result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("update resource:%v quota:%v failed, affected rows is 0", resource.TaskId, resource.Quota)
		return nil
	}

	return nil
}

func (r *resourceRepo) UpdateStatus(ctx context.Context, taskId string, status int8) error {
	data := map[string]interface{}{
		"Status":       status,
		"ModifiedTime": gorm.Expr("CURRENT_TIMESTAMP"),
	}

	result := r.data.db.WithContext(ctx).Model(&Resource{}).Where("task_id = ?", taskId).Updates(data)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateStatus resource:%v status:%v failed, err:%v", taskId, status, result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("update resource:%v status:%v failed, affected rows is 0", taskId, status)
		return nil
	}

	return nil
}

// toBizResource 保留原方法以保持兼容性
func (r *resourceRepo) toBizResource(ctx context.Context, dbResource *Resource) (*biz.Resource, error) {
	resource := biz.Resource{}
	err := copier.Copy(&resource, &dbResource)
	if nil != err {
		r.log.WithContext(ctx).Errorf("copy resource:%v failed, err:%v", biz.JsonToString(resource), err)
		return nil, err
	}

	resource.Affinity = &biz.Affinity{}
	err = http.JSONStringToStruct(dbResource.TaskAffinity, resource.Affinity)
	if err != nil {
		r.log.WithContext(ctx).Warnf("task affinity[%s] json string to struct failed, err:%v",
			dbResource.TaskAffinity, err)
	}
	return &resource, nil
}

func (r *resourceRepo) FindByTaskId(ctx context.Context, taskId string) (*biz.Resource, error) {
	var dbResource Resource
	result := r.data.db.WithContext(ctx).Where("task_id = ? AND status = ?", taskId, biz.ResourceStatusRunning).Take(&dbResource)
	if result.Error != nil {
		//r.log.WithContext(ctx).Errorf("FindByTaskId resource:%v failed, resource not exist. err:%v", taskId, result)
		return nil, result.Error
	}

	return r.toBizResource(ctx, &dbResource)
}

func (r *resourceRepo) FindByWorker(ctx context.Context, worker *biz.Worker) ([]*biz.Resource, error) {
	var rs []Resource
	result := r.data.db.WithContext(ctx).Where("status = ? and worker_id = ?", biz.ResourceStatusRunning, worker.WorkerId).Find(&rs)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("FindByWorker resource failed, err:%v", result)
		return nil, result.Error
	}

	var resources []*biz.Resource

	for _, dbResource := range rs {
		resource, err := r.toBizResource(ctx, &dbResource)
		if err != nil {
			continue
		}
		resources = append(resources, resource)
	}
	return resources, nil
}

func (r *resourceRepo) ListLost(ctx context.Context, taskType *v1common.TaskType) ([]*biz.Resource, error) {
	var rs []Resource

	// SELECT *
	//		FROM resources as r JOIN workers as w
	//		ON
	//			r.worker_uuid = w.uuid AND r.status = 1 AND r.task_type = #{task_type}
	//			AND (w.status = 0 OR (w.status = 1 AND r.worker_ov < w.online_version ))

	query := r.data.db.WithContext(ctx).
		Table("resources as r").
		Joins("JOIN workers as w ON r.worker_id = w.worker_id").
		Where("r.status = ?", biz.ResourceStatusRunning)

	if taskType != nil && *taskType != v1common.TaskType_Unknown {
		query = query.Where("r.task_type = ?", taskType)
	}

	// 添加worker状态条件：w.status = 0 OR (w.status = 1 AND r.worker_ov < w.online_version)
	query = query.Where("(w.status = 0 OR (w.status = 1 AND r.worker_version < w.online_version))")

	result := query.Find(&rs)

	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("ListLost resource failed, err:%v", result.Error)
		return nil, result.Error
	}

	var resources []*biz.Resource

	for _, dbResource := range rs {
		resource, err := r.toBizResource(ctx, &dbResource)
		if err != nil {
			continue
		}
		resources = append(resources, resource)
	}
	return resources, nil
}

//func (r *resourceRepo) ListAll(ctx context.Context, taskType *v1common.TaskType) ([]*biz.Resource, error) {
//	query := map[string]interface{}{
//		"status": biz.ResourceStatusRunning,
//	}
//
//	if taskType != nil && *taskType != v1common.TaskType_Unknown {
//		query["task_type"] = taskType
//	}
//
//	var rs []Resource
//	r.log.WithContext(ctx).Infof("ListAll resource start from db, time:%v", time.Now())
//	result := r.data.db.WithContext(ctx).Where(query).Find(&rs)
//	r.log.WithContext(ctx).Infof("ListAll resource end from db:%v, time:%v", len(rs), time.Now())
//	if result.Error != nil {
//		r.log.WithContext(ctx).Errorf("ListAll resource failed, err:%v", result)
//		return nil, result.Error
//	}
//
//	var resources []*biz.Resource
//
//	// todo 耗时严重，42w数据查询耗时4s, 内存处理耗时12s..
//	//for _, dbResource := range rs {
//	//	resource, err := r.toBizResource(ctx, &dbResource)
//	//	if err != nil {
//	//		continue
//	//	}
//	//	resources = append(resources, resource)
//	//}
//
//	// 并发工作池处理，也仅能缩短一半左右时间
//	resources, _ = r.processResources(rs, ctx)
//
//	return resources, nil
//}

func (r *resourceRepo) ListAll(ctx context.Context, taskType *v1common.TaskType) ([]*biz.Resource, error) {
	// 使用基于ID的游标分页，避免OFFSET在大数据量时的性能问题
	const batchSize = 30000 // 每批次查询3w条记录
	var allResources []*biz.Resource
	var lastId uint64 = 0 // 游标：上一批最后一条记录的ID

	baseQuery := map[string]interface{}{
		"status": biz.ResourceStatusRunning,
	}

	if taskType != nil && *taskType != v1common.TaskType_Unknown {
		baseQuery["task_type"] = taskType
	}

	timeSec := time.Now()
	r.log.WithContext(ctx).Infof("ListAll resource start with cursor-based pagination, time:%v", timeSec)

	for {
		var rs []Resource

		// 构建查询，使用 ID 作为游标
		query := r.data.db.WithContext(ctx).Where(baseQuery)
		if lastId > 0 {
			query = query.Where("id > ?", lastId)
		}

		// 按ID排序，确保顺序一致
		result := query.Order("id ASC").Limit(batchSize).Find(&rs)
		if result.Error != nil {
			r.log.WithContext(ctx).Errorf("ListAll resource failed, err:%v", result)
			return nil, result.Error
		}

		if len(rs) == 0 {
			break // 没有更多数据了
		}

		// 批次处理当前数据
		batchResources, err := r.processResources(rs, ctx)
		if err != nil {
			r.log.WithContext(ctx).Errorf("Process batch resources failed, err:%v", err)
			return nil, err
		}

		allResources = append(allResources, batchResources...)

		// 更新游标为当前批次最后一条记录的ID
		lastId = rs[len(rs)-1].ID

		r.log.WithContext(ctx).Infof("Processed batch: lastId=%d, count=%d, total so far=%d",
			lastId, len(rs), len(allResources))

		// 如果这批数据少于batchSize，说明已经是最后一批了
		if len(rs) < batchSize {
			break
		}
	}

	r.log.WithContext(ctx).Infof("ListAll resource completed, total=%d, timecost:%v", len(allResources), time.Since(timeSec))
	return allResources, nil
}

func (r *resourceRepo) processResources(rs []Resource, ctx context.Context) ([]*biz.Resource, error) {
	timeSec := time.Now()
	defer func() {
		r.log.WithContext(ctx).Infof("Process batch resources completed, time:%v, timeCost:%v, size:%v", time.Now(), time.Since(timeSec), len(rs))
	}()

	// 预分配结果slice容量，避免动态扩容
	resources := make([]*biz.Resource, 0, len(rs))
	results := make(chan *biz.Resource, len(rs))

	// 根据CPU核心数设置工作池大小，默认不少于8个，不超过32个
	workerCount := 8
	if numCPU := runtime.NumCPU(); numCPU > 8 {
		workerCount = numCPU
		if workerCount > 32 {
			workerCount = 32
		}
	}

	// 创建工作池
	var wg sync.WaitGroup
	input := make(chan Resource, len(rs))

	// 将任务分配给工作池
	for _, dbResource := range rs {
		input <- dbResource
	}
	close(input)

	// 启动多个工作 goroutine
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for dbResource := range input {
				resource, err := r.toBizResourceOptimized(ctx, &dbResource)
				if err != nil {
					continue
				}
				results <- resource
			}
		}()
	}

	// 收集结果
	go func() {
		wg.Wait()
		close(results)
	}()

	// 收集所有结果
	for result := range results {
		resources = append(resources, result)
	}

	return resources, nil
}

// toBizResourceOptimized 优化版本的数据转换方法，消除反射操作
func (r *resourceRepo) toBizResourceOptimized(ctx context.Context, dbResource *Resource) (*biz.Resource, error) {
	// 手动复制字段，避免反射操作
	resource := &biz.Resource{
		JobId:         dbResource.JobId,
		TaskId:        dbResource.TaskId,
		ModifiedTime:  dbResource.ModifiedTime,
		Quota:         dbResource.Quota,
		RealQuota:     dbResource.RealQuota,
		AvgQuota:      dbResource.AvgQuota,
		WorkerId:      dbResource.WorkerId,
		WorkerVersion: dbResource.WorkerVersion,
		IsMaster:      dbResource.IsMaster,
		Label:         dbResource.Label,
		Schedule:      dbResource.Schedule,
		Extend:        dbResource.Extend,
		TaskType:      v1common.TaskType(dbResource.TaskType),
	}

	// 优化JSON解析 - 处理空字符串和预分配对象
	if dbResource.TaskAffinity != "" && dbResource.TaskAffinity != "null" {
		resource.Affinity = &biz.Affinity{}
		if err := http.JSONStringToStruct(dbResource.TaskAffinity, resource.Affinity); err != nil {
			r.log.WithContext(ctx).Warnf("task affinity[%s] json string to struct failed, err:%v",
				dbResource.TaskAffinity, err)
			// 设置默认空值而不是失败
			resource.Affinity = &biz.Affinity{}
		}
	} else {
		resource.Affinity = &biz.Affinity{}
	}

	return resource, nil
}

// ListAllOptimized 高性能版本的资源查询，支持配置化选项
func (r *resourceRepo) ListAllOptimized(ctx context.Context, taskType *v1common.TaskType, opts *ListOptions) ([]*biz.Resource, error) {
	// 默认选项
	if opts == nil {
		opts = &ListOptions{
			BatchSize:                 5000,
			EnablePagination:          true,
			EnableOptimizedConversion: true,
		}
	}

	baseQuery := map[string]interface{}{
		"status": biz.ResourceStatusRunning,
	}

	if taskType != nil && *taskType != v1common.TaskType_Unknown {
		baseQuery["task_type"] = taskType
	}

	// 如果不启用分页，则使用原始方式（但仍建议对大数据量使用排序）
	if !opts.EnablePagination {
		var rs []Resource
		r.log.WithContext(ctx).Infof("ListAllOptimized resource start (no pagination), time:%v", time.Now())
		// 即使不分页，也建议按ID排序以保证顺序一致性
		result := r.data.db.WithContext(ctx).Where(baseQuery).Order("id ASC").Find(&rs)
		if result.Error != nil {
			r.log.WithContext(ctx).Errorf("ListAllOptimized resource failed, err:%v", result)
			return nil, result.Error
		}

		resources, err := r.processResourcesWithOptions(rs, ctx, opts)
		r.log.WithContext(ctx).Infof("ListAllOptimized resource completed, total=%d, time:%v", len(resources), time.Now())
		return resources, err
	}

	// 使用基于ID的游标分页
	var allResources []*biz.Resource
	var lastId uint64 = 0
	batchSize := opts.BatchSize
	if batchSize <= 0 {
		batchSize = 5000
	}

	r.log.WithContext(ctx).Infof("ListAllOptimized resource start with cursor-based pagination, batchSize=%d, time:%v", batchSize, time.Now())

	for {
		var rs []Resource

		// 构建查询，使用 ID 作为游标
		query := r.data.db.WithContext(ctx).Where(baseQuery)
		if lastId > 0 {
			query = query.Where("id > ?", lastId)
		}

		// 按ID排序，确保顺序一致
		result := query.Order("id ASC").Limit(batchSize).Find(&rs)
		if result.Error != nil {
			r.log.WithContext(ctx).Errorf("ListAllOptimized resource failed, err:%v", result)
			return nil, result.Error
		}

		if len(rs) == 0 {
			break
		}

		batchResources, err := r.processResourcesWithOptions(rs, ctx, opts)
		if err != nil {
			r.log.WithContext(ctx).Errorf("Process batch resources failed, err:%v", err)
			return nil, err
		}

		allResources = append(allResources, batchResources...)

		// 更新游标为当前批次最后一条记录的ID
		lastId = rs[len(rs)-1].ID

		r.log.WithContext(ctx).Infof("Processed batch: lastId=%d, count=%d, total so far=%d",
			lastId, len(rs), len(allResources))

		if len(rs) < batchSize {
			break
		}
	}

	r.log.WithContext(ctx).Infof("ListAllOptimized resource completed, total=%d, time:%v", len(allResources), time.Now())
	return allResources, nil
}

// ListOptions 查询选项配置
type ListOptions struct {
	BatchSize                 int  `json:"batch_size"`                  // 分页大小，默认5000
	EnablePagination          bool `json:"enable_pagination"`           // 是否启用分页，默认true
	EnableOptimizedConversion bool `json:"enable_optimized_conversion"` // 是否启用优化转换，默认true
}

// processResourcesWithOptions 支持配置选项的资源处理方法
func (r *resourceRepo) processResourcesWithOptions(rs []Resource, ctx context.Context, opts *ListOptions) ([]*biz.Resource, error) {
	if opts == nil {
		opts = &ListOptions{EnableOptimizedConversion: true}
	}

	resources := make([]*biz.Resource, 0, len(rs))
	results := make(chan *biz.Resource, len(rs))

	workerCount := 8
	if numCPU := runtime.NumCPU(); numCPU > 8 {
		workerCount = numCPU
		if workerCount > 32 {
			workerCount = 32
		}
	}

	var wg sync.WaitGroup
	input := make(chan Resource, len(rs))

	for _, dbResource := range rs {
		input <- dbResource
	}
	close(input)

	// 根据配置选择转换方法
	conversionFunc := r.toBizResource
	if opts.EnableOptimizedConversion {
		conversionFunc = r.toBizResourceOptimized
	}

	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for dbResource := range input {
				resource, err := conversionFunc(ctx, &dbResource)
				if err != nil {
					continue
				}
				results <- resource
			}
		}()
	}

	go func() {
		wg.Wait()
		close(results)
	}()

	for result := range results {
		resources = append(resources, result)
	}

	return resources, nil
}

type taskManagerHttp struct {
	data *Data
	addr string
	//discovery registry.Discovery
	//httpClient *http.Client
	taskManagerClient v1.TaskHTTPClient
	log               *log.Helper
}

func NewTaskManagerApi(c *conf.App, discovery registry.Discovery, data *Data, logger log.Logger) biz.TaskManagerApi {
	taskManagerClient := initTaskManagerClient(discovery)
	wo := &taskManagerHttp{
		data: data,
		addr: c.TaskManager.Addr,
		//discovery:         discovery,
		log:               log.NewHelper(logger),
		taskManagerClient: taskManagerClient,
	}
	return wo
}

func initTaskManagerClient(discovery registry.Discovery) v1.TaskHTTPClient {
	connHTTP, err := http.InitHttpClientWithDiscovery(context.Background(), discovery)
	if err != nil {
		log.Fatalf("init tasker manager connection failed:%v ", err)
		return nil
	}

	c := v1.NewTaskHTTPClient(connHTTP)
	if err != nil {
		log.Fatalf("init tasker manager client failed:%v ", err)
		return nil
	}

	log.Infof("init tasker manager client success")

	return c
}

func (w *taskManagerHttp) MigrateTasks(ctx context.Context, ids []string) error {
	req := &v1.MigrateTaskRequest{TaskIds: ids, RequestId: NewUUID()}
	resp, err := w.taskManagerClient.MigrateTask(ctx, req)
	if err != nil {
		w.log.WithContext(ctx).Errorf("MigrateTasks resource:%v failed:%v", ids, err)
		return err
	}

	w.log.WithContext(ctx).Warnf("MigrateTasks resource:%v success. %v", ids, resp)

	return nil
}
