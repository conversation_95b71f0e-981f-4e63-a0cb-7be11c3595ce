package data_test

import (
	"sort"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/data"
	"mpp/internal/taskscheduler/testdata"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

/**
 * @Author: qinxin
 * @Date: 2025/4/1 10:50
 * @Desc:
 * @Version 1.0
 */

/*
待测试方法：
	Save(context.Context, *ChildWorker) (*ChildWorker, error)
	UpdateHeartbeat(context.Context, *ChildWorker, bool) error
	UpdateQuota(context.Context, *ChildWorker, string) error
	UpdateChangeQuota(ctx context.Context, workerId string, quota QuotaSet, scheduleConfig *StrategyConfig, opt string) (bool, error)
	UpdateStatus(context.Context, string, int8) error
	BatchUpdateStatus(context.Context, []string, int8) error
	//根据父workerId批量更新状态
	BatchUpdateStatusByParentWorkerIds(ctx context.Context, parentWorkerIds []string, status int8) error
	UpdateLabel(context.Context, *ChildWorker) error
	UpdateTaint(context.Context, []string, []string, []*Taint) error
	UpdateTagByIps(context.Context, []string, string) error
	UpdateTagByWorkerIds(context.Context, []string, string) error
	FindByWorkerId(context.Context, string, int8) (*ChildWorker, error)
	FindByIp(context.Context, string) (*ChildWorker, error)
	ListOnlineWorker(context.Context) ([]*ChildWorker, error)
	ListByConditions(context.Context, string, string, string, []string) ([]*ChildWorker, error)
	ListByWorkerIds(context.Context, []string) ([]*ChildWorker, error)
	ListByIps(context.Context, []string) ([]*ChildWorker, error)
*/

var _ = Describe("child_Worker", func() {
	var ro biz.ChildWorkerRepo
	var uD *biz.ChildWorker
	var uDList []*biz.ChildWorker
	BeforeEach(func() {
		// 这里的 Db 是 data_suite_test.go 文件里面定义的
		ro = data.NewChildWorkerRepo(Db, log.GetLogger())
		// 这里你可以引入外部组装好的数据
		uD = testdata.ChildWorker()

		uDList = testdata.ChildWorkerList(10)
	})

	// ListWorker
	It("ListWorker", func() {
		for _, level := range uDList {
			_, err := ro.Save(ctx, level)
			Ω(err).ShouldNot(HaveOccurred())
		}

		workers, err := ro.ListOnlineWorker(ctx)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(workers)).Should(Equal(len(uDList)))
		for i, worker := range workers {
			ChildWorkerBaseEqual(worker, uDList[i])
		}

		_, err = ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		workers, err = ro.ListByConditions(ctx, uDList[0].Product, uDList[0].EngineModel, uDList[0].Tag, nil)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(workers)).Should(Equal(len(uDList)))
		for i, worker := range workers {
			ChildWorkerBaseEqual(worker, uDList[i])
		}

		workers, err = ro.ListByWorkerIds(ctx, []string{uDList[2].WorkerId, uDList[3].WorkerId})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(workers)).Should(Equal(2))
		for i, worker := range workers {
			ChildWorkerBaseEqual(worker, uDList[2+i])
		}

		workers, err = ro.ListByIps(ctx, []string{uDList[4].Ip, uDList[5].Ip})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(workers)).Should(Equal(2))
		for i, worker := range workers {
			ChildWorkerBaseEqual(worker, uDList[4+i])
		}
	})

	// 创建和查询worker
	It("CreateWorker", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		worker, err := ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		ChildWorkerBaseEqual(worker, uD)
	})

	// 查询worker
	It("QueryWorker", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		worker, err := ro.FindByIp(ctx, uD.Ip)
		Ω(err).ShouldNot(HaveOccurred())
		ChildWorkerBaseEqual(worker, uD)
	})

	// 更新心跳
	It("UpdateWorkerHeartbeat", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())
		worker, _ := ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		log.Infof("worker:%+v", biz.JsonToString(worker))

		uD.Load = 1.1
		uD.RealQuota = map[string]int64{"cpu": 55}
		uD.AvgQuota = map[string]int64{"cpu": 44}

		time.Sleep(1 * time.Second)
		err = ro.UpdateHeartbeat(ctx, uD, false)
		Ω(err).ShouldNot(HaveOccurred())
		err = ro.UpdateHeartbeat(ctx, uD, false)
		worker, _ = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		log.Infof("worker:%+v", biz.JsonToString(worker))
		Ω(err).ShouldNot(HaveOccurred())

		worker, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		ChildWorkerHeartbeatCompare(worker, uD)

		// 更新心跳
		uD.HeartbeatTime = worker.HeartbeatTime
	})

	// 更新心跳
	It("UpdateWorkerHeartbeatWithScheduler", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		uD.Load = 1.1
		uD.RealQuota = map[string]int64{"cpu": 55}
		uD.AvgQuota = map[string]int64{"cpu": 44}
		uD.ScheduleQuota = map[string]int64{"cpu": 33}
		uD.AllocQuota = map[string]int64{"cpu": 44}
		uD.TaskNum = 5

		time.Sleep(1 * time.Second)
		err = ro.UpdateHeartbeat(ctx, uD, true)
		Ω(err).ShouldNot(HaveOccurred())

		time.Sleep(10 * time.Millisecond)
		err = ro.UpdateHeartbeat(ctx, uD, true)
		worker, _ := ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		log.Infof("worker:%+v", worker)
		Ω(err).ShouldNot(HaveOccurred())

		worker, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		ChildWorkerHeartbeatCompare(worker, uD)

		// 更新心跳
		uD.HeartbeatTime = worker.HeartbeatTime
	})

	// 更新Quota
	It("UpdateWorkerQuota", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		uD.AllocQuota = map[string]int64{"cpu": 111}
		uD.ScheduleQuota = map[string]int64{"cpu": 222}

		time.Sleep(1 * time.Second)
		// 申请Quota，会更新最后申请资源时间
		err = ro.UpdateQuota(ctx, uD, biz.QuotaOptAdd)
		Ω(err).ShouldNot(HaveOccurred())

		worker, err := ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		ChildWorkerAllocCompare(worker, uD)

		// 更新最后申请时间
		uD.LatestAllocTime = worker.LatestAllocTime
		uD.UpdateQuotaVersion += 1

		time.Sleep(1 * time.Second)
		uD.AllocQuota = map[string]int64{"cpu": 55}
		uD.ScheduleQuota = map[string]int64{"cpu": 66}
		// 释放Quota
		err = ro.UpdateQuota(ctx, uD, biz.QuotaOptSub)
		Ω(err).ShouldNot(HaveOccurred())

		worker, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		ChildWorkerEqual(worker, uD)
	})

	// 更新变化的Quota
	It("UpdateWorkerChangedQuota", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		quota := biz.QuotaSet{"cpu": 35}
		ok, err := ro.UpdateChangeQuota(ctx, uD.WorkerId, quota, biz.DefaultMasterStrategy.Config, biz.QuotaOptAdd)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(ok).Should(BeTrue())

		worker, err := ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())

		Ω(worker.Quota).Should(Equal(uD.Quota))
		Ω(*(worker.AllocQuota.Sub(quota))).Should(Equal(uD.AllocQuota))
		Ω(worker.RealQuota).Should(Equal(uD.RealQuota))
		Ω(worker.AvgQuota).Should(Equal(uD.AvgQuota))
		Ω(*(worker.ScheduleQuota.Add(quota))).Should(Equal(uD.ScheduleQuota))

		ok, err = ro.UpdateChangeQuota(ctx, uD.WorkerId, quota, biz.DefaultMasterStrategy.Config, biz.QuotaOptSub)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(ok).Should(BeTrue())

		worker, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(worker.Quota).Should(Equal(uD.Quota))
		Ω(worker.AllocQuota).Should(Equal(uD.AllocQuota))
		Ω(worker.RealQuota).Should(Equal(uD.RealQuota))
		Ω(worker.AvgQuota).Should(Equal(uD.AvgQuota))
		Ω(worker.ScheduleQuota).Should(Equal(uD.ScheduleQuota))

		config := &biz.StrategyConfig{
			ScheduleResourceType: biz.ScheduleResourceTypeReal,
			LimitRatio:           map[string]int64{"cpu": 100},
			MaxTaskNum:           1000,
			LoadRatio:            100,
			Dominants:            []string{"cpu"},
		}
		ok, err = ro.UpdateChangeQuota(ctx, uD.WorkerId, quota, config, biz.QuotaOptAdd)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(ok).Should(BeTrue())

		worker, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())

		Ω(worker.Quota).Should(Equal(uD.Quota))
		Ω(*(worker.AllocQuota.Sub(quota))).Should(Equal(uD.AllocQuota))
		Ω(*(worker.RealQuota.Sub(quota))).Should(Equal(uD.RealQuota))
		Ω(worker.AvgQuota).Should(Equal(uD.AvgQuota))
		Ω(*(worker.ScheduleQuota.Add(quota))).Should(Equal(uD.ScheduleQuota))

		ok, err = ro.UpdateChangeQuota(ctx, uD.WorkerId, quota, config, biz.QuotaOptSub)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(ok).Should(BeTrue())

		worker, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())

		Ω(worker.Quota).Should(Equal(uD.Quota))
		Ω(worker.AllocQuota).Should(Equal(uD.AllocQuota))
		Ω(*(worker.RealQuota.Sub(quota))).Should(Equal(uD.RealQuota))
		Ω(worker.AvgQuota).Should(Equal(uD.AvgQuota))
		Ω(*(worker.ScheduleQuota.Add(quota))).Should(Equal(uD.ScheduleQuota))

		//worker, err := ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		//Ω(err).ShouldNot(HaveOccurred())
		//WorkerAllocCompare(worker, uD)
		//
		//// 更新最后申请时间
		//uD.LatestAllocTime = worker.LatestAllocTime
		//uD.UpdateQuotaVersion += 1
		//
		//time.Sleep(1 * time.Second)
		//uD.AllocQuota = map[string]int64{"cpu": 55}
		//uD.ScheduleQuota = map[string]int64{"cpu": 66}
		//// 释放Quota
		//err = ro.UpdateQuota(ctx, uD, biz.QuotaOptSub)
		//Ω(err).ShouldNot(HaveOccurred())
		//
		//worker, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		//Ω(err).ShouldNot(HaveOccurred())
		//WorkerEqual(worker, uD)
	})

	// 更新状态
	It("UpdateWorkerStatus", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		err = ro.UpdateStatus(ctx, uD.WorkerId, biz.WorkerStatusOffline)
		Ω(err).ShouldNot(HaveOccurred())

		_, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).Should(HaveOccurred())

		err = ro.UpdateStatus(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		worker, err := ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		ChildWorkerBaseEqual(worker, uD)
	})

	// 更新标签和污点
	It("UpdateWorkerLabel", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		uD.Label = map[string]string{"labelUpdated": "valueUpdated"}
		err = ro.UpdateLabel(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		worker, err := ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		ChildWorkerBaseEqual(worker, uD)
	})

	// 更新标签和污点
	It("UpdateWorkerTaint", func() {
		worker, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())
		worker, _ = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		log.Infof("111=====worker:%+v", biz.JsonToString(worker))

		uD.Taint = []*biz.Taint{{Key: "key_taint_2", Value: "", Effect: "Delete"}}
		err = ro.UpdateTaint(ctx, []string{uD.WorkerId}, nil, uD.Taint)
		Ω(err).ShouldNot(HaveOccurred())
		worker, _ = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		log.Infof("222====worker:%+v", biz.JsonToString(worker))

		uD.Taint = []*biz.Taint{{Key: "taintUpdated2", Value: "valueUpdated2", Effect: "NoSchedule"}}
		err = ro.UpdateTaint(ctx, []string{uD.WorkerId}, nil, uD.Taint)
		Ω(err).ShouldNot(HaveOccurred())

		worker, err = ro.FindByWorkerId(ctx, uD.WorkerId, biz.WorkerStatusOnline)
		log.Infof("333=====worker:%+v", biz.JsonToString(worker))

		uD.Taint = []*biz.Taint{{Key: "key_taint_1", Value: "value_taint_1", Effect: "NoSchedule"}, {Key: "taintUpdated2", Value: "valueUpdated2", Effect: "NoSchedule"}}
		Ω(err).ShouldNot(HaveOccurred())
		ChildWorkerBaseEqual(worker, uD)
	})

	// 更新tag
	It("UpdateWorkerTag", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		tag := "tagUpdated"
		err = ro.UpdateTagByWorkerIds(ctx, []string{uDList[0].WorkerId, uDList[1].WorkerId}, tag)
		Ω(err).ShouldNot(HaveOccurred())
		uDList[0].Tag = tag
		uDList[1].Tag = tag

		workers, err := ro.ListByConditions(ctx, uDList[0].Product, uDList[0].EngineModel, tag, nil)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(workers)).Should(Equal(2))
		for i, worker := range workers {
			ChildWorkerBaseEqual(worker, uDList[i])
		}

		tag = "tagUpdated-2"
		err = ro.UpdateTagByWorkerIds(ctx, []string{uDList[4].WorkerId, uDList[5].WorkerId}, tag)
		Ω(err).ShouldNot(HaveOccurred())
		uDList[4].Tag = tag
		uDList[5].Tag = tag

		workers, err = ro.ListByConditions(ctx, uDList[4].Product, uDList[4].EngineModel, tag, nil)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(workers)).Should(Equal(2))
		for i, worker := range workers {
			ChildWorkerBaseEqual(worker, uDList[4+i])
		}
	})

	It("BatchUpdateStatus", func() {
		err := ro.BatchUpdateStatus(ctx, []string{uDList[0].WorkerId, uDList[1].WorkerId}, biz.WorkerStatusOffline)
		Ω(err).ShouldNot(HaveOccurred())
		worker, err := ro.FindByWorkerId(ctx, uDList[0].WorkerId, biz.WorkerStatusOffline)
		Ω(err).ShouldNot(HaveOccurred())
		ChildWorkerBaseEqual(worker, uDList[0])
	})
	It("BatchUpdateStatusByParentWorkerIds", func() {
		err := ro.BatchUpdateStatusByParentWorkerIds(ctx, []string{uDList[0].ParentWorkerId, uDList[1].ParentWorkerId}, biz.WorkerStatusOffline)
		Ω(err).ShouldNot(HaveOccurred())
		worker, err := ro.FindByWorkerId(ctx, uDList[0].WorkerId, biz.WorkerStatusOffline)
		Ω(err).ShouldNot(HaveOccurred())
		ChildWorkerBaseEqual(worker, uDList[0])
	})
	It("UpdateTagByIps", func() {
		err := ro.UpdateTagByIps(ctx, []string{uDList[0].Ip, uDList[1].Ip}, "tagUpdated")
		Ω(err).ShouldNot(HaveOccurred())
		worker, err := ro.FindByIp(ctx, uDList[0].Ip)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(worker.Tag).Should(Equal("tagUpdated"))
	})

})

func ChildWorkerEqual(a *biz.ChildWorker, b *biz.ChildWorker) {
	ChildWorkerBaseEqual(a, b)
	Ω(a.LatestAllocTime.Unix()).Should(Equal(b.LatestAllocTime.Unix()))
	Ω(a.HeartbeatTime.Unix()).Should(Equal(b.HeartbeatTime.Unix()))
}

func ChildWorkerHeartbeatCompare(a *biz.ChildWorker, b *biz.ChildWorker) {
	ChildWorkerBaseEqual(a, b)
	Ω(a.HeartbeatTime.Unix()).Should(BeNumerically(">", b.HeartbeatTime.Unix()))
}

func ChildWorkerAllocCompare(a *biz.ChildWorker, b *biz.ChildWorker) {
	ChildWorkerBaseEqual(a, b)
	Ω(a.LatestAllocTime.Unix()).Should(BeNumerically(">", b.LatestAllocTime.Unix()))
}

func ChildWorkerBaseEqual(a *biz.ChildWorker, b *biz.ChildWorker) {
	Ω(a.ParentProduct).Should(Equal(b.ParentProduct))
	Ω(a.ParentTag).Should(Equal(b.ParentTag))
	Ω(a.ParentEngineModel).Should(Equal(b.ParentEngineModel))
	Ω(a.ParentWorkerId).Should(Equal(b.ParentWorkerId))
	Ω(a.WorkerId).Should(Equal(b.WorkerId))
	Ω(a.Ip).Should(Equal(b.Ip))
	Ω(a.PodName).Should(Equal(b.PodName))
	Ω(a.NodeName).Should(Equal(b.NodeName))
	Ω(a.Port).Should(Equal(b.Port))
	Ω(a.Product).Should(Equal(b.Product))
	Ω(a.EngineModel).Should(Equal(b.EngineModel))
	Ω(a.Tag).Should(Equal(b.Tag))
	Ω(a.Load).Should(Equal(b.Load))
	Ω(a.OnlineVersion).Should(BeNumerically(">=", b.OnlineVersion))
	Ω(a.Quota).Should(Equal(b.Quota))
	Ω(a.AllocQuota).Should(Equal(b.AllocQuota))
	Ω(a.RealQuota).Should(Equal(b.RealQuota))
	Ω(a.AvgQuota).Should(Equal(b.AvgQuota))
	Ω(a.ScheduleQuota).Should(Equal(b.ScheduleQuota))
	Ω(a.Label).Should(Equal(b.Label))
	sort.Sort(TestTaint(a.Taint))
	sort.Sort(TestTaint(b.Taint))
	Ω(a.Taint).Should(Equal(b.Taint))
	Ω(a.Extend).Should(Equal(b.Extend))
}
