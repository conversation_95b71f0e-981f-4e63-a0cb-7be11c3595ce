package data_test

import (
	"context"
	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/data"
	"mpp/pkg/utils"

	"github.com/go-kratos/kratos/v2/log"
	pb "proto.mpp/api/taskscheduler/v1"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Schedule", func() {
	var client biz.TaskScheduleMasterApi
	BeforeEach(func() {
		// 这里的 Db 是 data_suite_test.go 文件里面定义的
		client = data.NewTaskScheduleMasterClient(Db, log.GetLogger())
		ctx = utils.SetRequestId(context.Background())
	})

	It("GetEndpoint", func() {
		endpoint := "127.0.0.1:8080"
		client.InitClient(ctx, endpoint)
		ep := client.GetEndpoint()
		Ω(ep).Should(Equal(endpoint))

		endpoint2 := "127.0.0.1:8081"
		client.ResetClient(ctx, endpoint2)
		ep = client.GetEndpoint()
		Ω(ep).Should(Equal(endpoint2))

	})

	It("GetMasterWorker", func() {
		endpoint := "127.0.0.1:8080"
		client.InitClient(ctx, endpoint)
		worker, err := client.GetMasterWorker(ctx, "test")
		Ω(err).Should(HaveOccurred())
		Ω(worker).Should(BeNil())

	})

	It("AllocMasterResource", func() {
		endpoint := "127.0.0.1:8080"
		client.InitClient(ctx, endpoint)
		worker, err := client.AllocMasterResource(ctx, &pb.AllocResourceRequest{})
		Ω(err).Should(HaveOccurred())
		Ω(worker).Should(BeNil())
	})

	It("FreeMasterResource", func() {
		endpoint := "127.0.0.1:8080"
		client.InitClient(ctx, endpoint)
		worker, err := client.FreeMasterResource(ctx, "test")
		Ω(err).Should(HaveOccurred())
		Ω(worker).Should(BeNil())
	})

})
