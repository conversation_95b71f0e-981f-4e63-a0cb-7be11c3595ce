package data_test

import (
	"time"

	"github.com/go-kratos/kratos/v2/log"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/data"
	"mpp/internal/taskscheduler/testdata"
)

var _ = Describe("WaterLevel", func() {
	var ro biz.WaterLevelRepo
	var uD *biz.WaterLevel
	var uDList []*biz.WaterLevel
	BeforeEach(func() {
		// 这里的 Db 是 data_suite_test.go 文件里面定义的
		ro = data.NewWaterLevelRepo(Db, log.GetLogger())
		// 这里你可以引入外部组装好的数据
		uD = testdata.WaterLevel()

		uDList = testdata.WaterLevelList(10)
	})

	// List策略
	It("ListWaterLevel", func() {
		for _, level := range uDList {
			err := ro.Save(ctx, level)
			Ω(err).ShouldNot(HaveOccurred())
		}

		cond := &biz.WaterLevel{
			Product:     uD.Product,
			EngineModel: uD.EngineModel,
			Tag:         uD.Tag,
		}
		levels, err := ro.ListAll(ctx, cond, 10)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(levels)).Should(Equal(len(uDList)))
		for i, level := range levels {
			WaterLevelEqual(level, uDList[len(uDList)-1-i])
		}

		levels, err = ro.ListAll(ctx, cond, 5)
		Ω(err).ShouldNot(HaveOccurred())
		for i, level := range levels {
			WaterLevelEqual(level, uDList[len(uDList)-1-i])
		}
	})

	// 创建和查询最新策略
	It("CreateWaterLevel", func() {
		err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		cond := &biz.WaterLevel{
			Product:     uD.Product,
			EngineModel: uD.EngineModel,
			Tag:         uD.Tag,
		}

		level, err := ro.FindLatest(ctx, cond)
		Ω(err).ShouldNot(HaveOccurred())

		WaterLevelEqual(level, uD)
	})

	// 测试聚合查询功能
	It("FindLatestAggregated", func() {
		// 准备测试数据 - 同一个三元组下的不同 Label 记录
		now := time.Now()

		waterLevel1 := &biz.WaterLevel{
			Product:     "mediaai",
			EngineModel: "rtc-robot-workflow",
			Tag:         "default",
			Label:       map[string]string{"env": "prod"},
			StatTime:    now,
			TotalQuota:  biz.QuotaSet{"cpu": 64000, "mem": 128000},
			AllocQuota:  biz.QuotaSet{"cpu": 2000, "mem": 16000},
			RealQuota:   biz.QuotaSet{"cpu": 1500, "mem": 12000},
			TaskNum:     10,
			Type:        "logic",
			Dominant:    "cpu",
			WaterLevel:  0.03125,
			Strategy: &biz.StrategyConfig{
				Dominants:  []string{"cpu"},
				LimitRatio: map[string]int64{"cpu": 100},
			},
		}

		waterLevel2 := &biz.WaterLevel{
			Product:     "mediaai",
			EngineModel: "rtc-robot-workflow",
			Tag:         "default",
			Label:       map[string]string{"env": "test"},
			StatTime:    now,
			TotalQuota:  biz.QuotaSet{"cpu": 32000, "mem": 64000},
			AllocQuota:  biz.QuotaSet{"cpu": 1500, "mem": 8000},
			RealQuota:   biz.QuotaSet{"cpu": 1000, "mem": 6000},
			TaskNum:     5,
			Type:        "logic",
			Dominant:    "cpu",
			WaterLevel:  0.046875,
			Strategy: &biz.StrategyConfig{
				Dominants:  []string{"cpu"},
				LimitRatio: map[string]int64{"cpu": 100},
			},
		}

		// 保存两条记录
		err := ro.Save(ctx, waterLevel1)
		Ω(err).ShouldNot(HaveOccurred())

		err = ro.Save(ctx, waterLevel2)
		Ω(err).ShouldNot(HaveOccurred())

		// 查询时不指定 Label，应该返回聚合数据
		cond := &biz.WaterLevel{
			Product:     "mediaai",
			EngineModel: "rtc-robot-workflow",
			Tag:         "default",
		}

		aggregated, err := ro.FindLatest(ctx, cond)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(aggregated).ShouldNot(BeNil())

		// 验证聚合结果
		Ω(aggregated.Product).Should(Equal("mediaai"))
		Ω(aggregated.EngineModel).Should(Equal("rtc-robot-workflow"))
		Ω(aggregated.Tag).Should(Equal("default"))
		Ω(aggregated.Label).Should(BeNil()) // 聚合数据不保留 Label

		// 验证配额聚合
		expectedTotalCpu := int64(64000 + 32000)
		expectedAllocCpu := int64(2000 + 1500)
		expectedTaskNum := int32(10 + 5)

		Ω(aggregated.TotalQuota["cpu"]).Should(Equal(expectedTotalCpu))
		Ω(aggregated.AllocQuota["cpu"]).Should(Equal(expectedAllocCpu))
		Ω(aggregated.TaskNum).Should(Equal(expectedTaskNum))

		// 验证水位重新计算
		expectedWaterLevel := float32(expectedAllocCpu) / float32(expectedTotalCpu)
		Ω(aggregated.WaterLevel).Should(BeNumerically("~", expectedWaterLevel, 0.001))
	})

	// 测试精确查询功能
	It("FindLatestExact", func() {
		// 准备测试数据
		now := time.Now()

		waterLevel1 := &biz.WaterLevel{
			Product:     "mediaai",
			EngineModel: "rtc-robot-workflow",
			Tag:         "default",
			Label:       map[string]string{"env": "prod"},
			StatTime:    now,
			TotalQuota:  biz.QuotaSet{"cpu": 64000},
			AllocQuota:  biz.QuotaSet{"cpu": 2000},
			TaskNum:     10,
			Type:        "logic",
			Strategy: &biz.StrategyConfig{
				Dominants:  []string{"cpu"},
				LimitRatio: map[string]int64{"cpu": 100},
			},
		}

		waterLevel2 := &biz.WaterLevel{
			Product:     "mediaai",
			EngineModel: "rtc-robot-workflow",
			Tag:         "default",
			Label:       map[string]string{"env": "test"},
			StatTime:    now,
			TotalQuota:  biz.QuotaSet{"cpu": 32000},
			AllocQuota:  biz.QuotaSet{"cpu": 1500},
			TaskNum:     5,
			Type:        "logic",
			Strategy: &biz.StrategyConfig{
				Dominants:  []string{"cpu"},
				LimitRatio: map[string]int64{"cpu": 100},
			},
		}

		// 保存两条记录
		err := ro.Save(ctx, waterLevel1)
		Ω(err).ShouldNot(HaveOccurred())

		err = ro.Save(ctx, waterLevel2)
		Ω(err).ShouldNot(HaveOccurred())

		// 精确查询指定 Label 的记录
		cond := &biz.WaterLevel{
			Product:     "mediaai",
			EngineModel: "rtc-robot-workflow",
			Tag:         "default",
			Label:       map[string]string{"env": "prod"},
		}

		exact, err := ro.FindLatest(ctx, cond)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(exact).ShouldNot(BeNil())

		// 验证返回的是精确匹配的记录
		Ω(exact.Label).Should(Equal(map[string]string{"env": "prod"}))
		Ω(exact.TotalQuota["cpu"]).Should(Equal(int64(64000))) // 只有第一条记录的数据
		Ω(exact.TaskNum).Should(Equal(int32(10)))
	})
})

// 时间格式会变化，不能用整个equal比较
func WaterLevelEqual(a *biz.WaterLevel, b *biz.WaterLevel) {
	Ω(a.Product).Should(Equal(b.Product))
	Ω(a.EngineModel).Should(Equal(b.EngineModel))
	Ω(a.Tag).Should(Equal(b.Tag))
	Ω(a.Label).Should(Equal(b.Label))
	Ω(a.StatTime.Unix()).Should(Equal(b.StatTime.Unix()))
	Ω(a.TotalQuota).Should(Equal(b.TotalQuota))
	Ω(a.AllocQuota).Should(Equal(b.AllocQuota))
	Ω(a.RealQuota).Should(Equal(b.RealQuota))
	Ω(a.AvgQuota).Should(Equal(b.AvgQuota))
	Ω(a.ScheduleQuota).Should(Equal(b.ScheduleQuota))
	//Ω(a.Type).Should(Equal(b.Type))
	//Ω(a.Dominant).Should(Equal(b.Dominant))
	Ω(a.WaterLevel).Should(Equal(b.WaterLevel))
}
