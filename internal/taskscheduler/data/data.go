package data

import (
	"fmt"
	"time"

	"github.com/cinience/animus/datastores/gormhelper"
	"mpp/internal/taskscheduler/conf"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/plugin/opentelemetry/tracing"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(
	NewData,
	NewDB,
	NewWorkerRepo,
	NewChildWorkerRepo,
	NewResourceRepo,
	NewWorkerApi,
	NewStrategyRepo,
	NewDBLeaderRepo,
	NewMutexRepo,
	NewDBKVStore,
	NewWaterLevelRepo,
	NewTaskManagerApi,
	NewJobManagerClusterApi,
	NewTaskScheduleMasterClient,
)

type DBBaseModel struct {
	ID           uint64    `gorm:"column:id;primarykey;COMMENT:主键"`
	CreateTime   time.Time `gorm:"column:gmt_create;type:datetime;not null;COMMENT:创建时间"`
	ModifiedTime time.Time `gorm:"column:gmt_modified;type:datetime;not null;COMMENT:修改时间"`
}

// Data .
type Data struct {
	// TODO wrapped database client
	db *gorm.DB
}

// NewData .
func NewData(c *conf.Data, logger log.Logger, db *gorm.DB) (*Data, func(), error) {
	cleanup := func() {
		log.NewHelper(logger).Info("closing the data resources")
	}
	return &Data{
		db: db,
	}, cleanup, nil
}

func NewDB(c *conf.Data) (*gorm.DB, error) {
	log.Infof("init db. info:%v", c.Database)

	db, err := gorm.Open(mysql.Open(c.Database.Source),
		&gorm.Config{
			// 数据库日志开关
			Logger: logger.Default.LogMode(logger.Warn),
			// 关闭默认事务
			SkipDefaultTransaction: true,
			// 开启预编译
			PrepareStmt: true,
		})
	if err != nil {
		log.Fatalf("failed opening connection to mysql: %v", err)
		return nil, err
	}
	if err := db.Use(tracing.NewPlugin()); err != nil {
		log.Fatalf("failed open tracing to mysql: %v", err)
		return nil, err
	}

	if err := configDB(db); err != nil {
		log.Fatalf("failed config to mysql: %v", err)
		return nil, err
	}

	registerModel(db)

	if c.Database.AutoMigrate {
		log.Warn("start initTables, this operation is dangerous ....")
		initTables(db)
	}

	return db, nil
}

//
//func NewTaskManagerClient(r registry.Discovery, tp *tracesdk.TracerProvider) orderv1.OrderClient {
//	conn, err := grpc.DialInsecure(
//		context.Background(),
//		grpc.WithEndpoint("discovery:///beer.order.service"),
//		grpc.WithDiscovery(r),
//		grpc.WithMiddleware(
//			tracing.Client(tracing.WithTracerProvider(tp)),
//			recovery.Recovery(),
//		),
//	)
//	if err != nil {
//		panic(err)
//	}
//	return orderv1.NewOrderClient(conn)
//}
//
//
//func NewTaskManagerHttpClient(c *conf.App, r registry.Discovery, tp *tracesdk.TracerProvider) v1.TaskHTTPClient {
//	connHTTP, err := InitHttpConnection(context.Background(), c.TaskManager.Addr)
//	http.NewClient(
//		context.Background(),
//		http.WithEndpoint(fmt.Sprintf("http://%v", c.TaskManager.Addr)),
//		http.WithBlock(),
//	)
//	if err != nil {
//		log.Errorf("init tasker manager:%v connection failed:%v ", c.TaskManager.Addr, err)
//		panic(err)
//	}
//
//	client := v1.NewTaskHTTPClient(connHTTP)
//	if err != nil {
//		log.Errorf("MigrateResources new client failed:%v ", err)
//		return nil
//	}
//
//	return client
//}

func registerModel(db *gorm.DB) {
	db.Model(&Worker{})
	db.Model(&ChildWorker{})
	db.Model(&Resource{})
	db.Model(&Strategy{})
}

func initTables(db *gorm.DB) {
	if err := gormhelper.AutoMigrate(db, &Worker{}); err != nil {
		log.Fatalf("failed to create table workers: %v", err)
	}

	if err := gormhelper.AutoMigrate(db, &ChildWorker{}); err != nil {
		log.Fatalf("failed to create table child_workers: %v", err)
	}

	if err := gormhelper.AutoMigrate(db, &Resource{}); err != nil {
		log.Fatalf("failed to create table resources: %v", err)
	}

	if err := gormhelper.AutoMigrate(db, &Strategy{}); err != nil {
		log.Fatalf("failed to create table scheduler: %v", err)
	}

	if err := gormhelper.AutoMigrate(db, &Leader{}); err != nil {
		log.Fatalf("failed to create table leader: %v", err)
	}

	if err := gormhelper.AutoMigrate(db, &Mutex{}); err != nil {
		log.Fatalf("failed to create table mutex: %v", err)
	}

	if err := gormhelper.AutoMigrate(db, &kvStore{}); err != nil {
		log.Fatalf("failed to create table kv store: %v", err)
	}

	if err := gormhelper.AutoMigrate(db, &WaterLevel{}); err != nil {
		log.Fatalf("failed to create table water level: %v", err)
	}
}

func configDB(db *gorm.DB) error {
	sqlDb, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to config db: %v", err)
	}

	// 设置连接池
	// 打开
	sqlDb.SetMaxOpenConns(200)
	// 空闲
	sqlDb.SetMaxIdleConns(100)
	// 连接存活时间
	sqlDb.SetConnMaxLifetime(time.Second * 5)

	return nil
}
