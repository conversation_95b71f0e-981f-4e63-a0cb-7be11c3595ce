package data

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"google.golang.org/grpc/metadata"
	"gorm.io/gorm"

	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/conf"

	v1 "proto.mpp/api/jobmanager/v1"
)

type WaterLevel struct {
	DBBaseModel
	Product          string       `gorm:"column:product;index:idx_product_model_tag;NOT NULL;COMMENT:产品"`
	EngineModel      string       `gorm:"column:engine_model;index:idx_product_model_tag;NOT NULL;COMMENT:引擎模型"`
	Tag              string       `gorm:"column:tag;NOT NULL;index:idx_product_model_tag;COMMENT:标签"`
	Label            MAPSTRING    `gorm:"type:json;COMMENT:标签组，不同的标签组，可以定义不同的策略"`
	StatTime         time.Time    `gorm:"column:gmt_stat_time;index:idx_stat_time;type:datetime;COMMENT:统计时间"`
	TotalQuota       biz.QuotaSet `gorm:"column:total_quota;type:json;COMMENT:总quota"`
	AllocQuota       biz.QuotaSet `gorm:"column:alloc_quota;type:json;COMMENT:逻辑quota"`
	RealQuota        biz.QuotaSet `gorm:"column:real_quota;type:json;COMMENT:真实quota"`
	ScheduleQuota    biz.QuotaSet `gorm:"column:schedule_quota;type:json;COMMENT:调度quota"`
	AvgQuota         biz.QuotaSet `gorm:"column:avg_quota;type:json;COMMENT:平均quota"`
	Type             string       `gorm:"column:type;NOT NULL;COMMENT:水位统计类型：real:根据真实值统计, logic:根据逻辑值统计"`
	Dominant         string       `gorm:"column:dominant;NOT NULL;COMMENT:调度主导资源"`
	WaterLevel       float32      `gorm:"column:water_level;COMMENT:水位"`
	ScheduleStrategy string       `gorm:"column:strategy;type:json;COMMENT:配置"`
	TaskNum          int32        `gorm:"column:task_num;COMMENT:总任务数"`
}

type jobManagerClusterApiHttp struct {
	data      *Data
	log       *log.Helper
	addr      string
	addrs     []string
	clusterId string
}

type waterLevelRepo struct {
	data *Data
	log  *log.Helper
}

// 多端点上报水位 (mps ai)
func (j *jobManagerClusterApiHttp) UpdateWaterLevel(ctx context.Context, waterLevels []*biz.WaterLevel) error {
	var errors []error
	connAddrs := []string{}
	if j.addrs != nil && len(j.addrs) > 0 {
		connAddrs = j.addrs
		j.log.WithContext(ctx).Infof("UpdateWaterLevel addrs:%v", connAddrs)
	} else {
		connAddrs = []string{j.addr}
		j.log.WithContext(ctx).Infof("UpdateWaterLevel addr:%v", connAddrs)
	}

	for _, addr := range connAddrs { // 遍历所有地址
		if strings.Contains(addr, "127.0.0.1") {
			continue
		}
		connHTTP, err := InitHttpConnection(ctx, addr)
		if err != nil {
			j.log.WithContext(ctx).Errorf("UpdateWaterLevel init connection to %s failed: %v", addr, err)
			errors = append(errors, err)
			continue
		}
		defer connHTTP.Close()

		client := v1.NewClusterHTTPClient(connHTTP)

		var wls []*v1.UpdateWaterLevelRequest_WaterLevel

		for _, wl := range waterLevels {
			waterLevel := v1.UpdateWaterLevelRequest_WaterLevel{}
			err := copier.Copy(&waterLevel, &wl)
			if err != nil {
				j.log.WithContext(ctx).Errorf("UpdateWaterLevel copy water level:%v failed:%v", biz.JsonToString(wl), err)
				return err
			}
			wls = append(wls, &waterLevel)
		}

		// 创建带有元数据的上下文
		md := metadata.New(map[string]string{
			"trace_id": "mpp-taskscheduler",
		})
		reqCtx := metadata.NewOutgoingContext(context.Background(), md)

		req := &v1.UpdateWaterLevelRequest{ClusterId: j.clusterId, WaterLevels: wls, RequestId: NewUUID()}

		resp, err := client.UpdateWaterLevel(reqCtx, req)
		if err != nil {
			j.log.WithContext(ctx).Errorf("UpdateWaterLevel water level:%v failed:%v", biz.JsonToString(waterLevels), err)
			errors = append(errors, err)
			continue
		}

		j.log.WithContext(ctx).Debugf("UpdateWaterLevel water level:%v success. %v", biz.JsonToString(waterLevels), resp)
	}

	if len(errors) > 0 {
		return fmt.Errorf("multiple update failures: %+v", errors)
	}

	return nil
}

//
//func (j *jobManagerClusterApiHttp) UpdateWaterLevel(ctx context.Context, waterLevels []*biz.WaterLevel) error {
//	connHTTP, err := InitHttpConnection(ctx, j.addr)
//	if err != nil {
//		j.log.WithContext(ctx).Errorf("UpdateWaterLevel init connection failed:%v ", err)
//		return err
//	}
//	defer connHTTP.Close()
//
//	client := v1.NewClusterHTTPClient(connHTTP)
//
//	var wls []*v1.UpdateWaterLevelRequest_WaterLevel
//
//	for _, wl := range waterLevels {
//		waterLevel := v1.UpdateWaterLevelRequest_WaterLevel{}
//		err := copier.Copy(&waterLevel, &wl)
//		if nil != err {
//			j.log.WithContext(ctx).Errorf("UpdateWaterLevel copy water level:%v failed:%v", biz.JsonToString(wl), err)
//			return err
//		}
//		wls = append(wls, &waterLevel)
//	}
//
//	// 创建带有元数据的上下文
//	md := metadata.New(map[string]string{
//		"trace_id": "mpp-taskscheduler",
//	})
//	reqCtx := metadata.NewOutgoingContext(context.Background(), md)
//
//	req := &v1.UpdateWaterLevelRequest{ClusterId: j.clusterId, WaterLevels: wls, RequestId: NewUUID()}
//
//	resp, err := client.UpdateWaterLevel(reqCtx, req)
//	if err != nil {
//		j.log.WithContext(ctx).Errorf("UpdateWaterLevel water level:%v failed:%v", biz.JsonToString(waterLevels), err)
//		return err
//	}
//
//	j.log.WithContext(ctx).Debugf("UpdateWaterLevel water level:%v success. %v", biz.JsonToString(waterLevels), resp)
//
//	return nil
//}

func NewJobManagerClusterApi(c *conf.App, data *Data, logger log.Logger) biz.JobManagerClusterApi {
	return &jobManagerClusterApiHttp{
		data:      data,
		addr:      c.JobManager.Addr,
		addrs:     c.JobManager.Addrs,
		clusterId: c.ClusterId,
		log:       log.NewHelper(logger),
	}
}

func NewWaterLevelRepo(data *Data, logger log.Logger) biz.WaterLevelRepo {
	return &waterLevelRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (r *waterLevelRepo) Save(ctx context.Context, waterLevel *biz.WaterLevel) error {
	dbWaterLevel, err := r.toDBWaterLevel(ctx, waterLevel)
	if err != nil {
		return err
	}

	result := r.data.db.WithContext(ctx).Create(&dbWaterLevel)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("save water level:%v failed, err:%v", biz.JsonToString(waterLevel), err)
		return result.Error
	}

	return nil
}

func (r *waterLevelRepo) toDBWaterLevel(ctx context.Context, waterLevel *biz.WaterLevel) (WaterLevel, error) {
	dbWaterLevel := WaterLevel{}
	dbWaterLevel.CreateTime = time.Now()
	dbWaterLevel.ModifiedTime = time.Now()
	err := copier.Copy(&dbWaterLevel, waterLevel)
	if nil != err {
		r.log.WithContext(ctx).Errorf("save water level:%v failed, err:%v", biz.JsonToString(waterLevel), err)
		return WaterLevel{}, err
	}
	dbWaterLevel.ScheduleStrategy = biz.JsonToString(waterLevel.Strategy)
	return dbWaterLevel, nil
}

func (r *waterLevelRepo) FindLatest(ctx context.Context, w *biz.WaterLevel) (*biz.WaterLevel, error) {
	start := time.Now()
	defer func() {
		r.log.WithContext(ctx).Infof("FindLatest total took %v", time.Since(start))
	}()

	// 如果指定了完整的 Label，使用精确查询
	if w.Label != nil && len(w.Label) > 0 {
		return r.findLatestExact(ctx, w)
	}

	// 使用聚合查询，返回三元组下所有 Label 的聚合数据
	return r.findLatestAggregated(ctx, w)
}

// findLatestExact 精确查询，匹配完整的 Label
func (r *waterLevelRepo) findLatestExact(ctx context.Context, w *biz.WaterLevel) (*biz.WaterLevel, error) {
	query := r.buildBaseQuery(ctx, w)

	// Label 字段的 JSON 查询
	for key, value := range w.Label {
		if value != "" {
			query = query.Where("JSON_EXTRACT(label, ?) = ?", fmt.Sprintf("$.%s", key), value)
		} else {
			// 如果值为空，检查键是否存在
			query = query.Where("JSON_CONTAINS_PATH(label, 'one', ?)", fmt.Sprintf("$.%s", key))
		}
	}

	var dbWaterLevel WaterLevel
	result := query.Order("id DESC").Take(&dbWaterLevel)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("FindLatest exact water level query failed, conditions:%v, err:%v", biz.JsonToString(w), result.Error)
		return nil, result.Error
	}

	return r.toBizWaterLevel(ctx, w, nil, dbWaterLevel)
}

// findLatestAggregated 聚合查询，返回三元组下所有 Label 的聚合数据
func (r *waterLevelRepo) findLatestAggregated(ctx context.Context, w *biz.WaterLevel) (*biz.WaterLevel, error) {
	start := time.Now()
	defer func() {
		r.log.WithContext(ctx).Infof("findLatestAggregated took %v", time.Since(start))
	}()

	query := r.buildBaseQuery(ctx, w)

	// 直接查询最近20秒的记录，避免额外的MAX查询
	timeThreshold := time.Now().Add(-20 * time.Second)
	query = query.Where("gmt_stat_time >= ?", timeThreshold)

	r.log.WithContext(ctx).Infof("Querying records since: %v (last 20 seconds)", timeThreshold)

	// 限制查询数量，避免查询过多记录
	var dbWaterLevels []WaterLevel
	result := query.Order("gmt_stat_time DESC, id DESC").Limit(50).Find(&dbWaterLevels)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("FindLatest aggregated water level query failed, conditions:%v, err:%v", biz.JsonToString(w), result.Error)
		return nil, result.Error
	}

	r.log.WithContext(ctx).Infof("Found %d records for aggregation in last 20 seconds", len(dbWaterLevels))

	if len(dbWaterLevels) == 0 {
		r.log.WithContext(ctx).Warnf("FindLatest aggregated water level not found in last 20 seconds, conditions:%v", biz.JsonToString(w))
		return nil, fmt.Errorf("record not found")
	}

	// 按统计时间分组，取最新时间的所有记录
	latestRecords := r.getLatestRecordsByTime(dbWaterLevels)
	r.log.WithContext(ctx).Infof("Latest records count: %d", len(latestRecords))

	// 聚合所有记录
	return r.aggregateWaterLevels(ctx, latestRecords)
}

func (r *waterLevelRepo) toBizWaterLevel(ctx context.Context, w *biz.WaterLevel, err error, dbWaterLevel WaterLevel) (*biz.WaterLevel, error) {
	waterLevel := biz.WaterLevel{}
	err = copier.Copy(&waterLevel, &dbWaterLevel)
	if nil != err {
		r.log.WithContext(ctx).Errorf("FindLatest water level:%v failed, err:%v", biz.JsonToString(w), err)
		return nil, err
	}
	err = json.Unmarshal([]byte(dbWaterLevel.ScheduleStrategy), &waterLevel.Strategy)
	if err != nil {
		return nil, err
	}
	return &waterLevel, nil
}

func (r *waterLevelRepo) ListAll(ctx context.Context, w *biz.WaterLevel, limit int) ([]*biz.WaterLevel, error) {
	// 构建查询条件
	query := r.data.db.WithContext(ctx).Model(&WaterLevel{})

	// 基础字段过滤
	if w.Product != "" {
		query = query.Where("product = ?", w.Product)
	}
	if w.EngineModel != "" {
		query = query.Where("engine_model = ?", w.EngineModel)
	}
	if w.Tag != "" {
		query = query.Where("tag = ?", w.Tag)
	}
	if w.Type != "" {
		query = query.Where("type = ?", w.Type)
	}
	if w.Dominant != "" {
		query = query.Where("dominant = ?", w.Dominant)
	}

	// Label 字段的 JSON 查询
	if w.Label != nil && len(w.Label) > 0 {
		for key, value := range w.Label {
			if value != "" {
				query = query.Where("JSON_EXTRACT(label, ?) = ?", fmt.Sprintf("$.%s", key), value)
			} else {
				// 如果值为空，检查键是否存在
				query = query.Where("JSON_CONTAINS_PATH(label, 'one', ?)", fmt.Sprintf("$.%s", key))
			}
		}
	}

	var ss []WaterLevel
	result := query.Order("id DESC").Limit(limit).Find(&ss)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("ListAll water level query failed, conditions:%v, err:%v", biz.JsonToString(w), result.Error)
		return nil, result.Error
	}

	var waterLevels []*biz.WaterLevel
	for _, dbWaterLevel := range ss {
		waterLevel := biz.WaterLevel{}
		err := copier.Copy(&waterLevel, &dbWaterLevel)
		if nil != err {
			r.log.WithContext(ctx).Errorf("ListAll water level copy failed, err:%v", err)
			return nil, err
		}

		// 解析 JSON 字段
		if dbWaterLevel.ScheduleStrategy != "" {
			err = json.Unmarshal([]byte(dbWaterLevel.ScheduleStrategy), &waterLevel.Strategy)
			if err != nil {
				r.log.WithContext(ctx).Warnf("parse strategy failed, strategy:%s, err:%v", dbWaterLevel.ScheduleStrategy, err)
			}
		}

		waterLevels = append(waterLevels, &waterLevel)
	}
	return waterLevels, nil
}

// buildBaseQuery 构建基础查询条件（不包含 Label）
func (r *waterLevelRepo) buildBaseQuery(ctx context.Context, w *biz.WaterLevel) *gorm.DB {
	query := r.data.db.WithContext(ctx).Model(&WaterLevel{})

	// 基础字段过滤
	if w.Product != "" {
		query = query.Where("product = ?", w.Product)
	}
	if w.EngineModel != "" {
		query = query.Where("engine_model = ?", w.EngineModel)
	}
	if w.Tag != "" {
		query = query.Where("tag = ?", w.Tag)
	}
	if w.Type != "" {
		query = query.Where("type = ?", w.Type)
	}
	if w.Dominant != "" {
		query = query.Where("dominant = ?", w.Dominant)
	}

	return query
}

// getLatestRecordsByTime 按统计时间分组，返回最新时间的所有记录
func (r *waterLevelRepo) getLatestRecordsByTime(records []WaterLevel) []WaterLevel {
	if len(records) == 0 {
		return records
	}

	// 找到最新的统计时间
	latestTime := records[0].StatTime
	for _, record := range records {
		if record.StatTime.After(latestTime) {
			latestTime = record.StatTime
		}
	}

	// 返回最新时间的所有记录
	var latestRecords []WaterLevel
	for _, record := range records {
		// 使用时间差小于1秒来判断是否为同一批统计
		if latestTime.Sub(record.StatTime).Abs() < time.Second {
			latestRecords = append(latestRecords, record)
		}
	}

	return latestRecords
}

// aggregateWaterLevels 聚合多条水位记录
func (r *waterLevelRepo) aggregateWaterLevels(ctx context.Context, records []WaterLevel) (*biz.WaterLevel, error) {
	if len(records) == 0 {
		return nil, fmt.Errorf("no records to aggregate")
	}

	// 以第一条记录为基础
	base := records[0]
	aggregated := &biz.WaterLevel{}
	err := copier.Copy(aggregated, &base)
	if err != nil {
		r.log.WithContext(ctx).Errorf("copy base water level failed, err:%v", err)
		return nil, err
	}

	// 解析基础记录的 Strategy
	if base.ScheduleStrategy != "" {
		err = json.Unmarshal([]byte(base.ScheduleStrategy), &aggregated.Strategy)
		if err != nil {
			r.log.WithContext(ctx).Warnf("parse base strategy failed, strategy:%s, err:%v", base.ScheduleStrategy, err)
		}
	}

	// 初始化聚合字段
	aggregated.TotalQuota = make(biz.QuotaSet)
	aggregated.AllocQuota = make(biz.QuotaSet)
	aggregated.RealQuota = make(biz.QuotaSet)
	aggregated.ScheduleQuota = make(biz.QuotaSet)
	aggregated.AvgQuota = make(biz.QuotaSet)
	aggregated.TaskNum = 0
	aggregated.Label = nil // 聚合数据不保留具体的 Label

	// 聚合所有记录的配额数据
	for _, record := range records {
		aggregated.TotalQuota.Add(record.TotalQuota)
		aggregated.AllocQuota.Add(record.AllocQuota)
		aggregated.RealQuota.Add(record.RealQuota)
		aggregated.ScheduleQuota.Add(record.ScheduleQuota)
		aggregated.AvgQuota.Add(record.AvgQuota)
		aggregated.TaskNum += record.TaskNum
	}

	// 重新计算水位值
	r.recalculateWaterLevel(aggregated)

	r.log.WithContext(ctx).Infof("aggregated %d water level records, final water level: %f", len(records), aggregated.WaterLevel)
	return aggregated, nil
}

// recalculateWaterLevel 重新计算水位值
func (r *waterLevelRepo) recalculateWaterLevel(waterLevel *biz.WaterLevel) {
	if waterLevel.Strategy == nil || len(waterLevel.Strategy.Dominants) == 0 {
		r.log.Warnf("recalculate water level failed: no strategy or dominants")
		return
	}

	dominant := waterLevel.Strategy.Dominants[0]
	ratio, ok := waterLevel.Strategy.LimitRatio[dominant]
	if !ok {
		r.log.Warnf("recalculate water level failed: no limit ratio for dominant %s", dominant)
		return
	}

	totalQuota, ok := waterLevel.TotalQuota[dominant]
	if !ok || totalQuota <= 0 {
		r.log.Warnf("recalculate water level failed: no total quota for dominant %s", dominant)
		return
	}

	var quota int64
	if waterLevel.Type == "real" {
		quota = waterLevel.RealQuota[dominant]
	} else {
		quota = waterLevel.AllocQuota[dominant]
	}

	waterLevel.Used = quota
	waterLevel.Total = totalQuota * ratio / 100
	if waterLevel.Total > 0 {
		waterLevel.WaterLevel = float32(waterLevel.Used) / float32(waterLevel.Total)
	} else {
		waterLevel.WaterLevel = 0
	}
	waterLevel.Dominant = dominant
}
