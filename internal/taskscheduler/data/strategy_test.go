package data_test

import (
	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/data"
	"mpp/internal/taskscheduler/testdata"

	"github.com/go-kratos/kratos/v2/log"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Strategy", func() {
	var ro biz.StrategyRepo
	var uD *biz.Strategy
	var uDList []*biz.Strategy
	BeforeEach(func() {
		// 这里的 Db 是 data_suite_test.go 文件里面定义的
		ro = data.NewStrategyRepo(Db, log.GetLogger())
		// 这里你可以引入外部组装好的数据
		uD = testdata.Strategy()

		uDList = testdata.StrategyList(10)
	})

	// List策略
	It("ListStrategy", func() {
		for _, strategy := range uDList {
			_, err := ro.Save(ctx, strategy)
			Ω(err).ShouldNot(HaveOccurred())
		}

		strategies, err := ro.ListAll(ctx)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(len(strategies)).Should(Equal(len(uDList)))
		for i, strategy := range strategies {
			Ω(strategy).Should(Equal(uDList[i]))
		}
	})

	// 创建策略
	It("CreateStrategy", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		strategy, err := ro.FindByStrategyId(ctx, uD.StrategyId)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(strategy).Should(Equal(uD))
	})

	// 更新状态、或者删除策略
	It("UpdateStrategyStatus", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		log.Infof("update strategy to invalid start.")
		err = ro.UpdateStatus(ctx, uD.StrategyId, biz.StrategyStatusInvalid)
		Ω(err).ShouldNot(HaveOccurred())

		strategy, err := ro.FindByStrategyId(ctx, uD.StrategyId)
		Ω(err).Should(HaveOccurred())
		Ω(strategy).Should(BeNil())
		log.Infof("update strategy to invalid success.")

		log.Infof("update strategy to valid start.")
		err = ro.UpdateStatus(ctx, uD.StrategyId, biz.StrategyStatusValid)
		Ω(err).ShouldNot(HaveOccurred())

		strategy, err = ro.FindByStrategyId(ctx, uD.StrategyId)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(strategy).Should(Equal(uD))
	})

	// 更新策略
	It("UpdateStrategy", func() {
		_, err := ro.Save(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		uD.Strategy = biz.StrategyCentral
		uD.Callback = "http://www.taobao.com"
		uD.Config = &biz.StrategyConfig{
			ScheduleResourceType: biz.ScheduleResourceTypeReal,
			LimitRatio:           map[string]int64{"*": 85},
			MaxTaskNum:           700,
			LoadRatio:            85,
			Dominants:            []string{"cpu", "disk"},
		}
		uD.Extend = map[string]interface{}{"extend2": "value2"}
		err = ro.Update(ctx, uD)
		Ω(err).ShouldNot(HaveOccurred())

		strategy, err := ro.FindByStrategyId(ctx, uD.StrategyId)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(strategy).Should(Equal(uD))
	})
})
