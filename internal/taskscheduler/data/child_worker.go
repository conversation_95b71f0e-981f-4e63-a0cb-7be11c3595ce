package data

import (
	"context"
	"fmt"
	"time"

	"mpp/internal/taskscheduler/biz"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

type ChildWorker struct {
	DBBaseModel
	WorkerId       string `gorm:"column:worker_id;uniqueIndex;type:varchar(64);NOT NULL;COMMENT:唯一ID"`
	ParentWorkerId string `gorm:"column:parent_worker_id;type:varchar(64);NOT NULL;index:idx_parent_worker_id;;COMMENT:父workerID"`
	PodName        string `gorm:"column:pod_name;type:varchar(128);COMMENT:worker pod 名称"`
	NodeName       string `gorm:"column:node_name;type:varchar(128);COMMENT:worker node 名称"`
	Ip             string `gorm:"type:varchar(64);NOT NULL;index:idx_ip;COMMENT:IP地址"`
	Port           int32  `gorm:"DEFAULT:80;COMMENT:worker监听端口"`
	Product        string `gorm:"type:varchar(32);NOT NULL;COMMENT:产品"`
	EngineModel    string `gorm:"column:engine_model;type:varchar(32);NOT NULL;COMMENT:引擎模型"`
	Tag            string `gorm:"type:varchar(32);NOT NULL;COMMENT:资源组标签"`
	//parent_product, parent_engine_model, parent_tag
	ParentProduct      string       `gorm:"column:parent_product;type:varchar(32);COMMENT:父产品"`
	ParentEngineModel  string       `gorm:"column:parent_engine_model;type:varchar(32);COMMENT:父引擎模型"`
	ParentTag          string       `gorm:"column:parent_tag;type:varchar(32);COMMENT:父资源组标签"`
	Version            string       `gorm:"type:varchar(32);NOT NULL;COMMENT:引擎版本"`
	HeartBeatTime      time.Time    `gorm:"column:gmt_heartbeat;type:datetime;NOT NULL;COMMENT:最近心跳时间"`
	Status             int8         `gorm:"default:1;index:idx_status;COMMENT:状态：0-不在线；1-在线"`
	OnlineVersion      int32        `gorm:"column:online_version;DEFAULT:0;COMMENT:每次上线加1"`
	UpdateQuotaVersion uint64       `gorm:"column:update_quota_version;DEFAULT:0;COMMENT:每次更新加1"`
	Quota              biz.QuotaSet `gorm:"column:quota;type:json;NOT NULL;COMMENT:多维度quota上限值"`
	RealQuota          biz.QuotaSet `gorm:"column:real_quota;type:json;COMMENT:多维度quota实际值"`
	AllocQuota         biz.QuotaSet `gorm:"column:alloc_quota;type:json;COMMENT:多维度quot申请值"`
	AvgQuota           biz.QuotaSet `gorm:"column:avg_quota;type:json;COMMENT:多维度quot平均值"`
	ScheduleQuota      biz.QuotaSet `gorm:"column:schedule_quota;type:json;COMMENT:多维度quot调度值"`
	ScheduleConfig     string       `gorm:"column:schedule_config;type:json;COMMENT:调度配置"`
	Label              MAPSTRING    `gorm:"type:json;COMMENT:标签组"`
	WorkerTaint        string       `gorm:"column:taint;type:json;COMMENT:污点组"`
	Load               float32      `gorm:"type:decimal(10,2);column:load;COMMENT:worker负载"`
	LatestAllocTime    time.Time    `gorm:"column:gmt_latest_alloc;type:datetime;NOT NULL;DEFAULT:CURRENT_TIMESTAMP"`
	ConnectMode        LISTSTRING   `gorm:"column:connect_mode;type:json;COMMENT:访问worker模式连接模式，direct:直连，gateway:网关代理"`
	TaskNum            int32        `gorm:"column:task_num;DEFAULT:0;COMMENT:worker任务数"`
	Extend             MAPOBJECT    `gorm:"type:json;COMMENT:扩展字段"`
	MetaData           MAPSTRING    `gorm:"column:metadata;type:json;COMMENT:metadata"`
}

type childWorkerRepo struct {
	data *Data
	log  *log.Helper
}

func NewChildWorkerRepo(data *Data, logger log.Logger) biz.ChildWorkerRepo {
	wr := &childWorkerRepo{
		data: data,
		log:  log.NewHelper(logger),
	}

	return wr
}

func (r *childWorkerRepo) Save(ctx context.Context, w *biz.ChildWorker) (*biz.ChildWorker, error) {
	result := r.data.db.WithContext(ctx).Exec(
		"INSERT INTO child_workers(gmt_create, gmt_modified, worker_id, parent_worker_id, pod_name, node_name, ip, port, product, engine_model, tag, parent_product, parent_engine_model, parent_tag, version, gmt_heartbeat, status, online_version, update_quota_version, quota, alloc_quota, avg_quota, real_quota, schedule_quota, schedule_config, gmt_latest_alloc, label, taint, `load`, connect_mode, extend, metadata, task_num) "+
			"VALUES(now(), now(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, now(), ?, 0, ?, ?, ?, ?, ?, ?, ?, now(), ?, ?, ?, ?, ?, ?, 0) "+
			"ON DUPLICATE KEY UPDATE parent_worker_id = ?, version = ?, pod_name = ?, node_name = ?, ip = ?, port = ?, product = ?, engine_model = ?, tag = ?, quota = ?, alloc_quota = ?, avg_quota = ?, real_quota = ?, schedule_quota = ?, schedule_config = ?, status = ?, update_quota_version = ?, label = ?, taint = ?, `load` = ?, connect_mode = ?, extend = ?, metadata = ?, online_version = online_version+1, gmt_heartbeat = now(), gmt_modified = now(), task_num = 0",
		w.WorkerId, w.ParentWorkerId, w.PodName, w.NodeName, w.Ip, w.Port, w.Product, w.EngineModel, w.Tag, w.ParentProduct, w.ParentEngineModel, w.ParentTag, w.Version, biz.WorkerStatusOnline, w.UpdateQuotaVersion, w.Quota, w.AllocQuota, w.AvgQuota, w.RealQuota, w.ScheduleQuota, biz.JsonToString(w.ScheduleConfig), MAPSTRING(w.Label), toWorkerTaint(w.Taint), w.Load, biz.JsonToString(w.ConnectMode), MAPOBJECT(w.Extend), MAPSTRING(w.Metadata),
		w.ParentWorkerId, w.Version, w.PodName, w.NodeName, w.Ip, w.Port, w.Product, w.EngineModel, w.Tag, w.Quota, w.AllocQuota, w.AvgQuota, w.RealQuota, w.ScheduleQuota, biz.JsonToString(w.ScheduleConfig), biz.WorkerStatusOnline, w.UpdateQuotaVersion, MAPSTRING(w.Label), toWorkerTaint(w.Taint), w.Load, biz.JsonToString(w.ConnectMode), MAPOBJECT(w.Extend), MAPSTRING(w.Metadata),
	)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("save worker:%v failed, err:%v", biz.JsonToString(w), result.Error)
		return nil, result.Error
	}

	return w, nil
}

func (r *childWorkerRepo) UpdateHeartbeat(ctx context.Context, w *biz.ChildWorker, isUpdateSchedule bool) error {
	data := map[string]interface{}{
		"Load": w.Load,
		//动态更新totalQuota
		//"Quota":              w.Quota,
		"RealQuota":          w.RealQuota,
		"AvgQuota":           w.AvgQuota,
		"ModifiedTime":       gorm.Expr("CURRENT_TIMESTAMP"),
		"HeartBeatTime":      gorm.Expr("CURRENT_TIMESTAMP"),
		"UpdateQuotaVersion": gorm.Expr("update_quota_version + 1"),
	}
	//如果心跳中包含totalQuota,同步更新
	if w.Quota != nil && len(w.Quota) > 0 {
		data["Quota"] = w.Quota
	}
	if isUpdateSchedule {
		data["ScheduleQuota"] = w.ScheduleQuota
		data["AllocQuota"] = w.AllocQuota
		data["TaskNum"] = w.TaskNum
		data["LatestAllocTime"] = gorm.Expr("CURRENT_TIMESTAMP")
	}
	if w.Metadata != nil && len(w.Metadata) > 0 {
		data["Metadata"] = MAPSTRING(w.Metadata)
	}

	// 增加、删除，嵌套
	r.appendTaint(w.Taint, data)

	result := r.data.db.WithContext(ctx).Model(&ChildWorker{}).Where("worker_id = ? AND status = ?", w.WorkerId, biz.WorkerStatusOnline).Updates(
		data)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateHeartbeat worker:%v failed, result:%v", biz.JsonToString(w), result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("update heartbeat worker:%v failed, affected rows is 0", biz.JsonToString(w))
		return biz.ErrNoRegister
	}

	return nil
}

func (r *childWorkerRepo) UpdateChangeQuota(ctx context.Context, workerId string, quota biz.QuotaSet, scheduleConfig *biz.StrategyConfig, opt string) (bool, error) {
	dominant := scheduleConfig.Dominants[0]
	var result *gorm.DB
	if scheduleConfig.ScheduleResourceType == biz.ScheduleResourceTypeReal {
		if opt == biz.QuotaOptAdd {
			sql := fmt.Sprintf("UPDATE workers SET task_num=task_num+1, alloc_quota = JSON_SET(alloc_quota, '$.%v', CAST(alloc_quota->>'$.%v' AS SIGNED)+?), real_quota = JSON_SET(real_quota, '$.%v', CAST(real_quota->>'$.%v' AS SIGNED)+?), schedule_quota = JSON_SET(schedule_quota, '$.%v', CAST(schedule_quota->>'$.%v' AS SIGNED)-?), update_quota_version = update_quota_version + 1, gmt_modified = now(), gmt_latest_alloc = now() WHERE worker_id = ? AND status = ? AND quota->>'$.%v'*?/100 - alloc_quota->>'$.%v' >= ?",
				dominant, dominant, dominant, dominant, dominant, dominant, dominant, dominant)
			result = r.data.db.Exec(
				sql, quota[dominant], quota[dominant], quota[dominant], workerId, biz.WorkerStatusOnline, scheduleConfig.LimitRatio[dominant], quota[dominant],
			)
		} else {
			sql := fmt.Sprintf("UPDATE workers SET task_num=task_num-1, alloc_quota = JSON_SET(alloc_quota, '$.%v', CAST(alloc_quota->>'$.%v' AS SIGNED)-?), update_quota_version = update_quota_version + 1, gmt_modified = now(), gmt_latest_alloc = now() WHERE worker_id = ? AND status = ?",
				dominant, dominant)
			result = r.data.db.Exec(
				sql, quota[dominant], workerId, biz.WorkerStatusOnline,
			)
		}

	} else {
		if opt == biz.QuotaOptAdd {
			sql := fmt.Sprintf("UPDATE workers SET task_num=task_num+1, alloc_quota = JSON_SET(alloc_quota, '$.%v', CAST(alloc_quota->>'$.%v' AS SIGNED)+?), schedule_quota = JSON_SET(schedule_quota, '$.%v', CAST(schedule_quota->>'$.%v' AS SIGNED)-?), update_quota_version = update_quota_version + 1, gmt_modified = now(), gmt_latest_alloc = now() WHERE worker_id = ? AND status = ? AND quota->>'$.%v'*?/100 - alloc_quota->>'$.%v' >= ?",
				dominant, dominant, dominant, dominant, dominant, dominant)
			result = r.data.db.Exec(
				sql, quota[dominant], quota[dominant], workerId, biz.WorkerStatusOnline, scheduleConfig.LimitRatio[dominant], quota[dominant],
			)
		} else {
			sql := fmt.Sprintf("UPDATE workers SET task_num=task_num-1, alloc_quota = JSON_SET(alloc_quota, '$.%v', CAST(alloc_quota->>'$.%v' AS SIGNED)-?), schedule_quota = JSON_SET(schedule_quota, '$.%v', CAST(schedule_quota->>'$.%v' AS SIGNED)+?), update_quota_version = update_quota_version + 1, gmt_modified = now(), gmt_latest_alloc = now() WHERE worker_id = ? AND status = ?",
				dominant, dominant, dominant, dominant)
			result = r.data.db.Exec(
				sql, quota[dominant], quota[dominant], workerId, biz.WorkerStatusOnline,
			)
		}

	}

	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateChangeQuota worker:%v failed, result:%v", workerId, result)
		return false, result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("UpdateChangeQuota worker:%v failed, affected rows is 0", workerId)
		return false, nil
	}

	return true, nil
}

func (r *childWorkerRepo) UpdateQuota(ctx context.Context, w *biz.ChildWorker, opt string) error {
	data := map[string]interface{}{
		"AllocQuota":         w.AllocQuota,
		"ScheduleQuota":      w.ScheduleQuota,
		"ModifiedTime":       gorm.Expr("CURRENT_TIMESTAMP"),
		"UpdateQuotaVersion": gorm.Expr("update_quota_version + 1"),
		"TaskNum":            w.TaskNum,
	}

	// 申请资源，更新最后申请时间
	if opt == biz.QuotaOptAdd {
		data["LatestAllocTime"] = gorm.Expr("CURRENT_TIMESTAMP")
	}

	result := r.data.db.WithContext(ctx).Model(&ChildWorker{}).Where("worker_id = ? AND status = ?", w.WorkerId, biz.WorkerStatusOnline).Updates(data)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateQuota worker:%v failed, result:%v", biz.JsonToString(w), result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("update quota worker:%v failed, affected rows is 0", biz.JsonToString(w))
		return nil
	}

	return nil
}

func (r *childWorkerRepo) BatchUpdateStatus(ctx context.Context, workerIds []string, status int8) error {
	data := map[string]interface{}{
		"Status":       status,
		"ModifiedTime": gorm.Expr("CURRENT_TIMESTAMP"),
	}

	result := r.data.db.WithContext(ctx).Model(&ChildWorker{}).Where("worker_id in ?", workerIds).Updates(data)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("BatchUpdateStatus worker:%v failed, result:%v", workerIds, result)
		return result.Error
	}

	if result.RowsAffected != int64(len(workerIds)) {
		r.log.WithContext(ctx).Warnf("batch update status no equal. workerIds:%v status:%v workerIds len:%v affected rows is %v", workerIds,
			status, len(workerIds), result.RowsAffected)
		return nil
	}

	return nil
}

func (r *childWorkerRepo) BatchUpdateStatusByParentWorkerIds(ctx context.Context, parentWorkerIds []string, status int8) error {
	data := map[string]interface{}{
		"Status":       status,
		"ModifiedTime": gorm.Expr("CURRENT_TIMESTAMP"),
	}
	result := r.data.db.WithContext(ctx).Model(&ChildWorker{}).Where("parent_worker_id in ?", parentWorkerIds).Updates(data)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("BatchUpdateStatusByParentWorkerIds parentWorkerIds:%v failed, result:%v", parentWorkerIds, result)
		return result.Error
	}
	if result.RowsAffected != int64(len(parentWorkerIds)) {
		r.log.WithContext(ctx).Warnf("batch update status no equal. parentWorkerIds:%v status:%v parentWorkerIds len:%v affected rows is %v", parentWorkerIds,
			status, len(parentWorkerIds), result.RowsAffected)
		return nil
	}
	return nil
}

func (r *childWorkerRepo) UpdateStatus(ctx context.Context, workerId string, status int8) error {
	data := map[string]interface{}{
		"Status":             status,
		"UpdateQuotaVersion": gorm.Expr("update_quota_version + 1"),
		"ModifiedTime":       gorm.Expr("CURRENT_TIMESTAMP"),
	}

	result := r.data.db.WithContext(ctx).Model(&ChildWorker{}).Where("worker_id = ?", workerId).Updates(data)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateStatus worker:%v failed, result:%v", workerId, result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("update status worker:%v status:%v failed, affected rows is 0", workerId, status)
		return biz.ErrWorkerNotFound
	}

	return nil
}

func (r *childWorkerRepo) UpdateLabel(ctx context.Context, w *biz.ChildWorker) error {
	data := map[string]interface{}{
		"Label":              MAPSTRING(w.Label),
		"UpdateQuotaVersion": gorm.Expr("update_quota_version + 1"),
		"ModifiedTime":       gorm.Expr("CURRENT_TIMESTAMP"),
	}

	result := r.data.db.WithContext(ctx).Model(&ChildWorker{}).Where("worker_id = ? AND status = ?", w.WorkerId, biz.WorkerStatusOnline).Updates(data)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateLabel worker:%v failed, result:%v", biz.JsonToString(w), result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("update label worker:%v failed, affected rows is 0", biz.JsonToString(w))
		return nil
	}

	return nil
}

func (r *childWorkerRepo) UpdateTaint(ctx context.Context, workerIds []string, workerIps []string, taint []*biz.Taint) error {
	query := map[string]interface{}{}
	if workerIds != nil {
		query["worker_id"] = workerIds
	} else {
		query["ip"] = workerIps
	}

	data := map[string]interface{}{
		"ModifiedTime":       gorm.Expr("CURRENT_TIMESTAMP"),
		"UpdateQuotaVersion": gorm.Expr("update_quota_version + 1"),
	}

	// 增加、删除，嵌套
	r.appendTaint(taint, data)

	result := r.data.db.WithContext(ctx).Model(&ChildWorker{}).Where(query).Updates(data)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateTaint workerIds:%v workerIps:%v taint:%v failed, result:%v",
			workerIds, workerIps, taint, result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("UpdateTaint taint workerIds:%v workerIps:%v taint:%v failed, affected rows is 0",
			workerIds, workerIps, taint)
		return biz.ErrWorkerNotFound
	}

	return nil
}

func (r *childWorkerRepo) appendTaint(taint []*biz.Taint, data map[string]interface{}) {
	if taint != nil && len(taint) > 0 {
		exp := "taint"
		for _, taint := range taint {
			if taint.Effect == "Delete" {
				exp = fmt.Sprintf("JSON_REMOVE(%s, '$.\"%s\"')", exp, taint.Key)
			} else {
				exp = fmt.Sprintf("JSON_SET(%s, '$.\"%s\"', CAST('%s' AS JSON))", exp, taint.Key, biz.JsonToString(taint))
			}
		}
		data["WorkerTaint"] = gorm.Expr(exp)
	}
}

func (r *childWorkerRepo) UpdateTagByIps(ctx context.Context, ips []string, tag string) error {
	result := r.data.db.WithContext(ctx).Exec("UPDATE workers SET tag = ?, gmt_modified = now() where status = ? AND ip IN ?", tag,
		biz.WorkerStatusOnline, ips)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateTagByIps ips:%v tag:%v failed, result:%v", ips, tag, result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("update tag by ips:%v tag:%v failed, affected rows is 0", ips, tag)
		return nil
	}

	return nil
}

func (r *childWorkerRepo) UpdateTagByWorkerIds(ctx context.Context, workerIds []string, tag string) error {
	result := r.data.db.WithContext(ctx).Exec("UPDATE workers SET tag = ?, gmt_modified = now() where status = ? AND worker_id IN ?", tag,
		biz.WorkerStatusOnline, workerIds)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("UpdateTagByWorkerIds workerIds:%v tag:%v failed, result:%v", workerIds, tag, result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("update tag by workerIds:%v tag:%v failed, affected rows is 0", workerIds, tag)
		return nil
	}

	return nil
}

func (r *childWorkerRepo) FindByWorkerId(ctx context.Context, workerId string, status int8) (*biz.ChildWorker, error) {
	var worker ChildWorker
	result := r.data.db.WithContext(ctx).Where("worker_id = ? AND status = ?", workerId, status).Take(&worker)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("FindByWorkerId workerId:%v failed, result:%v", workerId, result)
		if result.Error == gorm.ErrRecordNotFound {
			return nil, biz.ErrWorkerNotFound
		} else {
			return nil, result.Error
		}
	}

	w, err := r.coverDbWorkerToBizWorker(ctx, &worker)
	if err != nil {
		return nil, err
	}

	return w, nil
}

func (r *childWorkerRepo) coverDbWorkerToBizWorker(ctx context.Context, worker *ChildWorker) (*biz.ChildWorker, error) {
	w := biz.ChildWorker{}
	err := copier.Copy(&w, worker)
	if nil != err {
		r.log.WithContext(ctx).Warnf("FindByWorkerId worker:%v failed, err:%v", biz.JsonToString(w), err)
		return nil, err
	}

	w.Taint = toBizTaint(worker.WorkerTaint)
	return &w, nil
}

// todo ip能查出多个
func (r *childWorkerRepo) FindByIp(ctx context.Context, ip string) (*biz.ChildWorker, error) {
	var worker ChildWorker
	result := r.data.db.WithContext(ctx).Where("status = ? AND ip = ?", biz.WorkerStatusOnline, ip).Take(&worker)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("FindByIp ip:%v failed, result:%v", ip, result)
		return nil, result.Error
	}

	w, err := r.coverDbWorkerToBizWorker(ctx, &worker)
	if err != nil {
		return nil, err
	}

	return w, nil
}

func (r *childWorkerRepo) ListOnlineWorker(ctx context.Context) ([]*biz.ChildWorker, error) {
	var ws []ChildWorker
	result := r.data.db.Where("status = ?", biz.WorkerStatusOnline).Find(&ws)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("ListAll failed, result:%v", result)
		return nil, result.Error
	}

	var workers []*biz.ChildWorker

	for _, w := range ws {
		worker, err := r.coverDbWorkerToBizWorker(ctx, &w)
		if err != nil {
			return nil, err
		}

		workers = append(workers, worker)
	}
	return workers, nil
}

func (r *childWorkerRepo) ListByConditions(ctx context.Context, product string, engineModel string, tag string, ips []string) ([]*biz.ChildWorker, error) {
	var ws []ChildWorker

	filters := map[string]interface{}{}
	filters["status"] = biz.WorkerStatusOnline
	if product != "" {
		filters["product"] = product
	}

	if engineModel != "" {
		filters["engine_model"] = engineModel
	}

	if tag != "" {
		filters["tag"] = tag
	}

	result := r.data.db.WithContext(ctx).Where(filters)
	if ips != nil && len(ips) > 0 {
		result = result.Where("ip IN ?", ips)
	}

	result = result.Find(&ws)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("ListByConditions failed, product:%v engineModel:%v tag:%v ips:%v result:%v",
			product, engineModel, tag, ips, result)
		return nil, result.Error
	}

	var workers []*biz.ChildWorker

	for _, w := range ws {
		worker, err := r.coverDbWorkerToBizWorker(ctx, &w)
		if err != nil {
			return nil, err
		}
		workers = append(workers, worker)
	}

	return workers, nil
}

func (r *childWorkerRepo) ListByWorkerIds(ctx context.Context, workerIds []string) ([]*biz.ChildWorker, error) {
	var ws []ChildWorker
	result := r.data.db.WithContext(ctx).Where("status = ? and worker_id IN ?", biz.WorkerStatusOnline, workerIds).Find(&ws)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("ListByWorkerIds failed, workerIds:%v result:%v", workerIds, result)
		return nil, result.Error
	}

	var workers []*biz.ChildWorker

	for _, w := range ws {
		worker, err := r.coverDbWorkerToBizWorker(ctx, &w)
		if err != nil {
			return nil, err
		}
		workers = append(workers, worker)
	}
	return workers, nil
}

func (r *childWorkerRepo) ListByIps(ctx context.Context, ips []string) ([]*biz.ChildWorker, error) {
	var ws []ChildWorker
	result := r.data.db.WithContext(ctx).Where("status = ? and ip IN ?", biz.WorkerStatusOnline, ips).Find(&ws)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("ListByIps failed, ips:%v result:%v", ips, result)
		return nil, result.Error
	}

	var workers []*biz.ChildWorker

	for _, w := range ws {
		worker, err := r.coverDbWorkerToBizWorker(ctx, &w)
		if err != nil {
			return nil, err
		}
		workers = append(workers, worker)
	}
	return workers, nil
}
