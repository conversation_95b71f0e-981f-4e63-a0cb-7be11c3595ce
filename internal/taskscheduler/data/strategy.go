package data

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"

	"mpp/internal/taskscheduler/biz"
)

type Strategy struct {
	DBBaseModel
	StrategyId     string    `gorm:"column:strategy_id;uniqueIndex;type:varchar(64);NOT NULL;COMMENT:策略唯一ID"`
	Product        string    `gorm:"column:product;NOT NULL;COMMENT:产品"`
	EngineModel    string    `gorm:"column:engine_model;NOT NULL;COMMENT:引擎模型"`
	Tag            string    `gorm:"column:tag;NOT NULL;COMMENT:标签"`
	Label          MAPSTRING `gorm:"type:json;COMMENT:标签组，不同的标签组，可以定义不同的策略"`
	Strategy       string    `gorm:"column:strategy;NOT NULL;COMMENT:策略"`
	Callback       string    `gorm:"column:callback;COMMENT:回调地址"`
	ScheduleConfig string    `gorm:"column:config;type:json;COMMENT:配置"`
	Status         int8      `gorm:"default:1;index:idx_status;COMMENT:状态：0-无效；1-有效"`
	Extend         MAPOBJECT `gorm:"column:extend;type:json;COMMENT:扩展字段"`
}

type strategyRepo struct {
	data *Data
	log  *log.Helper
}

func NewStrategyRepo(data *Data, logger log.Logger) biz.StrategyRepo {
	return &strategyRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (r *strategyRepo) Save(ctx context.Context, strategy *biz.Strategy) (*biz.Strategy, error) {
	dbStrategy := Strategy{}
	err := copier.Copy(&dbStrategy, strategy)
	if nil != err {
		r.log.WithContext(ctx).Errorf("strategy:%v copy failed, err:%v", biz.JsonToString(strategy), err)
		return nil, err
	}
	dbStrategy.ScheduleConfig = biz.JsonToString(strategy.Config)

	result := r.data.db.WithContext(ctx).Exec(
		"INSERT INTO strategies(gmt_create, gmt_modified, strategy_id, product, engine_model, tag, label, strategy, config, callback, status, extend) "+
			"VALUES(now(), now(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) "+
			"ON DUPLICATE KEY UPDATE status = ?, strategy = ?, config = ?, callback = ?, extend = ?, gmt_modified = now()",
		dbStrategy.StrategyId, dbStrategy.Product, dbStrategy.EngineModel, dbStrategy.Tag, dbStrategy.Label,
		dbStrategy.Strategy, dbStrategy.ScheduleConfig, dbStrategy.Callback, biz.StrategyStatusValid, dbStrategy.Extend,
		biz.StrategyStatusValid, dbStrategy.Strategy, dbStrategy.ScheduleConfig, dbStrategy.Callback, dbStrategy.Extend,
	)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("strategy:%v save failed, err:%v", biz.JsonToString(strategy), err)
		return nil, result.Error
	}

	return strategy, nil
}

func (r *strategyRepo) Update(ctx context.Context, strategy *biz.Strategy) error {
	data := map[string]interface{}{
		"Strategy":     strategy.Strategy,
		"Callback":     strategy.Callback,
		"Config":       biz.JsonToString(strategy.Config),
		"Extend":       MAPOBJECT(strategy.Extend),
		"ModifiedTime": gorm.Expr("CURRENT_TIMESTAMP"),
	}

	result := r.data.db.WithContext(ctx).Model(&Strategy{}).Where("strategy_id = ? AND status = ?", strategy.StrategyId, biz.StrategyStatusValid).Updates(data)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("strategy:%v Update failed, err:%v", biz.JsonToString(strategy), result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("update strategy:%v status:%v failed, affected rows is 0", strategy.StrategyId, biz.StrategyStatusValid)
		return nil
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("update strategy:%v failed, affected rows is 0", biz.JsonToString(strategy))
		return nil
	}

	return nil
}

func (r *strategyRepo) UpdateStatus(ctx context.Context, id string, status int8) error {
	data := map[string]interface{}{
		"status":       status,
		"ModifiedTime": gorm.Expr("CURRENT_TIMESTAMP"),
	}

	result := r.data.db.WithContext(ctx).Model(&Strategy{}).Where("strategy_id = ?", id).Updates(data)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("strategy UpdateStatus failed id:%v status:%v, err:%v", id, status, result)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.WithContext(ctx).Warnf("update strategy:%v status:%v failed, affected rows is 0", id, status)
		return nil
	}

	return nil
}

func (r *strategyRepo) FindByStrategyId(ctx context.Context, strategyId string) (*biz.Strategy, error) {
	var dbStrategy Strategy
	result := r.data.db.WithContext(ctx).Where("strategy_id = ? AND status = ?", strategyId, biz.StrategyStatusValid).Take(&dbStrategy)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("strategy FindByStrategyId failed id:%v , err:%v", strategyId, result)
		return nil, result.Error
	}

	strategy := biz.Strategy{}
	err := copier.Copy(&strategy, &dbStrategy)
	if nil != err {
		r.log.WithContext(ctx).Errorf("strategy:%v copy failed, err:%v", biz.JsonToString(strategy), err)
		return nil, err
	}
	strategy.Config = biz.StringToStrategyConfig(dbStrategy.ScheduleConfig)

	return &strategy, nil
}

func (r *strategyRepo) ListAll(ctx context.Context) ([]*biz.Strategy, error) {
	var ss []Strategy
	result := r.data.db.WithContext(ctx).Where("status = ?", biz.StrategyStatusValid).Find(&ss)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("strategy ListAll failed, err:%v", result)
		return nil, result.Error
	}

	var strategys []*biz.Strategy

	for _, dbStrategy := range ss {
		strategy := biz.Strategy{}
		err := copier.Copy(&strategy, &dbStrategy)
		if nil != err {
			log.Errorf("strategy:%v copy failed, err:%v", biz.JsonToString(strategy), err)
			return nil, err
		}
		strategys = append(strategys, &strategy)
		strategy.Config = biz.StringToStrategyConfig(dbStrategy.ScheduleConfig)

	}
	return strategys, nil
}
