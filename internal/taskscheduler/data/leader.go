package data

import (
	"github.com/cinience/animus/safe"
	"mpp/pkg/hostinfo"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"mpp/internal/taskscheduler/biz"
)

const (
	DefaultTickTime          time.Duration = 1
	DefaultLeaderExpiredTime int           = 3
	DefaultLeaderVersion     uint64        = 0
)

type LeaderService interface {
	DoesLeaderLost(key string) (*Leader, bool)
	Elect(key string) bool
	Reelect(key string) bool
	IsLeader(key string) bool
}

type Leader struct {
	DBBaseModel
	Key      string    `gorm:"uniqueIndex:idx_key;type:varchar(64);NOT NULL;COMMENT:选举的键"`
	Instance string    `gorm:"Index:idx_instance;type:varchar(64);NOT NULL;COMMENT:实例信息"`
	Version  uint64    `gorm:"NOT NULL;COMMENT:版本号"`
	TickTime time.Time `gorm:"column:gmt_tick;type:datetime;NOT NULL;COMMENT:心跳时间"`
}

type DBLeaderRepo struct {
	data *Data
	log  *log.Helper

	instance string
	mutex    sync.Mutex

	leaders map[string]*biz.Leader
}

func NewDBLeaderRepo(data *Data, logger log.Logger) biz.LeaderRepo {
	return &DBLeaderRepo{
		data:     data,
		instance: hostinfo.GetHostIP(),
		log:      log.NewHelper(logger),
		leaders:  make(map[string]*biz.Leader),
	}
}

func NewDBLeaderRepo2(data *Data, instance string, logger log.Logger) biz.LeaderRepo {
	return &DBLeaderRepo{
		data:     data,
		instance: instance,
		log:      log.NewHelper(logger),
		leaders:  make(map[string]*biz.Leader),
	}
}

func (l *DBLeaderRepo) Watch(key string, lo biz.LeaderObserver) {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	if len(key) == 0 || lo == nil {
		l.log.Warnf("leader add watch failed. invalid params key:%v w:%v", key, lo)
		return
	}

	if leader, ok := l.leaders[key]; ok {
		log.Infof("leader add watch key:%v...", key)
		leader.Watchers = append(leader.Watchers, lo)

		// 已经存在leader，直接通知leader变化
		if leader.Instance != "" {
			go lo(biz.EventLeaderChanged, leader.Instance)
		}
	} else {
		leader = &biz.Leader{
			Key:      key,
			Instance: "",
			Watchers: []biz.LeaderObserver{lo},
		}
		l.leaders[key] = leader

		// 启动选举
		go l.doStart(key, leader)
	}
}

func (l *DBLeaderRepo) doStart(key string, bizLeader *biz.Leader) {
	log.Infof("leader elect start key:%v...", key)
	// 选举leader key
	if ok := l.ElectAndSetLeader(bizLeader); ok {
		// 通知leader选举成功
		bizLeader.Watchers[0](biz.EventLeaderSuccess, bizLeader.Instance)
	} else {
		// 通知leader更新
		bizLeader.Watchers[0](biz.EventLeaderChanged, bizLeader.Instance)
	}

	for {
		time.Sleep(DefaultTickTime * time.Second)

		if l.IsLeader(bizLeader.Instance) {
			// 容忍心跳两次失败
			ok := l.tickAndUpdateVersion(bizLeader)
			if !ok {
				if ok, err := l.ReelectAndSetLeader(bizLeader); !ok {
					// 重新选举失败
					if err == nil {
						// 通知leader丢失
						for _, watcher := range bizLeader.Watchers {
							watcher(biz.EventLeaderLost, bizLeader.Instance)
						}
					}
				}
			}
		} else {
			leader, ok := l.DoesLeaderLost(key, bizLeader.Instance)
			if ok {
				// 原leader超时，重新选举
				if ok, err := l.ReelectAndSetLeader(bizLeader); ok {
					// 通知leader选举成功
					for _, watcher := range bizLeader.Watchers {
						// todo 避免初始化超时，导致心跳丢失引起重新选举，此处改为异步初始化  ---但可能有未知原因此节点无法初始化成功，又无法成功更换节点
						safe.Go(func() {
							watcher(biz.EventLeaderSuccess, bizLeader.Instance)
						})
						//watcher(biz.EventLeaderSuccess, bizLeader.Instance)
					}
				} else {
					// 通知leader发生变化
					if err == nil {
						for _, watcher := range bizLeader.Watchers {
							watcher(biz.EventLeaderChanged, bizLeader.Instance)
						}
					}
				}
			} else {
				// 原leader未超时
				if leader != nil {
					bizLeader.Version = leader.Version
				}
			}
		}
	}
}

func (l *DBLeaderRepo) DoesLeaderLost(key string, instance string) (*Leader, bool) {
	return l.isTickTimeout(key, instance)
}

func (l *DBLeaderRepo) ElectAndSetLeader(leader *biz.Leader) bool {
	version := DefaultLeaderVersion
	ok := l.add(leader.Key, l.instance, version)
	if ok {
		// 抢锁成功
		log.Infof("elect success. key:%v instance:%v", leader.Key, l.instance)
		leader.Instance = l.instance
		leader.Version = version
		return l.IsLeader(leader.Instance)
	} else {
		// 抢锁失败，获取leader，并记录到内存
		dbLeader, err := l.getWithKey(leader.Key)
		if err != nil {
			log.Errorf("elect failed and get failed. key:%v instance:%v", leader.Key, l.instance)
			return false
		} else {
			leader.Instance = dbLeader.Instance
			leader.Version = dbLeader.Version
			log.Infof("elect failed, get leader key:%v instance:%v leader:%v", leader.Key, l.instance, leader.Instance)
		}

		return l.IsLeader(leader.Instance)
	}
}

func (l *DBLeaderRepo) ReelectAndSetLeader(leader *biz.Leader) (bool, error) {
	version := DefaultLeaderVersion
	ok := l.updateLeader(leader.Key, l.instance, version, leader)
	if ok {
		// 抢锁成功
		log.Infof("reelect success. key:%v instance:%v", leader.Key, l.instance)
		leader.Instance = l.instance
		leader.Version = version
		return l.IsLeader(leader.Instance), nil
	} else {
		// 抢锁失败，获取leader，并记录到内存
		dbLeader, err := l.getWithKey(leader.Key)
		if err != nil {
			log.Errorf("reelect failed, then get failed. key:%v", leader.Key)
			return false, err
		} else {
			log.Infof("reelect failed. get leader key:%v instance:%v leader:%v", leader.Key, l.instance, leader.Instance)
		}
		leader.Instance = dbLeader.Instance
		leader.Version = version
		return l.IsLeader(leader.Instance), nil
	}
}

func (l *DBLeaderRepo) IsLeader(Instance string) bool {
	return Instance == l.instance
}

func (l *DBLeaderRepo) get(key string, instance string) (*Leader, error) {
	var leader Leader
	result := l.data.db.Where("`key` = ? AND instance = ?", key, instance).Take(&leader)
	if result.Error != nil {
		return nil, result.Error
	}

	return &leader, nil
}

func (l *DBLeaderRepo) getWithKey(key string) (*Leader, error) {
	var leader Leader
	result := l.data.db.Where("`key` = ?", key).Take(&leader)
	if result.Error != nil {
		log.Warnf("get leader failed. key:%v", key)
		return nil, result.Error
	}

	return &leader, nil
}

func (l *DBLeaderRepo) tickAndUpdateVersion(leader *biz.Leader) bool {
	// 更新心跳
	ok := l.updateVersion(leader.Key, leader.Instance)
	if !ok {
		return false
	}

	dbLeader, err := l.get(leader.Key, leader.Instance)
	if err == nil {
		leader.Version = dbLeader.Version
	} else {
		log.Warnf("tickAndUpdateVersion updateVersion success. get Failed. key:%v instance:%v", leader.Key,
			leader.Instance)
		leader.Version += 1

	}
	return true
}

// 可能会发生leader已经变化，所以返回数据库信息
func (l *DBLeaderRepo) isTickTimeout(key string, instance string) (*Leader, bool) {
	var leader Leader
	// 统一使用数据库时间做diff，不会有多机时间同步问题.
	result := l.data.db.Where("`key` = ? AND instance = ? AND TIMESTAMPDIFF(SECOND, gmt_tick, now()) <= ?",
		key, instance, DefaultLeaderExpiredTime).Take(&leader)
	if result.Error == nil {
		return &leader, false
	}

	return nil, true
}

func (l *DBLeaderRepo) updateLeader(key string, instance string, version uint64, old *biz.Leader) bool {
	result := l.data.db.Exec(
		"UPDATE leaders SET instance = ?, version = ?, gmt_tick = CURRENT_TIMESTAMP, gmt_modified = CURRENT_TIMESTAMP "+
			"WHERE `key` = ? AND instance = ? AND version = ?",
		instance, version, key, old.Instance, old.Version,
	)
	if result.Error != nil {
		log.Errorf("updateLeader failed. key:%v instance:%v version:%v old:%v", key, instance, version,
			biz.JsonToString(old))
		return false
	}

	if result.RowsAffected == 0 {
		log.Warnf("leader update without affected row. key:%v instance:%v version:%v old:%v", key, instance, version,
			biz.JsonToString(old))
		return false
	}

	return true
}

func (l *DBLeaderRepo) updateVersion(key string, instance string) bool {
	result := l.data.db.Exec(
		"UPDATE leaders SET version = version + 1, gmt_tick = CURRENT_TIMESTAMP, gmt_modified = CURRENT_TIMESTAMP "+
			"WHERE `key` = ? AND instance = ?", key, instance,
	)
	if result.Error != nil {
		log.Warnf("leader update version failed. err:%v", biz.JsonToString(result))
		return false
	}

	if result.RowsAffected == 0 {
		log.Warnf("leader update version without affected row. key:%v instance:%v", key, instance)
		return false
	}

	return true
}

func (l *DBLeaderRepo) add(key string, instance string, version uint64) bool {
	result := l.data.db.Exec(
		"INSERT INTO leaders(gmt_create, gmt_modified, `key`, instance, version, gmt_tick) "+
			"VALUES(CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, ?, ?, ?, CURRENT_TIMESTAMP)",
		key, instance, version,
	)
	if result.Error == nil {
		return true
	} else {
		log.Infof("elect leader fail. key:%v instance:%v version:%v err:%v", key, instance, version, result.Error)
		return false
	}
}
