package data

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"

	"mpp/internal/taskscheduler/biz"
)

const (
	DefaultOwner            = "default"
	DefaultMutexExpiredTime = 3
)

type Mutex struct {
	DBBaseModel
	Name        string    `gorm:"column:name;uniqueIndex:idx_name;type:varchar(64);NOT NULL;COMMENT:名称"`
	Owner       string    `gorm:"column:owner;index:idx_owner;type:varchar(64);NOT NULL;COMMENT:拥有者"`
	ExpiredTime time.Time `gorm:"column:gmt_expired;index:idx_expired;type:datetime;NOT NULL;COMMENT:过期时间"`
}

type dbMutexRepo struct {
	data *Data
	log  *log.Helper
}

func NewMutexRepo(data *Data, logger log.Logger) biz.MutexRepo {
	return &dbMutexRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (k *dbMutexRepo) Lock(ctx context.Context, mutex *biz.Mutex) bool {
	if mutex.Second == 0 {
		mutex.Second = DefaultMutexExpiredTime
	}
	// 获取新锁
	if k.add(ctx, mutex) == nil {
		return true
	}

	// 获取超时锁
	if ok, _ := k.cas(ctx, mutex); ok {
		return true
	}

	return false
}

func (k *dbMutexRepo) ListLock(ctx context.Context) ([]*biz.Mutex, error) {
	var ms []Mutex
	result := k.data.db.WithContext(ctx).Where("CURRENT_TIMESTAMP <= gmt_expired").Find(&ms)
	if result.Error != nil {
		return nil, result.Error
	}

	var mutexes []*biz.Mutex

	for _, m := range ms {
		mutex := biz.Mutex{}
		err := copier.Copy(&mutex, &m)
		if nil != err {
			k.log.WithContext(ctx).Errorf("copy mutex:%+v failed. err:%v", m, err)
			return nil, err
		}
		mutexes = append(mutexes, &mutex)
	}

	return mutexes, nil
}

func (k *dbMutexRepo) ListExpired(ctx context.Context) ([]*biz.Mutex, error) {
	var ms []Mutex
	result := k.data.db.WithContext(ctx).Where("CURRENT_TIMESTAMP > gmt_expired").Find(&ms)
	if result.Error != nil {
		return nil, result.Error
	}

	var mutexes []*biz.Mutex

	for _, m := range ms {
		mutex := biz.Mutex{}
		err := copier.Copy(&mutex, &m)
		if nil != err {
			k.log.WithContext(ctx).Errorf("copy mutex:%+v failed. err:%v", m, err)
			return nil, err
		}
		mutexes = append(mutexes, &mutex)
	}
	return mutexes, nil
}

func (k *dbMutexRepo) Unlock(ctx context.Context, mutex *biz.Mutex) bool {
	ok, err := k.delete(ctx, mutex)
	if err != nil {
		k.log.WithContext(ctx).Errorf("unlock mutex:%+v failed. err:%v", mutex, err)
		return false
	}

	return ok
}

func (k *dbMutexRepo) delete(ctx context.Context, mutex *biz.Mutex) (bool, error) {
	result := k.data.db.WithContext(ctx).Where("name = ? AND owner = ?", mutex.Name, mutex.Owner).Delete(&Mutex{})

	if result.RowsAffected == 0 {
		k.log.WithContext(ctx).Warnf("delete mutex:%+v failed, affected rows is 0", mutex)
		return false, nil
	}

	return true, nil
}

func (k *dbMutexRepo) cas(ctx context.Context, mutex *biz.Mutex) (bool, error) {
	result := k.data.db.WithContext(ctx).Exec(
		"UPDATE mutexes SET owner = ?, gmt_expired = CURRENT_TIMESTAMP + INTERVAL ? SECOND, gmt_modified = CURRENT_TIMESTAMP "+
			"WHERE name = ? AND gmt_expired < CURRENT_TIMESTAMP",
		mutex.Owner, mutex.Second, mutex.Name,
	)
	if result.Error != nil {
		k.log.WithContext(ctx).Warnf("cas mutex:%+v failed, affected rows is 0", mutex)
		return false, result.Error
	}

	if result.RowsAffected > 0 {
		return true, nil
	}

	return false, nil
}

func (k *dbMutexRepo) add(ctx context.Context, mutex *biz.Mutex) error {
	result := k.data.db.WithContext(ctx).Exec(
		"INSERT INTO mutexes(gmt_create, gmt_modified, name, owner, gmt_expired) "+
			"VALUES(CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, ?, ?, CURRENT_TIMESTAMP + INTERVAL ? SECOND)",
		mutex.Name, mutex.Owner, mutex.Second,
	)
	if result.Error != nil {
		k.log.WithContext(ctx).Warnf("add mutex:%+v failed. err:%v", mutex, result.Error)
		return result.Error
	}

	return nil
}
