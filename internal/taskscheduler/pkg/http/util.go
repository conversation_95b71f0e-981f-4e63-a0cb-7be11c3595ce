package http

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/cinience/animus/registry"
	"mpp/pkg/httputils"
	"mpp/pkg/tracer/middleware/tracer"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/http"
)

func JSONStringToStruct(jsonStr string, v interface{}) error {
	return json.Unmarshal([]byte(jsonStr), v)
}

func InitHttpConnection(ctx context.Context, addr string) (*http.Client, error) {
	connHTTP, err := http.NewClient(
		ctx,
		http.WithEndpoint(fmt.Sprintf("http://%v", addr)),
		http.WithBlock(),
		http.WithTimeout(time.Second*1),
		//arms链路追踪埋点
		http.WithMiddleware(
			tracing.Client(),
			tracer.MPPClientTracer(),
			metadata.Client(),
		),
	)
	if err != nil {
		log.Errorf("init http:%v connection failed:%v ", addr, err)
		return nil, err
	}

	return connHTTP, nil
}

func InitHttpClientWithDiscovery(ctx context.Context, dis registry.Discovery) (*http.Client, error) {
	// todo 后续要整合到一起，否则nacos sdk写文件是否有问题
	// TODO 这个名字后续要改成自动的吧
	serviceName := "mpp-taskmanager.http"
	return http.NewClient(ctx,
		http.WithTransport(httputils.NewRetryTransportWithDiscovery(dis, serviceName)),
		http.WithTimeout(time.Second*5),
		http.WithMiddleware(
			tracing.Client(),
			tracer.MPPClientTracer(),
			metadata.Client(),
		))
}
