package biz

import (
	"context"
	"fmt"
	"github.com/emirpasic/gods/maps/treemap"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMixScheduler(t *testing.T) {

	scheduler := MixScheduler{
		BaseScheduler: BaseScheduler{
			log: log.NewHelper(log.DefaultLogger),
			Strategy: &Strategy{
				Strategy: "mix",
				Config: &StrategyConfig{
					ScheduleResourceType: "real",
					Dominants:            []string{"cpu"},
					Steps: []*StrategyStep{
						{
							Strategy: "central",
						},
					},
				},
				MixConfig: &MixStrategyConfig{
					ScheduleResourceType: "real",
					ProductLevel:         1,
					Steps: []*StrategyStep{
						{
							Strategy: "central",
						},
					},
				},
			},
		},
	}
	resource := &Resource{
		JobId:       fmt.Sprintf("test-1"),
		TaskId:      fmt.Sprintf("test-1"),
		Product:     "ols",
		EngineModel: "transcode",
		Tag:         "livetranscode_pub",
		Quota:       map[string]int64{"cpu": 2000},
	}
	wq, err := scheduler.Alloc(context.Background(), resource)
	assert.NotEqual(t, err, nil)
	assert.Nil(t, wq)
}

func TestSeizeAlloc(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	//mockBaseScheduler := mocks.NewMockBaseScheduler(ctrl)
	mockStrategy := &Strategy{
		Strategy: "mix",
		Config: &StrategyConfig{
			ScheduleResourceType: "cpu",
			Dominants:            []string{"cpu"},
		},
		MixConfig: &MixStrategyConfig{
			SeizeAble:    true,
			ProductLevel: 1,
		},
	}
	mockProductStrategy := &Strategy{
		Config: &StrategyConfig{
			ScheduleResourceType: "cpu",
			Dominants:            []string{"cpu"},
		},
		MixConfig: &MixStrategyConfig{
			SeizeAble:    true,
			ProductLevel: 2,
		},
	}

	mixScheduler := &MixScheduler{
		allStrategies: map[string]*Strategy{
			"product1-engine1-tag1": mockStrategy,
			"product2-engine2-tag2": mockProductStrategy,
		},
		BaseScheduler: BaseScheduler{
			Strategy: mockStrategy,
			WorkerQuotaMap: map[string]*treemap.Map{
				"cpu": treemap.NewWithIntComparator(),
			},
			log: log.NewHelper(log.DefaultLogger),
		},
	}

	resource := &Resource{
		TaskId:   "task1",
		JobId:    "job1",
		WorkerId: "worker1",
		TaskType: 1,
	}
	workerQuota := &WorkerQuota{
		Worker: &Worker{
			Ip: "worker1",
		},
	}

	quotaTreeMap := mixScheduler.WorkerQuotaMap["cpu"]
	quotaTreeMap.Put(15, map[string]*WorkerQuota{"worker1": workerQuota})

	ctx := context.Background()

	// Test failed seizure
	wq, err := mixScheduler.seizeAlloc(ctx, resource, quotaTreeMap, mockStrategy)
	assert.NotNil(t, err)
	assert.Nil(t, wq)
	//assert.Equal(t, "worker1", wq.Worker.Ip)
	//assert.Len(t, wq.Resources, 1)
	//assert.Len(t, wq.SeizeResources, 1)

}
