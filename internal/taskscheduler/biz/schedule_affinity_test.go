package biz_test

import (
	"context"
	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/testdata"

	"github.com/go-kratos/kratos/v2/log"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("ScheduleAffinity", func() {
	var scheduleManager *biz.ScheduleManager

	BeforeEach(func() {
		//mStrategyRepo = mrepo.NewMockStrategyRepo(ctl)
		scheduleManager = biz.NewScheduleManager(nil, log.GetLogger())
	})

	// worker级、推荐亲和性
	It("WorkerPreferred", func() {
		workers := testdata.ScheduleWorkerList(2)
		resource := testdata.ScheduleResource(0, nil)
		strategies := []*biz.Strategy{testdata.ScheduleStrategy(biz.StrategyCentral)}
		scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)

		// 正常申请
		log.Infof("normal alloc start.")
		w1, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		Ω(err).ShouldNot(HaveOccurred())
		_, err = scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		Ω(err).ShouldNot(HaveOccurred())
		log.Infof("normal alloc success.")

		log.Infof("preferred alloc start.")
		resource.Affinity = &biz.Affinity{
			Required: nil,
			Preferred: []metav1.LabelSelectorRequirement{
				{
					Key:      "workerIp",
					Operator: metav1.LabelSelectorOpNotIn,
					Values:   []string{w1.Ip},
				},
			},
		}

		// 推荐申请
		w2, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		Ω(err).ShouldNot(HaveOccurred())
		if w1.WorkerId == workers[0].WorkerId {
			Ω(w2).Should(Equal(workers[1]))
		} else {
			Ω(w2).Should(Equal(workers[0]))
		}
		_, err = scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		Ω(err).ShouldNot(HaveOccurred())
		log.Infof("preferred alloc success.")

		log.Infof("preferred alloc fail, backup start.")

		// 推荐无效，backup申请
		taintWorkers := make(map[string]*biz.WorkerQuota)
		scheduleManager.ScheduleUnits[biz.SchedulerMaster].Workers[w2.WorkerId].Worker.Taint = []*biz.Taint{{Key: "taint", Value: "taint"}}
		taintWorkers[w2.WorkerId] = scheduleManager.ScheduleUnits[biz.SchedulerMaster].Workers[w2.WorkerId]
		scheduleManager.ScheduleUnits[biz.SchedulerMaster].SyncWorkers(ctx, workers, map[string]*biz.Worker{}, taintWorkers, make(map[string]*biz.WorkerQuota), []*biz.Worker{}, strategies)
		w3, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(w3.WorkerId).Should(Equal(w1.WorkerId))
		//Ω(w1).Should(Equal(workers[0]))
		_, err = scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		Ω(err).ShouldNot(HaveOccurred())
		log.Infof("preferred alloc fail, backup success.")
	})

	// x86/arm标签亲和性
	It("LabelPreferred", func() {
		workers := make([]*biz.Worker, 0)
		workers = append(workers, testdata.ScheduleWorker(0, map[string]string{}))
		workers = append(workers, testdata.ScheduleWorker(1, map[string]string{"arch": "x86"}))
		workers = append(workers, testdata.ScheduleWorker(2, map[string]string{"arch": "arm"}))

		resource := testdata.ScheduleResource(0, nil)
		strategies := []*biz.Strategy{testdata.ScheduleStrategy(biz.StrategyCentral)}
		scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)

		// 正常申请
		log.Infof("normal alloc start.")
		w, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(w).Should(Equal(workers[0]))
		_, err = scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		Ω(err).ShouldNot(HaveOccurred())
		log.Infof("normal alloc success.")

		log.Infof("preferred alloc start.")
		resource.Affinity = &biz.Affinity{
			Required: nil,
			Preferred: []metav1.LabelSelectorRequirement{
				{
					Key:      "arch",
					Operator: metav1.LabelSelectorOpIn,
					Values:   []string{"x86"},
				},
			},
		}

		// 推荐申请
		w, err = scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(w).Should(Equal(workers[1]))
		_, err = scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		Ω(err).ShouldNot(HaveOccurred())

		resource.Affinity = &biz.Affinity{
			Required: nil,
			Preferred: []metav1.LabelSelectorRequirement{
				{
					Key:      "arch",
					Operator: metav1.LabelSelectorOpIn,
					Values:   []string{"arm"},
				},
			},
		}

		// 推荐申请
		w, err = scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(w).Should(Equal(workers[2]))
		_, err = scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		Ω(err).ShouldNot(HaveOccurred())

		log.Infof("preferred alloc success.")

		log.Infof("preferred alloc fail, backup start.")

		// 推荐无效，backup申请
		taintWorkers := make(map[string]*biz.WorkerQuota)
		scheduleManager.ScheduleUnits[biz.SchedulerMaster].Workers[workers[1].WorkerId].Worker.Taint = []*biz.Taint{{Key: "taint", Value: "taint"}}
		scheduleManager.ScheduleUnits[biz.SchedulerMaster].Workers[workers[2].WorkerId].Worker.Taint = []*biz.Taint{{Key: "taint", Value: "taint"}}

		taintWorkers[workers[1].WorkerId] = scheduleManager.ScheduleUnits[biz.SchedulerMaster].Workers[workers[1].WorkerId]
		taintWorkers[workers[2].WorkerId] = scheduleManager.ScheduleUnits[biz.SchedulerMaster].Workers[workers[2].WorkerId]
		scheduleManager.ScheduleUnits[biz.SchedulerMaster].SyncWorkers(ctx, workers, map[string]*biz.Worker{}, taintWorkers, make(map[string]*biz.WorkerQuota), []*biz.Worker{}, strategies)
		resource.Affinity = &biz.Affinity{
			Required: nil,
			Preferred: []metav1.LabelSelectorRequirement{
				{
					Key:      "arch",
					Operator: metav1.LabelSelectorOpIn,
					Values:   []string{"x86"},
				},
			},
		}
		w, err = scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(w).Should(Equal(workers[0]))
		_, err = scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		Ω(err).ShouldNot(HaveOccurred())

		resource.Affinity = &biz.Affinity{
			Required: nil,
			Preferred: []metav1.LabelSelectorRequirement{
				{
					Key:      "arch",
					Operator: metav1.LabelSelectorOpIn,
					Values:   []string{"arm"},
				},
			},
		}
		w, err = scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(w).Should(Equal(workers[0]))
		_, err = scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		Ω(err).ShouldNot(HaveOccurred())
		log.Infof("preferred alloc fail, backup success.")
	})

	// cpu/v100标签亲和性测试
	It("LabelPreferred", func() {
		workers := make([]*biz.Worker, 0)
		workers = append(workers, testdata.ScheduleWorker(0, map[string]string{}))
		workers = append(workers, testdata.ScheduleWorker(1, map[string]string{"arch": "x86", "resourceType": "cpu"}))
		workers = append(workers, testdata.ScheduleWorker(2, map[string]string{"resourceType": "v100"}))

		resource := testdata.ScheduleResource(0, nil)
		strategies := []*biz.Strategy{testdata.ScheduleStrategy(biz.StrategyCentral)}
		scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)

		// 正常申请
		log.Infof("normal alloc start.")
		w, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(w).Should(Equal(workers[0]))
		_, err = scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		Ω(err).ShouldNot(HaveOccurred())
		log.Infof("normal alloc success.")

		log.Infof("preferred alloc start.")
		resource.Affinity = &biz.Affinity{
			Required: nil,
			Preferred: []metav1.LabelSelectorRequirement{
				{
					Key:      "resourceType",
					Operator: metav1.LabelSelectorOpIn,
					Values:   []string{"cpu"},
				},
			},
		}
		// 推荐申请
		w, err = scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(w).Should(Equal(workers[2]))
		_, err = scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		Ω(err).ShouldNot(HaveOccurred())

		log.Infof("preferred alloc success.")

		log.Infof("preferred alloc fail, backup start.")

	})
})
