package biz

import (
	"context"
	"math/rand"
	"mpp/internal/taskscheduler/conf"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"

	v1 "proto.mpp/api/common/v1"
)

var (
	// ErrWorkerNotFound is worker not found.
	ErrWorkerNotFound = errors.NotFound(v1.ErrorReason_WORKER_NOT_FOUND.String(), "worker not found")
	ErrNotEnoughFound = errors.NotFound(v1.ErrorReason_NOT_ENOUGH_QUOTA.String(), "not enough quota")
	ErrNoRegister     = errors.NotFound(v1.ErrorReason_WORKER_NO_REGISTER.String(), "no register")
)

// todo 是否应该增加请求下线，置为下线中的状态。。。
const (
	WorkerStatusOffline int8   = 0
	WorkerStatusOnline  int8   = 1
	WorkerExpiredTime   int64  = 12
	MaxCheckNum         int    = 256
	QuotaOptAdd         string = "Add"
	QuotaOptSub         string = "Sub"
)

type Taint struct {
	Key    string `json:"Key,omitempty"`
	Value  string `json:"Value,omitempty"`
	Effect string `json:"Effect,omitempty"`
}

type Worker struct {
	WorkerId           string                 `json:"WorkerId,omitempty"`           // worker唯一ID
	Ip                 string                 `json:"Ip,omitempty"`                 // worker IP 地址
	PodName            string                 `json:"PodName,omitempty"`            // pod 名称
	NodeName           string                 `json:"NodeName,omitempty"`           // node 名称，L2时方便定位问题
	Port               int32                  `json:"Port,omitempty"`               // 端口
	Product            string                 `json:"Product,omitempty"`            // 产品
	EngineModel        string                 `json:"EngineModel,omitempty"`        // 引擎
	Tag                string                 `json:"Tag,omitempty"`                // 标签，用于划分小集群
	Load               float32                `json:"Load,omitempty"`               // 机器负载
	OnlineVersion      int32                  `json:"OnlineVersion,omitempty"`      // 在线版本，每注册一次加1
	UpdateQuotaVersion int32                  `json:"UpdateQuotaVersion,omitempty"` // 更新一次Quota，每更新一次加1
	TaskNum            int32                  `json:"TaskNum,omitempty"`            // 机器任务数
	Quota              QuotaSet               `json:"Quota,omitempty"`              // worker多纬度总资源，由worker注册时汇报
	AllocQuota         QuotaSet               `json:"AllocQuota,omitempty"`         // 按逻辑quota，多纬度已分配资源，
	RealQuota          QuotaSet               `json:"RealQuota,omitempty"`          // worker心跳汇报，多纬度真实资源
	AvgQuota           QuotaSet               `json:"AvgQuota,omitempty"`           // worker心跳汇报，多纬度滑动平均资源，1分钟窗口
	ScheduleQuota      QuotaSet               `json:"ScheduleQuota,omitempty"`      // 可调度Quota，考虑分配时，Quota的冻结时间
	ScheduleConfig     map[string]interface{} `json:"ScheduleConfig,omitempty"`     // 调度配置，包括资源类型，Real：根据真实资源调度，Logic:根据逻辑资源调度
	Label              map[string]string      `json:"Label,omitempty"`              // worker标签组
	Taint              []*Taint               `json:"Taint,omitempty"`              // worker污点组
	HeartbeatTime      time.Time              `json:"HeartbeatTime,omitempty"`      // 最近一次心跳时间
	LatestAllocTime    time.Time              `json:"LatestAllocTime,omitempty"`    // 最近一次申请资源时间
	ConnectMode        []string               `json:"ConnectMode,omitempty"`        // 访问worker模式，direct:直连，gateway:网关代理
	Extend             map[string]interface{} `json:"Extend,omitempty"`             // 扩展字段
	Metadata           map[string]string      `json:"Metadata,omitempty"`           // metadata信息，包括机器的机型等
}

type WorkerRepo interface {
	Save(context.Context, *Worker) (*Worker, error)
	UpdateHeartbeat(context.Context, *Worker, bool) error
	UpdateQuota(context.Context, *Worker, string) error
	UpdateChangeQuota(ctx context.Context, workerId string, quota QuotaSet, scheduleConfig *StrategyConfig, opt string) (bool, error)
	UpdateStatus(context.Context, string, int8) error
	BatchUpdateStatus(context.Context, []string, int8) error
	UpdateLabel(context.Context, *Worker) error
	UpdateTaint(context.Context, []string, []string, []*Taint) error
	UpdateTagByIps(context.Context, []string, string) error
	UpdateTagByWorkerIds(context.Context, []string, string) error
	FindByWorkerId(context.Context, string, int8) (*Worker, error)
	FindByIp(context.Context, string) (*Worker, error)
	ListOnlineWorker(context.Context) ([]*Worker, error)
	ListByConditions(context.Context, string, string, string, []string) ([]*Worker, error)
	ListByWorkerIds(context.Context, []string) ([]*Worker, error)
	ListByIps(context.Context, []string) ([]*Worker, error)
}

type WorkerApi interface {
	CheckOnline(context.Context, *Worker) (bool, interface{})
}

type WorkerManager struct {
	repo         WorkerRepo
	opt          WorkerApi
	masterClient TaskScheduleMasterApi
	conf         *conf.App
	log          *log.Helper
	labelManager *LabelManager // 添加 LabelManager
}

func NewWorkerManager(c *conf.App, repo WorkerRepo, opt WorkerApi, client TaskScheduleMasterApi, logger log.Logger) *WorkerManager {
	return &WorkerManager{
		repo:         repo,
		opt:          opt,
		masterClient: client,
		conf:         c,
		log:          log.NewHelper(logger),
		labelManager: NewLabelManager(MPSProducts), // 初始化 LabelManager
	}
}

func (wm *WorkerManager) Register(ctx context.Context, w *Worker) (*Worker, error) {
	wm.log.WithContext(ctx).Infof("Worker Register: %v", w)
	if w == nil || w.WorkerId == "" {
		log.Warnf("Register worker:%+v is null", w)
		return nil, ErrWorkerNotFound
	}
	//尝试添加resourceType label
	wm.labelManager.AddResourceType(w.Product, &w.Label, w.Quota)

	return wm.repo.Save(ctx, w)
}

func (wm *WorkerManager) Heartbeat(ctx context.Context, w *Worker) error {
	if w == nil || w.WorkerId == "" {
		log.Warnf("Heartbeat worker:%+v is null", w)
		return ErrWorkerNotFound
	}

	isUpdateSchedule := false
	if wm.conf.DelayScheduleUpdate && wm.masterClient != nil {
		// 从master的待更新列表里面取,worker没新任务则不在更新列表里，会导致数据库中的ScheduleQuota记录不是最终版本
		// 任务归0时worker上报realQuota有概率不是最终的，下一次上报时worker不在master的更新列表里，ScheduleQuota就会无法更新到数据库
		masterWorker, err := wm.masterClient.GetMasterWorker(ctx, w.WorkerId)
		if err == nil && masterWorker != nil {
			// 更新allocQuota, schedulerQuota, taskNum
			w.AllocQuota = masterWorker.AllocQuota
			w.ScheduleQuota = masterWorker.ScheduleQuota
			w.TaskNum = masterWorker.TaskNum
			isUpdateSchedule = true
			wm.log.WithContext(ctx).Infof("Heartbeat delay update scheduler success. worker:%+v, masterWorker:%+v", w, masterWorker)
		}
		if err != nil {
			wm.log.WithContext(ctx).Warnf("Heartbeat delay update scheduler failed. worker:%+v, masterWorker:%+v", w, masterWorker)
		}
	}

	// todo 删除
	//wm.log.WithContext(ctx).Infof("Heartbeat update scheduler worker message. worker:%+v, delayScheduleUpdate:%t", w, wm.conf.DelayScheduleUpdate)

	return wm.repo.UpdateHeartbeat(ctx, w, isUpdateSchedule)
}

func (wm *WorkerManager) Unregister(ctx context.Context, w *Worker) error {
	wm.log.WithContext(ctx).Infof("Worker Unregister: %v", w)
	if w == nil || w.WorkerId == "" {
		log.Warnf("Unregister worker:%+v is null", w)
		return ErrWorkerNotFound
	}

	return wm.repo.UpdateStatus(ctx, w.WorkerId, WorkerStatusOffline)
}

func (wm *WorkerManager) Get(ctx context.Context, id string, status int8) (*Worker, error) {
	//wm.log.WithContext(ctx).Infof("Worker Get: %v", id)
	if id == "" {
		log.Warnf("Get workerIds:%+v is null", id)
		return nil, ErrWorkerNotFound
	}

	worker, err := wm.repo.FindByWorkerId(ctx, id, status)
	if err != nil {
		return nil, err
	}

	return worker, nil
}

func (wm *WorkerManager) ListOnlineWorker(ctx context.Context) ([]*Worker, error) {
	return wm.repo.ListOnlineWorker(ctx)
}

func (wm *WorkerManager) ListWorker(ctx context.Context, worker *Worker, ips []string) ([]*Worker, error) {
	return wm.repo.ListByConditions(ctx, worker.Product, worker.EngineModel, worker.Tag, ips)
}

func (wm *WorkerManager) TagWorker(ctx context.Context, ids []string, ips []string, tag string) error {
	wm.log.WithContext(ctx).Infof("TagWorker: tag:%v ids:%v ips:%v", tag, ids, ips)
	if ips != nil || len(ips) > 0 {
		return wm.repo.UpdateTagByIps(ctx, ips, tag)
	} else if ids != nil || len(ids) > 0 {
		return wm.repo.UpdateTagByWorkerIds(ctx, ids, tag)
	} else {
		log.Warnf("TagWorker worker ips:%+v ids:%+ v is null", ips, ids)
		return ErrWorkerNotFound
	}

}

func (wm *WorkerManager) TaintWorker(ctx context.Context, workerIds []string, workerIps []string, taints []*Taint) error {
	wm.log.WithContext(ctx).Infof("TaintWorker: workerIds:%+v workerIps:%+v taints:%+v",
		workerIds, workerIps, taints)
	if workerIds == nil && workerIps == nil {
		log.Warnf("TagWorker workerIds:%+v workerIps:%+v param invalid.", workerIds, workerIps)
		return ErrWorkerNotFound
	}

	return wm.repo.UpdateTaint(ctx, workerIds, workerIps, taints)
}

func (wm *WorkerManager) LabelWorker(ctx context.Context, workerId string, label map[string]string) error {
	wm.log.WithContext(ctx).Infof("LabelWorker: workerId:%v taints:%+v", workerId, label)
	if workerId == "" {
		log.Warnf("LabelWorker worker:%+v is null", workerId)
		return ErrWorkerNotFound
	}

	return wm.repo.UpdateLabel(ctx, &Worker{WorkerId: workerId, Label: label})
}

func (wm *WorkerManager) UpdateQuota(ctx context.Context, w *Worker, opt string) error {
	wm.log.WithContext(ctx).Infof("Worker Update Quota: %v", w)
	if w == nil || w.WorkerId == "" {
		log.Warnf("UpdateQuota worker:%+v is null", w)
		return ErrWorkerNotFound
	}

	return wm.repo.UpdateQuota(ctx, w, opt)
}

func (wm *WorkerManager) UpdateChangedQuota(ctx context.Context, workerId string, quota QuotaSet, config *StrategyConfig, opt string) (bool, error) {
	wm.log.WithContext(ctx).Infof("UpdateChangedQuota workerId:%v quota:%v config:%v, opt:%v", workerId, quota,
		config, opt)
	if workerId == "" {
		log.Warnf("UpdateChangedQuota worker:%+v is null", workerId)
		return false, ErrWorkerNotFound
	}

	return wm.repo.UpdateChangeQuota(ctx, workerId, quota, config, opt)
}

func (wm *WorkerManager) BatchUpdateWorkerStatus(ctx context.Context, workerIds []string) error {
	wm.log.WithContext(ctx).Infof("Worker BatchUpdateWorkerStatus: %v", workerIds)
	if len(workerIds) == 0 {
		log.Warnf("BatchUpdateWorkerStatus workerIds:%v is null", workerIds)
		return nil
	}

	if err := wm.repo.BatchUpdateStatus(ctx, workerIds, WorkerStatusOffline); err != nil {
		return err
	}

	return nil
}

func (wm *WorkerManager) LogWorker(ctx context.Context, worker *Worker, event string, f string, ok bool, msg interface{}) {
	wm.log.WithContext(ctx).Log(log.LevelInfo,
		"worker.id", worker.WorkerId,
		"worker.ip", worker.Ip,
		"worker.taint", worker.Taint,
		"event", event,
		"func", f,
		"info", worker,
		"ok", ok,
		"msg", msg,
	)
}

func (wm *WorkerManager) MultiCheckWorkerStatus(ctx context.Context, workers []*Worker) ([]*Worker, []*Worker) {
	wm.log.WithContext(ctx).Warnf("MultiCheckWorkerStatus num:%v", len(workers))
	var checks []*Worker
	var offlines []*Worker
	var onlines []*Worker
	if len(workers) > MaxCheckNum {
		wm.shuffle(workers)
		checks = workers[0 : MaxCheckNum-1]
		onlines = workers[MaxCheckNum:]
	} else {
		checks = workers
	}

	// 使用10个协程并发执行CheckOnline
	const maxWorkers = 10
	var wg sync.WaitGroup
	var onlinesMutex, offlinesMutex sync.Mutex

	// 创建工作队列
	workerChan := make(chan *Worker, len(checks))

	// 启动10个工作协程
	for i := 0; i < maxWorkers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for worker := range workerChan {
				ok, msg := wm.opt.CheckOnline(ctx, worker)

				// 并发安全地添加到结果切片
				if ok {
					onlinesMutex.Lock()
					onlines = append(onlines, worker)
					onlinesMutex.Unlock()
				} else {
					offlinesMutex.Lock()
					offlines = append(offlines, worker)
					offlinesMutex.Unlock()
				}

				wm.LogWorker(ctx, worker, "timer.sync", "Worker.CheckOnline", ok, msg)
			}
		}()
	}

	// 将所有worker发送到队列
	for _, worker := range checks {
		workerChan <- worker
	}
	close(workerChan)

	// 等待所有协程完成
	wg.Wait()

	return onlines, offlines
}

func (wm *WorkerManager) shuffle(workers []*Worker) {
	rand.Shuffle(len(workers), func(i, j int) {
		workers[i], workers[j] = workers[j], workers[i]
	})
}
