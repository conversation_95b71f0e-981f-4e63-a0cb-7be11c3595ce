package biz

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"reflect"
	"strings"

	"github.com/google/uuid"
)

type QuotaSet map[string]int64

func (q *QuotaSet) Add(quota QuotaSet) *QuotaSet {
	for key, value := range quota {
		if v, ok := (*q)[key]; ok {
			(*q)[key] = v + value
		} else {
			(*q)[key] = value
		}
	}

	return q
}

func (q *QuotaSet) Sub(quota QuotaSet) *QuotaSet {
	for key := range *q {
		if val, ok := quota[key]; ok {
			(*q)[key] -= val
		}
	}

	return q
}

func (q *QuotaSet) CopyWithRatio(quota QuotaSet, ratio int64) *QuotaSet {
	for key, value := range quota {
		(*q)[key] = value * ratio / 100
	}

	return q
}

func (q *QuotaSet) Copy(quota QuotaSet) *QuotaSet {
	for key, value := range quota {
		(*q)[key] = value
	}

	return q
}

func (q *QuotaSet) QuotaGEQ(quota QuotaSet) bool {
	// 确保q包含所有quota
	for aKey := range quota {
		if _, ok := (*q)[aKey]; !ok {
			return false
		}
	}

	for aKey, aValue := range *q {
		if bValue, ok := quota[aKey]; ok {
			if aValue < bValue {
				return false
			}
		}
	}

	return true
}

func (q QuotaSet) Value() (driver.Value, error) {
	bytes, err := json.Marshal(q)
	return bytes, err
}

func (q *QuotaSet) Scan(input interface{}) error {
	return json.Unmarshal(input.([]byte), q)
}

func BuildLabelKey(label map[string]string) string {
	if label == nil || len(label) == 0 {
		return "0-label"
	} else {
		data, err := json.Marshal(label)
		if err != nil {
			return "0-error"
		}

		u := uuid.NewSHA1(uuid.Nil, data)
		str := strings.ReplaceAll(u.String(), "-", "")
		return fmt.Sprintf("%v-%v", len(label), str)
	}
}

func BuildTagKey(product, model, tag string) string {
	str := product + "-" + model + "-" + tag
	u := uuid.NewSHA1(uuid.Nil, []byte(str))
	return strings.ReplaceAll(u.String(), "-", "")
}

func JsonToString(i interface{}) string {
	str, err := json.Marshal(i)
	if err != nil {
		return ""
	}

	return string(str)
}

func StringToMap(s string) map[string]interface{} {
	var data map[string]interface{}
	err := json.Unmarshal([]byte(s), &data)
	if err != nil {
		return nil
	}

	return data
}

func StringToResource(s string) *Affinity {
	var affinity Affinity
	err := json.Unmarshal([]byte(s), &affinity)
	if err != nil {
		return nil
	}

	return &affinity
}

func StringToStrategyConfig(s string) *StrategyConfig {
	var config StrategyConfig
	err := json.Unmarshal([]byte(s), &config)
	if err != nil {
		return nil
	}

	return &config
}

type Request struct {
	RequestId string
	TaskId    string
}

// ParseToMap 将interface{} 解析为map[string]interface{}, 自动处理string和map两种类型
func ParseToMap(input interface{}) (map[string]interface{}, error) {
	// 如果直接是map类型，直接返回
	if m, ok := input.(map[string]interface{}); ok {
		return m, nil
	}

	// 如果是字符串类型，尝试解析为JSON
	if str, ok := input.(string); ok {
		var result map[string]interface{}
		if err := json.Unmarshal([]byte(str), &result); err != nil {
			return nil, fmt.Errorf("string not json type: %v", err)
		}
		return result, nil
	}

	// 其他类型返回错误
	return nil, fmt.Errorf("unsupport type: %v", reflect.TypeOf(input))
}

func ParseMapStrValue(input map[string]interface{}, key string) string {
	if _, ok := input[key]; !ok {
		return ""
	}
	v, ok := input[key].(string)
	if !ok {
		return ""
	}
	return v
}

func ParseMapIntValue(input map[string]interface{}, key string) int {
	if _, ok := input[key]; !ok {
		return 0
	}
	v, ok := input[key].(int)
	if !ok {
		return 0
	}
	return v
}
