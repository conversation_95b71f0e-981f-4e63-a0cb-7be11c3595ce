package biz

import (
	"context"
	"errors"
)

// StepAverageScheduler 多阶平均策略
type StepAverageScheduler struct {
	BaseScheduler
}

func (ss *StepAverageScheduler) Alloc(ctx context.Context, resource *Resource) (*WorkerQuota, error) {
	resourceType := ss.Strategy.Config.ScheduleResourceType
	dominant := ss.Strategy.Config.Dominants[0]
	quotaTreeMap, ok := ss.WorkerQuotaMap[resourceType]
	if !ok {
		ss.log.WithContext(ctx).Errorf("step average scheduler, taskId:%v unknown schedule resource type:%v not exist. %v",
			resource.TaskId, resourceType, JsonToString(ss.WorkerQuotaMap))
		return nil, ErrResourceNotFound
	}

	keys := quotaTreeMap.Keys()

	// 跳过弹性保留机器，如果是大Quota任务，优先分配，不保留
	length := ss.getElasticMaxLen(ctx, quotaTreeMap, resource)
	var backupWorkerQuota *WorkerQuota

	for i := length - 1; i >= 0; i-- {
		treeMapKey := keys[i]
		if keys[i].(int64) < resource.Quota[dominant] {
			break
		}

		value, _ := quotaTreeMap.Get(treeMapKey)
		var workerQuota *WorkerQuota
		if workerQuotaMap, ok := value.(map[string]*WorkerQuota); ok {
			for _, workerQuota = range workerQuotaMap {
				if wq, err := ss.allocWorkerQuota(ctx, resource, workerQuota); err == nil {
					return wq, nil
				} else if errors.Is(err, ErrNotEnoughQuota) {
					break
				} else if errors.Is(err, ErrMatchLabelPreferredKey) && backupWorkerQuota == nil {
					backupWorkerQuota = workerQuota
				}
			}
		}
	}

	if backupWorkerQuota != nil {
		// backup
		ss.log.WithContext(ctx).Errorf("step average scheduler, taskId:%v no affinity worker, use backup. %v",
			resource.TaskId, JsonToString(ss.WorkerQuotaMap))

		return ss.allocWorkerQuotaWithAffinity(ctx, resource, backupWorkerQuota, true)
	}

	//log.Errorf("average scheduler, task:%v alloc resource failed. worker:%v.", JsonToString(resource),
	//	JsonToString(as.WorkerQuotaMap))
	return nil, ErrNotEnoughQuota
}
