package biz

import (
	"context"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
)

/**
 * @Author: qinxin
 * @Date: 2025/6/25 09:43
 * @Desc:
 * @Version 1.0
 */

func TestChildWorkerManager_LogWorker(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	cwm := &ChildWorkerManager{
		log: log.NewHelper(log.DefaultLogger),
	}

	ctx := context.Background()
	worker := &ChildWorker{
		ParentWorkerId: "parent-1",
		WorkerId:       "worker-1",
		Ip:             "127.0.0.1",
		Taint:          []*Taint{},
	}
	event := "test-event"
	f := "test-func"
	ok := true
	msg := "test message"

	//mockLogger.EXPECT().Log(log.LevelInfo, []interface{}{
	//	"parent.worker.id", worker.ParentWorkerId,
	//	"worker.id", worker.WorkerId,
	//	"worker.ip", worker.Ip,
	//	"worker.taint", worker.Taint,
	//	"event", event,
	//	"func", f,
	//	"info", worker,
	//	"ok", ok,
	//	"msg", msg,
	//}).Return(nil)

	cwm.LogWorker(ctx, worker, event, f, ok, msg)
}
