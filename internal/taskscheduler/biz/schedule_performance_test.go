package biz_test

// todo 修正，性能测试暂时注释
//var _ = Describe("ScheduleManagerPerformance", func() {
//	var scheduleManager *biz.ScheduleManager
//	//var mStrategyRepo *mrepo.MockStrategyRepo
//
//	BeforeEach(func() {
//		//mStrategyRepo = mrepo.NewMockStrategyRepo(ctl)
//		scheduleManager = biz.NewScheduleManager(nil, log.GetLogger())
//	})
//
//	// PIt("ScheduleAverage", func() {
//	It("ScheduleAverage", func() {
//		//test("product", "engine_moieee", "tag")
//		workers := testdata.ScheduleWorkerList(20000)
//		resources := testdata.ScheduleResourceList(320000)
//		strategies := testdata.ScheduleStrategyList(0)
//		scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
//		start := time.Now().UnixMicro()
//
//		//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
//		for _, resource := range resources {
//			_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
//			Ω(err).ShouldNot(HaveOccurred())
//		}
//
//		end := time.Now().UnixMicro()
//		log.Infof("average schedule: worker:%v resource:%v, alloc cost time:%v", len(workers), len(resources), end-start)
//
//		for _, resource := range resources {
//			_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
//			Ω(err).ShouldNot(HaveOccurred())
//		}
//
//		free := time.Now().UnixMicro()
//		log.Infof("average schedule: worker:%v resource:%v, free cost time:%v", len(workers), len(resources), free-end)
//	})
//
//	It("ScheduleCentral", func() {
//		//test("product", "engine_moieee", "tag")
//		workers := testdata.ScheduleWorkerList(20000)
//		resources := testdata.ScheduleResourceList(320000)
//		strategies := []*biz.Strategy{testdata.ScheduleStrategy(biz.StrategyCentral)}
//		t1 := time.Now().UnixMicro()
//		scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
//		start := time.Now().UnixMicro()
//		log.Infof("central schedule: worker:%v resource:%v, init time:%v", len(workers), len(resources), start-t1)
//		log.Infof("=========================================================")
//
//		//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
//		for _, resource := range resources {
//			_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
//			Ω(err).ShouldNot(HaveOccurred())
//			//log.Infof("worker:%v", biz.JsonToString(workerQuota))
//		}
//
//		end := time.Now().UnixMicro()
//		log.Infof("central schedule: worker:%v resource:%v, alloc time:%v", len(workers), len(resources), end-start)
//		log.Infof("=========================================================")
//
//		scheduleManager.SyncWorkers(ctx, workers, strategies)
//		sync := time.Now().UnixMicro()
//
//		log.Infof("central schedule: worker:%v resource:%v, sync time:%v", len(workers), len(resources), sync-end)
//		log.Infof("=========================================================")
//
//		for _, resource := range resources {
//			_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
//			Ω(err).ShouldNot(HaveOccurred())
//		}
//
//		free := time.Now().UnixMicro()
//		log.Infof("central schedule: worker:%v resource:%v, free time:%v", len(workers), len(resources), free-sync)
//
//		log.Infof("=========================================================")
//		for _, resource := range resources {
//			_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
//			Ω(err).ShouldNot(HaveOccurred())
//			//log.Infof("worker:%v", biz.JsonToString(workerQuota))
//		}
//		for _, resource := range resources {
//			_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
//			Ω(err).ShouldNot(HaveOccurred())
//		}
//		redo := time.Now().UnixMicro()
//		log.Infof("central schedule redo alloc+free: worker:%v resource:%v, free time:%v", len(workers), len(resources), redo-free)
//	})
//
//	It("ScheduleRandom", func() {
//		//test("product", "engine_moieee", "tag")
//		workers := testdata.ScheduleWorkerList(20000)
//		resources := testdata.ScheduleResourceList(320000)
//		strategies := []*biz.Strategy{testdata.ScheduleStrategy(biz.StrategyRandom)}
//		scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
//		start := time.Now().UnixMicro()
//
//		//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
//		for _, resource := range resources {
//			_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
//			Ω(err).ShouldNot(HaveOccurred())
//			//log.Infof("worker:%v", biz.JsonToString(workerQuota))
//		}
//
//		end := time.Now().UnixMicro()
//		log.Infof("random schedule: worker:%v resource:%v, cost time:%v", len(workers), len(resources), end-start)
//
//		for _, resource := range resources {
//			_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
//			Ω(err).ShouldNot(HaveOccurred())
//		}
//
//		free := time.Now().UnixMicro()
//		log.Infof("random schedule: worker:%v resource:%v, free cost time:%v", len(workers), len(resources), free-end)
//	})
//
//	It("ScheduleStepRandom", func() {
//		//test("product", "engine_moieee", "tag")
//		workers := testdata.ScheduleWorkerList(20000)
//		resources := testdata.ScheduleResourceList(320000)
//		strategies := []*biz.Strategy{testdata.ScheduleStrategy(biz.StrategyStepRandom)}
//		scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
//		start := time.Now().UnixMicro()
//
//		//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
//		for _, resource := range resources {
//			_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
//			Ω(err).ShouldNot(HaveOccurred())
//			//log.Infof("worker:%v", biz.JsonToString(workerQuota))
//		}
//
//		end := time.Now().UnixMicro()
//		log.Infof("random schedule: worker:%v resource:%v, cost time:%v", len(workers), len(resources), end-start)
//
//		for _, resource := range resources {
//			_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
//			Ω(err).ShouldNot(HaveOccurred())
//		}
//
//		free := time.Now().UnixMicro()
//		log.Infof("random schedule: worker:%v resource:%v, free cost time:%v", len(workers), len(resources), free-end)
//	})
//
//	It("ScheduleStatistics", func() {
//		//test("product", "engine_moieee", "tag")
//		workers := testdata.ScheduleWorkerList(10000)
//		resources := testdata.ScheduleResourceList(160000)
//		strategies := []*biz.Strategy{testdata.ScheduleStrategy(biz.StrategyStepRandom)}
//		scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
//		start := time.Now().UnixMicro()
//
//		//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
//		for _, resource := range resources {
//			_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
//			Ω(err).ShouldNot(HaveOccurred())
//			//log.Infof("worker:%v", biz.JsonToString(workerQuota))
//		}
//
//		end := time.Now().UnixMicro()
//		log.Infof("random schedule: worker:%v resource:%v, cost time:%v", len(workers), len(resources), end-start)
//
//		stat := scheduleManager.ScheduleUnits[biz.SchedulerMaster].Statistics(ctx, time.Now())
//		log.Infof("schedule stat time: worker:%v resource:%v waterlevel:%v, cost time:%v", len(workers), len(resources),
//			biz.JsonToString(stat), time.Now().UnixMicro()-end)
//
//		for _, resource := range resources {
//			_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
//			Ω(err).ShouldNot(HaveOccurred())
//		}
//
//		free := time.Now().UnixMicro()
//		log.Infof("random schedule: worker:%v resource:%v, free cost time:%v", len(workers), len(resources), free-end)
//	})
//})
