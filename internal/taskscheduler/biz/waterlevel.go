package biz

import (
	"context"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
)

type WaterLevel struct {
	Product       string            `json:"Product,omitempty"`
	EngineModel   string            `json:"EngineModel,omitempty"`
	Tag           string            `json:"Tag,omitempty"`
	Label         map[string]string `json:"Label,omitempty"`
	StatTime      time.Time         `json:"StatTime,omitempty"`
	TotalQuota    QuotaSet          `json:"TotalQuota,omitempty"`
	AllocQuota    QuotaSet          `json:"AllocQuota,omitempty"`
	RealQuota     QuotaSet          `json:"RealQuota,omitempty"`
	AvgQuota      QuotaSet          `json:"AvgQuota,omitempty"`
	ScheduleQuota QuotaSet          `json:"ScheduleQuota,omitempty"`
	Strategy      *StrategyConfig   `json:"Strategy,omitempty"`
	WaterLevel    float32           `json:"WaterLevel,omitempty"`
	Used          int64             `json:"Used,omitempty"`
	Total         int64             `json:"Total,omitempty"`
	Type          string            `json:"Type,omitempty"`
	Dominant      string            `json:"Dominant,omitempty"`
	TaskNum       int32             `json:"TaskNum,omitempty"`
}

type JobManagerClusterApi interface {
	UpdateWaterLevel(context.Context, []*WaterLevel) error
}

type WaterLevelManager struct {
	repo WaterLevelRepo
	api  JobManagerClusterApi
	log  *log.Helper
}

type WaterLevelRepo interface {
	Save(context.Context, *WaterLevel) error
	FindLatest(context.Context, *WaterLevel) (*WaterLevel, error)
	ListAll(context.Context, *WaterLevel, int) ([]*WaterLevel, error)
}

func NewWaterLevelManager(repo WaterLevelRepo, api JobManagerClusterApi, logger log.Logger) *WaterLevelManager {
	return &WaterLevelManager{repo: repo, api: api, log: log.NewHelper(logger)}
}

func (wlm *WaterLevelManager) CreateWaterLevel(ctx context.Context, w *WaterLevel) error {
	wlm.log.WithContext(ctx).Infof("CreateWaterLevel: %v", w)
	return wlm.repo.Save(ctx, w)
}

func (wlm *WaterLevelManager) buildKey(ctx context.Context, w *Worker) string {
	if w == nil {
		return "err"
	}

	if w.Label == nil {
		w.Label = make(map[string]string)
	}

	str := w.Product + "-" + w.EngineModel + "-" + w.Tag + "-" + JsonToString(w.Label)
	u := uuid.NewSHA1(uuid.Nil, []byte(str))
	return strings.ReplaceAll(u.String(), "-", "")
}

func (wlm *WaterLevelManager) StatWaterLevel(ctx context.Context, ws []*Worker, cws []*ChildWorker, strategies map[string]*Strategy, statTime time.Time) []*WaterLevel {
	wls := make(map[string]*WaterLevel)
	// 统计水位
	for _, w := range ws {
		if w.Taint != nil && len(w.Taint) > 0 {
			wlm.log.WithContext(ctx).Warnf("water level statistics worker:%v taint", JsonToString(w))
			continue
		}

		key := wlm.buildKey(ctx, w)

		waterLevel, ok := wls[key]
		if !ok {
			strategy := wlm.getStrategy(w.Product, w.EngineModel, w.Tag, w.Label, strategies)
			waterLevel = &WaterLevel{
				Product:       w.Product,
				EngineModel:   w.EngineModel,
				Tag:           w.Tag,
				Label:         w.Label,
				StatTime:      statTime,
				TotalQuota:    map[string]int64{},
				AllocQuota:    map[string]int64{},
				RealQuota:     map[string]int64{},
				AvgQuota:      map[string]int64{},
				ScheduleQuota: map[string]int64{},
				Strategy:      strategy.Config,
			}
			wls[key] = waterLevel
		}

		waterLevel.TotalQuota.Add(w.Quota)
		waterLevel.RealQuota.Add(w.RealQuota)
		waterLevel.AllocQuota.Add(w.AllocQuota)
		waterLevel.ScheduleQuota.Add(w.ScheduleQuota)
		waterLevel.AvgQuota.Add(w.AvgQuota)
	}

	var waterLevels []*WaterLevel
	for _, level := range wls {
		if level.Strategy == nil || level.Strategy.Dominants == nil || len(level.Strategy.Dominants) == 0 {
			wlm.log.WithContext(ctx).Warnf("stat water level failed. strategy:%v is nil", JsonToString(level))
			continue
		}

		dominant := level.Strategy.Dominants[0]
		ratio := level.Strategy.LimitRatio[dominant]
		if ratio <= 0 {
			ratio := level.Strategy.LimitRatio[DefaultScheduleDominantAll]
			if ratio <= 0 {
				wlm.log.WithContext(ctx).Warnf("stat water level failed. ratio:%v is zero.", JsonToString(level))
			}
			continue
		}

		totalQuota, ok := level.TotalQuota[dominant]
		if !ok || totalQuota <= 0 {
			wlm.log.WithContext(ctx).Warnf("stat water level failed. total quota:%v is zero.", JsonToString(level))
			continue
		}

		var quota int64
		if level.Strategy.ScheduleResourceType == ScheduleResourceTypeReal {
			// 根据真实值计算
			quota = level.RealQuota[dominant]
		} else if level.Strategy.ScheduleResourceType == ScheduleResourceTypeLogic {
			// 根据逻辑值来计算
			quota = level.AllocQuota[dominant]
		} else {
			// 混跑类型 安照子三元组的类型统计
			quota, ratio = wlm.StatMixedWaterLevel(ctx, *level, cws, strategies, dominant)
		}

		used := quota
		total := (totalQuota) * ratio / 100
		level.WaterLevel = float32(used) / float32(total)

		err := wlm.repo.Save(ctx, level)
		if err != nil {
			wlm.log.WithContext(ctx).Errorf("stat water level failed. err:%v", err)
		}
		waterLevels = append(waterLevels, level)
		wlm.LogWaterLevel(ctx, level, used, total)
	}

	return waterLevels

	//err := sm.api.UpdateWaterLevel(ctx, waterLevels)
	//if err != nil {
	//	sm.log.WithContext(ctx).Errorf("stat water level failed. err:%v", err)
	//}
}

func (wlm *WaterLevelManager) StatMixedWaterLevel(ctx context.Context, level WaterLevel, cws []*ChildWorker, strategies map[string]*Strategy, dominant string) (int64, int64) {
	product := level.Product
	engineModel := level.EngineModel
	tag := level.Tag
	var quota int64
	ratio := level.Strategy.LimitRatio[dominant]
	for _, childWorker := range cws {
		if childWorker.ParentProduct != product || childWorker.ParentEngineModel != engineModel || childWorker.ParentTag != tag {
			continue
		}
		strategy := wlm.getStrategy(childWorker.Product, childWorker.EngineModel, childWorker.Tag, childWorker.Label, strategies)
		if strategy.Config.ScheduleResourceType == ScheduleResourceTypeReal {
			quota += childWorker.RealQuota[dominant]
		} else {
			quota += childWorker.AllocQuota[dominant]
		}
		if ratio < strategy.Config.LimitRatio[dominant] {
			// 混跑取最低的比例
			ratio = strategy.Config.LimitRatio[dominant]
		}
	}
	return quota, ratio
}

func (wlm *WaterLevelManager) LogWaterLevel(ctx context.Context, waterLevel *WaterLevel, used int64, total int64) {
	wlm.log.WithContext(ctx).Log(log.LevelInfo,
		"statTime", waterLevel.StatTime,
		"product", waterLevel.Product,
		"model", waterLevel.EngineModel,
		"tag", waterLevel.Tag,
		"label", waterLevel.Label,
		"percent", waterLevel.WaterLevel,
		"msg", waterLevel,
		"used", used,
		"total", total,
		"strategy", waterLevel.Strategy,
		"event", "timer.stat")
}

func (wlm *WaterLevelManager) getStrategy(product, engineModel, tag string, label map[string]string, strategies map[string]*Strategy) *Strategy {
	strategyId := MakeStrategyId(&Strategy{
		Product:     product,
		EngineModel: engineModel,
		Tag:         tag,
		Label:       label,
	})

	strategy, ok := strategies[strategyId]
	if !ok {
		strategy = &DefaultMasterStrategy
	}
	return strategy
}

func (wlm *WaterLevelManager) GetWaterLevel(ctx context.Context, w *WaterLevel) (*WaterLevel, error) {
	wlm.log.WithContext(ctx).Infof("GetWaterLevel: %v", w)
	return wlm.repo.FindLatest(ctx, w)
}

func (wlm *WaterLevelManager) ListWaterLevel(ctx context.Context, w *WaterLevel, limit int) ([]*WaterLevel, error) {
	wlm.log.WithContext(ctx).Infof("ListWaterLevel: %v %v", w, limit)
	return wlm.repo.ListAll(ctx, w, limit)
}

func (wlm *WaterLevelManager) UpdateWaterLevel(ctx context.Context, w *WaterLevel) error {
	wlm.log.WithContext(ctx).Infof("UpdateWaterLevel no implemented: %v", w)
	return nil
}

func (wlm *WaterLevelManager) NotifyWaterLevel(ctx context.Context, w []*WaterLevel) error {
	wlm.log.WithContext(ctx).Infof("NotifyWaterLevel: %v", w)
	err := wlm.api.UpdateWaterLevel(ctx, w)
	if err != nil {
		log.Warnf("notify waterleve failed. err:%v", err)
		return err
	}

	return nil
}

func (wlm *WaterLevelManager) DeleteWaterLevel(ctx context.Context, w *WaterLevel) error {
	wlm.log.WithContext(ctx).Infof("DeleteStrategy no implemented: %v", w)
	return nil
}
