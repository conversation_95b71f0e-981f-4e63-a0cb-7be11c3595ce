package biz

import (
	"context"
	"time"
)

type Mutex struct {
	Name        string    `json:"Name,omitempty"`
	Owner       string    `json:"Owner,omitempty"`
	ExpiredTime time.Time `json:"Expired,omitempty"`
	Second      uint      `json:"Second,omitempty"`
}

type MutexRepo interface {
	Lock(context.Context, *Mutex) bool
	Unlock(context.Context, *Mutex) bool
	ListLock(ctx context.Context) ([]*Mutex, error)
	ListExpired(ctx context.Context) ([]*Mutex, error)
}
