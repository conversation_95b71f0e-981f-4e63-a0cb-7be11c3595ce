package biz_test

import (
	"context"
	"time"

	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/conf"
	"mpp/internal/taskscheduler/mocks/mrepo"
	"mpp/internal/taskscheduler/testdata"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("WorkerManager", func() {
	var workerManager *biz.WorkerManager
	var mWorkerRepo *mrepo.MockWorkerRepo
	var mWorkerApi *mrepo.MockWorkerApi

	BeforeEach(func() {
		mWorkerRepo = mrepo.NewMockWorkerRepo(ctl)
		mWorkerApi = mrepo.NewMockWorkerApi(ctl)
		workerManager = biz.NewWorkerManager(&conf.App{}, mWorkerRepo, mWorkerApi, nil, log.DefaultLogger)
	})

	It("Register", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().Save(ctx, gomock.Any()).Return(worker, nil)
		result, err := workerManager.Register(ctx, worker)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result).To(Equal(worker))
	})

	It("Heartbeat", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().UpdateHeartbeat(ctx, gomock.Any(), gomock.Any()).Return(nil)
		err := workerManager.Heartbeat(ctx, worker)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
	})

	It("Unregister", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().UpdateStatus(ctx, gomock.Any(), gomock.Any()).Return(nil)
		err := workerManager.Unregister(ctx, worker)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
	})

	It("GetWorker", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().FindByWorkerId(ctx, gomock.Any(), gomock.Any()).Return(worker, nil)
		w, err := workerManager.Get(ctx, worker.WorkerId, biz.WorkerStatusOnline)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(w).To(Equal(worker))
	})

	It("ListOnlineWorker", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().ListOnlineWorker(ctx).Return([]*biz.Worker{worker}, nil)
		workers, err := workerManager.ListOnlineWorker(ctx)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(workers[0]).To(Equal(worker))
	})

	It("ListWorker", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().ListByConditions(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*biz.Worker{worker}, nil)
		w, err := workerManager.ListWorker(ctx, worker, nil)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(w[0]).To(Equal(worker))
	})

	It("TagWorker", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().UpdateTagByWorkerIds(ctx, gomock.Any(), gomock.Any()).Return(nil)
		err := workerManager.TagWorker(ctx, []string{worker.WorkerId}, nil, "new-tag")
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())

		mWorkerRepo.EXPECT().UpdateTagByIps(ctx, gomock.Any(), gomock.Any()).Return(nil)
		err = workerManager.TagWorker(ctx, nil, []string{worker.Ip}, "new-tag")
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())

		err = workerManager.TagWorker(ctx, nil, nil, "new-tag")
		Ω(err).Should(HaveOccurred())
	})

	It("TaintWorker", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().UpdateTaint(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		err := workerManager.TaintWorker(ctx, []string{worker.WorkerId}, nil, []*biz.Taint{})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
	})

	It("LabelWorker", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().UpdateLabel(ctx, gomock.Any()).Return(nil)
		err := workerManager.LabelWorker(ctx, worker.WorkerId, map[string]string{"label": "value"})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
	})

	It("UpdateQuota", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().UpdateQuota(ctx, gomock.Any(), gomock.Any()).Return(nil)
		err := workerManager.UpdateQuota(ctx, worker, biz.QuotaOptAdd)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
	})

	It("UpdateChangedQuota", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().UpdateChangeQuota(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
		ok, err := workerManager.UpdateChangedQuota(ctx, worker.WorkerId, worker.Quota, nil, biz.QuotaOptAdd)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(ok).To(BeTrue())
	})

	It("BatchUpdateWorkerStatus", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().BatchUpdateStatus(ctx, gomock.Any(), gomock.Any()).Return(nil)
		err := workerManager.BatchUpdateWorkerStatus(ctx, []string{worker.WorkerId})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
	})

	It("MultiCheckWorkerStatus", func() {
		workers := make([]*biz.Worker, 0)
		for i := 0; i < 256; i++ {
			workers = append(workers, testdata.Worker())
		}
		//mWorkerRepo.EXPECT().BatchUpdateStatus(ctx, gomock.Any(), gomock.Any()).Return(nil)
		//验证并发耗时
		mWorkerApi.EXPECT().CheckOnline(ctx, gomock.Any()).Return(true, nil).Times(128).Do(func(_ context.Context, _ *biz.Worker) {
			time.Sleep(1 * time.Millisecond)
		})
		mWorkerApi.EXPECT().CheckOnline(ctx, gomock.Any()).Return(false, nil).Times(128).Do(func(_ context.Context, _ *biz.Worker) {
			time.Sleep(1 * time.Millisecond)
		})
		t := time.Now()
		onlines, offlines := workerManager.MultiCheckWorkerStatus(ctx, workers)
		Ω(len(onlines)).Should(Equal(128))
		Ω(len(offlines)).Should(Equal(128))
		//log.Infof("%v", time.Since(t).Milliseconds())
		Ω(time.Since(t).Milliseconds() < 50).Should(Equal(true))
	})

})
