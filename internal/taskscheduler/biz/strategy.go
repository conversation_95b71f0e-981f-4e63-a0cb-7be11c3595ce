package biz

import (
	"context"
	"strings"

	"github.com/emirpasic/gods/maps/treemap"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"

	v1 "proto.mpp/api/common/v1"
)

var (
	ErrStrategyNotFound = errors.NotFound(v1.ErrorReason_STRATEGY_NOT_FOUND.String(), "strategy not found")
)

const (
	StrategyStatusInvalid      int8   = 0
	StrategyStatusValid        int8   = 1
	DefaultLimitRatio          int64  = 100
	StrategyAverage            string = "average"
	StrategyCentral            string = "central"
	StrategyRandom             string = "random"
	StrategyMix                string = "mix"
	StrategyStepRandom         string = "step_random"
	StrategyStepAverage        string = "step_average"
	StrategyStepCentral        string = "step_central"
	ScheduleResourceTypeReal   string = "real"
	ScheduleResourceTypeLogic  string = "logic"
	DefaultScheduleDominant    string = "cpu"
	DefaultScheduleDominantAll string = "*"
)

var (
	DefaultMasterStrategy = Strategy{
		Strategy: StrategyAverage,
		Config: &StrategyConfig{
			ScheduleResourceType: ScheduleResourceTypeLogic,
			LimitRatio:           map[string]int64{"cpu": 100},
			MaxTaskNum:           1000,
			LoadRatio:            100,
			Dominants:            []string{"cpu"},
		},
	}

	DefaultSlaveStrategy = Strategy{
		Strategy: StrategyRandom,
		Config: &StrategyConfig{
			ScheduleResourceType: ScheduleResourceTypeLogic,
			LimitRatio:           map[string]int64{"cpu": 100},
			MaxTaskNum:           1000,
			LoadRatio:            100,
			Dominants:            []string{"cpu"},
		},
	}
)

type StrategyStep struct {
	WorkerRatio  int    `json:"WorkerRatio,omitempty"`  // 单机阈值，百分比，取值范围[0,100]
	ClusterRatio int    `json:"ClusterRatio,omitempty"` // 集群阈值，单机阈值有碎片，集群阈值会略低于单机阈值，百分比，取值范围[0,100]
	Strategy     string `json:"Strategy,omitempty"`     // 多阶策略，会覆盖主策略配置，可选值：average、central、random
}

type ElasticConfig struct {
	MinWorkerNum int `json:"MinWorkerNum,omitempty"` // 最小worker数
	Threshold    int `json:"Threshold,omitempty"`    // 缩容阈值，百分比，取值范围[0,100]
	Ratio        int `json:"Ratio,omitempty"`        // 资源池缩容比例，百分比，取值范围[0,100]
}

type StrategyConfig struct {
	ScheduleResourceType string          `json:"ScheduleResourceType,omitempty"` // 调度类型，可选值：real、logic
	Steps                []*StrategyStep `json:"Steps,omitempty"`                // 多阶调度策略
	LimitRatio           QuotaSet        `json:"LimitRatio,omitempty"`           // 单机保护阈值，百分比，取值范围[0,100]
	MaxTaskNum           int             `json:"MaxTaskNum,omitempty"`           // 单机最大任务数
	LoadRatio            int             `json:"LoadRatio,omitempty"`            // 单机load保护阈值，百分比，取值范围[0,100]
	Dominants            []string        `json:"Dominants,omitempty"`            // 纬度优先级列表
	ElasticConfig        *ElasticConfig  `json:"ElasticConfig,omitempty"`        // 弹性配置
}

type MixStrategyConfig struct {
	ScheduleResourceType string          `json:"ScheduleResourceType,omitempty"` // 调度类型，可选值：real、logic
	Steps                []*StrategyStep `json:"Steps,omitempty"`                // 多阶调度策略
	LimitRatio           QuotaSet        `json:"LimitRatio,omitempty"`           // 单机保护阈值，百分比，取值范围[0,100]
	MaxTaskNum           int             `json:"MaxTaskNum,omitempty"`           // 单机最大任务数
	LoadRatio            int             `json:"LoadRatio,omitempty"`            // 单机load保护阈值，百分比，取值范围[0,100]
	Dominants            []string        `json:"Dominants,omitempty"`            // 纬度优先级列表
	ElasticConfig        *ElasticConfig  `json:"ElasticConfig,omitempty"`        // 弹性配置
	ProductLevel         int             `json:"ProductLevel,omitempty"`         // 产品等级，抢占使用
	SeizeAble            bool            `json:"SeizeAble,omitempty"`            // 是否抢占
}

type Strategy struct {
	StrategyId  string                 `json:"StrategyId,omitempty"`
	Product     string                 `json:"Product,omitempty"`
	EngineModel string                 `json:"EngineModel,omitempty"`
	Tag         string                 `json:"Tag,omitempty"`
	Label       map[string]string      `json:"Label,omitempty"`
	Strategy    string                 `json:"Strategy,omitempty"` // 调度策略，average、central、random
	Callback    string                 `json:"Callback,omitempty"`
	Config      *StrategyConfig        `json:"Config,omitempty"`
	Extend      map[string]interface{} `json:"Extend,omitempty"`
	MixConfig   *MixStrategyConfig     `json:"MixConfig,omitempty"`
}

type StrategyManager struct {
	repo StrategyRepo
	log  *log.Helper

	Strategies map[string]bool
}

// StrategyRepo
// 策略仓库
//
//go:generate mockgen -destination=../mocks/mrepo/strategy.go -package=mrepo . StrategyRepo
type StrategyRepo interface {
	Save(context.Context, *Strategy) (*Strategy, error)
	Update(context.Context, *Strategy) error
	UpdateStatus(context.Context, string, int8) error
	FindByStrategyId(context.Context, string) (*Strategy, error)
	ListAll(context.Context) ([]*Strategy, error)
}

func NewStrategyManager(repo StrategyRepo, logger log.Logger) *StrategyManager {
	strategies := make(map[string]bool)
	strategies[StrategyAverage] = true
	strategies[StrategyCentral] = true
	strategies[StrategyRandom] = true
	strategies[StrategyStepRandom] = true
	strategies[StrategyStepAverage] = true
	strategies[StrategyStepCentral] = true
	strategies[StrategyMix] = true
	return &StrategyManager{repo: repo, Strategies: strategies, log: log.NewHelper(logger)}
}

func (sm *StrategyManager) CreateStrategy(ctx context.Context, s *Strategy) (*Strategy, error) {
	if s.StrategyId == "" {
		s.StrategyId = MakeStrategyId(s)
	}

	return sm.repo.Save(ctx, s)
}

func (sm *StrategyManager) UpdateStrategy(ctx context.Context, s *Strategy) error {
	if s.StrategyId == "" {
		s.StrategyId = MakeStrategyId(s)
	}

	return sm.repo.Update(ctx, s)
}

func (sm *StrategyManager) DeleteStrategy(ctx context.Context, s *Strategy) error {
	if s.StrategyId == "" {
		s.StrategyId = MakeStrategyId(s)
	}

	return sm.repo.UpdateStatus(ctx, s.StrategyId, StrategyStatusInvalid)
}

func (sm *StrategyManager) GetStrategyById(ctx context.Context, id string) (*Strategy, error) {
	return sm.repo.FindByStrategyId(ctx, id)
}

func (sm *StrategyManager) GetStrategy(ctx context.Context, s *Strategy) (*Strategy, error) {
	sm.log.WithContext(ctx).Infof("GetResource: %v", s)
	if s.StrategyId == "" {
		s.StrategyId = MakeStrategyId(s)
	}

	return sm.repo.FindByStrategyId(ctx, s.StrategyId)
}

func (sm *StrategyManager) ListStrategy(ctx context.Context) ([]*Strategy, error) {
	return sm.repo.ListAll(ctx)
}

func MakeStrategyId(strategy *Strategy) string {
	labels := treemap.NewWithStringComparator()
	if strategy.Label != nil {
		// 有序后，再系列化
		for key, label := range strategy.Label {
			labels.Put(key, label)
		}
	} else {
		strategy.Label = make(map[string]string)
	}

	str := strategy.Product + "-" + strategy.EngineModel + "-" + strategy.Tag + "-" + JsonToString(labels)
	u := uuid.NewSHA1(uuid.Nil, []byte(str))
	return strings.ReplaceAll(u.String(), "-", "")
}
