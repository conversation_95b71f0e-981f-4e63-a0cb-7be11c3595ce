package biz

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"

	v1 "proto.mpp/api/common/v1"
)

var (
	ErrResourceNotFound = errors.NotFound(v1.ErrorReason_RESOURCE_NOT_FOUND.String(), "resource not found")
	ErrResourceLost     = errors.NotFound(v1.ErrorReason_RESOURCE_LOST.String(), "resource already lost.")
)

const (
	ResourceStatusStopped int8 = 0
	ResourceStatusRunning int8 = 1
	IsMasterFalse         int8 = 0
	IsMasterTrue          int8 = 1

	AffinityNo        int8 = 0
	AffinityRequired  int8 = 1
	AffinityPreferred int8 = 2
)

type Affinity struct {
	Required  []metav1.LabelSelectorRequirement `json:"required,omitempty"`
	Preferred []metav1.LabelSelectorRequirement `json:"preferred,omitempty"`
}

type Resource struct {
	JobId             string                 `json:"JobId,omitempty"`
	TaskId            string                 `json:"TaskId,omitempty"`
	Product           string                 `json:"Product,omitempty"`
	EngineModel       string                 `json:"EngineModel,omitempty"`
	ModifiedTime      time.Time              `json:"ModifiedTime,omitempty"`
	Tag               string                 `json:"Tag,omitempty"`
	Quota             QuotaSet               `json:"Quota,omitempty"`
	RealQuota         QuotaSet               `json:"RealQuota,omitempty"`
	AvgQuota          QuotaSet               `json:"AvgQuota,omitempty"`
	WorkerId          string                 `json:"WorkerId,omitempty"`
	WorkerVersion     int32                  `json:"WorkerVersion,omitempty"`
	WorkerIp          string                 `json:"WorkerIp,omitempty"`
	WorkerPort        int32                  `json:"WorkerPort,omitempty"`
	WorkerConnectMode []string               `json:"WorkerConnectMode,omitempty"`
	IsMaster          int8                   `json:"IsMaster,omitempty"`
	Label             map[string]string      `json:"Label,omitempty"`
	Affinity          *Affinity              `json:"Affinity,omitempty"`
	Schedule          map[string]interface{} `json:"Schedule,omitempty"`
	Extend            map[string]interface{} `json:"Extend,omitempty"`
	TaskType          v1.TaskType            `json:"TaskType,omitempty"`
	IsMixed           bool                   `json:"IsMixed,omitempty"`
	MixConfig         map[string]string      `json:"MixConfig,omitempty"`
}

type ResourceRepo interface {
	Save(context.Context, *Resource) (*Resource, error)
	UpdateQuota(context.Context, *Resource) error
	UpdateStatus(context.Context, string, int8) error
	FindByTaskId(context.Context, string) (*Resource, error)
	FindByWorker(context.Context, *Worker) ([]*Resource, error)
	ListAll(context.Context, *v1.TaskType) ([]*Resource, error)
	ListLost(context.Context, *v1.TaskType) ([]*Resource, error)
}

type TaskManagerApi interface {
	MigrateTasks(context.Context, []string) error
}

type ResourceManager struct {
	repo ResourceRepo
	// todo 这里scheduler 和 taskmanager 变成互相调用了，最好分层清楚scheduler可以开启让 manager 用list watch机制
	api TaskManagerApi
	log *log.Helper
}

func (a *Affinity) matchLabelKey(label map[string]string) int8 {
	// required 必需
	ls := metav1.LabelSelector{MatchExpressions: a.Required}
	selector, err := metav1.LabelSelectorAsSelector(&ls)
	if err == nil {
		if !selector.Matches(labels.Set(label)) {
			return AffinityNo
		}
	}

	// preferred 推荐
	ls = metav1.LabelSelector{MatchExpressions: a.Preferred}
	selector, err = metav1.LabelSelectorAsSelector(&ls)
	if err != nil {
		return AffinityPreferred
	}

	if selector.Matches(labels.Set(label)) {
		return AffinityPreferred
	} else {
		return AffinityRequired
	}
}

func NewResourceManager(repo ResourceRepo, api TaskManagerApi, logger log.Logger) *ResourceManager {
	return &ResourceManager{repo: repo, api: api, log: log.NewHelper(logger)}
}

func (rm *ResourceManager) CreateResource(ctx context.Context, r *Resource) (*Resource, error) {
	//rm.log.WithContext(ctx).Infof("CreateResource: %v", r)
	if r == nil || r.TaskId == "" {
		log.Warnf("CreateResource resource:%+v is null", r)
		return nil, ErrResourceNotFound
	}
	return rm.repo.Save(ctx, r)
}

func (rm *ResourceManager) UpdateResource(ctx context.Context, r *Resource) error {
	rm.log.WithContext(ctx).Infof("UpdateResource: %v", r)
	if r == nil || r.TaskId == "" {
		log.Warnf("UpdateResource resource:%+v is null", r)
		return nil
	}
	return rm.repo.UpdateQuota(ctx, r)
}

func (rm *ResourceManager) FreeResource(ctx context.Context, r *Resource) error {
	rm.log.WithContext(ctx).Infof("FreeResource: %v", r)
	if r == nil || r.TaskId == "" {
		log.Warnf("FreeResource resource:%+v is null", r)
		return nil
	}
	return rm.repo.UpdateStatus(ctx, r.TaskId, ResourceStatusStopped)
}

func (rm *ResourceManager) GetResource(ctx context.Context, id string) (*Resource, error) {
	rm.log.WithContext(ctx).Infof("GetResource: %v", id)
	if id == "" {
		log.Warnf("GetResource resource:%+v is null", id)
		return nil, ErrResourceNotFound
	}
	resource, err := rm.repo.FindByTaskId(ctx, id)
	if err != nil {
		return nil, ErrResourceNotFound
	}

	return resource, nil
}

func (rm *ResourceManager) ListResource(ctx context.Context, taskType *v1.TaskType) ([]*Resource, error) {
	return rm.repo.ListAll(ctx, taskType)
}

func (rm *ResourceManager) GetResourceByWorker(ctx context.Context, worker *Worker) ([]*Resource, error) {
	if worker == nil || worker.WorkerId == "" {
		log.Warnf("GetResourceByWorker resource:%+v is null", worker)
		return nil, ErrWorkerNotFound
	}

	return rm.repo.FindByWorker(ctx, worker)
}

func (rm *ResourceManager) MigrateResources(ctx context.Context, ids []string) error {
	if len(ids) == 0 {
		log.Warnf("MigrateResources resource:%+v is null", ids)
		return ErrResourceNotFound
	}
	return rm.api.MigrateTasks(ctx, ids)
}

func (rm *ResourceManager) ListLostResource(ctx context.Context, taskType *v1.TaskType) ([]*Resource, error) {
	return rm.repo.ListLost(ctx, taskType)
}
