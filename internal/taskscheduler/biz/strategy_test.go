package biz_test

import (
	"github.com/golang/mock/gomock"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/mocks/mrepo"
	"mpp/internal/taskscheduler/testdata"
)

var _ = Describe("StrategyManager", func() {
	var strategyManager *biz.StrategyManager
	var mStrategyRepo *mrepo.MockStrategyRepo

	BeforeEach(func() {
		mStrategyRepo = mrepo.NewMockStrategyRepo(ctl)
		strategyManager = biz.NewStrategyManager(mStrategyRepo, nil)
	})

	It("Create", func() {
		strategy := testdata.Strategy()
		mStrategyRepo.EXPECT().Save(ctx, gomock.Any()).Return(strategy, nil)
		result, err := strategyManager.CreateStrategy(ctx, strategy)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result).To(Equal(strategy))

	})

	It("GetStrategyById", func() {
		strategy := testdata.Strategy()
		mStrategyRepo.EXPECT().FindByStrategyId(ctx, gomock.Any()).Return(strategy, nil)
		result, err := strategyManager.GetStrategyById(ctx, strategy.StrategyId)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result).To(Equal(strategy))
	})

	It("UpdateStrategy", func() {
		strategy := testdata.Strategy()
		strategy.Strategy = biz.StrategyCentral
		mStrategyRepo.EXPECT().Update(ctx, gomock.Any()).Return(nil)
		err := strategyManager.UpdateStrategy(ctx, strategy)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
	})

	It("DeleteStrategy", func() {
		strategy := testdata.Strategy()
		mStrategyRepo.EXPECT().UpdateStatus(ctx, gomock.Any(), gomock.Any()).Return(nil)
		err := strategyManager.DeleteStrategy(ctx, strategy)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
	})

	It("ListStrategy", func() {
		strategies := testdata.StrategyList(10)
		mStrategyRepo.EXPECT().ListAll(ctx).Return(strategies, nil)
		result, err := strategyManager.ListStrategy(ctx)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result).To(Equal(strategies))
	})

})
