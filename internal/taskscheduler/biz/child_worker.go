package biz

import (
	"context"
	"mpp/internal/taskscheduler/conf"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

type ChildWorker struct {
	WorkerId           string                 `json:"WorkerId,omitempty"`           // worker唯一ID
	ParentWorkerId     string                 `json:"ParentWorkerId,omitempty"`     // 父workerID
	Ip                 string                 `json:"Ip,omitempty"`                 // worker IP 地址
	PodName            string                 `json:"PodName,omitempty"`            // pod 名称
	NodeName           string                 `json:"NodeName,omitempty"`           // node 名称，L2时方便定位问题
	Port               int32                  `json:"Port,omitempty"`               // 端口
	Product            string                 `json:"Product,omitempty"`            // 产品
	EngineModel        string                 `json:"EngineModel,omitempty"`        // 引擎
	Tag                string                 `json:"Tag,omitempty"`                // 标签，用于划分小集群
	ParentProduct      string                 `json:"ParentProduct,omitempty"`      // 产品
	ParentEngineModel  string                 `json:"ParentEngineModel,omitempty"`  // 引擎
	ParentTag          string                 `json:"ParentTag,omitempty"`          // 标签，用于划分小集群
	Version            string                 `json:"Version,omitempty"`            // 引擎版本
	Load               float32                `json:"Load,omitempty"`               // 机器负载
	OnlineVersion      int32                  `json:"OnlineVersion,omitempty"`      // 在线版本，每注册一次加1
	UpdateQuotaVersion int32                  `json:"UpdateQuotaVersion,omitempty"` // 更新一次Quota，每更新一次加1
	TaskNum            int32                  `json:"TaskNum,omitempty"`            // 机器任务数
	Quota              QuotaSet               `json:"Quota,omitempty"`              // worker多纬度总资源，由worker注册时汇报
	AllocQuota         QuotaSet               `json:"AllocQuota,omitempty"`         // 按逻辑quota，多纬度已分配资源，
	RealQuota          QuotaSet               `json:"RealQuota,omitempty"`          // worker心跳汇报，多纬度真实资源
	AvgQuota           QuotaSet               `json:"AvgQuota,omitempty"`           // worker心跳汇报，多纬度滑动平均资源，1分钟窗口
	ScheduleQuota      QuotaSet               `json:"ScheduleQuota,omitempty"`      // 可调度Quota，考虑分配时，Quota的冻结时间
	ScheduleConfig     map[string]interface{} `json:"ScheduleConfig,omitempty"`     // 调度配置，包括资源类型，Real：根据真实资源调度，Logic:根据逻辑资源调度
	Label              map[string]string      `json:"Label,omitempty"`              // worker标签组
	Taint              []*Taint               `json:"Taint,omitempty"`              // worker污点组
	HeartbeatTime      time.Time              `json:"HeartbeatTime,omitempty"`      // 最近一次心跳时间
	LatestAllocTime    time.Time              `json:"LatestAllocTime,omitempty"`    // 最近一次申请资源时间
	ConnectMode        []string               `json:"ConnectMode,omitempty"`        // 访问worker模式，direct:直连，gateway:网关代理
	Extend             map[string]interface{} `json:"Extend,omitempty"`             // 扩展字段
	Metadata           map[string]string      `json:"Metadata,omitempty"`           // metadata信息，包括机器的机型等
}

type ChildWorkerRepo interface {
	Save(context.Context, *ChildWorker) (*ChildWorker, error)
	UpdateHeartbeat(context.Context, *ChildWorker, bool) error
	UpdateQuota(context.Context, *ChildWorker, string) error
	UpdateChangeQuota(ctx context.Context, workerId string, quota QuotaSet, scheduleConfig *StrategyConfig, opt string) (bool, error)
	UpdateStatus(context.Context, string, int8) error
	BatchUpdateStatus(context.Context, []string, int8) error
	//根据父workerId批量更新状态
	BatchUpdateStatusByParentWorkerIds(ctx context.Context, parentWorkerIds []string, status int8) error
	UpdateLabel(context.Context, *ChildWorker) error
	UpdateTaint(context.Context, []string, []string, []*Taint) error
	UpdateTagByIps(context.Context, []string, string) error
	UpdateTagByWorkerIds(context.Context, []string, string) error
	FindByWorkerId(context.Context, string, int8) (*ChildWorker, error)
	FindByIp(context.Context, string) (*ChildWorker, error)
	ListOnlineWorker(context.Context) ([]*ChildWorker, error)
	ListByConditions(context.Context, string, string, string, []string) ([]*ChildWorker, error)
	ListByWorkerIds(context.Context, []string) ([]*ChildWorker, error)
	ListByIps(context.Context, []string) ([]*ChildWorker, error)
}

func NewChildWorkerManager(c *conf.App, repo ChildWorkerRepo, client TaskScheduleMasterApi, logger log.Logger) *ChildWorkerManager {
	return &ChildWorkerManager{
		repo:         repo,
		masterClient: client,
		conf:         c,
		log:          log.NewHelper(logger),
		labelManager: NewLabelManager(MPSProducts),
	}
}

type ChildWorkerManager struct {
	repo         ChildWorkerRepo
	masterClient TaskScheduleMasterApi
	conf         *conf.App
	log          *log.Helper
	labelManager *LabelManager // 添加 LabelManager
}

func (cwm *ChildWorkerManager) Get(ctx context.Context, id string, status int8) (*ChildWorker, error) {
	//wm.log.WithContext(ctx).Infof("Worker Get: %v", id)
	if id == "" {
		log.Warnf("Get workerIds:%+v is null", id)
		return nil, ErrWorkerNotFound
	}

	worker, err := cwm.repo.FindByWorkerId(ctx, id, status)
	if err != nil {
		return nil, err
	}

	return worker, nil
}

func (cwm *ChildWorkerManager) Heartbeat(ctx context.Context, cw *ChildWorker) error {
	if cw == nil || cw.WorkerId == "" {
		log.Warnf("Heartbeat worker:%+v is null", cw)
		return ErrWorkerNotFound
	}

	isUpdateSchedule := true
	//if cwm.conf.DelayScheduleUpdate {
	//	masterWorker, err := cwm.masterClient.GetMasterWorker(ctx, cw.WorkerId)
	//	if err == nil && masterWorker != nil {
	//		// 更新allocQuota, schedulerQuota, taskNum
	//		cw.AllocQuota = masterWorker.AllocQuota
	//		cw.ScheduleQuota = masterWorker.ScheduleQuota
	//		cw.TaskNum = masterWorker.TaskNum
	//		isUpdateSchedule = true
	//		cwm.log.WithContext(ctx).Infof("Heartbeat delay update scheduler success. worker:%+v, masterWorker:%+v", w, masterWorker)
	//	}
	//}

	return cwm.repo.UpdateHeartbeat(ctx, cw, isUpdateSchedule)
}

func (cwm *ChildWorkerManager) ListOnlineWorker(ctx context.Context) ([]*ChildWorker, error) {
	return cwm.repo.ListOnlineWorker(ctx)
}
func (cwm *ChildWorkerManager) ListWorker(ctx context.Context, worker *Worker, ips []string) ([]*ChildWorker, error) {
	if worker == nil {
		return cwm.repo.ListByConditions(ctx, "", "", "", ips)
	}
	return cwm.repo.ListByConditions(ctx, worker.Product, worker.EngineModel, worker.Tag, ips)
}

func (cwm *ChildWorkerManager) Register(ctx context.Context, w *ChildWorker) (*ChildWorker, error) {
	cwm.log.WithContext(ctx).Infof("ChildWorker Register: %v", w)
	if w == nil || w.WorkerId == "" {
		log.Warnf("Register childWorker:%+v is null", w)
		return nil, ErrWorkerNotFound
	}
	//尝试添加resourceType label
	cwm.labelManager.AddResourceType(w.Product, &w.Label, w.Quota)

	return cwm.repo.Save(ctx, w)
}

func (cwm *ChildWorkerManager) Unregister(ctx context.Context, w *ChildWorker) error {
	cwm.log.WithContext(ctx).Infof("ChildWorker Unregister: %v", w)
	if w == nil || w.WorkerId == "" {
		log.Warnf("Unregister worker:%+v is null", w)
		return ErrWorkerNotFound
	}

	return cwm.repo.UpdateStatus(ctx, w.WorkerId, WorkerStatusOffline)
}

func (cwm *ChildWorkerManager) TaintChildWorker(ctx context.Context, workerIds []string, workerIps []string, taints []*Taint) error {
	cwm.log.WithContext(ctx).Infof("TaintChildWorker: workerIds:%+v workerIps:%+v taints:%+v",
		workerIds, workerIps, taints)
	if workerIds == nil && workerIps == nil {
		log.Warnf("TagChildWorker workerIds:%+v workerIps:%+v param invalid.", workerIds, workerIps)
		return ErrWorkerNotFound
	}

	return cwm.repo.UpdateTaint(ctx, workerIds, workerIps, taints)
}

func (cwm *ChildWorkerManager) BatchUpdateWorkerStatus(ctx context.Context, workerIds []string) error {
	cwm.log.WithContext(ctx).Infof("Worker BatchUpdateWorkerStatus: %v", workerIds)
	if len(workerIds) == 0 {
		log.Warnf("BatchUpdateWorkerStatus workerIds:%v is null", workerIds)
		return nil
	}

	if err := cwm.repo.BatchUpdateStatus(ctx, workerIds, WorkerStatusOffline); err != nil {
		return err
	}

	return nil
}

func (cwm *ChildWorkerManager) BatchUpdateWorkerStatusByParentWorkerIds(ctx context.Context, parentWorkerIds []string) error {
	cwm.log.WithContext(ctx).Infof("Worker BatchUpdateWorkerStatusByParentWorkerIds: %v", parentWorkerIds)
	if len(parentWorkerIds) == 0 {
		log.Warnf("BatchUpdateWorkerStatusByParentWorkerIds parentWorkerIds:%v is null", parentWorkerIds)
		return nil
	}

	if err := cwm.repo.BatchUpdateStatusByParentWorkerIds(ctx, parentWorkerIds, WorkerStatusOffline); err != nil {
		return err
	}
	return nil
}

func (cwm *ChildWorkerManager) LogWorker(ctx context.Context, worker *ChildWorker, event string, f string, ok bool, msg interface{}) {
	cwm.log.WithContext(ctx).Log(log.LevelInfo,
		"parent.worker.id", worker.ParentWorkerId,
		"worker.id", worker.WorkerId,
		"worker.ip", worker.Ip,
		"worker.taint", worker.Taint,
		"event", event,
		"func", f,
		"info", worker,
		"ok", ok,
		"msg", msg,
	)
}
