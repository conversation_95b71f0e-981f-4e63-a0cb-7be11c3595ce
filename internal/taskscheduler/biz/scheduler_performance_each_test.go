package biz_test

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/testdata"
	"testing"
	"time"
)

var (
	//mockWorkerSize           = 20000
	//mockQuotaResource        = 320000
	//mockCentralQuotaResource = 30000
	mockWorkerSize           = 2
	mockQuotaResource        = 32
	mockCentralQuotaResource = 30
)

func TestSchedulerAverage(t *testing.T) {
	//test("product", "engine_moieee", "tag")
	var scheduleManager *biz.ScheduleManager
	scheduleManager = biz.NewScheduleManager(nil, log.GetLogger())
	workers := testdata.ScheduleWorkerList(mockWorkerSize)
	// 使用随机quota，耗时从6s -> 21s
	resources := testdata.ScheduleResourceListRandomQuota(mockQuotaResource)
	strategies := testdata.ScheduleStrategyList(0)
	scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
	start := time.Now().UnixMicro()

	//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
	for _, resource := range resources {
		_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		assert.Nil(t, err)
	}

	end := time.Now().UnixMicro()
	log.Infof("average schedule: worker:%v resource:%v, alloc cost time:%v", len(workers), len(resources), end-start)

	for _, resource := range resources {
		_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		assert.Nil(t, err)
	}

	free := time.Now().UnixMicro()
	log.Infof("average schedule: worker:%v resource:%v, free cost time:%v", len(workers), len(resources), free-end)
}

func TestSchedulerCentral(t *testing.T) {
	//test("product", "engine_moieee", "tag")
	var scheduleManager *biz.ScheduleManager
	scheduleManager = biz.NewScheduleManager(nil, log.GetLogger())
	workers := testdata.ScheduleWorkerList(mockWorkerSize)
	resources := testdata.ScheduleResourceListRandomQuota(mockQuotaResource)
	strategies := []*biz.Strategy{testdata.ScheduleStrategy(biz.StrategyCentral)}
	t1 := time.Now().UnixMicro()
	scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
	start := time.Now().UnixMicro()
	log.Infof("central schedule: worker:%v resource:%v, init time:%v", len(workers), len(resources), start-t1)
	log.Infof("=========================================================")

	//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
	for _, resource := range resources {
		_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		assert.Nil(t, err)
		//log.Infof("worker:%v", biz.JsonToString(workerQuota))
	}

	end := time.Now().UnixMicro()
	log.Infof("central schedule: worker:%v resource:%v, alloc time:%v", len(workers), len(resources), end-start)
	log.Infof("=========================================================")

	scheduleManager.SyncWorkers(ctx, workers, strategies)
	sync := time.Now().UnixMicro()

	log.Infof("central schedule: worker:%v resource:%v, sync time:%v", len(workers), len(resources), sync-end)
	log.Infof("=========================================================")

	for _, resource := range resources {
		_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		assert.Nil(t, err)
	}

	free := time.Now().UnixMicro()
	log.Infof("central schedule: worker:%v resource:%v, free time:%v", len(workers), len(resources), free-sync)

	log.Infof("=========================================================")
	for _, resource := range resources {
		_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		assert.Nil(t, err)
		//log.Infof("worker:%v", biz.JsonToString(workerQuota))
	}
	for _, resource := range resources {
		_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		assert.Nil(t, err)
	}
	redo := time.Now().UnixMicro()
	log.Infof("central schedule redo alloc+free: worker:%v resource:%v, free time:%v", len(workers), len(resources), redo-free)
}

func TestSchedulerCentralWithRandomScheduler(t *testing.T) {
	//test("product", "engine_moieee", "tag")
	var scheduleManager *biz.ScheduleManager
	scheduleManager = biz.NewScheduleManager(nil, log.GetLogger())
	workers := testdata.ScheduleWorkerList(mockWorkerSize)
	resources := testdata.ScheduleResourceListRandomQuota(mockQuotaResource)
	strategy := testdata.ScheduleStrategy(biz.StrategyStepCentral)
	strategy.Extend = map[string]interface{}{"RandomScheduler": true, "RandomPercent": 50}
	strategies := []*biz.Strategy{strategy}
	t1 := time.Now().UnixMicro()
	scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
	start := time.Now().UnixMicro()
	log.Infof("central schedule: worker:%v resource:%v, init time:%v", len(workers), len(resources), start-t1)
	log.Infof("=========================================================")

	//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
	for _, resource := range resources {
		_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		assert.Nil(t, err)
		//log.Infof("worker:%v", biz.JsonToString(workerQuota))
	}

	end := time.Now().UnixMicro()
	log.Infof("central schedule: worker:%v resource:%v, alloc time:%v", len(workers), len(resources), end-start)
	log.Infof("=========================================================")

	scheduleManager.SyncWorkers(ctx, workers, strategies)
	sync := time.Now().UnixMicro()

	log.Infof("central schedule: worker:%v resource:%v, sync time:%v", len(workers), len(resources), sync-end)
	log.Infof("=========================================================")

	for _, resource := range resources {
		_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		assert.Nil(t, err)
	}

	free := time.Now().UnixMicro()
	log.Infof("central schedule: worker:%v resource:%v, free time:%v", len(workers), len(resources), free-sync)

	log.Infof("=========================================================")
	for _, resource := range resources {
		_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		assert.Nil(t, err)
		//log.Infof("worker:%v", biz.JsonToString(workerQuota))
	}
	for _, resource := range resources {
		_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		assert.Nil(t, err)
	}
	redo := time.Now().UnixMicro()
	log.Infof("central schedule redo alloc+free: worker:%v resource:%v, free time:%v", len(workers), len(resources), redo-free)
}

// 域名打散
func TestSchedulerCentralWithRandomDomain(t *testing.T) {
	//test("product", "engine_moieee", "tag")
	var scheduleManager *biz.ScheduleManager
	scheduleManager = biz.NewScheduleManager(nil, log.GetLogger())
	workers := testdata.ScheduleWorkerList(mockWorkerSize)
	resources := testdata.ScheduleResourceListRandomQuota(mockQuotaResource)
	strategy := testdata.ScheduleStrategy(biz.StrategyStepCentral)
	strategy.Extend = map[string]interface{}{"RandomDomain": "test", "RandomPercent": 50}
	strategies := []*biz.Strategy{strategy}
	t1 := time.Now().UnixMicro()
	scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
	start := time.Now().UnixMicro()
	log.Infof("central schedule: worker:%v resource:%v, init time:%v", len(workers), len(resources), start-t1)
	log.Infof("=========================================================")

	//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
	for _, resource := range resources {
		resource.Extend = map[string]interface{}{"domain": "test"}
		_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		assert.Nil(t, err)
		//log.Infof("worker:%v", biz.JsonToString(workerQuota))
	}

	end := time.Now().UnixMicro()
	log.Infof("central schedule: worker:%v resource:%v, alloc time:%v", len(workers), len(resources), end-start)
	log.Infof("=========================================================")

	scheduleManager.SyncWorkers(ctx, workers, strategies)
	sync := time.Now().UnixMicro()

	log.Infof("central schedule: worker:%v resource:%v, sync time:%v", len(workers), len(resources), sync-end)
	log.Infof("=========================================================")

	for _, resource := range resources {
		_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		assert.Nil(t, err)
	}

	free := time.Now().UnixMicro()
	log.Infof("central schedule: worker:%v resource:%v, free time:%v", len(workers), len(resources), free-sync)

	log.Infof("=========================================================")
	for _, resource := range resources {
		_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		assert.Nil(t, err)
		//log.Infof("worker:%v", biz.JsonToString(workerQuota))
	}
	for _, resource := range resources {
		_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		assert.Nil(t, err)
	}
	redo := time.Now().UnixMicro()
	log.Infof("central schedule redo alloc+free: worker:%v resource:%v, free time:%v", len(workers), len(resources), redo-free)
}

func TestSchedulerRandom(t *testing.T) {
	var scheduleManager *biz.ScheduleManager
	scheduleManager = biz.NewScheduleManager(nil, log.GetLogger())
	//test("product", "engine_moieee", "tag")
	workers := testdata.ScheduleWorkerList(mockWorkerSize)
	resources := testdata.ScheduleResourceListRandomQuota(mockQuotaResource)
	strategies := []*biz.Strategy{testdata.ScheduleStrategy(biz.StrategyRandom)}
	scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
	start := time.Now().UnixMicro()

	//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
	for _, resource := range resources {
		_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		assert.Nil(t, err)
		//log.Infof("worker:%v", biz.JsonToString(workerQuota))
	}

	end := time.Now().UnixMicro()
	log.Infof("random schedule: worker:%v resource:%v, cost time:%v", len(workers), len(resources), end-start)

	for _, resource := range resources {
		_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		assert.Nil(t, err)
	}

	free := time.Now().UnixMicro()
	log.Infof("random schedule: worker:%v resource:%v, free cost time:%v", len(workers), len(resources), free-end)
}

func TestScheduleStepRandom(t *testing.T) {
	var scheduleManager *biz.ScheduleManager

	scheduleManager = biz.NewScheduleManager(nil, log.GetLogger())

	//test("product", "engine_moieee", "tag")
	workers := testdata.ScheduleWorkerList(mockWorkerSize)
	// 原来的基准测试数据太极端，这里使用随机quota数据，测试更真实的性能情况
	resources := testdata.ScheduleResourceListRandomQuota(mockQuotaResource)
	strategies := []*biz.Strategy{testdata.ScheduleStrategyWithSteps(biz.StrategyStepRandom, 8)}
	scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
	start := time.Now().UnixMicro()

	//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
	for _, resource := range resources {
		_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		assert.Nil(t, err)
		//log.Infof("worker:%v", biz.JsonToString(workerQuota))
	}

	end := time.Now().UnixMicro()
	log.Infof("random schedule: worker:%v resource:%v, cost time:%v", len(workers), len(resources), end-start)

	for _, resource := range resources {
		_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		assert.Nil(t, err)
	}

	free := time.Now().UnixMicro()
	log.Infof("random schedule: worker:%v resource:%v, free cost time:%v", len(workers), len(resources), free-end)
}

func TestSyncResource(t *testing.T) {
	var scheduleManager *biz.ScheduleManager
	scheduleManager = biz.NewScheduleManager(nil, log.GetLogger())

	workers := testdata.ScheduleWorkerList(mockWorkerSize)
	resources := testdata.ScheduleResourceListRandomQuota(mockQuotaResource)
	strategies := []*biz.Strategy{testdata.ScheduleStrategyWithSteps(biz.StrategyStepRandom, 8)}
	t1 := time.Now().UnixMicro()
	scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
	start := time.Now().UnixMicro()
	log.Infof("average schedule: worker:%v resource:%v, init time:%v", len(workers), len(resources), start-t1)
	log.Infof("=========================================================")

	//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
	for _, resource := range resources {
		_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		assert.Nil(t, err)
		//log.Infof("worker:%v", biz.JsonToString(workerQuota))
	}

	end := time.Now().UnixMicro()
	log.Infof("average schedule: worker:%v resource:%v, alloc time:%v", len(workers), len(resources), end-start)
	log.Infof("=========================================================")

	scheduleManager.SyncWorkers(ctx, workers, strategies)
	sync := time.Now().UnixMicro()

	log.Infof("average schedule: worker:%v resource:%v, sync time:%v", len(workers), len(resources), sync-end)
	log.Infof("=========================================================")

	for _, resource := range resources {
		_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		assert.Nil(t, err)
	}

	free := time.Now().UnixMicro()
	log.Infof("average schedule: worker:%v resource:%v, free time:%v", len(workers), len(resources), free-sync)

	workers[0].OnlineVersion = 2
	scheduleManager.SyncWorkers(ctx, workers, strategies)
	log.Infof("average schedule: worker:%v resource:%v, sync time:%v", len(workers), len(resources), sync-end)
}

func TestScheduleStepCentral(t *testing.T) {
	var scheduleManager *biz.ScheduleManager

	scheduleManager = biz.NewScheduleManager(nil, log.GetLogger())

	//test("product", "engine_moieee", "tag")
	workers := testdata.ScheduleWorkerList(mockWorkerSize)
	resources := testdata.ScheduleResourceListRandomQuota(mockQuotaResource)
	strategies := []*biz.Strategy{testdata.ScheduleStrategyWithSteps(biz.StrategyStepCentral, 2)}
	scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
	start := time.Now().UnixMicro()

	//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
	for _, resource := range resources {
		_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		assert.Nil(t, err)
		//log.Infof("worker:%v", biz.JsonToString(workerQuota))
	}

	end := time.Now().UnixMicro()
	log.Infof("step_central schedule: worker:%v resource:%v, cost time:%v", len(workers), len(resources), end-start)

	for _, resource := range resources {
		_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		assert.Nil(t, err)
	}

	free := time.Now().UnixMicro()
	log.Infof("step_central schedule: worker:%v resource:%v, free cost time:%v", len(workers), len(resources), free-end)
}

func TestScheduleStepCentralWorkerNum(t *testing.T) {
	var scheduleManager *biz.ScheduleManager

	scheduleManager = biz.NewScheduleManager(nil, log.GetLogger())

	//test("product", "engine_moieee", "tag")
	workers := testdata.ScheduleWorkerList(mockWorkerSize)
	//resources := testdata.ScheduleResourceListRandomQuota(320000)
	resources := testdata.ScheduleResourceList(mockCentralQuotaResource)
	strategies := []*biz.Strategy{testdata.ScheduleStrategyWithSteps(biz.StrategyStepCentral, 2)}
	scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
	start := time.Now().UnixMicro()

	//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
	for _, resource := range resources {
		_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
		assert.Nil(t, err)
		//log.Infof("worker:%v", biz.JsonToString(workerQuota))
	}

	end := time.Now().UnixMicro()
	log.Infof("step_central schedule: worker:%v resource:%v, cost time:%v", len(workers), len(resources), end-start)

	// 查看任务分布是否符合多阶: 一部分worker有任务，一部分完全没任务
	var taskNumMap = make(map[int]int)
	for _, worker := range workers {
		taskNumMap[int(worker.TaskNum)]++
	}

	for k, v := range taskNumMap {
		log.Infof("taskNum:%v, workerNum:%v", k, v)
	}

	for _, resource := range resources {
		_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
		assert.Nil(t, err)
	}

	free := time.Now().UnixMicro()
	log.Infof("step_central schedule: worker:%v resource:%v, free cost time:%v", len(workers), len(resources), free-end)
}
