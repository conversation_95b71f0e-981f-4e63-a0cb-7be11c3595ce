package biz

import (
	"context"
	"errors"
)

// CentralScheduler 集中策略
type CentralScheduler struct {
	BaseScheduler
}

// Alloc 集中策略(根据真实资源多阶集中分配：直播转码)   没有考虑集群水位情况
func (cs *CentralScheduler) Alloc(ctx context.Context, resource *Resource) (*WorkerQuota, error) {
	resourceType := cs.Strategy.Config.ScheduleResourceType
	dominant := cs.Strategy.Config.Dominants[0]
	quotaTreeMap, ok := cs.WorkerQuotaMap[resourceType]
	if !ok {
		cs.log.WithContext(ctx).Errorf("central scheduler, taskId:%v unknown schedule resource type:%v not exist.", resource.TaskId, resourceType)
		return nil, ErrWorkerNotFound
	}
	keys := quotaTreeMap.Keys()

	start := cs.search(keys, resource.Quota[dominant])
	if start == len(keys) {
		cs.log.WithContext(ctx).<PERSON><PERSON><PERSON>("central scheduler, taskId:%v not enough quota. type:%v not exist.", resource.TaskId, resourceType)
		return nil, ErrNotEnoughQuota
	}

	var backupWorkerQuota *WorkerQuota
	for i := start; i < len(keys); i++ {
		treeMapKey := keys[i]
		value, _ := quotaTreeMap.Get(treeMapKey)
		var workerQuota *WorkerQuota
		if workerQuotaMap, ok := value.(map[string]*WorkerQuota); ok {
			for _, workerQuota = range workerQuotaMap {
				if wq, err := cs.allocWorkerQuota(ctx, resource, workerQuota); err == nil {
					return wq, nil
				} else if errors.Is(err, ErrNotEnoughQuota) {
					break
				} else if errors.Is(err, ErrMatchLabelPreferredKey) && backupWorkerQuota == nil {
					backupWorkerQuota = workerQuota
				}
			}
		}
	}

	if backupWorkerQuota != nil {
		// backup
		cs.log.WithContext(ctx).Errorf("central scheduler, taskId:%v no affinity worker, use backup. %v",
			resource.TaskId, JsonToString(cs.WorkerQuotaMap))

		return cs.allocWorkerQuotaWithAffinity(ctx, resource, backupWorkerQuota, true)
	}

	cs.log.WithContext(ctx).Errorf("central scheduler, tasker:%v alloc resource failed. type:%v not exist.", JsonToString(resource),
		resourceType)
	return nil, ErrWorkerNotFound
}
