package biz

import (
	"context"
	"fmt"
	"math"
	"sort"

	"github.com/emirpasic/gods/maps/treemap"
	"github.com/emirpasic/gods/utils"
	"github.com/go-kratos/kratos/v2/log"
)

// BaseScheduler 平均策略
type BaseScheduler struct {
	WorkerQuotaMap map[string]*treemap.Map
	Strategy       *Strategy   `json:"Strategy,omitempty"` // 排序优先级
	WorkerNum      int         `json:"WorkerNum,omitempty"`
	WaterLevel     *WaterLevel `json:"WaterLevel,omitempty"`

	log *log.Helper
}

// 有序数组中查找大于等于quota的位置
func (bs *BaseScheduler) search(keys []interface{}, quota int64) int {
	length := len(keys)

	// 使用二分查找查找元素
	return sort.Search(length, func(i int) bool {
		return keys[i].(int64) >= quota
	})
}

func (bs *BaseScheduler) allocWorkerQuota(ctx context.Context, resource *Resource, workerQuota *WorkerQuota) (*WorkerQuota, error) {
	return bs.allocWorkerQuotaWithAffinity(ctx, resource, workerQuota, false)
}

func (bs *BaseScheduler) allocWorkerQuotaWithAffinity(ctx context.Context, resource *Resource, workerQuota *WorkerQuota, ignoreAffinity bool) (*WorkerQuota, error) {
	err := bs.isLimited(resource, workerQuota)
	if err != nil {
		return nil, err
	}

	if workerQuota.RestQuota.QuotaGEQ(resource.Quota) {
		// todo restquota 需要修改心跳时的混跑的逻辑与真实quota消耗
		if !ignoreAffinity && resource.Affinity != nil {
			switch resource.Affinity.matchLabelKey(workerQuota.Label) {
			case AffinityPreferred:
				log.Infof("==========task affinity perferred success. resource:%v worker:%v affinity:%v label:%v",
					resource.TaskId, workerQuota.Worker.WorkerId, JsonToString(resource.Affinity), JsonToString(workerQuota.Label))
			case AffinityRequired:
				log.Infof("==========task affinity required success, perferred fail. resource:%v worker:%v affinity:%v label:%v", resource.TaskId,
					workerQuota.Worker.WorkerId, JsonToString(resource.Affinity), JsonToString(workerQuota.Label))
				return nil, ErrMatchLabelPreferredKey
			case AffinityNo:
				log.Infof("==========task affinity required failed, perferred failed. resource:%v worker:%v affinity:%v label:%v", resource.TaskId,
					workerQuota.Worker.WorkerId, JsonToString(resource.Affinity), JsonToString(workerQuota.Label))
				return nil, ErrMatchLabelKey
			}
		}

		//log.Debugf("Scheduler alloc quota success. resource:%v worker:%v", JsonToString(resource), JsonToString(workerQuota))
		bs.Remove(ctx, workerQuota)
		workerQuota.RestQuota.Sub(resource.Quota)
		workerQuota.Worker.AllocQuota.Add(resource.Quota)
		bs.Put(ctx, workerQuota)
		workerQuota.Resources[resource.TaskId] = resource
		workerQuota.Worker.UpdateQuotaVersion += 1
		workerQuota.Worker.TaskNum = int32(len(workerQuota.Resources))
		//log.Infof("%v Scheduler alloc quota success. resource:%v worker:%v", bs.Strategy.Strategy,
		//	JsonToString(resource), JsonToString(workerQuota.Worker))
		return workerQuota, nil
	} else {
		if workerQuota.RestQuota[bs.Strategy.Config.Dominants[0]] >= resource.Quota[bs.Strategy.Config.Dominants[0]] {
			log.Warnf("%v Scheduler alloc quota failed, no enough no dominant quota. resource:%v worker:%v", bs.Strategy.Strategy,
				JsonToString(resource), JsonToString(workerQuota.Worker))
			return nil, ErrNotEnoughNoDominantQuota
		} else {
			log.Warnf("%v Scheduler alloc quota failed, no enough dominant quota. resource:%v worker:%v", bs.Strategy.Strategy,
				JsonToString(resource), JsonToString(workerQuota.Worker))
			return nil, ErrNotEnoughQuota
		}
	}
}

func (bs *BaseScheduler) isLimited(resource *Resource, workerQuota *WorkerQuota) error {
	if len(workerQuota.Resources) >= bs.Strategy.Config.MaxTaskNum {
		log.Infof("%v Scheduler alloc worker num limit. resource:%v workerId:%v taskNum:%v", bs.Strategy.Strategy,
			resource.TaskId, workerQuota.Worker.WorkerId, len(workerQuota.Resources))
		return ErrMaxTaskNum
	}

	// 根据CPU算load保护
	if quota, ok := workerQuota.Worker.Quota[DefaultScheduleDominant]; ok {
		loadLimit := float32(bs.Strategy.Config.LoadRatio) / 100 * float32(quota/1000)
		if workerQuota.Worker.Load > loadLimit {
			log.Infof("%v Scheduler alloc worker load limit. resource:%v workerId:%v load:%v limit:%v",
				bs.Strategy.Strategy, resource.TaskId, workerQuota.Worker.WorkerId, workerQuota.Worker.Load, loadLimit)
			return ErrLoadOverflow
		}
	}

	// 根据各个纬度限制保护
	if nil != bs.Strategy.Config.LimitRatio {
		for key, ratio := range bs.Strategy.Config.LimitRatio {

			if _, ok := workerQuota.Worker.Quota[key]; !ok {
				continue
			}

			if bs.Strategy.Config.ScheduleResourceType == ScheduleResourceTypeReal {
				if _, ok := workerQuota.Worker.RealQuota[key]; !ok {
					continue
				}

				if workerQuota.Worker.RealQuota[key] > workerQuota.Worker.Quota[key]*ratio/100 {
					log.Infof("%v Scheduler alloc worker quota set limit. resource:%v workerId:%v real quota:%v total quota:%v ratio:%v limit:%v",
						bs.Strategy.Strategy, resource.TaskId, workerQuota.Worker.WorkerId, workerQuota.Worker.RealQuota[key],
						workerQuota.Worker.Quota[key], ratio, workerQuota.Worker.Quota[key]*ratio/100)
					return ErrQuotaSetOverflow
				}
			} else {
				if _, ok := workerQuota.Worker.AllocQuota[key]; !ok {
					continue
				}

				if workerQuota.Worker.AllocQuota[key] > workerQuota.Worker.Quota[key]*ratio/100 {
					log.Infof("%v Scheduler alloc worker quota set limit. resource:%v workerId:%v alloc quota:%v total quota:%v ratio:%v limit:%v",
						bs.Strategy.Strategy, resource.TaskId, workerQuota.Worker.WorkerId, workerQuota.Worker.AllocQuota[key],
						workerQuota.Worker.Quota[key], ratio, workerQuota.Worker.Quota[key]*ratio/100)
					return ErrQuotaSetOverflow
				}
			}
		}
	}

	return nil
}

func (bs *BaseScheduler) Free(ctx context.Context, resource *Resource, workerQuota *WorkerQuota) error {
	resourceType := bs.Strategy.Config.ScheduleResourceType
	quotaTreeMap, ok := bs.WorkerQuotaMap[resourceType]
	if !ok {
		log.Errorf("taskId:%v unknown schedule resource type:%v not exist.", resource.TaskId, resourceType)
		return ErrResourceNotFound
	}
	idx := workerQuota.RestQuota[bs.Strategy.Config.Dominants[0]]
	value, _ := quotaTreeMap.Get(idx)
	if nil == value {
		// worker 有taint会导致这里选不到机器
		log.Errorf("taskId:%v unknown schedule tree map resource type:%v idx:%v, workerQuota:%+v", resource.TaskId, resourceType, idx, workerQuota)
		return ErrResourceNotFound
	}

	workerQuotaMap, ok := value.(map[string]*WorkerQuota)
	if !ok {
		log.Errorf("taskId:%v resource type:%v idx:%v cover from value to workerQuotaMap:%v failed..",
			resource.TaskId, resourceType, idx, workerQuotaMap)
		return ErrResourceNotFound
	}

	if _, ok := workerQuotaMap[workerQuota.Worker.WorkerId]; !ok {
		log.Errorf("taskId:%v resource type:%v idx:%v workerId:%v not exist...",
			resource.TaskId, resourceType, idx, workerQuota.Worker.WorkerId)
		return ErrResourceNotFound
	}

	// 根据真实资源消耗分配策略，不能马上归还逻辑资源，锁住等待同步worker心跳订正，或者如果有任务的平均真实资源消耗，可订正
	if resourceType == ScheduleResourceTypeLogic {
		bs.Remove(ctx, workerQuota)
		workerQuota.RestQuota.Add(resource.Quota)
		bs.Put(ctx, workerQuota)
	} else {
		if resource.AvgQuota != nil && len(resource.AvgQuota) > 0 {
			bs.Remove(ctx, workerQuota)
			// todo 这里应该是sub？
			workerQuota.RestQuota.Add(resource.AvgQuota)
			bs.Put(ctx, workerQuota)
		}
	}

	workerQuota.Worker.AllocQuota.Sub(resource.Quota)
	workerQuota.Worker.UpdateQuotaVersion += 1
	delete(workerQuota.Resources, resource.TaskId)
	workerQuota.Worker.TaskNum = int32(len(workerQuota.Resources))
	//if len(workerQuota.Label) > 0 {
	//	// 重建标签
	//}

	return nil
}

func (bs *BaseScheduler) Put(ctx context.Context, workerQuota *WorkerQuota) {
	resourceType := bs.Strategy.Config.ScheduleResourceType
	quotaTreeMap, ok := bs.WorkerQuotaMap[resourceType]
	if !ok {
		quotaTreeMap = treemap.NewWith(utils.Int64Comparator)
		bs.WorkerQuotaMap[resourceType] = quotaTreeMap
	}

	idx := workerQuota.RestQuota[bs.Strategy.Config.Dominants[0]]
	if val, ok := quotaTreeMap.Get(idx); ok {
		workerQuotaMap, _ := val.(map[string]*WorkerQuota)
		workerQuotaMap[workerQuota.Worker.WorkerId] = workerQuota
	} else {
		workerQuotaMap := make(map[string]*WorkerQuota)
		workerQuotaMap[workerQuota.Worker.WorkerId] = workerQuota
		quotaTreeMap.Put(idx, workerQuotaMap)
	}

	bs.WorkerNum += 1
}

func (bs *BaseScheduler) Remove(ctx context.Context, workerQuota *WorkerQuota) {
	resourceType := bs.Strategy.Config.ScheduleResourceType
	dominant := bs.Strategy.Config.Dominants[0]
	quotaTreeMap, ok := bs.WorkerQuotaMap[resourceType]
	if !ok {
		log.Errorf("schedule remove failed.%v", workerQuota)
		return
	}
	treeMapKey := workerQuota.RestQuota[dominant]
	treeMapValue, ok := quotaTreeMap.Get(treeMapKey)
	if !ok {
		log.Errorf("workerQuota not found, value:%v", workerQuota)
		return
	}

	workerQuotaMap, _ := treeMapValue.(map[string]*WorkerQuota)
	delete(workerQuotaMap, workerQuota.Worker.WorkerId)
	// 如果treemap无worker，直接删除这个key
	if len(workerQuotaMap) == 0 {
		quotaTreeMap.Remove(treeMapKey)
	}

	bs.WorkerNum -= 1
}
func (bs *BaseScheduler) Statistics(ctx context.Context, cws []*ChildWorker, strategies map[string]*Strategy) *WaterLevel {
	resourceType := bs.Strategy.Config.ScheduleResourceType
	quotaTreeMap, ok := bs.WorkerQuotaMap[resourceType]
	if !ok && len(cws) == 0 {
		log.Errorf("statistics failed. get quotaTreeMap failed. resourceType:%v", resourceType)
		return nil
	}
	// else if quotaTreeMap not exist, and cws is not empty, will add cws to waterLevel below

	waterLevel := &WaterLevel{
		TotalQuota:    map[string]int64{},
		AllocQuota:    map[string]int64{},
		RealQuota:     map[string]int64{},
		AvgQuota:      map[string]int64{},
		ScheduleQuota: map[string]int64{},
		Strategy:      bs.Strategy.Config,
		Type:          resourceType,
	}

	// 用于统计新的未消耗的worker, 实际在新worker3s定时同步后此字段已无用
	newWorkerSchedulerQuota := make(QuotaSet)

	// 只有当quotaTreeMap存在时才进行遍历统计
	if ok {
		quotaTreeMap.Each(func(key interface{}, value interface{}) {
			workerQuotaMap, ok := value.(map[string]*WorkerQuota)
			if !ok {
				log.Errorf("statistics failed. get workerQuotaMap failed. value:%+v", workerQuotaMap)
				return
			}

			for _, workerQuota := range workerQuotaMap {
				w := workerQuota.Worker
				waterLevel.TotalQuota.Add(w.Quota)
				waterLevel.RealQuota.Add(w.RealQuota)
				waterLevel.AllocQuota.Add(w.AllocQuota)
				waterLevel.ScheduleQuota.Add(w.ScheduleQuota)
				//waterLevel.AvgQuota.Add(w.AvgQuota)
				waterLevel.TaskNum += workerQuota.Worker.TaskNum

				// 新worker schedulerQuota为空
				if len(w.ScheduleQuota) == 0 {
					newWorkerSchedulerQuota.Add(w.Quota)
				}
			}
		})
	}

	// 新增：统计所有 Parent 匹配的 ChildWorker
	// 测试日志 输出当前waterlevel和childWorker信息
	// uuid := uuid.New()
	// log.Infof("test statistics ,uuid:%+v, strategy: %+v, waterLevel:%+v,childWorker:%+v", uuid, JsonToString(bs.Strategy), JsonToString(waterLevel), JsonToString(cws))
	for _, childWorker := range cws {
		// 添加 ChildWorker 的资源信息到水位中
		waterLevel.TotalQuota.Add(childWorker.Quota)
		waterLevel.RealQuota.Add(childWorker.RealQuota)
		waterLevel.AllocQuota.Add(childWorker.AllocQuota)
		waterLevel.TaskNum += childWorker.TaskNum
		//log.Infof("test statistics ,uuid:%+v, strategy: %+v, waterLevel:%+v,childWorker:%+v", uuid, JsonToString(bs.Strategy), JsonToString(waterLevel), JsonToString(cws))
	}

	waterLevel.Dominant = waterLevel.Strategy.Dominants[0]
	ratio := waterLevel.Strategy.LimitRatio[waterLevel.Dominant]
	if ratio <= 0 {
		ratio = waterLevel.Strategy.LimitRatio[DefaultScheduleDominantAll]
		if ratio <= 0 {
			bs.log.WithContext(ctx).Warnf("stat water level failed. ratio:%v is zero.", JsonToString(waterLevel))
			return nil
		}
	}

	totalQuota, ok := waterLevel.TotalQuota[waterLevel.Dominant]
	if !ok || totalQuota <= 0 {
		bs.log.WithContext(ctx).Warnf("stat water level failed. total quota:%v is zero.", JsonToString(waterLevel))
		return nil
	}

	var quota int64
	if resourceType == ScheduleResourceTypeReal {
		// 根据真实值计算  这里少计算了锁quota的资源，应该用总quota * limit - schedulerQuota
		log.Infof("stat water level by real quota. waterLevel.ScheduleQuota:%+v, newWorkerSchedulerQuota:%+v", JsonToString(waterLevel.ScheduleQuota), JsonToString(newWorkerSchedulerQuota))
		quota = (waterLevel.TotalQuota[waterLevel.Dominant]-newWorkerSchedulerQuota[waterLevel.Dominant])*ratio/100 - waterLevel.ScheduleQuota[waterLevel.Dominant]
	} else if resourceType == ScheduleResourceTypeLogic {
		// 根据逻辑值来计算
		quota = waterLevel.AllocQuota[waterLevel.Dominant]
	} else {
		//todo 确认这里的策略作用？
		quota, ratio = bs.statMixedWaterLevel(ctx, *waterLevel, cws, strategies, waterLevel.Dominant)
	}

	waterLevel.Used = quota
	waterLevel.Total = (totalQuota) * ratio / 100
	waterLevel.WaterLevel = float32(waterLevel.Used) / float32(waterLevel.Total)

	//todo 这里加入了混跑资源，对原调度器水位有影响，当前业务状态下未发现明显异常
	bs.WaterLevel = waterLevel
	return waterLevel
}

func (bs *BaseScheduler) statMixedWaterLevel(ctx context.Context, level WaterLevel, cws []*ChildWorker, strategies map[string]*Strategy, dominant string) (int64, int64) {
	product := bs.Strategy.Product
	engineModel := bs.Strategy.EngineModel
	tag := bs.Strategy.Tag
	var quota int64
	ratio := level.Strategy.LimitRatio[dominant]
	for _, childWorker := range cws {
		if childWorker.ParentProduct != product || childWorker.ParentEngineModel != engineModel || childWorker.ParentTag != tag {
			continue
		}
		strategy := getStrategy(childWorker.Product, childWorker.EngineModel, childWorker.Tag, childWorker.Label, strategies)
		if strategy.Config.ScheduleResourceType == ScheduleResourceTypeReal {
			quota += childWorker.RealQuota[dominant]
		} else {
			quota += childWorker.AllocQuota[dominant]
		}
		if ratio < strategy.Config.LimitRatio[dominant] {
			// 混跑取最低的比例
			ratio = strategy.Config.LimitRatio[dominant]
		}
	}
	return quota, ratio
}

func getStrategy(product, engineModel, tag string, label map[string]string, strategies map[string]*Strategy) *Strategy {
	strategyId := MakeStrategyId(&Strategy{
		Product:     product,
		EngineModel: engineModel,
		Tag:         tag,
		Label:       label,
	})

	strategy, ok := strategies[strategyId]
	if !ok {
		strategy = &DefaultMasterStrategy
	}
	return strategy
}

func (bs *BaseScheduler) getElasticMaxLen(ctx context.Context, quotaTreeMap *treemap.Map, resource *Resource) int {
	num := bs.getElasticScaleDownNum()
	//log.Infof("treemap:%+v \nscale down num:%v", quotaTreeMap, num)
	keys := quotaTreeMap.Keys()
	if num <= 0 {
		return len(keys)
	}

	for i := len(keys) - 1; i >= 0; i-- {
		if keys[i].(int64) < resource.Quota[bs.Strategy.Config.Dominants[0]] {
			return len(keys)
		}

		value, ok := quotaTreeMap.Get(keys[i])
		if !ok {
			bs.log.WithContext(ctx).Warnf("allocTreemapQuota quotaTreeMap.Get failed. resource:%v key:%v quotaTreeMap:%v", resource.TaskId, i, quotaTreeMap)
			return len(keys)
		}

		workerQuotaMap, ok := value.(map[string]*WorkerQuota)
		if !ok {
			return len(keys)
		}

		if len(workerQuotaMap) > num {
			return i + 1
		}

		num -= len(workerQuotaMap)
	}

	return len(keys)
}

func (bs *BaseScheduler) getElasticScaleDownNum() int {
	if bs.Strategy.Config.ElasticConfig == nil {
		return 0
	}

	config := bs.Strategy.Config.ElasticConfig

	if config.MinWorkerNum == 0 || bs.WorkerNum <= config.MinWorkerNum {
		log.Infof("min worker num is zero or worker num is less than min worker num(%v).", config.MinWorkerNum)
		return 0
	}

	if bs.WaterLevel == nil || config.Threshold <= int(bs.WaterLevel.WaterLevel*100) {
		if bs.WaterLevel == nil {
			log.Infof("water level is nil.")
		} else {
			log.Infof("water level(%v) is nil or water level is less than threshold(%v).", bs.WaterLevel, config.Threshold)
		}
		return 0
	}

	num := int(math.Ceil(float64(bs.WorkerNum*config.Ratio) / float64(100)))
	if bs.WorkerNum-num < config.MinWorkerNum {
		log.Infof("num(%v)", num)

		// 按比例缩容后，机器数比最小机器数小
		num = bs.WorkerNum - config.MinWorkerNum
		log.Infof("bs.WorkerNum(%v)-num(%v) < config.MinWorkerNum(%v)", bs.WorkerNum, num, config.MinWorkerNum)
	}
	log.Infof("num(%v)", num)

	return num
}

func (bs *BaseScheduler) Printf(ctx context.Context) {
	for s, t := range bs.WorkerQuotaMap {
		t.Each(func(key interface{}, value interface{}) {
			fmt.Printf("treemap %v: Key: %v, Value: %v\n", s, key, value)
		})
	}
}

func (bs *BaseScheduler) SetWaterLevel(waterLevel *WaterLevel) {
	bs.WaterLevel = waterLevel
}

func (bs *BaseScheduler) GetWaterLevel() *WaterLevel {
	return bs.WaterLevel
}
