package biz

import (
	"context"
	"time"
)

type KVStore struct {
	Repo        string    `json:"Repo,omitempty"`
	Key         string    `json:"Key,omitempty"`
	Value       string    `json:"Value,omitempty"`
	ExpiredTime time.Time `json:"ExpiredTime,omitempty"`
}

type KVStoreRepo interface {
	Set(context.Context, *KVStore) error
	Get(context.Context, *KVStore) (string, error)
	Delete(context.Context, *KVStore)
	ListAll(context.Context) ([]*KVStore, error)
	Cas(context.Context, *KVStore, *KVStore) (bool, error)
}
