package biz_test

import (
	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/mocks/mrepo"
	"mpp/internal/taskscheduler/testdata"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("WaterLevelManager", func() {
	var waterLevelManager *biz.WaterLevelManager
	var mWaterLevelRepo *mrepo.MockWaterLevelRepo

	BeforeEach(func() {
		mWaterLevelRepo = mrepo.NewMockWaterLevelRepo(ctl)
		waterLevelManager = biz.NewWaterLevelManager(mWaterLevelRepo, nil, log.DefaultLogger)
	})

	It("WaterLevelManagerCreate", func() {
		waterlevel := testdata.WaterLevel()
		mWaterLevelRepo.EXPECT().Save(ctx, gomock.Any()).Return(nil)
		err := waterLevelManager.CreateWaterLevel(ctx, waterlevel)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
	})

	It("WaterLevelManagerGet", func() {
		waterlevel := testdata.WaterLevel()
		mWaterLevelRepo.EXPECT().FindLatest(ctx, gomock.Any()).Return(waterlevel, nil)
		result, err := waterLevelManager.GetWaterLevel(ctx, waterlevel)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result).To(Equal(waterlevel))
	})

	It("WaterLevelManagerList", func() {
		waterlevel := testdata.WaterLevel()
		mWaterLevelRepo.EXPECT().ListAll(ctx, gomock.Any(), gomock.Any()).Return([]*biz.WaterLevel{waterlevel}, nil)
		result, err := waterLevelManager.ListWaterLevel(ctx, waterlevel, 0)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result[0]).To(Equal(waterlevel))
	})

	It("WaterLevelManagerDelete", func() {
		waterlevel := testdata.WaterLevel()
		err := waterLevelManager.DeleteWaterLevel(ctx, waterlevel)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
	})

	It("WaterLevelManagerUpdate", func() {
		waterlevel := testdata.WaterLevel()
		err := waterLevelManager.UpdateWaterLevel(ctx, waterlevel)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
	})

	It("StatMixedWaterLevel", func() {
		waterlevel := testdata.WaterLevel()
		waterLevelManager.StatMixedWaterLevel(ctx, *waterlevel, nil, nil, "")

	})
})
