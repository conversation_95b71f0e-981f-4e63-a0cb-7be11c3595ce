package biz

import (
	"context"
	"errors"
	"math/rand"
	"strings"
)

// StepCentralScheduler 集中策略
type StepCentralScheduler struct {
	BaseScheduler
}

// Alloc 集中策略(根据真实资源多阶集中分配：直播转码)
func (cs *StepCentralScheduler) Alloc(ctx context.Context, resource *Resource) (*WorkerQuota, error) {
	resourceType := cs.Strategy.Config.ScheduleResourceType
	dominant := cs.Strategy.Config.Dominants[0]
	quotaTreeMap, ok := cs.WorkerQuotaMap[resourceType]
	if !ok {
		cs.log.WithContext(ctx).Errorf("central scheduler, taskId:%v unknown schedule resource type:%v not exist.", resource.TaskId, resourceType)
		return nil, ErrWorkerNotFound
	}
	keys := quotaTreeMap.Keys()
	start := cs.search(keys, resource.Quota[dominant])
	if start == len(keys) {
		cs.log.WithContext(ctx).Errorf("central scheduler, taskId:%v not enough quota. type:%v not exist.", resource.TaskId, resourceType)
		return nil, ErrNotEnoughQuota
	}

	steps := cs.Strategy.Config.Steps

	// 当step没有配置时，需要设置默认step
	// 考虑集群水位限制，实际不限制死
	// 难点：只有剩余quota，机器总quota需要解析使用
	// 逻辑：先找剩余quota，满足的列表里按照多阶水位决断分配
	step := len(steps)
	if step == 0 {
		steps = []*StrategyStep{
			{
				Strategy:     "central",
				WorkerRatio:  100,
				ClusterRatio: 100,
			},
		}
		step = 1
	}

	// 确定step
	//// 计算集群水位 方式一1. 现场计算  把keys的值*对应机器数得到总的restQuota   性能较差:3ms一次   总压测938秒
	//var restQuotaSum int64
	//var totalQuotaSum int64
	//for _, restQuota := range keys {
	//	restQuotaInt := restQuota.(int64)
	//	value, _ := quotaTreeMap.Get(restQuota)
	//	if workerQuotaMap, ok := value.(map[string]*WorkerQuota); ok {
	//		restQuotaSum += restQuotaInt * int64(len(workerQuotaMap))
	//		for _, workerQuota := range workerQuotaMap {
	//			w := workerQuota.Worker
	//			totalQuotaSum += w.Quota[dominant]
	//		}
	//	}
	//}
	//clusterRatio := int((totalQuotaSum - restQuotaSum) / totalQuotaSum * 100)

	// 2. 使用历史水位   ---压测空指针，实际呢？
	//clusterRatio := int(cs.WaterLevel.WaterLevel * 100)
	//cs.log.WithContext(ctx).Infof("stepCentral scheduler, taskId:%v clusterRatio:%v", resource.TaskId, clusterRatio)

	// 3. 综合 现场计算的剩余quota和上一次水位统计的总quota（预期10s没总quota不会发生大的变化)  总压测16s
	var restQuotaSum int64
	var totalQuotaSum int64
	for _, restQuota := range keys {
		restQuotaInt := restQuota.(int64)
		value, _ := quotaTreeMap.Get(restQuota)
		if workerQuotaMap, ok := value.(map[string]*WorkerQuota); ok {
			restQuotaSum += restQuotaInt * int64(len(workerQuotaMap))
		}
	}

	//cs.log.WithContext(ctx).Infof("stepCentral scheduler, taskId:%v calculate restQuotaSum cost time:%v", resource.TaskId, time.Since(t).Milliseconds())

	if cs.WaterLevel != nil && cs.WaterLevel.Total == 0 {
		cs.log.WithContext(ctx).Warnf("stepCentral scheduler, taskId:%v totalQuotaSum is 0.", resource.TaskId)
		return nil, ErrNotEnoughQuota
	}
	if cs.WaterLevel == nil {
		cs.log.WithContext(ctx).Warnf("stepCentral scheduler without WaterLevel, taskId:%v.", resource.TaskId)
		// 退化为第一种方式统计集群总quota，性能较差
		//time1 := time.Now().UnixMicro()
		//it := quotaTreeMap.Iterator()
		//for it.Next() {
		//	if workerQuotaMap, ok := it.Value().(map[string]*WorkerQuota); ok {
		//		for _, workerQuota := range workerQuotaMap {
		//			totalQuotaSum += workerQuota.Worker.Quota[dominant]
		//		}
		//	}
		//}

		for _, restQuota := range keys {
			value, _ := quotaTreeMap.Get(restQuota)
			if workerQuotaMap, ok := value.(map[string]*WorkerQuota); ok {
				for _, workerQuota := range workerQuotaMap {
					totalQuotaSum += workerQuota.Worker.Quota[dominant]
				}
			}
		}
		//time2 := time.Now().UnixMicro()
		//cs.log.WithContext(ctx).Infof("stepCentral scheduler, taskId:%v totalQuotaSum cost time:%v", resource.TaskId, time2-time1)
	} else {
		totalQuotaSum = cs.WaterLevel.Total
	}

	if totalQuotaSum == 0 {
		cs.log.WithContext(ctx).Errorf("stepCentral scheduler, taskId:%v totalQuotaSum is zero, cannot compute clusterRatio.", resource.TaskId)
		return nil, ErrNotEnoughQuota
	}

	//totalQuotaSum = 32000 * 20000
	clusterRatio := int((totalQuotaSum - restQuotaSum) / totalQuotaSum * 100)

	//cs.log.WithContext(ctx).Infof("stepCentral scheduler, taskId:%v calculate clusterRatio cost time:%v", resource.TaskId, time.Since(t).Milliseconds())

	startStep := -1
	for i := 0; i < step; i++ {
		if clusterRatio <= steps[i].ClusterRatio {
			startStep = i
			break
		}
	}

	// todo 用水位算好的totalQuota有10秒误差，需要这里在没有满足step的时候选择最后一阶step
	if startStep == -1 {
		startStep = step - 1
		//cs.log.WithContext(ctx).Errorf("stepCentral scheduler, taskId:%v not enough quota. type:%v not exist.", resource.TaskId, resourceType)
		//return nil, ErrNotEnoughQuota
	}

	// 单机保护阈值限制
	limitRatio := int64(100)
	if cs.Strategy.Config.LimitRatio != nil && cs.Strategy.Config.LimitRatio[dominant] != 0 {
		limitRatio = cs.Strategy.Config.LimitRatio[dominant]
	}

	// 如有随机域名,从随机位置开始
	startNew := cs.getRandDomainIndex(resource, start, len(keys))
	start = startNew

	limitRatioFloat := float64(100-limitRatio) / 100

	var backupWorkerQuota *WorkerQuota
	for i := startStep; i < step; i++ {
		stepParams := steps[i]
		workerRatioFloat := float64(stepParams.WorkerRatio) / 100
		for j := start; j < len(keys); j++ {
			treeMapKey := keys[j]
			value, _ := quotaTreeMap.Get(treeMapKey)
			var workerQuota *WorkerQuota
			if workerQuotaMap, ok := value.(map[string]*WorkerQuota); ok {
				for _, workerQuota = range workerQuotaMap {
					// 单机水位超了阶段水位,就看下一台   这里注意，剩余资源本身是扣减了单机限制水位的 eg 32 * 0.75 = 24
					quota := float64(workerQuota.Worker.Quota[dominant])
					rest := float64(treeMapKey.(int64)) + quota*limitRatioFloat
					if (quota-rest)/quota >= workerRatioFloat {
						continue
					}
					if wq, err := cs.allocWorkerQuota(ctx, resource, workerQuota); err == nil {

						//cs.log.WithContext(ctx).Infof("stepCentral scheduler, taskId:%v alloc resource cost time:%v", resource.TaskId, time.Since(t).Milliseconds())
						return wq, nil
					} else if errors.Is(err, ErrNotEnoughQuota) {
						break
					} else if errors.Is(err, ErrMatchLabelPreferredKey) && backupWorkerQuota == nil {
						backupWorkerQuota = workerQuota
					}
				}
			}
		}
	}

	if backupWorkerQuota != nil {
		// backup
		cs.log.WithContext(ctx).Errorf("stepCentral scheduler, taskId:%v no affinity worker, use backup. %v",
			resource.TaskId, JsonToString(cs.WorkerQuotaMap))

		return cs.allocWorkerQuotaWithAffinity(ctx, resource, backupWorkerQuota, true)
	}

	cs.log.WithContext(ctx).Errorf("stepCentral scheduler, tasker:%v alloc resource failed. type:%v no avaliable worker exist.", JsonToString(resource),
		resourceType)
	return nil, ErrWorkerNotFound
}

func (cs *StepCentralScheduler) getRandDomainIndex(resource *Resource, start, totalLength int) int {
	if start >= totalLength {
		return start
	}
	// 产品级别的随机  录制时移等避免单机一次超多任务
	if cs.Strategy.Extend != nil && cs.Strategy.Extend["RandomScheduler"] != nil {
		if cs.Strategy.Extend["RandomScheduler"].(bool) {
			if cs.Strategy.Extend["RandomPercent"] != nil {
				// 限定百分比比例
				randomPercent := cs.Strategy.Extend["RandomPercent"].(int)
				indexRange := (totalLength - start) * randomPercent / 100
				if indexRange <= 0 {
					return start
				}
				start = start + rand.Intn(indexRange)
				return start
			} else {
				start = start + rand.Intn(totalLength-start)
				return start
			}
		}
	}
	if cs.Strategy.Extend != nil && cs.Strategy.Extend["RandomDomain"] != nil {
		if resource.Extend["domain"] != nil && resource.Extend["domain"].(string) != "" {
			randomDomains := cs.Strategy.Extend["RandomDomain"].(string)
			randomDomainList := strings.Split(randomDomains, ",")

			domain := resource.Extend["domain"].(string)
			for _, randomDomain := range randomDomainList {
				if domain == randomDomain {
					// todo 删除
					cs.log.Warnf("stepCentral scheduler, taskId:%v random domain:%v.", resource.TaskId, domain)
					if cs.Strategy.Extend["RandomPercent"] != nil {
						// 限定百分比比例
						randomPercent := cs.Strategy.Extend["RandomPercent"].(int)
						indexRange := (totalLength - start) * randomPercent / 100
						if indexRange <= 0 {
							return start
						}
						start = start + rand.Intn(indexRange)
						return start
					} else {
						start = start + rand.Intn(totalLength-start)
						return start
					}
				}
			}
		}
	}
	return start
}
