package biz

import (
	"context"
	"errors"

	"github.com/go-kratos/kratos/v2/log"
)

// AverageScheduler 平均策略
type AverageScheduler struct {
	BaseScheduler
}

// Alloc 平均策略(根据逻辑资源平均分配：RMS、录制、时移、导播台ASR、截图)
// 全局平均，从剩余最大的Quota开始选
func (as *AverageScheduler) Alloc(ctx context.Context, resource *Resource) (*WorkerQuota, error) {
	resourceType := as.Strategy.Config.ScheduleResourceType
	dominant := as.Strategy.Config.Dominants[0]
	quotaTreeMap, ok := as.WorkerQuotaMap[resourceType]
	if !ok {
		as.log.WithContext(ctx).Errorf("average scheduler, taskId:%v unknown schedule resource type:%v not exist. %v", resource.TaskId, resourceType, JsonToString(as.WorkerQuotaMap))
		return nil, ErrWorkerNotFound
	}

	keys := quotaTreeMap.Keys()

	// 跳过弹性保留机器，如果是大Quota任务，优先分配，不保留
	length := as.getElasticMaxLen(ctx, quotaTreeMap, resource)
	log.Infof("total:%v scale max len:%v", len(keys), length)

	var backupWorkerQuota *WorkerQuota

	for i := length - 1; i >= 0; i-- {
		if keys[i].(int64) < resource.Quota[dominant] {
			break
		}

		treeMapKey := keys[i]
		value, _ := quotaTreeMap.Get(treeMapKey)
		var workerQuota *WorkerQuota
		if workerQuotaMap, ok := value.(map[string]*WorkerQuota); ok {
			for _, workerQuota = range workerQuotaMap {
				if wq, err := as.allocWorkerQuota(ctx, resource, workerQuota); err == nil {
					return wq, nil
				} else if errors.Is(err, ErrNotEnoughQuota) {
					break
				} else if errors.Is(err, ErrMatchLabelPreferredKey) && backupWorkerQuota == nil {
					backupWorkerQuota = workerQuota
				}
			}
		}
	}

	if backupWorkerQuota != nil {
		// backup
		as.log.WithContext(ctx).Errorf("average scheduler, taskId:%v no affinity worker, use backup. %v", resource.TaskId, JsonToString(as.WorkerQuotaMap))

		return as.allocWorkerQuotaWithAffinity(ctx, resource, backupWorkerQuota, true)
	}

	//log.Errorf("average scheduler, tasker:%v alloc resource failed. worker:%v.", JsonToString(resource),
	//	JsonToString(as.WorkerQuotaMap))
	return nil, ErrNotEnoughQuota
}
