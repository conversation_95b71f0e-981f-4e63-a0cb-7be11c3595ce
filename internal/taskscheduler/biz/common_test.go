package biz_test

import (
	"mpp/internal/taskscheduler/biz"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// 测试函数
func TestCommon(t *testing.T) {
	qs := biz.QuotaSet{"cpu": 100, "mem": 1024, "disk": 2048}
	qs2 := biz.QuotaSet{}
	qs2.Copy(qs)
	log.Info(qs2.Value())
	assert.Equal(t, qs, qs2)

	qs2.Scan([]byte("cpu=100,mem=1024,disk=2048"))
	log.Info(qs2.Value())
	assert.Equal(t, qs, qs2)

	m := biz.StringToMap(`{"cpu":100,"mem":1024,"disk":2048,"gpu":"0"}`)
	assert.NotEqual(t, m, nil)
	assert.Equal(t, m["cpu"], float64(100))
	assert.Equal(t, m["mem"], float64(1024))
	assert.Equal(t, m["disk"], float64(2048))

	affinity := &biz.Affinity{
		Required: nil,
		Preferred: []metav1.LabelSelectorRequirement{
			{
				Key:      "workerIp",
				Operator: metav1.LabelSelectorOpNotIn,
				Values:   []string{"127.0.0.1"},
			},
		},
	}

	a := biz.StringToResource(biz.JsonToString(affinity))
	assert.Equal(t, a, affinity)

	config := &biz.StrategyConfig{
		ScheduleResourceType: "real",
		Steps:                nil,
		LimitRatio:           biz.QuotaSet{"cpu": 90, "mem": 90, "disk": 90},
		MaxTaskNum:           100,
		LoadRatio:            90,
		Dominants:            []string{"cpu"},
		ElasticConfig:        nil,
	}

	c := biz.StringToStrategyConfig(biz.JsonToString(config))
	assert.Equal(t, c, config)
	t.Logf("config: %v", m)
	v := biz.ParseMapStrValue(m, "gpu")
	assert.Equal(t, v, "0")
	input := map[string]interface{}{"cpu": 100}
	v1 := biz.ParseMapIntValue(input, "cpu")
	assert.Equal(t, v1, 100)
}
