package biz_test

import (
	"context"
	"math/rand"
	"time"

	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/testdata"

	"github.com/go-kratos/kratos/v2/log"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("ScheduleManagerPerformance", func() {
	var scheduleManager *biz.ScheduleManager
	//var mStrategyRepo *mrepo.MockStrategyRepo

	BeforeEach(func() {
		//mStrategyRepo = mrepo.NewMockStrategyRepo(ctl)
		scheduleManager = biz.NewScheduleManager(nil, log.GetLogger())
	})

	It("SyncWorker", func() {
		//test("product", "engine_moieee", "tag")
		workers := testdata.ScheduleWorkerList(2)
		resources := testdata.ScheduleResourceList(4)
		strategies := []*biz.Strategy{testdata.ScheduleStrategy(biz.StrategyAverage)}
		t1 := time.Now().UnixMicro()
		scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
		start := time.Now().UnixMicro()
		log.Infof("average schedule: worker:%v resource:%v, init time:%v", len(workers), len(resources), start-t1)
		log.Infof("=========================================================")

		//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
		for _, resource := range resources {
			_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
			Ω(err).ShouldNot(HaveOccurred())
			//log.Infof("worker:%v", biz.JsonToString(workerQuota))
		}

		end := time.Now().UnixMicro()
		log.Infof("average schedule: worker:%v resource:%v, alloc time:%v", len(workers), len(resources), end-start)
		log.Infof("=========================================================")

		scheduleManager.SyncWorkers(ctx, workers, strategies)
		sync := time.Now().UnixMicro()

		log.Infof("average schedule: worker:%v resource:%v, sync time:%v", len(workers), len(resources), sync-end)
		log.Infof("=========================================================")

		for _, resource := range resources {
			_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
			Ω(err).ShouldNot(HaveOccurred())
		}

		free := time.Now().UnixMicro()
		log.Infof("average schedule: worker:%v resource:%v, free time:%v", len(workers), len(resources), free-sync)

		workers[0].OnlineVersion = 2
		scheduleManager.SyncWorkers(ctx, workers, strategies)

		time.Sleep(3 * time.Second)

		newWorkers := testdata.ScheduleWorkerList(1)
		log.Infof("222222:new workers:%v", biz.JsonToString(newWorkers))
		scheduleManager.SyncWorkers(ctx, newWorkers, strategies)

		time.Sleep(3 * time.Second)
		newWorkers = testdata.ScheduleWorkerList(1)
		log.Infof("3333333:new workers:%v", biz.JsonToString(newWorkers))
		scheduleManager.SyncWorkers(ctx, newWorkers, strategies)

		tagGroups := scheduleManager.GetTagGroup(ctx, biz.SchedulerMaster)
		Ω(tagGroups).ShouldNot(BeNil())

		worker := scheduleManager.GetSlaveStrategy(ctx, workers[0].WorkerId)
		Ω(worker).ShouldNot(BeNil())

		scheduleManager.GetStrategies(ctx)
	})

	It("ScheduleStepRandomElastic", func() {
		workers := testdata.ScheduleWorkerList(10)
		resources := testdata.ScheduleResourceList(160)
		strategies := []*biz.Strategy{testdata.ScheduleStrategy(biz.StrategyStepRandom)}
		strategies[0].Config.ElasticConfig = &biz.ElasticConfig{
			MinWorkerNum: 5,
			Threshold:    30,
			Ratio:        20,
		}
		scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
		start := time.Now().UnixMicro()

		//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
		for i, resource := range resources {
			_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
			scheduleManager.ScheduleUnits[biz.SchedulerMaster].Statistics(ctx, nil, time.Now())
			Ω(err).ShouldNot(HaveOccurred())

			// 小于30%水位时，有一台是空的
			if i == 16*3 {
				num := getEmptyWorkerNum(workers)
				Ω(num == 1 || num == 2).Should(BeTrue())
			}

			//log.Infof("worker:%v", biz.JsonToString(workerQuota))
		}

		end := time.Now().UnixMicro()
		log.Infof("random schedule: worker:%v resource:%v, cost time:%v", len(workers), len(resources), end-start)

		for _, resource := range resources {
			_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
			Ω(err).ShouldNot(HaveOccurred())
		}

		free := time.Now().UnixMicro()
		log.Infof("random schedule: worker:%v resource:%v, free cost time:%v", len(workers), len(resources), free-end)
	})

	It("ScheduleStepAverageElastic", func() {
		workers := testdata.ScheduleWorkerList(10)
		resources := testdata.ScheduleResourceList(160)
		strategies := []*biz.Strategy{testdata.ScheduleStrategy(biz.StrategyStepAverage)}
		strategies[0].Config.ElasticConfig = &biz.ElasticConfig{
			MinWorkerNum: 5,
			Threshold:    30,
			Ratio:        20,
		}
		scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
		start := time.Now().UnixMicro()

		//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
		for i, resource := range resources {
			_, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
			scheduleManager.ScheduleUnits[biz.SchedulerMaster].Statistics(ctx, nil, time.Now())
			Ω(err).ShouldNot(HaveOccurred())

			// 小于30%水位时，有两台是空的
			if i == 16*3-1 {
				num := getEmptyWorkerNum(workers)
				Ω(num).Should(Equal(2))
			} else if i == 16*3 {
				num := getEmptyWorkerNum(workers)
				//log.Infof("worker:%v", biz.JsonToString(workerQuota))
				Ω(num).Should(Equal(1))
			} else if i == 16*3+1 {
				num := getEmptyWorkerNum(workers)
				log.Infof("worker:%v", biz.JsonToString(workers))
				Ω(num).Should(Equal(0))
			}

			//log.Infof("worker:%v", biz.JsonToString(workerQuota))
		}

		end := time.Now().UnixMicro()
		log.Infof("random schedule: worker:%v resource:%v, cost time:%v", len(workers), len(resources), end-start)

		for _, resource := range resources {
			_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
			Ω(err).ShouldNot(HaveOccurred())
		}

		free := time.Now().UnixMicro()
		log.Infof("random schedule: worker:%v resource:%v, free cost time:%v", len(workers), len(resources), free-end)
		scheduleManager.ScheduleUnits[biz.SchedulerMaster].LogScheduleInfo()
	})

	It("ScheduleAverageElastic", func() {
		workers := testdata.ScheduleWorkerList(10)
		resources := testdata.ScheduleResourceList(80)
		strategy := testdata.ScheduleStrategy(biz.StrategyAverage)
		strategy.Config.ScheduleResourceType = biz.ScheduleResourceTypeReal
		strategies := []*biz.Strategy{testdata.ScheduleStrategy(biz.StrategyAverage)}
		strategies[0].Config.ElasticConfig = &biz.ElasticConfig{
			MinWorkerNum: 6,
			Threshold:    50,
			Ratio:        20,
		}
		scheduleManager.Init(context.Background(), workers, []*biz.Resource{}, strategies)
		start := time.Now().UnixMicro()

		childWorks := []*biz.ChildWorker{
			{
				WorkerId:          "task1",
				ParentProduct:     "worker-product",
				ParentEngineModel: "worker-engine",
				ParentTag:         "worker-tag",
				ParentWorkerId:    "worker1",
				Product:           workers[0].Product,
				EngineModel:       workers[0].EngineModel,
				Tag:               workers[0].Tag,
				Quota:             workers[0].Quota,
				AllocQuota:        workers[0].AllocQuota,
				Label:             map[string]string{"key1": "testLabel"},
				Taint:             workers[0].Taint,
				Port:              workers[0].Port,
				HeartbeatTime:     time.Now(),
			},
		}
		//log.Infof("tag:%v", biz.JsonToString(scheduleManager.GetTagGroup(context.Background())))
		for _, resource := range resources {
			w, err := scheduleManager.Alloc(context.Background(), biz.SchedulerMaster, resource)
			scheduleManager.ScheduleUnits[biz.SchedulerMaster].Statistics(ctx, childWorks, time.Now())
			Ω(err).ShouldNot(HaveOccurred())
			for _, worker := range workers {
				if worker.WorkerId == w.WorkerId {
					// 250*80<2000，保证水位不会超过50%
					worker.RealQuota["cpu"] = w.AllocQuota["cpu"] + int64(rand.Intn(250))
					worker.AllocQuota = w.AllocQuota
					break
				}
			}
			scheduleManager.SyncWorkers(context.Background(), workers, strategies)
		}

		num := getEmptyWorkerNum(workers)
		Ω(num).Should(Equal(0))

		end := time.Now().UnixMicro()
		log.Infof("average elastic scale down. schedule: worker:%v resource:%v, cost time:%v", len(workers), len(resources), end-start)

		for _, resource := range resources {
			_, err := scheduleManager.Free(context.Background(), biz.SchedulerMaster, resource.TaskId)
			Ω(err).ShouldNot(HaveOccurred())
		}

		free := time.Now().UnixMicro()
		log.Infof("average elastic scale down. schedule: worker:%v resource:%v, free cost time:%v", len(workers), len(resources), free-end)
	})

})

func getEmptyWorkerNum(workers []*biz.Worker) int {
	num := 0
	for _, worker := range workers {
		if worker.TaskNum == 0 {
			log.Infof("empty worker:%+v", worker)
			num++
		}
	}

	return num
}
