package biz

import (
	"context"
	"errors"
	"math"
	"math/rand"
)

// StepRandomScheduler 多阶随机策略
type StepRandomScheduler struct {
	BaseScheduler
}

func (ss *StepRandomScheduler) Alloc(ctx context.Context, resource *Resource) (*WorkerQuota, error) {
	resourceType := ss.Strategy.Config.ScheduleResourceType
	dominant := ss.Strategy.Config.Dominants[0]

	// 最小堆树
	quotaTreeMap, ok := ss.WorkerQuotaMap[resourceType]
	if !ok {
		ss.log.WithContext(ctx).Errorf("step random scheduler, taskId:%v unknown schedule resource type:%v not exist. %v",
			resource.TaskId, resourceType, JsonToString(ss.WorkerQuotaMap))
		return nil, ErrResourceNotFound
	}

	// worker的剩余quota列表做key,keys是由小到大的序列
	keys := quotaTreeMap.Keys()

	steps := ss.Strategy.Config.Steps

	step := len(steps)
	if step == 0 {
		step = 2
	}

	/* 方法1：step 随机性好，性能好*/
	// 随机分配资源
	stepLen := int(math.Ceil(float64(ss.WorkerNum) / float64(step)))
	randomIndex := rand.Int() % (stepLen)

	idx := 0
	random := -1
	length := 1

	// 跳过弹性保留机器, 找到最大位置
	keyLength := ss.getElasticMaxLen(ctx, quotaTreeMap, resource)

	workerNum := 0
	var workerQuotaMap map[string]*WorkerQuota
	// 查找step位置
	for start := keyLength - 1; start >= 0; start-- {
		treeMapKey := keys[start]
		if keys[start].(int64) < resource.Quota[dominant] {
			// 资源足够的worker不够step数
			idx = start + 1
			break
		}

		value, _ := quotaTreeMap.Get(treeMapKey)
		if workerQuotaMap, ok = value.(map[string]*WorkerQuota); ok {
			workerNum += len(workerQuotaMap)
			// 找到随机位置
			if randomIndex < workerNum && random == -1 {
				// 找到随机位置就可以提前退出循环了,起始位置定在随机位置即可
				random = start
				idx = start
				//ss.log.Infof("111taskId:%v random worker:%v. stepLength:%v, rindex:%v, workerNum:%v", resource.TaskId, random, stepLen, randomIndex, workerNum)
				break
			}

			// 找到起始位置
			if workerNum >= stepLen {
				idx = start
				break
			}
		}
	}

	length = keyLength - idx
	if random == -1 {
		if length == 0 {
			ss.log.WithContext(ctx).Errorf("step random scheduler, taskId:%v no enough worker quota error",
				resource.TaskId)
			return nil, ErrNotEnoughQuota
		} else {
			random = idx + rand.Int()%length
		}
	}
	var backupWorkerQuota *WorkerQuota
	for i := 0; i < length; i++ {
		start := random + i
		if start > keyLength-1 {
			start = idx + start%keyLength
		}

		treeMapKey := keys[start]
		value, _ := quotaTreeMap.Get(treeMapKey)
		var workerQuota *WorkerQuota
		if workerQuotaMap, ok := value.(map[string]*WorkerQuota); ok {
			for _, workerQuota = range workerQuotaMap {
				if wq, err := ss.allocWorkerQuota(ctx, resource, workerQuota); err == nil {
					return wq, nil
				} else if errors.Is(err, ErrNotEnoughQuota) {
					break
				} else if errors.Is(err, ErrMatchLabelPreferredKey) && backupWorkerQuota == nil {
					backupWorkerQuota = workerQuota
				}
			}
		}
	}

	if backupWorkerQuota != nil {
		// backup
		ss.log.WithContext(ctx).Errorf("step random scheduler, taskId:%v no affinity worker, use backup. %v",
			resource.TaskId, JsonToString(ss.WorkerQuotaMap))

		return ss.allocWorkerQuotaWithAffinity(ctx, resource, backupWorkerQuota, true)
	}

	/* 方法2：step 随机性好，性能有点低*/
	//var workerIndexes []int64
	//start := len(keys) - 1
	//// 查找step位置
	//for ; start >= 0; start-- {
	//	treeMapKey := keys[start]
	//	if keys[start].(int64) < resource.Quota[dominant] {
	//		break
	//	}
	//
	//	value, _ := quotaTreeMap.Get(treeMapKey)
	//	if workerQuotaMap, ok := value.(map[string]*WorkerQuota); ok {
	//		for j := 0; j < len(workerQuotaMap); j++ {
	//			workerIndexes = append(workerIndexes, treeMapKey.(int64))
	//		}
	//		if len(workerIndexes) >= ss.WorkerNum/step {
	//			break
	//		}
	//	}
	//}
	//
	//// 随机分配资源
	//indexLen := len(workerIndexes)
	//random := rand.Int() % (indexLen)
	//fmt.Printf("%v:%v\n", random, indexLen)
	//for i, key := range keys {
	//	value, _ := quotaTreeMap.Get(key)
	//	workerQuotaMap, _ := value.(map[string]*WorkerQuota)
	//	fmt.Printf("%v %v:%v\n", i, key, len(workerQuotaMap))
	//}
	//
	//for i := 0; i < indexLen; i++ {
	//	idx := i + random
	//	if idx > indexLen-1 {
	//		idx = idx - indexLen
	//	}
	//	treeMapKey := workerIndexes[idx]
	//	value, _ := quotaTreeMap.Get(treeMapKey)
	//	var workerQuota *WorkerQuota
	//	if workerQuotaMap, ok := value.(map[string]*WorkerQuota); ok {
	//		for _, workerQuota = range workerQuotaMap {
	//			if wq, err := ss.allocWorkerQuota(ctx, resource, workerQuota); err == nil {
	//				return wq, nil
	//			} else if err == ErrNotEnoughQuota {
	//				return nil, ErrNotEnoughQuota
	//			}
	//		}
	//	}
	//}

	/* 方法三：step 性能高，随机性不好*/
	//workerNum := 0
	//start := len(keys) - 1
	//// 查找step位置
	//for ; start >= 0; start-- {
	//	treeMapKey := keys[start]
	//	if keys[start].(int64) < resource.Quota[dominant] {
	//		start++
	//		break
	//	}
	//
	//	value, _ := quotaTreeMap.Get(treeMapKey)
	//	if workerQuotaMap, ok := value.(map[string]*WorkerQuota); ok {
	//		workerNum += len(workerQuotaMap)
	//		if workerNum >= ss.WorkerNum/step {
	//			break
	//		}
	//	}
	//}
	//if start == -1 {
	//	start = 0
	//}
	//
	//// 随机分配资源
	//random := rand.Int() % (len(keys) - start)
	//fmt.Printf("%v:%v\n", start+random, len(keys))
	//for i, key := range keys {
	//	value, _ := quotaTreeMap.Get(key)
	//	workerQuotaMap, _ := value.(map[string]*WorkerQuota)
	//	fmt.Printf("%v %v:%v\n", i, key, len(workerQuotaMap))
	//}
	//for i := 0; i < len(keys)-start; i++ {
	//	idx := start + random + i
	//	if idx > len(keys)-1 {
	//		idx = start + idx - len(keys)
	//	}
	//	treeMapKey := keys[idx]
	//	value, _ := quotaTreeMap.Get(treeMapKey)
	//	var workerQuota *WorkerQuota
	//	if workerQuotaMap, ok := value.(map[string]*WorkerQuota); ok {
	//		for _, workerQuota = range workerQuotaMap {
	//			if wq, err := ss.allocWorkerQuota(ctx, resource, workerQuota); err == nil {
	//				return wq, nil
	//			} else if err == ErrNotEnoughQuota {
	//				return nil, ErrNotEnoughQuota
	//			}
	//		}
	//	}
	//}

	ss.log.Errorf("step random scheduler fail, no worker. task:%v alloc resource failed. worker:%v.", JsonToString(resource),
		JsonToString(ss.WorkerQuotaMap))
	return nil, ErrNotEnoughQuota
}
