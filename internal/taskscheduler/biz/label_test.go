package biz

import (
	"reflect"
	"testing"
)

/**
 * @Author: qinxin
 * @Date: 2025/7/30 21:07
 * @Desc:
 * @Version 1.0
 */

func TestLabelManager_AddResourceType(t *testing.T) {
	// 创建 LabelManager 实例
	lm := NewLabelManager(MPSProducts)

	// 测试用例涵盖了各种可能的情况:
	// 1. nil label 和 nil quota - 测试边界情况，应该创建新的map并设置默认的CPU类型
	// 2. label中已存在resourceType字段 - 验证函数不会重复添加标签，应该直接返回false
	// 3. label中无resourceType且quota为nil - 使用默认的CPU类型
	// 4. label中无resourceType且有GPU配额 - 应该添加GPU类型标签
	// 5. label中无resourceType且只有CPU配额 - 应该添加CPU类型标签
	// 6. 空label且有GPU配额 - 应该添加GPU类型标签
	// 7. 空label且有CPU配额 - 应该添加CPU类型标签
	// 8. nil label且有GPU配额 - 应该创建map并添加GPU类型标签
	// 9. 非mps和mediaai产品 - 应该不添加resourceType标签
	// 10. mps和mediaai产品 - 应该添加resourceType标签
	tests := []struct {
		name           string
		product        string
		label          map[string]string
		quota          QuotaSet
		expectedLabel  map[string]string
		expectedResult bool
	}{
		// 测试场景1: mps产品，nil label 和 nil quota
		// 预期结果: 创建新的map，添加默认的CPU类型标签，返回true
		{
			name:    "mps product, nil label and nil quota",
			product: "mps",
			label:   nil,
			quota:   nil,
			expectedLabel: map[string]string{
				LabelResourceType: ResourceTypeCPU,
			},
			expectedResult: true,
		},
		// 测试场景2: mediaai产品，label中已存在resourceType字段
		// 预期结果: 不修改label，直接返回false
		{
			name:    "mediaai product, label with existing resourceType",
			product: "mediaai",
			label:   map[string]string{LabelResourceType: ResourceTypeGPUT4},
			quota:   QuotaSet{GpuQuotaKey: 100},
			expectedLabel: map[string]string{
				LabelResourceType: ResourceTypeGPUT4,
			},
			expectedResult: false,
		},
		// 测试场景3: mps产品，label中无resourceType且quota为nil
		// 预期结果: 添加默认的CPU类型标签，返回true
		{
			name:    "mps product, label without resourceType and nil quota",
			product: "mps",
			label:   map[string]string{"env": "prod"},
			quota:   nil,
			expectedLabel: map[string]string{
				"env":             "prod",
				LabelResourceType: ResourceTypeCPU,
			},
			expectedResult: true,
		},
		// 测试场景4: mediaai产品，label中无resourceType且有GPU配额
		// 预期结果: 添加GPU类型标签，返回true
		{
			name:    "mediaai product, label without resourceType and GPU quota",
			product: "mediaai",
			label:   map[string]string{"env": "prod"},
			quota:   QuotaSet{GpuQuotaKey: 100},
			expectedLabel: map[string]string{
				"env":             "prod",
				LabelResourceType: ResourceTypeGPUT4,
			},
			expectedResult: true,
		},
		// 测试场景5: mps产品，label中无resourceType且只有CPU配额
		// 预期结果: 添加CPU类型标签，返回true
		{
			name:    "mps product, label without resourceType and CPU only quota",
			product: "mps",
			label:   map[string]string{"env": "prod"},
			quota:   QuotaSet{CpuQuotaKey: 100},
			expectedLabel: map[string]string{
				"env":             "prod",
				LabelResourceType: ResourceTypeCPU,
			},
			expectedResult: true,
		},
		// 测试场景6: mediaai产品，空label且有GPU配额
		// 预期结果: 添加GPU类型标签，返回true
		{
			name:    "mediaai product, empty label and GPU quota",
			product: "mediaai",
			label:   map[string]string{},
			quota:   QuotaSet{GpuQuotaKey: 100},
			expectedLabel: map[string]string{
				LabelResourceType: ResourceTypeGPUT4,
			},
			expectedResult: true,
		},
		// 测试场景7: mps产品，空label且有CPU配额
		// 预期结果: 添加CPU类型标签，返回true
		{
			name:    "mps product, empty label and CPU quota",
			product: "mps",
			label:   map[string]string{},
			quota:   QuotaSet{CpuQuotaKey: 100},
			expectedLabel: map[string]string{
				LabelResourceType: ResourceTypeCPU,
			},
			expectedResult: true,
		},
		// 测试场景8: mediaai产品，nil label且有GPU配额
		// 预期结果: 创建新的map，添加GPU类型标签，返回true
		{
			name:    "mediaai product, nil label and GPU quota",
			product: "mediaai",
			label:   nil,
			quota:   QuotaSet{GpuQuotaKey: 100},
			expectedLabel: map[string]string{
				LabelResourceType: ResourceTypeGPUT4,
			},
			expectedResult: true,
		},
		// 测试场景9: 非mps和mediaai产品(speech)
		// 预期结果: 不添加resourceType标签，返回false
		{
			name:    "speech product, should not add resourceType",
			product: "speech",
			label:   map[string]string{"env": "prod"},
			quota:   QuotaSet{GpuQuotaKey: 100},
			expectedLabel: map[string]string{
				"env": "prod",
			},
			expectedResult: false,
		},
		// 测试场景10: 非mps和mediaai产品(other)
		// 预期结果: 不添加resourceType标签，返回false
		{
			name:           "other product, should not add resourceType",
			product:        "other",
			label:          nil,
			quota:          QuotaSet{GpuQuotaKey: 100},
			expectedLabel:  nil,
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := lm.AddResourceType(tt.product, &tt.label, tt.quota)

			// 检查返回值是否符合预期
			if result != tt.expectedResult {
				t.Errorf("LabelManager.AddResourceType() result = %v, want %v", result, tt.expectedResult)
			}

			// 检查label是否正确更新
			if !reflect.DeepEqual(tt.label, tt.expectedLabel) {
				t.Errorf("LabelManager.AddResourceType() label = %v, want %v", tt.label, tt.expectedLabel)
			}
		})
	}
}

// 基准测试 - 测试函数性能
func BenchmarkLabelManager_AddResourceType(b *testing.B) {
	lm := NewLabelManager(MPSProducts)
	label := &map[string]string{"env": "prod"}
	quota := QuotaSet{GpuQuotaKey: 100}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		lm.AddResourceType("mps", label, quota)
	}
}
