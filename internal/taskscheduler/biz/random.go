package biz

import (
	"context"
	"errors"
	"math/rand"

	"github.com/go-kratos/kratos/v2/log"
)

// RandomScheduler 平均策略
type RandomScheduler struct {
	BaseScheduler
}

// Alloc 随机策略，从大于等于resource的Quota随机选
func (rs *RandomScheduler) Alloc(ctx context.Context, resource *Resource) (*WorkerQuota, error) {
	resourceType := rs.Strategy.Config.ScheduleResourceType
	dominant := rs.Strategy.Config.Dominants[0]
	quotaTreeMap, ok := rs.WorkerQuotaMap[resourceType]
	if !ok {
		rs.log.WithContext(ctx).Errorf("random scheduler, taskId:%v unknown schedule resource type:%v not exist.", resource.TaskId, resourceType)
		return nil, ErrWorkerNotFound
	}

	keys := quotaTreeMap.Keys()

	start := rs.search(keys, resource.Quota[dominant])
	if start == len(keys) {
		rs.log.WithContext(ctx).Errorf("random scheduler, taskId:%v not enough quota. type:%v not exist.", resource.TaskId, resourceType)
		return nil, ErrNotEnoughQuota
	}

	// 跳过弹性保留机器，如果是大Quota任务，优先分配，不保留
	length := rs.getElasticMaxLen(ctx, quotaTreeMap, resource)

	var backupWorkerQuota *WorkerQuota
	random := rand.Int() % (length - start)
	for i := 0; i < length-start; i++ {
		idx := start + random + i
		if idx > length-1 {
			idx = start + idx - length
		}

		treeMapKey := keys[idx]
		value, _ := quotaTreeMap.Get(treeMapKey)
		var workerQuota *WorkerQuota
		if workerQuotaMap, ok := value.(map[string]*WorkerQuota); ok {
			for _, workerQuota = range workerQuotaMap {
				if wq, err := rs.allocWorkerQuota(ctx, resource, workerQuota); err == nil {
					return wq, nil
				} else if errors.Is(err, ErrNotEnoughQuota) {
					log.Warnf("random scheduler, taskId:%v no enough quota. worker:%v", resource.TaskId, JsonToString(workerQuota))
				} else if errors.Is(err, ErrMatchLabelPreferredKey) && backupWorkerQuota == nil {
					backupWorkerQuota = workerQuota
				}
			}
		}
	}
	if backupWorkerQuota != nil {
		// backup
		rs.log.WithContext(ctx).Errorf("random scheduler, taskId:%v no affinity worker, use backup. %v",
			resource.TaskId, JsonToString(rs.WorkerQuotaMap))

		return rs.allocWorkerQuotaWithAffinity(ctx, resource, backupWorkerQuota, true)
	}

	rs.log.WithContext(ctx).Errorf("random scheduler fail, no worker. task:%v alloc resource failed. type:%v not exist.", JsonToString(resource),
		resourceType)
	return nil, ErrWorkerNotFound
}
