package biz

const (
	EventLeaderSuccess int8 = 0
	EventLeaderLost    int8 = 1
	EventLeaderChanged int8 = 2

	LeaderConfigKey = "app.leader.addr"
	LeaderName      = "TaskSchedule"
)

type Leader struct {
	Key      string           `json:"Key,omitempty"`
	Instance string           `json:"Instance,omitempty"`
	Version  uint64           `json:"Version,omitempty"`
	Watchers []LeaderObserver `json:"Watchers,omitempty"`
}

type LeaderObserver func(int8, string)

type LeaderRepo interface {
	Watch(string, LeaderObserver)
}
