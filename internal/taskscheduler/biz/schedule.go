package biz

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/emirpasic/gods/maps/treemap"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/mohae/deepcopy"
	v1 "proto.mpp/api/common/v1"
	pb "proto.mpp/api/taskscheduler/v1"
)

var (
	ErrNotFound                 = errors.NotFound(v1.ErrorReason_RESOURCE_NOT_FOUND.String(), "resource not found.")
	ErrNotEnoughQuota           = errors.New(http.StatusTooManyRequests, v1.ErrorReason_NOT_ENOUGH_QUOTA.String(), "not enough quota.")
	ErrMaxTaskNum               = errors.New(http.StatusTooManyRequests, "ErrMaxTaskNum", "larger than the maximum number of tasks.")
	ErrPodAffinityFail          = errors.New(http.StatusTooManyRequests, "PodAffinityFail", "pod affinity failed.")
	ErrElasticReserved          = errors.New(http.StatusTooManyRequests, "ErrElasticReserved", "elastic reserved.")
	ErrLoadOverflow             = errors.New(http.StatusTooManyRequests, "ErrLoadOverflow", "worker load overflow.")
	ErrQuotaSetOverflow         = errors.New(http.StatusTooManyRequests, "ErrQuotaSetOverflow", "worker quota set overflow.")
	ErrNotEnoughNoDominantQuota = errors.New(http.StatusTooManyRequests, "ErrNotEnoughNoDominantQuota", "not enough no dominant quota.")
	ErrInternalError            = errors.InternalServer(v1.ErrorReason_INTERNAL_ERROR.String(), "internal error.")
	ErrMasterError              = errors.InternalServer(v1.ErrorReason_MASTER_ERROR.String(), "call master error.")
	ErrMasterNotReady           = errors.InternalServer(v1.ErrorReason_MASTER_ERROR.String(), "master not ready.")
	ErrMatchLabelKey            = errors.New(http.StatusTooManyRequests, "ErrMatchLabelKey", "match label key fail.")
	ErrMatchLabelPreferredKey   = errors.New(http.StatusTooManyRequests, "ErrMatchLabelPreferredKey", "match label preferred key fail.")
)

const (
	SchedulerMaster    string = "SchedulerMaster"
	ScheduleSlave      string = "ScheduleSlave"
	ScheduleLockSecond int64  = 5
)

type WorkerQuota struct {
	Worker         *Worker              `json:"Worker,omitempty"`
	Label          map[string]string    `json:"Label,omitempty"`          // 调度的动态标签，如源流反亲和
	Resources      map[string]*Resource `json:"Resources,omitempty"`      // 资源列表
	RestQuota      QuotaSet             `json:"RestQuota,omitempty"`      // 剩余Quota
	Strategy       *Strategy            `json:"Strategy,omitempty"`       // 策略
	TagKey         string               `json:"TagKey,omitempty"`         // tag中的key
	LabelKey       string               `json:"LabelKey,omitempty"`       // label中的key
	NeedSeize      bool                 `json:"NeedSeize,omitempty"`      // 混跑需要抢占
	SeizeResources map[string]*Resource `json:"SeizeResources,omitempty"` // 混跑需要抢占的资源列表
	SubVersion     int32                `json:"SubVersion,omitempty"`     // todo 返回混跑引擎的具体版本(基线和灰度版本） 或者返回是否灰度中的worker，由任务管理选择worker
}

type Scheduler interface {
	Alloc(context.Context, *Resource) (*WorkerQuota, error)
	Free(context.Context, *Resource, *WorkerQuota) error
	Put(context.Context, *WorkerQuota)
	Remove(context.Context, *WorkerQuota)
	Statistics(context.Context, []*ChildWorker, map[string]*Strategy) *WaterLevel
	SetWaterLevel(*WaterLevel)
	GetWaterLevel() *WaterLevel
	Printf(ctx context.Context)
}

// RoundRobinScheduler 轮询策略
type RoundRobinScheduler struct {
	WorkerQuotaMap *treemap.Map // 红黑树
}

// LabelGroup 解决标签亲和性问题
type LabelGroup struct {
	Strategy  *Strategy         `json:"Strategy,omitempty"`
	Label     map[string]string `json:"Label,omitempty"`
	Scheduler Scheduler         `json:"Scheduler,omitempty"`
}

// TagGroup 解决Product、Engine、Tag分组问题
type TagGroup struct {
	Product     string       `json:"Product,omitempty"`
	EngineModel string       `json:"EngineModel,omitempty"`
	Tag         string       `json:"Tag,omitempty"`
	Strategy    *Strategy    `json:"Strategy,omitempty"`
	LabelGroup  *treemap.Map `json:"LabelGroup,omitempty"`
}

type SchedulerUnit struct {
	mutex           sync.Mutex
	TagGroup        map[string]*TagGroup // key为 Product、Engine、Tag的组合
	Workers         map[string]*WorkerQuota
	Resources       map[string]*Resource
	RecentAlloc     map[string]*Resource
	RecentFree      map[string]*Resource
	Strategies      map[string]*Strategy
	DefaultStrategy *Strategy
	Role            string
	log             *log.Helper
}

type ScheduleManager struct {
	//mutex         sync.Mutex
	//TagGroup      map[string]*TagGroup
	//tagGroupLocal map[string]*TagGroup
	//Workers       map[string]*WorkerQuota
	//Resources     map[string]*Resource
	//recentAlloc   map[string]*Resource
	//RecentFree    map[string]*Resource
	//Strategies    map[string]*Strategy
	ScheduleUnits map[string]*SchedulerUnit

	leader LeaderRepo
	log    *log.Helper
}

type TaskScheduleMasterApi interface {
	InitClient(context.Context, string) error
	ResetClient(context.Context, string) error
	GetEndpoint() string
	FreeMasterResource(context.Context, string) (*Worker, error)
	AllocMasterResource(context.Context, *pb.AllocResourceRequest) (*Worker, error)
	GetMasterWorker(context.Context, string) (*Worker, error)
}

func NewScheduleManager(leader LeaderRepo, logger log.Logger) *ScheduleManager {
	scheduleUnits := make(map[string]*SchedulerUnit)
	scheduleUnits[SchedulerMaster] = &SchedulerUnit{
		DefaultStrategy: &DefaultMasterStrategy,
		Role:            SchedulerMaster,
		log:             log.NewHelper(logger)}
	scheduleUnits[ScheduleSlave] = &SchedulerUnit{
		DefaultStrategy: &DefaultSlaveStrategy,
		Role:            ScheduleSlave,
		log:             log.NewHelper(logger)}
	return &ScheduleManager{leader: leader, ScheduleUnits: scheduleUnits, log: log.NewHelper(logger)}
}

func (sm *ScheduleManager) GetTagGroup(ctx context.Context, scheduleType string) map[string]*TagGroup {
	return sm.ScheduleUnits[scheduleType].TagGroup
}

func (sm *ScheduleManager) GetStrategies(ctx context.Context) map[string]*Strategy {
	return sm.ScheduleUnits[ScheduleSlave].Strategies
}

func (sm *ScheduleManager) GetSlaveStrategy(ctx context.Context, workerId string) *Strategy {
	if workerQuota, ok := sm.ScheduleUnits[ScheduleSlave].Workers[workerId]; ok {
		labelGroup, _ := sm.ScheduleUnits[ScheduleSlave].findLabelByWorkerQuota(ctx, workerQuota)
		return labelGroup.Strategy
	} else {
		sm.log.WithContext(ctx).Warnf("get worker from mem fail. id:%v workerQuota:%v", workerId, JsonToString(workerQuota))
		return &DefaultSlaveStrategy
	}
}

func (sm *ScheduleManager) GetWorker(ctx context.Context, workerId string) *Worker {
	if workerQuota, ok := sm.ScheduleUnits[ScheduleSlave].Workers[workerId]; ok {
		return workerQuota.Worker
	} else {
		sm.log.WithContext(ctx).Warnf("get worker from mem fail. id:%v workerQuota:%v", workerId, JsonToString(workerQuota))
		return nil
	}
}

func (sm *ScheduleManager) SyncResources(ctx context.Context, scheduleType string, resources []*Resource) error {
	// 只有master有必要同步所有资源
	return sm.ScheduleUnits[scheduleType].SyncResources(ctx, resources)
}

func (sm *ScheduleManager) SyncWorkers(ctx context.Context, workers []*Worker, strategies []*Strategy) (map[string]*Worker, []*Worker) {
	start := time.Now().UnixMicro()
	defer func() {
		end := time.Now().UnixMicro()
		latency := end - start
		if latency > 10000 {
			sm.log.WithContext(ctx).Warnf("ScheduleManager SyncWorkers, latency time:%v", end-start)
		}
	}()

	newStrategies := sm.makeSlaveStrategies(ctx, strategies)

	taintWorkers, labelDiffWorkers, newWorkers, offlineWorkers := sm.diffSyncWorkers(ctx, workers)

	newSlaveWorkers := sm.copyWorkerMap(ctx, newWorkers)

	sm.ScheduleUnits[ScheduleSlave].SyncWorkers(ctx, workers, newSlaveWorkers, taintWorkers, labelDiffWorkers, offlineWorkers, newStrategies)
	versionChangedWorkers, _ := sm.ScheduleUnits[SchedulerMaster].SyncWorkers(ctx, workers, newWorkers, taintWorkers, labelDiffWorkers, offlineWorkers, strategies)
	return versionChangedWorkers, offlineWorkers
}

func (sm *ScheduleManager) diffSyncWorkers(ctx context.Context, workers []*Worker) (map[string]*WorkerQuota, map[string]*WorkerQuota, map[string]*Worker, []*Worker) {
	start := time.Now().UnixMicro()
	defer func() {
		end := time.Now().UnixMicro()
		latency := end - start
		if latency > 10000 {
			sm.log.WithContext(ctx).Warnf("diffSyncWorkers, latency time:%v", end-start)
		}
	}()

	taintWorkers := make(map[string]*WorkerQuota)
	labelDiffWorkers := make(map[string]*WorkerQuota)
	newWorkers := make(map[string]*Worker)
	dbWorkerMap := make(map[string]*Worker)
	var offlineWorkers []*Worker
	for _, worker := range workers {
		dbWorkerMap[worker.WorkerId] = worker
		if workerQuota, ok := sm.ScheduleUnits[SchedulerMaster].Workers[worker.WorkerId]; ok {
			//if len(workerQuota.Worker.Taint) == 0 && len(worker.Taint) > 0 {
			if (len(workerQuota.Worker.Taint) == 0 && len(worker.Taint) > 0) || (len(workerQuota.Worker.Taint) > 0 && len(worker.Taint) == 0) {
				// 0->1或者1->0对调度有影响，其它变化忽略
				sm.log.WithContext(ctx).Warnf("worker taint changed. worker:%v workerQuota:%v worker.taint:%v workerQuota.taint:%v", worker, workerQuota, worker.Taint, workerQuota.Worker.Taint)
				taintWorkers[worker.WorkerId] = workerQuota
			} else if JsonToString(worker.Label) != JsonToString(workerQuota.Worker.Label) || worker.Tag != workerQuota.Worker.Tag {
				sm.log.WithContext(ctx).Warnf("worker tag or label changed. worker:%v workerQuota:%v", worker, workerQuota)
				labelDiffWorkers[worker.WorkerId] = workerQuota
			}
		} else {
			newWorkers[worker.WorkerId] = worker
		}
	}

	for _, workerQuota := range sm.ScheduleUnits[SchedulerMaster].Workers {
		if _, ok := dbWorkerMap[workerQuota.Worker.WorkerId]; !ok {
			sm.log.WithContext(ctx).Warnf("worker not exist in db. worker:%v", workerQuota.Worker)
			offlineWorkers = append(offlineWorkers, workerQuota.Worker)
		}
	}

	return taintWorkers, labelDiffWorkers, newWorkers, offlineWorkers
}

func (sm *ScheduleManager) Init(ctx context.Context, workers []*Worker, resources []*Resource, strategies []*Strategy) map[string]*Resource {
	sm.log.WithContext(ctx).Infof("schedule init. worker:%v resource:%v strategy:%v", len(workers),
		len(resources), len(strategies))

	newWorker := sm.copyWorkers(ctx, workers)
	newStrategies := sm.makeSlaveStrategies(ctx, strategies)

	sm.ScheduleUnits[ScheduleSlave].Init(ctx, newWorker, nil, newStrategies)
	return sm.ScheduleUnits[SchedulerMaster].Init(ctx, workers, resources, strategies)
}

func (sm *ScheduleManager) diffStrategies(ctx context.Context, strategies []*Strategy) []*Strategy {
	start := time.Now().UnixMicro()
	defer func() {
		end := time.Now().UnixMicro()
		latency := end - start
		if latency > 10000 {
			sm.log.WithContext(ctx).Warnf("diffStrategies, latency time:%v", end-start)
		}
	}()

	var newStrategies []*Strategy
	for _, strategy := range strategies {
		var s Strategy
		err := copier.Copy(&s, strategy)
		if err != nil {
			sm.log.WithContext(ctx).Warnf("sync worker copy strategy failed. err:%v", err)
		}
		s.Strategy = StrategyRandom
		newStrategies = append(newStrategies, &s)
	}
	return newStrategies
}

func (sm *ScheduleManager) makeSlaveStrategies(ctx context.Context, strategies []*Strategy) []*Strategy {
	start := time.Now().UnixMicro()
	defer func() {
		end := time.Now().UnixMicro()
		latency := end - start
		if latency > 10000 {
			sm.log.WithContext(ctx).Warnf("makeSlaveStrategies, latency time:%v", end-start)
		}
	}()

	var newStrategies []*Strategy
	for _, strategy := range strategies {
		var s Strategy
		err := copier.Copy(&s, strategy)
		if err != nil {
			sm.log.WithContext(ctx).Warnf("sync worker copy strategy failed. err:%v", err)
		}
		s.Strategy = StrategyRandom
		newStrategies = append(newStrategies, &s)
	}
	return newStrategies
}

func (sm *ScheduleManager) copyWorkers(ctx context.Context, workers []*Worker) []*Worker {
	start := time.Now().UnixMicro()
	defer func() {
		end := time.Now().UnixMicro()
		latency := end - start
		if latency > 10000 {
			sm.log.WithContext(ctx).Warnf("copyWorkers, latency time:%v", end-start)
		}
	}()

	var newWorkers []*Worker
	for _, worker := range workers {
		newWorker := deepcopy.Copy(worker).(*Worker)

		newWorkers = append(newWorkers, newWorker)
	}
	return newWorkers
}

func (sm *ScheduleManager) copyWorkerMap(ctx context.Context, workers map[string]*Worker) map[string]*Worker {
	start := time.Now().UnixMicro()
	defer func() {
		end := time.Now().UnixMicro()
		latency := end - start
		if latency > 10000 {
			sm.log.WithContext(ctx).Warnf("copyWorkerMap, latency time:%v", end-start)
		}
	}()

	newWorkerMap := make(map[string]*Worker)
	for key, worker := range workers {
		newWorkerMap[key] = deepcopy.Copy(worker).(*Worker)
	}
	return newWorkerMap
}

func (sm *ScheduleManager) Alloc(ctx context.Context, scheduleType string, resource *Resource) (*Worker, error) {
	if unit, ok := sm.ScheduleUnits[scheduleType]; ok {
		return unit.Alloc(ctx, resource)
	} else {
		sm.log.WithContext(ctx).Warnf("get schedule unit fail. type:%v", scheduleType)
		return nil, errors.NotFound(v1.ErrorReason_INTERNAL_ERROR.String(), fmt.Sprintf("schedule:%v type not found",
			scheduleType))
	}
}

func (sm *ScheduleManager) Free(ctx context.Context, scheduleType string, taskId string) (*Worker, error) {
	if unit, ok := sm.ScheduleUnits[scheduleType]; ok {
		return unit.Free(ctx, taskId)
	} else {
		sm.log.WithContext(ctx).Warnf("get schedule unit fail. type:%v", scheduleType)
		return nil, errors.NotFound(v1.ErrorReason_INTERNAL_ERROR.String(), fmt.Sprintf("schedule:%v type not found",
			scheduleType))
	}
}

func (su *SchedulerUnit) Init(ctx context.Context, workers []*Worker, resources []*Resource, strategies []*Strategy) map[string]*Resource {
	start := time.Now().UnixMicro()
	defer func() {
		end := time.Now().UnixMicro()
		latency := end - start
		if latency > 10000 {
			su.log.WithContext(ctx).Warnf("unit:%v Init, latency time:%v", su.Role, end-start)
		}
	}()
	//log.Infof("schedule init. worker:%v resource:%v strategy:%v", len(Workers),
	//	len(Resources), len(Strategies))

	su.mutex.Lock()
	defer su.mutex.Unlock()

	su.RecentAlloc = make(map[string]*Resource)
	su.RecentFree = make(map[string]*Resource)
	su.Resources = make(map[string]*Resource)

	su.initAndCompareStrategies(ctx, strategies)

	su.initWorkerQuota(ctx, workers)

	var lockWorkerQuota map[string]*QuotaSet
	var lostResources map[string]*Resource
	if resources != nil || len(resources) > 0 {
		for _, resource := range resources {
			su.Resources[resource.TaskId] = resource
		}
		lostResources, lockWorkerQuota = su.batchCalcAllocResource(ctx, su.Resources)
	}

	su.makeScheduleWorkers(ctx, lockWorkerQuota)

	//log.Debugf("TagGroup:%v Init: \n %v", su.Role, su.getTagGroupString(ctx))
	//sm.printTagGroup(ctx)
	return lostResources
}

func (su *SchedulerUnit) initAndCompareStrategies(ctx context.Context, strategies []*Strategy) map[string]*Strategy {
	oldStrategies := su.Strategies
	su.Strategies = make(map[string]*Strategy)
	for _, strategy := range strategies {
		// 判断配置有效性
		if strategy.Config == nil || strategy.Config.Dominants == nil || len(strategy.Config.ScheduleResourceType) == 0 {
			su.log.WithContext(ctx).Warnf("inavlid strategy config: %v", strategy)
			continue
		}

		strategy.StrategyId = MakeStrategyId(&Strategy{
			Product:     strategy.Product,
			EngineModel: strategy.EngineModel,
			Tag:         strategy.Tag,
			Label:       strategy.Label,
		})

		su.Strategies[strategy.StrategyId] = strategy
	}

	// 策略变化
	return su.diffStrategies(ctx, oldStrategies)
}

func (su *SchedulerUnit) diffStrategies(ctx context.Context, oldStrategies map[string]*Strategy) map[string]*Strategy {
	diffStrategies := make(map[string]*Strategy)
	for s, news := range su.Strategies {
		if olds, ok := oldStrategies[s]; ok {
			if JsonToString(olds) != JsonToString(news) {
				su.log.WithContext(ctx).Infof("strategy changed. old:%v new:%v", JsonToString(olds), JsonToString(news))
				diffStrategies[s] = news
			}
		} else {
			log.Infof("new strategy. new:%v", JsonToString(news))
			diffStrategies[s] = news
		}
	}

	for s, olds := range oldStrategies {
		if strategy, ok := su.Strategies[s]; !ok {
			su.log.WithContext(ctx).Infof("delete strategy. old:%v", JsonToString(olds))
			diffStrategies[s] = strategy
		}
	}

	return diffStrategies
}

func (su *SchedulerUnit) getTagGroupString(ctx context.Context) string {
	if su.TagGroup == nil || len(su.TagGroup) == 0 {
		return ""
	}

	return JsonToString(su.TagGroup)
}

// SyncWorkers 3秒同步一次worker
// 1. taint变化，删除调度
// 2. tag、label变化，移动位置
// 3. 新申请任务，worker心跳未更新，锁定资源更新
func (su *SchedulerUnit) SyncWorkers(ctx context.Context, workers []*Worker, newWorkers map[string]*Worker,
	taintWorkers map[string]*WorkerQuota, labelDiff map[string]*WorkerQuota, offlineWorkers []*Worker, strategies []*Strategy) (map[string]*Worker, map[string]*Resource) {
	start := time.Now().UnixMicro()
	defer func() {
		end := time.Now().UnixMicro()
		latency := end - start
		if latency > 10000 {
			su.log.WithContext(ctx).Warnf("unit:%v SyncWorkers, latency time:%v", su.Role, end-start)
		}
	}()

	su.mutex.Lock()
	defer su.mutex.Unlock()

	diffStrategies := su.initAndCompareStrategies(ctx, strategies)

	versionChangedWorkers := su.remakeWorkerQuotas(ctx, workers, newWorkers, taintWorkers, labelDiff, offlineWorkers)

	su.reloadStrategies(ctx, diffStrategies)

	// todo 这里不传resource,real就没有锁quota机制了,给master模块传入resource
	resources := make(map[string]*Resource)
	if su.Role == SchedulerMaster {
		resources = su.Resources
		log.Infof("resources length: %v", len(resources))
	}
	lostResource, lockWorkerQuota := su.batchCalcAllocResourceForSyncWorker(ctx, resources)
	su.makeScheduleWorkers(ctx, lockWorkerQuota)

	//sm.printTagGroup(ctx)
	//newTagGroup := sm.getTagGroupString(ctx)
	//if oldWorkersLen != len(su.Workers) {
	//	log.Debugf("TagGroup:%v Changed: \n %v", su.Role, su.getTagGroupString(ctx))
	//}

	return versionChangedWorkers, lostResource
}

func (su *SchedulerUnit) reloadStrategies(ctx context.Context, diffStrategies map[string]*Strategy) {
	for _, strategy := range diffStrategies {
		groupKey := BuildTagKey(strategy.Product, strategy.EngineModel, strategy.Tag)
		// todo 验证 如果先配了策略，再配置机器，这里是否策略就不生效？
		tag, ok := su.TagGroup[groupKey]
		if !ok {
			continue
		}

		labelKey := BuildLabelKey(strategy.Label)
		label, ok := tag.LabelGroup.Get(labelKey)
		if !ok {
			continue
		}

		labelGroup := label.(*LabelGroup)
		labelGroup.Strategy = strategy
	}

	for _, group := range su.TagGroup {
		for it := group.LabelGroup.Iterator(); it.Next(); {
			labelGroup := it.Value().(*LabelGroup)
			scheduler := su.makeScheduler(ctx, labelGroup.Strategy)
			scheduler.SetWaterLevel(labelGroup.Scheduler.GetWaterLevel())
			labelGroup.Scheduler = scheduler
		}
	}
}

func (su *SchedulerUnit) SyncResources(ctx context.Context, resources []*Resource) error {
	su.mutex.Lock()
	defer su.mutex.Unlock()

	dbMap := make(map[string]*Resource)

	su.syncDBAllocResource(ctx, resources, dbMap)

	su.syncDBFreeResource(ctx, dbMap)

	return nil
}

func (su *SchedulerUnit) Alloc(ctx context.Context, resource *Resource) (*Worker, error) {
	su.mutex.Lock()
	defer su.mutex.Unlock()

	var err error
	var wq *WorkerQuota

	// 接口幂等
	if r, ok := su.Resources[resource.TaskId]; ok {
		if worker, ok1 := su.Workers[r.WorkerId]; ok1 {
			return worker.Worker, nil
		}
	}

	tagKey := BuildTagKey(resource.Product, resource.EngineModel, resource.Tag)
	// 混跑调度，更换混跑的三元组
	if resource.IsMixed {
		oldTagKey := tagKey
		tagKey = BuildTagKey(resource.MixConfig["product"], resource.MixConfig["engineModel"], resource.MixConfig["tag"])
		//混跑测试日志
		log.Infof(" generate mixed tagGroup, mixTagKey:%v, oldTagKey:%v", tagKey, oldTagKey)
	}

	tagGroup, ok := su.TagGroup[tagKey]
	if !ok {
		log.Infof("TagGroup:%v not found. resource:%v", tagKey, resource)
		return nil, ErrWorkerNotFound
	}

	labelGroupAffinity := su.splitAffinity(resource)
	backupLabelGroup := make(map[string]*LabelGroup)
	iterator := tagGroup.LabelGroup.Iterator()
	for iterator.Next() {
		labelGroup := iterator.Value().(*LabelGroup)
		//su.log.WithContext(ctx).Infof("label taskId %s group cost time %v cost lock time %v", resource.TaskId, time.Since(t).Milliseconds(), time.Since(t1).Milliseconds())
		if labelGroupAffinity != nil {
			switch labelGroupAffinity.matchLabelKey(labelGroup.Label) {
			case AffinityRequired:
				log.Infof("[AffinityRequired] label affinity preferred fail, required success, add to backup. jobId[%s] taskId[%s] labelGroup[%v] labelGroupAffinity[%v] resourceLabel[%v] resourceAffinity[%v]",
					resource.JobId, resource.TaskId,
					labelGroup.Label, JsonToString(labelGroupAffinity), JsonToString(resource.Label), JsonToString(resource.Affinity))
				backupLabelGroup[iterator.Key().(string)] = labelGroup
				continue
			case AffinityPreferred:
				log.Infof("[AffinityPreferred] label affinity preferred success. jobId[%s] taskId[%s] labelGroup[%v] labelGroupAffinity[%v] resourceAffinity[%v]",
					resource.JobId, resource.TaskId, labelGroup.Label, JsonToString(labelGroupAffinity), JsonToString(resource.Affinity))
			case AffinityNo:
				log.Infof("[AffinityNo] label affinity required fail. jobId[%s] taskId[%s] labelGroup[%v] labelGroupAffinity[%v] resourceLabel[%v] resourceAffinity[%v]",
					resource.JobId, resource.TaskId,
					labelGroup.Label, JsonToString(labelGroupAffinity), JsonToString(resource.Label), JsonToString(resource.Affinity))
				continue
			default:
				log.Infof("[AffinityDefault] label affinity required fail. jobId[%s] taskId[%s] labelGroup[%v] resourceAffinity[%v] labelGroupLabel[%v]",
					resource.JobId, resource.TaskId,
					labelGroup.Label, JsonToString(resource.Affinity), JsonToString(labelGroup.Label))
				continue
			}
		}

		//su.log.WithContext(ctx).Infof("matchLabelKey taskId %s group cost time %v cost lock time %v", resource.TaskId, time.Since(t).Milliseconds(), time.Since(t1).Milliseconds())

		wq, err = su.allocLabelResource(ctx, resource, labelGroup)

		//su.log.WithContext(ctx).Infof("allocLabelResource  taskId %s group cost time %v cost lock time %v", resource.TaskId, time.Since(t).Milliseconds(), time.Since(t1).Milliseconds())

		if err == nil {
			var newWorker *Worker
			if wq.Worker != nil {
				newWorker = deepcopy.Copy(wq.Worker).(*Worker)
			} else {
				newWorker = nil
			}
			return newWorker, err
		}
	}

	// backup
	for _, group := range backupLabelGroup {
		labelGroup := group
		wq, err = su.allocLabelResource(ctx, resource, labelGroup)
		if err == nil {
			var newWorker *Worker
			if wq.Worker != nil {
				newWorker = deepcopy.Copy(wq.Worker).(*Worker)
			} else {
				newWorker = nil
			}
			return newWorker, err
		}
	}

	//修复required不满足，返回worker err为空的问题
	if err == nil {
		err = ErrWorkerNotFound
	}
	su.log.WithContext(ctx).Warnf("call alloc quota failed, no enough quota. resource:%v, err:%v", JsonToString(resource), err)

	return nil, err
}

func (su *SchedulerUnit) splitAffinity(resource *Resource) *Affinity {
	if resource.Affinity == nil {
		return nil
	}

	labelGroupAffinity := Affinity{}
	workerAffinity := Affinity{}
	for _, require := range resource.Affinity.Required {
		if strings.HasPrefix(require.Key, "worker") {
			workerAffinity.Required = append(workerAffinity.Required, require)
		} else {
			labelGroupAffinity.Required = append(labelGroupAffinity.Required, require)
		}
	}
	for _, preferred := range resource.Affinity.Preferred {
		if strings.HasPrefix(preferred.Key, "worker") {
			workerAffinity.Preferred = append(workerAffinity.Preferred, preferred)
		} else {
			labelGroupAffinity.Preferred = append(labelGroupAffinity.Preferred, preferred)
		}
	}

	resource.Affinity = &workerAffinity
	return &labelGroupAffinity
}

func (su *SchedulerUnit) allocLabelResource(ctx context.Context, resource *Resource, labelGroup *LabelGroup) (*WorkerQuota, error) {
	workerQuota, err := labelGroup.Scheduler.Alloc(ctx, resource)
	if err != nil {
		return nil, err
	}

	resource.WorkerId = workerQuota.Worker.WorkerId
	resource.WorkerVersion = workerQuota.Worker.OnlineVersion
	resource.WorkerIp = workerQuota.Worker.Ip
	resource.WorkerPort = workerQuota.Worker.Port
	resource.ModifiedTime = time.Now()

	su.Resources[resource.TaskId] = resource
	su.RecentAlloc[resource.TaskId] = resource
	//log.Infof("alloc success: taskId:%v worker:%v", resource.TaskId, JsonToString(workerQuota))
	//log.Infof("alloc success: resource:%v", JsonToString(sm.Resources))

	return workerQuota, nil
}

func (su *SchedulerUnit) Free(ctx context.Context, taskId string) (*Worker, error) {
	//su.log.WithContext(ctx).Infof("schedule free resource: %v", taskId)
	su.mutex.Lock()
	defer su.mutex.Unlock()

	// 查询任务是否存在
	resource, ok := su.Resources[taskId]
	if !ok {
		return nil, ErrResourceNotFound
	}

	worker, err := su.freeResource(ctx, resource)
	if err != nil {
		return nil, err
	}

	var newWorker *Worker
	if worker != nil {
		newWorker = deepcopy.Copy(worker).(*Worker)
	}

	return newWorker, err
}

func (su *SchedulerUnit) freeResource(ctx context.Context, resource *Resource) (*Worker, error) {
	workerQuota, ok := su.Workers[resource.WorkerId]
	if !ok {
		return su.makeFreeResourceFail(ctx, resource, "freeResource get worker failed. resource:%v", JsonToString(resource))
	}

	labelGroup, err := su.findLabelByWorkerQuota(ctx, workerQuota)
	if err != nil {
		if len(workerQuota.Worker.Taint) > 0 {
			// 加了污点的机器，不会scheduler里，直接删除。  0722 worker有taint并不会导致这里进err
			workerQuota.Worker.AllocQuota.Sub(resource.Quota)
			workerQuota.RestQuota.Add(resource.Quota)
			delete(workerQuota.Resources, resource.TaskId)
		}
		return su.makeFreeResourceFail(ctx, resource, "freeResource find label group failed. resource:%v worker:%v err:%v",
			JsonToString(resource), JsonToString(workerQuota.Worker), err)
	}

	err = labelGroup.Scheduler.Free(ctx, resource, workerQuota)
	if err != nil {
		if len(workerQuota.Worker.Taint) > 0 {
			// 加了污点的机器，不会scheduler里，直接删除。
			workerQuota.Worker.AllocQuota.Sub(resource.Quota)
			workerQuota.RestQuota.Add(resource.Quota)
			delete(workerQuota.Resources, resource.TaskId)
		} else {
			su.log.WithContext(ctx).Warnf("free resource failed. resource:%v worker:%v err:%v", JsonToString(resource), JsonToString(workerQuota.Worker), err)
		}
	}

	su.RecentFree[resource.TaskId] = resource
	delete(su.Resources, resource.TaskId)

	return workerQuota.Worker, nil
}

func (su *SchedulerUnit) makeFreeResourceFail(ctx context.Context, resource *Resource, fmt string, args ...interface{}) (*Worker, error) {
	su.log.WithContext(ctx).Warnf(fmt, args...)
	su.RecentFree[resource.TaskId] = resource
	delete(su.Resources, resource.TaskId)
	return nil, ErrWorkerNotFound
}

func (su *SchedulerUnit) remakeWorkerQuotas(ctx context.Context, workers []*Worker, newWorkers map[string]*Worker,
	taintWorkers map[string]*WorkerQuota, labelDiff map[string]*WorkerQuota, offlineWorkers []*Worker) map[string]*Worker {
	start := time.Now().UnixMicro()
	defer func() {
		end := time.Now().UnixMicro()
		latency := end - start
		if latency > 10000 {
			su.log.WithContext(ctx).Warnf("unit:%v remakeWorkerQuotas, latency time:%v", su.Role, end-start)
		}
	}()

	// todo 删除
	//for key, tagGroup := range su.TagGroup {
	//	su.log.WithContext(ctx).Infof("remakeWorkerQuotas print: tagGroup:%v, value:%+v", key, tagGroup)
	//	labelGroupMap := tagGroup.LabelGroup
	//	for it := labelGroupMap.Iterator(); it.Next(); {
	//		labelGroup := it.Value().(*LabelGroup)
	//		su.log.WithContext(ctx).Infof("remakeWorkerQuotas print: labelGroupKey:%v, value:%+v", it.Key(), labelGroup)
	//	}
	//}

	versionChangedWorkers := make(map[string]*Worker)
	for _, worker := range workers {
		memWorkerQuota, ok := su.Workers[worker.WorkerId]
		if ok {
			if worker.OnlineVersion != memWorkerQuota.Worker.OnlineVersion {
				su.log.WithContext(ctx).Warnf("worker online version changed. old:%v new:%v", JsonToString(memWorkerQuota.Worker),
					JsonToString(worker))

				memWorkerQuota.Worker.OnlineVersion = worker.OnlineVersion
				versionChangedWorkers[worker.WorkerId] = worker
				memWorkerQuota.Worker.Taint = worker.Taint
			} else if _, ok := taintWorkers[worker.WorkerId]; ok {
				// 同步污点
				memWorkerQuota.Worker.Taint = worker.Taint
			} else if _, ok := labelDiff[worker.WorkerId]; ok {
				// 更新标签，移动位置
				memWorkerQuota.Worker.Tag = worker.Tag
				memWorkerQuota.Worker.Label = worker.Label
				memWorkerQuota.TagKey = BuildTagKey(memWorkerQuota.Worker.Product, memWorkerQuota.Worker.EngineModel, memWorkerQuota.Worker.Tag)
				memWorkerQuota.LabelKey = BuildLabelKey(memWorkerQuota.Worker.Label)
				// todo 如果是改成一个不存在的标签，这里没有重新makeTagGroup？？ 重新make一下
				su.makeTagGroup(ctx, memWorkerQuota)
			}

			memWorkerQuota.Worker.RealQuota = worker.RealQuota
			memWorkerQuota.Worker.Load = worker.Load
		} else {
			su.log.WithContext(ctx).Infof("worker online. worker:%v", JsonToString(worker))
			if newWorker, ok := newWorkers[worker.WorkerId]; ok {
				memWorkerQuota = su.makeWorkerQuota(worker)
				su.Workers[worker.WorkerId] = memWorkerQuota
				su.makeTagGroup(ctx, memWorkerQuota)
			} else {
				su.log.WithContext(ctx).Errorf("remakeWorkerQuotas: new worker not exist no lock map.newWorker:%v, map:%v", JsonToString(newWorker), JsonToString(newWorkers))
				continue
			}
		}
	}

	for _, worker := range offlineWorkers {
		// 删除掉线worker
		if _, ok := su.Workers[worker.WorkerId]; ok {
			// todo su.TagGroup 不用同步清理？ --水位统计会报找不到worker日志
			delete(su.Workers, worker.WorkerId)
		}
	}

	return versionChangedWorkers
}

func (su *SchedulerUnit) initWorkerQuota(ctx context.Context, workers []*Worker) {
	start := time.Now().UnixMicro()
	defer func() {
		end := time.Now().UnixMicro()
		latency := end - start
		if latency > 10000 {
			su.log.WithContext(ctx).Warnf("unit:%v initWorkerQuota, latency time:%v", su.Role, end-start)
		}
	}()

	su.Workers = make(map[string]*WorkerQuota)
	su.TagGroup = make(map[string]*TagGroup)

	for _, worker := range workers {
		workerQuota := su.makeWorkerQuota(worker)

		su.Workers[worker.WorkerId] = workerQuota

		su.makeTagGroup(ctx, workerQuota)
	}
}

func (su *SchedulerUnit) makeTagGroup(ctx context.Context, workerQuota *WorkerQuota) *LabelGroup {
	workerQuota.TagKey = BuildTagKey(workerQuota.Worker.Product, workerQuota.Worker.EngineModel, workerQuota.Worker.Tag)
	tagGroup, ok := su.TagGroup[workerQuota.TagKey]
	if !ok {
		strategy := su.getGroupStrategy(workerQuota)
		//log.Infof("get group product:%v model:%v tag:%v strategy:%v", workerQuota.Worker.Product,
		//	workerQuota.Worker.EngineModel, workerQuota.Worker.Tag, JsonToString(strategy))
		tagGroup = &TagGroup{
			Product:     workerQuota.Worker.Product,
			EngineModel: workerQuota.Worker.EngineModel,
			Tag:         workerQuota.Worker.Tag,
			Strategy:    strategy,
			LabelGroup:  treemap.NewWithStringComparator(),
		}

		su.TagGroup[workerQuota.TagKey] = tagGroup
	}

	workerQuota.LabelKey = BuildLabelKey(workerQuota.Worker.Label)

	var labelGroup *LabelGroup
	value, ok := tagGroup.LabelGroup.Get(workerQuota.LabelKey)
	if !ok {
		strategy := su.getLabelStrategy(workerQuota, tagGroup.Strategy)
		//log.Infof("get label product:%v model:%v tag:%v strategy:%v", workerQuota.Worker.Product,
		//	workerQuota.Worker.EngineModel, workerQuota.Worker.Tag, JsonToString(strategy))
		scheduler := su.makeScheduler(ctx, strategy)
		labelGroup = &LabelGroup{
			Strategy:  strategy,
			Label:     workerQuota.Worker.Label,
			Scheduler: scheduler,
		}
		tagGroup.LabelGroup.Put(workerQuota.LabelKey, labelGroup)
	} else {
		labelGroup = value.(*LabelGroup)
	}

	return labelGroup
}

func (su *SchedulerUnit) makeWorkerQuota(worker *Worker) *WorkerQuota {
	worker.ScheduleQuota = make(map[string]int64)
	worker.AllocQuota = make(map[string]int64)

	if su.Role == SchedulerMaster {
		for key, value := range worker.Quota {
			worker.ScheduleQuota[key] = value
			worker.AllocQuota[key] = 0
		}
	}

	workerQuota := &WorkerQuota{
		Worker:    worker,
		RestQuota: worker.ScheduleQuota,
		Resources: make(map[string]*Resource),
		Label:     make(map[string]string),
	}
	workerQuota.Label["workerId"] = worker.WorkerId
	workerQuota.Label["workerIp"] = worker.Ip
	return workerQuota
}

func (su *SchedulerUnit) syncDBFreeResource(ctx context.Context, dbMap map[string]*Resource) {
	var inMemNotInDb []*Resource

	for _, resource := range su.Resources {
		// 内存中有，数据库中没有
		if _, ok := dbMap[resource.TaskId]; !ok {
			if _, ok := su.RecentAlloc[resource.TaskId]; !ok {
				su.log.WithContext(ctx).Warnf("sync from db, remove resource:%v", resource.TaskId)
				inMemNotInDb = append(inMemNotInDb, resource)
			}
		}
	}

	su.RecentAlloc = make(map[string]*Resource)
	for _, resource := range inMemNotInDb {
		_, err := su.freeResource(ctx, resource)
		if err != nil {
			su.log.WithContext(ctx).Errorf("sync from db, free resource:%v", resource.TaskId)
		}
	}
}

func (su *SchedulerUnit) syncDBAllocResource(ctx context.Context, resources []*Resource, dbMap map[string]*Resource) {
	inDbNotInMem := make(map[string]*WorkerQuota)

	for _, resource := range resources {
		// 非master分配，返回???

		dbMap[resource.TaskId] = resource

		// 数据库有，内存没有的
		if _, ok := su.Resources[resource.TaskId]; !ok {
			// 不是最近归还的
			if _, ok := su.RecentFree[resource.TaskId]; !ok {
				su.log.WithContext(ctx).Warnf("sync from db, put resource:%v", resource.TaskId)
				if _, ok := inDbNotInMem[resource.WorkerId]; !ok {
					if worker, ok := su.Workers[resource.WorkerId]; ok {
						workerQuota := &WorkerQuota{
							Worker:    worker.Worker,
							Resources: make(map[string]*Resource),
							RestQuota: worker.RestQuota,
							TagKey:    BuildTagKey(worker.Worker.Product, worker.Worker.EngineModel, worker.Worker.Tag),
							LabelKey:  BuildLabelKey(worker.Worker.Label),
						}
						inDbNotInMem[resource.WorkerId] = workerQuota
					} else {
						su.log.WithContext(ctx).Warnf("sync from db, worker not found. resource:%v", JsonToString(resource))
						continue
					}
				}
				inDbNotInMem[resource.WorkerId].Resources[resource.TaskId] = resource
			}
		}
	}

	su.RecentFree = make(map[string]*Resource)

	// 更新资源，并移动worker位置，
	su.batchSyncAllocResource(ctx, inDbNotInMem)

}

func (su *SchedulerUnit) makeScheduleWorkers(ctx context.Context, lockedWorkerQuota map[string]*QuotaSet) {
	start := time.Now().UnixMicro()
	defer func() {
		end := time.Now().UnixMicro()
		latency := end - start
		if latency > 10000 {
			su.log.WithContext(ctx).Warnf("unit:%v makeScheduleWorkers, latency time:%v", su.Role, end-start)
		}
	}()

	tagKeySet := make(map[string]bool)
	for _, workerQuota := range su.Workers {
		tagKeySet[workerQuota.TagKey] = true
		labelGroup, err := su.findLabelByWorkerQuota(ctx, workerQuota)
		if err != nil {
			su.log.WithContext(ctx).Errorf("find label by worker quota failed. workerQuota:%v, err:%v", JsonToString(workerQuota), err)
			continue
		}

		su.makeScheduleQuota(ctx, labelGroup, workerQuota, lockedWorkerQuota[workerQuota.Worker.WorkerId])
	}

	// 如果某一标签的worker都已经不存在了,则移除对应的tagGroup,消除水位统计告警  --会不会存在未知风险？
	for tagKey := range su.TagGroup {
		_, ok := tagKeySet[tagKey]
		if !ok {
			su.log.WithContext(ctx).Warnf("remove non-existent tag group:%v", tagKey)
			delete(su.TagGroup, tagKey)
		}
	}
}

func (su *SchedulerUnit) makeScheduleQuota(ctx context.Context, labelGroup *LabelGroup, workerQuota *WorkerQuota, lockedQuota *QuotaSet) {
	if len(workerQuota.Worker.Taint) > 0 {
		return
	}

	//workerQuota.Strategy = labelGroup.Strategy

	if labelGroup.Strategy.Config.ScheduleResourceType == ScheduleResourceTypeReal {
		// 根据真实资源调度
		//log.Infof("schedule by real quota. strategy:% workerQuota:%v", JsonToString(labelGroup.Strategy),
		//	JsonToString(workerQuota))
		workerQuota.RestQuota.CopyWithRatio(workerQuota.Worker.Quota, su.getLimitRatio(labelGroup.Strategy))
		workerQuota.RestQuota.Sub(workerQuota.Worker.RealQuota)
		if lockedQuota != nil {
			workerQuota.RestQuota.Sub(*lockedQuota)
		}
		//log.Infof("schedule by real quota. strategy:% workerQuota:%v", JsonToString(labelGroup.Strategy),
		//	JsonToString(workerQuota))
	} else {
		// 根据预估资源调度
		//log.Infof("schedule by estimate quota. strategy:% workerQuota:%v", JsonToString(labelGroup.Strategy),
		//    JsonToString(workerQuota))
		workerQuota.RestQuota.CopyWithRatio(workerQuota.Worker.Quota, su.getLimitRatio(labelGroup.Strategy))
		workerQuota.RestQuota.Sub(workerQuota.Worker.AllocQuota)
		//log.Infof("schedule by estimate quota. strategy:% workerQuota:%v", JsonToString(labelGroup.Strategy),
		//	JsonToString(workerQuota))
	}

	labelGroup.Scheduler.Put(ctx, workerQuota)
}

func (su *SchedulerUnit) findLabelByWorkerQuota(ctx context.Context, workerQuota *WorkerQuota) (*LabelGroup, error) {
	tagGroup, ok := su.TagGroup[workerQuota.TagKey]
	if !ok {
		su.log.WithContext(ctx).Errorf("get label group failed. tagKey:%v workerQuota:%v", workerQuota.TagKey, JsonToString(workerQuota))
		return nil, ErrWorkerNotFound
	}

	label, ok := tagGroup.LabelGroup.Get(workerQuota.LabelKey)
	if !ok {
		su.log.WithContext(ctx).Errorf("get label group failed. labelKey:%v workerQuota:%v", workerQuota.LabelKey, JsonToString(workerQuota))
		return nil, ErrWorkerNotFound
	}

	return label.(*LabelGroup), nil
}

func (su *SchedulerUnit) getLimitRatio(strategy *Strategy) int64 {
	ratio := DefaultLimitRatio
	if strategy.Config.LimitRatio != nil {
		if strategy.Config.Dominants != nil && len(strategy.Config.Dominants) > 0 {
			ratio = strategy.Config.LimitRatio[strategy.Config.Dominants[0]]
		}
	}

	return ratio
}

func (su *SchedulerUnit) makeScheduler(ctx context.Context, strategy *Strategy) Scheduler {
	var scheduler Scheduler
	switch strategy.Strategy {
	case StrategyCentral:
		scheduler = &CentralScheduler{
			BaseScheduler: BaseScheduler{
				WorkerQuotaMap: make(map[string]*treemap.Map),
				Strategy:       strategy,
				log:            su.log,
			},
		}
	case StrategyRandom:
		scheduler = &RandomScheduler{
			BaseScheduler: BaseScheduler{
				WorkerQuotaMap: make(map[string]*treemap.Map),
				Strategy:       strategy,
				log:            su.log,
			},
		}
	case StrategyAverage:
		scheduler = &AverageScheduler{
			BaseScheduler: BaseScheduler{
				WorkerQuotaMap: make(map[string]*treemap.Map),
				Strategy:       strategy,
				log:            su.log,
			},
		}
	case StrategyStepRandom:
		scheduler = &StepRandomScheduler{
			BaseScheduler: BaseScheduler{
				WorkerQuotaMap: make(map[string]*treemap.Map),
				Strategy:       strategy,
				log:            su.log,
			},
		}
	case StrategyStepAverage:
		scheduler = &StepAverageScheduler{
			BaseScheduler: BaseScheduler{
				WorkerQuotaMap: make(map[string]*treemap.Map),
				Strategy:       strategy,
				log:            su.log,
			},
		}
	case StrategyMix:
		scheduler = &MixScheduler{
			BaseScheduler: BaseScheduler{
				WorkerQuotaMap: make(map[string]*treemap.Map),
				Strategy:       strategy,
				log:            su.log,
			},
			allStrategies: su.Strategies,
		}
	case StrategyStepCentral:
		scheduler = &StepCentralScheduler{
			BaseScheduler: BaseScheduler{
				WorkerQuotaMap: make(map[string]*treemap.Map),
				Strategy:       strategy,
				log:            su.log,
			},
		}
	default:
		su.log.WithContext(ctx).Errorf("unknow type:%v", JsonToString(strategy))
	}
	return scheduler
}

func (su *SchedulerUnit) getLabelStrategy(workerQuota *WorkerQuota, defaultStrategy *Strategy) *Strategy {
	// 标签策略
	strategyKey := MakeStrategyId(&Strategy{
		Product:     workerQuota.Worker.Product,
		EngineModel: workerQuota.Worker.EngineModel,
		Tag:         workerQuota.Worker.Tag,
		Label:       workerQuota.Worker.Label,
	})
	strategy, ok := su.Strategies[strategyKey]
	if !ok {
		strategy = defaultStrategy
	}
	return strategy
}

func (su *SchedulerUnit) getGroupStrategy(workerQuota *WorkerQuota) *Strategy {
	// 默认分组策略
	strategyKey := MakeStrategyId(&Strategy{
		Product:     workerQuota.Worker.Product,
		EngineModel: workerQuota.Worker.EngineModel,
		Tag:         workerQuota.Worker.Tag,
	})
	strategy, ok := su.Strategies[strategyKey]
	if !ok {
		strategy = su.DefaultStrategy
	}
	return strategy
}

func (su *SchedulerUnit) calcAllocResource(ctx context.Context, resource *Resource) error {
	// 根据策略，计算Quota
	if workerQuota, ok := su.Workers[resource.WorkerId]; ok {
		if resource.WorkerVersion == workerQuota.Worker.OnlineVersion {
			workerQuota.RestQuota.Sub(resource.Quota)
			workerQuota.Worker.AllocQuota.Add(resource.Quota)
			workerQuota.Resources[resource.TaskId] = resource
			for k, v := range resource.Label {
				workerQuota.Label[k] = v
			}
			return nil
		} else {
			su.log.WithContext(ctx).Warnf("worker version changed.resource:%v worker:%v resource:%v, worker:%v", resource.WorkerVersion,
				workerQuota.Worker.OnlineVersion, JsonToString(resource), JsonToString(workerQuota.Worker))
		}
	} else {
		su.log.WithContext(ctx).Warnf("worker not exist.resource:%v", JsonToString(resource))
	}

	return ErrWorkerNotFound
}

func (su *SchedulerUnit) batchCalcAllocResource(ctx context.Context, resources map[string]*Resource) (map[string]*Resource, map[string]*QuotaSet) {
	start := time.Now().UnixMicro()
	defer func() {
		end := time.Now().UnixMicro()
		latency := end - start
		if latency > 10000 {
			su.log.WithContext(ctx).Warnf("unit:%v batchCalcAllocResource, latency time:%v", su.Role, end-start)
		}
	}()

	lostResource := make(map[string]*Resource)
	lockWorkersQuota := make(map[string]*QuotaSet)
	for _, resource := range resources {
		if err := su.calcAllocResource(ctx, resource); err != nil {
			lostResource[resource.TaskId] = resource
		} else {
			// 锁定时间内的资源，加到真实资源消耗里
			if time.Now().Unix()-resource.ModifiedTime.Unix() <= ScheduleLockSecond {
				if value, ok := lockWorkersQuota[resource.WorkerId]; ok {
					value.Add(resource.Quota)
				} else {
					quota := make(QuotaSet)
					quota.Copy(resource.Quota)
					lockWorkersQuota[resource.WorkerId] = &quota
					//quota.Add(resource.Quota)
				}
			}
		}
	}

	return lostResource, lockWorkersQuota
}

func (su *SchedulerUnit) batchCalcAllocResourceForSyncWorker(ctx context.Context, resources map[string]*Resource) (map[string]*Resource, map[string]*QuotaSet) {
	start := time.Now().UnixMicro()
	defer func() {
		end := time.Now().UnixMicro()
		latency := end - start
		if latency > 10000 {
			su.log.WithContext(ctx).Warnf("unit:%v batchCalcAllocResourceForSyncWorker, latency time:%v", su.Role, end-start)
		}
	}()

	lockWorkersQuota := make(map[string]*QuotaSet)
	for _, resource := range resources {
		// 移除原方法里的calcAllocResource 这里会导致每次都是累加
		// 锁定时间内的资源，加到真实资源消耗里
		// 压测一下，几十万并发的时候是否会慢
		if time.Now().Unix()-resource.ModifiedTime.Unix() <= ScheduleLockSecond {
			if value, ok := lockWorkersQuota[resource.WorkerId]; ok {
				value.Add(resource.Quota)
			} else {
				quota := make(QuotaSet)
				quota.Copy(resource.Quota)
				lockWorkersQuota[resource.WorkerId] = &quota
				//quota.Add(resource.Quota)
			}
		}
	}
	//// todo 删除
	//log.Infof("lockWorkersQuota xxxxxx:%v", JsonToString(lockWorkersQuota))
	return nil, lockWorkersQuota
}

func (su *SchedulerUnit) batchSyncAllocResource(ctx context.Context, workerQuotas map[string]*WorkerQuota) {
	for _, workerQuota := range workerQuotas {
		// 找到原来workerQuota位置，并移动树位置
		labelGroup, err := su.findLabelByWorkerQuota(ctx, workerQuota)
		if err != nil {
			su.log.WithContext(ctx).Warnf("sync alloc resource from db, find label by worker quota failed.resource:%v err:%v", JsonToString(workerQuota), err)
			continue
		}

		labelGroup.Scheduler.Remove(ctx, workerQuota)
		for _, resource := range workerQuota.Resources {
			if err := su.calcAllocResource(ctx, resource); err != nil {
				su.log.WithContext(ctx).Warnf("sync alloc resource from db, calcAllocResource failed.resource:%v err:%v", JsonToString(resource), err)
			} else {
				su.Resources[resource.TaskId] = resource
			}
		}
		labelGroup.Scheduler.Put(ctx, workerQuota)
	}
}

func (su *SchedulerUnit) buildKey(ctx context.Context, w *Worker) string {
	if w == nil {
		return "err"
	}

	if w.Label == nil {
		w.Label = make(map[string]string)
	}

	str := w.Product + "-" + w.EngineModel + "-" + w.Tag + "-" + JsonToString(w.Label)
	u := uuid.NewSHA1(uuid.Nil, []byte(str))
	return strings.ReplaceAll(u.String(), "-", "")
}

// WorkerMatcher 定义了worker匹配的接口
type WorkerMatcher interface {
	Match(cw *ChildWorker) bool
}

// ProductEngineTagMatcher 实现WorkerMatcher接口，用于匹配Product、EngineModel和Tag
type ProductEngineTagMatcher struct {
	Product     string
	EngineModel string
	Tag         string
	Label       map[string]string
}

// Match 检查ChildWorker是否与matcher匹配
func (m *ProductEngineTagMatcher) Match(cw *ChildWorker) bool {
	if cw.Product != m.Product ||
		cw.EngineModel != m.EngineModel ||
		cw.Tag != m.Tag {
		return false
	}

	// 判断label是否完全匹配
	if len(m.Label) != len(cw.Label) {
		return false
	}

	for k, v := range m.Label {
		if cwVal, ok := cw.Label[k]; !ok || cwVal != v {
			return false
		}
	}

	return true
}

// filterActiveWorkers 过滤出活跃的worker（心跳未超时）
func filterActiveWorkers(cws []*ChildWorker) []*ChildWorker {
	activeWorkers := make([]*ChildWorker, 0)
	for _, cw := range cws {
		// 如果childWorker心跳超时，则不进行水位计算
		if time.Now().Unix()-cw.HeartbeatTime.Unix() <= WorkerExpiredTime {
			activeWorkers = append(activeWorkers, cw)
		}
	}
	return activeWorkers
}

func (su *SchedulerUnit) Statistics(ctx context.Context, cws []*ChildWorker, statTime time.Time) []*WaterLevel {
	su.mutex.Lock()
	defer su.mutex.Unlock()

	// 先过滤出活跃的worker
	activeWorkers := filterActiveWorkers(cws)

	// 先按照预定义的TagGroup和LabelGroup进行水位计算
	var wls []*WaterLevel
	for _, group := range su.TagGroup {
		iterator := group.LabelGroup.Iterator()
		for iterator.Next() {
			labelGroup := iterator.Value().(*LabelGroup)

			// 将三元组和label满足的child传入
			cwsNew := make([]*ChildWorker, 0)
			matcher := &ProductEngineTagMatcher{
				Product:     group.Product,
				EngineModel: group.EngineModel,
				Tag:         group.Tag,
				Label:       labelGroup.Label,
			}

			for _, cw := range activeWorkers {
				if matcher.Match(cw) {
					cwsNew = append(cwsNew, cw)
				}
			}

			level := labelGroup.Scheduler.Statistics(ctx, cwsNew, su.Strategies)
			if level == nil {
				su.log.WithContext(ctx).Warnf("statistics failed.product:%v engineModel:%v tag:%v label:%v", group.Product, group.EngineModel, group.Tag, labelGroup.Label)
				continue
			}

			level.Product = group.Product
			level.EngineModel = group.EngineModel
			level.Tag = group.Tag
			level.Label = labelGroup.Label
			level.StatTime = statTime
			wls = append(wls, level)
		}
	}

	// 创建一个映射，用于快速查找已存在的水位信息
	wlMap := make(map[string]*WaterLevel)
	for _, wl := range wls {
		key := fmt.Sprintf("%s-%s-%s-%s", wl.Product, wl.EngineModel, wl.Tag, BuildLabelKey(wl.Label))
		wlMap[key] = wl
	}

	// 遍历所有活跃的子worker，处理未被预定义TagGroup和LabelGroup包含的情况
	for _, cw := range activeWorkers {
		// 构造当前worker对应的key
		key := fmt.Sprintf("%s-%s-%s-%s", cw.Product, cw.EngineModel, cw.Tag, BuildLabelKey(cw.Label))

		// 检查是否已存在对应的水位信息
		if _, exists := wlMap[key]; !exists {
			// 如果不存在，则收集所有具有相同属性的worker
			cwsNew := make([]*ChildWorker, 0)
			matcher := &ProductEngineTagMatcher{
				Product:     cw.Product,
				EngineModel: cw.EngineModel,
				Tag:         cw.Tag,
				Label:       cw.Label,
			}

			for _, cw2 := range activeWorkers {
				if matcher.Match(cw2) {
					cwsNew = append(cwsNew, cw2)
				}
			}

			// 创建临时的调度器进行统计
			//使用原父三元组的的调度策略
			tempScheduler := su.makeScheduler(ctx, su.DefaultStrategy)
			level := tempScheduler.Statistics(ctx, cwsNew, su.Strategies)
			if level == nil {
				su.log.WithContext(ctx).Warnf("statistics failed for new group. product:%v engineModel:%v tag:%v label:%v",
					cw.Product, cw.EngineModel, cw.Tag, cw.Label)
				continue
			}

			level.Product = cw.Product
			level.EngineModel = cw.EngineModel
			level.Tag = cw.Tag
			level.Label = cw.Label
			level.StatTime = statTime
			wls = append(wls, level)
			// 添加到映射中，避免重复创建
			wlKey := fmt.Sprintf("%s-%s-%s-%s", level.Product, level.EngineModel, level.Tag, BuildLabelKey(level.Label))
			wlMap[wlKey] = level
		}
	}

	return wls
}

func (su *SchedulerUnit) LogScheduleInfo() {
	su.mutex.Lock()
	defer su.mutex.Unlock()

	su.log.Infof("LogScheduleInfo start================================")
	str := fmt.Sprintf("schedule info:\n%v\n", JsonToString(su))
	fileName := fmt.Sprintf("schedule_info_%v_%v.txt", su.Role, time.Now().UnixMicro())
	err := os.WriteFile(fileName, []byte(str), 0644)
	if err != nil {
		log.Warnf("log schedule info failed.fileName:%v err:%v", fileName, err)
	}
	su.log.Infof("LogScheduleInfo end ================================")
}
