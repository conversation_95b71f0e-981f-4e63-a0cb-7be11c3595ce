package biz

import (
	"context"
	"errors"
	"github.com/emirpasic/gods/maps/treemap"
	"github.com/go-kratos/kratos/v2/log"
	"sort"
)

// MixScheduler 混合策略
type MixScheduler struct {
	BaseScheduler
	allStrategies map[string]*Strategy
}

// Alloc 混合策略 混跑三元组在resource的extend中
func (ms *MixScheduler) Alloc(ctx context.Context, resource *Resource) (*WorkerQuota, error) {
	// 先查找空闲worker  todo 还涉及到worker上版本的问题
	resourceType := ms.Strategy.Config.ScheduleResourceType
	dominant := ms.Strategy.Config.Dominants[0]
	quotaTreeMap, ok := ms.WorkerQuotaMap[resourceType]
	// 具体产品对应的策略
	tagKey := BuildTagKey(resource.Product, resource.EngineModel, resource.Tag)
	productStrategy, ok := ms.allStrategies[tagKey]
	if !ok {
		ms.log.WithContext(ctx).Errorf("central scheduler, taskId:%v unknown schedule resource type:%v not exist.", resource.TaskId, resourceType)
		return nil, ErrWorkerNotFound
	}
	keys := quotaTreeMap.Keys()

	start := ms.search(keys, resource.Quota[dominant])
	if start == len(keys) {
		// 走抢占逻辑
		if productStrategy.MixConfig.SeizeAble {
			return ms.seizeAlloc(ctx, resource, quotaTreeMap, productStrategy)
		}
		ms.log.WithContext(ctx).Errorf("central scheduler, taskId:%v not enough quota. type:%v not exist.", resource.TaskId, resourceType)
		return nil, ErrNotEnoughQuota
	}

	var backupWorkerQuota *WorkerQuota
	for i := start; i < len(keys); i++ {
		treeMapKey := keys[i]
		value, _ := quotaTreeMap.Get(treeMapKey)
		var workerQuota *WorkerQuota
		if workerQuotaMap, ok := value.(map[string]*WorkerQuota); ok {
			for _, workerQuota = range workerQuotaMap {
				if wq, err := ms.allocWorkerQuota(ctx, resource, workerQuota); err == nil {
					return wq, nil
				} else if errors.Is(err, ErrNotEnoughQuota) {
					break
				} else if errors.Is(err, ErrMatchLabelPreferredKey) && backupWorkerQuota == nil {
					backupWorkerQuota = workerQuota
				}
			}
		}
	}

	if backupWorkerQuota != nil {
		// backup
		ms.log.WithContext(ctx).Errorf("central scheduler, taskId:%v no affinity worker, use backup. %v",
			resource.TaskId, JsonToString(ms.WorkerQuotaMap))

		return ms.allocWorkerQuotaWithAffinity(ctx, resource, backupWorkerQuota, true)
	}

	ms.log.WithContext(ctx).Errorf("central scheduler, tasker:%v alloc resource failed. type:%v not exist.", JsonToString(resource),
		resourceType)
	return nil, ErrWorkerNotFound
}

//func needSeize(productStrategy *Strategy) bool {
//	return productStrategy.MixConfig.SeizeAble
//}

//func (ms *MixScheduler) seizeAlloc(ctx context.Context, resource *Resource, quotaTreeMap *treemap.Map) (*WorkerQuota, error) {
//	if true {
//		return ms.allocFirst(ctx, resource, quotaTreeMap)
//	} else {
//		// 抢占低级别优先
//		return ms.lowLevelResourceFirst(ctx, resource, quotaTreeMap)
//	}
//}

//func (ms *MixScheduler) allocFirst(ctx context.Context, resource *Resource, quotaTreeMap *treemap.Map) (*WorkerQuota, error) {
//	productLevelConfig := ms.Strategy.Config.ProductLevelConfig
//	// ProductLevelConfig 转成map[string]int
//	productLevelMap := make(map[string]int)
//	for _, config := range productLevelConfig {
//		productLevelMap[BuildTagKey(config.Product, config.EngineModel, config.Tag)] = config.Level
//	}
//	keys := quotaTreeMap.Keys()
//	length := len(keys)
//	for i := length - 1; i >= 0; i-- {
//		treeMapKey := keys[i]
//		value, _ := quotaTreeMap.Get(treeMapKey)
//		var workerQuota *WorkerQuota
//		if workerQuotaMap, ok := value.(map[string]*WorkerQuota); ok {
//			for _, workerQuota = range workerQuotaMap {
//				if wq, err := ms.seizeAllocWorkerQuotaIgnoreAffinity(ctx, resource, workerQuota, productLevelMap, true); err == nil {
//					return wq, nil
//				} else if errors.Is(err, ErrNotEnoughQuota) {
//					//break
//					continue
//				}
//			}
//		}
//	}
//	return nil, ErrNotEnoughQuota
//}

func (ms *MixScheduler) seizeAlloc(ctx context.Context, resource *Resource, quotaTreeMap *treemap.Map, productStrategy *Strategy) (*WorkerQuota, error) {
	//ProductLevelConfig 转成map[string]int
	productLevelMap := make(map[string]int)
	for key, stragy := range ms.allStrategies {
		if stragy.MixConfig != nil {
			productLevelMap[key] = stragy.MixConfig.ProductLevel
		}
	}
	keys := quotaTreeMap.Keys()
	length := len(keys)
	var backupWorkerQuota *WorkerQuota
	seizeLevelLowest := 100 // todo 100改为已知的最大级别
	for i := length - 1; i >= 0; i-- {
		treeMapKey := keys[i]
		value, _ := quotaTreeMap.Get(treeMapKey)
		var workerQuota *WorkerQuota
		if workerQuotaMap, ok := value.(map[string]*WorkerQuota); ok {
			for _, workerQuota = range workerQuotaMap {
				if wq, seizeLevel, err := ms.seizeAllocWorkerQuotaLowLevelResourceFirst(resource, workerQuota, productLevelMap, productStrategy); err == nil {
					if true { // todo 任务优先还是低级别抢占优先
						return ms.xxx(ctx, resource, wq)
					} else {
						if seizeLevel == 0 { // todo 0改为已知的最小级别
							return ms.xxx(ctx, resource, wq)
						} else {
							if seizeLevel < seizeLevelLowest {
								seizeLevelLowest = seizeLevel
								backupWorkerQuota = wq
							}
						}
					}
				} else if errors.Is(err, ErrNotEnoughQuota) {
					//break
					continue
				}
			}
		}
	}
	if backupWorkerQuota != nil {
		return ms.xxx(ctx, resource, backupWorkerQuota)
	}
	// 从待选中选择
	return nil, ErrNotEnoughQuota
}

func (ms *MixScheduler) xxx(ctx context.Context, resource *Resource, workerQuota *WorkerQuota) (*WorkerQuota, error) {
	ms.Remove(ctx, workerQuota)
	workerQuota.RestQuota.Sub(resource.Quota)
	workerQuota.Worker.AllocQuota.Add(resource.Quota)
	ms.Put(ctx, workerQuota)
	workerQuota.Resources[resource.TaskId] = resource
	workerQuota.Worker.UpdateQuotaVersion += 1
	workerQuota.Worker.TaskNum = int32(len(workerQuota.Resources))
	workerQuota.NeedSeize = true
	return workerQuota, nil
}

func (ms *MixScheduler) seizeAllocWorkerQuotaLowLevelResourceFirst(resource *Resource, workerQuota *WorkerQuota, productLevelMap map[string]int, productStrategy *Strategy) (*WorkerQuota, int, error) {
	workerResources := workerQuota.Resources
	resourceLevelMap := make(map[int][]*Resource)
	// 先从productLevelMap中找出优先级最低的level
	for _, workerResource := range workerResources {
		// 先找到优先级最低的resource  todo 这里相同三元组会做多次buildKey,需要优化性能
		workerResourceLevelKey := BuildTagKey(workerResource.Product, workerResource.EngineModel, workerResource.Tag)
		if resourceLevelMap[productLevelMap[workerResourceLevelKey]] == nil {
			resourceLevelMap[productLevelMap[workerResourceLevelKey]] = []*Resource{}
			continue
		}
		resourceLevelMap[productLevelMap[workerResourceLevelKey]] = append(resourceLevelMap[productLevelMap[workerResourceLevelKey]], workerResource)
	}
	currentLevel := productStrategy.MixConfig.ProductLevel

	// 开始抢占
	levelSort := make([]int, 0, len(resourceLevelMap))
	for level := range resourceLevelMap {
		levelSort = append(levelSort, level)
	}
	sort.Ints(levelSort)
	// 现在的resource quota先减去worker剩下的quota  // todo worker剩余quota不能全部用，考虑百分比预留
	resource.Quota.Sub(workerQuota.RestQuota)
	var seizeResource map[string]*Resource
	var seizeResourceQuota QuotaSet
	flag := false
	resultHighestLevel := 0
	for _, level := range levelSort {
		if currentLevel <= level {
			continue
		}
		for _, workerResource := range resourceLevelMap[level] {
			seizeResource[workerResource.TaskId] = workerResource
			// todo 这里不确定被强制的是real还是logic，理论上不能直接减，但目前没有resource级别的real消耗，默认抢占按照logic来
			seizeResourceQuota.Add(workerResource.Quota)
			if seizeResourceQuota.QuotaGEQ(resource.Quota) {
				flag = true
				resultHighestLevel = level
				break
			}
		}
		if flag {
			break
		}
	}
	if flag {
		workerQuota.SeizeResources = seizeResource

		//log.Infof("%v Scheduler alloc quota success. resource:%v worker:%v", bs.Strategy.Strategy,
		//	JsonToString(resource), JsonToString(workerQuota.Worker))
		return workerQuota, resultHighestLevel, nil
	} else {
		log.Warnf("%v Scheduler alloc quota failed, no enough dominant quota. resource:%v worker:%v", ms.Strategy.Strategy,
			JsonToString(resource), JsonToString(workerQuota.Worker))
		return nil, 0, ErrNotEnoughQuota
	}

}

//func (ms *MixScheduler) seizeAllocWorkerQuotaIgnoreAffinity(ctx context.Context, resource *Resource, workerQuota *WorkerQuota, productLevelMap map[string]int, ignoreAffinity bool) (*WorkerQuota, error) {
//	//err := ms.isLimited(resource, workerQuota)
//	//if err != nil {
//	//	return nil, err
//	//}
//	// 抢占，看优先级最低的resource占多少quota
//	// todo 根据策略中心配置，拿到能抢占的resource的级别
//	workerResources := workerQuota.Resources
//	resourceLevelMap := make(map[int][]*Resource)
//	// 先从productLevelMap中找出优先级最低的level
//	for _, workerResource := range workerResources {
//		// 先找到优先级最低的resource  todo 这里相同三元组会做多次buildKey,需要优化性能
//		workerResourceLevelKey := BuildTagKey(workerResource.Product, workerResource.EngineModel, workerResource.Tag)
//		if resourceLevelMap[productLevelMap[workerResourceLevelKey]] == nil {
//			resourceLevelMap[productLevelMap[workerResourceLevelKey]] = []*Resource{}
//			continue
//		}
//		resourceLevelMap[productLevelMap[workerResourceLevelKey]] = append(resourceLevelMap[productLevelMap[workerResourceLevelKey]], workerResource)
//	}
//	currentLevel := productLevelMap[BuildTagKey(resource.Product, resource.EngineModel, resource.Tag)]
//
//	// 开始抢占
//	levelSort := make([]int, 0, len(resourceLevelMap))
//	for level := range resourceLevelMap {
//		levelSort = append(levelSort, level)
//	}
//	sort.Ints(levelSort)
//	// 现在的resource quota先减去worker剩下的quota  // todo worker剩余quota不能全部用，考虑百分比预留
//	resource.Quota.Sub(workerQuota.RestQuota)
//	var seizeResource map[string]*Resource
//	var seizeResourceQuota QuotaSet
//	flag := false
//	for _, level := range levelSort {
//		if currentLevel <= level {
//			continue
//		}
//		for _, workerResource := range resourceLevelMap[level] {
//			seizeResource[workerResource.TaskId] = workerResource
//			// todo 这里不确定被强制的是real还是logic，理论上不能直接减，但目前没有resource级别的real消耗，默认抢占按照logic来
//			seizeResourceQuota.Add(workerResource.Quota)
//			if seizeResourceQuota.QuotaGEQ(resource.Quota) {
//				flag = true
//				break
//			}
//		}
//		if flag {
//			break
//		}
//	}
//
//	if flag {
//		//log.Debugf("Scheduler alloc quota success. resource:%v worker:%v", JsonToString(resource), JsonToString(workerQuota))
//		// todo 这里应该不用移除抢占的资源，而是增加新任务资源就行了，抢占的任务id返回给taskmanager会走freeResource
//		ms.Remove(ctx, workerQuota)
//		//workerQuota.RestQuota.Add(seizeResourceQuota)
//		workerQuota.RestQuota.Sub(resource.Quota)
//		workerQuota.Worker.AllocQuota.Add(resource.Quota)
//		//workerQuota.Worker.AllocQuota.Sub(seizeResourceQuota)
//		ms.Put(ctx, workerQuota)
//		workerQuota.Resources[resource.TaskId] = resource
//		//for _, seizeResourceId := range seizeResourceIds {
//		//	delete(workerQuota.Resources, seizeResourceId)
//		//}
//		workerQuota.Worker.UpdateQuotaVersion += 1
//		workerQuota.Worker.TaskNum = int32(len(workerQuota.Resources))
//
//		// 被抢占的任务id
//		workerQuota.SeizeResources = seizeResource
//
//		//log.Infof("%v Scheduler alloc quota success. resource:%v worker:%v", bs.Strategy.Strategy,
//		//	JsonToString(resource), JsonToString(workerQuota.Worker))
//		return workerQuota, nil
//	} else {
//		log.Warnf("%v Scheduler alloc quota failed, no enough dominant quota. resource:%v worker:%v", ms.Strategy.Strategy,
//			JsonToString(resource), JsonToString(workerQuota.Worker))
//		return nil, ErrNotEnoughQuota
//	}
//}
