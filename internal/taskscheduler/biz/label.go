package biz

/**
 * @Author: qinxin
 * @Date: 2025/8/1 17:11
 * @Desc:
 * @Version 1.0
 */

// LabelManager 用于处理标签相关操作
type LabelManager struct {
	mpsProducts map[string]bool
}

// NewLabelManager 创建 LabelManager 实例
func NewLabelManager(mpsProducts map[string]bool) *LabelManager {
	return &LabelManager{
		mpsProducts: mpsProducts,
	}
}

const (
	LabelResourceType string = "resourceType"
	//默认资源类型
	ResourceTypeCPU   string = "cpu"
	ResourceTypeGPUT4 string = "t4"

	CpuQuotaKey string = "cpu"
	GpuQuotaKey string = "gpu"

	ProductMediaai string = "mediaai"
	ProductMPS     string = "mps"
)

// 定义需要添加resourceType标签的产品列表
var MPSProducts = map[string]bool{
	ProductMPS:     true,
	ProductMediaai: true,
}

// AddResourceType 为产品添加 resourceType 标签
func (lm *LabelManager) AddResourceType(product string, label *map[string]string, quota QuotaSet) bool {
	isUpdateLabel := false

	// 如果product不在指定列表中，直接返回
	if !lm.mpsProducts[product] {
		return isUpdateLabel
	}

	// 初始化map如果为nil
	if *label == nil {
		*label = make(map[string]string)
	}

	// 如果label中包含resourceType字段，直接返回
	if _, ok := (*label)[LabelResourceType]; ok {
		return isUpdateLabel
	}

	// 如果quota为nil，使用默认的CPU类型
	if quota == nil {
		(*label)[LabelResourceType] = ResourceTypeCPU
	} else {
		// 如果label中不包含resourceType字段，则根据quota种类，添加resourceType字段
		if _, ok := quota[GpuQuotaKey]; ok {
			(*label)[LabelResourceType] = ResourceTypeGPUT4
		} else {
			(*label)[LabelResourceType] = ResourceTypeCPU
		}
	}

	isUpdateLabel = true
	return isUpdateLabel
}
