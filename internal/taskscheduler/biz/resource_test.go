package biz_test

import (
	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/mocks/mrepo"
	"mpp/internal/taskscheduler/testdata"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	v1 "proto.mpp/api/common/v1"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("ResourceManager", func() {
	var resourceManager *biz.ResourceManager
	var mResourceRepo *mrepo.MockResourceRepo
	var mTaskManagerApi *mrepo.MockTaskManagerApi

	BeforeEach(func() {
		mResourceRepo = mrepo.NewMockResourceRepo(ctl)
		mTaskManagerApi = mrepo.NewMockTaskManagerApi(ctl)
		resourceManager = biz.NewResourceManager(mResourceRepo, mTaskManagerApi, log.DefaultLogger)
	})

	It("ResourceManagerCreate", func() {
		resource := testdata.Resource()
		mResourceRepo.EXPECT().Save(ctx, gomock.Any()).Return(resource, nil)
		result, err := resourceManager.CreateResource(ctx, resource)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result).To(Equal(resource))

	})

	It("ResourceManagerGet", func() {
		resource := testdata.Resource()
		mResourceRepo.EXPECT().FindByTaskId(ctx, gomock.Any()).Return(resource, nil)
		result, err := resourceManager.GetResource(ctx, resource.TaskId)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result).To(Equal(resource))
	})

	It("ResourceManagerUpdate", func() {
		resource := testdata.Resource()
		mResourceRepo.EXPECT().UpdateQuota(ctx, gomock.Any()).Return(nil)
		err := resourceManager.UpdateResource(ctx, resource)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
	})

	It("ResourceManagerDelete", func() {
		resource := testdata.Resource()
		mResourceRepo.EXPECT().UpdateStatus(ctx, gomock.Any(), gomock.Any()).Return(nil)
		err := resourceManager.FreeResource(ctx, resource)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
	})

	It("ResourceManagerList", func() {
		resource := testdata.Resource()
		mResourceRepo.EXPECT().ListAll(ctx, gomock.Any()).Return([]*biz.Resource{resource}, nil)
		taskType := v1.TaskType(0)
		resources, err := resourceManager.ListResource(ctx, &taskType)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(resources).To(Equal([]*biz.Resource{resource}))
	})

	It("GetResourceByWorker", func() {
		resource := testdata.Resource()
		mResourceRepo.EXPECT().FindByWorker(ctx, gomock.Any()).Return([]*biz.Resource{resource}, nil)
		resources, err := resourceManager.GetResourceByWorker(ctx, &biz.Worker{WorkerId: resource.WorkerId})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(resources).To(Equal([]*biz.Resource{resource}))
	})

	It("ResourceManagerMigrateResource", func() {
		resource := testdata.Resource()
		mTaskManagerApi.EXPECT().MigrateTasks(ctx, gomock.Any()).Return(nil)
		err := resourceManager.MigrateResources(ctx, []string{resource.TaskId})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())

		err = resourceManager.MigrateResources(ctx, []string{})
		Ω(err).Should(HaveOccurred())
	})
})
