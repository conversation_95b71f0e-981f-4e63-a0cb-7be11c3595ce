package biz_test

import (
	"context"
	"mpp/internal/taskscheduler/biz"
	"testing"

	"github.com/emirpasic/gods/maps/treemap"
	"github.com/emirpasic/gods/utils"
	"github.com/stretchr/testify/assert"
)

func TestBaseScheduler(t *testing.T) {
	scheduler := biz.BaseScheduler{
		WorkerQuotaMap: map[string]*treemap.Map{},
	}
	scheduler.Printf(context.Background())
}

// 测试子worker quota合并到当前waterlevel中
func TestBaseScheduler_Statistics_IncludesChildWorkers(t *testing.T) {
	// 创建调度策略
	strategy := &biz.Strategy{
		Product:     "mpp",
		EngineModel: "transcode",
		Tag:         "default",
		Config: &biz.StrategyConfig{
			ScheduleResourceType: biz.ScheduleResourceTypeReal,
			Dominants:            []string{"cpu"},
			LimitRatio: map[string]int64{
				"cpu":                          80,
				biz.DefaultScheduleDominantAll: 80,
			},
		},
	}

	// 初始化 BaseScheduler
	bs := &biz.BaseScheduler{
		WorkerQuotaMap: map[string]*treemap.Map{
			"real": treemap.NewWith(utils.Int64Comparator),
		},
		Strategy: strategy,
		WaterLevel: &biz.WaterLevel{
			Label: map[string]string{"priority": "high"},
		},
	}

	// 添加一个本地 Worker
	worker := &biz.Worker{
		WorkerId:    "w001",
		Product:     "mpp",
		EngineModel: "transcode",
		Tag:         "default",
		Quota:       biz.QuotaSet{"cpu": 100, "mem": 512},
		RealQuota:   biz.QuotaSet{"cpu": 50, "mem": 256},
		AllocQuota:  biz.QuotaSet{"cpu": 50, "mem": 256},
		TaskNum:     2,
		Label:       map[string]string{"priority": "high"},
	}

	workerQuota := &biz.WorkerQuota{
		Worker: worker,
		Label:  worker.Label,
	}

	bs.WorkerQuotaMap["real"].Put(int64(100), map[string]*biz.WorkerQuota{
		"w001": workerQuota,
	})

	// 构造 cws ChildWorker 列表
	cws := []*biz.ChildWorker{
		{
			WorkerId:          "cw001",
			ParentProduct:     "mpp",
			ParentEngineModel: "transcode",
			ParentTag:         "default",
			Product:           "mpp",
			EngineModel:       "transcode",
			Tag:               "default",
			Label:             map[string]string{"priority": "high"},
			Quota:             biz.QuotaSet{"cpu": 200, "mem": 1024},
			RealQuota:         biz.QuotaSet{"cpu": 150, "mem": 768},
			AllocQuota:        biz.QuotaSet{"cpu": 150, "mem": 768},
			TaskNum:           3,
		},
		//{
		//	WorkerId:          "cw002",
		//	ParentProduct:     "rms", // 不匹配
		//	ParentEngineModel: "screenshot",
		//	ParentTag:         "default",
		//	Product:           "rms",
		//	EngineModel:       "screenshot",
		//	Tag:               "default",
		//	Quota:             biz.QuotaSet{"cpu": 100, "mem": 512},
		//	RealQuota:         biz.QuotaSet{"cpu": 50, "mem": 256},
		//	AllocQuota:        biz.QuotaSet{"cpu": 50, "mem": 256},
		//	TaskNum:           1,
		//},
	}

	// 调用 Statistics 方法
	waterLevel := bs.Statistics(context.Background(), cws, nil)

	assert.NotNil(t, waterLevel, "返回的 WaterLevel 不应为 nil")

	// 验证主资源统计
	assert.Equal(t, int64(300), waterLevel.TotalQuota["cpu"], "总 CPU 应为 100 + 200 = 300")
	assert.Equal(t, int64(200), waterLevel.RealQuota["cpu"], "真实 CPU 应为 50 + 150 = 200")
	assert.Equal(t, int32(5), waterLevel.TaskNum, "总任务数应为 2 (本地) + 3 (ChildWorker) = 5")
}

//
//func TestBaseScheduler_Statistics_SkipNonMatchingChildWorkers(t *testing.T) {
//	// 创建调度策略
//	strategy := &Strategy{
//		Product:     "mpp",
//		EngineModel: "transcode",
//		Tag:         "default",
//		Config: &StrategyConfig{
//			ScheduleResourceType: ScheduleResourceTypeReal,
//			Dominants:            []string{"cpu"},
//			LimitRatio: map[string]int64{
//				"cpu":             80,
//				DefaultScheduleDominantAll: 80,
//			},
//		},
//	}
//
//	bs := &BaseScheduler{
//		WorkerQuotaMap: map[string]*treemap.Map{
//			"real": treemap.NewWith(utils.Int64Comparator),
//		},
//		Strategy: strategy,
//		WaterLevel: &WaterLevel{
//			Label: map[string]string{"priority": "high"},
//		},
//		log: log.NewHelper(log.DefaultLogger),
//	}
//
//	// 添加一个本地 Worker
//	worker := &Worker{
//		WorkerId:    "w001",
//		Product:     "mpp",
//		EngineModel: "transcode",
//		Tag:         "default",
//		Quota:       QuotaSet{"cpu": 100, "mem": 512},
//		RealQuota:   QuotaSet{"cpu": 50, "mem": 256},
//		AllocQuota:  QuotaSet{"cpu": 50, "mem": 256},
//		TaskNum:     2,
//		Label:       map[string]string{"priority": "high"},
//	}
//
//	workerQuota := &WorkerQuota{
//		Worker: worker,
//		Label:  worker.Label,
//	}
//
//	bs.WorkerQuotaMap["real"].Put(100, map[string]*WorkerQuota{
//		"w001": workerQuota,
//	})
//
//	// 构造不匹配的 cws
//	cws := []*ChildWorker{
//		{
//			WorkerId:         "cw001",
//			ParentProduct:    "rms", // 不匹配
//			ParentEngineModel: "transcode",
//			ParentTag:        "default",
//			Product:          "rms",
//			EngineModel:      "transcode",
//			Tag:              "default",
//			Quota:            QuotaSet{"cpu": 200, "mem": 1024},
//			RealQuota:        QuotaSet{"cpu": 150, "mem": 768},
//			AllocQuota:       QuotaSet{"cpu": 150, "mem": 768},
//			TaskNum:          3,
//		},
//		{
//			WorkerId:         "cw002",
//			ParentProduct:    "mpp",
//			ParentEngineModel: "screenshot", // 不匹配
//			ParentTag:        "default",
//			Product:          "mpp",
//			EngineModel:      "screenshot",
//			Tag:              "default",
//			Quota:            QuotaSet{"cpu": 100, "mem": 512},
//			RealQuota:        QuotaSet{"cpu": 50, "mem": 256},
//			AllocQuota:       QuotaSet{"cpu": 50, "mem": 256},
//			TaskNum:          1,
//		},
//		{
//			WorkerId:         "cw003",
//			ParentProduct:    "mpp",
//			ParentEngineModel: "transcode",
//			ParentTag:        "beta", // Tag 不匹配
//			Product:          "mpp",
//			EngineModel:      "transcode",
//			Tag:              "beta",
//			Quota:            QuotaSet{"cpu": 100, "mem": 512},
//			RealQuota:        QuotaSet{"cpu": 50, "mem": 256},
//			AllocQuota:       QuotaSet{"cpu": 50, "mem": 256},
//			TaskNum:          1,
//		},
//		{
//			WorkerId:         "cw004",
//			ParentProduct:    "mpp",
//			ParentEngineModel: "transcode",
//			ParentTag:        "default",
//			Product:          "mpp",
//			EngineModel:      "transcode",
//			Tag:              "default",
//			Label:            map[string]string{"priority": "low"}, // Label 不匹配
//			Quota:            QuotaSet{"cpu": 100, "mem": 512},
//			RealQuota:        QuotaSet{"cpu": 50, "mem": 256},
//			AllocQuota:       QuotaSet{"cpu": 50, "mem": 256},
//			TaskNum:          1,
//		},
//	}
//
//	// 调用 Statistics 方法
//	waterLevel := bs.Statistics(context.Background(), cws, nil)
//
//	assert.NotNil(t, waterLevel, "返回的 WaterLevel 不应为 nil")
//
//	// 验证只有本地 Worker 被统计
//	assert.Equal(t, int64(100), waterLevel.TotalQuota["cpu"])
//	assert.Equal(t, int64(50), waterLevel.RealQuota["cpu"])
//	assert.Equal(t, int32(2), waterLevel.TaskNum)
//}
