package server

import (
	"mpp"
	"time"

	"mpp/pkg/pprof"

	mppMetrics "github.com/cinience/animus/components/metrics"
	"github.com/cinience/animus/contribs/transport/wrapper"
	"github.com/cinience/animus/health"
	"github.com/cinience/animus/registry"
	"mpp/internal/taskscheduler/conf"
	"mpp/internal/taskscheduler/service"
	"mpp/pkg/tracer/middleware/tracer"

	"github.com/go-kratos/kratos/v2/middleware/ratelimit"
	"github.com/prometheus/client_golang/prometheus"
	v1 "proto.mpp/api/taskscheduler/v1"

	swaggerUI "mpp/third_party/swagger/ui"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/middleware/validate"
	"github.com/go-kratos/kratos/v2/transport/http"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(c *conf.Server, worker *service.WorkerService, resource *service.ResourceService,
	schedule *service.ScheduleService, strategy *service.StrategyService, waterLevel *service.WaterLevelService,
	logger log.Logger, discovery registry.Discovery, swagger *conf.Swagger) *http.Server {

	metricsSeconds := prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Namespace: "server",
		Subsystem: "requests",
		Name:      "duration_sec",
		Help:      "server requests duratio(sec).",
		Buckets:   []float64{0.005, 0.01, 0.025, 0.05, 0.1, 0.250, 0.5, 1},
	}, []string{"kind", "operation"})

	metricsRequests := prometheus.NewCounterVec(prometheus.CounterOpts{
		Namespace: "client",
		Subsystem: "requests",
		Name:      "code_total",
		Help:      "The total number of processed requests",
	}, []string{"kind", "operation", "code", "reason"})

	mppMetrics.GetPromRegistry().MustRegister(metricsSeconds, metricsRequests)

	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			metadata.Server(),
			tracing.Server(),
			tracer.MPPServerTracer(),
			logging.Server(logger),
			//metrics.Server(
			//	metrics.WithSeconds(prom.NewHistogram(metricsSeconds)),
			//	metrics.WithRequests(prom.NewCounter(metricsRequests)),
			//),
			validate.Validator(),
			ratelimit.Server(),
		),
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	} else {
		opts = append(opts, http.Timeout(3*time.Second))
	}

	httpServer := http.NewServer(opts...)
	//// todo: 显式设置keepalive，看看有没变化
	//log.Infof("httpServer config mesaage .IdleTimeout: %v, keepalive:%v", httpServer.ReadTimeout, httpServer.WriteTimeout)
	//httpServer.IdleTimeout = 10 * time.Second
	//httpServer.SetKeepAlivesEnabled(true)
	//httpServer.WriteTimeout = 5 * time.Second
	//httpServer.ReadTimeout = 5 * time.Second
	if swagger.Enabled == "true" {
		//swaggerUI服务注册
		swaggerUI.RegisterSwaggerUIServerWithOption(
			httpServer,
			swaggerUI.WithTitle("Front Service"),
			swaggerUI.WithMemoryData(mpp.OpenApiData, "yaml"),
		)
	}

	// pprof API
	httpServer.HandlePrefix("/pprof", pprof.DefaultPProfService)

	// 增加默认健康检查API
	httpServer.HandlePrefix("/health", health.DefaultHealthService)

	r := httpServer.Route("/")

	wrapper.POST(r, "/worker/register", wrapper.WithStdHandler(worker.LiveWorkerRegister))
	wrapper.POST(r, "/worker/heartbeat", wrapper.WithStdHandler(worker.LiveWorkerHeartbeat))
	wrapper.POST(r, "/worker/unregister", wrapper.WithStdHandler(worker.LiveWorkerUnregister))
	wrapper.POST(r, "/worker/online", wrapper.WithStdHandler(worker.LiveWorkerOnline))
	wrapper.POST(r, "/worker/offline", wrapper.WithStdHandler(worker.LiveWorkerOffline))
	wrapper.POST(r, "/schedule/log", wrapper.WithStdHandler(schedule.LogSchedulerInfo))

	//压测运维接口
	//wrapper.POST(r, "/mock/worker/clean", wrapper.WithStdHandler(worker.MockWorkerClean))

	// 内部管理接口
	wrapper.GETAndPOST(r, "/animus/discovery", wrapper.WithStdHandler(discovery.Overhaul))

	v1.RegisterWorkerHTTPServer(httpServer, worker)
	v1.RegisterResourceHTTPServer(httpServer, resource)
	v1.RegisterScheduleHTTPServer(httpServer, schedule)
	v1.RegisterStrategyHTTPServer(httpServer, strategy)
	v1.RegisterWaterLevelHTTPServer(httpServer, waterLevel)
	return httpServer
}
