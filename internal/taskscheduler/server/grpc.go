package server

import (
	"mpp/internal/taskscheduler/conf"
	"mpp/internal/taskscheduler/service"
	"mpp/pkg/tracer/middleware/tracer"

	"github.com/go-kratos/kratos/v2/middleware/ratelimit"
	v1 "proto.mpp/api/taskscheduler/v1"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/middleware/validate"
	"github.com/go-kratos/kratos/v2/transport/grpc"
)

// NewGRPCServer new a gRPC server.
func NewGRPCServer(c *conf.Server, worker *service.WorkerService, resource *service.ResourceService,
	schedule *service.ScheduleService, strategy *service.StrategyService, waterLevel *service.WaterLevelService,
	logger log.Logger, swagger *conf.Swagger) *grpc.Server {

	var opts = []grpc.ServerOption{
		grpc.Middleware(
			recovery.Recovery(),
			metadata.Server(),
			tracing.Server(),
			tracer.MPPServerTracer(),
			logging.Server(logger),
			validate.Validator(),
			ratelimit.Server(),
		),
	}
	if c.Grpc.Network != "" {
		opts = append(opts, grpc.Network(c.Grpc.Network))
	}
	if c.Grpc.Addr != "" {
		opts = append(opts, grpc.Address(c.Grpc.Addr))
	}
	if c.Grpc.Timeout != nil {
		opts = append(opts, grpc.Timeout(c.Grpc.Timeout.AsDuration()))
	}
	srv := grpc.NewServer(opts...)
	v1.RegisterWorkerServer(srv, worker)
	v1.RegisterResourceServer(srv, resource)
	v1.RegisterScheduleServer(srv, schedule)
	v1.RegisterStrategyServer(srv, strategy)
	v1.RegisterWaterLevelServer(srv, waterLevel)
	return srv
}
