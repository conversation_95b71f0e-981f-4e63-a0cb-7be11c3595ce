package server

import (
	"mpp/internal/taskscheduler/conf"
	"mpp/pkg/utils"

	_ "github.com/cinience/animus/contribs/registry/nacos"
	_ "github.com/cinience/animus/contribs/registry/redis"
	"github.com/cinience/animus/registry"
	"github.com/google/wire"
)

// ProviderSet is server providers.
var ProviderSet = wire.NewSet(NewGRPCServer, NewHTTPServer, NewRegister, NewDiscover)

func NewRegister(conf *conf.Registry, liveTransCodeConfig *conf.LiveTransCodeConfig) []registry.Registrar {
	if conf == nil {
		return nil
	}
	rs := make([]registry.Registrar, 0)
	r, err := registry.GetRegistrarProvider(utils.PatchRegistryUrl(conf.Endpoint))
	if err != nil {
		panic(err)
	}
	if r == nil {
		return nil
	}
	rs = append(rs, r)
	if liveTransCodeConfig != nil && len(liveTransCodeConfig.UnitRegistry) > 0 && liveTransCodeConfig.UnitRegistry != conf.Endpoint {
		ur, err := registry.GetRegistrarProvider(utils.PatchRegistryUrl(liveTransCodeConfig.UnitRegistry))
		if err != nil {
			panic(err)
		}
		if ur == nil {
			return nil
		}
		rs = append(rs, ur)
	}
	return rs
}

func NewDiscover(conf *conf.Registry) registry.Discovery {
	if conf == nil {
		return nil
	}

	r, err := registry.GetDiscoveryProvider(utils.PatchRegistryUrl(conf.Endpoint))
	if err != nil {
		panic(err)
	}

	if r == nil {
		return nil
	}

	return registry.NewFailOverDiscovery(r)
}
