package service

import (
	"context"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/wrapperspb"
	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/mocks/mrepo"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
)

/**
 * @Author: qinxin
 * @Date: 2025/6/25 11:12
 * @Desc:
 * @Version 1.0
 */

func TestTimerSyncAllChildWorkers_WhenLeaderAndHeartbeatTimeout(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	now := time.Now()
	childWorker := &biz.ChildWorker{
		WorkerId:      "worker-1",
		HeartbeatTime: now.Add(-15 * time.Second), // 超时
	}

	cmWorkerRepo := mrepo.NewMockChildWorkerRepo(ctrl)
	childWorkerManager := biz.NewChildWorkerManager(nil, cmWorkerRepo, nil, log.DefaultLogger)
	svc := &ScheduleService{
		cwm: childWorkerManager,
	}
	cmWorkerRepo.EXPECT().ListOnlineWorker(gomock.Any()).Return([]*biz.ChildWorker{childWorker}, nil)
	cmWorkerRepo.EXPECT().BatchUpdateStatus(gomock.Any(), []string{"worker-1"}, biz.WorkerStatusOffline).Return(nil)

	svc.TimerSyncAllChildWorkers(context.Background())

	assert.True(t, true) // 如果没有 panic，说明成功调用了预期的方法
}

func TestAllocResourceParseExtend(t *testing.T) {
	stringV, _ := anypb.New(&wrapperspb.StringValue{Value: "pull-l3.test.com"})
	boolV, _ := anypb.New(&wrapperspb.BoolValue{Value: true})
	int64V, _ := anypb.New(&wrapperspb.Int64Value{Value: 1})
	int32V, _ := anypb.New(&wrapperspb.Int32Value{Value: 1})
	uint64V, _ := anypb.New(&wrapperspb.UInt64Value{Value: 1})
	uint32V, _ := anypb.New(&wrapperspb.UInt32Value{Value: 1})
	doubleV, _ := anypb.New(&wrapperspb.DoubleValue{Value: 1})
	floatV, _ := anypb.New(&wrapperspb.FloatValue{Value: 1})
	extend := map[string]*anypb.Any{
		"stringV": stringV,
		"boolV":   boolV,
		"int64V":  int64V,
		"int32V":  int32V,
		"uint64V": uint64V,
		"uint32V": uint32V,
		"doubleV": doubleV,
		"floatV":  floatV,
	}
	parse := parseExtend(extend)
	assert.Equal(t, parse, map[string]interface{}{
		"stringV": "pull-l3.test.com",
		"boolV":   true,
		"int64V":  int64(1),
		"int32V":  int32(1),
		"uint64V": uint64(1),
		"uint32V": uint32(1),
		"doubleV": float64(1),
		"floatV":  float32(1),
	})
}
