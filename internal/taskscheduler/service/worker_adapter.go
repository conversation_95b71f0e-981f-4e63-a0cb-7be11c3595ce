package service

import (
	"errors"
	"fmt"
	"io"
	"mpp/internal/taskscheduler/biz"
	"mpp/pkg/utils"
	stdhttp "net/http"
	"net/url"
	"strconv"

	error2 "github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	pb "proto.mpp/api/taskscheduler/v1"

	"github.com/go-kratos/kratos/v2/transport/http"
	jsoniter "github.com/json-iterator/go"
)

const (
	InternalError = "InternalError"
	Unavailable   = "Unavailable"
	Offline       = "Offline"
	NotFound      = "NotFound"

	LiveTaskManager = "LiveTaskManager"
	LiveTranscode   = "LiveTranscode"
	UnknownType     = "UnknownType"
	UnknownSupport  = "UnknownSupport"

	LiveTaskManagerKey = "id"
	LiveTranscodeKey   = "uuid"

	NetworkTotal    = "network.total"
	NetworkInternet = "network.internet"

	DefaultCpu         = 8000
	MaxNetworkTotal    = 200 * 1024 * 1024
	MaxNetworkInternet = 50 * 1024 * 1024
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

func (s *WorkerService) LiveWorkerRegister(w http.ResponseWriter, r *http.Request) {
	log.Infof("LiveWorkerRegister, url:%s, headers:%+v", r.URL.String(), r.Header)
	js, query, err := s.getBodyAndQuery(GetFunctionName(), r)
	if err != nil {
		s.writeResponse(w, err, nil)
		return
	}

	var result any
	switch s.getBizType(js, query) {
	case LiveTaskManager:
		result, err = s.LiveTaskManagerRegister(js, query, r)
	case LiveTranscode:
		result, err = s.LiveTranscodeRegister(js, query, r)
	default:
		result, err = nil, errors.New(UnknownType)
	}
	s.writeResponse(w, err, result)
}

func (s *WorkerService) LiveWorkerHeartbeat(w http.ResponseWriter, r *http.Request) {
	log.Infof("LiveWorkerHeartbeat, url:%s, headers:%+v", r.URL.String(), r.Header)
	js, query, err := s.getBodyAndQuery(GetFunctionName(), r)
	if err != nil {
		s.writeResponse(w, err, nil)
		return
	}

	var result any
	switch s.getBizType(js, query) {
	case LiveTaskManager:
		result, err = s.LiveTaskManagerHeartbeat(js, query, r)
	case LiveTranscode:
		result, err = s.LiveTranscodeHeartbeat(js, query, r)
	default:
		result, err = nil, errors.New(UnknownType)
	}

	s.writeResponse(w, err, result)
}

func (s *WorkerService) LiveWorkerUnregister(w http.ResponseWriter, r *http.Request) {
	log.Infof("LiveWorkerUnregister, url:%s, headers:%+v", r.URL.String(), r.Header)
	js, query, err := s.getBodyAndQuery(GetFunctionName(), r)
	if err != nil {
		s.writeResponse(w, err, nil)
		return
	}

	var result any
	switch s.getBizType(js, query) {
	case LiveTaskManager:
		result, err = s.LiveTaskManagerUnregister(js, query, r)
	case LiveTranscode:
		result, err = s.LiveTranscodeUnregister(js, query, r)
	default:
		result, err = nil, errors.New(UnknownType)
	}

	s.writeResponse(w, err, result)
}

func (s *WorkerService) LiveWorkerOnline(w http.ResponseWriter, r *http.Request) {
	log.Infof("LiveWorkerOnline, url:%s, headers:%+v", r.URL.String(), r.Header)
	js, query, err := s.getBodyAndQuery(GetFunctionName(), r)
	if err != nil {
		s.writeResponse(w, err, nil)
		return
	}

	var result any
	switch s.getBizType(js, query) {
	case LiveTaskManager:
		result, err = s.LiveTaskManagerOnline(js, query, r)
	case LiveTranscode:
		result, err = nil, errors.New(UnknownSupport)
	default:
		result, err = nil, errors.New(UnknownType)
	}

	s.writeResponse(w, err, result)
}

func (s *WorkerService) LiveWorkerOffline(w http.ResponseWriter, r *http.Request) {
	log.Infof("LiveWorkerOffline, url:%s, headers:%+v", r.URL.String(), r.Header)
	js, query, err := s.getBodyAndQuery(GetFunctionName(), r)
	if err != nil {
		s.writeResponse(w, err, nil)
		return
	}

	var result any

	switch s.getBizType(js, query) {
	case LiveTaskManager:
		result, err = s.LiveTaskManagerOffline(js, query, r)
	case LiveTranscode:
		result, err = nil, errors.New(UnknownSupport)
	default:
		result, err = nil, errors.New(UnknownType)
	}

	s.writeResponse(w, err, result)
}

func (s *WorkerService) getBodyAndQuery(f string, r *http.Request) (jsoniter.Any, url.Values, error) {
	query := r.URL.Query()
	var bodyStr string
	body, err := io.ReadAll(r.Body)
	if body != nil {
		bodyStr = string(body)
	}

	log.Infof("%s, url:%s, headers:%+v, body:%s, err:%v", f, r.URL.String(), r.Header, bodyStr, err)
	if !json.Valid(body) {
		return nil, query, error2.BadRequest("InvalidParam", "request body is not json")
	}

	js := json.Get(body)
	return js, query, err
}

func (s *WorkerService) writeResponse(w http.ResponseWriter, err error, result any) {
	if err != nil {
		e2 := error2.FromError(err)
		w.WriteHeader(int(e2.Code))
		result = s.makeResult(e2.Reason, e2.Message)
	} else {
		w.WriteHeader(stdhttp.StatusOK)
	}

	fmt.Fprint(w, biz.JsonToString(result))
}

func (s *WorkerService) getBizType(js jsoniter.Any, query url.Values) string {
	if getBodyOrQueryString(js, query, LiveTaskManagerKey) != "" {
		return LiveTaskManager
	} else if getBodyOrQueryString(js, query, LiveTranscodeKey) != "" {
		return LiveTranscode
	} else {
		return UnknownType
	}
}

func (s *WorkerService) makeResult(code string, message string) map[string]interface{} {
	result := make(map[string]string)
	result["code"] = code
	result["message"] = message

	resp := make(map[string]interface{})
	resp["result"] = result

	return resp
}

func (s *WorkerService) Result(reason string, message string, workerId string) map[string]interface{} {
	resp := s.makeResult(reason, message)
	resp["id"] = workerId

	return resp
}

func getBodyOrQueryInt64(js jsoniter.Any, query url.Values, key string) int64 {
	str := getBodyOrQueryString(js, query, key)
	num, err := strconv.ParseInt(str, 10, 64)
	if err != nil {
		return 0
	}

	return num
}

func getBodyOrQueryFloat(js jsoniter.Any, query url.Values, key string) float64 {
	str := getBodyOrQueryString(js, query, key)
	num, err := strconv.ParseFloat(str, 32)
	if err != nil {
		return 0
	}

	return num
}

func getBodyOrQueryString(js jsoniter.Any, query url.Values, key string) string {
	if query.Get(key) == "" {
		return js.Get(key).ToString()
	}
	return query.Get(key)
}

type LiveTaskManagerMetrics struct {
	CpuTotal            float64 `json:"sys.cpu.sy"`      // cpu总数
	CpuIdle             float64 `json:"sys.cpu.id"`      // cpu空闲比较，取值范围[0.0-100.0]
	NetworkUsedTotal    int64   `json:"io.outbound.all"` // 网络总带宽
	NetworkUsedInternal int64   `json:"io.outbound.vpc"` // 网络内网带宽
}

func getBodyOrQueryMetric(js jsoniter.Any, query url.Values, key string) *LiveTaskManagerMetrics {
	str := getBodyOrQueryString(js, query, key)
	var metrics LiveTaskManagerMetrics
	err := json.Unmarshal([]byte(str), &metrics)
	if err != nil {
		log.Errorf("get heartbeat body:%s metrics error, %v", str, err)
		return nil
	}

	return &metrics
}

func getBodyOrQueryBool(js jsoniter.Any, query url.Values, key string) bool {
	checkOnline := getBodyOrQueryString(js, query, key)
	b, err := strconv.ParseBool(checkOnline)
	if err != nil {
		log.Errorf("get heartbeat body:%s metrics error, %v", checkOnline, err)
		b = false
	}

	return b
}

/*
	{
	  "app": "liverecord",
	  "hostname": "liverecord011000139213.et2",
	  "endpoint": "oss-cn-shanghai.aliyuncs.com",
	  "ip": "************",
	  "id": "a1d47f56-514b-4213-b335-bcd869d315e5",
	  "env": "pre",
	  "checkOnline": false,
	}
*/
func (s *WorkerService) LiveTaskManagerRegister(js jsoniter.Any, query url.Values, r *http.Request) (any, error) {
	in := &pb.RegisterWorkerRequest{
		RequestId:   js.Get("trace").Get("requestId").ToString(),
		WorkerId:    getBodyOrQueryString(js, query, "id"),
		Ip:          getBodyOrQueryString(js, query, "ip"),
		Port:        80,
		Product:     "ols",
		EngineModel: getBodyOrQueryString(js, query, "app"),
		Tag:         "default",
		Label:       map[string]string{},
		Quota:       map[string]int64{"cpu": DefaultCpu, NetworkTotal: MaxNetworkTotal, NetworkInternet: MaxNetworkInternet},
		//Taint:       []*pb.Taint{{Key: "Online", Value: "True", Effect: "NoSchedule"}},
		PodName:     getBodyOrQueryString(js, query, "hostname"),
		NodeName:    getBodyOrQueryString(js, query, "hostname"),
		ConnectMode: []string{},
		Version:     "1.0.0",
		Metadata:    map[string]string{},
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	// 检查是否已经注册，已注册直接返回成功
	isCheckOnline := getBodyOrQueryBool(js, query, "checkOnline")
	if isCheckOnline {
		if _, err := s.wm.Get(newCtx, in.WorkerId, biz.WorkerStatusOnline); err == nil {
			return s.Result(SuccessResult.Reason, SuccessResult.Message, in.WorkerId), nil
		} else {
			err := error2.FromError(err)
			if err.Code != biz.ErrWorkerNotFound.Code {
				err.Reason = InternalError
				return nil, err
			}
		}
	}

	out, err := s.Register(newCtx, in)
	if err != nil {
		return nil, biz.ErrInternalError
	}

	return s.Result(out.Result.Reason, out.Result.Message, in.WorkerId), nil
}

/*
	{
	  "ip": "*************",
	  "quota": 6200,
	  "tag": "livetranscode_ffv5_pub",
	  "uuid": "d9bdc852-152a-3c85-956e-8e913bf9ace3"
	}
*/
func (s *WorkerService) LiveTranscodeRegister(js jsoniter.Any, query url.Values, r *http.Request) (any, error) {
	quota := s.liveTranscodeQuotaToMppQuota(getBodyOrQueryInt64(js, query, "quota"))
	in := &pb.RegisterWorkerRequest{
		RequestId:   js.Get("trace").Get("requestId").ToString(),
		WorkerId:    getBodyOrQueryString(js, query, "uuid"),
		Ip:          getBodyOrQueryString(js, query, "ip"),
		Port:        80,
		Product:     "ols",
		EngineModel: "livetranscode",
		Tag:         getBodyOrQueryString(js, query, "tag"),
		Label:       map[string]string{},
		Quota:       map[string]int64{"cpu": quota},
		ConnectMode: []string{},
		Version:     "1.0.0",
		Metadata:    map[string]string{},
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.Register(newCtx, in)
	if err != nil {
		return nil, biz.ErrInternalError
	}

	return s.Result(out.Result.Reason, out.Result.Message, in.WorkerId), nil
}

/*
	{
	  "currentQuota": 0,
	  "load1": 0.12,
	  "realQuota": 0,
	  "uuid": "*************-3a05-95a1-8a698bcefefa"
	}
*/
func (s *WorkerService) LiveTranscodeHeartbeat(js jsoniter.Any, query url.Values, r *http.Request) (any, error) {
	realQuota := getBodyOrQueryInt64(js, query, "realQuota")
	currentQuota := getBodyOrQueryInt64(js, query, "currentQuota")
	load := getBodyOrQueryFloat(js, query, "load1")
	quota := map[string]int64{
		"cpu":  s.liveTranscodeQuotaToMppQuota(realQuota),
		"cpu2": s.liveTranscodeQuotaToMppQuota(currentQuota),
	}

	// todo 没有处理realQuotaSet和protectSet
	in := &pb.HeartbeatWorkerRequest{
		RequestId: js.Get("trace").Get("requestId").ToString(),
		WorkerId:  getBodyOrQueryString(js, query, "uuid"),
		RealQuota: quota,
		Load:      float32(load),
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.Heartbeat(newCtx, in)
	if err != nil {
		err := error2.FromError(err)
		if err.Code == biz.ErrNoRegister.Code {
			err.Reason = Offline
		} else {
			err.Reason = InternalError
		}
		return nil, err
	}

	return s.Result(out.Result.Reason, out.Result.Message, in.WorkerId), nil
}

func (s *WorkerService) LiveTaskManagerOnline(js jsoniter.Any, query url.Values, r *http.Request) (any, error) {
	in := &pb.TaintWorkerRequest{
		RequestId: js.Get("trace").Get("requestId").ToString(),
		WorkerId:  getBodyOrQueryString(js, query, "id"),
		Taint:     []*pb.Taint{{Key: "Online", Value: "False", Effect: "Delete"}},
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.TaintWorker(newCtx, in)
	if err != nil {
		err := error2.FromError(err)
		if err.Code == biz.ErrWorkerNotFound.Code {
			err.Reason = NotFound
		} else {
			err.Reason = InternalError
		}
		return nil, err
	}

	return s.Result(out.Result.Reason, out.Result.Message, in.WorkerId), nil
}

func (s *WorkerService) LiveTaskManagerOffline(js jsoniter.Any, query url.Values, r *http.Request) (any, error) {
	in := &pb.TaintWorkerRequest{
		RequestId: js.Get("trace").Get("requestId").ToString(),
		WorkerId:  getBodyOrQueryString(js, query, "id"),
		Taint:     []*pb.Taint{{Key: "Online", Value: "False", Effect: "NoSchedule"}},
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.TaintWorker(newCtx, in)
	if err != nil {
		err := error2.FromError(err)
		if err.Code == biz.ErrWorkerNotFound.Code {
			err.Reason = NotFound
		} else {
			err.Reason = InternalError
		}
		return nil, err
	}

	return s.Result(out.Result.Reason, out.Result.Message, in.WorkerId), nil
}

/*
	{
	  "endpoint": "oss-cn-shanghai.aliyuncs.com",
	  "id": "a1d47f56-514b-4213-b335-bcd869d315e5",
	}
*/
func (s *WorkerService) LiveTaskManagerUnregister(js jsoniter.Any, query url.Values, r *http.Request) (any, error) {
	in := &pb.UnregisterWorkerRequest{
		RequestId: js.Get("trace").Get("requestId").ToString(),
		WorkerId:  getBodyOrQueryString(js, query, "id"),
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.UnregisterWorker(newCtx, in)
	if err != nil {
		err := error2.FromError(err)
		if err.Code == biz.ErrWorkerNotFound.Code {
			err.Reason = NotFound
		} else {
			err.Reason = InternalError
		}
		return nil, err
	}

	return s.Result(out.Result.Reason, out.Result.Message, in.WorkerId), nil
}

func (s *WorkerService) LiveTranscodeUnregister(js jsoniter.Any, query url.Values, r *http.Request) (any, error) {
	in := &pb.UnregisterWorkerRequest{
		RequestId: js.Get("trace").Get("requestId").ToString(),
		WorkerId:  getBodyOrQueryString(js, query, "uuid"),
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	out, err := s.UnregisterWorker(newCtx, in)
	if err != nil {
		return nil, err
	}

	return s.Result(out.Result.Reason, out.Result.Message, in.WorkerId), nil
}

/*
	{
	  "endpoint": "oss-cn-shanghai.aliyuncs.com",
	  "id": "a1d47f56-514b-4213-b335-bcd869d315e5",
	  "metrics": {
		"sys.cpu.sy": 1.0,
		"io.outbound.vpc": 0,
		"io.outbound.all": 0,
		"sys.cpu.id": 97.0
	  },
	  "busy": true
	}
*/
func (s *WorkerService) LiveTaskManagerHeartbeat(js jsoniter.Any, query url.Values, r *http.Request) (any, error) {
	metrics := getBodyOrQueryMetric(js, query, "metrics")

	quota := make(map[string]int64)
	quota[NetworkTotal] = metrics.NetworkUsedTotal
	if metrics.NetworkUsedInternal == -1 {
		quota[NetworkInternet] = 0
	} else {
		quota[NetworkInternet] = metrics.NetworkUsedTotal - metrics.NetworkUsedInternal
	}
	quota["cpu"] = (100 - int64(metrics.CpuIdle)) * DefaultCpu / 100

	in := &pb.HeartbeatWorkerRequest{
		RequestId: js.Get("trace").Get("requestId").ToString(),
		WorkerId:  getBodyOrQueryString(js, query, "id"),
		RealQuota: quota,
	}

	newCtx := utils.SetCustomRequestId(r.Context(), in.RequestId)
	in.RequestId = utils.GetRequestIdFromContext(newCtx)

	isBusy := getBodyOrQueryBool(js, query, "busy")
	if isBusy {
		in.Taint = []*pb.Taint{{Key: "HeartbeatBusy", Value: "True", Effect: "NoSchedule"}}
	} else {
		in.Taint = []*pb.Taint{{Key: "HeartbeatBusy", Value: "True", Effect: "Delete"}}
	}
	out, err := s.Heartbeat(newCtx, in)
	if err != nil {
		err := error2.FromError(err)
		if err.Code == biz.ErrNoRegister.Code {
			err.Reason = Unavailable
		} else {
			err.Reason = InternalError
		}
		return nil, err
	}

	return s.Result(out.Result.Reason, out.Result.Message, in.WorkerId), nil
}

func (s *WorkerService) liveTranscodeQuotaToMppQuota(quota int64) int64 {
	// 转码的Quota1核100，MPP的Quota1核1000
	quota *= 10
	return quota
}

//// 压测运维接口
//func (s *WorkerService) MockWorkerClean(w http.ResponseWriter, r *http.Request) {
//	ctx := r.Context()
//	workers, err := s.wm.ListWorker(ctx, &biz.Worker{
//		Product:     "ols",
//		EngineModel: "livetranscode",
//		Tag:         "livetranscode_mpp_pre",
//	}, []string{})
//	if err != nil {
//		s.log.WithContext(ctx).Errorf("MockWorkerClean ListWorker err:%v", err)
//		s.writeResponse(w, err, s.Result(InternalError, err.Error(), ""))
//	}
//	for _, worker := range workers {
//		err = s.CallMockWorkerClean(ctx, worker)
//		if err != nil {
//			s.log.WithContext(ctx).Errorf("MockWorkerClean CallMockWorkerClean err:%v", err)
//			s.writeResponse(w, err, s.Result(InternalError, err.Error(), ""))
//		}
//	}
//	s.writeResponse(w, nil, s.Result("Success", "success", ""))
//}
//
//// 压测运维接口
//func (s *WorkerService) CallMockWorkerClean(ctx context.Context, worker *biz.Worker) error {
//	connHTTP, err := InitHttpConnection(ctx, fmt.Sprintf("%v:%v", worker.Ip, worker.Port))
//	defer connHTTP.Close()
//	if err != nil {
//		s.log.WithContext(ctx).Errorf("MockWorkerClean worker[%s] init connection failed:%v ", worker.Ip, err)
//		return err
//	}
//
//	reply := make(map[string]interface{})
//	path := fmt.Sprintf("%s", "/cleanTask")
//	err = connHTTP.Invoke(ctx, "POST", path, nil, &reply)
//	if err != nil {
//		s.log.WithContext(ctx).Errorf("worker:%v clean task failed. resp:%v err:%v", worker.Ip, reply, err)
//		return err
//	}
//	return nil
//}
//
//// 压测运维接口
//func InitHttpConnection(ctx context.Context, addr string) (*http.Client, error) {
//	connHTTP, err := http.NewClient(
//		ctx,
//		http.WithEndpoint(fmt.Sprintf("http://%v", addr)),
//		http.WithBlock(),
//		//arms链路追踪埋点
//		http.WithMiddleware(
//			tracing.Client(),
//			tracer.MPPClientTracer(),
//			metadata.Client(),
//		),
//	)
//	if err != nil {
//		log.Errorf("init http:%v connection failed:%v ", addr, err)
//		return nil, err
//	}
//
//	return connHTTP, nil
//}
