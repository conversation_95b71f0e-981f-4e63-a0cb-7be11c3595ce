package service_test

import (
	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/conf"
	"mpp/internal/taskscheduler/mocks/mrepo"
	http2 "mpp/internal/taskscheduler/pkg/http"
	"mpp/internal/taskscheduler/service"
	"mpp/internal/taskscheduler/testdata"
	"net/http"
	"net/http/httptest"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("WorkerAdapterService", func() {
	var (
		workerManager      *biz.WorkerManager
		childworkerManager *biz.ChildWorkerManager
		mWorkerRepo        *mrepo.MockWorkerRepo
		workerService      *service.WorkerService
	)
	BeforeEach(func() {
		mWorkerRepo = mrepo.NewMockWorkerRepo(ctl)
		config := &conf.App{DelayScheduleUpdate: true}

		workerManager = biz.NewWorkerManager(config, mWorkerRepo, nil, nil, log.DefaultLogger)
		childworkerManager = biz.NewChildWorkerManager(config, nil, nil, log.DefaultLogger)
		workerService = service.NewWorkerService(workerManager, childworkerManager, log.DefaultLogger)
	})
	It("TesHlsRegister", func() {
		worker := testdata.Worker()
		req, _ := http.NewRequest("POST", "http://mpp.com/test?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(testdata.HlsRegisterReq()))
		rr := httptest.NewRecorder()

		//mWorkerRepo.EXPECT().FindByWorkerId(ctx, gomock.Any(), gomock.Any()).Return(worker, nil)
		mWorkerRepo.EXPECT().Save(gomock.Any(), gomock.Any()).Return(worker, nil)
		workerService.LiveWorkerRegister(rr, req)
		response := rr.Body.String()
		var result map[string]interface{}
		http2.JSONStringToStruct(response, &result)
		log.Infof("body:%s result:%+v", response, result)

		Ω(rr.Code).To(Equal(http.StatusOK))

		r2 := result["result"].(map[string]interface{})
		Ω(r2["code"]).To(Equal(service.SuccessResult.Reason))
		Ω(r2["message"]).To(Equal(service.SuccessResult.Message))
	})

	It("TesHlsHeartbeat", func() {
		req, _ := http.NewRequest("POST", "http://mpp.com/test?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(testdata.HlsHeartbeatReq()))
		rr := httptest.NewRecorder()

		//mWorkerRepo.EXPECT().FindByWorkerId(ctx, gomock.Any(), gomock.Any()).Return(worker, nil)
		mWorkerRepo.EXPECT().UpdateHeartbeat(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		workerService.LiveWorkerHeartbeat(rr, req)
		response := rr.Body.String()
		var result map[string]interface{}
		http2.JSONStringToStruct(response, &result)
		log.Infof("body:%s result:%+v", response, result)

		Ω(rr.Code).To(Equal(http.StatusOK))

		r2 := result["result"].(map[string]interface{})
		Ω(r2["code"]).To(Equal(service.SuccessResult.Reason))
		Ω(r2["message"]).To(Equal(service.SuccessResult.Message))
	})

	It("TesHlsUnregister", func() {
		req, _ := http.NewRequest("POST", "http://mpp.com/test?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(testdata.HlsOnlineReq()))
		rr := httptest.NewRecorder()

		//mWorkerRepo.EXPECT().FindByWorkerId(ctx, gomock.Any(), gomock.Any()).Return(worker, nil)
		mWorkerRepo.EXPECT().UpdateStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		workerService.LiveWorkerUnregister(rr, req)
		response := rr.Body.String()
		var result map[string]interface{}
		http2.JSONStringToStruct(response, &result)
		log.Infof("body:%s result:%+v", response, result)

		Ω(rr.Code).To(Equal(http.StatusOK))

		r2 := result["result"].(map[string]interface{})
		Ω(r2["code"]).To(Equal(service.SuccessResult.Reason))
		Ω(r2["message"]).To(Equal(service.SuccessResult.Message))
	})

	It("TesHlsOnline", func() {
		req, _ := http.NewRequest("POST", "http://mpp.com/test?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(testdata.HlsOfflineReq()))
		rr := httptest.NewRecorder()

		//mWorkerRepo.EXPECT().FindByWorkerId(ctx, gomock.Any(), gomock.Any()).Return(worker, nil)
		mWorkerRepo.EXPECT().UpdateTaint(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		workerService.LiveWorkerOnline(rr, req)
		response := rr.Body.String()
		var result map[string]interface{}
		http2.JSONStringToStruct(response, &result)
		log.Infof("body:%s result:%+v", response, result)

		Ω(rr.Code).To(Equal(http.StatusOK))

		r2 := result["result"].(map[string]interface{})
		Ω(r2["code"]).To(Equal(service.SuccessResult.Reason))
		Ω(r2["message"]).To(Equal(service.SuccessResult.Message))
	})

	It("TesHlsOffline", func() {
		req, _ := http.NewRequest("POST", "http://mpp.com/test?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(testdata.HlsUnregisterReq()))
		rr := httptest.NewRecorder()

		//mWorkerRepo.EXPECT().FindByWorkerId(ctx, gomock.Any(), gomock.Any()).Return(worker, nil)
		mWorkerRepo.EXPECT().UpdateTaint(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		workerService.LiveWorkerOffline(rr, req)
		response := rr.Body.String()
		var result map[string]interface{}
		http2.JSONStringToStruct(response, &result)
		log.Infof("body:%s result:%+v", response, result)

		Ω(rr.Code).To(Equal(http.StatusOK))

		r2 := result["result"].(map[string]interface{})
		Ω(r2["code"]).To(Equal(service.SuccessResult.Reason))
		Ω(r2["message"]).To(Equal(service.SuccessResult.Message))
	})
	It("TestTranscodeRegister", func() {
		worker := testdata.Worker()
		req, _ := http.NewRequest("POST", "http://mpp.com/test?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(testdata.LiveTranscodeRegisterReq()))
		rr := httptest.NewRecorder()

		//mWorkerRepo.EXPECT().FindByWorkerId(ctx, gomock.Any(), gomock.Any()).Return(worker, nil)
		mWorkerRepo.EXPECT().Save(gomock.Any(), gomock.Any()).Return(worker, nil)
		workerService.LiveWorkerRegister(rr, req)
		response := rr.Body.String()
		var result map[string]interface{}
		http2.JSONStringToStruct(response, &result)
		log.Infof("body:%s result:%+v", response, result)

		Ω(rr.Code).To(Equal(http.StatusOK))

		r2 := result["result"].(map[string]interface{})
		Ω(r2["code"]).To(Equal(service.SuccessResult.Reason))
		Ω(r2["message"]).To(Equal(service.SuccessResult.Message))
	})

	It("TesTranscodeHeartbeat", func() {
		req, _ := http.NewRequest("POST", "http://mpp.com/test?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(testdata.LiveTranscodeHeartbeatReq()))
		rr := httptest.NewRecorder()

		//mWorkerRepo.EXPECT().FindByWorkerId(ctx, gomock.Any(), gomock.Any()).Return(worker, nil)
		mWorkerRepo.EXPECT().UpdateHeartbeat(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		workerService.LiveWorkerHeartbeat(rr, req)
		response := rr.Body.String()
		var result map[string]interface{}
		http2.JSONStringToStruct(response, &result)
		log.Infof("body:%s result:%+v", response, result)

		Ω(rr.Code).To(Equal(http.StatusOK))

		r2 := result["result"].(map[string]interface{})
		Ω(r2["code"]).To(Equal(service.SuccessResult.Reason))
		Ω(r2["message"]).To(Equal(service.SuccessResult.Message))
	})

	It("TesTranscodeUnregister", func() {
		req, _ := http.NewRequest("POST", "http://mpp.com/test?requestId=00000001-3ccc-4128-85e5-4a172c1a5170", strings.NewReader(testdata.LiveTranscodeUnregisterReq()))
		rr := httptest.NewRecorder()

		//mWorkerRepo.EXPECT().FindByWorkerId(ctx, gomock.Any(), gomock.Any()).Return(worker, nil)
		mWorkerRepo.EXPECT().UpdateStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		workerService.LiveWorkerUnregister(rr, req)
		response := rr.Body.String()
		var result map[string]interface{}
		http2.JSONStringToStruct(response, &result)
		log.Infof("body:%s result:%+v", response, result)

		Ω(rr.Code).To(Equal(http.StatusOK))

		r2 := result["result"].(map[string]interface{})
		Ω(r2["code"]).To(Equal(service.SuccessResult.Reason))
		Ω(r2["message"]).To(Equal(service.SuccessResult.Message))
	})
})
