package service

import (
	"context"
	"fmt"
	"net/http"
	"runtime"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/wrapperspb"

	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/conf"

	"github.com/cinience/animus/utils"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"

	pb "proto.mpp/api/taskscheduler/v1"
)

const (
	SyncWorkerDuration     = 3 * time.Second
	SyncResourceDuration   = 30 * time.Second
	StatWaterLevelDuration = 10 * time.Second
)

type ScheduleService struct {
	pb.UnimplementedScheduleServer

	conf         *conf.App
	sm           *biz.ScheduleManager
	rm           *biz.ResourceManager
	wm           *biz.WorkerManager
	cwm          *biz.ChildWorkerManager
	cm           *biz.StrategyManager
	wlm          *biz.WaterLevelManager
	client       biz.TaskScheduleMasterApi
	labelManager *biz.LabelManager // 添加 LabelManager

	isLeader       bool
	isLeaderReady  bool
	lostResources  map[string]*biz.Resource
	updatedWorkers sync.Map
	log            *log.Helper
}

func NewScheduleService(c *conf.App, sm *biz.ScheduleManager, rm *biz.ResourceManager, wm *biz.WorkerManager, cwm *biz.ChildWorkerManager, cm *biz.StrategyManager,
	wlm *biz.WaterLevelManager, leader biz.LeaderRepo, client biz.TaskScheduleMasterApi, logger log.Logger) *ScheduleService {
	ss := &ScheduleService{
		conf:          c,
		sm:            sm,
		rm:            rm,
		wm:            wm,
		cwm:           cwm,
		cm:            cm,
		wlm:           wlm,
		isLeader:      false,
		isLeaderReady: false,
		client:        client,
		log:           log.NewHelper(logger),
		labelManager:  biz.NewLabelManager(biz.MPSProducts), // 初始化 LabelManager
	}

	ss.Init()
	ss.Start(leader)

	return ss
}

func (s *ScheduleService) logApiIn(ctx context.Context, f string, req interface{}) time.Time {
	s.log.WithContext(ctx).Log(log.LevelInfo,
		"event", "api.in",
		"func", f,
		"request", req)

	return time.Now()
}

func (s *ScheduleService) logApiOut(ctx context.Context, f string, start time.Time, req interface{}, resp interface{}, err error) {
	var code int32
	logLevel := log.LevelInfo
	if nil == err {
		code = 200
	} else {
		code = errors.FromError(err).Code
		logLevel = log.LevelWarn
	}
	s.log.WithContext(ctx).Log(logLevel,
		"event", "api.out",
		"latency", time.Since(start).Milliseconds(),
		"request", req,
		"response", resp,
		"func", f,
		"error", err,
		"code", code)
}

func (s *ScheduleService) getFunctionName() string {
	pc := make([]uintptr, 1)
	runtime.Callers(2, pc)
	f := runtime.FuncForPC(pc[0])
	return "Schedule" + f.Name()[strings.LastIndex(f.Name(), "."):]
}

func (s *ScheduleService) AllocMasterResource(ctx context.Context, req *pb.AllocMasterResourceRequest) (*pb.AllocMasterResourceReply, error) {
	var err error
	var resp *pb.AllocMasterResourceReply
	f := s.getFunctionName()

	start := s.logApiIn(ctx, f, req)
	defer func() {
		s.logApiOut(ctx, f, start, req, resp, err)
	}()

	if !s.isLeaderReady {
		s.log.WithContext(ctx).Warnf("master not ready:%v", req)
		return nil, biz.ErrMasterNotReady
	}

	var resource biz.Resource
	err = copier.Copy(&resource, req)
	//// press log
	//s.log.WithContext(ctx).Infof("copy resource alloc from master. taskId:%v cost time %v", req.TaskId, time.Since(start).Milliseconds())
	if err != nil {
		s.log.WithContext(ctx).Errorf("copy resource:%v failed. err:%v", biz.JsonToString(req), err)
		return nil, err
	}

	worker, err := s.sm.Alloc(ctx, biz.SchedulerMaster, &resource)
	if err != nil {
		return nil, err
	}
	//这里需要判断返回的worker是否为空,否则可能导致panic问题
	if worker == nil {
		s.log.WithContext(ctx).Errorf("alloc resource from master. taskId:%v failed. err:%v", req.TaskId, err)
		return nil, biz.ErrInternalError
	}

	//s.log.WithContext(ctx).Infof("alloc resource from master. taskId:%v cost time %v", req.TaskId, time.Since(start).Milliseconds())
	var replyWorker pb.ScheduleWorker
	err = copier.Copy(&replyWorker, worker)

	s.updatedWorkers.Store(worker.WorkerId, worker)

	if err != nil {
		s.log.WithContext(ctx).Errorf("copy worker:%v failed. err:%v", biz.JsonToString(worker), err)
		return nil, err
	}

	data := pb.AllocMasterResourceReply_Data{
		Worker: &replyWorker,
	}

	resp = &pb.AllocMasterResourceReply{Result: SuccessResult, Data: &data}
	return resp, nil
}

func (s *ScheduleService) FreeMasterResource(ctx context.Context, req *pb.FreeMasterResourceRequest) (*pb.FreeMasterResourceReply, error) {
	var err error
	var resp *pb.FreeMasterResourceReply
	f := s.getFunctionName()

	start := s.logApiIn(ctx, f, req)
	defer func() {
		s.logApiOut(ctx, f, start, req, resp, err)
	}()

	if !s.isLeaderReady {
		return nil, biz.ErrMasterNotReady
	}

	worker, err := s.sm.Free(ctx, biz.SchedulerMaster, req.TaskId)
	if nil != err {
		return nil, err
	}

	var w pb.ScheduleWorker
	err = copier.Copy(&w, worker)
	if err != nil {
		s.log.WithContext(ctx).Errorf("copy worker:%v failed. err:%v", biz.JsonToString(worker), err)
		return nil, err
	}

	s.updatedWorkers.Store(worker.WorkerId, worker)

	data := pb.FreeMasterResourceReply_Data{
		Worker: &w,
	}

	resp = &pb.FreeMasterResourceReply{Result: SuccessResult, Data: &data}
	return resp, nil
}

func (s *ScheduleService) GetMasterWorker(ctx context.Context, req *pb.GetMasterWorkerRequest) (*pb.GetMasterWorkerReply, error) {
	var err error
	var resp *pb.GetMasterWorkerReply
	f := s.getFunctionName()

	start := s.logApiIn(ctx, f, req)
	defer func() {
		s.logApiOut(ctx, f, start, req, resp, err)
	}()

	if !s.isLeaderReady {
		return nil, biz.ErrMasterNotReady
	}

	worker, ok := s.updatedWorkers.LoadAndDelete(req.WorkerId)
	if !ok {
		return &pb.GetMasterWorkerReply{Result: SuccessResult}, nil
	}

	var w pb.ScheduleWorker
	err = copier.Copy(&w, worker)
	if err != nil {
		s.log.WithContext(ctx).Errorf("copy worker:%v failed. err:%v", biz.JsonToString(worker), err)
		return nil, err
	}

	data := pb.GetMasterWorkerReply_Data{
		Worker: &w,
	}
	resp = &pb.GetMasterWorkerReply{Result: SuccessResult, Data: &data}
	return resp, nil
}

func (s *ScheduleService) AllocResource(ctx context.Context, req *pb.AllocResourceRequest) (*pb.AllocResourceReply, error) {
	var err error
	var resp *pb.AllocResourceReply
	f := s.getFunctionName()

	start := s.logApiIn(ctx, f, req)
	defer func() {
		s.logApiOut(ctx, f, start, req, resp, err)
	}()

	var resource biz.Resource
	err = copier.Copy(&resource, req)
	if err != nil {
		s.log.WithContext(ctx).Errorf("copy resource:%v failed. err:%v", biz.JsonToString(req), err)
		return nil, err
	}
	if resource.EngineModel == LiveTranscode {
		resource.Extend = parseExtend(req.Extend)
	}

	if r, err := s.rm.GetResource(ctx, resource.TaskId); err == nil {
		// 有可能已经掉线，内存中获取不到worker
		worker := s.sm.GetWorker(ctx, r.WorkerId)
		if worker == nil {
			if worker, err = s.wm.Get(ctx, r.WorkerId, biz.WorkerStatusOnline); err != nil {
				s.log.WithContext(ctx).Warnf("get worker from db failed. err:%v", biz.JsonToString(err))
				return nil, biz.ErrResourceLost
			}
		}
		s.setWorkerInfo(ctx, r, worker)
		return s.buildAllocReply(ctx, r)
	}

	var worker *biz.Worker
	// 发送请求到master分配资源
	worker, err = s.client.AllocMasterResource(ctx, req)
	//worker, err = s.sendAllocMasterResource(ctx, req)
	if err != nil {
		s.log.WithContext(ctx).Warnf("alloc from master failed. task:%v err:%v", req.TaskId, err)
		if errors.FromError(err).Reason == "NOT_ENOUGH_QUOTA" || errors.FromError(err).Reason == "WORKER_NOT_FOUND" {
			// master分配失败，无须重试
			return nil, err
		}
		// master分配其它失败，从本地申请
		s.log.WithContext(ctx).Infof("request from local. req:%v", req)

		// master分配失败，本地申请资源，用随机策略。
		worker, err = s.sm.Alloc(ctx, biz.ScheduleSlave, &resource)
		if err != nil {
			return nil, err
		}
		if worker == nil {
			s.log.WithContext(ctx).Warnf("alloc from local failed. resource:%v workerQuota:%v", biz.JsonToString(resource),
				biz.JsonToString(worker))
			return nil, biz.ErrWorkerNotFound
		}

		resource.IsMaster = biz.IsMasterFalse
		strategy := s.sm.GetSlaveStrategy(ctx, resource.WorkerId)
		// 获取配置
		ok, err := s.wm.UpdateChangedQuota(ctx, resource.WorkerId, resource.Quota, strategy.Config, biz.QuotaOptAdd)
		if err != nil || !ok {
			s.log.WithContext(ctx).Warnf("update changed quota failed. workerId:%v, resource:%v, config:%v, opt:%v, err:%v", resource.WorkerId, resource, strategy.Config, biz.QuotaOptAdd, err)
		}
		//}
	} else {
		resource.IsMaster = biz.IsMasterTrue
		if !s.conf.DelayScheduleUpdate {
			//更新workerQuota
			err = s.wm.UpdateQuota(ctx, worker, biz.QuotaOptAdd)
			if err != nil {
				s.log.WithContext(ctx).Warnf("update worker quota failed. err:%v", err)
			}
		}
	}

	s.setWorkerInfo(ctx, &resource, worker)
	_, err = s.rm.CreateResource(ctx, &resource)
	if err != nil {
		return nil, err
	}

	resp, err = s.buildAllocReply(ctx, &resource)
	return resp, err
}

func parseExtend(reqExtend map[string]*anypb.Any) map[string]interface{} {
	// 目前只有直播转码 转回domain
	extend := make(map[string]interface{})
	for k, v := range reqExtend {
		msg, err := v.UnmarshalNew()
		if err != nil {
			return extend
		}
		switch msg.ProtoReflect().Descriptor().Name() {
		case "StringValue":
			extend[k] = msg.(*wrapperspb.StringValue).GetValue()
		case "BoolValue":
			extend[k] = msg.(*wrapperspb.BoolValue).GetValue()
		case "Int64Value":
			extend[k] = msg.(*wrapperspb.Int64Value).GetValue()
		case "Int32Value":
			extend[k] = msg.(*wrapperspb.Int32Value).GetValue()
		case "UInt64Value":
			extend[k] = msg.(*wrapperspb.UInt64Value).GetValue()
		case "UInt32Value":
			extend[k] = msg.(*wrapperspb.UInt32Value).GetValue()
		case "DoubleValue":
			extend[k] = msg.(*wrapperspb.DoubleValue).GetValue()
		case "FloatValue":
			extend[k] = msg.(*wrapperspb.FloatValue).GetValue()
		}
	}
	return extend
}

func (s *ScheduleService) setWorkerInfo(ctx context.Context, resource *biz.Resource, worker *biz.Worker) {
	if worker != nil {
		resource.WorkerId = worker.WorkerId
		resource.WorkerIp = worker.Ip
		resource.WorkerVersion = worker.OnlineVersion
		resource.WorkerPort = worker.Port
		resource.WorkerConnectMode = worker.ConnectMode

		// set span
		utils.SpanWithLogEvent(ctx, "AllocResource.Worker", map[string]string{
			"worker.endpoint":  fmt.Sprintf("%s:%d", worker.Ip, worker.Port),
			"worker.heartbeat": worker.HeartbeatTime.Format(time.RFC3339Nano),
			"worker.connmode":  strings.Join(worker.ConnectMode, ","),
		})
	}
}

func (s *ScheduleService) FreeResource(ctx context.Context, req *pb.FreeResourceRequest) (*pb.FreeResourceReply, error) {
	var err error
	var resp *pb.FreeResourceReply
	f := s.getFunctionName()

	start := s.logApiIn(ctx, f, req)
	defer func() {
		s.logApiOut(ctx, f, start, req, resp, err)
	}()

	resource, err := s.rm.GetResource(ctx, req.TaskId)
	if err != nil {
		s.log.WithContext(ctx).Warnf("get resource:%v failed. err:%v", biz.JsonToString(req), err)
		return &pb.FreeResourceReply{Result: SuccessResult}, nil
	}

	// 发送到master释放资源
	worker, err := s.client.FreeMasterResource(ctx, req.TaskId)
	//worker, err := s.sendFreeMasterResource(ctx, req)
	if err != nil {
		s.log.WithContext(ctx).Warnf("free from master failed. taskId:%v err:%v", req.TaskId, err)

		strategy := s.sm.GetSlaveStrategy(ctx, resource.WorkerId)
		// 获取配置
		ok, err := s.wm.UpdateChangedQuota(ctx, resource.WorkerId, resource.Quota, strategy.Config, biz.QuotaOptSub)
		if err != nil || !ok {
			s.log.WithContext(ctx).Warnf("update changed quota failed. workerId:%v, resource:%v, config:%v, opt:%v", resource.WorkerId, resource, strategy.Config, biz.QuotaOptSub)
		}
	} else {
		if !s.conf.DelayScheduleUpdate {
			//更新workerQuota
			err := s.wm.UpdateQuota(ctx, worker, biz.QuotaOptSub)
			if err != nil {
				s.log.WithContext(ctx).Warnf("update worker quota failed. err:%v", err)
			}
		}
	}

	err = s.rm.FreeResource(ctx, resource)
	if err != nil {
		return nil, err
	}

	resp = &pb.FreeResourceReply{Result: SuccessResult}
	return resp, nil
}

func (s *ScheduleService) UpdateResource(ctx context.Context, req *pb.UpdateResourceRequest) (*pb.UpdateResourceReply, error) {
	var err error
	var resp *pb.UpdateResourceReply
	f := s.getFunctionName()

	start := s.logApiIn(ctx, f, req)
	defer func() {
		s.logApiOut(ctx, f, start, req, resp, err)
	}()

	var resource biz.Resource
	err = copier.Copy(&resource, req)
	if err != nil {
		s.log.WithContext(ctx).Warnf("copy resource:%v failed. err:%v", biz.JsonToString(req), err)
		return nil, err
	}

	err = s.rm.UpdateResource(ctx, &resource)
	if err != nil {
		s.log.WithContext(ctx).Warnf("update resource:%v failed. err:%v", biz.JsonToString(req), err)
		return nil, err
	}

	resp = &pb.UpdateResourceReply{Result: SuccessResult}
	return resp, nil
}

func (s *ScheduleService) LogSchedulerInfo(w http.ResponseWriter, r *http.Request) {
	s.sm.ScheduleUnits[biz.SchedulerMaster].LogScheduleInfo()
}

func (s *ScheduleService) buildAllocReply(ctx context.Context, r *biz.Resource) (*pb.AllocResourceReply, error) {
	var resource pb.AllocResourceReply_Resource
	err := copier.Copy(&resource, r)
	if err != nil {
		s.log.WithContext(ctx).Warnf("copy resource:%v failed. err:%v", biz.JsonToString(r), err)
		return nil, err
	}

	data := pb.AllocResourceReply_Data{
		Resource: &resource,
	}
	return &pb.AllocResourceReply{Result: SuccessResult, Data: &data}, nil
}

func (s *ScheduleService) OnLeaderEvent(event int8, leader string) {
	log.Infof("leader event: %v leader:%v\n", event, leader)
	switch event {
	case biz.EventLeaderSuccess:
		s.OnLeaderSuccess()
	case biz.EventLeaderLost:
		s.OnLeaderLost(leader)
	case biz.EventLeaderChanged:
		s.OnLeaderChanged(leader)
	default:
		// 其他
		log.Warnf("unknown event:%v leader:%v.", event, leader)
	}
}

func (s *ScheduleService) OnLeaderSuccess() {
	log.Infof("OnLeaderSuccess called.")
	s.isLeader = true
	endpoint := fmt.Sprintf("%v:%v", "127.0.0.1", 9000)
	s.LeaderInit(endpoint)
}

func (s *ScheduleService) OnLeaderLost(ip string) {
	log.Infof("OnLeaderLost called. new:%v", fmt.Sprintf("%v:%v", ip, 9000))
	s.isLeader = false
	s.isLeaderReady = false
	endpoint := fmt.Sprintf("%v:%v", ip, 9000)
	s.client.ResetClient(context.Background(), endpoint)
	//s.CloseTaskScheduleClient()
	//s.NewTaskScheduleClient()
}

func (s *ScheduleService) OnLeaderChanged(ip string) {
	log.Infof("OnLeaderChanged called. instance:%v", ip)
	s.isLeaderReady = false
	endpoint := fmt.Sprintf("%v:%v", ip, 9000)
	s.client.ResetClient(context.Background(), endpoint)
	//s.CloseTaskScheduleClient()
	//s.NewTaskScheduleClient()
}

func (s *ScheduleService) Init() {
	ctx := context.Background()
	workers, _ := s.wm.ListOnlineWorker(ctx)
	strategies, _ := s.cm.ListStrategy(ctx)
	s.sm.Init(ctx, workers, nil, strategies)
}

func (s *ScheduleService) LeaderInit(endpoint string) {
	log.Infof("LeaderInit start. endpoint:%v", endpoint)
	ctx := context.Background()
	workers, _ := s.wm.ListOnlineWorker(ctx)
	// 10w+数据，查询就会有慢sql，主节点每秒心跳，2s超时会导致无法成功启动
	resources, _ := s.rm.ListResource(ctx, nil)
	log.Infof("LeaderInit listResource end. resources num:%v", len(resources))
	strategies, _ := s.cm.ListStrategy(ctx)
	s.lostResources = s.sm.Init(ctx, workers, resources, strategies)
	if len(s.lostResources) > 0 {
		log.Warnf("init return lost resource, data:%v", biz.JsonToString(s.lostResources))
	}

	s.client.ResetClient(ctx, endpoint)
	//s.CloseTaskScheduleClient()
	//s.NewTaskScheduleClient()

	s.isLeaderReady = true
	log.Infof("LeaderInit end. endpoint:%v", endpoint)
}

func (s *ScheduleService) Start(leader biz.LeaderRepo) {
	log.Infof("timer task start.")

	s.timerSyncAllWorkers()
	s.timerSyncAllResource()
	s.timerWaterLevelStat()
	s.StartTimerSyncAllChildWorkers()
	leader.Watch(biz.LeaderName, s.OnLeaderEvent)
}

func (s *ScheduleService) timerWaterLevelStat() {
	ticker := time.NewTicker(StatWaterLevelDuration)
	log.Infof("stat water level start....")

	go func() {
		defer func() {
			if r := recover(); r != nil {
				// 发生异常时进行恢复和处理
				log.Fatalf("timerWaterLevelStat error:%v stack:%v", r, string(debug.Stack()))
			}
		}()

		ctx := context.Background()

		for {
			<-ticker.C
			if !s.isLeader {
				//log.Debugf("timerWaterLevelStat not leader:%v, skip.", s.client.GetEndpoint())
				continue
			}

			/*
				statTime := time.Now()
				workers, err := s.wm.ListWorker(context.Background(), nil, nil)
				if err != nil {
					log.Warnf("stat water level fail. err:%v", err)
					continue
				}

				waterLevels := s.wlm.StatWaterLevel(context.Background(), workers, s.sm.GetStrategies(ctx), statTime)
				waterLevelMap := make(map[string]map[string]*biz.WaterLevel)
				for _, level := range waterLevels {
					tagKey := biz.BuildTagKey(level.Product, level.EngineModel, level.Tag)
					labelKey := biz.BuildLabelKey(level.Label)
					waterLevelMap[tagKey] = make(map[string]*biz.WaterLevel)
					waterLevelMap[tagKey][labelKey] = level
				}
			*/

			childWorkers, err := s.cwm.ListWorker(context.Background(), nil, nil)
			if err != nil {
				log.Warnf("stat water level fail. err:%v", err)
				continue
			}

			waterLevels := s.sm.ScheduleUnits[biz.SchedulerMaster].Statistics(ctx, childWorkers, time.Now())
			for _, level := range waterLevels {
				err := s.wlm.CreateWaterLevel(ctx, level)
				if err != nil {
					s.log.WithContext(ctx).Errorf("stat water level failed. err:%v", err)
				}
				s.wlm.LogWaterLevel(ctx, level, level.Used, level.Total)
			}
			//水位上报TC流控层
			err = s.wlm.NotifyWaterLevel(ctx, waterLevels)
			if err != nil {
				log.Errorf("stat water level failed. err:%v", err)
			}

		}
	}()
}

func (s *ScheduleService) timerSyncAllResource() {
	ticker := time.NewTicker(SyncResourceDuration)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				// 发生异常时进行恢复和处理
				log.Fatalf("timerSyncAllResource error:%v stack:%v", r, string(debug.Stack()))
			}
		}()

		ctx := context.Background()
		for {
			<-ticker.C
			if !s.isLeader {
				//log.Debugf("timerSyncAllResource not leader:%v, skip.", s.client.GetEndpoint())
				continue
			}

			err := s.syncResource(ctx)
			if err != nil {
				log.Warnf("timerSyncAllResource sync resource failed:%v", err)
			}
		}
	}()
}

func (s *ScheduleService) syncResource(ctx context.Context) error {
	startTime := time.Now().UnixMilli()
	// todo 定时高频查询几十万数据，会存在慢sql
	resources, err := s.rm.ListResource(ctx, nil)
	if err != nil {
		log.Errorf("timerSyncAllResource list resource failed:%v", err)
		return err
	}
	dbTime := time.Now().UnixMilli()
	err = s.sm.SyncResources(ctx, biz.SchedulerMaster, resources)
	if err != nil {
		log.Errorf("timerSyncAllResource sync resource failed:%v", err)
	}
	endTime := time.Now().UnixMilli()
	if endTime-startTime > 20 {
		log.Warnf("timer to load resource, total time:%v db time:%v memory time:%v, resource size:%v",
			endTime-startTime, dbTime-startTime, endTime-dbTime, len(resources))
	}

	return nil
}

func (s *ScheduleService) timerSyncAllWorkers() {
	ticker := time.NewTicker(SyncWorkerDuration)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				// 发生异常时进行恢复和处理
				log.Fatalf("timerSyncAllWorkers error:%v stack:%v", r, string(debug.Stack()))
			}
		}()

		ctx := context.Background()
		for {
			<-ticker.C
			workers, err := s.wm.ListOnlineWorker(ctx)
			if err != nil {
				log.Warnf("timerSyncAllWorkers list worker failed:%v", err)
				continue
			}

			strategies, err := s.cm.ListStrategy(ctx)
			if err != nil {
				log.Warnf("timerSyncAllWorkers list strategy failed:%v", err)
				continue
			}

			var onlineWorkers []*biz.Worker
			var heartbeatTimeoutWorkers []*biz.Worker
			for _, worker := range workers {
				//新增resourceType同步
				s.labelManager.AddResourceType(worker.Product, &worker.Label, worker.Quota)

				// 当前时间小于等于心跳超时时间，则认为是在线的
				if time.Now().Unix() <= worker.HeartbeatTime.Unix()+biz.WorkerExpiredTime {
					//log.Debugf("worker heartbeat online, now:%v heartbeat:%v expired:%v worker:%v",
					//	time.Now().Unix(), worker.HeartbeatTime.Unix(), biz.WorkerExpiredTime, biz.JsonToString(worker))
					onlineWorkers = append(onlineWorkers, worker)
				} else {
					//log.Warnf("worker heartbeat timeout, now:%v heartbeat:%v expired:%v worker:%v",
					//	time.Now().Unix(), worker.HeartbeatTime.Unix(), biz.WorkerExpiredTime, biz.JsonToString(worker))
					s.wm.LogWorker(ctx, worker, "timer.sync", "Worker.HeartbeatTimeout", true, "")
					heartbeatTimeoutWorkers = append(heartbeatTimeoutWorkers, worker)
				}
			}

			var offlineWorkers []*biz.Worker
			if len(heartbeatTimeoutWorkers) > 0 {
				//log.Warnf("heartbeatTimeoutWorkers, len:%v data:%v", len(heartbeatTimeoutWorkers), biz.JsonToString(heartbeatTimeoutWorkers))
				var onlines []*biz.Worker
				onlines, offlineWorkers = s.wm.MultiCheckWorkerStatus(context.Background(), heartbeatTimeoutWorkers)
				onlineWorkers = append(onlineWorkers, onlines...)
			}

			versionChangedWorkers, memOfflineWorkers := s.sm.SyncWorkers(ctx, onlineWorkers, strategies)

			if !s.isLeader {
				log.Infof("timerSyncAllWorkers not leader:%v, skip.", s.client.GetEndpoint())
				continue
			}

			if len(versionChangedWorkers) > 0 {
				log.Warnf("versionChangedWorkers, len:%v data:%v", len(versionChangedWorkers), biz.JsonToString(versionChangedWorkers))
				for _, worker := range versionChangedWorkers {
					offlineWorkers = append(offlineWorkers, worker)
				}
			}

			if len(memOfflineWorkers) > 0 {
				log.Warnf("memory offline workers, len:%v data:%v", len(memOfflineWorkers), biz.JsonToString(memOfflineWorkers))
				offlineWorkers = append(offlineWorkers, memOfflineWorkers...)
			}

			// 增加有任务但没有资源消耗的worker报警

			// 启动任务迁移，并更新worker状态
			if len(offlineWorkers) > 0 || len(s.lostResources) > 0 {
				workerMap := make(map[string]*biz.Worker)
				for _, worker := range offlineWorkers {
					workerMap[worker.WorkerId] = worker
				}

				offlineWorkers = offlineWorkers[:0]
				var workerIds []string
				for _, worker := range workerMap {
					offlineWorkers = append(offlineWorkers, worker)
					workerIds = append(workerIds, worker.WorkerId)
				}

				// todo 转码场景需要适配
				if s.migrateTask(offlineWorkers, ctx) {
					if s.lostResources != nil {
						s.lostResources = nil
					}

					for _, worker := range offlineWorkers {
						s.wm.LogWorker(ctx, worker, "timer.sync", "Worker.Offline", true, "")
					}

					var offlineWorkerIds []string
					for _, workerId := range workerIds {
						if _, ok := versionChangedWorkers[workerId]; !ok {
							offlineWorkerIds = append(offlineWorkerIds, workerId)
						}
					}

					err := s.wm.BatchUpdateWorkerStatus(ctx, offlineWorkerIds)
					if err != nil {
						log.Warnf("batch update worker status failed:%v", err)
					}

					//批量更新子worker状态
					//todo qinxin 确认非混跑worker 调用该方法更新返回结果
					err = s.cwm.BatchUpdateWorkerStatusByParentWorkerIds(ctx, offlineWorkerIds)
					if err != nil {
						log.Warnf("batch update child worker status by parent ids failed:%v", err)
					}
				}
			}
		}
	}()
}

func (s *ScheduleService) migrateTask(offlineWorkers []*biz.Worker, ctx context.Context) bool {
	log.Warnf("offlineWorkers, len:%v data:%v", len(offlineWorkers), biz.JsonToString(offlineWorkers))

	var resources []*biz.Resource
	for _, worker := range offlineWorkers {
		r, err := s.rm.GetResourceByWorker(ctx, worker)
		if err != nil {
			log.Errorf("timerSyncAllWorkers get resource by worker ids failed:%v", err)
			return false
		}
		resources = append(resources, r...)
	}

	if s.lostResources != nil {
		for _, resource := range s.lostResources {
			log.Warnf("init return lost resource, data:%v", biz.JsonToString(resource))
			resources = append(resources, resource)
		}
	}

	if len(resources) > 0 {
		var resourceIds []string
		for _, resource := range resources {
			resourceIds = append(resourceIds, resource.TaskId)
		}

		lastIds := RemoveDuplicates(resourceIds)

		// todo  增加了分批的操作 by bianmu
		batchSize := 10
		var j int
		for i := 0; i < len(lastIds); i += batchSize {
			j += batchSize
			if j > len(lastIds) {
				j = len(lastIds)
			}
			// do what do you want to with the sub-slice, here just printing the sub-slices
			inIds := lastIds[i:j]
			log.Warnf("lost resources, len:%v data:%v", len(inIds), inIds)
			err := s.rm.MigrateResources(ctx, inIds)
			if err != nil {
				return false
			}
		}
	}

	return true
}

func (s *ScheduleService) StartTimerSyncAllChildWorkers() {
	ticker := time.NewTicker(SyncWorkerDuration)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Fatalf("timerSyncAllChildWorkers error:%v stack:%v", r, string(debug.Stack()))
			}
		}()
		for {
			<-ticker.C
			if !s.isLeader {
				continue // 只有 Leader 才能执行同步
			}
			log.Infof("start timerSyncAllChildWorkers.")
			s.TimerSyncAllChildWorkers(context.Background())
		}
	}()
}
func (s *ScheduleService) TimerSyncAllChildWorkers(ctx context.Context) {
	// 1. 获取在线的子 worker 列表
	childWorkers, err := s.cwm.ListOnlineWorker(ctx)
	if err != nil {
		log.Warnf("timerSyncAllChildWorkers list child worker failed:%v", err)
		return
	}

	var onlineChildWorkers []*biz.ChildWorker
	var heartbeatTimeoutChildWorkers []*biz.ChildWorker

	// 2. 根据心跳判断是否超时
	for _, childWorker := range childWorkers {
		if time.Now().Unix() <= childWorker.HeartbeatTime.Unix()+biz.WorkerExpiredTime {
			onlineChildWorkers = append(onlineChildWorkers, childWorker)
		} else {
			s.cwm.LogWorker(ctx, childWorker, "timer.sync", "ChildWorker.HeartbeatTimeout", true, "")
			heartbeatTimeoutChildWorkers = append(heartbeatTimeoutChildWorkers, childWorker)
		}
	}

	// 3. 处理心跳超时的 workers
	if len(heartbeatTimeoutChildWorkers) > 0 {
		childWorkerMap := make(map[string]*biz.ChildWorker)
		for _, cw := range heartbeatTimeoutChildWorkers {
			childWorkerMap[cw.WorkerId] = cw
		}

		var childWorkerIds []string
		for _, cw := range childWorkerMap {
			childWorkerIds = append(childWorkerIds, cw.WorkerId)
		}

		for _, cw := range heartbeatTimeoutChildWorkers {
			s.cwm.LogWorker(ctx, cw, "timer.sync", "ChildWorker.Offline", true, "")
		}

		err := s.cwm.BatchUpdateWorkerStatus(ctx, childWorkerIds)
		if err != nil {
			log.Warnf("batch update child worker status failed:%v", err)
		}
	}
}
