package service_test

import (
	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/mocks/mrepo"
	"mpp/internal/taskscheduler/service"
	"mpp/internal/taskscheduler/testdata"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	pb "proto.mpp/api/taskscheduler/v1"
)

var _ = Describe("StrategyService", func() {
	var (
		strategyManager *biz.StrategyManager
		mStrategyRepo   *mrepo.MockStrategyRepo
		strategyService *service.StrategyService
	)
	BeforeEach(func() {
		mStrategyRepo = mrepo.NewMockStrategyRepo(ctl)
		strategyManager = biz.NewStrategyManager(mStrategyRepo, log.DefaultLogger)
		strategyService = service.NewStrategyService(strategyManager, log.DefaultLogger)
	})
	It("TestCreate", func() {
		strategy := testdata.Strategy()
		mStrategyRepo.EXPECT().Save(ctx, gomock.Any()).Return(strategy, nil)
		result, err := strategyService.CreateStrategy(ctx, testdata.BizToCreateRequest(strategy))
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result.Result.Code).To(Equal(service.SuccessResult.Code))
		Ω(result.Result.Reason).To(Equal(service.SuccessResult.Reason))
		Ω(result.Result.Message).To(Equal(service.SuccessResult.Message))
	})

	It("TestGet", func() {
		strategy := testdata.Strategy()

		mStrategyRepo.EXPECT().FindByStrategyId(ctx, gomock.Any()).Return(strategy, nil)
		req := &pb.GetStrategyRequest{
			RequestId:  "123",
			StrategyId: strategy.StrategyId,
		}
		result, err := strategyService.GetStrategy(ctx, req)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result.Result.Code).To(Equal(service.SuccessResult.Code))
		Ω(result.Result.Reason).To(Equal(service.SuccessResult.Reason))
		Ω(result.Result.Message).To(Equal(service.SuccessResult.Message))
		Ω(result.GetData().GetStrategy().StrategyId).To(Equal(strategy.StrategyId))
	})

	It("TestUpdate", func() {
		strategy := testdata.Strategy()

		mStrategyRepo.EXPECT().Update(ctx, gomock.Any()).Return(nil)
		req := &pb.UpdateStrategyRequest{
			RequestId:  "123",
			StrategyId: strategy.StrategyId,
			Strategy:   strategy.Strategy,
			Callback:   strategy.Callback,
		}
		result, err := strategyService.UpdateStrategy(ctx, req)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result.Result.Code).To(Equal(service.SuccessResult.Code))
		Ω(result.Result.Reason).To(Equal(service.SuccessResult.Reason))
		Ω(result.Result.Message).To(Equal(service.SuccessResult.Message))
	})

	It("TestDelete", func() {
		strategy := testdata.Strategy()

		mStrategyRepo.EXPECT().UpdateStatus(ctx, gomock.Any(), gomock.Any()).Return(nil)
		req := &pb.DeleteStrategyRequest{
			RequestId:  "123",
			StrategyId: strategy.StrategyId,
		}
		result, err := strategyService.DeleteStrategy(ctx, req)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result.Result.Code).To(Equal(service.SuccessResult.Code))
		Ω(result.Result.Reason).To(Equal(service.SuccessResult.Reason))
		Ω(result.Result.Message).To(Equal(service.SuccessResult.Message))
	})

	It("TestList", func() {
		strategy := testdata.Strategy()

		mStrategyRepo.EXPECT().ListAll(ctx).Return([]*biz.Strategy{strategy}, nil)
		req := &pb.ListStrategyRequest{
			RequestId: "123",
		}
		result, err := strategyService.ListStrategy(ctx, req)
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result.Result.Code).To(Equal(service.SuccessResult.Code))
		Ω(result.Result.Reason).To(Equal(service.SuccessResult.Reason))
		Ω(result.Result.Message).To(Equal(service.SuccessResult.Message))
	})
})
