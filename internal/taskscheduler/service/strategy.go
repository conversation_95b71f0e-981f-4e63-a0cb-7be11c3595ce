package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"

	"mpp/internal/taskscheduler/biz"

	pb "proto.mpp/api/taskscheduler/v1"
)

type StrategyService struct {
	pb.UnimplementedStrategyServer

	sm  *biz.StrategyManager
	log *log.Helper
}

func NewStrategyService(sm *biz.StrategyManager, logger log.Logger) *StrategyService {
	return &StrategyService{sm: sm, log: log.NewHelper(logger)}
}

func (s *StrategyService) CreateStrategy(ctx context.Context, req *pb.CreateStrategyRequest) (*pb.CreateStrategyReply, error) {
	s.log.WithContext(ctx).Infof("create scheduler. req:%v", req)
	var strategy biz.Strategy
	err := copier.Copy(&strategy, req)
	if err != nil {
		s.log.WithContext(ctx).Errorf("copy strategy error. err:%v", err)
		return nil, err
	}

	if _, ok := s.sm.Strategies[strategy.Strategy]; !ok {
		return nil, biz.ErrStrategyNotFound
	}

	s.log.WithContext(ctx).Infof("strategy:%v", strategy)
	result, err := s.sm.CreateStrategy(ctx, &strategy)
	if err != nil {
		return nil, err
	}

	return &pb.CreateStrategyReply{Result: SuccessResult, Data: &pb.CreateStrategyReply_Data{StrategyId: result.StrategyId}}, nil
}
func (s *StrategyService) UpdateStrategy(ctx context.Context, req *pb.UpdateStrategyRequest) (*pb.UpdateStrategyReply, error) {
	s.log.WithContext(ctx).Infof("schedule update . req:%v", req)
	var strategy biz.Strategy
	err := copier.Copy(&strategy, req)
	if err != nil {
		s.log.WithContext(ctx).Errorf("copy strategy error. err:%v", err)
		return nil, err
	}
	err = s.sm.UpdateStrategy(ctx, &strategy)
	if err != nil {
		return nil, err
	}
	return &pb.UpdateStrategyReply{Result: SuccessResult}, nil
}
func (s *StrategyService) DeleteStrategy(ctx context.Context, req *pb.DeleteStrategyRequest) (*pb.DeleteStrategyReply, error) {
	s.log.WithContext(ctx).Infof("delete scheduler . req:%v", req)
	var strategy biz.Strategy
	err := copier.Copy(&strategy, req)
	if err != nil {
		s.log.WithContext(ctx).Errorf("copy strategy error. err:%v", err)
		return nil, err
	}

	err = s.sm.DeleteStrategy(ctx, &strategy)
	if err != nil {
		return nil, err
	}
	return &pb.DeleteStrategyReply{Result: SuccessResult}, nil
}
func (s *StrategyService) GetStrategy(ctx context.Context, req *pb.GetStrategyRequest) (*pb.GetStrategyReply, error) {
	s.log.WithContext(ctx).Infof("scheduler get. req:%v", req)
	var strategy biz.Strategy
	err := copier.Copy(&strategy, req)
	if err != nil {
		s.log.WithContext(ctx).Errorf("copy strategy error. err:%v", err)
		return nil, err
	}
	w, err := s.sm.GetStrategy(ctx, &strategy)
	if err != nil {
		return nil, err
	}

	var strategyInfo pb.ScheduleStrategy
	err = copier.Copy(&strategyInfo, w)
	if err != nil {
		s.log.WithContext(ctx).Errorf("copy strategy error. err:%v", err)
		return nil, err
	}

	data := pb.GetStrategyReply_Data{
		Strategy: &strategyInfo,
	}
	return &pb.GetStrategyReply{Result: SuccessResult, Data: &data}, nil
}
func (s *StrategyService) ListStrategy(ctx context.Context, req *pb.ListStrategyRequest) (*pb.ListStrategyReply, error) {
	list, err := s.sm.ListStrategy(ctx)
	if err != nil {
		return nil, err
	}

	var strategies []*pb.ScheduleStrategy
	for i := 0; i < len(list); i++ {
		var strategy pb.ScheduleStrategy
		err := copier.Copy(&strategy, list[i])
		if err != nil {
			s.log.WithContext(ctx).Errorf("copy strategy error. err:%v", err)
			return nil, err
		}
		strategies = append(strategies, &strategy)
	}

	data := pb.ListStrategyReply_Data{
		Strategies: strategies,
	}
	return &pb.ListStrategyReply{Result: SuccessResult, Data: &data}, nil
}
