package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"

	"mpp/internal/taskscheduler/biz"

	pb "proto.mpp/api/taskscheduler/v1"
)

type ResourceService struct {
	pb.UnimplementedResourceServer

	rm  *biz.ResourceManager
	log *log.Helper
}

func NewResourceService(rm *biz.ResourceManager, logger log.Logger) *ResourceService {
	rs := ResourceService{rm: rm, log: log.NewHelper(logger)}

	return &rs
}

func (s *ResourceService) GetResource(ctx context.Context, req *pb.GetResourceRequest) (*pb.GetResourceReply, error) {
	s.log.WithContext(ctx).Infof("resource get. req:%v", req)
	r, err := s.rm.GetResource(ctx, req.TaskId)
	if err != nil {
		return nil, err
	}

	var resourceInfo pb.ResourceDetail
	err = copier.Copy(&resourceInfo, r)
	if err != nil {
		s.log.WithContext(ctx).Warnf("get resource copy reply failed. reply:%v err:%v", r, err)
		return nil, err
	}

	data := pb.GetResourceReply_Data{
		Resource: &resourceInfo,
	}
	return &pb.GetResourceReply{Result: SuccessResult, Data: &data}, nil
}

func (s *ResourceService) ListResource(ctx context.Context, req *pb.ListResourceRequest) (*pb.ListResourceReply, error) {
	s.log.WithContext(ctx).Infof("list resource. req:%v", req)
	rs, err := s.rm.ListResource(ctx, &req.TaskType)
	if err != nil {
		return nil, err
	}

	var resources []*pb.ResourceDetail
	for i := 0; i < len(rs); i++ {
		var resourceInfo pb.ResourceDetail
		err := copier.Copy(&resourceInfo, rs[i])
		if err != nil {
			s.log.WithContext(ctx).Warnf("get resource copy reply failed. reply:%v err:%v", rs[i], err)
			return nil, err
		}
		resources = append(resources, &resourceInfo)
	}

	data := pb.ListResourceReply_Data{
		Resources: resources,
	}
	return &pb.ListResourceReply{Result: SuccessResult, Data: &data}, nil
}

func (s *ResourceService) ListLostResource(ctx context.Context, req *pb.ListResourceRequest) (*pb.ListResourceReply, error) {
	s.log.WithContext(ctx).Infof("list lost resource. req:%v", req)
	rs, err := s.rm.ListLostResource(ctx, &req.TaskType)
	if err != nil {
		return nil, err
	}

	var resources []*pb.ResourceDetail
	for i := 0; i < len(rs); i++ {
		var resourceInfo pb.ResourceDetail
		err := copier.Copy(&resourceInfo, rs[i])
		if err != nil {
			s.log.WithContext(ctx).Warnf("get lost resource copy reply failed. reply:%v err:%v", rs[i], err)
			return nil, err
		}
		resources = append(resources, &resourceInfo)
	}

	data := pb.ListResourceReply_Data{
		Resources: resources,
	}
	return &pb.ListResourceReply{Result: SuccessResult, Data: &data}, nil
}
