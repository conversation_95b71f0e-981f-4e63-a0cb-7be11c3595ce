package service_test

import (
	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/conf"
	"mpp/internal/taskscheduler/mocks/mrepo"
	"mpp/internal/taskscheduler/service"
	"mpp/internal/taskscheduler/testdata"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	pb "proto.mpp/api/taskscheduler/v1"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("WaterLevelService", func() {
	var (
		waterLevelManager *biz.WaterLevelManager
		workerManager     *biz.WorkerManager
		mWaterLevelRepo   *mrepo.MockWaterLevelRepo
		mWorkerRepo       *mrepo.MockWorkerRepo
		waterlevelService *service.WaterLevelService
	)
	BeforeEach(func() {
		mWaterLevelRepo = mrepo.NewMockWaterLevelRepo(ctl)
		mWorkerRepo = mrepo.NewMockWorkerRepo(ctl)
		config := &conf.App{DelayScheduleUpdate: true}

		waterLevelManager = biz.NewWaterLevelManager(mWaterLevelRepo, nil, log.DefaultLogger)
		workerManager = biz.NewWorkerManager(config, mWorkerRepo, nil, nil, log.DefaultLogger)
		waterlevelService = service.NewWaterLevelService(waterLevelManager, workerManager, log.DefaultLogger)
	})
	It("TestGet", func() {
		waterlevel := testdata.WaterLevel()
		mWaterLevelRepo.EXPECT().FindLatest(ctx, gomock.Any()).Return(waterlevel, nil)
		result, err := waterlevelService.GetWaterLevel(ctx, &pb.GetWaterLevelRequest{Product: "test", EngineModel: "test", Tag: "test"})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result.Result.Code).To(Equal(service.SuccessResult.Code))
		Ω(result.Result.Reason).To(Equal(service.SuccessResult.Reason))
		Ω(result.Result.Message).To(Equal(service.SuccessResult.Message))
	})

	It("TestList", func() {
		waterlevel := testdata.WaterLevel()
		mWaterLevelRepo.EXPECT().ListAll(ctx, gomock.Any(), gomock.Any()).Return([]*biz.WaterLevel{waterlevel}, nil)
		result, err := waterlevelService.ListWaterLevel(ctx, &pb.ListWaterLevelRequest{Product: "test", EngineModel: "test", Tag: "test"})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result.Result.Code).To(Equal(service.SuccessResult.Code))
		Ω(result.Result.Reason).To(Equal(service.SuccessResult.Reason))
		Ω(result.Result.Message).To(Equal(service.SuccessResult.Message))
	})
})
