package service_test

import (
	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/conf"
	"mpp/internal/taskscheduler/mocks/mrepo"
	"mpp/internal/taskscheduler/service"
	"mpp/internal/taskscheduler/testdata"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	pb "proto.mpp/api/taskscheduler/v1"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

func TestWorkerService_RegisterChildWorker(t *testing.T) {
	ctl = gomock.NewController(t)
	//mock
	defer ctl.Finish()
	cleaner = ctl.Finish
	mWorkerRepo := mrepo.NewMockWorkerRepo(ctl)
	cmWorkerRepo := mrepo.NewMockChildWorkerRepo(ctl)

	config := &conf.App{DelayScheduleUpdate: true}

	workerManager := biz.NewWorkerManager(config, mWorkerRepo, nil, nil, log.DefaultLogger)
	childWorkerManager := biz.NewChildWorkerManager(config, cmWorkerRepo, nil, log.DefaultLogger)
	workerService := service.NewWorkerService(workerManager, childWorkerManager, log.DefaultLogger)

	worker := testdata.Worker()
	childWorker := testdata.TestChildWorker()
	mWorkerRepo.EXPECT().FindByWorkerId(ctx, gomock.Any(), gomock.Any()).Return(worker, nil)
	cmWorkerRepo.EXPECT().Save(ctx, gomock.Any()).Return(childWorker, nil)

	//注册成功
	_, err := workerService.RegisterChildWorker(ctx, &pb.RegisterChildWorkerRequest{
		ParentWorkerId: "123",
		ChildWorkerList: []*pb.ChildWorkerRegister{
			{
				WorkerId:    "1234",
				Ip:          "127.0.0.1",
				Port:        123,
				PodName:     "test",
				NodeName:    "test",
				EngineModel: "test",
				Product:     "test",
				Tag:         "test",
				ConnectMode: []string{"test"},
				Quota: map[string]int64{
					"cpu": 8000,
				},
			},
		},
	})
	assert.Nil(t, err)
	//下线
	cmWorkerRepo.EXPECT().BatchUpdateStatus(ctx, gomock.Any(), gomock.Any()).Return(nil)
	_, err = workerService.UnregisterChildWorker(ctx, &pb.UnregisterChildWorkerRequest{
		ParentWorkerId: "123",
		ChildWorkerIds: []string{"1234"},
	})
	assert.Nil(t, err)

}

var _ = Describe("WorkerService", func() {
	var (
		workerManager      *biz.WorkerManager
		childWorkerManager *biz.ChildWorkerManager
		mWorkerRepo        *mrepo.MockWorkerRepo
		workerService      *service.WorkerService
	)
	BeforeEach(func() {
		mWorkerRepo = mrepo.NewMockWorkerRepo(ctl)
		config := &conf.App{DelayScheduleUpdate: true}

		workerManager = biz.NewWorkerManager(config, mWorkerRepo, nil, nil, log.DefaultLogger)
		childWorkerManager = biz.NewChildWorkerManager(config, nil, nil, log.DefaultLogger)
		workerService = service.NewWorkerService(workerManager, childWorkerManager, log.DefaultLogger)
	})
	It("TestGet", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().FindByWorkerId(ctx, gomock.Any(), gomock.Any()).Return(worker, nil)
		result, err := workerService.GetWorker(ctx, &pb.GetWorkerRequest{
			RequestId: "123",
			WorkerId:  worker.WorkerId,
		})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result.Result.Code).To(Equal(service.SuccessResult.Code))
		Ω(result.Result.Reason).To(Equal(service.SuccessResult.Reason))
		Ω(result.Result.Message).To(Equal(service.SuccessResult.Message))
	})

	It("TestList", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().ListByConditions(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*biz.Worker{worker}, nil)
		result, err := workerService.ListWorker(ctx, &pb.ListWorkerRequest{})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result.Result.Code).To(Equal(service.SuccessResult.Code))
		Ω(result.Result.Reason).To(Equal(service.SuccessResult.Reason))
		Ω(result.Result.Message).To(Equal(service.SuccessResult.Message))
	})

	It("TestLabel", func() {
		mWorkerRepo.EXPECT().UpdateLabel(gomock.Any(), gomock.Any()).Return(nil)
		result, err := workerService.LabelWorker(ctx, &pb.LabelWorkerRequest{WorkerId: "123"})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result.Result.Code).To(Equal(service.SuccessResult.Code))
		Ω(result.Result.Reason).To(Equal(service.SuccessResult.Reason))
		Ω(result.Result.Message).To(Equal(service.SuccessResult.Message))
	})

	It("TestTag", func() {
		mWorkerRepo.EXPECT().UpdateTagByWorkerIds(ctx, gomock.Any(), gomock.Any()).Return(nil)
		result, err := workerService.TagWorker(ctx, &pb.TagWorkerRequest{WorkerIds: []string{"123"}})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result.Result.Code).To(Equal(service.SuccessResult.Code))
		Ω(result.Result.Reason).To(Equal(service.SuccessResult.Reason))
		Ω(result.Result.Message).To(Equal(service.SuccessResult.Message))
	})

	It("TestUnTaint", func() {
		mWorkerRepo.EXPECT().UpdateTaint(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		result, err := workerService.UntaintWorker(ctx, &pb.UntaintWorkerRequest{WorkerIds: []string{"123"}})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result.Result.Code).To(Equal(service.SuccessResult.Code))
		Ω(result.Result.Reason).To(Equal(service.SuccessResult.Reason))
		Ω(result.Result.Message).To(Equal(service.SuccessResult.Message))
	})

})
