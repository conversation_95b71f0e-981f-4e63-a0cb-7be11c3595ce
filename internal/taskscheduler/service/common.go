package service

import (
	"runtime"
	"strings"

	v1 "proto.mpp/api/common/v1"
)

var (
	SuccessResult = &v1.CommonReplyResult{
		Code:    200,
		Reason:  "Success",
		Message: "Success.",
	}
)

func RemoveDuplicates(slice []string) []string {
	seen := make(map[string]bool)
	var result []string

	for _, str := range slice {
		if !seen[str] {
			result = append(result, str)
			seen[str] = true
		}
	}

	return result
}

func GetFunctionName() string {
	pc := make([]uintptr, 1)
	runtime.Callers(2, pc)
	f := runtime.FuncForPC(pc[0])
	return f.Name()[strings.LastIndex(f.Name(), ".")+1:]
}
