package service

import (
	"context"
	"runtime"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"

	"mpp/internal/taskscheduler/biz"

	"google.golang.org/protobuf/types/known/timestamppb"
	pb "proto.mpp/api/taskscheduler/v1"
)

type WaterLevelService struct {
	pb.UnimplementedWaterLevelServer

	lm  *biz.WaterLevelManager
	wm  *biz.WorkerManager
	log *log.Helper
}

func NewWaterLevelService(lm *biz.WaterLevelManager, wm *biz.WorkerManager, logger log.Logger) *WaterLevelService {
	service := &WaterLevelService{lm: lm, wm: wm, log: log.NewHelper(logger)}

	return service
}

func (s *WaterLevelService) GetWaterLevel(ctx context.Context, req *pb.GetWaterLevelRequest) (*pb.GetWaterLevelReply, error) {
	var err error
	var resp *pb.GetWaterLevelReply
	f := s.getFunctionName()

	start := s.logApiIn(ctx, f, req.Product, req.EngineModel, req.Tag, req.Label, req)
	defer func() {
		s.logApiOut(ctx, f, req.Product, req.EngineModel, req.Tag, req.Label, start, req, resp, err)
	}()

	s.log.WithContext(ctx).Infof("GetWaterLevel. req:%v", req)
	var waterLevel biz.WaterLevel
	err = copier.Copy(&waterLevel, req)
	if err != nil {
		s.log.WithContext(ctx).Errorf("copy water level error:%v", err)
		return nil, err
	}
	w, err := s.lm.GetWaterLevel(ctx, &waterLevel)
	if err != nil {
		return nil, err
	}

	var level pb.GetWaterLevelReply_WaterLevel
	err = copier.Copy(&level, w)
	if err != nil {
		s.log.WithContext(ctx).Errorf("copy water level error:%v", err)
		return nil, err
	}
	level.StatTime = timestamppb.New(w.StatTime)

	data := pb.GetWaterLevelReply_Data{
		WaterLevel: &level,
	}

	resp = &pb.GetWaterLevelReply{Result: SuccessResult, Data: &data}
	return resp, nil
}

func (s *WaterLevelService) ListWaterLevel(ctx context.Context, req *pb.ListWaterLevelRequest) (*pb.ListWaterLevelReply, error) {
	var err error
	var resp *pb.ListWaterLevelReply
	f := s.getFunctionName()

	start := s.logApiIn(ctx, f, req.Product, req.EngineModel, req.Tag, req.Label, req)
	defer func() {
		s.logApiOut(ctx, f, req.Product, req.EngineModel, req.Tag, req.Label, start, req, resp, err)
	}()

	var level biz.WaterLevel
	err = copier.Copy(&level, req)
	if err != nil {
		s.log.WithContext(ctx).Errorf("copy water level error:%v", err)
		return nil, err
	}
	if req.Limit == 0 {
		req.Limit = 10
	}

	waterLevels, err := s.lm.ListWaterLevel(ctx, &level, int(req.Limit))
	if err != nil {
		return nil, err
	}

	s.log.WithContext(ctx).Infof("list result:%v", biz.JsonToString(waterLevels))

	var wls []*pb.ListWaterLevelReply_WaterLevel
	for _, waterLevel := range waterLevels {
		var wl pb.ListWaterLevelReply_WaterLevel
		err := copier.Copy(&wl, waterLevel)
		if err != nil {
			s.log.WithContext(ctx).Errorf("copy water level error:%v", err)
			return nil, err
		}
		wl.StatTime = timestamppb.New(waterLevel.StatTime)
		wls = append(wls, &wl)
	}

	data := pb.ListWaterLevelReply_Data{
		WaterLevels: wls,
	}

	resp = &pb.ListWaterLevelReply{Result: SuccessResult, Data: &data}
	return resp, nil
}

func (s *WaterLevelService) logApiIn(ctx context.Context, f string, product string, model string, tag string, label map[string]string, req interface{}) time.Time {
	s.log.WithContext(ctx).Log(log.LevelInfo,
		"product", product,
		"model", model,
		"tag", tag,
		"label", label,
		"event", "api.in",
		"func", f,
		"request", req)

	return time.Now()
}

func (s *WaterLevelService) logApiOut(ctx context.Context, f string, product string, model string, tag string, label map[string]string, start time.Time, req interface{}, resp interface{}, err error) {
	var code int32
	logLevel := log.LevelInfo
	if nil == err {
		code = 200
	} else {
		code = errors.FromError(err).Code
		logLevel = log.LevelWarn
	}
	s.log.WithContext(ctx).Log(logLevel,
		"product", product,
		"model", model,
		"tag", tag,
		"label", label,
		"event", "api.out",
		"latency", time.Since(start).Milliseconds(),
		"request", req,
		"response", resp,
		"func", f,
		"error", err,
		"code", code)
}

func (s *WaterLevelService) getFunctionName() string {
	pc := make([]uintptr, 1)
	runtime.Callers(2, pc)
	f := runtime.FuncForPC(pc[0])
	return "WaterLevel" + f.Name()[strings.LastIndex(f.Name(), "."):]
}
