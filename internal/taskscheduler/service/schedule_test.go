package service_test

import (
	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/conf"
	mclient "mpp/internal/taskscheduler/mocks/client"
	"mpp/internal/taskscheduler/mocks/mrepo"
	"mpp/internal/taskscheduler/service"
	"mpp/internal/taskscheduler/testdata"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	v1 "proto.mpp/api/common/v1"
	pb "proto.mpp/api/taskscheduler/v1"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("ScheduleService", func() {
	var (
		rm            *biz.ResourceManager
		mResourceRepo *mrepo.MockResourceRepo

		mStrategyRepo *mrepo.MockStrategyRepo
		mWorkerRepo   *mrepo.MockWorkerRepo

		mClient *mclient.MockTaskScheduleMasterApi

		wm *biz.WorkerManager

		sm *biz.StrategyManager

		wlm *biz.WaterLevelManager

		leader biz.LeaderRepo

		scheduleManager *biz.ScheduleManager
		scheduleService *service.ScheduleService
	)
	BeforeEach(func() {
		mResourceRepo = mrepo.NewMockResourceRepo(ctl)
		mWorkerRepo = mrepo.NewMockWorkerRepo(ctl)
		mStrategyRepo = mrepo.NewMockStrategyRepo(ctl)
		mClient = mclient.NewMockTaskScheduleMasterApi(ctl)

		leader = NewNullLeaderRepo()

		rm = biz.NewResourceManager(mResourceRepo, nil, log.DefaultLogger)
		sm = biz.NewStrategyManager(mStrategyRepo, log.DefaultLogger)
		wm = biz.NewWorkerManager(&conf.App{}, mWorkerRepo, nil, mClient, log.DefaultLogger)

		scheduleManager = biz.NewScheduleManager(leader, log.DefaultLogger)
	})

	It("TestGetMasterWorker", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().ListOnlineWorker(gomock.Any()).Return([]*biz.Worker{worker}, nil)

		strategy := testdata.Strategy()
		mStrategyRepo.EXPECT().ListAll(ctx).Return([]*biz.Strategy{strategy}, nil)

		config := &conf.App{DelayScheduleUpdate: true}
		scheduleService = service.NewScheduleService(config, scheduleManager, rm, wm, nil, sm,
			wlm, leader, mClient, log.DefaultLogger)

		log.Infof("scheduleService: %+v", scheduleService)
		//scheduleService.Start(leader)
		_, err := scheduleService.GetMasterWorker(ctx, &pb.GetMasterWorkerRequest{})

		Ω(err).Should(HaveOccurred())

	})

	It("TestAllocMasterResource", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().ListOnlineWorker(gomock.Any()).Return([]*biz.Worker{worker}, nil)

		strategy := testdata.Strategy()
		mStrategyRepo.EXPECT().ListAll(ctx).Return([]*biz.Strategy{strategy}, nil)

		config := &conf.App{DelayScheduleUpdate: true}
		scheduleService = service.NewScheduleService(config, scheduleManager, rm, wm, nil, sm,
			wlm, leader, mClient, log.DefaultLogger)

		log.Infof("scheduleService: %+v", scheduleService)
		//scheduleService.Start(leader)
		_, err := scheduleService.AllocMasterResource(ctx, &pb.AllocMasterResourceRequest{})

		Ω(err).Should(HaveOccurred())

	})

	It("TestAllocMasterResource copy affinity", func() {
		worker := testdata.ScheduleWorker(0, map[string]string{"arch": "x86"})
		//worker := testdata.ScheduleWorker(0, map[string]string{"arch": "x86", "resourceType": "cpu"})
		mWorkerRepo.EXPECT().ListOnlineWorker(gomock.Any()).Return([]*biz.Worker{worker}, nil)

		strategy := testdata.Strategy()
		mStrategyRepo.EXPECT().ListAll(ctx).Return([]*biz.Strategy{strategy}, nil)

		config := &conf.App{DelayScheduleUpdate: true}
		scheduleService = service.NewScheduleService(config, scheduleManager, rm, wm, nil, sm,
			wlm, leader, mClient, log.DefaultLogger)

		log.Infof("scheduleService: %+v", scheduleService)
		//scheduleService.Start(leader)
		cpuRequired := &v1.Affinity{
			Key:      "resourceType",
			Operator: "In",
			Values:   []string{"cpu"},
		}

		req := &pb.AllocMasterResourceRequest{
			Product:     "ols",
			EngineModel: "transcode",
			Tag:         "livetranscode_pub",
			Quota:       map[string]int64{"cpu": 2000},
			Affinity: &v1.TaskAffinity{
				Required: []*v1.Affinity{
					cpuRequired,
				},
			},
		}
		_, err := scheduleService.AllocMasterResource(ctx, req)

		Ω(err).Should(HaveOccurred())

	})

	It("TestFreeMasterResource", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().ListOnlineWorker(gomock.Any()).Return([]*biz.Worker{worker}, nil)

		strategy := testdata.Strategy()
		mStrategyRepo.EXPECT().ListAll(ctx).Return([]*biz.Strategy{strategy}, nil)

		config := &conf.App{DelayScheduleUpdate: true}
		scheduleService = service.NewScheduleService(config, scheduleManager, rm, wm, nil, sm,
			wlm, leader, mClient, log.DefaultLogger)

		log.Infof("scheduleService: %+v", scheduleService)
		//scheduleService.Start(leader)
		_, err := scheduleService.FreeMasterResource(ctx, &pb.FreeMasterResourceRequest{})

		Ω(err).Should(HaveOccurred())

	})

	It("TestLeader", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().ListOnlineWorker(gomock.Any()).Return([]*biz.Worker{worker}, nil)

		strategy := testdata.Strategy()
		mStrategyRepo.EXPECT().ListAll(ctx).Return([]*biz.Strategy{strategy}, nil)

		config := &conf.App{DelayScheduleUpdate: true}
		scheduleService = service.NewScheduleService(config, scheduleManager, rm, wm, nil, sm,
			wlm, leader, mClient, log.DefaultLogger)

		log.Infof("scheduleService: %+v", scheduleService)
		//scheduleService.Start(leader)
		//scheduleService.OnLeaderEvent(biz.EventLeaderSuccess, "127.0.0.1")
		mClient.EXPECT().ResetClient(gomock.Any(), gomock.Any()).Return(nil)
		scheduleService.OnLeaderEvent(biz.EventLeaderChanged, "127.0.0.1")

		mClient.EXPECT().ResetClient(gomock.Any(), gomock.Any()).Return(nil)
		scheduleService.OnLeaderEvent(biz.EventLeaderLost, "127.0.0.1")

	})

	It("TestAllocResource", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().ListOnlineWorker(gomock.Any()).Return([]*biz.Worker{worker}, nil)

		strategy := testdata.Strategy()
		mStrategyRepo.EXPECT().ListAll(ctx).Return([]*biz.Strategy{strategy}, nil)

		config := &conf.App{DelayScheduleUpdate: true}
		scheduleService = service.NewScheduleService(config, scheduleManager, rm, wm, nil, sm,
			wlm, leader, mClient, log.DefaultLogger)

		log.Infof("scheduleService: %+v", scheduleService)
		//scheduleService.Start(leader)
		mClient.EXPECT().AllocMasterResource(gomock.Any(), gomock.Any()).Return(nil, nil)
		_, err := scheduleService.AllocResource(ctx, &pb.AllocResourceRequest{})

		Ω(err).Should(HaveOccurred())

		scheduleService.LogSchedulerInfo(nil, nil)
	})

	It("TestFreeResource", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().ListOnlineWorker(gomock.Any()).Return([]*biz.Worker{worker}, nil)

		strategy := testdata.Strategy()
		mStrategyRepo.EXPECT().ListAll(ctx).Return([]*biz.Strategy{strategy}, nil)

		config := &conf.App{DelayScheduleUpdate: true}
		scheduleService = service.NewScheduleService(config, scheduleManager, rm, wm, nil, sm,
			wlm, leader, mClient, log.DefaultLogger)

		log.Infof("scheduleService: %+v", scheduleService)
		//scheduleService.Start(leader)
		_, err := scheduleService.FreeResource(ctx, &pb.FreeResourceRequest{})

		Ω(err).ShouldNot(HaveOccurred())
	})

	It("TestUpdateResource", func() {
		worker := testdata.Worker()
		mWorkerRepo.EXPECT().ListOnlineWorker(gomock.Any()).Return([]*biz.Worker{worker}, nil)

		strategy := testdata.Strategy()
		mStrategyRepo.EXPECT().ListAll(ctx).Return([]*biz.Strategy{strategy}, nil)

		config := &conf.App{DelayScheduleUpdate: true}
		scheduleService = service.NewScheduleService(config, scheduleManager, rm, wm, nil, sm,
			wlm, leader, mClient, log.DefaultLogger)

		log.Infof("scheduleService: %+v", scheduleService)
		//scheduleService.Start(leader)
		_, err := scheduleService.UpdateResource(ctx, &pb.UpdateResourceRequest{})

		Ω(err).ShouldNot(HaveOccurred())
	})
})

type NullleaderRepo struct {
}

func (l *NullleaderRepo) Watch(key string, listener biz.LeaderObserver) {
}

func NewNullLeaderRepo() biz.LeaderRepo {
	return &NullleaderRepo{}
}
