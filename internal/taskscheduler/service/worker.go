package service

import (
	"context"
	"runtime"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"

	"mpp/internal/taskscheduler/biz"

	pb "proto.mpp/api/taskscheduler/v1"
)

type WorkerService struct {
	pb.UnimplementedWorkerServer

	wm  *biz.WorkerManager
	cwm *biz.ChildWorkerManager
	log *log.Helper
}

func NewWorkerService(wm *biz.WorkerManager, cwm *biz.ChildWorkerManager, logger log.Logger) *WorkerService {
	ws := &WorkerService{wm: wm, cwm: cwm, log: log.NewHelper(logger)}

	return ws
}

func (s *WorkerService) Register(ctx context.Context, req *pb.RegisterWorkerRequest) (*pb.RegisterWorkerReply, error) {
	//s.wm.Register(ctx, req)
	//s.log.WithContext(ctx).Infof("worker register. req:%v", biz.JsonToString(req))
	var err error
	var resp *pb.RegisterWorkerReply
	f := s.getFunctionName()

	start := s.logApiIn(ctx, f, req.WorkerId, req)
	defer func() {
		s.logApiOut(ctx, f, req.WorkerId, start, req, resp, err)
	}()

	var worker biz.Worker
	err = copier.Copy(&worker, req)
	if err != nil {
		s.log.WithContext(ctx).Warnf("worker register. copy error. err:%v", err)
		return nil, err
	}

	_, err = s.wm.Register(ctx, &worker)
	if err != nil {
		return nil, err
	}

	if req.ChildWorker != nil && len(req.ChildWorker) > 0 {
		err = s.childWorkerRegister(ctx, req)
	}

	resp = &pb.RegisterWorkerReply{Result: SuccessResult}
	return resp, nil
}

func (s *WorkerService) childWorkerRegister(ctx context.Context, req *pb.RegisterWorkerRequest) error {
	var childWorkers []biz.ChildWorker
	err := copier.Copy(&childWorkers, req.ChildWorker)
	if err != nil {
		s.log.WithContext(ctx).Warnf("childWorker register. copy error. err:%v", err)
		return err
	}
	for _, cw := range childWorkers {
		cw.ParentWorkerId = req.WorkerId
		cw.ParentProduct = req.Product
		cw.ParentEngineModel = req.EngineModel
		cw.ParentTag = req.Tag

		_, err = s.cwm.Register(ctx, &cw)
		if err != nil {
			s.log.WithContext(ctx).Warnf("childWorker register. workermanager error. err:%v", err)
			return err
		}
	}
	return nil
}

func (s *WorkerService) RegisterChildWorker(ctx context.Context, req *pb.RegisterChildWorkerRequest) (*pb.RegisterChildWorkerReply, error) {
	var err error
	var resp *pb.RegisterChildWorkerReply
	f := s.getFunctionName()

	start := s.logApiIn(ctx, f, req.ParentWorkerId, req)
	defer func() {
		s.logApiOut(ctx, f, req.ParentWorkerId, start, req, resp, err)
	}()

	//验证父worker状态
	parentWorker, err := s.wm.Get(ctx, req.ParentWorkerId, biz.WorkerStatusOnline)
	if err != nil {
		s.log.WithContext(ctx).Warnf("childWorker register. workermanager error. err:%v", err)
		return nil, err
	}

	var childWorkers []biz.ChildWorker
	err = copier.Copy(&childWorkers, req.ChildWorkerList)
	if err != nil {
		s.log.WithContext(ctx).Warnf("childWorker register. copy error. err:%v", err)
		return nil, err
	}
	for _, cw := range childWorkers {
		cw.ParentWorkerId = parentWorker.WorkerId
		cw.ParentProduct = parentWorker.Product
		cw.ParentEngineModel = parentWorker.EngineModel
		cw.ParentTag = parentWorker.Tag
		_, err = s.cwm.Register(ctx, &cw)
		if err != nil {
			s.log.WithContext(ctx).Warnf("childWorker register. workermanager error. err:%v", err)
			return nil, err
		}
	}
	resp = &pb.RegisterChildWorkerReply{Result: SuccessResult}
	return resp, nil
}

func (s *WorkerService) Heartbeat(ctx context.Context, req *pb.HeartbeatWorkerRequest) (*pb.HeartbeatWorkerReply, error) {
	var err error
	var resp *pb.HeartbeatWorkerReply
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req.WorkerId, req)
	defer func() {
		s.logApiOut(ctx, f, req.WorkerId, start, req, resp, err)
	}()

	var worker biz.Worker
	err = copier.Copy(&worker, req)
	if err != nil {
		s.log.WithContext(ctx).Warnf("worker heartbeat. copy error. err:%v", err)
		return nil, err
	}

	err = s.wm.Heartbeat(ctx, &worker)
	if err != nil {
		return nil, err
	}

	if req.ChildWorker != nil && len(req.ChildWorker) > 0 {
		err = s.childWorkerHeartBeat(ctx, req)
	}
	if err != nil {
		return nil, err
	}

	resp = &pb.HeartbeatWorkerReply{Result: SuccessResult}
	return resp, nil
}

func (s *WorkerService) childWorkerHeartBeat(ctx context.Context, req *pb.HeartbeatWorkerRequest) error {
	var childWorkers []biz.ChildWorker
	//这里不能用copy，totalQuota字段字段无法正确对应quota字段
	//err := copier.Copy(&childWorkers, req.ChildWorker)
	//if err != nil {
	//	s.log.WithContext(ctx).Warnf("childWorker heartbeat. copy error. err:%v", err)
	//	return err
	//}
	for _, cw := range req.ChildWorker {
		childWorkers = append(childWorkers, biz.ChildWorker{
			WorkerId:  cw.WorkerId,
			Quota:     cw.TotalQuota,
			RealQuota: cw.RealQuota,
		})
	}
	//部分字段无法copy对应, 需要手动赋值

	for _, cw := range childWorkers {
		//cw.ParentWorkerId = req.WorkerId
		//// childWorker没有注册流程，查询的不到的时候创建 todo 优化 会导致心跳流程多查一次数据库
		//if _, err := s.cwm.Get(ctx, cw.WorkerId, biz.WorkerStatusOnline); err != nil && errors.Is(err, biz.ErrWorkerNotFound) {
		//	s.log.WithContext(ctx).Infof("Heartbeat childWorker:%+v is not register", cw)
		//	parentWorker, errPar := s.wm.Get(ctx, req.WorkerId, biz.WorkerStatusOnline); if errPar != nil {
		//        s.log.WithContext(ctx).Warnf("childWorker heartbeat. workermanager error. err:%v", err)
		//        return errPar
		//    }
		//	cw.ParentProduct = parentWorker.Product
		//	cw.ParentEngineModel = parentWorker.EngineModel
		//	cw.ParentTag = parentWorker.Tag
		//}
		err := s.cwm.Heartbeat(ctx, &cw)
		if err != nil {
			s.log.WithContext(ctx).Warnf("childWorker heartbeat. workermanager error. err:%v", err)
			return err
		}
	}
	return nil
}

func (s *WorkerService) UnregisterWorker(ctx context.Context, req *pb.UnregisterWorkerRequest) (*pb.UnregisterWorkerReply, error) {
	var err error
	var resp *pb.UnregisterWorkerReply
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req.WorkerId, req)
	defer func() {
		s.logApiOut(ctx, f, req.WorkerId, start, req, resp, err)
	}()

	var worker biz.Worker
	err = copier.Copy(&worker, req)
	if err != nil {
		s.log.WithContext(ctx).Warnf("worker unregister. copy error. err:%v", err)
		return nil, err
	}

	err = s.wm.Unregister(ctx, &worker)
	if err != nil {
		return nil, err
	}

	resp = &pb.UnregisterWorkerReply{Result: SuccessResult}
	return resp, nil
}

func (s *WorkerService) UnregisterChildWorker(ctx context.Context, req *pb.UnregisterChildWorkerRequest) (*pb.UnregisterChildWorkerReply, error) {
	var err error
	var resp *pb.UnregisterChildWorkerReply
	f := s.getFunctionName()
	start := s.logApiIn(ctx, f, req.ParentWorkerId, req)
	defer func() {
		s.logApiOut(ctx, f, req.ParentWorkerId, start, req, resp, err)
	}()

	var childWorkerIds []string
	err = copier.Copy(&childWorkerIds, req.ChildWorkerIds)
	if err != nil {
		s.log.WithContext(ctx).Warnf("childWorker unregister. copy error. err:%v", err)
		return nil, err
	}
	err = s.cwm.BatchUpdateWorkerStatus(ctx, childWorkerIds)
	if err != nil {
		return nil, err
	}

	resp = &pb.UnregisterChildWorkerReply{Result: SuccessResult}
	return resp, nil
}

func (s *WorkerService) GetWorker(ctx context.Context, req *pb.GetWorkerRequest) (*pb.GetWorkerReply, error) {
	var err error
	var resp *pb.GetWorkerReply
	f := s.getFunctionName()

	start := s.logApiIn(ctx, f, req.WorkerId, req)
	defer func() {
		s.logApiOut(ctx, f, req.WorkerId, start, req, resp, err)
	}()

	var worker biz.Worker
	err = copier.Copy(&worker, req)
	if err != nil {
		s.log.WithContext(ctx).Warnf("worker get. copy error. err:%v", err)
		return nil, err
	}

	w, err := s.wm.Get(ctx, req.WorkerId, biz.WorkerStatusOnline)
	if err != nil {
		return nil, err
	}

	var workerInfo pb.WorkerInfo
	err = copier.Copy(&workerInfo, w)
	if err != nil {
		s.log.WithContext(ctx).Warnf("worker get. copy error. err:%v", err)
		return nil, err
	}

	data := pb.GetWorkerReply_Data{
		Worker: &workerInfo,
	}

	resp = &pb.GetWorkerReply{Result: SuccessResult, Data: &data}
	return resp, nil
}
func (s *WorkerService) ListWorker(ctx context.Context, req *pb.ListWorkerRequest) (*pb.ListWorkerReply, error) {
	var err error
	var resp *pb.ListWorkerReply
	f := s.getFunctionName()

	start := s.logApiIn(ctx, f, "", req)
	defer func() {
		s.logApiOut(ctx, f, "", start, req, resp, err)
	}()

	worker := &biz.Worker{}
	err = copier.Copy(&worker, req)
	if err != nil {
		s.log.WithContext(ctx).Warnf("worker list. copy error. err:%v", err)
		return nil, err
	}
	w, err := s.wm.ListWorker(ctx, worker, req.Ips)
	if err != nil {
		return nil, err
	}

	var workers []*pb.WorkerInfo
	for i := 0; i < len(w); i++ {
		var workerInfo pb.WorkerInfo
		err := copier.Copy(&workerInfo, w[i])
		if err != nil {
			s.log.WithContext(ctx).Warnf("worker list. copy error. err:%v", err)
			return nil, err
		}
		workers = append(workers, &workerInfo)
	}

	data := pb.ListWorkerReply_Data{
		Workers: workers,
	}

	resp = &pb.ListWorkerReply{Result: SuccessResult, Data: &data}
	return resp, nil
}

func (s *WorkerService) TagWorker(ctx context.Context, req *pb.TagWorkerRequest) (*pb.TagWorkerReply, error) {
	var err error
	var resp *pb.TagWorkerReply
	f := s.getFunctionName()

	start := s.logApiIn(ctx, f, req.WorkerIds, req)
	defer func() {
		s.logApiOut(ctx, f, req.WorkerIds, start, req, resp, err)
	}()

	err = s.wm.TagWorker(ctx, req.WorkerIds, req.Ips, req.Tag)
	if err != nil {
		return nil, err
	}

	resp = &pb.TagWorkerReply{Result: SuccessResult}
	return resp, nil
}

func (s *WorkerService) TaintWorker(ctx context.Context, req *pb.TaintWorkerRequest) (*pb.TaintWorkerReply, error) {
	var err error
	var resp *pb.TaintWorkerReply
	f := s.getFunctionName()

	start := s.logApiIn(ctx, f, req.WorkerId, req)
	defer func() {
		s.logApiOut(ctx, f, req.WorkerId, start, req, resp, err)
	}()

	var taints []*biz.Taint
	for _, taint := range req.Taint {
		bizTaint := biz.Taint{}
		err := copier.Copy(&bizTaint, taint)
		if err != nil {
			s.log.WithContext(ctx).Warnf("worker taint. copy error. err:%v", err)
			return nil, err
		}
		taints = append(taints, &bizTaint)
	}

	if req.WorkerId != "" {
		req.WorkerIds = []string{req.WorkerId}
	}
	//if req.ChildWorker != nil && *req.ChildWorker {
	//	//add ChildWorker taint
	//	err = s.cwm.TaintChildWorker(ctx, req.WorkerIds, req.WorkerIps, taints)
	//	if err != nil {
	//		return nil, err
	//	}
	//	return nil, nil
	//} else {
	//	err = s.wm.TaintWorker(ctx, req.WorkerIds, req.WorkerIps, taints)
	//	if err != nil {
	//		return nil, err
	//	}
	//}
	err = s.wm.TaintWorker(ctx, req.WorkerIds, req.WorkerIps, taints)
	if err != nil {
		return nil, err
	}

	resp = &pb.TaintWorkerReply{Result: SuccessResult}
	return resp, nil
}

func (s *WorkerService) UntaintWorker(ctx context.Context, req *pb.UntaintWorkerRequest) (*pb.UntaintWorkerReply, error) {
	var err error
	var resp *pb.UntaintWorkerReply
	f := s.getFunctionName()

	start := s.logApiIn(ctx, f, req.WorkerId, req)
	defer func() {
		s.logApiOut(ctx, f, req.WorkerId, start, req, resp, err)
	}()

	var taints []*biz.Taint
	for _, taint := range req.Taint {
		bizTaint := biz.Taint{}
		err := copier.Copy(&bizTaint, taint)
		if err != nil {
			s.log.WithContext(ctx).Warnf("worker untaint. copy error. err:%v", err)
			return nil, err
		}
		bizTaint.Effect = "Delete"
		taints = append(taints, &bizTaint)
	}

	if req.WorkerId != "" {
		req.WorkerIds = []string{req.WorkerId}
	}
	//if req.ChildWorker != nil && *req.ChildWorker {
	//	//add ChildWorker unTaint
	//	err = s.cwm.TaintChildWorker(ctx, req.WorkerIds, req.WorkerIps, taints)
	//	if err != nil {
	//		return nil, err
	//	}
	//} else {
	//	err = s.wm.TaintWorker(ctx, req.WorkerIds, req.WorkerIps, taints)
	//	if err != nil {
	//		return nil, err
	//	}
	//}
	err = s.wm.TaintWorker(ctx, req.WorkerIds, req.WorkerIps, taints)
	if err != nil {
		return nil, err
	}

	resp = &pb.UntaintWorkerReply{Result: SuccessResult}
	return resp, nil
}

func (s *WorkerService) LabelWorker(ctx context.Context, req *pb.LabelWorkerRequest) (*pb.LabelWorkerReply, error) {
	var err error
	var resp *pb.LabelWorkerReply
	f := s.getFunctionName()

	start := s.logApiIn(ctx, f, req.WorkerId, req)
	defer func() {
		s.logApiOut(ctx, f, req.WorkerId, start, req, resp, err)
	}()

	err = s.wm.LabelWorker(ctx, req.WorkerId, req.Label)
	if err != nil {
		return nil, err
	}

	resp = &pb.LabelWorkerReply{Result: SuccessResult}
	return resp, nil
}

func (s *WorkerService) logApiIn(ctx context.Context, f string, workerId interface{}, req interface{}) time.Time {
	s.log.WithContext(ctx).Log(log.LevelInfo,
		"worker.id", workerId,
		"event", "api.in",
		"func", f,
		"request", req)

	return time.Now()
}

func (s *WorkerService) logApiOut(ctx context.Context, f string, workerId interface{}, start time.Time, req interface{}, resp interface{}, err error) {
	var code int32
	logLevel := log.LevelInfo
	if nil == err {
		code = 200
	} else {
		code = errors.FromError(err).Code
		logLevel = log.LevelWarn
	}
	s.log.WithContext(ctx).Log(logLevel,
		"worker.id", workerId,
		"event", "api.out",
		"latency", time.Since(start).Milliseconds(),
		"request", req,
		"response", resp,
		"func", f,
		"error", err,
		"code", code)
}

func (s *WorkerService) getFunctionName() string {
	pc := make([]uintptr, 1)
	runtime.Callers(2, pc)
	f := runtime.FuncForPC(pc[0])
	return "Worker" + f.Name()[strings.LastIndex(f.Name(), "."):]
}
