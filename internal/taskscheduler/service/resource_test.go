package service_test

import (
	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/mocks/mrepo"
	"mpp/internal/taskscheduler/service"
	"mpp/internal/taskscheduler/testdata"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang/mock/gomock"
	pb "proto.mpp/api/taskscheduler/v1"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("ResourceService", func() {
	var (
		resourceManager *biz.ResourceManager
		mResourceRepo   *mrepo.MockResourceRepo
		resourceService *service.ResourceService
	)
	BeforeEach(func() {
		mResourceRepo = mrepo.NewMockResourceRepo(ctl)
		resourceManager = biz.NewResourceManager(mResourceRepo, nil, log.DefaultLogger)
		resourceService = service.NewResourceService(resourceManager, log.DefaultLogger)
	})
	It("TestGet", func() {
		resource := testdata.Resource()
		mResourceRepo.EXPECT().FindByTaskId(ctx, gomock.Any()).Return(resource, nil)
		result, err := resourceService.GetResource(ctx, &pb.GetResourceRequest{
			RequestId: "123",
			TaskId:    resource.TaskId,
		})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result.Result.Code).To(Equal(service.SuccessResult.Code))
		Ω(result.Result.Reason).To(Equal(service.SuccessResult.Reason))
		Ω(result.Result.Message).To(Equal(service.SuccessResult.Message))
	})

	It("TestList", func() {
		resource := testdata.Resource()
		mResourceRepo.EXPECT().ListAll(ctx, gomock.Any()).Return([]*biz.Resource{resource}, nil)
		result, err := resourceService.ListResource(ctx, &pb.ListResourceRequest{})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result.Result.Code).To(Equal(service.SuccessResult.Code))
		Ω(result.Result.Reason).To(Equal(service.SuccessResult.Reason))
		Ω(result.Result.Message).To(Equal(service.SuccessResult.Message))
	})

	It("TestListLostResource", func() {
		resource := testdata.Resource()
		mResourceRepo.EXPECT().ListLost(ctx, gomock.Any()).Return([]*biz.Resource{resource}, nil)
		result, err := resourceService.ListLostResource(ctx, &pb.ListResourceRequest{})
		Ω(err).ShouldNot(HaveOccurred())
		Ω(err).ToNot(HaveOccurred())
		Ω(result.Result.Code).To(Equal(service.SuccessResult.Code))
		Ω(result.Result.Reason).To(Equal(service.SuccessResult.Reason))
		Ω(result.Result.Message).To(Equal(service.SuccessResult.Message))
	})
})
