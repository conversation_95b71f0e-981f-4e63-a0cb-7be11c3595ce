package testdata

import (
	"fmt"
	"time"

	"mpp/internal/taskscheduler/biz"
)

func ScheduleWorker(index int, affinity map[string]string) *biz.Worker {
	return &biz.Worker{
		WorkerId:        fmt.Sprintf("worker-uuid-%v", index),
		Ip:              fmt.Sprintf("1.1.1.%v", index),
		PodName:         fmt.Sprintf("pod-name-%v", index),
		NodeName:        fmt.Sprintf("node-name-%v", index),
		Port:            80,
		Product:         "ols",
		EngineModel:     "transcode",
		Tag:             "livetranscode_pub",
		OnlineVersion:   0,
		Quota:           map[string]int64{"cpu": 32000},
		AllocQuota:      map[string]int64{"cpu": 0},
		RealQuota:       map[string]int64{"cpu": 0},
		AvgQuota:        map[string]int64{"cpu": 0},
		ScheduleQuota:   map[string]int64{"cpu": 0},
		Label:           affinity,
		Taint:           nil,
		HeartbeatTime:   time.Now(),
		LatestAllocTime: time.Now(),
		Extend:          map[string]interface{}{"extend": fmt.Sprintf("test_%v", index)},
	}
}

func ScheduleWorkerList(len int) []*biz.Worker {
	workers := make([]*biz.Worker, 0)
	for i := 0; i < len; i++ {
		worker := &biz.Worker{
			WorkerId:      fmt.Sprintf("worker-uuid-batch-%v", i),
			Ip:            fmt.Sprintf("2.2.2.%v", i),
			PodName:       fmt.Sprintf("pod-name-batch-%v", i),
			NodeName:      fmt.Sprintf("node-name-batch-%v", i),
			Port:          80,
			Product:       "ols",
			EngineModel:   "transcode",
			Tag:           "livetranscode_pub",
			OnlineVersion: 0,
			Quota:         map[string]int64{"cpu": 32000},
			AllocQuota:    map[string]int64{"cpu": 0},
			RealQuota:     map[string]int64{"cpu": 0},
			AvgQuota:      map[string]int64{"cpu": 0},
			ScheduleQuota: map[string]int64{"cpu": 0},
			//Label:           map[string]string{"app": fmt.Sprintf("livetranscode_pub_%v", i)},
			Label: map[string]string{"app": "livetranscode_pub"},
			//Taint: []*biz.Taint{
			//	&biz.Taint{
			//		Key:    "taint",
			//		Value:  fmt.Sprintf("test_%v", i),
			//		Effect: "NoSchedule",
			//	},
			//},
			Taint:           nil,
			HeartbeatTime:   time.Now(),
			LatestAllocTime: time.Now(),
			Extend:          map[string]interface{}{"extend": fmt.Sprintf("test_%v", i)},
		}
		workers = append(workers, worker)
	}

	return workers
}

func ScheduleResourceList(len int) []*biz.Resource {
	resources := make([]*biz.Resource, 0)
	for i := 0; i < len; i++ {
		resource := &biz.Resource{
			JobId:       fmt.Sprintf("job-batch-%v", i),
			TaskId:      fmt.Sprintf("tasker-batch-%v", i),
			Product:     "ols",
			EngineModel: "transcode",
			Tag:         "livetranscode_pub",
			Quota:       map[string]int64{"cpu": 2000},
			//WorkerId:      fmt.Sprintf("worker-batch-%v", i),
			//WorkerVersion: 3,
			//WorkerIp:      fmt.Sprintf("2.2.2.%v", i),
			//WorkerPort:    100 + int32(i),
			//IsMaster:  1,
			//RealQuota: map[string]int64{"cpu": 150 + int32(i)},
			//AvgQuota:  map[string]int64{"cpu": 180 + int32(i)},
			//Label: map[string]string{"batch-key": fmt.Sprintf("batch-value-%v", i)},
			//Affinity: &biz.Affinity{
			//	Required: &metav1.LabelSelector{
			//		MatchLabels: map[string]string{"key": fmt.Sprintf("batch-required-%v", i)},
			//		MatchExpressions: []metav1.LabelSelectorRequirement{
			//			{
			//				Key:      "key2",
			//				Operator: metav1.LabelSelectorOpIn,
			//				Values:   []string{fmt.Sprintf("batch-exp-required-%v", i)},
			//			},
			//		},
			//	},
			//	Preferred: &metav1.LabelSelector{
			//		MatchLabels: map[string]string{"key": fmt.Sprintf("batch-preferred-%v", i)},
			//		MatchExpressions: []metav1.LabelSelectorRequirement{
			//			{
			//				Key:      "key2",
			//				Operator: metav1.LabelSelectorOpIn,
			//				Values:   []string{fmt.Sprintf("batch-exp-preferred-%v", i)},
			//			},
			//		},
			//	},
			//},
			//Extend: map[string]interface{}{"key": fmt.Sprintf("extend-%v", i)},
		}

		resources = append(resources, resource)
	}

	return resources
}

func ScheduleResourceListRandomQuota(len int) []*biz.Resource {
	resources := make([]*biz.Resource, 0)
	for i := 0; i < len; i++ {
		resource := &biz.Resource{
			JobId:       fmt.Sprintf("job-batch-%v", i),
			TaskId:      fmt.Sprintf("tasker-batch-%v", i),
			Product:     "ols",
			EngineModel: "transcode",
			Tag:         "livetranscode_pub",
			Quota:       map[string]int64{"cpu": 200 + int64(i%2000)},
		}

		resources = append(resources, resource)
	}

	return resources
}

func ScheduleResource(index int, affinity *biz.Affinity) *biz.Resource {
	if affinity == nil {
		//affinity = &biz.Affinity{
		//	Required: []metav1.LabelSelectorRequirement{
		//		{
		//			Key:      "key2",
		//			Operator: metav1.LabelSelectorOpIn,
		//			Values:   []string{fmt.Sprintf("batch-exp-required-%v", index)},
		//		},
		//	},
		//	Preferred: []metav1.LabelSelectorRequirement{
		//		{
		//			Key:      "key2",
		//			Operator: metav1.LabelSelectorOpIn,
		//			Values:   []string{fmt.Sprintf("batch-exp-preferred-%v", index)},
		//		},
		//	},
		//}
	}

	return &biz.Resource{
		JobId:       fmt.Sprintf("job-%v", index),
		TaskId:      fmt.Sprintf("task-%v", index),
		Product:     "ols",
		EngineModel: "transcode",
		Tag:         "livetranscode_pub",
		Quota:       map[string]int64{"cpu": 2000},
		Affinity:    affinity,
	}
}

func ScheduleStrategy(s string) *biz.Strategy {
	strategy := &biz.Strategy{
		StrategyId:  "strategy-id-1",
		Product:     "ols",
		EngineModel: "transcode",
		Tag:         "livetranscode_pub",
		//Label:       map[string]string{"label-1": "value-1"},
		Strategy: s,
		Callback: "http://127.0.0.1/test/callback",
		Config:   biz.DefaultMasterStrategy.Config,
		Extend:   map[string]interface{}{"extend-1": "value-2"},
	}

	return strategy
}

func ScheduleStrategyWithSteps(s string, step int) *biz.Strategy {
	strategy := &biz.Strategy{
		StrategyId:  "strategy-id-1",
		Product:     "ols",
		EngineModel: "transcode",
		Tag:         "livetranscode_pub",
		//Label:       map[string]string{"label-1": "value-1"},
		Strategy: s,
		Callback: "http://127.0.0.1/test/callback",
		Config:   biz.DefaultMasterStrategy.Config,
		Extend:   map[string]interface{}{"extend-1": "value-2"},
	}
	for i := 0; i < step; i++ {
		strategyStep := &biz.StrategyStep{
			WorkerRatio:  100 / (step - i),
			ClusterRatio: 100 / (step - i),
			Strategy:     s,
		}
		strategy.Config.Steps = append(strategy.Config.Steps, strategyStep)
	}
	return strategy
}

func ScheduleStrategyWithStepsAndRealType(s string, step int) *biz.Strategy {
	config := biz.DefaultMasterStrategy.Config
	config.ScheduleResourceType = biz.ScheduleResourceTypeReal
	strategy := &biz.Strategy{
		StrategyId:  "strategy-id-1",
		Product:     "ols",
		EngineModel: "transcode",
		Tag:         "livetranscode_pub",
		//Label:       map[string]string{"label-1": "value-1"},
		Strategy: s,
		Callback: "http://127.0.0.1/test/callback",
		Config:   config,
		Extend:   map[string]interface{}{"extend-1": "value-2"},
	}
	for i := 0; i < step; i++ {
		strategyStep := &biz.StrategyStep{
			WorkerRatio:  100 / (step - i),
			ClusterRatio: 100 / (step - i),
			Strategy:     s,
		}
		strategy.Config.Steps = append(strategy.Config.Steps, strategyStep)
	}
	return strategy
}

func ScheduleStrategyList(len int) []*biz.Strategy {
	strategies := make([]*biz.Strategy, 0)
	for i := 0; i < len; i++ {
		strategy := &biz.Strategy{
			StrategyId:  fmt.Sprintf("trategy-batch-id-%v", i),
			Product:     "ols",
			EngineModel: "transcode",
			Tag:         "livetranscode_pub",
			Label:       map[string]string{fmt.Sprintf("label-%v", i): fmt.Sprintf("value-%v", i)},
			Strategy:    biz.DefaultMasterStrategy.Strategy,
			Callback:    "http://127.0.0.1/test/callback",
			Config:      biz.DefaultMasterStrategy.Config,
			Extend:      map[string]interface{}{fmt.Sprintf("extend-%v", i): fmt.Sprintf("value-%v", i)},
		}

		strategies = append(strategies, strategy)
	}

	return strategies
}
