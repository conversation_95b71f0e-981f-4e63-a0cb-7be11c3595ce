package testdata

import (
	"fmt"
	"time"

	"mpp/internal/taskscheduler/biz"
)

func WaterLevel() *biz.WaterLevel {
	level := &biz.WaterLevel{
		Product:       "ols",
		EngineModel:   "transcode",
		Tag:           "livetranscode_pub",
		Label:         nil,
		StatTime:      time.Now().Add(2 * time.Minute).Truncate(time.Minute),
		TotalQuota:    map[string]int64{"cpu": 3200},
		AllocQuota:    map[string]int64{"cpu": 1600},
		RealQuota:     map[string]int64{"cpu": 1000},
		AvgQuota:      map[string]int64{"cpu": 1200},
		ScheduleQuota: map[string]int64{"cpu": 1600},
		//Type:          biz.ScheduleResourceTypeLogic,
		//Dominant:      biz.DefaultScheduleDominant,
		WaterLevel: float32(1600) / float32(3200),
		Strategy: &biz.StrategyConfig{
			LimitRatio: map[string]int64{"cpu": 100},
		},
	}

	return level
}

func WaterLevelList(len int) []*biz.WaterLevel {
	waterLevels := make([]*biz.WaterLevel, 0)
	for i := 0; i < len; i++ {
		waterLevel := &biz.WaterLevel{
			Product:       "ols",
			EngineModel:   "transcode",
			Tag:           fmt.Sprintf("livetranscode_pub"),
			Label:         map[string]string{"app": fmt.Sprintf("livetranscode_pub_%d", i)},
			StatTime:      time.Now().Truncate(time.Minute),
			TotalQuota:    map[string]int64{"cpu": 3200},
			AllocQuota:    map[string]int64{"cpu": 1200},
			RealQuota:     map[string]int64{"cpu": 1000},
			AvgQuota:      map[string]int64{"cpu": 1200},
			ScheduleQuota: map[string]int64{"cpu": 2000 + int64(i)},
			//Type:          biz.ScheduleResourceTypeLogic,
			//Dominant:      biz.DefaultScheduleDominant,
			WaterLevel: float32(1600) / float32(3200),
		}

		waterLevels = append(waterLevels, waterLevel)
	}

	return waterLevels
}
