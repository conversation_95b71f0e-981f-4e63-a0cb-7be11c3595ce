package testdata

import (
	"fmt"

	"mpp/internal/taskscheduler/biz"

	pb "proto.mpp/api/taskscheduler/v1"
)

func Strategy(strategyId ...string) *biz.Strategy {
	strategy := &biz.Strategy{
		StrategyId:  "Strategy-id-1",
		Product:     "ols",
		EngineModel: "transcode",
		Tag:         "pub",
		Label:       map[string]string{"label1": "value1"},
		Strategy:    biz.DefaultMasterStrategy.Strategy,
		Callback:    "http://127.0.0.1/test/callback",
		Config:      biz.DefaultMasterStrategy.Config,
		Extend:      map[string]interface{}{"extend1": "value1"},
	}
	if len(strategyId) > 0 {
		strategy.StrategyId = strategyId[0]
	}
	return strategy
}

func StrategyList(len int) []*biz.Strategy {
	strategies := make([]*biz.Strategy, 0)
	for i := 0; i < len; i++ {
		strategy := &biz.Strategy{
			StrategyId:  fmt.Sprintf("trategy-batch-id-%v", i),
			Product:     "ols",
			EngineModel: "transcode",
			Tag:         "pub",
			Label:       map[string]string{fmt.Sprintf("label-%v", i): fmt.Sprintf("value-%v", i)},
			Strategy:    biz.DefaultMasterStrategy.Strategy,
			Callback:    "http://127.0.0.1/test/callback",
			Config:      biz.DefaultMasterStrategy.Config,
			Extend:      map[string]interface{}{fmt.Sprintf("extend-%v", i): fmt.Sprintf("value-%v", i)},
		}

		strategies = append(strategies, strategy)
	}

	return strategies
}

func bizToCreateRequestConfig(config *biz.StrategyConfig) *pb.StrategyConfig {
	steps := make([]*pb.StrategyStep, 0)
	for _, s := range config.Steps {
		steps = append(steps, &pb.StrategyStep{
			WorkerRatio:  int32(s.WorkerRatio),
			ClusterRatio: int32(s.ClusterRatio),
		})
	}

	return &pb.StrategyConfig{
		ScheduleResourceType: config.ScheduleResourceType,
		Steps:                steps,
		LimitRatio:           config.LimitRatio,
		MaxTaskNum:           int32(config.MaxTaskNum),
		LoadRatio:            int32(config.LoadRatio),
		Dominants:            config.Dominants,
	}
}

func BizToCreateRequest(strategy *biz.Strategy) *pb.CreateStrategyRequest {
	req := &pb.CreateStrategyRequest{
		Product:     strategy.Product,
		EngineModel: strategy.EngineModel,
		Tag:         strategy.Tag,
		Label:       strategy.Label,
		Strategy:    strategy.Strategy,
		Callback:    strategy.Callback,
		Config:      bizToCreateRequestConfig(strategy.Config),
	}
	return req
}

func BizToGetRequest(strategy *biz.Strategy) *pb.CreateStrategyRequest {
	req := &pb.CreateStrategyRequest{
		Product:     strategy.Product,
		EngineModel: strategy.EngineModel,
		Tag:         strategy.Tag,
		Label:       strategy.Label,
		Strategy:    strategy.Strategy,
		Callback:    strategy.Callback,
		Config:      bizToCreateRequestConfig(strategy.Config),
	}
	return req
}
