package testdata

import (
	"fmt"
	"time"

	"mpp/internal/taskscheduler/biz"
)

func KVStore(key, value string) *biz.KVStore {
	kv := &biz.KVStore{
		Repo:        "repo-test",
		Key:         key,
		Value:       value,
		ExpiredTime: time.Now().Add(1 * time.Second),
	}
	return kv
}

func KVStoreList(len int) []*biz.KVStore {
	kvs := make([]*biz.KVStore, 0)
	for i := 0; i < len; i++ {

		var second time.Duration
		if i < 5 {
			second = 1
		} else {
			second = 3
		}

		kv := &biz.KVStore{
			Repo:        fmt.Sprintf("repo-test-batch-%v", i),
			Key:         fmt.Sprintf("key-test-batch-%v", i),
			Value:       fmt.Sprintf("value-test-batch-%v", i),
			ExpiredTime: time.Now().Add(second * time.Second),
		}

		kvs = append(kvs, kv)
	}

	return kvs
}
