package testdata

type HlsRegisterRequest struct {
	App         string `json:"app"`
	Hostname    string `json:"hostname"`
	Endpoint    string `json:"endpoint"`
	Ip          string `json:"ip"`
	Id          string `json:"id"`
	Env         string `json:"env"`
	CheckOnline bool   `json:"checkOnline"`
}

func HlsRegisterReq() string {
	return `{
	  "app": "liverecord",
	  "hostname": "liverecord011000139213.et2",
	  "endpoint": "oss-cn-shanghai.aliyuncs.com",
	  "ip": "************",
	  "id": "a1d47f56-514b-4213-b335-bcd869d315e5",
	  "env": "pre",
	  "checkOnline": false
	}`
}

func HlsHeartbeatReq() string {
	return `{
	  "endpoint": "oss-cn-shanghai.aliyuncs.com",
	  "id": "a1d47f56-514b-4213-b335-bcd869d315e5",
	  "metrics": {
		"sys.cpu.sy": 1.0,
		"io.outbound.vpc": 0,
		"io.outbound.all": 0,
		"sys.cpu.id": 97.0
	  },
	  "busy": true
	}`
}

func HlsUnregisterReq() string {
	return `{
	  "id": "a1d47f56-514b-4213-b335-bcd869d315e5"
	}`
}

func HlsOnlineReq() string {
	return `{
	  "id": "a1d47f56-514b-4213-b335-bcd869d315e5"
	}`
}

func HlsOfflineReq() string {
	return `{
	  "id": "a1d47f56-514b-4213-b335-bcd869d315e5"
	}`
}

func LiveTranscodeRegisterReq() string {
	return `{
	  "ip":"************",
	  "quota":6400,
	  "tag":"livetranscode_hdreci_pub",
	  "uuid":"98b8649c-f773-3401-8ac9-73a680bfc585"
	}`
}

func LiveTranscodeHeartbeatReq() string {
	return `{
	  "realQuota": 100,
	  "currentQuota": 100,
	  "load1": 1,
	  "uuid":"98b8649c-f773-3401-8ac9-73a680bfc585"
	}`
}

func LiveTranscodeUnregisterReq() string {
	return `{
	  "ip":"************",
	  "uuid":"98b8649c-f773-3401-8ac9-73a680bfc585"
	}`
}
