package testdata

import (
	"fmt"

	"mpp/internal/taskscheduler/biz"
	"mpp/internal/taskscheduler/data"
)

func Lock() *biz.Mutex {
	lock := &biz.Mutex{
		Name:   "test-lock",
		Owner:  "owner-1",
		Second: 1,
	}
	return lock
}

func LockList(len int) []*biz.Mutex {
	locks := make([]*biz.Mutex, 0)
	for i := 0; i < len; i++ {
		var second uint
		if i < 5 {
			second = 1
		} else {
			second = data.DefaultMutexExpiredTime
		}
		lock := &biz.Mutex{
			Name:   fmt.Sprintf("test-lock-batch-%d", i),
			Owner:  fmt.Sprintf("test-batch-owner-%d", i),
			Second: second,
		}

		locks = append(locks, lock)
	}

	return locks
}
