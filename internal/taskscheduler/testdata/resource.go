package testdata

import (
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	v1 "proto.mpp/api/common/v1"

	"mpp/internal/taskscheduler/biz"
)

func Resource() *biz.Resource {
	resource := &biz.Resource{
		JobId:  "job-1",
		TaskId: "tasker-1",
		//Product:       "ols",
		//EngineModel:   "transcode",
		//Tag:           "livetranscode_pub",
		Quota:         map[string]int64{"cpu": 100},
		WorkerId:      "worker-1",
		WorkerVersion: 2,
		//WorkerIp:      "*******",
		//WorkerPort:    80,
		IsMaster:  1,
		RealQuota: map[string]int64{"cpu": 50},
		AvgQuota:  map[string]int64{"cpu": 80},
		Label:     map[string]string{"key": "value"},
		Affinity: &biz.Affinity{
			Required: []metav1.LabelSelectorRequirement{
				{
					Key:      "key2",
					Operator: metav1.LabelSelectorOpIn,
					Values:   []string{"value"},
				},
			},
			Preferred: []metav1.LabelSelectorRequirement{
				{
					Key:      "key2",
					Operator: metav1.LabelSelectorOpIn,
					Values:   []string{"value"},
				},
			},
		},
		Extend: map[string]interface{}{"key": "value"},
	}

	return resource
}

func ResourceList(len int) []*biz.Resource {
	resources := make([]*biz.Resource, 0)
	for i := 0; i < len; i++ {
		resource := &biz.Resource{
			JobId:  fmt.Sprintf("job-batch-%v", i),
			TaskId: fmt.Sprintf("tasker-batch-%v", i),
			//Product:       "ols",
			//EngineModel:   "transcode",
			//Tag:           "livetranscode_batch_pub",
			Quota:         map[string]int64{"cpu": 200 + int64(i)},
			WorkerId:      fmt.Sprintf("worker-batch-%v", i),
			WorkerVersion: 3,
			//WorkerIp:      fmt.Sprintf("2.2.2.%v", i),
			//WorkerPort:    100 + int32(i),
			IsMaster:  1,
			RealQuota: map[string]int64{"cpu": 150 + int64(i)},
			AvgQuota:  map[string]int64{"cpu": 180 + int64(i)},
			Label:     map[string]string{"batch-key": fmt.Sprintf("batch-value-%v", i)},
			Affinity: &biz.Affinity{
				Required: []metav1.LabelSelectorRequirement{
					{
						Key:      "key2",
						Operator: metav1.LabelSelectorOpIn,
						Values:   []string{fmt.Sprintf("batch-exp-required-%v", i)},
					},
				},
				Preferred: []metav1.LabelSelectorRequirement{
					{
						Key:      "key2",
						Operator: metav1.LabelSelectorOpIn,
						Values:   []string{fmt.Sprintf("batch-exp-preferred-%v", i)},
					},
				},
			},
			Extend:   map[string]interface{}{"key": fmt.Sprintf("extend-%v", i)},
			TaskType: v1.TaskType_Async,
		}

		resources = append(resources, resource)
	}

	return resources
}
