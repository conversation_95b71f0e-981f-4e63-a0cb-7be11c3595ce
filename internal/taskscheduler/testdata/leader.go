package testdata

import (
	"fmt"

	"mpp/internal/taskscheduler/biz"
)

func Leader() *biz.Leader {
	leader := &biz.Leader{
		Key:      "scheduler",
		Instance: "127.0.0.1",
		Version:  0,
	}
	return leader
}

func LeaderList(len int) []*biz.Leader {
	leaders := make([]*biz.Leader, 0)
	for i := 0; i < len; i++ {
		leader := &biz.Leader{
			Key:      "scheduler",
			Instance: fmt.Sprintf("127.0.0.%d", i+1),
			Version:  0,
		}

		leaders = append(leaders, leader)
	}

	return leaders
}
