package testdata

import (
	"fmt"
	"mpp/internal/taskscheduler/biz"
	"time"
)

/**
 * @Author: qinxin
 * @Date: 2025/4/1 10:53
 * @Desc:
 * @Version 1.0
 */

func ChildWorker() *biz.ChildWorker {
	worker := &biz.ChildWorker{
		ParentProduct:     "parent-ols",
		ParentTag:         "parent-tag-1",
		ParentEngineModel: "parent-transcode",
		ParentWorkerId:    "parent-worker-uuid-1",
		WorkerId:          "worker-uuid-1",
		Ip:                "*******",
		PodName:           "pod-name-1",
		NodeName:          "node-name-1",
		Port:              80,
		Product:           "ols",
		EngineModel:       "transcode",
		Tag:               "livetranscode_pub",
		Load:              1,
		OnlineVersion:     0,
		Quota:             map[string]int64{"cpu": 3200},
		AllocQuota:        map[string]int64{"cpu": 200},
		RealQuota:         map[string]int64{"cpu": 100},
		AvgQuota:          map[string]int64{"cpu": 130},
		ScheduleQuota:     map[string]int64{"cpu": 3200},
		Label:             map[string]string{"app": "livetranscode_pub"},
		Taint: []*biz.Taint{
			{
				Key:    "key_taint_1",
				Value:  "value_taint_1",
				Effect: "NoSchedule",
			},
			{
				Key:    "key_taint_2",
				Value:  "value_taint_2",
				Effect: "NoSchedule",
			},
		},
		HeartbeatTime:   time.Now(),
		LatestAllocTime: time.Now(),
		Extend:          map[string]interface{}{"extend": "test"},
	}

	return worker
}

func ChildWorkerList(len int) []*biz.ChildWorker {
	workers := make([]*biz.ChildWorker, 0)
	for i := 0; i < len; i++ {
		worker := &biz.ChildWorker{
			ParentProduct:     "parent-ols",
			ParentTag:         "parent-tag-1",
			ParentEngineModel: "parent-transcode",
			ParentWorkerId:    fmt.Sprintf("parent-worker-uuid-batch-%v", i),
			WorkerId:          fmt.Sprintf("worker-uuid-batch-%v", i),
			Ip:                fmt.Sprintf("2.2.2.%v", i),
			PodName:           fmt.Sprintf("pod-name-batch-%v", i),
			NodeName:          fmt.Sprintf("node-name-batch-%v", i),
			Port:              90 + int32(i),
			Product:           "ols",
			EngineModel:       "transcode",
			Tag:               "livetranscode_pub_batch",
			Load:              2 + float32(i),
			OnlineVersion:     0,
			Quota:             map[string]int64{"cpu": 3200},
			AllocQuota:        map[string]int64{"cpu": 200 + int64(i)},
			RealQuota:         map[string]int64{"cpu": 100 + int64(i)},
			AvgQuota:          map[string]int64{"cpu": 130 + int64(i)},
			ScheduleQuota:     map[string]int64{"cpu": 3000 - int64(i)},
			Label:             map[string]string{"app": fmt.Sprintf("livetranscode_pub_%v", i)},
			Taint: []*biz.Taint{
				&biz.Taint{
					Key:    "taint",
					Value:  fmt.Sprintf("test_%v", i),
					Effect: "NoSchedule",
				},
			},
			HeartbeatTime:   time.Now(),
			LatestAllocTime: time.Now(),
			Extend:          map[string]interface{}{"extend": fmt.Sprintf("test_%v", i)},
			Metadata:        map[string]string{"meta_key": fmt.Sprintf("meta_%v", i)},
		}
		workers = append(workers, worker)
	}

	return workers
}
