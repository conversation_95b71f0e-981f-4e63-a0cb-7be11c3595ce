// Code generated by MockGen. DO NOT EDIT.
// Source: mpp/internal/taskscheduler/biz (interfaces: WaterLevelRepo)

// Package mrepo is a generated GoMock package.
package mrepo

import (
	context "context"
	biz "mpp/internal/taskscheduler/biz"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockWaterLevelRepo is a mock of WaterLevelRepo interface.
type MockWaterLevelRepo struct {
	ctrl     *gomock.Controller
	recorder *MockWaterLevelRepoMockRecorder
}

// MockWaterLevelRepoMockRecorder is the mock recorder for MockWaterLevelRepo.
type MockWaterLevelRepoMockRecorder struct {
	mock *MockWaterLevelRepo
}

// NewMockWaterLevelRepo creates a new mock instance.
func NewMockWaterLevelRepo(ctrl *gomock.Controller) *MockWaterLevelRepo {
	mock := &MockWaterLevelRepo{ctrl: ctrl}
	mock.recorder = &MockWaterLevelRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWaterLevelRepo) EXPECT() *MockWaterLevelRepoMockRecorder {
	return m.recorder
}

// FindLatest mocks base method.
func (m *MockWaterLevelRepo) FindLatest(arg0 context.Context, arg1 *biz.WaterLevel) (*biz.WaterLevel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindLatest", arg0, arg1)
	ret0, _ := ret[0].(*biz.WaterLevel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindLatest indicates an expected call of FindLatest.
func (mr *MockWaterLevelRepoMockRecorder) FindLatest(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindLatest", reflect.TypeOf((*MockWaterLevelRepo)(nil).FindLatest), arg0, arg1)
}

// ListAll mocks base method.
func (m *MockWaterLevelRepo) ListAll(arg0 context.Context, arg1 *biz.WaterLevel, arg2 int) ([]*biz.WaterLevel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAll", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*biz.WaterLevel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAll indicates an expected call of ListAll.
func (mr *MockWaterLevelRepoMockRecorder) ListAll(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAll", reflect.TypeOf((*MockWaterLevelRepo)(nil).ListAll), arg0, arg1, arg2)
}

// Save mocks base method.
func (m *MockWaterLevelRepo) Save(arg0 context.Context, arg1 *biz.WaterLevel) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockWaterLevelRepoMockRecorder) Save(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockWaterLevelRepo)(nil).Save), arg0, arg1)
}
