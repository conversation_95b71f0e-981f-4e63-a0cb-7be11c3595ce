// Code generated by MockGen. DO NOT EDIT.
// Source: mpp/internal/taskscheduler/biz (interfaces: StrategyRepo)

// Package mrepo is a generated GoMock package.
package mrepo

import (
	context "context"
	biz "mpp/internal/taskscheduler/biz"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockStrategyRepo is a mock of StrategyRepo interface.
type MockStrategyRepo struct {
	ctrl     *gomock.Controller
	recorder *MockStrategyRepoMockRecorder
}

// MockStrategyRepoMockRecorder is the mock recorder for MockStrategyRepo.
type MockStrategyRepoMockRecorder struct {
	mock *MockStrategyRepo
}

// NewMockStrategyRepo creates a new mock instance.
func NewMockStrategyRepo(ctrl *gomock.Controller) *MockStrategyRepo {
	mock := &MockStrategyRepo{ctrl: ctrl}
	mock.recorder = &MockStrategyRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStrategyRepo) EXPECT() *MockStrategyRepoMockRecorder {
	return m.recorder
}

// FindByStrategyId mocks base method.
func (m *MockStrategyRepo) FindByStrategyId(arg0 context.Context, arg1 string) (*biz.Strategy, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByStrategyId", arg0, arg1)
	ret0, _ := ret[0].(*biz.Strategy)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByStrategyId indicates an expected call of FindByStrategyId.
func (mr *MockStrategyRepoMockRecorder) FindByStrategyId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByStrategyId", reflect.TypeOf((*MockStrategyRepo)(nil).FindByStrategyId), arg0, arg1)
}

// ListAll mocks base method.
func (m *MockStrategyRepo) ListAll(arg0 context.Context) ([]*biz.Strategy, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAll", arg0)
	ret0, _ := ret[0].([]*biz.Strategy)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAll indicates an expected call of ListAll.
func (mr *MockStrategyRepoMockRecorder) ListAll(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAll", reflect.TypeOf((*MockStrategyRepo)(nil).ListAll), arg0)
}

// Save mocks base method.
func (m *MockStrategyRepo) Save(arg0 context.Context, arg1 *biz.Strategy) (*biz.Strategy, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", arg0, arg1)
	ret0, _ := ret[0].(*biz.Strategy)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Save indicates an expected call of Save.
func (mr *MockStrategyRepoMockRecorder) Save(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockStrategyRepo)(nil).Save), arg0, arg1)
}

// Update mocks base method.
func (m *MockStrategyRepo) Update(arg0 context.Context, arg1 *biz.Strategy) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockStrategyRepoMockRecorder) Update(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockStrategyRepo)(nil).Update), arg0, arg1)
}

// UpdateStatus mocks base method.
func (m *MockStrategyRepo) UpdateStatus(arg0 context.Context, arg1 string, arg2 int8) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatus indicates an expected call of UpdateStatus.
func (mr *MockStrategyRepoMockRecorder) UpdateStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatus", reflect.TypeOf((*MockStrategyRepo)(nil).UpdateStatus), arg0, arg1, arg2)
}
