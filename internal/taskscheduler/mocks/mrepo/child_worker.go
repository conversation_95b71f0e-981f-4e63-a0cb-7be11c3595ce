package mrepo

import (
	"context"
	"reflect"

	"mpp/internal/taskscheduler/biz"

	"github.com/golang/mock/gomock"
)

// MockChildWorkerRepo is a mock of ChildWorkerRepo interface.
type MockChildWorkerRepo struct {
	ctrl     *gomock.Controller
	recorder *MockChildWorkerRepoMockRecorder
}

// MockChildWorkerRepoMockRecorder is the mock recorder for MockChildWorkerRepo.
type MockChildWorkerRepoMockRecorder struct {
	mock *MockChildWorkerRepo
}

// NewMockChildWorkerRepo creates a new mock instance.
func NewMockChildWorkerRepo(ctrl *gomock.Controller) *MockChildWorkerRepo {
	mock := &MockChildWorkerRepo{ctrl: ctrl}
	mock.recorder = &MockChildWorkerRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChildWorkerRepo) EXPECT() *MockChildWorkerRepoMockRecorder {
	return m.recorder
}

// Save mocks base method.
func (m *MockChildWorkerRepo) Save(arg0 context.Context, arg1 *biz.ChildWorker) (*biz.ChildWorker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", arg0, arg1)
	ret0, _ := ret[0].(*biz.ChildWorker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Save indicates an expected call of Save.
func (mr *MockChildWorkerRepoMockRecorder) Save(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockChildWorkerRepo)(nil).Save), arg0, arg1)
}

// FindByWorkerId(context.Context, string, int8) (*ChildWorker, error)
func (m *MockChildWorkerRepo) FindByWorkerId(ctx context.Context, workerId string, status int8) (*biz.ChildWorker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByWorkerId", ctx, workerId, status)
	ret0, _ := ret[0].(*biz.ChildWorker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}
func (mr *MockChildWorkerRepoMockRecorder) FindByWorkerId(ctx, workerId, status interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByWorkerId", reflect.TypeOf((*MockChildWorkerRepo)(nil).FindByWorkerId), ctx, workerId, status)
}

// UpdateHeartbeat mocks base method.
func (m *MockChildWorkerRepo) UpdateHeartbeat(arg0 context.Context, arg1 *biz.ChildWorker, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateHeartbeat", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateHeartbeat indicates an expected call of UpdateHeartbeat.
func (mr *MockChildWorkerRepoMockRecorder) UpdateHeartbeat(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateHeartbeat", reflect.TypeOf((*MockChildWorkerRepo)(nil).UpdateHeartbeat), arg0, arg1, arg2)
}

// UpdateStatus mocks base method.
func (m *MockChildWorkerRepo) UpdateStatus(arg0 context.Context, arg1 string, arg2 int8) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatus indicates an expected call of UpdateStatus.
func (mr *MockChildWorkerRepoMockRecorder) UpdateStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatus", reflect.TypeOf((*MockChildWorkerRepo)(nil).UpdateStatus), arg0, arg1, arg2)
}

// ListByParentWorkerId mocks base method.
func (m *MockChildWorkerRepo) ListByParentWorkerId(arg0 context.Context, arg1 string) ([]*biz.ChildWorker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByParentWorkerId", arg0, arg1)
	ret0, _ := ret[0].([]*biz.ChildWorker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByParentWorkerId indicates an expected call of ListByParentWorkerId.
func (mr *MockChildWorkerRepoMockRecorder) ListByParentWorkerId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByParentWorkerId", reflect.TypeOf((*MockChildWorkerRepo)(nil).ListByParentWorkerId), arg0, arg1)
}

// UpdateQuota(context.Context, *ChildWorker, string) error
func (m *MockChildWorkerRepo) UpdateQuota(ctx context.Context, w *biz.ChildWorker, opt string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateQuota", ctx, w, opt)
	ret0, _ := ret[0].(error)
	return ret0
}
func (mr *MockChildWorkerRepoMockRecorder) UpdateQuota(ctx, w, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateQuota", reflect.TypeOf((*MockChildWorkerRepo)(nil).UpdateQuota), ctx, w, opt)
}

// UpdateChangeQuota(ctx context.Context, workerId string, quota QuotaSet, scheduleConfig *StrategyConfig, opt string) (bool, error)
func (m *MockChildWorkerRepo) UpdateChangeQuota(ctx context.Context, workerId string, quota biz.QuotaSet, scheduleConfig *biz.StrategyConfig, opt string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateChangeQuota", ctx, workerId, quota, scheduleConfig, opt)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}
func (mr *MockChildWorkerRepoMockRecorder) UpdateChangeQuota(ctx, workerId, quota, scheduleConfig, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChangeQuota", reflect.TypeOf((*MockChildWorkerRepo)(nil).UpdateChangeQuota), ctx, workerId, quota, scheduleConfig, opt)
}

// BatchUpdateStatus(context.Context, []string, int8) error
func (m *MockChildWorkerRepo) BatchUpdateStatus(ctx context.Context, workerIds []string, status int8) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateStatus", ctx, workerIds, status)
	ret0, _ := ret[0].(error)
	return ret0
}
func (mr *MockChildWorkerRepoMockRecorder) BatchUpdateStatus(ctx, workerIds, status interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateStatus", reflect.TypeOf((*MockChildWorkerRepo)(nil).BatchUpdateStatus), ctx, workerIds, status)
}

// BatchUpdateStatusByParentWorkerIds(ctx context.Context, parentWorkerIds []string, status int8) error
func (m *MockChildWorkerRepo) BatchUpdateStatusByParentWorkerIds(ctx context.Context, parentWorkerIds []string, status int8) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateStatusByParentWorkerIds", ctx, parentWorkerIds, status)
	ret0, _ := ret[0].(error)
	return ret0
}
func (mr *MockChildWorkerRepoMockRecorder) BatchUpdateStatusByParentWorkerIds(ctx, parentWorkerIds, status interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateStatusByParentWorkerIds", reflect.TypeOf((*MockChildWorkerRepo)(nil).BatchUpdateStatusByParentWorkerIds), ctx, parentWorkerIds, status)
}

// UpdateLabel(context.Context, *ChildWorker) error
func (m *MockChildWorkerRepo) UpdateLabel(ctx context.Context, w *biz.ChildWorker) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLabel", ctx, w)
	ret0, _ := ret[0].(error)
	return ret0
}
func (mr *MockChildWorkerRepoMockRecorder) UpdateLabel(ctx, w interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLabel", reflect.TypeOf((*MockChildWorkerRepo)(nil).UpdateLabel), ctx, w)
}

// UpdateTaint(context.Context, []string, []string, []*Taint) error
func (m *MockChildWorkerRepo) UpdateTaint(ctx context.Context, workerIds []string, workerIps []string, taint []*biz.Taint) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTaint", ctx, workerIds, workerIps, taint)
	ret0, _ := ret[0].(error)
	return ret0
}
func (mr *MockChildWorkerRepoMockRecorder) UpdateTaint(ctx, workerIds, workerIps, taint interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaint", reflect.TypeOf((*MockChildWorkerRepo)(nil).UpdateTaint), ctx, workerIds, workerIps, taint)
}

// UpdateTagByIps(context.Context, []string, string) error
func (m *MockChildWorkerRepo) UpdateTagByIps(ctx context.Context, ips []string, tag string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTagByIps", ctx, ips, tag)
	ret0, _ := ret[0].(error)
	return ret0
}
func (mr *MockChildWorkerRepoMockRecorder) UpdateTagByIps(ctx, ips, tag interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTagByIps", reflect.TypeOf((*MockChildWorkerRepo)(nil).UpdateTagByIps), ctx, ips, tag)
}

// UpdateTagByWorkerIds(context.Context, []string, string) error
func (m *MockChildWorkerRepo) UpdateTagByWorkerIds(ctx context.Context, workerIds []string, tag string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTagByWorkerIds", ctx, workerIds, tag)
	ret0, _ := ret[0].(error)
	return ret0
}

// FindByIp(context.Context, string) (*ChildWorker, error)
func (m *MockChildWorkerRepo) FindByIp(ctx context.Context, ip string) (*biz.ChildWorker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByIp", ctx, ip)
	ret0, _ := ret[0].(*biz.ChildWorker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockChildWorkerRepoMockRecorder) FindByIp(ctx, ip interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByIp", reflect.TypeOf((*MockChildWorkerRepo)(nil).FindByIp), ctx, ip)
}

// ListOnlineWorker(context.Context) ([]*ChildWorker, error)
func (m *MockChildWorkerRepo) ListOnlineWorker(ctx context.Context) ([]*biz.ChildWorker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListOnlineWorker", ctx)
	ret0, _ := ret[0].([]*biz.ChildWorker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}
func (mr *MockChildWorkerRepoMockRecorder) ListOnlineWorker(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOnlineWorker", reflect.TypeOf((*MockChildWorkerRepo)(nil).ListOnlineWorker), ctx)
}

// ListByConditions(context.Context, string, string, string, []string) ([]*ChildWorker, error)
func (m *MockChildWorkerRepo) ListByConditions(ctx context.Context, product string, engineModel string, tag string, ips []string) ([]*biz.ChildWorker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByConditions", ctx, product, engineModel, tag, ips)
	ret0, _ := ret[0].([]*biz.ChildWorker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}
func (mr *MockChildWorkerRepoMockRecorder) ListByConditions(ctx, product, engineModel, tag, ips interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByConditions", reflect.TypeOf((*MockChildWorkerRepo)(nil).ListByConditions), ctx, product, engineModel, tag, ips)
}

// ListByWorkerIds(context.Context, []string) ([]*ChildWorker, error)
func (m *MockChildWorkerRepo) ListByWorkerIds(ctx context.Context, workerIds []string) ([]*biz.ChildWorker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByWorkerIds", ctx, workerIds)
	ret0, _ := ret[0].([]*biz.ChildWorker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}
func (mr *MockChildWorkerRepoMockRecorder) ListByWorkerIds(ctx, workerIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByWorkerIds", reflect.TypeOf((*MockChildWorkerRepo)(nil).ListByWorkerIds), ctx, workerIds)
}

// ListByIps(context.Context, []string) ([]*ChildWorker, error)
func (m *MockChildWorkerRepo) ListByIps(ctx context.Context, ips []string) ([]*biz.ChildWorker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByIps", ctx, ips)
	ret0, _ := ret[0].([]*biz.ChildWorker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}
func (mr *MockChildWorkerRepoMockRecorder) ListByIps(ctx, ips interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByIps", reflect.TypeOf((*MockChildWorkerRepo)(nil).ListByIps), ctx, ips)
}
