// Code generated by MockGen. DO NOT EDIT.
// Source: mpp/internal/taskscheduler/biz (interfaces: TaskManagerApi)

// Package mrepo is a generated GoMock package.
package mrepo

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockTaskManagerApi is a mock of TaskManagerApi interface.
type MockTaskManagerApi struct {
	ctrl     *gomock.Controller
	recorder *MockTaskManagerApiMockRecorder
}

// MockTaskManagerApiMockRecorder is the mock recorder for MockTaskManagerApi.
type MockTaskManagerApiMockRecorder struct {
	mock *MockTaskManagerApi
}

// NewMockTaskManagerApi creates a new mock instance.
func NewMockTaskManagerApi(ctrl *gomock.Controller) *MockTaskManagerApi {
	mock := &MockTaskManagerApi{ctrl: ctrl}
	mock.recorder = &MockTaskManagerApiMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTaskManagerApi) EXPECT() *MockTaskManagerApiMockRecorder {
	return m.recorder
}

// MigrateTasks mocks base method.
func (m *MockTaskManagerApi) MigrateTasks(arg0 context.Context, arg1 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MigrateTasks", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// MigrateTasks indicates an expected call of MigrateTasks.
func (mr *MockTaskManagerApiMockRecorder) MigrateTasks(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MigrateTasks", reflect.TypeOf((*MockTaskManagerApi)(nil).MigrateTasks), arg0, arg1)
}
