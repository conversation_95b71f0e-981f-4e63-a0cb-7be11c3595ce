// Code generated by MockGen. DO NOT EDIT.
// Source: proto.mpp/api/taskscheduler/v1 (interfaces: ScheduleClient)

// Package mrepo is a generated GoMock package.
package mrepo

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	v1 "proto.mpp/api/taskscheduler/v1"
)

// MockScheduleClient is a mock of ScheduleClient interface.
type MockScheduleClient struct {
	ctrl     *gomock.Controller
	recorder *MockScheduleClientMockRecorder
}

// MockScheduleClientMockRecorder is the mock recorder for MockScheduleClient.
type MockScheduleClientMockRecorder struct {
	mock *MockScheduleClient
}

// NewMockScheduleClient creates a new mock instance.
func NewMockScheduleClient(ctrl *gomock.Controller) *MockScheduleClient {
	mock := &MockScheduleClient{ctrl: ctrl}
	mock.recorder = &MockScheduleClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockScheduleClient) EXPECT() *MockScheduleClientMockRecorder {
	return m.recorder
}

// AllocMasterResource mocks base method.
func (m *MockScheduleClient) AllocMasterResource(arg0 context.Context, arg1 *v1.AllocMasterResourceRequest, arg2 ...grpc.CallOption) (*v1.AllocMasterResourceReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AllocMasterResource", varargs...)
	ret0, _ := ret[0].(*v1.AllocMasterResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AllocMasterResource indicates an expected call of AllocMasterResource.
func (mr *MockScheduleClientMockRecorder) AllocMasterResource(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllocMasterResource", reflect.TypeOf((*MockScheduleClient)(nil).AllocMasterResource), varargs...)
}

// AllocResource mocks base method.
func (m *MockScheduleClient) AllocResource(arg0 context.Context, arg1 *v1.AllocResourceRequest, arg2 ...grpc.CallOption) (*v1.AllocResourceReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AllocResource", varargs...)
	ret0, _ := ret[0].(*v1.AllocResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AllocResource indicates an expected call of AllocResource.
func (mr *MockScheduleClientMockRecorder) AllocResource(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllocResource", reflect.TypeOf((*MockScheduleClient)(nil).AllocResource), varargs...)
}

// FreeMasterResource mocks base method.
func (m *MockScheduleClient) FreeMasterResource(arg0 context.Context, arg1 *v1.FreeMasterResourceRequest, arg2 ...grpc.CallOption) (*v1.FreeMasterResourceReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FreeMasterResource", varargs...)
	ret0, _ := ret[0].(*v1.FreeMasterResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreeMasterResource indicates an expected call of FreeMasterResource.
func (mr *MockScheduleClientMockRecorder) FreeMasterResource(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreeMasterResource", reflect.TypeOf((*MockScheduleClient)(nil).FreeMasterResource), varargs...)
}

// FreeResource mocks base method.
func (m *MockScheduleClient) FreeResource(arg0 context.Context, arg1 *v1.FreeResourceRequest, arg2 ...grpc.CallOption) (*v1.FreeResourceReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FreeResource", varargs...)
	ret0, _ := ret[0].(*v1.FreeResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreeResource indicates an expected call of FreeResource.
func (mr *MockScheduleClientMockRecorder) FreeResource(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreeResource", reflect.TypeOf((*MockScheduleClient)(nil).FreeResource), varargs...)
}

// GetMasterWorker mocks base method.
func (m *MockScheduleClient) GetMasterWorker(arg0 context.Context, arg1 *v1.GetMasterWorkerRequest, arg2 ...grpc.CallOption) (*v1.GetMasterWorkerReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMasterWorker", varargs...)
	ret0, _ := ret[0].(*v1.GetMasterWorkerReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMasterWorker indicates an expected call of GetMasterWorker.
func (mr *MockScheduleClientMockRecorder) GetMasterWorker(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMasterWorker", reflect.TypeOf((*MockScheduleClient)(nil).GetMasterWorker), varargs...)
}

// UpdateMasterResource mocks base method.
func (m *MockScheduleClient) UpdateMasterResource(arg0 context.Context, arg1 *v1.UpdateMasterResourceRequest, arg2 ...grpc.CallOption) (*v1.UpdateMasterResourceReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateMasterResource", varargs...)
	ret0, _ := ret[0].(*v1.UpdateMasterResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateMasterResource indicates an expected call of UpdateMasterResource.
func (mr *MockScheduleClientMockRecorder) UpdateMasterResource(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMasterResource", reflect.TypeOf((*MockScheduleClient)(nil).UpdateMasterResource), varargs...)
}

// UpdateResource mocks base method.
func (m *MockScheduleClient) UpdateResource(arg0 context.Context, arg1 *v1.UpdateResourceRequest, arg2 ...grpc.CallOption) (*v1.UpdateResourceReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateResource", varargs...)
	ret0, _ := ret[0].(*v1.UpdateResourceReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateResource indicates an expected call of UpdateResource.
func (mr *MockScheduleClientMockRecorder) UpdateResource(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateResource", reflect.TypeOf((*MockScheduleClient)(nil).UpdateResource), varargs...)
}
