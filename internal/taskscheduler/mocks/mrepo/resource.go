// Code generated by MockGen. DO NOT EDIT.
// Source: internal/taskscheduler/biz/resource.go

// Package mrepo is a generated GoMock package.
package mrepo

import (
	context "context"
	biz "mpp/internal/taskscheduler/biz"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	v1 "proto.mpp/api/common/v1"
)

// MockResourceRepo is a mock of ResourceRepo interface.
type MockResourceRepo struct {
	ctrl     *gomock.Controller
	recorder *MockResourceRepoMockRecorder
}

// MockResourceRepoMockRecorder is the mock recorder for MockResourceRepo.
type MockResourceRepoMockRecorder struct {
	mock *MockResourceRepo
}

// NewMockResourceRepo creates a new mock instance.
func NewMockResourceRepo(ctrl *gomock.Controller) *MockResourceRepo {
	mock := &MockResourceRepo{ctrl: ctrl}
	mock.recorder = &MockResourceRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockResourceRepo) EXPECT() *MockResourceRepoMockRecorder {
	return m.recorder
}

// FindByTaskId mocks base method.
func (m *MockResourceRepo) FindByTaskId(arg0 context.Context, arg1 string) (*biz.Resource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByTaskId", arg0, arg1)
	ret0, _ := ret[0].(*biz.Resource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByTaskId indicates an expected call of FindByTaskId.
func (mr *MockResourceRepoMockRecorder) FindByTaskId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByTaskId", reflect.TypeOf((*MockResourceRepo)(nil).FindByTaskId), arg0, arg1)
}

// FindByWorker mocks base method.
func (m *MockResourceRepo) FindByWorker(arg0 context.Context, arg1 *biz.Worker) ([]*biz.Resource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByWorker", arg0, arg1)
	ret0, _ := ret[0].([]*biz.Resource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByWorker indicates an expected call of FindByWorker.
func (mr *MockResourceRepoMockRecorder) FindByWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByWorker", reflect.TypeOf((*MockResourceRepo)(nil).FindByWorker), arg0, arg1)
}

// ListAll mocks base method.
func (m *MockResourceRepo) ListAll(arg0 context.Context, arg1 *v1.TaskType) ([]*biz.Resource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAll", arg0, arg1)
	ret0, _ := ret[0].([]*biz.Resource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAll indicates an expected call of ListAll.
func (mr *MockResourceRepoMockRecorder) ListAll(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAll", reflect.TypeOf((*MockResourceRepo)(nil).ListAll), arg0, arg1)
}

// ListLost mocks base method.
func (m *MockResourceRepo) ListLost(arg0 context.Context, arg1 *v1.TaskType) ([]*biz.Resource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListLost", arg0, arg1)
	ret0, _ := ret[0].([]*biz.Resource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListLost indicates an expected call of ListLost.
func (mr *MockResourceRepoMockRecorder) ListLost(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListLost", reflect.TypeOf((*MockResourceRepo)(nil).ListLost), arg0, arg1)
}

// Save mocks base method.
func (m *MockResourceRepo) Save(arg0 context.Context, arg1 *biz.Resource) (*biz.Resource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", arg0, arg1)
	ret0, _ := ret[0].(*biz.Resource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Save indicates an expected call of Save.
func (mr *MockResourceRepoMockRecorder) Save(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockResourceRepo)(nil).Save), arg0, arg1)
}

// UpdateQuota mocks base method.
func (m *MockResourceRepo) UpdateQuota(arg0 context.Context, arg1 *biz.Resource) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateQuota", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateQuota indicates an expected call of UpdateQuota.
func (mr *MockResourceRepoMockRecorder) UpdateQuota(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateQuota", reflect.TypeOf((*MockResourceRepo)(nil).UpdateQuota), arg0, arg1)
}

// UpdateStatus mocks base method.
func (m *MockResourceRepo) UpdateStatus(arg0 context.Context, arg1 string, arg2 int8) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatus indicates an expected call of UpdateStatus.
func (mr *MockResourceRepoMockRecorder) UpdateStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatus", reflect.TypeOf((*MockResourceRepo)(nil).UpdateStatus), arg0, arg1, arg2)
}

//// MockTaskManagerApi is a mock of TaskManagerApi interface.
//type MockTaskManagerApi struct {
//	ctrl     *gomock.Controller
//	recorder *MockTaskManagerApiMockRecorder
//}
//
//// MockTaskManagerApiMockRecorder is the mock recorder for MockTaskManagerApi.
//type MockTaskManagerApiMockRecorder struct {
//	mock *MockTaskManagerApi
//}
//
//// NewMockTaskManagerApi creates a new mock instance.
//func NewMockTaskManagerApi(ctrl *gomock.Controller) *MockTaskManagerApi {
//	mock := &MockTaskManagerApi{ctrl: ctrl}
//	mock.recorder = &MockTaskManagerApiMockRecorder{mock}
//	return mock
//}
//
//// EXPECT returns an object that allows the caller to indicate expected use.
//func (m *MockTaskManagerApi) EXPECT() *MockTaskManagerApiMockRecorder {
//	return m.recorder
//}
//
//// MigrateTasks mocks base method.
//func (m *MockTaskManagerApi) MigrateTasks(arg0 context.Context, arg1 []string) error {
//	m.ctrl.T.Helper()
//	ret := m.ctrl.Call(m, "MigrateTasks", arg0, arg1)
//	ret0, _ := ret[0].(error)
//	return ret0
//}
//
//// MigrateTasks indicates an expected call of MigrateTasks.
//func (mr *MockTaskManagerApiMockRecorder) MigrateTasks(arg0, arg1 interface{}) *gomock.Call {
//	mr.mock.ctrl.T.Helper()
//	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MigrateTasks", reflect.TypeOf((*MockTaskManagerApi)(nil).MigrateTasks), arg0, arg1)
//}
