// Code generated by MockGen. DO NOT EDIT.
// Source: internal/taskscheduler/biz/worker.go

// Package mocks is a generated GoMock package.
package mrepo

import (
	context "context"
	biz "mpp/internal/taskscheduler/biz"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockWorkerRepo is a mock of WorkerRepo interface.
type MockWorkerRepo struct {
	ctrl     *gomock.Controller
	recorder *MockWorkerRepoMockRecorder
}

// MockWorkerRepoMockRecorder is the mock recorder for MockWorkerRepo.
type MockWorkerRepoMockRecorder struct {
	mock *MockWorkerRepo
}

// NewMockWorkerRepo creates a new mock instance.
func NewMockWorkerRepo(ctrl *gomock.Controller) *MockWorkerRepo {
	mock := &MockWorkerRepo{ctrl: ctrl}
	mock.recorder = &MockWorkerRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWorkerRepo) EXPECT() *MockWorkerRepoMockRecorder {
	return m.recorder
}

// BatchUpdateStatus mocks base method.
func (m *MockWorkerRepo) BatchUpdateStatus(arg0 context.Context, arg1 []string, arg2 int8) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateStatus indicates an expected call of BatchUpdateStatus.
func (mr *MockWorkerRepoMockRecorder) BatchUpdateStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateStatus", reflect.TypeOf((*MockWorkerRepo)(nil).BatchUpdateStatus), arg0, arg1, arg2)
}

// FindByIp mocks base method.
func (m *MockWorkerRepo) FindByIp(arg0 context.Context, arg1 string) (*biz.Worker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByIp", arg0, arg1)
	ret0, _ := ret[0].(*biz.Worker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByIp indicates an expected call of FindByIp.
func (mr *MockWorkerRepoMockRecorder) FindByIp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByIp", reflect.TypeOf((*MockWorkerRepo)(nil).FindByIp), arg0, arg1)
}

// FindByWorkerId mocks base method.
func (m *MockWorkerRepo) FindByWorkerId(arg0 context.Context, arg1 string, arg2 int8) (*biz.Worker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByWorkerId", arg0, arg1, arg2)
	ret0, _ := ret[0].(*biz.Worker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByWorkerId indicates an expected call of FindByWorkerId.
func (mr *MockWorkerRepoMockRecorder) FindByWorkerId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByWorkerId", reflect.TypeOf((*MockWorkerRepo)(nil).FindByWorkerId), arg0, arg1, arg2)
}

// ListByConditions mocks base method.
func (m *MockWorkerRepo) ListByConditions(arg0 context.Context, arg1, arg2, arg3 string, arg4 []string) ([]*biz.Worker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByConditions", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*biz.Worker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByConditions indicates an expected call of ListByConditions.
func (mr *MockWorkerRepoMockRecorder) ListByConditions(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByConditions", reflect.TypeOf((*MockWorkerRepo)(nil).ListByConditions), arg0, arg1, arg2, arg3, arg4)
}

// ListByIps mocks base method.
func (m *MockWorkerRepo) ListByIps(arg0 context.Context, arg1 []string) ([]*biz.Worker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByIps", arg0, arg1)
	ret0, _ := ret[0].([]*biz.Worker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByIps indicates an expected call of ListByIps.
func (mr *MockWorkerRepoMockRecorder) ListByIps(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByIps", reflect.TypeOf((*MockWorkerRepo)(nil).ListByIps), arg0, arg1)
}

// ListByWorkerIds mocks base method.
func (m *MockWorkerRepo) ListByWorkerIds(arg0 context.Context, arg1 []string) ([]*biz.Worker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByWorkerIds", arg0, arg1)
	ret0, _ := ret[0].([]*biz.Worker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByWorkerIds indicates an expected call of ListByWorkerIds.
func (mr *MockWorkerRepoMockRecorder) ListByWorkerIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByWorkerIds", reflect.TypeOf((*MockWorkerRepo)(nil).ListByWorkerIds), arg0, arg1)
}

// ListOnlineWorker mocks base method.
func (m *MockWorkerRepo) ListOnlineWorker(arg0 context.Context) ([]*biz.Worker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListOnlineWorker", arg0)
	ret0, _ := ret[0].([]*biz.Worker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListOnlineWorker indicates an expected call of ListOnlineWorker.
func (mr *MockWorkerRepoMockRecorder) ListOnlineWorker(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOnlineWorker", reflect.TypeOf((*MockWorkerRepo)(nil).ListOnlineWorker), arg0)
}

// Save mocks base method.
func (m *MockWorkerRepo) Save(arg0 context.Context, arg1 *biz.Worker) (*biz.Worker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", arg0, arg1)
	ret0, _ := ret[0].(*biz.Worker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Save indicates an expected call of Save.
func (mr *MockWorkerRepoMockRecorder) Save(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockWorkerRepo)(nil).Save), arg0, arg1)
}

// UpdateChangeQuota mocks base method.
func (m *MockWorkerRepo) UpdateChangeQuota(ctx context.Context, workerId string, quota biz.QuotaSet, scheduleConfig *biz.StrategyConfig, opt string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateChangeQuota", ctx, workerId, quota, scheduleConfig, opt)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateChangeQuota indicates an expected call of UpdateChangeQuota.
func (mr *MockWorkerRepoMockRecorder) UpdateChangeQuota(ctx, workerId, quota, scheduleConfig, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChangeQuota", reflect.TypeOf((*MockWorkerRepo)(nil).UpdateChangeQuota), ctx, workerId, quota, scheduleConfig, opt)
}

// UpdateHeartbeat mocks base method.
func (m *MockWorkerRepo) UpdateHeartbeat(arg0 context.Context, arg1 *biz.Worker, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateHeartbeat", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateHeartbeat indicates an expected call of UpdateHeartbeat.
func (mr *MockWorkerRepoMockRecorder) UpdateHeartbeat(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateHeartbeat", reflect.TypeOf((*MockWorkerRepo)(nil).UpdateHeartbeat), arg0, arg1, arg2)
}

// UpdateLabel mocks base method.
func (m *MockWorkerRepo) UpdateLabel(arg0 context.Context, arg1 *biz.Worker) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLabel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateLabel indicates an expected call of UpdateLabel.
func (mr *MockWorkerRepoMockRecorder) UpdateLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLabel", reflect.TypeOf((*MockWorkerRepo)(nil).UpdateLabel), arg0, arg1)
}

// UpdateQuota mocks base method.
func (m *MockWorkerRepo) UpdateQuota(arg0 context.Context, arg1 *biz.Worker, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateQuota", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateQuota indicates an expected call of UpdateQuota.
func (mr *MockWorkerRepoMockRecorder) UpdateQuota(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateQuota", reflect.TypeOf((*MockWorkerRepo)(nil).UpdateQuota), arg0, arg1, arg2)
}

// UpdateStatus mocks base method.
func (m *MockWorkerRepo) UpdateStatus(arg0 context.Context, arg1 string, arg2 int8) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatus indicates an expected call of UpdateStatus.
func (mr *MockWorkerRepoMockRecorder) UpdateStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatus", reflect.TypeOf((*MockWorkerRepo)(nil).UpdateStatus), arg0, arg1, arg2)
}

// UpdateTagByIps mocks base method.
func (m *MockWorkerRepo) UpdateTagByIps(arg0 context.Context, arg1 []string, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTagByIps", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTagByIps indicates an expected call of UpdateTagByIps.
func (mr *MockWorkerRepoMockRecorder) UpdateTagByIps(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTagByIps", reflect.TypeOf((*MockWorkerRepo)(nil).UpdateTagByIps), arg0, arg1, arg2)
}

// UpdateTagByWorkerIds mocks base method.
func (m *MockWorkerRepo) UpdateTagByWorkerIds(arg0 context.Context, arg1 []string, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTagByWorkerIds", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTagByWorkerIds indicates an expected call of UpdateTagByWorkerIds.
func (mr *MockWorkerRepoMockRecorder) UpdateTagByWorkerIds(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTagByWorkerIds", reflect.TypeOf((*MockWorkerRepo)(nil).UpdateTagByWorkerIds), arg0, arg1, arg2)
}

// UpdateTaint mocks base method.
func (m *MockWorkerRepo) UpdateTaint(arg0 context.Context, arg1, arg2 []string, arg3 []*biz.Taint) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTaint", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTaint indicates an expected call of UpdateTaint.
func (mr *MockWorkerRepoMockRecorder) UpdateTaint(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaint", reflect.TypeOf((*MockWorkerRepo)(nil).UpdateTaint), arg0, arg1, arg2, arg3)
}

// MockWorkerApi is a mock of WorkerApi interface.
type MockWorkerApi struct {
	ctrl     *gomock.Controller
	recorder *MockWorkerApiMockRecorder
}

// MockWorkerApiMockRecorder is the mock recorder for MockWorkerApi.
type MockWorkerApiMockRecorder struct {
	mock *MockWorkerApi
}

// NewMockWorkerApi creates a new mock instance.
func NewMockWorkerApi(ctrl *gomock.Controller) *MockWorkerApi {
	mock := &MockWorkerApi{ctrl: ctrl}
	mock.recorder = &MockWorkerApiMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWorkerApi) EXPECT() *MockWorkerApiMockRecorder {
	return m.recorder
}

// CheckOnline mocks base method.
func (m *MockWorkerApi) CheckOnline(arg0 context.Context, arg1 *biz.Worker) (bool, interface{}) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckOnline", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(interface{})
	return ret0, ret1
}

// CheckOnline indicates an expected call of CheckOnline.
func (mr *MockWorkerApiMockRecorder) CheckOnline(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckOnline", reflect.TypeOf((*MockWorkerApi)(nil).CheckOnline), arg0, arg1)
}
