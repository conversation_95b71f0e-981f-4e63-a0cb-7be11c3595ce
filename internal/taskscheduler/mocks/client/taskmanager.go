// Code generated by MockGen. DO NOT EDIT.
// Source: proto.mpp/api/taskmanager/v1 (interfaces: TaskHTTPClient)

// Package mclient is a generated GoMock package.
package mclient

import (
	context "context"
	reflect "reflect"

	http "github.com/go-kratos/kratos/v2/transport/http"
	gomock "github.com/golang/mock/gomock"
	v1 "proto.mpp/api/taskmanager/v1"
)

// MockTaskHTTPClient is a mock of TaskHTTPClient interface.
type MockTaskHTTPClient struct {
	ctrl     *gomock.Controller
	recorder *MockTaskHTTPClientMockRecorder
}

// MockTaskHTTPClientMockRecorder is the mock recorder for MockTaskHTTPClient.
type MockTaskHTTPClientMockRecorder struct {
	mock *MockTaskHTTPClient
}

// NewMockTaskHTTPClient creates a new mock instance.
func NewMockTaskHTTPClient(ctrl *gomock.Controller) *MockTaskHTTPClient {
	mock := &MockTaskHTTPClient{ctrl: ctrl}
	mock.recorder = &MockTaskHTTPClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTaskHTTPClient) EXPECT() *MockTaskHTTPClientMockRecorder {
	return m.recorder
}

// CancelTask mocks base method.
func (m *MockTaskHTTPClient) CancelTask(arg0 context.Context, arg1 *v1.CancelTaskRequest, arg2 ...http.CallOption) (*v1.CancelTaskReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelTask", varargs...)
	ret0, _ := ret[0].(*v1.CancelTaskReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelTask indicates an expected call of CancelTask.
func (mr *MockTaskHTTPClientMockRecorder) CancelTask(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelTask", reflect.TypeOf((*MockTaskHTTPClient)(nil).CancelTask), varargs...)
}

// CommandTask mocks base method.
func (m *MockTaskHTTPClient) CommandTask(arg0 context.Context, arg1 *v1.CommandTaskRequest, arg2 ...http.CallOption) (*v1.CommandTaskReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CommandTask", varargs...)
	ret0, _ := ret[0].(*v1.CommandTaskReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CommandTask indicates an expected call of CommandTask.
func (mr *MockTaskHTTPClientMockRecorder) CommandTask(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommandTask", reflect.TypeOf((*MockTaskHTTPClient)(nil).CommandTask), varargs...)
}

// CompleteTask mocks base method.
func (m *MockTaskHTTPClient) CompleteTask(arg0 context.Context, arg1 *v1.CompleteTaskRequest, arg2 ...http.CallOption) (*v1.CompleteTaskReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CompleteTask", varargs...)
	ret0, _ := ret[0].(*v1.CompleteTaskReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CompleteTask indicates an expected call of CompleteTask.
func (mr *MockTaskHTTPClientMockRecorder) CompleteTask(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompleteTask", reflect.TypeOf((*MockTaskHTTPClient)(nil).CompleteTask), varargs...)
}

// GetTask mocks base method.
func (m *MockTaskHTTPClient) GetTask(arg0 context.Context, arg1 *v1.GetTaskRequest, arg2 ...http.CallOption) (*v1.GetTaskReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTask", varargs...)
	ret0, _ := ret[0].(*v1.GetTaskReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTask indicates an expected call of GetTask.
func (mr *MockTaskHTTPClientMockRecorder) GetTask(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTask", reflect.TypeOf((*MockTaskHTTPClient)(nil).GetTask), varargs...)
}

// InvokeTask mocks base method.
func (m *MockTaskHTTPClient) InvokeTask(arg0 context.Context, arg1 *v1.InvokeTaskRequest, arg2 ...http.CallOption) (*v1.InvokeTaskReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InvokeTask", varargs...)
	ret0, _ := ret[0].(*v1.InvokeTaskReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InvokeTask indicates an expected call of InvokeTask.
func (mr *MockTaskHTTPClientMockRecorder) InvokeTask(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InvokeTask", reflect.TypeOf((*MockTaskHTTPClient)(nil).InvokeTask), varargs...)
}

// ListRunningTaskIds mocks base method.
func (m *MockTaskHTTPClient) ListRunningTaskIds(arg0 context.Context, arg1 *v1.ListRunningTaskIdsRequest, arg2 ...http.CallOption) (*v1.ListRunningTaskIdsReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListRunningTaskIds", varargs...)
	ret0, _ := ret[0].(*v1.ListRunningTaskIdsReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRunningTaskIds indicates an expected call of ListRunningTaskIds.
func (mr *MockTaskHTTPClientMockRecorder) ListRunningTaskIds(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRunningTaskIds", reflect.TypeOf((*MockTaskHTTPClient)(nil).ListRunningTaskIds), varargs...)
}

// ListTask mocks base method.
func (m *MockTaskHTTPClient) ListTask(arg0 context.Context, arg1 *v1.ListTaskRequest, arg2 ...http.CallOption) (*v1.ListTaskReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListTask", varargs...)
	ret0, _ := ret[0].(*v1.ListTaskReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTask indicates an expected call of ListTask.
func (mr *MockTaskHTTPClientMockRecorder) ListTask(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTask", reflect.TypeOf((*MockTaskHTTPClient)(nil).ListTask), varargs...)
}

// MigrateTask mocks base method.
func (m *MockTaskHTTPClient) MigrateTask(arg0 context.Context, arg1 *v1.MigrateTaskRequest, arg2 ...http.CallOption) (*v1.MigrateTaskReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MigrateTask", varargs...)
	ret0, _ := ret[0].(*v1.MigrateTaskReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MigrateTask indicates an expected call of MigrateTask.
func (mr *MockTaskHTTPClientMockRecorder) MigrateTask(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MigrateTask", reflect.TypeOf((*MockTaskHTTPClient)(nil).MigrateTask), varargs...)
}

// StartTask mocks base method.
func (m *MockTaskHTTPClient) StartTask(arg0 context.Context, arg1 *v1.StartTaskRequest, arg2 ...http.CallOption) (*v1.StartTaskReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartTask", varargs...)
	ret0, _ := ret[0].(*v1.StartTaskReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartTask indicates an expected call of StartTask.
func (mr *MockTaskHTTPClientMockRecorder) StartTask(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartTask", reflect.TypeOf((*MockTaskHTTPClient)(nil).StartTask), varargs...)
}

// StartTaskLegacy mocks base method.
func (m *MockTaskHTTPClient) StartTaskLegacy(arg0 context.Context, arg1 *v1.StartTaskRequest, arg2 ...http.CallOption) (*v1.StartTaskLegacyReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartTaskLegacy", varargs...)
	ret0, _ := ret[0].(*v1.StartTaskLegacyReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartTaskLegacy indicates an expected call of StartTaskLegacy.
func (mr *MockTaskHTTPClientMockRecorder) StartTaskLegacy(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartTaskLegacy", reflect.TypeOf((*MockTaskHTTPClient)(nil).StartTaskLegacy), varargs...)
}

// StopTask mocks base method.
func (m *MockTaskHTTPClient) StopTask(arg0 context.Context, arg1 *v1.StopTaskRequest, arg2 ...http.CallOption) (*v1.StopTaskReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StopTask", varargs...)
	ret0, _ := ret[0].(*v1.StopTaskReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StopTask indicates an expected call of StopTask.
func (mr *MockTaskHTTPClientMockRecorder) StopTask(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopTask", reflect.TypeOf((*MockTaskHTTPClient)(nil).StopTask), varargs...)
}

// StopTaskLegacy mocks base method.
func (m *MockTaskHTTPClient) StopTaskLegacy(arg0 context.Context, arg1 *v1.StopTaskRequest, arg2 ...http.CallOption) (*v1.StopTaskLegacyReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StopTaskLegacy", varargs...)
	ret0, _ := ret[0].(*v1.StopTaskLegacyReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StopTaskLegacy indicates an expected call of StopTaskLegacy.
func (mr *MockTaskHTTPClientMockRecorder) StopTaskLegacy(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopTaskLegacy", reflect.TypeOf((*MockTaskHTTPClient)(nil).StopTaskLegacy), varargs...)
}

// SubmitTask mocks base method.
func (m *MockTaskHTTPClient) SubmitTask(arg0 context.Context, arg1 *v1.SubmitTaskRequest, arg2 ...http.CallOption) (*v1.SubmitTaskReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitTask", varargs...)
	ret0, _ := ret[0].(*v1.SubmitTaskReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitTask indicates an expected call of SubmitTask.
func (mr *MockTaskHTTPClientMockRecorder) SubmitTask(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitTask", reflect.TypeOf((*MockTaskHTTPClient)(nil).SubmitTask), varargs...)
}

// UpdateTask mocks base method.
func (m *MockTaskHTTPClient) UpdateTask(arg0 context.Context, arg1 *v1.UpdateTaskRequest, arg2 ...http.CallOption) (*v1.UpdateTaskReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTask", varargs...)
	ret0, _ := ret[0].(*v1.UpdateTaskReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTask indicates an expected call of UpdateTask.
func (mr *MockTaskHTTPClientMockRecorder) UpdateTask(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTask", reflect.TypeOf((*MockTaskHTTPClient)(nil).UpdateTask), varargs...)
}

// UpdateTaskLegacy mocks base method.
func (m *MockTaskHTTPClient) UpdateTaskLegacy(arg0 context.Context, arg1 *v1.UpdateTaskRequest, arg2 ...http.CallOption) (*v1.UpdateTaskLegacyReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTaskLegacy", varargs...)
	ret0, _ := ret[0].(*v1.UpdateTaskLegacyReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTaskLegacy indicates an expected call of UpdateTaskLegacy.
func (mr *MockTaskHTTPClientMockRecorder) UpdateTaskLegacy(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaskLegacy", reflect.TypeOf((*MockTaskHTTPClient)(nil).UpdateTaskLegacy), varargs...)
}

// UpdateTaskStatus mocks base method.
func (m *MockTaskHTTPClient) UpdateTaskStatus(arg0 context.Context, arg1 *v1.UpdateTaskStatusRequest, arg2 ...http.CallOption) (*v1.UpdateTaskStatusReply, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTaskStatus", varargs...)
	ret0, _ := ret[0].(*v1.UpdateTaskStatusReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTaskStatus indicates an expected call of UpdateTaskStatus.
func (mr *MockTaskHTTPClientMockRecorder) UpdateTaskStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaskStatus", reflect.TypeOf((*MockTaskHTTPClient)(nil).UpdateTaskStatus), varargs...)
}
