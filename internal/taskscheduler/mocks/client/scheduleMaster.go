// Code generated by MockGen. DO NOT EDIT.
// Source: mpp/internal/taskscheduler/biz (interfaces: TaskScheduleMasterApi)

// Package mclient is a generated GoMock package.
package mclient

import (
	context "context"
	biz "mpp/internal/taskscheduler/biz"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	v1 "proto.mpp/api/taskscheduler/v1"
)

// MockTaskScheduleMasterApi is a mock of TaskScheduleMasterApi interface.
type MockTaskScheduleMasterApi struct {
	ctrl     *gomock.Controller
	recorder *MockTaskScheduleMasterApiMockRecorder
}

// MockTaskScheduleMasterApiMockRecorder is the mock recorder for MockTaskScheduleMasterApi.
type MockTaskScheduleMasterApiMockRecorder struct {
	mock *MockTaskScheduleMasterApi
}

// NewMockTaskScheduleMasterApi creates a new mock instance.
func NewMockTaskScheduleMasterApi(ctrl *gomock.Controller) *MockTaskScheduleMasterApi {
	mock := &MockTaskScheduleMasterApi{ctrl: ctrl}
	mock.recorder = &MockTaskScheduleMasterApiMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTaskScheduleMasterApi) EXPECT() *MockTaskScheduleMasterApiMockRecorder {
	return m.recorder
}

// AllocMasterResource mocks base method.
func (m *MockTaskScheduleMasterApi) AllocMasterResource(arg0 context.Context, arg1 *v1.AllocResourceRequest) (*biz.Worker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AllocMasterResource", arg0, arg1)
	ret0, _ := ret[0].(*biz.Worker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AllocMasterResource indicates an expected call of AllocMasterResource.
func (mr *MockTaskScheduleMasterApiMockRecorder) AllocMasterResource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllocMasterResource", reflect.TypeOf((*MockTaskScheduleMasterApi)(nil).AllocMasterResource), arg0, arg1)
}

// FreeMasterResource mocks base method.
func (m *MockTaskScheduleMasterApi) FreeMasterResource(arg0 context.Context, arg1 string) (*biz.Worker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FreeMasterResource", arg0, arg1)
	ret0, _ := ret[0].(*biz.Worker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreeMasterResource indicates an expected call of FreeMasterResource.
func (mr *MockTaskScheduleMasterApiMockRecorder) FreeMasterResource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreeMasterResource", reflect.TypeOf((*MockTaskScheduleMasterApi)(nil).FreeMasterResource), arg0, arg1)
}

// GetEndpoint mocks base method.
func (m *MockTaskScheduleMasterApi) GetEndpoint() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEndpoint")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetEndpoint indicates an expected call of GetEndpoint.
func (mr *MockTaskScheduleMasterApiMockRecorder) GetEndpoint() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEndpoint", reflect.TypeOf((*MockTaskScheduleMasterApi)(nil).GetEndpoint))
}

// GetMasterWorker mocks base method.
func (m *MockTaskScheduleMasterApi) GetMasterWorker(arg0 context.Context, arg1 string) (*biz.Worker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMasterWorker", arg0, arg1)
	ret0, _ := ret[0].(*biz.Worker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMasterWorker indicates an expected call of GetMasterWorker.
func (mr *MockTaskScheduleMasterApiMockRecorder) GetMasterWorker(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMasterWorker", reflect.TypeOf((*MockTaskScheduleMasterApi)(nil).GetMasterWorker), arg0, arg1)
}

// InitClient mocks base method.
func (m *MockTaskScheduleMasterApi) InitClient(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitClient", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InitClient indicates an expected call of InitClient.
func (mr *MockTaskScheduleMasterApiMockRecorder) InitClient(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitClient", reflect.TypeOf((*MockTaskScheduleMasterApi)(nil).InitClient), arg0, arg1)
}

// ResetClient mocks base method.
func (m *MockTaskScheduleMasterApi) ResetClient(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetClient", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetClient indicates an expected call of ResetClient.
func (mr *MockTaskScheduleMasterApiMockRecorder) ResetClient(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetClient", reflect.TypeOf((*MockTaskScheduleMasterApi)(nil).ResetClient), arg0, arg1)
}
