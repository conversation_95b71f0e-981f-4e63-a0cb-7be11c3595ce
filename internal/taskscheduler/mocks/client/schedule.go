// Code generated by MockGen. DO NOT EDIT.
// Source: internal/taskscheduler/biz/schedule.go

// Package mclient is a generated GoMock package.
package mclient

import (
	context "context"
	biz "mpp/internal/taskscheduler/biz"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	//v1 "proto.mpp/api/taskscheduler/v1"
)

// MockScheduler is a mock of Scheduler interface.
type MockScheduler struct {
	ctrl     *gomock.Controller
	recorder *MockSchedulerMockRecorder
}

// MockSchedulerMockRecorder is the mock recorder for MockScheduler.
type MockSchedulerMockRecorder struct {
	mock *MockScheduler
}

// NewMockScheduler creates a new mock instance.
func NewMockScheduler(ctrl *gomock.Controller) *MockScheduler {
	mock := &MockScheduler{ctrl: ctrl}
	mock.recorder = &MockSchedulerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockScheduler) EXPECT() *MockSchedulerMockRecorder {
	return m.recorder
}

// Alloc mocks base method.
func (m *MockScheduler) Alloc(arg0 context.Context, arg1 *biz.Resource) (*biz.WorkerQuota, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Alloc", arg0, arg1)
	ret0, _ := ret[0].(*biz.WorkerQuota)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Alloc indicates an expected call of Alloc.
func (mr *MockSchedulerMockRecorder) Alloc(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Alloc", reflect.TypeOf((*MockScheduler)(nil).Alloc), arg0, arg1)
}

// Free mocks base method.
func (m *MockScheduler) Free(arg0 context.Context, arg1 *biz.Resource, arg2 *biz.WorkerQuota) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Free", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Free indicates an expected call of Free.
func (mr *MockSchedulerMockRecorder) Free(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Free", reflect.TypeOf((*MockScheduler)(nil).Free), arg0, arg1, arg2)
}

// Printf mocks base method.
func (m *MockScheduler) Printf(ctx context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Printf", ctx)
}

// Printf indicates an expected call of Printf.
func (mr *MockSchedulerMockRecorder) Printf(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Printf", reflect.TypeOf((*MockScheduler)(nil).Printf), ctx)
}

// Put mocks base method.
func (m *MockScheduler) Put(arg0 context.Context, arg1 *biz.WorkerQuota) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Put", arg0, arg1)
}

// Put indicates an expected call of Put.
func (mr *MockSchedulerMockRecorder) Put(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Put", reflect.TypeOf((*MockScheduler)(nil).Put), arg0, arg1)
}

// Remove mocks base method.
func (m *MockScheduler) Remove(arg0 context.Context, arg1 *biz.WorkerQuota) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Remove", arg0, arg1)
}

// Remove indicates an expected call of Remove.
func (mr *MockSchedulerMockRecorder) Remove(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Remove", reflect.TypeOf((*MockScheduler)(nil).Remove), arg0, arg1)
}

// Statistics mocks base method.
func (m *MockScheduler) Statistics(ctx context.Context) *biz.WaterLevel {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Statistics", ctx)
	ret0, _ := ret[0].(*biz.WaterLevel)
	return ret0
}

// Statistics indicates an expected call of Statistics.
func (mr *MockSchedulerMockRecorder) Statistics(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Statistics", reflect.TypeOf((*MockScheduler)(nil).Statistics), ctx)
}

//
//// MockTaskScheduleMasterApi2 is a mock of TaskScheduleMasterApi interface.
//type MockTaskScheduleMasterApi2 struct {
//	ctrl     *gomock.Controller
//	recorder *MockTaskScheduleMasterApi2MockRecorder
//}
//
//// MockTaskScheduleMasterApi2MockRecorder is the mock recorder for MockTaskScheduleMasterApi2.
//type MockTaskScheduleMasterApi2MockRecorder struct {
//	mock *MockTaskScheduleMasterApi2
//}
//
//// NewMockTaskScheduleMasterApi2 creates a new mock instance.
//func NewMockTaskScheduleMasterApi2(ctrl *gomock.Controller) *MockTaskScheduleMasterApi2 {
//	mock := &MockTaskScheduleMasterApi2{ctrl: ctrl}
//	mock.recorder = &MockTaskScheduleMasterApi2MockRecorder{mock}
//	return mock
//}
//
//// EXPECT returns an object that allows the caller to indicate expected use.
//func (m *MockTaskScheduleMasterApi2) EXPECT() *MockTaskScheduleMasterApi2MockRecorder {
//	return m.recorder
//}
//
//// AllocMasterResource mocks base method.
//func (m *MockTaskScheduleMasterApi2) AllocMasterResource(arg0 context.Context, arg1 *v1.AllocResourceRequest) (*biz.Worker, error) {
//	m.ctrl.T.Helper()
//	ret := m.ctrl.Call(m, "AllocMasterResource", arg0, arg1)
//	ret0, _ := ret[0].(*biz.Worker)
//	ret1, _ := ret[1].(error)
//	return ret0, ret1
//}
//
//// AllocMasterResource indicates an expected call of AllocMasterResource.
//func (mr *MockTaskScheduleMasterApi2MockRecorder) AllocMasterResource(arg0, arg1 interface{}) *gomock.Call {
//	mr.mock.ctrl.T.Helper()
//	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllocMasterResource", reflect.TypeOf((*MockTaskScheduleMasterApi2)(nil).AllocMasterResource), arg0, arg1)
//}
//
//// FreeMasterResource mocks base method.
//func (m *MockTaskScheduleMasterApi2) FreeMasterResource(arg0 context.Context, arg1 string) (*biz.Worker, error) {
//	m.ctrl.T.Helper()
//	ret := m.ctrl.Call(m, "FreeMasterResource", arg0, arg1)
//	ret0, _ := ret[0].(*biz.Worker)
//	ret1, _ := ret[1].(error)
//	return ret0, ret1
//}
//
//// FreeMasterResource indicates an expected call of FreeMasterResource.
//func (mr *MockTaskScheduleMasterApi2MockRecorder) FreeMasterResource(arg0, arg1 interface{}) *gomock.Call {
//	mr.mock.ctrl.T.Helper()
//	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreeMasterResource", reflect.TypeOf((*MockTaskScheduleMasterApi2)(nil).FreeMasterResource), arg0, arg1)
//}
//
//// GetEndpoint mocks base method.
//func (m *MockTaskScheduleMasterApi2) GetEndpoint() string {
//	m.ctrl.T.Helper()
//	ret := m.ctrl.Call(m, "GetEndpoint")
//	ret0, _ := ret[0].(string)
//	return ret0
//}
//
//// GetEndpoint indicates an expected call of GetEndpoint.
//func (mr *MockTaskScheduleMasterApi2MockRecorder) GetEndpoint() *gomock.Call {
//	mr.mock.ctrl.T.Helper()
//	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEndpoint", reflect.TypeOf((*MockTaskScheduleMasterApi2)(nil).GetEndpoint))
//}
//
//// GetMasterWorker mocks base method.
//func (m *MockTaskScheduleMasterApi2) GetMasterWorker(arg0 context.Context, arg1 string) (*biz.Worker, error) {
//	m.ctrl.T.Helper()
//	ret := m.ctrl.Call(m, "GetMasterWorker", arg0, arg1)
//	ret0, _ := ret[0].(*biz.Worker)
//	ret1, _ := ret[1].(error)
//	return ret0, ret1
//}
//
//// GetMasterWorker indicates an expected call of GetMasterWorker.
//func (mr *MockTaskScheduleMasterApi2MockRecorder) GetMasterWorker(arg0, arg1 interface{}) *gomock.Call {
//	mr.mock.ctrl.T.Helper()
//	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMasterWorker", reflect.TypeOf((*MockTaskScheduleMasterApi2)(nil).GetMasterWorker), arg0, arg1)
//}
//
//// InitClient mocks base method.
//func (m *MockTaskScheduleMasterApi2) InitClient(arg0 context.Context, arg1 string) error {
//	m.ctrl.T.Helper()
//	ret := m.ctrl.Call(m, "InitClient", arg0, arg1)
//	ret0, _ := ret[0].(error)
//	return ret0
//}
//
//// InitClient indicates an expected call of InitClient.
//func (mr *MockTaskScheduleMasterApi2MockRecorder) InitClient(arg0, arg1 interface{}) *gomock.Call {
//	mr.mock.ctrl.T.Helper()
//	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitClient", reflect.TypeOf((*MockTaskScheduleMasterApi2)(nil).InitClient), arg0, arg1)
//}
//
//// ResetClient mocks base method.
//func (m *MockTaskScheduleMasterApi2) ResetClient(arg0 context.Context, arg1 string) error {
//	m.ctrl.T.Helper()
//	ret := m.ctrl.Call(m, "ResetClient", arg0, arg1)
//	ret0, _ := ret[0].(error)
//	return ret0
//}
//
//// ResetClient indicates an expected call of ResetClient.
//func (mr *MockTaskScheduleMasterApi2MockRecorder) ResetClient(arg0, arg1 interface{}) *gomock.Call {
//	mr.mock.ctrl.T.Helper()
//	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetClient", reflect.TypeOf((*MockTaskScheduleMasterApi2)(nil).ResetClient), arg0, arg1)
//}
