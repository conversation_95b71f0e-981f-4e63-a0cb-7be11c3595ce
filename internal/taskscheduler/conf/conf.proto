syntax = "proto3";
package kratos.api.taskscheduler;

option go_package = "mpp/internal/taskscheduler/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Registry registry = 2;
  Data data = 3;
  Swagger swagger = 4;
  App app = 5;
  LiveTransCodeConfig liveTransCodeConfig = 6;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;

  string tracingUrl = 3;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
    bool autoMigrate = 3;
  }
  message Redis {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration readTimeout = 3;
    google.protobuf.Duration writeTimeout = 4;
  }
  Database database = 1;
  Redis redis = 2;
}

message App {
  string region = 1;
  string clusterId = 2;

  message Leader {
    string addr = 1;
  }

  message TaskManager {
    string addr = 1;
  }
  message JobManager {
    string addr = 1;
    repeated string addrs = 2;
  }
  Leader leader = 3;
  TaskManager taskManager = 4;
  JobManager jobManager = 5;
  bool delayScheduleUpdate = 6;
  string taskSchedulerTag = 7;
}

message Swagger {
  string enabled = 1;
}

message Registry {
  string endpoint = 1;
}

message LiveTransCodeConfig {
  string unitRegistry = 1;
}