GOHOSTOS:=$(shell go env GOHOSTOS)
GOPATH:=$(shell go env GOPATH)
VERSION=$(shell git rev-parse --short HEAD)
BUILD_DAY=$(shell date "+%Y%m%d%H%M")
BUILD_DATE=$(shell date "+%Y%m%d%H%M%S" | sed 's/-//g')

ifeq ($(GOHOSTOS), windows)
	#the `find.exe` is different from `find` in bash/shell.
	#to see https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/find.
	#changed to use git-bash.exe to run find cli or other cli friendly, caused of every developer has a Git.
	#Git_Bash= $(subst cmd\,bin\bash.exe,$(dir $(shell where git)))
	Git_Bash=$(subst \,/,$(subst cmd\,bin\bash.exe,$(dir $(shell where git))))
	INTERNAL_PROTO_FILES=$(shell $(Git_Bash) -c "find internal -name *.proto")
	API_PROTO_FILES=$(shell $(Git_Bash) -c "find api -name *.proto")
	PKG_PROTO_FILES=$(shell $(Git_Bash) -c "find pkg -name *.proto")
else
	INTERNAL_PROTO_FILES=$(shell find internal -name *.proto)
	API_PROTO_FILES=$(shell find api -name *.proto)
	PKG_PROTO_FILES=$(shell find pkg -name *.proto)
	INTERNAL_AGENT_PROTO_FILES=$(shell find internal/agent -name *.proto -not -path "internal/agent/v1/*")
	INTERNAL_TM_PROTO_FILES=$(shell find internal/taskmanager -name *.proto)
	INTERNAL_TS_PROTO_FILES=$(shell find internal/taskscheduler -name *.proto)
	INTERNAL_SENSING_PROTO_FILES=$(shell find internal/sensing -name *.proto)
	INTERNAL_WORKFLOW_PROTO_FILES=$(shell find internal/workflow -name *.proto)
endif

.PHONY: init
# init env
init:
	go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.31.0
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
	go install github.com/go-kratos/kratos/cmd/kratos/v2@latest
	go install github.com/go-kratos/kratos/cmd/protoc-gen-go-http/v2@latest
	go install github.com/google/gnostic/cmd/protoc-gen-openapi@latest
	go install github.com/google/wire/cmd/wire@latest
	go install github.com/envoyproxy/protoc-gen-validate@latest
	go install github.com/golang/mock/mockgen@latest
	go install github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-grpc-gateway

.PHONY: config
# generate internal proto
config:
	protoc --proto_path=./internal \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./internal \
	       $(INTERNAL_TM_PROTO_FILES)
	protoc --proto_path=./internal \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./internal \
	       $(INTERNAL_TS_PROTO_FILES)
	protoc --proto_path=./internal \
		   --proto_path=./third_party \
		   --go_out=paths=source_relative:./internal \
		   $(INTERNAL_SENSING_PROTO_FILES)
	protoc --proto_path=./internal \
		   --proto_path=./third_party \
		   --go_out=paths=source_relative:./internal \
		   $(INTERNAL_AGENT_PROTO_FILES)
	protoc --proto_path=./internal \
    		   --proto_path=./third_party \
    		   --go_out=paths=source_relative:./internal \
    		   $(INTERNAL_WORKFLOW_PROTO_FILES)
	protoc --proto_path=./pkg \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./pkg \
	       $(PKG_PROTO_FILES)

.PHONY: validate
# generate validate proto
validate:
	protoc --proto_path=./api \
		   --proto_path=./third_party \
		   --go_out=paths=source_relative:./api \
		   --go-http_out=paths=source_relative:./api \
		   --go-grpc_out=paths=source_relative:./api \
		   --openapi_out=fq_schema_naming=true,default_response=false:. \
		   --validate_out=paths=source_relative,lang=go:./api \
		   $(API_PROTO_FILES)

.PHONY: api
# generate api proto
api:
	test -d ./api && \
	protoc --proto_path=./api \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./api \
 	       --go-http_out=paths=source_relative:./api \
 	       --go-grpc_out=paths=source_relative:./api \
	       --openapi_out=fq_schema_naming=true,default_response=false:. \
	       --validate_out=paths=source_relative,lang=go:./api \
	       $(API_PROTO_FILES)
#	protoc --proto_path=./third_party --grpc-gateway_out=paths=source_relative:./ --grpc-gateway_opt=logtostderr=true --grpc-gateway_opt=paths=source_relative --proto_path=. ./api/pubsub/v1/pubsub.proto


.PHONY: build
# build
build:
	mkdir -p bin/ && go build -ldflags "-X main.Version=${BUILD_DAY}-$(VERSION) -X main.BuildDate=$(BUILD_DATE) -w -s" -o ./bin/ ./...

.PHONY: build-mpp-worker-agent
# build
build-mpp-agent:
	mkdir -p bin/ && go build -tags "containers_image_openpgp exclude_graphdriver_btrfs exclude_graphdriver_devicemapper" -ldflags "-X main.Version=${BUILD_DAY}-$(VERSION) -X main.BuildDate=$(BUILD_DATE) -w -s" -o ./bin/mpp-worker-agent ./cmd/agent/main.go && echo $(BUILD_DATE) > ./bin/version
	mkdir -p bin/ && go build -tags "containers_image_openpgp exclude_graphdriver_btrfs exclude_graphdriver_devicemapper" -ldflags "-X main.Version=${BUILD_DAY}-$(VERSION) -X main.BuildDate=$(BUILD_DATE) -w -s" -o ./bin/tinyproxy ./cmd/tinyproxy/main.go
	mkdir -p bin/ && go build -tags "containers_image_openpgp exclude_graphdriver_btrfs exclude_graphdriver_devicemapper" -ldflags "-X main.Version=${BUILD_DAY}-$(VERSION) -X main.BuildDate=$(BUILD_DATE) -w -s" -o ./bin/shellproxy ./cmd/shellproxy/main.go

.PHONY: build-mpp-gateway
# build
build-mpp-gateway:
	make build

.PHONY: build-mpp-taskmanager
# build
build-mpp-taskmanager:
	make build

.PHONY: build-mpp-taskscheduler
# build
build-mpp-taskscheduler:
	make build

.PHONY: build-mpp-sensing
# build
build-mpp-sensing:
	make build

build-mpp-chief:
	cd apps/chief && make build
	cd apps/ark && make build

.PHONY: build-mpp-workflow
# build
build-mpp-workflow:
	make build

.PHONY: generate
# generate
generate:
	go mod tidy
	go get github.com/google/wire/cmd/wire@latest
	go generate ./...

.PHONY: all
# generate all
all:
#	make api;
	make config;
	make generate;

# show help
help:
	@echo ''
	@echo 'Usage:'
	@echo ' make [target]'
	@echo ''
	@echo 'Targets:'
	@awk '/^[a-zA-Z\-\_0-9]+:/ { \
	helpMessage = match(lastLine, /^# (.*)/); \
		if (helpMessage) { \
			helpCommand = substr($$1, 0, index($$1, ":")); \
			helpMessage = substr(lastLine, RSTART + 2, RLENGTH); \
			printf "\033[36m%-22s\033[0m %s\n", helpCommand,helpMessage; \
		} \
	} \
	{ lastLine = $$0 }' $(MAKEFILE_LIST)

.DEFAULT_GOAL := help
